<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.security.BBacnetKeyIdentifier" name="BBacnetKeyIdentifier" packageName="javax.baja.bacnet.datatypes.security" public="true" final="true">
<description>
BBacnetKeyIdentifier represents the BACnetKeyIdentifier&#xa; sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="algorithm" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;algorithm&lt;/code&gt; property.
</description>
<tag name="@see">#getAlgorithm</tag>
<tag name="@see">#setAlgorithm</tag>
</property>

<property name="keyId" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;keyId&lt;/code&gt; property.
</description>
<tag name="@see">#getKeyId</tag>
<tag name="@see">#setKeyId</tag>
</property>

<!-- javax.baja.bacnet.datatypes.security.BBacnetKeyIdentifier() -->
<constructor name="BBacnetKeyIdentifier" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.security.BBacnetKeyIdentifier(int, int) -->
<constructor name="BBacnetKeyIdentifier" public="true">
<parameter name="algorithm">
<type class="int"/>
</parameter>
<parameter name="keyId">
<type class="int"/>
</parameter>
<description>
Standard constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.security.BBacnetKeyIdentifier.getAlgorithm() -->
<method name="getAlgorithm"  public="true">
<description>
Get the &lt;code&gt;algorithm&lt;/code&gt; property.
</description>
<tag name="@see">#algorithm</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetKeyIdentifier.setAlgorithm(int) -->
<method name="setAlgorithm"  public="true">
<description>
Set the &lt;code&gt;algorithm&lt;/code&gt; property.
</description>
<tag name="@see">#algorithm</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetKeyIdentifier.getKeyId() -->
<method name="getKeyId"  public="true">
<description>
Get the &lt;code&gt;keyId&lt;/code&gt; property.
</description>
<tag name="@see">#keyId</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetKeyIdentifier.setKeyId(int) -->
<method name="setKeyId"  public="true">
<description>
Set the &lt;code&gt;keyId&lt;/code&gt; property.
</description>
<tag name="@see">#keyId</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetKeyIdentifier.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetKeyIdentifier.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetKeyIdentifier.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetKeyIdentifier.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetKeyIdentifier.algorithm -->
<field name="algorithm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;algorithm&lt;/code&gt; property.
</description>
<tag name="@see">#getAlgorithm</tag>
<tag name="@see">#setAlgorithm</tag>
</field>

<!-- javax.baja.bacnet.datatypes.security.BBacnetKeyIdentifier.keyId -->
<field name="keyId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;keyId&lt;/code&gt; property.
</description>
<tag name="@see">#getKeyId</tag>
<tag name="@see">#setKeyId</tag>
</field>

<!-- javax.baja.bacnet.datatypes.security.BBacnetKeyIdentifier.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.security.BBacnetKeyIdentifier.ALGORITHM_TAG -->
<field name="ALGORITHM_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.security.BBacnetKeyIdentifier.KEY_ID_TAG -->
<field name="KEY_ID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
