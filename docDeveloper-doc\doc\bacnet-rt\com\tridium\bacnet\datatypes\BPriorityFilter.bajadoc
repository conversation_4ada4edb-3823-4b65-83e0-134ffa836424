<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.datatypes.BPriorityFilter" name="BPriorityFilter" packageName="com.tridium.bacnet.datatypes" public="true">
<description>
BPriorityFilter represents the structure for restricting the types&#xa; of event-initiating objects that shall be included in a response to a&#xa; GetEnrollmentSummary-Request, based on their priority.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">04 Aug 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="minPriority" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;minPriority&lt;/code&gt; property.
</description>
<tag name="@see">#getMinPriority</tag>
<tag name="@see">#setMinPriority</tag>
</property>

<property name="maxPriority" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;maxPriority&lt;/code&gt; property.
</description>
<tag name="@see">#getMaxPriority</tag>
<tag name="@see">#setMaxPriority</tag>
</property>

</class>
</bajadoc>
