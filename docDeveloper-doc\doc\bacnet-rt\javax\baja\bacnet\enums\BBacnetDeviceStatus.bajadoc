<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetDeviceStatus" name="BBacnetDeviceStatus" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetDeviceStatus represents the BACnetDeviceStatus&#xa; enumeration.&#xa; &lt;p&gt;&#xa; BBacnetDeviceStatus is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0xFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Craig Gemmill</tag>
<tag name="@version">$Revision: 7$ $Date: 12/19/01 4:35:56 PM$</tag>
<tag name="@creation">30 Jan 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;operational&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;operationalReadOnly&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;downloadRequired&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;downloadInProgress&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;nonOperational&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;backupInProgress&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetDeviceStatus"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetDeviceStatus"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
String representation of this BEnum.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.OPERATIONAL -->
<field name="OPERATIONAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for operational.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.OPERATIONAL_READ_ONLY -->
<field name="OPERATIONAL_READ_ONLY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for operationalReadOnly.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.DOWNLOAD_REQUIRED -->
<field name="DOWNLOAD_REQUIRED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for downloadRequired.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.DOWNLOAD_IN_PROGRESS -->
<field name="DOWNLOAD_IN_PROGRESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for downloadInProgress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.NON_OPERATIONAL -->
<field name="NON_OPERATIONAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for nonOperational.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.BACKUP_IN_PROGRESS -->
<field name="BACKUP_IN_PROGRESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for backupInProgress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.operational -->
<field name="operational"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetDeviceStatus"/>
<description>
BBacnetDeviceStatus constant for operational.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.operationalReadOnly -->
<field name="operationalReadOnly"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetDeviceStatus"/>
<description>
BBacnetDeviceStatus constant for operationalReadOnly.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.downloadRequired -->
<field name="downloadRequired"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetDeviceStatus"/>
<description>
BBacnetDeviceStatus constant for downloadRequired.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.downloadInProgress -->
<field name="downloadInProgress"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetDeviceStatus"/>
<description>
BBacnetDeviceStatus constant for downloadInProgress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.nonOperational -->
<field name="nonOperational"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetDeviceStatus"/>
<description>
BBacnetDeviceStatus constant for nonOperational.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.backupInProgress -->
<field name="backupInProgress"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetDeviceStatus"/>
<description>
BBacnetDeviceStatus constant for backupInProgress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetDeviceStatus"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetDeviceStatus.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
