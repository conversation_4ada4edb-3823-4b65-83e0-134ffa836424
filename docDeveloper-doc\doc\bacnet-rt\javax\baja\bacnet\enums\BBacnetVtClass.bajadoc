<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetVtClass" name="BBacnetVtClass" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetVtClass represents the Bacnet VT Class&#xa; enumeration.&#xa; &lt;p&gt;&#xa; BBacnetVtClass is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Craig Gemmill</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">16 May 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;defaultTerminal&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ansiX3_64&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;decVt52&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;decVt100&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;decVt220&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;hp700_94&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ibm3130&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetVtClass.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetVtClass"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetVtClass"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.DEFAULT_TERMINAL -->
<field name="DEFAULT_TERMINAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for defaultTerminal.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.ANSI_X3_64 -->
<field name="ANSI_X3_64"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ansiX3_64.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.DEC_VT_52 -->
<field name="DEC_VT_52"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for decVt52.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.DEC_VT_100 -->
<field name="DEC_VT_100"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for decVt100.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.DEC_VT_220 -->
<field name="DEC_VT_220"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for decVt220.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.HP_700_94 -->
<field name="HP_700_94"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for hp700_94.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.IBM_3130 -->
<field name="IBM_3130"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ibm3130.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.defaultTerminal -->
<field name="defaultTerminal"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetVtClass"/>
<description>
BBacnetVtClass constant for defaultTerminal.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.ansiX3_64 -->
<field name="ansiX3_64"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetVtClass"/>
<description>
BBacnetVtClass constant for ansiX3_64.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.decVt52 -->
<field name="decVt52"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetVtClass"/>
<description>
BBacnetVtClass constant for decVt52.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.decVt100 -->
<field name="decVt100"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetVtClass"/>
<description>
BBacnetVtClass constant for decVt100.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.decVt220 -->
<field name="decVt220"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetVtClass"/>
<description>
BBacnetVtClass constant for decVt220.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.hp700_94 -->
<field name="hp700_94"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetVtClass"/>
<description>
BBacnetVtClass constant for hp700_94.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.ibm3130 -->
<field name="ibm3130"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetVtClass"/>
<description>
BBacnetVtClass constant for ibm3130.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetVtClass"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetVtClass.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
