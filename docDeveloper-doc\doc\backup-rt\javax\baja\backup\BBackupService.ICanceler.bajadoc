<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="backup" runtimeProfile="rt" qualifiedName="javax.baja.backup.BBackupService$ICanceler" name="BBackupService.ICanceler" packageName="javax.baja.backup" public="true" interface="true" abstract="true" static="true" innerClass="true" category="interface">
<description/>
<!-- javax.baja.backup.BBackupService.ICanceler.isCanceled() -->
<method name="isCanceled"  public="true" abstract="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

</class>
</bajadoc>
