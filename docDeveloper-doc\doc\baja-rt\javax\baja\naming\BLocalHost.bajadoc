<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.BLocalHost" name="BLocalHost" packageName="javax.baja.naming" public="true">
<description>
BLocalHost is the BHost for the local machine.  There is&#xa; only one instance accessed via INSTANCE.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">14 Jan 03</tag>
<tag name="@version">$Revision: 20$ $Date: 6/17/10 12:50:31 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.naming.BIpHost"/>
</extends>
<implements>
<type class="javax.baja.naming.BISession"/>
</implements>
<implements>
<type class="javax.baja.naming.BServiceScheme$ServiceSession"/>
</implements>
<implements>
<type class="javax.baja.space.BISpaceContainer"/>
</implements>
<!-- javax.baja.naming.BLocalHost.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.naming.BLocalHost.getAbsoluteOrd() -->
<method name="getAbsoluteOrd"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return BLocalScheme.ORD.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.naming.BLocalHost.isConnected() -->
<method name="isConnected"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return true.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.BLocalHost.connect() -->
<method name="connect"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Do nothing.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.naming.BLocalHost.getHost() -->
<method name="getHost"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return this.
</description>
<return>
<type class="javax.baja.naming.BHost"/>
</return>
</method>

<!-- javax.baja.naming.BLocalHost.getOrdInHost() -->
<method name="getOrdInHost"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &#x22;&#x22;.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.naming.BLocalHost.getSessionContext() -->
<method name="getSessionContext"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return null (subject to change).
</description>
<return>
<type class="javax.baja.sys.Context"/>
</return>
</method>

<!-- javax.baja.naming.BLocalHost.mountSpace(javax.baja.space.BISpace) -->
<method name="mountSpace"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="space">
<type class="javax.baja.space.BISpace"/>
</parameter>
<return>
<type class="javax.baja.space.BISpace"/>
</return>
</method>

<!-- javax.baja.naming.BLocalHost.unmountSpace(javax.baja.space.BISpace) -->
<method name="unmountSpace"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="space">
<type class="javax.baja.space.BISpace"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.BLocalHost.getSpaces() -->
<method name="getSpaces"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<parameterizedType class="java.util.Iterator">
<args>
<type class="javax.baja.space.BISpace"/>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.naming.BLocalHost.fw(int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object) -->
<method name="fw"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="x">
<type class="int"/>
</parameter>
<parameter name="a">
<type class="java.lang.Object"/>
</parameter>
<parameter name="b">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c">
<type class="java.lang.Object"/>
</parameter>
<parameter name="d">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="java.lang.Object"/>
</return>
</method>

<!-- javax.baja.naming.BLocalHost.getService(javax.baja.sys.Type) -->
<method name="getService"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;Sys.getService()&lt;/code&gt;
</description>
<parameter name="type">
<type class="javax.baja.sys.Type"/>
</parameter>
<return>
<type class="javax.baja.sys.BComponent"/>
</return>
</method>

<!-- javax.baja.naming.BLocalHost.getDefaultNavDisplayName(javax.baja.sys.Context) -->
<method name="getDefaultNavDisplayName"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.BLocalHost.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the icon.
</description>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.naming.BLocalHost.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.naming.BLocalHost.INSTANCE -->
<field name="INSTANCE"  public="true" static="true" final="true">
<type class="javax.baja.naming.BLocalHost"/>
<description/>
</field>

</class>
</bajadoc>
