<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BAlarmSource$SourceComparator" name="BAlarmSource.SourceComparator" packageName="javax.baja.alarm" public="true" static="true" innerClass="true">
<description/>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<parameterizedType class="java.util.Comparator">
<args>
<type class="javax.baja.alarm.BAlarmSource"/>
</args>
</parameterizedType>
</implements>
<!-- javax.baja.alarm.BAlarmSource.SourceComparator() -->
<constructor name="SourceComparator" public="true">
<description/>
</constructor>

<!-- javax.baja.alarm.BAlarmSource.SourceComparator.compare(javax.baja.alarm.BAlarmSource, javax.baja.alarm.BAlarmSource) -->
<method name="compare"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="o1">
<type class="javax.baja.alarm.BAlarmSource"/>
</parameter>
<parameter name="o2">
<type class="javax.baja.alarm.BAlarmSource"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

</class>
</bajadoc>
