<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.routing.BJsonMessageRouter" name="BJsonMessageRouter" packageName="com.tridiumx.jsonToolkit.inbound.routing" public="true">
<description>
The Json Message Router allows inbound messages to be transferred to an onward component&#xa; suitable for processing, or handling, the message.&#xa;&#xa; By adding dynamic slots of type baja:String with a name matching the value of the *key*, the entire incoming message&#xa; will be routed to that slot for onward processing.&#xa;&#xa; Dynamic String slots can be added using the slot sheet view, they should use _transient_&#xa; and _readonly_ flags to avoid onward handlers running again at start.&#xa;&#xa; Given the input:&#xa;&#xa; {&#xa;   &#x22;messageType&#x22;: &#x22;alarmAck&#x22;,&#xa;   &#x22;user&#x22;: &#x22;Maya&#x22;,&#xa;   &#x22;alarms&#x22;: [ &#x22;5cf9c8b2-1542-42ba-a1fd-5f753c777bc0&#x22; ]&#xa; }&#xa;&#xa; This message would be routed to a String slot named &#x22;alarmAck&#x22; if the key&#xa; is set to &#x22;messageType&#x22;.
</description>
<tag name="@author">Jason Woollard</tag>
<extends>
<type class="com.tridiumx.jsonToolkit.inbound.routing.BJsonRouter"/>
</extends>
<property name="key" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;key&lt;/code&gt; property.&#xa; Which part of the incoming message to switch on
</description>
<tag name="@see">#getKey</tag>
<tag name="@see">#setKey</tag>
</property>

<property name="resendWithBlank" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;resendWithBlank&lt;/code&gt; property.&#xa; If a duplicate/matching message is received, should this output am empty string to the&#xa; target slot, then resent the output. Allows messages to be &#x22;resent&#x22;, without a injecting an&#xa; empty message the &#x22;change&#x22; will not be propagated by the link, which could be an issue if the&#xa; handler needed other values in place to respond to this message
</description>
<tag name="@see">#getResendWithBlank</tag>
<tag name="@see">#setResendWithBlank</tag>
</property>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonMessageRouter() -->
<constructor name="BJsonMessageRouter" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonMessageRouter.getKey() -->
<method name="getKey"  public="true">
<description>
Get the &lt;code&gt;key&lt;/code&gt; property.&#xa; Which part of the incoming message to switch on
</description>
<tag name="@see">#key</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonMessageRouter.setKey(java.lang.String) -->
<method name="setKey"  public="true">
<description>
Set the &lt;code&gt;key&lt;/code&gt; property.&#xa; Which part of the incoming message to switch on
</description>
<tag name="@see">#key</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonMessageRouter.getResendWithBlank() -->
<method name="getResendWithBlank"  public="true">
<description>
Get the &lt;code&gt;resendWithBlank&lt;/code&gt; property.&#xa; If a duplicate/matching message is received, should this output am empty string to the&#xa; target slot, then resent the output. Allows messages to be &#x22;resent&#x22;, without a injecting an&#xa; empty message the &#x22;change&#x22; will not be propagated by the link, which could be an issue if the&#xa; handler needed other values in place to respond to this message
</description>
<tag name="@see">#resendWithBlank</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonMessageRouter.setResendWithBlank(boolean) -->
<method name="setResendWithBlank"  public="true">
<description>
Set the &lt;code&gt;resendWithBlank&lt;/code&gt; property.&#xa; If a duplicate/matching message is received, should this output am empty string to the&#xa; target slot, then resent the output. Allows messages to be &#x22;resent&#x22;, without a injecting an&#xa; empty message the &#x22;change&#x22; will not be propagated by the link, which could be an issue if the&#xa; handler needed other values in place to respond to this message
</description>
<tag name="@see">#resendWithBlank</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonMessageRouter.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonMessageRouter.routeValue(javax.baja.sys.BString, javax.baja.sys.Context) -->
<method name="routeValue"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="message">
<type class="javax.baja.sys.BString"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.inbound.routing.RoutingFailedException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonMessageRouter.getRerunTriggers() -->
<method name="getRerunTriggers"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Property" dimension="1"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonMessageRouter.key -->
<field name="key"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;key&lt;/code&gt; property.&#xa; Which part of the incoming message to switch on
</description>
<tag name="@see">#getKey</tag>
<tag name="@see">#setKey</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonMessageRouter.resendWithBlank -->
<field name="resendWithBlank"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;resendWithBlank&lt;/code&gt; property.&#xa; If a duplicate/matching message is received, should this output am empty string to the&#xa; target slot, then resent the output. Allows messages to be &#x22;resent&#x22;, without a injecting an&#xa; empty message the &#x22;change&#x22; will not be propagated by the link, which could be an issue if the&#xa; handler needed other values in place to respond to this message
</description>
<tag name="@see">#getResendWithBlank</tag>
<tag name="@see">#setResendWithBlank</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonMessageRouter.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
