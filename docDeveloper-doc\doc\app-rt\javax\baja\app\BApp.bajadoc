<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="app" runtimeProfile="rt" qualifiedName="javax.baja.app.BApp" name="BApp" packageName="javax.baja.app" public="true" abstract="true">
<description>
Niagara Application
</description>
<tag name="@author">g<PERSON><PERSON><PERSON></tag>
<tag name="@creation">27 Jul 2011</tag>
<tag name="@version">1</tag>
<tag name="@since">Niagara 3.7</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.sys.BIService"/>
</implements>
<implements>
<type class="javax.baja.status.BIStatus"/>
</implements>
<implements>
<type class="com.tridium.sys.service.ServiceListener"/>
</implements>
<implements>
<type class="javax.baja.app.BIAppComponent"/>
</implements>
<implements>
<type class="javax.baja.license.BILicensed"/>
</implements>
<property name="status" flags="rt">
<type class="javax.baja.status.BStatus"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.&#xa; App Status
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</property>

<property name="faultCause" flags="rt">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.&#xa; App Fault Cause
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</property>

<property name="enabled" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;enabled&lt;/code&gt; property.&#xa; App enable
</description>
<tag name="@see">#getEnabled</tag>
<tag name="@see">#setEnabled</tag>
</property>

<property name="version" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;version&lt;/code&gt; property.&#xa; App version
</description>
<tag name="@see">#getVersion</tag>
<tag name="@see">#setVersion</tag>
</property>

<action name="update" flags="ah">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;update&lt;/code&gt; action.&#xa; Update App Configuration
</description>
<tag name="@see">#update()</tag>
</action>

<!-- javax.baja.app.BApp() -->
<constructor name="BApp" public="true">
<description/>
</constructor>

<!-- javax.baja.app.BApp.getStatus() -->
<method name="getStatus"  public="true">
<description>
Get the &lt;code&gt;status&lt;/code&gt; property.&#xa; App Status
</description>
<tag name="@see">#status</tag>
<return>
<type class="javax.baja.status.BStatus"/>
</return>
</method>

<!-- javax.baja.app.BApp.setStatus(javax.baja.status.BStatus) -->
<method name="setStatus"  public="true">
<description>
Set the &lt;code&gt;status&lt;/code&gt; property.&#xa; App Status
</description>
<tag name="@see">#status</tag>
<parameter name="v">
<type class="javax.baja.status.BStatus"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.app.BApp.getFaultCause() -->
<method name="getFaultCause"  public="true">
<description>
Get the &lt;code&gt;faultCause&lt;/code&gt; property.&#xa; App Fault Cause
</description>
<tag name="@see">#faultCause</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.app.BApp.setFaultCause(java.lang.String) -->
<method name="setFaultCause"  public="true">
<description>
Set the &lt;code&gt;faultCause&lt;/code&gt; property.&#xa; App Fault Cause
</description>
<tag name="@see">#faultCause</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.app.BApp.getEnabled() -->
<method name="getEnabled"  public="true">
<description>
Get the &lt;code&gt;enabled&lt;/code&gt; property.&#xa; App enable
</description>
<tag name="@see">#enabled</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.app.BApp.setEnabled(boolean) -->
<method name="setEnabled"  public="true">
<description>
Set the &lt;code&gt;enabled&lt;/code&gt; property.&#xa; App enable
</description>
<tag name="@see">#enabled</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.app.BApp.getVersion() -->
<method name="getVersion"  public="true">
<description>
Get the &lt;code&gt;version&lt;/code&gt; property.&#xa; App version
</description>
<tag name="@see">#version</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.app.BApp.setVersion(java.lang.String) -->
<method name="setVersion"  public="true">
<description>
Set the &lt;code&gt;version&lt;/code&gt; property.&#xa; App version
</description>
<tag name="@see">#version</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.app.BApp.update() -->
<method name="update"  public="true">
<description>
Invoke the &lt;code&gt;update&lt;/code&gt; action.&#xa; Update App Configuration
</description>
<tag name="@see">#update</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.app.BApp.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.app.BApp.getRequiredServices() -->
<method name="getRequiredServices"  public="true">
<description>
Return the list of Services required for this App.
</description>
<return>
<type class="javax.baja.sys.Type" dimension="1"/>
</return>
</method>

<!-- javax.baja.app.BApp.getAppId() -->
<method name="getAppId"  public="true">
<description>
Return a String that uniquely identifies this App.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.app.BApp.getAppDisplayName(javax.baja.sys.Context) -->
<method name="getAppDisplayName"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.app.BApp.getAppDisplayIcon() -->
<method name="getAppDisplayIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.app.BApp.enabled() -->
<method name="enabled"  protected="true">
<description>
This method is called when moving from disabled&#xa; state into the enabled state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.app.BApp.disabled() -->
<method name="disabled"  protected="true">
<description>
This method is called when moving from enabled&#xa; state into the disabled state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.app.BApp.appOk() -->
<method name="appOk"  protected="true">
<description>
This method is called when moving from a non-operational to an operational state
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.app.BApp.appFail() -->
<method name="appFail"  protected="true">
<description>
This method is called when moving from an operational to a non-operational state
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.app.BApp.append(javax.baja.sys.Type[], javax.baja.sys.Type) -->
<method name="append"  protected="true" static="true" final="true">
<description/>
<parameter name="a">
<type class="javax.baja.sys.Type" dimension="1"/>
</parameter>
<parameter name="b">
<type class="javax.baja.sys.Type"/>
</parameter>
<return>
<type class="javax.baja.sys.Type" dimension="1"/>
</return>
</method>

<!-- javax.baja.app.BApp.append(javax.baja.sys.Type[], javax.baja.sys.Type[]) -->
<method name="append"  protected="true" static="true" final="true">
<description/>
<parameter name="a">
<type class="javax.baja.sys.Type" dimension="1"/>
</parameter>
<parameter name="b">
<type class="javax.baja.sys.Type" dimension="1"/>
</parameter>
<return>
<type class="javax.baja.sys.Type" dimension="1"/>
</return>
</method>

<!-- javax.baja.app.BApp.serviceStarted() -->
<method name="serviceStarted"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.app.BApp.serviceStopped() -->
<method name="serviceStopped"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.app.BApp.getServiceTypes() -->
<method name="getServiceTypes"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type" dimension="1"/>
</return>
</method>

<!-- javax.baja.app.BApp.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.app.BApp.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.app.BApp.fw(int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object) -->
<method name="fw"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Framework use only.
</description>
<parameter name="x">
<type class="int"/>
</parameter>
<parameter name="a">
<type class="java.lang.Object"/>
</parameter>
<parameter name="b">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c">
<type class="java.lang.Object"/>
</parameter>
<parameter name="d">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="java.lang.Object"/>
</return>
</method>

<!-- javax.baja.app.BApp.isDisabled() -->
<method name="isDisabled"  public="true" final="true">
<description>
Return true if the App is disabled.  An App is&#xa; disabled if the user has manually set the enabled property&#xa; to false.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.app.BApp.isFault() -->
<method name="isFault"  public="true" final="true">
<description>
Return true if the service is in fault.  An App&#xa; is in fault if either a fatal fault was detected or if&#xa; &lt;code&gt;configFail()&lt;/code&gt; has been called more recently&#xa; than &lt;code&gt;configOk()&lt;/code&gt;.  Refer to &lt;code&gt;faultCause&lt;/code&gt;&#xa; for the fault reason.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.app.BApp.isOperational() -->
<method name="isOperational"  public="true" final="true">
<description>
Return true if the App is neither disabled, nor in fault.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.app.BApp.doUpdate() -->
<method name="doUpdate"  public="true">
<description>
Update App Status
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.app.BApp.isFatalFault() -->
<method name="isFatalFault"  public="true" final="true">
<description>
Return true if the App detected a fatal fault.&#xa; Fatal faults cannot be recovered until the App&#xa; is restarted.  Fatal faults trump config faults.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.app.BApp.isConfigFault() -->
<method name="isConfigFault"  public="true" final="true">
<description>
Return true if a config fault was detected
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.app.BApp.isServicesFault() -->
<method name="isServicesFault"  public="true" final="true">
<description>
Return true if a services fault was detected (i.e. a required service is not installed)
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.app.BApp.configOk() -->
<method name="configOk"  public="true" final="true">
<description>
Clear the configuration fault status.  If there are no fatal&#xa; or service faults then clear the entire App&#x27;s fault status, otherwise&#xa; the service remains in fault.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.app.BApp.configFail(java.lang.String) -->
<method name="configFail"  public="true" final="true">
<description>
Set the service into configuration fault.  If the service was&#xa; previously not in fault, then this sets the service into fault.
</description>
<parameter name="cause">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.app.BApp.configFatal(java.lang.String) -->
<method name="configFatal"  public="true" final="true">
<description>
Set the service into the fatal fault condition.  Unlike&#xa; configFail(), the fatal fault condition cannot be cleared&#xa; until station restart.
</description>
<parameter name="cause">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.app.BApp.getLicenseFeature() -->
<method name="getLicenseFeature"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
If this service is to be licensed using the standard licensing&#xa; mechanism then override this method to return the Feature or&#xa; return null for no license checks.  Convention is that the&#xa; vendor and feature name matches the declaring module.
</description>
<return>
<type class="javax.baja.license.Feature"/>
</return>
</method>

<!-- javax.baja.app.BApp.serviceEvent(com.tridium.sys.service.BServiceEvent) -->
<method name="serviceEvent"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="event">
<type class="com.tridium.sys.service.BServiceEvent"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.app.BApp.status -->
<field name="status"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.&#xa; App Status
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</field>

<!-- javax.baja.app.BApp.faultCause -->
<field name="faultCause"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.&#xa; App Fault Cause
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</field>

<!-- javax.baja.app.BApp.enabled -->
<field name="enabled"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;enabled&lt;/code&gt; property.&#xa; App enable
</description>
<tag name="@see">#getEnabled</tag>
<tag name="@see">#setEnabled</tag>
</field>

<!-- javax.baja.app.BApp.version -->
<field name="version"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;version&lt;/code&gt; property.&#xa; App version
</description>
<tag name="@see">#getVersion</tag>
<tag name="@see">#setVersion</tag>
</field>

<!-- javax.baja.app.BApp.update -->
<field name="update"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;update&lt;/code&gt; action.&#xa; Update App Configuration
</description>
<tag name="@see">#update()</tag>
</field>

<!-- javax.baja.app.BApp.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
