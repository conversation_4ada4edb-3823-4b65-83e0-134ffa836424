<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.routing.RoutingFailedException" name="RoutingFailedException" packageName="com.tridiumx.jsonToolkit.inbound.routing" public="true" category="exception">
<description>
Routing failed for a given payload and reason.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="java.lang.Exception"/>
</extends>
<!-- com.tridiumx.jsonToolkit.inbound.routing.RoutingFailedException(java.lang.String, java.lang.String) -->
<constructor name="RoutingFailedException" public="true">
<parameter name="payload">
<type class="java.lang.String"/>
</parameter>
<parameter name="cause">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.routing.RoutingFailedException(java.lang.String) -->
<constructor name="RoutingFailedException" public="true">
<parameter name="cause">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.routing.RoutingFailedException(java.lang.String, java.lang.String, java.lang.Throwable) -->
<constructor name="RoutingFailedException" public="true">
<parameter name="payload">
<type class="java.lang.String"/>
</parameter>
<parameter name="cause">
<type class="java.lang.String"/>
</parameter>
<parameter name="throwable">
<type class="java.lang.Throwable"/>
</parameter>
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.routing.RoutingFailedException.getPayload() -->
<method name="getPayload"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

</class>
</bajadoc>
