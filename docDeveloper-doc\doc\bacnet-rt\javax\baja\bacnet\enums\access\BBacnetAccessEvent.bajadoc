<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.access.BBacnetAccessEvent" name="BBacnetAccessEvent" packageName="javax.baja.bacnet.enums.access" public="true" final="true">
<description>
BBacnetAccessEvent represents the Bacnet&#xa; Access Event enumeration.&#xa; &lt;p&gt;&#xa; BBacnetAccessEvent is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Joseph Chandler</tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;none&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>0</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;granted&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>1</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;muster&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>2</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;passbackDetected&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>3</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;duress&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>4</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;trace&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>5</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lockoutMaxAttempts&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>6</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lockoutOther&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>7</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lockoutRelinquished&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>8</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lockedByHigherPriority&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>9</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;outOfService&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>10</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;outOfServiceRelinquished&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>11</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accompanimentBy&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>12</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;authenticationFactorRead&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>13</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;authorizationDelayed&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>14</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;verificationRequired&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>15</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedDenyAll&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>128</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedUnknownCredential&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>129</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedAuthenticationUnavailable&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>130</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedAuthenticationFactorTimeout&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>131</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedIncorrectAuthenticationFactor&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>132</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedZoneNoAccessRights&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>133</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedPointNoAccessRights&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>134</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedNoAccessRights&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>135</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedOutOfTimeRange&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>136</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedThreatLevel&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>137</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedPassback&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>138</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedUnexpectedLocationUsage&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>139</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedMaxAttempts&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>140</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedLowerOccupancyLimit&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>141</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedUpperOccupancyLimit&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>142</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedAuthenticationFactorLost&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>143</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedAuthenticationFactorStolen&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>144</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedAuthenticationFactorDamaged&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>145</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedAuthenticationFactorDestroyed&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>146</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedAuthenticationFactorDisabled&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>147</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedAuthenticationFactorError&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>148</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedCredentialUnassigned&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>149</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedCredentialNotProvisioned&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>150</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedCredentialNotYetActive&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>151</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedCredentialExpired&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>152</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedCredentialManualDisable&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>153</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedCredentialLockout&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>154</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedCredentialMaxDays&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>155</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedCredentialMaxUses&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>156</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedCredentialInactivity&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>157</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedCredentialDisabled&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>158</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedNoAccompaniment&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>159</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedIncorrectAccompaniment&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>160</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedLockout&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>161</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedVerificationFailed&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>162</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedVerificationTimeout&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>163</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deniedOther&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>164</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.NONE -->
<field name="NONE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for none.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.GRANTED -->
<field name="GRANTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for granted.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.MUSTER -->
<field name="MUSTER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for muster.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.PASSBACK_DETECTED -->
<field name="PASSBACK_DETECTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for passbackDetected.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DURESS -->
<field name="DURESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for duress.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.TRACE -->
<field name="TRACE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for trace.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.LOCKOUT_MAX_ATTEMPTS -->
<field name="LOCKOUT_MAX_ATTEMPTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lockoutMaxAttempts.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.LOCKOUT_OTHER -->
<field name="LOCKOUT_OTHER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lockoutOther.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.LOCKOUT_RELINQUISHED -->
<field name="LOCKOUT_RELINQUISHED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lockoutRelinquished.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.LOCKED_BY_HIGHER_PRIORITY -->
<field name="LOCKED_BY_HIGHER_PRIORITY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lockedByHigherPriority.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.OUT_OF_SERVICE -->
<field name="OUT_OF_SERVICE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for outOfService.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.OUT_OF_SERVICE_RELINQUISHED -->
<field name="OUT_OF_SERVICE_RELINQUISHED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for outOfServiceRelinquished.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.ACCOMPANIMENT_BY -->
<field name="ACCOMPANIMENT_BY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accompanimentBy.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.AUTHENTICATION_FACTOR_READ -->
<field name="AUTHENTICATION_FACTOR_READ"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for authenticationFactorRead.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.AUTHORIZATION_DELAYED -->
<field name="AUTHORIZATION_DELAYED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for authorizationDelayed.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.VERIFICATION_REQUIRED -->
<field name="VERIFICATION_REQUIRED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for verificationRequired.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_DENY_ALL -->
<field name="DENIED_DENY_ALL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedDenyAll.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_UNKNOWN_CREDENTIAL -->
<field name="DENIED_UNKNOWN_CREDENTIAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedUnknownCredential.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_AUTHENTICATION_UNAVAILABLE -->
<field name="DENIED_AUTHENTICATION_UNAVAILABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedAuthenticationUnavailable.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_AUTHENTICATION_FACTOR_TIMEOUT -->
<field name="DENIED_AUTHENTICATION_FACTOR_TIMEOUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedAuthenticationFactorTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_INCORRECT_AUTHENTICATION_FACTOR -->
<field name="DENIED_INCORRECT_AUTHENTICATION_FACTOR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedIncorrectAuthenticationFactor.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_ZONE_NO_ACCESS_RIGHTS -->
<field name="DENIED_ZONE_NO_ACCESS_RIGHTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedZoneNoAccessRights.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_POINT_NO_ACCESS_RIGHTS -->
<field name="DENIED_POINT_NO_ACCESS_RIGHTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedPointNoAccessRights.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_NO_ACCESS_RIGHTS -->
<field name="DENIED_NO_ACCESS_RIGHTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedNoAccessRights.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_OUT_OF_TIME_RANGE -->
<field name="DENIED_OUT_OF_TIME_RANGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedOutOfTimeRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_THREAT_LEVEL -->
<field name="DENIED_THREAT_LEVEL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedThreatLevel.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_PASSBACK -->
<field name="DENIED_PASSBACK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedPassback.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_UNEXPECTED_LOCATION_USAGE -->
<field name="DENIED_UNEXPECTED_LOCATION_USAGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedUnexpectedLocationUsage.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_MAX_ATTEMPTS -->
<field name="DENIED_MAX_ATTEMPTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedMaxAttempts.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_LOWER_OCCUPANCY_LIMIT -->
<field name="DENIED_LOWER_OCCUPANCY_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedLowerOccupancyLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_UPPER_OCCUPANCY_LIMIT -->
<field name="DENIED_UPPER_OCCUPANCY_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedUpperOccupancyLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_AUTHENTICATION_FACTOR_LOST -->
<field name="DENIED_AUTHENTICATION_FACTOR_LOST"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedAuthenticationFactorLost.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_AUTHENTICATION_FACTOR_STOLEN -->
<field name="DENIED_AUTHENTICATION_FACTOR_STOLEN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedAuthenticationFactorStolen.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_AUTHENTICATION_FACTOR_DAMAGED -->
<field name="DENIED_AUTHENTICATION_FACTOR_DAMAGED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedAuthenticationFactorDamaged.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_AUTHENTICATION_FACTOR_DESTROYED -->
<field name="DENIED_AUTHENTICATION_FACTOR_DESTROYED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedAuthenticationFactorDestroyed.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_AUTHENTICATION_FACTOR_DISABLED -->
<field name="DENIED_AUTHENTICATION_FACTOR_DISABLED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedAuthenticationFactorDisabled.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_AUTHENTICATION_FACTOR_ERROR -->
<field name="DENIED_AUTHENTICATION_FACTOR_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedAuthenticationFactorError.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_CREDENTIAL_UNASSIGNED -->
<field name="DENIED_CREDENTIAL_UNASSIGNED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedCredentialUnassigned.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_CREDENTIAL_NOT_PROVISIONED -->
<field name="DENIED_CREDENTIAL_NOT_PROVISIONED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedCredentialNotProvisioned.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_CREDENTIAL_NOT_YET_ACTIVE -->
<field name="DENIED_CREDENTIAL_NOT_YET_ACTIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedCredentialNotYetActive.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_CREDENTIAL_EXPIRED -->
<field name="DENIED_CREDENTIAL_EXPIRED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedCredentialExpired.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_CREDENTIAL_MANUAL_DISABLE -->
<field name="DENIED_CREDENTIAL_MANUAL_DISABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedCredentialManualDisable.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_CREDENTIAL_LOCKOUT -->
<field name="DENIED_CREDENTIAL_LOCKOUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedCredentialLockout.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_CREDENTIAL_MAX_DAYS -->
<field name="DENIED_CREDENTIAL_MAX_DAYS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedCredentialMaxDays.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_CREDENTIAL_MAX_USES -->
<field name="DENIED_CREDENTIAL_MAX_USES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedCredentialMaxUses.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_CREDENTIAL_INACTIVITY -->
<field name="DENIED_CREDENTIAL_INACTIVITY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedCredentialInactivity.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_CREDENTIAL_DISABLED -->
<field name="DENIED_CREDENTIAL_DISABLED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedCredentialDisabled.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_NO_ACCOMPANIMENT -->
<field name="DENIED_NO_ACCOMPANIMENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedNoAccompaniment.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_INCORRECT_ACCOMPANIMENT -->
<field name="DENIED_INCORRECT_ACCOMPANIMENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedIncorrectAccompaniment.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_LOCKOUT -->
<field name="DENIED_LOCKOUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedLockout.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_VERIFICATION_FAILED -->
<field name="DENIED_VERIFICATION_FAILED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedVerificationFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_VERIFICATION_TIMEOUT -->
<field name="DENIED_VERIFICATION_TIMEOUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedVerificationTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DENIED_OTHER -->
<field name="DENIED_OTHER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deniedOther.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.none -->
<field name="none"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for none.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.granted -->
<field name="granted"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for granted.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.muster -->
<field name="muster"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for muster.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.passbackDetected -->
<field name="passbackDetected"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for passbackDetected.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.duress -->
<field name="duress"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for duress.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.trace -->
<field name="trace"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for trace.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.lockoutMaxAttempts -->
<field name="lockoutMaxAttempts"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for lockoutMaxAttempts.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.lockoutOther -->
<field name="lockoutOther"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for lockoutOther.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.lockoutRelinquished -->
<field name="lockoutRelinquished"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for lockoutRelinquished.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.lockedByHigherPriority -->
<field name="lockedByHigherPriority"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for lockedByHigherPriority.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.outOfService -->
<field name="outOfService"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for outOfService.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.outOfServiceRelinquished -->
<field name="outOfServiceRelinquished"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for outOfServiceRelinquished.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.accompanimentBy -->
<field name="accompanimentBy"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for accompanimentBy.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.authenticationFactorRead -->
<field name="authenticationFactorRead"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for authenticationFactorRead.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.authorizationDelayed -->
<field name="authorizationDelayed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for authorizationDelayed.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.verificationRequired -->
<field name="verificationRequired"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for verificationRequired.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedDenyAll -->
<field name="deniedDenyAll"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedDenyAll.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedUnknownCredential -->
<field name="deniedUnknownCredential"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedUnknownCredential.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedAuthenticationUnavailable -->
<field name="deniedAuthenticationUnavailable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedAuthenticationUnavailable.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedAuthenticationFactorTimeout -->
<field name="deniedAuthenticationFactorTimeout"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedAuthenticationFactorTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedIncorrectAuthenticationFactor -->
<field name="deniedIncorrectAuthenticationFactor"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedIncorrectAuthenticationFactor.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedZoneNoAccessRights -->
<field name="deniedZoneNoAccessRights"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedZoneNoAccessRights.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedPointNoAccessRights -->
<field name="deniedPointNoAccessRights"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedPointNoAccessRights.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedNoAccessRights -->
<field name="deniedNoAccessRights"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedNoAccessRights.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedOutOfTimeRange -->
<field name="deniedOutOfTimeRange"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedOutOfTimeRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedThreatLevel -->
<field name="deniedThreatLevel"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedThreatLevel.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedPassback -->
<field name="deniedPassback"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedPassback.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedUnexpectedLocationUsage -->
<field name="deniedUnexpectedLocationUsage"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedUnexpectedLocationUsage.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedMaxAttempts -->
<field name="deniedMaxAttempts"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedMaxAttempts.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedLowerOccupancyLimit -->
<field name="deniedLowerOccupancyLimit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedLowerOccupancyLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedUpperOccupancyLimit -->
<field name="deniedUpperOccupancyLimit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedUpperOccupancyLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedAuthenticationFactorLost -->
<field name="deniedAuthenticationFactorLost"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedAuthenticationFactorLost.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedAuthenticationFactorStolen -->
<field name="deniedAuthenticationFactorStolen"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedAuthenticationFactorStolen.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedAuthenticationFactorDamaged -->
<field name="deniedAuthenticationFactorDamaged"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedAuthenticationFactorDamaged.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedAuthenticationFactorDestroyed -->
<field name="deniedAuthenticationFactorDestroyed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedAuthenticationFactorDestroyed.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedAuthenticationFactorDisabled -->
<field name="deniedAuthenticationFactorDisabled"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedAuthenticationFactorDisabled.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedAuthenticationFactorError -->
<field name="deniedAuthenticationFactorError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedAuthenticationFactorError.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedCredentialUnassigned -->
<field name="deniedCredentialUnassigned"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedCredentialUnassigned.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedCredentialNotProvisioned -->
<field name="deniedCredentialNotProvisioned"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedCredentialNotProvisioned.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedCredentialNotYetActive -->
<field name="deniedCredentialNotYetActive"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedCredentialNotYetActive.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedCredentialExpired -->
<field name="deniedCredentialExpired"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedCredentialExpired.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedCredentialManualDisable -->
<field name="deniedCredentialManualDisable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedCredentialManualDisable.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedCredentialLockout -->
<field name="deniedCredentialLockout"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedCredentialLockout.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedCredentialMaxDays -->
<field name="deniedCredentialMaxDays"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedCredentialMaxDays.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedCredentialMaxUses -->
<field name="deniedCredentialMaxUses"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedCredentialMaxUses.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedCredentialInactivity -->
<field name="deniedCredentialInactivity"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedCredentialInactivity.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedCredentialDisabled -->
<field name="deniedCredentialDisabled"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedCredentialDisabled.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedNoAccompaniment -->
<field name="deniedNoAccompaniment"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedNoAccompaniment.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedIncorrectAccompaniment -->
<field name="deniedIncorrectAccompaniment"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedIncorrectAccompaniment.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedLockout -->
<field name="deniedLockout"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedLockout.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedVerificationFailed -->
<field name="deniedVerificationFailed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedVerificationFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedVerificationTimeout -->
<field name="deniedVerificationTimeout"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedVerificationTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.deniedOther -->
<field name="deniedOther"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description>
BBacnetAccessEvent constant for deniedOther.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessEvent"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessEvent.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
