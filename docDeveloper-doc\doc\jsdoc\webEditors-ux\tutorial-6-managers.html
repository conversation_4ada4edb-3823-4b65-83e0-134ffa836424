<!-- Htmldoc has been run -->
<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>webEditors Tutorial: Managers</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

<!-- Auto-generated style sheet link --><link rel='StyleSheet' href='module://bajaui/doc/style.css' type='text/css' />
<!-- Auto-generated js link for Activity Monitoring --><script type='text/javascript' src='module://web/rc/util/activityMonitor.js'></script>
<script type='text/javascript'>window.addEventListener('load', activityMonitor.start);</script>
</head>

<body>
<!-- Auto-generated Header NavBar --><p class="navbar">  <a href="/doc/index.html" class="navbar">Index</a> |  <a href="/doc/ui/uxMedia.html" class="navbar">Prev</a> |  <a href="/doc/jsdoc/driver-ux/index.html" class="navbar">Next</a></p>


<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">webEditors</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-nmodule_webEditors_rc_fe_baja_BaseEditor.html">nmodule/webEditors/rc/fe/baja/BaseEditor</a></li><li><a href="module-nmodule_webEditors_rc_fe_BaseWidget.html">nmodule/webEditors/rc/fe/BaseWidget</a></li><li><a href="module-nmodule_webEditors_rc_fe_fe.html">nmodule/webEditors/rc/fe/fe</a></li><li><a href="module-nmodule_webEditors_rc_fe_feDialogs.html">nmodule/webEditors/rc/fe/feDialogs</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_commands_MgrCommand.html">nmodule/webEditors/rc/wb/mgr/commands/MgrCommand</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_Manager.html">nmodule/webEditors/rc/wb/mgr/Manager</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html">nmodule/webEditors/rc/wb/mgr/MgrLearn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrStateHandler.html">nmodule/webEditors/rc/wb/mgr/MgrStateHandler</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrTypeInfo.html">nmodule/webEditors/rc/wb/mgr/MgrTypeInfo</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_IconMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinPropMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_NameMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyPathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_TypeMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/MgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrModel.html">nmodule/webEditors/rc/wb/mgr/model/MgrModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">nmodule/webEditors/rc/wb/table/model/Column</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_DisplayNameColumn.html">nmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_IconColumn.html">nmodule/webEditors/rc/wb/table/model/columns/IconColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_JsonObjectPropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_PropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_ToStringColumn.html">nmodule/webEditors/rc/wb/table/model/columns/ToStringColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentSource.html">nmodule/webEditors/rc/wb/table/model/ComponentSource</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentTableModel.html">nmodule/webEditors/rc/wb/table/model/ComponentTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">nmodule/webEditors/rc/wb/table/model/Row</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html">nmodule/webEditors/rc/wb/table/model/TableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_Table.html">nmodule/webEditors/rc/wb/table/Table</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeNodeRow.html">nmodule/webEditors/rc/wb/table/tree/TreeNodeRow</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeTableModel.html">nmodule/webEditors/rc/wb/table/tree/TreeTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_tree_TreeNode.html">nmodule/webEditors/rc/wb/tree/TreeNode</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="tutorials.list.html" class="dropdown-toggle" data-toggle="dropdown">Tutorials<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="tutorial-6-managers.html">Managers</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-12">
	
		<div id="main">
			<section class="tutorial-section">

<header>
    

    <h2>Managers</h2>
</header>

<article>
    <h1>Bajaux Manager Framework</h1>
<h2>Contents</h2>
<ul>
<li><a href="#intro">Introduction</a></li>
<li><a href="#manager">The Manager Type</a></li>
<li><a href="#model">MgrModel</a></li>
<li><a href="#sources">Component Sources</a></li>
<li><a href="#column">MgrColumn</a></li>
<li><a href="#rows">Rows</a></li>
<li><a href="#commands">Commands</a></li>
<li><a href="#state">Manager State</a></li>
<li><a href="#typeinfo">MgrTypeInfo</a></li>
<li><a href="#discovery">Discovery</a></li>
<li><a href="#subscribers">Subscribers</a></li>
<li><a href="#pointmgr">Point Manager</a></li>
<li><a href="#devicemgr">Device Manager</a></li>
<li><a href="#glossary">Glossary</a></li>
</ul>
<p><a name=intro></a></p>
<h2>Introduction</h2>
<p>For a number of years, Niagara's bajaui manager framework has provided drivers<br>
and other component containers with a common, consistent user interface<br>
framework that allows a user to add, edit and delete components in a station. It<br>
also provides a user with a familiar way to discover new items and add them to<br>
the station using a simple drag and drop process between two tables. The bajaux<br>
manager framework exists to provide a similar, consistent batch editing<br>
framework using HTML5 technologies, allowing the same style of configuration to<br>
be performed by a user in a browser environment, but without the need for a Java<br>
runtime.</p>
<p>The manager framework is based around the concept of a view containing one or<br>
more tables. Niagara drivers are consumers of the manager APIs, with views<br>
provided to allow a user to configure devices and points within a station. As an<br>
example of the manager views in use, a user may use a manager to discover the<br>
points in a remote device via a protocol implemented by the driver. Once<br>
complete, the view's discovery table will display the points found during the<br>
discovery. The user can then pick points to add to the station, with the manager<br>
containing code for creating and configuring the proxy extensions for the<br>
points. The manager can configure the component from the discovered item's<br>
properties, but also provides editing capabilities to allow a user to adjust the<br>
properties, as required.</p>
<p>As with the bajaui version, the bajaux manager framework is implemented around<br>
table widgets and their corresponding models. A bajaux <code>Manager</code> will, at a<br>
minimum, create a model for a main table, providing a number of columns that<br>
describe the values that should be shown and which of those values should be<br>
available for editing. In addition to this basic functionality, the manager may<br>
optionally provide the support for discovery of new items, which will require<br>
the creation of a second model for the discovery table. As with the Java<br>
version, these tables are arranged with the discovery table on top and the main<br>
table at the bottom. A user may choose an item from the upper discovery table,<br>
and then by dragging and dropping onto the lower database table, or by using the<br>
'Add' command, can edit the properties of a newly created <code>Component</code> before it<br>
is added to the station.</p>
<p class="warning">
Warning: This document describes an API that should currently be considered
experimental (_development_ level stability). The current feature set does
not have complete parity with the Java manager framework and the JavaScript API
may be subject to changes in future releases as more functionality is added. Any
third-party code written against this API may require changes to function
correctly with future releases of Niagara.
</p>
<p><a name=manager></a></p>
<h2>The <a href="module-nmodule_webEditors_rc_wb_mgr_Manager.html">Manager</a> Type</h2>
<p>The base of the UX manager framework is a JavaScript type called <code>Manager</code>. This<br>
is a bajaux <code>Widget</code> type that will create a child <code>Table</code> widget for the main<br>
table and load the model into it. In addition to this, it also creates a<br>
<code>CommandButtonGroup</code> widget for the manager's commands, which will be arranged<br>
horizontally along the bottom edge of the view. It also provides some<br>
functionality to save manager state temporarily and restore it again. The<br>
functionality provided by the base <code>Manager</code> type is relatively small; extra<br>
functionality is provided by derived classes and by the use of mixed-in<br>
JavaScript modules.</p>
<p>The <code>Manager</code> type can be accessed by requiring it from the <code>webEditors-ux</code><br>
module:</p>
<pre class="prettyprint source lang-javascript"><code>define(['nmodule/webEditors/rc/wb/mgr/Manager'], function (Manager) {
</code></pre>
<p>The constructor of the <code>Manager</code> type requires some parameters to be passed via<br>
object properties. The required parameters are as follows:</p>
<ul>
<li><code>moduleName</code> - a string with the name of the module that contains the new<br>
manager type.</li>
<li><code>keyName</code> - a string to identify the manager, typically the name of the type.</li>
</ul>
<p>These values should be specified when the constructor of the derived class calls<br>
the super class constructor.</p>
<pre class="prettyprint source lang-javascript"><code>var MyManager = function MyManager (params) {
  Manager.call(this, {
    moduleName: 'myModule',
    keyName: 'MyManager' // Typically the name of the type
  });
};
</code></pre>
<p>The key and module names are used for the purposes of deciding a lexicon to load<br>
strings from, when necessary, and are also used to define a key for storing<br>
manager state, thus requiring them to be unique for each concrete manager type.</p>
<p>As with any other bajaux <code>Widget</code> acting as a view on a component, the manager<br>
type must have a corresponding Java type implementing the <code>BIJavaScript</code><br>
interface (and in a manager's case should implement <code>BIFormFactorMax</code>, too), and<br>
the Java type should be registered as an agent on the relevant component type<br>
via the module-include.xml file.</p>
<p><a name=model></a></p>
<h2><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrModel.html">MgrModel</a></h2>
<p>Each concrete manager type must define a model for its main table. In bajaux,<br>
the <code>MgrModel</code> type provides the base class for main table models. Derived from<br>
<code>TableModel</code>, it adds some extra functionality for creating new component<br>
instances and adding them to the station. The <code>MgrModel</code> type's constructor<br>
requires:</p>
<ol>
<li>One or more <code>Column</code>s.</li>
<li>A <code>Component</code> or <code>ComponentSource</code> used to obtain the rows.</li>
<li>An array of <code>MgrTypeInfo</code> instances representing the types of any new objects<br>
that the manager may create.</li>
</ol>
<p>For any Manager, the <code>makeModel</code> method must be implemented. It should resolve<br>
to an instance of <code>MgrModel</code>, or a subclass of it.</p>
<pre class="prettyprint source lang-javascript"><code>///// MyManager.js:

/**
 * @param {baja.Component} component the component being loaded into the Manager
 * @returns {Promise.&lt;MgrModel>}
 */
MyManager.prototype.makeModel = function (component) {
  return MyMgrModel.make(component);
};
</code></pre>
<pre class="prettyprint source lang-javascript"><code>///// MyMgrModel.js:

var TYPES_MY_MANAGER_CAN_CREATE = [
  'control:BooleanWritable', 'control:NumericWritable'
];

//it is permitted, but not required, to subclass MgrModel.
var MyMgrModel = function MyMgrModel () {
  MgrModel.apply(this, arguments);
};
MyMgrModel.prototype = Object.create(MgrModel.prototype);
MyMgrModel.prototype.constructor = MyMgrModel;

/** @returns {Promise.&lt;MgrModel>} */
MyMgrModel.make = function (component) {
  return MgrTypeInfo.make(TYPES_MY_MANAGER_CAN_CREATE)
    .then(function (newTypes) {
      return new MyMgrModel({
          columns: makeColumns(),      // An array of columns for the model
          componentSource: component,  // The component being loaded into the manager or a component source
          newTypes: newTypes           // The types that the manager may create new instances of
      });
    });
};
</code></pre>
<p>In the above example, the <code>makeColumns</code> function would instantiate one or more<br>
<code>MgrColumn</code> types and return them in an array.</p>
<p>The <code>MgrModel</code> you create can be accessed as soon as <code>doLoad()</code> is called using<br>
the <code>getModel</code> method. If overriding <code>doLoad()</code>, be sure to call the super<br>
method as <code>Manager#doLoad()</code> provides important functionality.</p>
<pre class="prettyprint source lang-javascript"><code>MyManager.prototype.doLoad = function (component) {
  var model = this.getModel();
  model.getRows().forEach(function (row) { /* ... */ });
  
  // be sure to call super.
  return Manager.prototype.doLoad.apply(this, arguments);
};
</code></pre>
<p><a name=sources></a></p>
<h2><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentSource.html">Component Sources</a></h2>
<p>A manager model needs a way to obtain the initial set of <code>Row</code>s it should<br>
contain. In bajaux, when viewing components of a station, this is provided by an<br>
instance of a <code>ComponentSource</code>. The most common type of source used for manager<br>
models will be a <code>ContainerComponentSource</code>, which uses the child property<br>
values of a parent container as the subjects for the model's rows. If a<br>
<code>Component</code> is passed to the model constructor, rather than a <code>ComponentSource</code>,<br>
then a <code>ContainerComponentSource</code> will be created automatically as the default.<br>
In addition to returning the rows for the model, the source also has the<br>
responsibility for adding or removing items from the container.</p>
<p>One important feature of the <code>ContainerComponentSource</code> to note is the filter<br>
functionality. The source's default behavior is to return all visible children<br>
of the parent container (by checking each slot's flags). This may be appropriate<br>
in many cases, but in others it may be necessary to have finer control over<br>
which children are used for the table rows. The <code>ContainerComponentSource</code><br>
provides for this by taking an optional <code>filter</code> parameter in its constructor.</p>
<p>This filter may take one of two forms: an array of type specs to identify types<br>
that should be allowed for the table's rows, or a predicate function, called for<br>
each <code>Slot</code> on the parent container and receiving the slot as a parameter, which<br>
should return <code>true</code> for the children that should be included in the model.</p>
<p>Taking the example <code>MgrModel</code> defined above, it could be modified to<br>
filter out components via the array method:</p>
<pre class="prettyprint source lang-javascript"><code>var TYPES_MY_MANAGER_SHOULD_DISPLAY = [
  'control:ControlPoint', 'driver:PointFolder'
];

MyMgrModel.make = function (component) {
  return MgrTypeInfo.make(TYPES_MY_MANAGER_CAN_CREATE)
    .then(function (newTypes) {
      return new MyMgrModel({
        columns: makeColumns(),
        componentSource: new ContainerComponentSource({
          container: component,
          filter: TYPES_MY_MANAGER_SHOULD_DISPLAY
        }),
        newTypes: newTypes
      });
    });
};
</code></pre>
<p>It could also filter its rows by passing a function as the <code>filter</code> parameter:</p>
<pre class="prettyprint source lang-javascript"><code>function filterComponentsByTypeAndVisibility(prop) {
  var visible = !(prop.getFlags() & baja.Flags.HIDDEN),
      type = prop.getType();
      
  return visible && (type.is('control:ControlPoint') || type.is('driver:PointFolder'));
}

//...
  componentSource: new ContainerComponentSource({
    container: component,
    filter: filterComponentsByTypeAndVisibility
  })
//...
</code></pre>
<p><a name=column></a></p>
<h2><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html">MgrColumn</a></h2>
<p>A manager's table model must define one or more columns to define exactly what<br>
should be displayed for each row's subject and, if the column supports editing<br>
the value, how a modified value should be saved for a subject. All columns are<br>
derived from a base <code>Column</code> type. This is a generic table column type and is<br>
usable outside of manager views. For manager specific functionality, the<br>
<code>MgrColumn</code> type is used.</p>
<p>The <code>MgrColumn</code> type can be used in one of two ways:</p>
<ul>
<li>As the direct base class for a new type of manager column.</li>
<li>As a mixin to augment a more generic <code>Column</code> type with the functionality<br>
required to be used in a manager model.</li>
</ul>
<p>To use it as a direct base class, set up the prototype and apply the constructor<br>
in the usual way:</p>
<pre class="prettyprint source lang-javascript"><code>// Create a new manager column, directly inheriting from MgrColumn

var MyMgrColumn = function MyMgrColumn () {
  MgrColumn.apply(this, arguments);
};
MyMgrColumn.prototype = Object.create(MgrColumn.prototype);
MyMgrColumn.prototype.constructor = MyMgrColumn;
</code></pre>
<p>Alternatively, to apply it to another generic <code>Column</code> type that may have uses<br>
in other, non-manager tables, use the static <code>mixin</code> function:</p>
<pre class="prettyprint source lang-javascript"><code>// Create a new manager column type, derived from another non-manager column, mixing in MgrColumn

var MyOtherMgrColumn = function MyOtherMgrColumn () {
  FooColumn.apply(this, arguments);
};
MyOtherMgrColumn.prototype = Object.create(FooColumn.prototype);
MyOtherMgrColumn.prototype.constructor = MyOtherMgrColumn;

MgrColumn.mixin(MyOtherMgrColumn);
</code></pre>
<p>The <code>Column</code> base class has a <code>name</code> parameter in the constructor. The column<br>
names are used when setting a component's initial values from a discovered item.<br>
This will be described in the <a href="#discovery">discovery</a> section. The constructor<br>
may also be provided with a separate <code>displayName</code> parameter, to provide a<br>
localized user visible name for the column. If this parameter is not specified,<br>
the <code>name</code> will be used as the display name.</p>
<p>When creating a column, a manager may also wish to set the flags via the<br>
constructor. There are three flags that can be specified:</p>
<ul>
<li><code>Column.flags.EDITABLE</code> - Use this to indicate that the component editor<br>
should show the value for the column's value.</li>
<li><code>Column.flags.UNSEEN</code> - Use this to indicate that the column should not be<br>
visible by default. The user can choose to show it if they wish.</li>
<li><code>Column.flags.READONLY</code> - Use this to indicate that the column's value should<br>
be shown in the component editor, but should be readonly.</li>
</ul>
<p>These flags can be bitwise-combined as required for the column.</p>
<p><code>getValueFor</code> is an abstract method on the <code>Column</code> type that should return the<br>
appropriate value for a given row. All new manager columns must implement this<br>
method.</p>
<pre class="prettyprint source lang-javascript"><code>/**
 * Return this column's value for the given row.
 */
MyMgrColumn.prototype.getValueFor = function (row) {
  var componentInRow = row.getSubject();
  return getSomeValueFrom(componentInRow);
};
</code></pre>
<p>Another important method on the <code>Column</code> type is <code>buildCell</code>. This is called<br>
when the table is creating its DOM content. The first parameter is the row, the<br>
second is the jQuery object for the &lt;td&gt; element.</p>
<pre class="prettyprint source lang-javascript"><code>/**
 * Build the dom content for the given row.
 */
MyMgrColumn.prototype.buildCell = function (row, dom) {
  var value = this.getValueFor(row),
      text = getDisplayText(value);
      
  return Promise.resolve(dom.text(text));
};
</code></pre>
<p>As well as displaying a value, a new manager column may also want to provide<br>
support for editing a value. There are several steps involved in editing a<br>
column's value: configuring the field editor, validating a user's change and<br>
committing a change back to the row's subject.</p>
<p>The <code>getConfigFor</code> override point allows the column to set a configuration<br>
object for the field editor before it is built. The default implementation will<br>
coalesce multiple rows into a single value to be provided to the editor as the<br>
value. If a manager requires specialized behavior, it may override this method<br>
and provide the required properties that will be passed to the field editor via<br>
the <code>fe.makeFor()</code> method. See the <code><a href="module-nmodule_webEditors_rc_fe_fe.html">fe</a></code> documentation for further details of<br>
editor configuration.</p>
<p>Data validation is a task an editable manager column will almost certainly want<br>
to perform. The <code>mgrValidate</code> method can be overridden to have an opportunity to<br>
inspect the proposed changes for the model's rows and possibly reject them. The<br>
validation method will be passed the model and an array of proposed changes for<br>
the column. Each item in the array will either contain the proposed change to<br>
the row at the same index, or <code>null</code> if there is no change for that particular<br>
row. The method should inspect the values in the array and return a rejected<br>
<code>Promise</code> if any values do not pass the validation criteria.</p>
<pre class="prettyprint source lang-javascript"><code>/**
 * Validate the proposed changes to the rows.
 */
MyMgrColumn.prototype.mgrValidate = function (model, data, params) {
  for (var i = 0; i &lt; data.length; i++) {
    if (!isValid(data[i])) {
      return Promise.reject(new Error('invalid value'));
    }
  }
};
</code></pre>
<p>After the edits for a column have been validated, they must be committed back to<br>
the source. The <code>commit</code> method should take the given value and write it to the<br>
subject of the <code>Row</code>, returning a <code>Promise</code> that will resolve when the write is<br>
complete. The framework can support the use of batches when rows are being<br>
committed, which will enable several changes to be sent to the station in a<br>
single network call.</p>
<pre class="prettyprint source lang-javascript"><code>/**
 * Commit the changes back to the station.
 */
MyMgrColumn.prototype.commit = function (value, row, params) {
  var comp = row.getSubject(),
      batch = params && params.batch,
      progressCallback = params && params.progressCallback,
      promise = setValueOnComponent(comp, value, batch);
      
  if (progressCallback) { progressCallback(MgrColumn.COMMIT_READY); }

  return promise;
};
</code></pre>
<p>The <code>webEditors</code> module provides several pre-defined columns that may be useful<br>
for managers:</p>
<ul>
<li><code><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_NameMgrColumn.html">NameMgrColumn</a></code>: Used to display the name of a row's subject component.</li>
<li><code><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_IconMgrColumn.html">IconMgrColumn</a></code>: Used to display the icon of a row's subject component.</li>
<li><code><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PathMgrColumn.html">PathMgrColumn</a></code>: Used to display the slot path of a row's subject component.</li>
<li><code><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyMgrColumn.html">PropertyMgrColumn</a></code>: Used to create a cell's content from a direct property<br>
of a row's subject component.</li>
<li><code><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyPathMgrColumn.html">PropertyPathMgrColumn</a></code>: Used to create a cell's content from a descendant<br>
property of a row's subject component, for example a property on a proxy<br>
extension for a control point subject.</li>
</ul>
<p>See the API documentation for those types for further details on their<br>
implementation and usage.</p>
<p><a name=rows></a></p>
<h2><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">Rows</a></h2>
<p>Rows in the database table are represented by a <code>Row</code> type. A <code>Row</code> has a<br>
subject, which can be a JavaScript object of arbitrary type (it will normally be<br>
a reference to the <code>Component</code> represented by the row), an optional icon, and<br>
optional metadata. The <code>Row</code> is passed as a parameter to many of the <code>Column</code>'s<br>
methods, such as when building the DOM content for a cell in the table. In such<br>
a case, the column will call the row's <code>getSubject</code> method to access the<br>
component, whereupon it will use the subject's properties to generate the table<br>
cell content.</p>
<p>New instances of a <code>Row</code> are created by the model's <code>makeRow</code> function. Unlike<br>
columns, it will not normally be necessary to subclass the <code>Row</code> type. <code>Row</code>s<br>
allow keyed data to be temporarily stored against an instance. This could allow<br>
a manager to store a value it may want use later against a row, without having<br>
to subclass the <code>Row</code> type or add direct properties to the row object.</p>
<pre class="prettyprint source lang-javascript"><code>/**
 * Create a new row for the model.
 */
MyMgrModel.prototype.makeRow = function (subject) {
  var row = new Row(subject, subject.getNavIcon());
  row.data('my-meta-data', 'foo');  // Set some data to be used later
  return row;
};
</code></pre>
<p><a name=commands></a></p>
<h2>Commands</h2>
<p>Manager views use the bajaux <code>Command</code> and <code>CommandGroup</code> types to provide the<br>
commands for the buttons at the bottom of the view and on the toolbar. These are<br>
accessible via the command API provided the base <code>Widget</code> type.</p>
<p>On top of base command functionality, the manager framework provides an optional<br>
mixin called <code>MgrCommand</code>, which can be used to extend the base <code>Command</code> type<br>
with extra features. The <code>MgrCommand</code> mixin provides a function named<br>
<code>setShowInActionBar</code>, which can be used to indicate a command should be<br>
available in the toolbar, but not in the action bar at the bottom of the view.<br>
This would normally be used for commands that are not frequently used. The<br>
'discovery mode' command, which is used to toggle the visibility of the<br>
discovery table, is an example of the use of this mixin.</p>
<pre class="prettyprint source lang-javascript"><code>  // Just show the command on the toolbar, not in the action bar at the bottom of the view.
  myCommand.setShowInActionBar(false);
</code></pre>
<p><a name=state></a></p>
<h2>Manager State</h2>
<p>The Manager type provides the ability to save state data temporarily, so that<br>
certain aspects of the manager's state can be restored when hyperlinking back to<br>
a previously visited <code>Manager</code> view. The user's web browser will store the state<br>
in session storage, which will preserve the state for the duration of the<br>
session; after a browser or Workbench restart, the state will have been<br>
discarded.</p>
<p>By default a small amount of information is saved by the <code>Manager</code> base class.<br>
The manager will remember which columns are currently shown or hidden, and will<br>
store whether the discovery table is currently visible, if the manager has<br>
discovery support mixed in. The <code>Manager</code> class allows for a couple of override<br>
points that give a derived class the opportunity to save its own custom state,<br>
should it wish to. A typical example might be a driver saving discovery data,<br>
meaning that returning to the manager view for a particular network does not<br>
require the user to perform a re-discovery (which might perhaps be time<br>
consuming, depending on the nature of the system the driver is communicating<br>
with).</p>
<p class="warning">
The storage provided by the Manager class is intended for simple, transient
state for the user interface. The storage mechanism <strong>should not be
considered secure</strong> and must not be used to store sensitive information
such as passwords, private keys or authorization tokens.
</p>
<p>The first way a manager can add support for saving data is to add a function<br>
named <code>saveStateForOrd</code> to the <code>Manager</code>'s prototype. This is intended to be<br>
used in the situation where the state is only appropriate for a particular<br>
<code>Component</code> instance - the <code>Component</code>'s ORD will be keyed against the data.<br>
This might be used in a case where device specific data is to be cached, for<br>
example the discovery data for a device, which has no relevance for other<br>
devices of the same type. This function should return an object with properties<br>
that the manager wants to be stored:</p>
<pre class="prettyprint source lang-javascript"><code>/**
 * Return the state that should be saved, keyed against the current Manager view's ord base.
 */
MyManager.prototype.saveStateForOrd = function () {
  return {
    discoveryConfig: {
      discoverInputs: true,
      discoverOutputs: false
    }
  };
};
</code></pre>
<p>Another option is to add a function to the prototype called <code>saveStateForKey</code>.<br>
This allows data to be cached against a particular type of manager view, and can<br>
be restored for any instance of that manager. This uses the <code>moduleName</code> and<br>
<code>keyName</code> parameters passed to the constructor. Again, this should return an<br>
object containing the properties to be stored:</p>
<pre class="prettyprint source lang-javascript"><code>/**
 * Return that state that should be saved for any instances of this Manager type.
 */
MyManager.prototype.saveStateForKey = function () {
  return {
    discoveryTimeout: 10000
  };
}
</code></pre>
<p>A Manager that implements either of the above functions will also want to<br>
provide corresponding functions to restore that state when the view is reloaded.</p>
<p>If it provides a <code>saveStateForOrd</code> function, then a <code>Manager</code> should also<br>
provide a <code>restoreStateForOrd</code> function, too. This function's argument will be a<br>
deserialized object containing the state that had previously been saved. The<br>
function may optionally return a <code>Promise</code> if the restoration of the state<br>
requires some asynchronous work to be performed.</p>
<pre class="prettyprint source lang-javascript"><code>/**
 * Restore the Manager's state from the deserialized state object.
 */
MyManager.prototype.restoreStateForOrd = function (state) {
  var that = this;
  return that.doSomethingAsynchronous(state)
    .then(function () {
      that.restoreMyState(state);
    });
};
</code></pre>
<p>Likewise, a <code>saveStateForKey</code> function should have a corresponding<br>
<code>restoreStateForKey</code> function, which again will received a deserialized state<br>
object as the argument when it is called. This too may also optionally return a<br>
<code>Promise</code> if the restore is asynchronous.</p>
<pre class="prettyprint source lang-javascript"><code>/**
 * Restore the Manager's state from the deserialized state object.
 */
MyManager.prototype.restoreStateForKey = function (state) {
  // Restore the state, possibly returning a Promise...
};
</code></pre>
<p>The manager will call these restore functions during the <code>Widget</code>'s <code>load()</code><br>
process. It will be called at a point after the main table has been loaded with<br>
the model.</p>
<p>As part of the restore process there is an optional function that can be defined,<br>
<code>postRestore</code>.  This function can be used to do any post restore processing that might<br>
be necessary.  This will receive the full state object as the argument when it is called,<br>
and it may also optionally return a <code>Promise</code> if its process is asynchronous.</p>
<pre class="prettyprint source lang-javascript"><code>/**
 * Does any post processing that might be necessary in a restore
 */
MyManager.prototype.postRestore = function (state) {
  // Does any post restore processing that might be needed, possibly
  //  returning a Promise...
};
</code></pre>
<p><a name=typeinfo></a></p>
<h2><a href="module-nmodule_webEditors_rc_wb_mgr_MgrTypeInfo.html">MgrTypeInfo</a></h2>
<p>The bajaux manager views make use of a type named <code>MgrTypeInfo</code> for representing<br>
the information about new type instances that can be created by the manager.<br>
This is used to represent types that can be created by the 'New' command and<br>
also types that may be created from a particular discovery item. This is similar<br>
to the Java type of the same name used with bajaui manager views.</p>
<p>The <code>MgrTypeInfo</code> class provides a static <code>make()</code> method that can be used to<br>
create instances in one of several ways:</p>
<ul>
<li>From a type spec string or <code>baja.Type</code> (which can be either a single instance<br>
or an array)</li>
<li>From a type spec string or <code>baja.Type</code> to be used as a base type, which will<br>
return MgrTypeInfo instances for the concrete subclasses of that type.</li>
<li>From a <code>Component</code> instance to be used as a 'prototype' for the <code>MgrTypeInfo</code>.<br>
Note that this is not a prototype in the JavaScript Object prototype sense,<br>
but is used as a way to create a new instance by cloning an existing<br>
<code>Component</code> via its <code>newCopy()</code> method.</li>
</ul>
<p>The <code>MgrTypeInfo.make()</code> method returns a <code>Promise</code> that will resolve to a<br>
single <code>MgrTypeInfo</code> or array of <code>MgrTypeInfos</code>, depending on the input<br>
parameters. The most basic use is to provide a type or array of types in the<br>
<code>from</code> parameter:</p>
<pre class="prettyprint source lang-javascript"><code>  MgrTypeInfo.make({ from: [ 'control:BooleanWritable', 'control:NumericWritable' ] })
    .then(function (mgrInfos) {
      // Do something with the MgrTypeInfos
    });
</code></pre>
<p>To create an array of <code>MgrTypeInfo</code>s that represent all the concrete types of a<br>
specified base type, pass an additional boolean <code>concreteTypes</code> parameter to the<br>
<code>make</code> method:</p>
<pre class="prettyprint source lang-javascript"><code>  MgrTypeInfo.make({ from: 'driver:Device', concreteTypes: true })
    .then(function (mgrInfos) {
      // Do something with the MgrTypeInfos
    });
</code></pre>
<p>The BajaScript registry can be used in to create an array of <code>MgrTypeInfo</code>s for<br>
the agents registered on a particular type. The <code>make()</code> method will accept the<br>
result returned by the registry's <code>getAgents()</code> function.</p>
<pre class="prettyprint source lang-javascript"><code>baja.registry.getAgents(&quot;type:myModule:MyType&quot;)
  .then(function (agentInfos) {
    return MgrTypeInfo.make({
      from: agentInfos
    });
  })
  .then(function (mgrInfos) {
    // Do something with the MgrTypeInfos 
  });
</code></pre>
<p>When providing an array of Types or type specs to the <code>make()</code> function, the<br>
resulting array of <code>MgrTypeInfo</code>s will be in the same order as the corresponding<br>
types in the 'from' array. A static helper function is provided that can be used<br>
to sort an array of <code>MgrTypeInfo</code>s alphabetically according to their display<br>
names. This function can be passed directly to the <code>sort</code> function of the<br>
JavaScript <code>Array</code> type.</p>
<pre class="prettyprint source lang-javascript"><code>typeInfos.sort(MgrTypeInfo.BY_DISPLAY_NAME);
</code></pre>
<p><a name=discovery></a></p>
<h2><a href="module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html">Discovery</a></h2>
<p>A manager that wishes to support dynamic discovery of items can do so by<br>
requiring the <a href="module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html"><code>MgrLearn</code></a> mixin:</p>
<pre class="prettyprint source lang-javascript"><code>define([...
        'nmodule/webEditors/rc/wb/mgr/MgrLearn'], function (
        ...,
        addLearnSupport) {

</code></pre>
<p>and can then apply it to the manager instance in its constructor:</p>
<pre class="prettyprint source lang-javascript"><code>addLearnSupport(this);
</code></pre>
<p>A typical pattern for a <code>Manager</code>'s discovery process will be something like<br>
this:</p>
<ul>
<li>The user clicks the 'Discover' button, which calls the <code>doDiscover()</code> method<br>
on the manager.</li>
<li>The <code>doDiscover</code> method invokes an <code>Action</code> on the station, perhaps first<br>
displaying a dialog to obtain some configuration parameters, if required. This<br>
action will start a discovery job and return its ORD.</li>
<li>The ORD of the discovery job is then passed to the <code>setJob()</code> method on the<br>
<code>Manager</code>.</li>
<li>The <code>Manager</code> will then wait for the event to signal that discovery is<br>
complete.</li>
<li>Once the job is complete, the <code>Manager</code> will obtain the discovered items<br>
(typically by reading dynamic slots from the job) and use those to create<br>
<code>TreeNode</code>s for the discovery table.</li>
<li>The discovery table is then loaded with the new tree table nodes.</li>
</ul>
<p>When applying this mixin, a number of methods are required to be implemented by<br>
the concrete manager. These are:</p>
<ul>
<li><code>makeLearnModel()</code></li>
<li><code>doDiscover()</code></li>
<li><code>getTypesForDiscoverySubject()</code></li>
<li><code>getProposedValuesFromDiscovery()</code></li>
</ul>
<p>Each of these will described separately below.</p>
<h3><a href="module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html#makeLearnModel">makeLearnModel()</a></h3>
<p>The <code>makeLearnModel</code> method will be called to create a <code>TreeTableModel</code> for the<br>
discovery table. It should return a <code>Promise</code> that will resolve to a<br>
<code>TreeTableModel</code>. The use of a tree table allows a multilevel hierarchy to be<br>
represented in the discovery table; to show a set of objects at the first level<br>
of the tree, and the properties of those objects (name, value, description, etc)<br>
at the second level, for instance. As with the main table model, this requires<br>
defining a set of <code>Column</code>s. <code>TreeTableModel</code> class defines a static <code>make()</code><br>
method for creating an instance, which is returned via a <code>Promise</code>:</p>
<pre class="prettyprint source lang-javascript"><code>/**
 * Return a Promise that will resolve to the model for the table.
 */
MyManager.prototype.makeLearnModel = function () {
  return MyLearnModel.make();
};
</code></pre>
<pre class="prettyprint source lang-javascript"><code>/**
 * A static factory method for the learn model.
 * @returns {Promise}
 */
MyLearnModel.make = function () {
  return TreeTableModel.make({
    columns: createColumns()  // return an array of Columns
  });
};
</code></pre>
<p>The learn model may use whatever columns are appropriate. The use of<br>
<code>PropertyColumn</code>s to read values from <code>Component</code>s or <code>Struct</code>s added to the job<br>
as dynamic slots will likely be common pattern.</p>
<h3><a href="module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html#doDiscover">doDiscover()</a></h3>
<p>The <code>doDiscover</code> function is called in response to the user clicking the<br>
'Discover' button and its implementation should contain the functionality<br>
required to start an asynchronous discovery via some means. As described<br>
earlier, the most typical pattern will be for the function to invoke an <code>Action</code><br>
slot on a <code>Component</code> which will submit the appropriate discovery job on the<br>
station side, and then return the job's ORD as the return value of the <code>Action</code>.<br>
This ORD will then be set on the manager via the <code>setJob()</code> method, which will<br>
load the job component into the job bar at the top of the view, thus giving a<br>
progress bar indicator for the discovery, and will cause the manager to<br>
subscribe to the job, in order to be informed of its progress.</p>
<pre class="prettyprint source lang-javascript"><code>/**
 * Invoke an Action on the station that will submit a discovery job, then
 * set the returned ORD on the manager
 */
MyManager.prototype.doDiscover = function () {
  var that = this,
      model = that.value(),
      pointExt = model.getComponentSource().getContainer();
  
  return that.showDiscoveryConfigurationDialog()
    .then(function (config) {
      // invoke an action that will submit a job and return the ORD
      return pointExt.discoverPoints(config);
    })
    .then(function (ord) {
      ord = baja.Ord.make({
        base: baja.Ord.make('station:'),
        child: ord.relativizeToSession()
      });

      return that.setJob(ord);
    });
};

</code></pre>
<p>Once the job has completed, in either success, cancellation or failure, the code<br>
added by the mixin will emit a <code>jobcomplete</code> event, which the concrete manager<br>
can attach a handler function for:</p>
<pre class="prettyprint source lang-javascript"><code>var MyManager = function MyManager (params) {
  var that = this;
  Manager.call(that, { moduleName: 'myModule', keyName: 'MyManager' });

  // Add an event handler for the 'jobcomplete' event to know when discovery has
  // finished.
  that.on('jobcomplete', function (job) {
    that.updateLearnTableModelFromJob(job).catch(baja.error);
  });
};

/**
 * Called asynchronously after the job submitted by doDiscover() has
 * finished. This should get the items found in the discovery and
 * update the TreeNodes in the learn table.
 * @returns {Promise}
 */
MyManager.prototype.updateLearnTableModelFromJob = function (job) {
  var that = this;
  
  return job.loadSlots()
    .then(function () {
      var discoveries = job.getSlots()
        .is('myModule:MyDiscoveryPoint')
        .toValueArray();
      that.updateLearnTableModel(discoveries);
    });
};

/**
 * Function to update the model for the learn table with the discovered
 * items obtained from the job.
 */
MyManager.prototype.updateLearnTableModel = function (discoveries) {
  var model = this.getLearnModel(),
      root = model.getRootNode();
      
  // Update the model with the discoveries. Create TreeNodes with a value
  // returning the discovered item.
};

</code></pre>
<h3><a href="module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html#getTypesForDiscoverySubject">getTypesForDiscoverySubject()</a></h3>
<p>In short: what new things can I create from this discovered object?</p>
<p><code>getTypesForDiscoverySubject</code> is used when the user is creating a new component<br>
in the station from something that has been discovered and displayed in the<br>
discovery table. The function will take the value of the discovered object that<br>
the user wishes to add, and should return a single <code>MgrTypeInfo</code> or an array of<br>
<code>MgrTypeInfo</code>s, if the discovery item may have several possible types in the<br>
station. A typical example of multiple types would be the discovery of control<br>
point items. When a user drags a point with a boolean output value, the manager<br>
might return <code>BooleanWritable</code>, <code>BooleanPoint</code>, <code>StringWritable</code> and<br>
<code>StringPoint</code> as potential types. If returning more than one type, the most<br>
appropriate type should be the first item in the array.</p>
<pre class="prettyprint source lang-javascript"><code>/**
 * Return the type(s) suitable for the given discovery item. Some managers may
 * need to inspect the discovery value to return a suitable type or several
 * types.
 * @param {*} discoveredObject
 * @returns {Promise.&lt;MgrTypeInfo[]>}
 */
MyManager.prototype.getTypesForDiscoverySubject = function (discoveredObject) {
  if (discoveredObject.isBoolean()) {
    return MgrTypeInfo.make({ from: [
     'control:BooleanWritable', 'control:BooleanPoint' ]
    });
  } else {
    // handle other data types....
  }
};
</code></pre>
<h3><a href="module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html#getProposedValuesFromDiscovery">getProposedValuesFromDiscovery()</a></h3>
<p>In short: when adding a new point from a discovered object, how should that<br>
point be initially configured?</p>
<p><code>getProposedValuesFromDiscovery</code> is used to take values found during the<br>
discovery process (point labels or engineering units, for example) and use them<br>
to set the initial values for a new component. These values will be displayed in<br>
the batch editor dialog, allowing the user to further adjust them before the<br>
component is actually added to the station. The method implementation should<br>
return an object with a string property containing the proposed name (the <code>name</code><br>
property) and an object property containing any proposed values (the <code>values</code><br>
property). Each property of the <code>values</code> object should have a name that matches<br>
the name of a column in the main table model and its value should be the<br>
proposed value for that column. It is not necessary to propose a value for every<br>
editable column, as any properties on the created component that do not have<br>
proposals will simply use the default slot value.</p>
<pre class="prettyprint source lang-javascript"><code>/**
 * Return a proposed name for the new Component, and proposed initial values
 * for the 'id', 'enabled' and 'facets' columns.
 * @param {baja.Value} discoveredObject
 */
MyManager.prototype.getProposedValuesFromDiscovery = function (discoveredObject) {
  return {
    name: discoveredObject.getPointLabel(),
    values: {
      id: discoveredObject.getPointId(),
      enabled: true,
      facets: makeProposedFacets(discoveredObject.getEngineeringUnits())
    }
  };
};
</code></pre>
<h3><a href="module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html#isExisting">isExisting()</a></h3>
<p>In short: have I discovered this thing already?</p>
<p>Implementations of the four methods described above are mandatory for discovery<br>
support. The manager may also optionally provide a method on its prototype<br>
called <code>isExisting()</code> to check whether a given item found during discovery<br>
corresponds to a component already existing within the station. This is used to<br>
adjust the row's icon, to give the user a visual indication that the given item<br>
is already represented in the station's database. When invoked, the first<br>
parameter passed to the function will be the value of a node in the discovery<br>
table, the second parameter will be a component in the station. If the component<br>
corresponds to the discovery item, the function should return <code>true</code>.</p>
<pre class="prettyprint source lang-javascript"><code>/**
 * A discovered object is considered existing if its ID corresponds to the ID
 * of a proxy component already in the station.
 * @param {baja.Value} discoveredObject
 * @param {baja.Component} component
 */
MyManager.prototype.isExisting = function (discoveredObject, component) {
  return discoveredObject.getId() === component.getProxyExt().getPointId();
};
</code></pre>
<p>The mixin also adds several methods to the <code>Manager</code> that can be used by a<br>
concrete manager class.</p>
<ul>
<li><code>setJob()</code></li>
<li><code>getJob()</code></li>
<li><code>makeDiscoveryCommands()</code></li>
</ul>
<p>The <code>setJob</code> method is used to set a discovery job against the manager. It can<br>
be called with either a job Component or its ORD as the parameter. This will<br>
attach the job to the progress bar and cause the manager to listen for events on<br>
the job. See the <code>doDiscover</code> code above for an example of using this method.</p>
<p>The <code>getJob</code> method will return the job passed to <code>setJob</code>.</p>
<p>The <code>makeDiscoveryCommands</code> method is a helper that will create and return five<br>
new commands in an array. These commands can then be added to the <code>Manager</code>'s<br>
command group. The returned array of commands will contain:</p>
<ul>
<li><code>LearnModeCommand</code> - used to toggle the visibility of the discovery pane.</li>
<li><code>DiscoverCommand</code> - used to start a new discovery.</li>
<li><code>CancelDiscoverCommand</code> - used to cancel a currently running discovery.</li>
<li><code>AddCommand</code> - used to add a new component from a discovered item.</li>
<li><code>MatchCommand</code> - used to update an existing component from a discovered item.</li>
</ul>
<p><a name=subscribers></a></p>
<h2>Subscribers</h2>
<p>The bajaux module provides a subscriber mixin that can be used in conjunction<br>
with a manager view. It can be required as follows:</p>
<pre class="prettyprint source lang-javascript"><code>define(['bajaux/mixin/subscriberMixIn'], function (subscribable) {
</code></pre>
<p>and applied in the constructor:</p>
<pre class="prettyprint source lang-javascript"><code>subscribable(this);
</code></pre>
<p>This will subscribe to the view's component at the time it is loaded, and will<br>
unsubscribe at the time the view is destroyed. This mixin uses a regular<br>
BajaScript <code>Subscriber</code> by default. In some cases, subscription to the root<br>
container and one or more levels of components under the root may be necessary.<br>
The <code>webEditors-ux</code> module provides a depth subscriber that can be used for this<br>
purpose. It too needs to be required:</p>
<pre class="prettyprint source lang-javascript"><code>define(['bajaux/mixin/subscriberMixIn',
        'nmodule/webEditors/rc/fe/baja/util/DepthSubscriber'], function (
         subscribable,
         DepthSubscriber) {
</code></pre>
<p>Then to use it, create an instance and add it to the manager as a property named<br>
<code>$subscriber</code>, before the subscriber mixin is applied:</p>
<pre class="prettyprint source lang-javascript"><code>this.$subscriber = new DepthSubscriber(2);
subscribable(this);
</code></pre>
<p><a name=pointmgr></a></p>
<h2>Point Manager</h2>
<p>The <code>driver-ux</code> module part contains a type named <code>PointMgr</code> that can be used as<br>
the base class for point manager views. It also provides a corresponding base<br>
class for a point manager model. The model sets up an appropriate default filter<br>
that will pick out points and point folders for inclusion. Its default<br>
implementation has the ability to create a new <code>ControlPoint</code> type, configured<br>
with a proxy extension type specified in the constructor.</p>
<p>A new point manager can be created by extending the base class:</p>
<pre class="prettyprint source lang-javascript"><code>define(['nmodule/driver/rc/wb/mgr/PointMgr'], function (PointMgr) {

  /**
   * Constructor. This specifies a point folder type and a depth to use for a DepthSubscriber
   */
  var MyPointManager = function MyPointManager () {
    PointMgr.call(this, {
      moduleName: 'myModule',
      keyName: 'MyPointManager',
      folderType: 'myModule:MyPointFolder',
      subscriptionDepth: 3
    });
  };

  MyPointManager.prototype = Object.create(PointMgr.prototype);
  MyPointManager.prototype.constructor = MyPointManager;
</code></pre>
<p>For developer convenience, <code>PointMgr</code> provides folder support and a depth<br>
subscriber that can be configured just by passing a the <code>folderType</code> and<br>
<code>subscriptionDepth</code> properties in the constructor, as in the example above.</p>
<p>The concrete point manager should override the <code>makeModel</code> method to return a<br>
<code>PointMgrModel</code> or subclass. The point manager model has a static<br>
<code>getDefaultNewTypes</code> method; this can be called to get an array of <code>MgrTypeInfo</code><br>
types for the four control point data types (boolean, numeric, enum, string) in<br>
both the 'Point' and 'Writable' versions. This can be used to obtain the new<br>
types for the model.</p>
<pre class="prettyprint source lang-javascript"><code>require([...'nmodule/driver/rc/wb/mgr/PointMgrModel'], function (...PointMgrModel) {
  //...
  MyPointManager.prototype.makeModel = function (component) {
    return PointMgrModel.getDefaultNewTypes()
      .then(function (newTypes) {
        return new PointMgrModel({
          columns: makeColumns(),
          component: component,
          newTypes: newTypes,
          folderType: 'myModule:MyPointFolder',
          proxyExtType: 'myModule:MyProxyExt'
        });
      });
  };
});
</code></pre>
<p>As with all manager models, a concrete point manager model must provide the<br>
columns in the call to the base class constructor. The <code>PointMgrModel</code><br>
constructor takes an additional optional parameter named <code>proxyExtType</code>. If this<br>
parameter is specified, then the default implementation of the <code>newInstance</code><br>
method will create an instance of that proxy extension type and set it on any<br>
new instances of ControlPoint derived types it creates. If the <code>proxyExtType</code><br>
parameter is not specified in the constructor (perhaps the driver supports<br>
several proxy extension types), then the concrete model should probably override<br>
the <code>newInstance</code> method, and provide an implementation that will configure the<br>
appropriate proxy extension on the new point component.</p>
<p>Something that a concrete point manager might wish to override is the<br>
<code>makeCommands()</code> method. The default implementation will at minimum return an<br>
array containing the 'New' and 'Edit' commands. If a folder type was provided in<br>
the call to the base class constructor then a 'New Folder' command will be added<br>
too. If the manager supports discovery, then the discovery related commands will<br>
be added ('Add', 'Match', 'Toggle Learn Mode', 'Discover', 'Cancel'). This may<br>
be sufficient for some managers, but others may wish to override the method to<br>
add new commands or remove them.</p>
<p><a name=devicemgr></a></p>
<h2>Device Manager</h2>
<p>The driver module also provides a base class for device managers and their<br>
models too. The device manager constructor accepts similar parameters to the<br>
point manager: the module and key strings, the subscription depth for the<br>
subscriber and the optional folder type:</p>
<pre class="prettyprint source lang-javascript"><code>define(['nmodule/driver/rc/wb/mgr/DeviceMgr'], function (DeviceMgr) {

  /**
   * Constructor.
   */
  var MyDeviceManager = function MyDeviceManager () {
    DeviceMgr.call(this, {
      moduleName: 'myModule',
      keyName: 'MyDeviceManager',
      folderType: 'myModule:MyDeviceFolder',
      subscriptionDepth: 1
    });
  };

  MyDeviceManager.prototype = Object.create(DeviceMgr.prototype);
  MyDeviceManager.prototype.constructor = MyDeviceManager;
</code></pre>
<p>As with the point manager, the concrete device manager should provide a<br>
<code>makeModel</code> method that returns a subclass of <code>DeviceMgrModel</code>.</p>
<pre class="prettyprint source lang-javascript"><code>/**
 * Return a Promise that will resolve to the device model
 */
MyDeviceManager.prototype.makeModel = function (component) {
  return this.getNewTypes()
    .then(function (newTypes) {
      return new MyDeviceManagerModel({
        component: component,
        newTypes: newTypes
      });
    });
};
</code></pre>
<p>Like <code>PointMgr</code>, the base <code>DeviceMgr</code> class provides a <code>makeCommands</code> method<br>
(returning the same default command set as the point manager), which can be<br>
overridden to suit the concrete manager's needs.</p>
<p><a name=glossary></a></p>
<h2>Glossary</h2>
<table>
<thead>
<tr>
<th style="text-align:left">Term</th>
<th style="text-align:left">Description</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:left"><strong>Action Bar</strong></td>
<td style="text-align:left">The set of <code>Command</code> buttons arranged horizontally along the bottom edge of the <code>Manager</code></td>
</tr>
<tr>
<td style="text-align:left"><strong>Depth Subscriber</strong></td>
<td style="text-align:left">A <code>Subscriber</code> type, used to subscribe and component and its descendants down to a certain tree depth</td>
</tr>
<tr>
<td style="text-align:left"><strong>Discovery</strong></td>
<td style="text-align:left">The act of running an automated process to find potential subjects to be added to the database, for example querying a remote device to find all the data points it contains</td>
</tr>
<tr>
<td style="text-align:left"><strong>Job Bar</strong></td>
<td style="text-align:left">A widget displayed along the top edge of the manager, used to display the progress of the discovery job, and allow the user to cancel it</td>
</tr>
<tr>
<td style="text-align:left"><strong>Learn</strong></td>
<td style="text-align:left">Synonymous with 'Discovery'</td>
</tr>
<tr>
<td style="text-align:left"><strong>MgrTypeInfo</strong></td>
<td style="text-align:left">A class used by manager views to represent a type that the manager is capable of creating</td>
</tr>
</tbody>
</table>
</article>

</section>

		</div>
	</div>

	<div class="clearfix"></div>

	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	webEditors Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:29:00+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>



<!-- Auto-generated Footer NavBar --><p class="navbar">  <a href="/doc/index.html" class="navbar">Index</a> |  <a href="/doc/ui/uxMedia.html" class="navbar">Prev</a> |  <a href="/doc/jsdoc/driver-ux/index.html" class="navbar">Next</a></p>
<!-- Auto-generated copyright note --><p class='copyright'></p>
</body>
</html>
