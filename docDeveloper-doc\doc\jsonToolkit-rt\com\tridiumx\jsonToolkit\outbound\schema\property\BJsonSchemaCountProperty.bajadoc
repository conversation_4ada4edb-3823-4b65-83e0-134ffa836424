<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaCountProperty" name="BJsonSchemaCountProperty" packageName="com.tridiumx.jsonToolkit.outbound.schema.property" public="true">
<description>
A json key value pair where the value is an incrementing integer.&#xa;&#xa; The value increments 1 each time the json schema is generated.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<parameterizedType class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaProperty">
<args>
<type class="java.lang.Integer"/>
</args>
</parameterizedType>
</extends>
<property name="count" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;count&lt;/code&gt; property.
</description>
<tag name="@see">#getCount</tag>
<tag name="@see">#setCount</tag>
</property>

<action name="reset" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;reset&lt;/code&gt; action.
</description>
<tag name="@see">#reset()</tag>
</action>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaCountProperty() -->
<constructor name="BJsonSchemaCountProperty" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaCountProperty.getCount() -->
<method name="getCount"  public="true">
<description>
Get the &lt;code&gt;count&lt;/code&gt; property.
</description>
<tag name="@see">#count</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaCountProperty.setCount(int) -->
<method name="setCount"  public="true">
<description>
Set the &lt;code&gt;count&lt;/code&gt; property.
</description>
<tag name="@see">#count</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaCountProperty.reset() -->
<method name="reset"  public="true">
<description>
Invoke the &lt;code&gt;reset&lt;/code&gt; action.
</description>
<tag name="@see">#reset</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaCountProperty.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaCountProperty.started() -->
<method name="started"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaCountProperty.getJsonValue() -->
<method name="getJsonValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.Integer"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaCountProperty.doReset() -->
<method name="doReset"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unused&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaCountProperty.count -->
<field name="count"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;count&lt;/code&gt; property.
</description>
<tag name="@see">#getCount</tag>
<tag name="@see">#setCount</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaCountProperty.reset -->
<field name="reset"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;reset&lt;/code&gt; action.
</description>
<tag name="@see">#reset()</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaCountProperty.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
