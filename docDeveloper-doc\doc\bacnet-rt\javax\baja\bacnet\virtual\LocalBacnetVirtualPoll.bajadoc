<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.virtual.LocalBacnetVirtualPoll" name="LocalBacnetVirtualPoll" packageName="javax.baja.bacnet.virtual" public="true">
<description>
LocalBacnetVirtualPoll&#xa; This class is used for polling local virtual properties pointing to local&#xa; BACnet export properties.  It allows them to be handled in a similar fashion&#xa; to regular BACnet virtual properties that exist in remote devices.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">Nov 11, 2008</tag>
<tag name="@since">NiagaraAX 3.5</tag>
<extends>
<type class="javax.baja.bacnet.util.LocalBacnetPoll"/>
</extends>
<!-- javax.baja.bacnet.virtual.LocalBacnetVirtualPoll.getPollRate() -->
<method name="getPollRate"  protected="true">
<description/>
<return>
<type class="javax.baja.sys.BRelTime"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.LocalBacnetVirtualPoll.getThreadName() -->
<method name="getThreadName"  protected="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.LocalBacnetVirtualPoll.getPolledType() -->
<method name="getPolledType"  protected="true">
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.LocalBacnetVirtualPoll.poll(javax.baja.sys.BObject) -->
<method name="poll"  protected="true">
<description/>
<parameter name="o">
<type class="javax.baja.sys.BObject"/>
</parameter>
<return>
<type class="boolean"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

</class>
</bajadoc>
