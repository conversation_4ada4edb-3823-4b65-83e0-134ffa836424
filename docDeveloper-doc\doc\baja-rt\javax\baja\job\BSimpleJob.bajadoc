<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.job.BSimpleJob" name="BSimpleJob" packageName="javax.baja.job" public="true" abstract="true">
<description>
BSimpleJob provides a base class to use for simple &#xa; implementations which just launch a new background&#xa; thread for processing.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">22 Jul04</tag>
<tag name="@version">$Revision: 4$ $Date: 4/12/05 3:40:30 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.job.BJob"/>
</extends>
<!-- javax.baja.job.BSimpleJob() -->
<constructor name="BSimpleJob" public="true">
<description/>
</constructor>

<!-- javax.baja.job.BSimpleJob.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.job.BSimpleJob.doRun(javax.baja.sys.Context) -->
<method name="doRun"  public="true">
<description>
Launch a background thread which calls &lt;code&gt;run()&lt;/code&gt;.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.job.BSimpleJob.doCancel(javax.baja.sys.Context) -->
<method name="doCancel"  public="true">
<description>
Set the state to canceling, and call interrupt on the&#xa; background thread.  Note that Thread.interrupt usually only&#xa; works on threads blocked on IO or in a sleep.  Subclasses &#xa; should also periodically check isAlive() in case they don&#x27;t &#xa; receive the InteruptedException.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.job.BSimpleJob.run(javax.baja.sys.Context) -->
<method name="run"  public="true" abstract="true">
<description>
This is the method used to perform the job&#x27;s work.  It&#xa; is guaranteed to be called on a background thread.  If&#xa; an exception is raised then &lt;code&gt;failed()&lt;/code&gt; is&#xa; called automatically.  If the method returns successfully&#xa; then &lt;code&gt;success&lt;/code&gt; is called automatically.&#xa; Subclasses should also periodically check isAlive() in &#xa; case they don&#x27;t receive the InteruptedException.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.job.BSimpleJob.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
