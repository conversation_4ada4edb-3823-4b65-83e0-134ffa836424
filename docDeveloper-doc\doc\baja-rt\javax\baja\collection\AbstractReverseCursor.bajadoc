<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.collection.AbstractReverseCursor" name="AbstractReverseCursor" packageName="javax.baja.collection" public="true" abstract="true">
<description>
A general purpose implementation of the &lt;code&gt;<see ref="javax.baja.sys.Cursor">Cursor</see>&lt;/code&gt; interface that enforces closed semantics.&#xa; Subclasses provide implementations for &lt;code&gt;<see ref="javax.baja.collection.AbstractReverseCursor#advanceCursor()">#advanceCursor()</see>&lt;/code&gt;, &lt;code&gt;<see ref="javax.baja.collection.AbstractReverseCursor#doGet()">#doGet()</see>&lt;/code&gt;, and &lt;code&gt;<see ref="javax.baja.collection.AbstractReverseCursor#getReverseCursor()">#getReverseCursor()</see>&lt;/code&gt;.
</description>
<tag name="@author">&lt;a href=&#x22;mailto:<EMAIL>&#x22;&gt;Erik Test&lt;/a&gt;</tag>
<extends>
<parameterizedType class="javax.baja.collection.AbstractCursor">
<args>
<typeVariable class="E"/>
</args>
</parameterizedType>
</extends>
<typeParameters>
<typeVariable class="E">
</typeVariable>
</typeParameters>
<!-- javax.baja.collection.AbstractReverseCursor() -->
<constructor name="AbstractReverseCursor" public="true">
<description/>
</constructor>

<!-- javax.baja.collection.AbstractReverseCursor.getReverseCursor() -->
<method name="getReverseCursor"  public="true" abstract="true">
<description/>
<return>
<parameterizedType class="javax.baja.sys.Cursor">
<args>
<typeVariable class="E"/>
</args>
</parameterizedType>
</return>
</method>

</class>
</bajadoc>
