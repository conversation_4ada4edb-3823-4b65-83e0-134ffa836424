<!-- Htmldoc has been run -->
<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>export Index</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

<!-- Auto-generated style sheet link --><link rel='StyleSheet' href='module://bajaui/doc/style.css' type='text/css' />
<!-- Auto-generated js link for Activity Monitoring --><script type='text/javascript' src='module://web/rc/util/activityMonitor.js'></script>
<script type='text/javascript'>window.addEventListener('load', activityMonitor.start);</script>
</head>

<body>
<!-- Auto-generated Header NavBar --><p class="navbar">  <a href="/doc/index.html" class="navbar">Index</a> |  <a href="/doc/dashboard.html" class="navbar">Prev</a> |  <a href="/doc/gx.html" class="navbar">Next</a></p>


<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">export</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-nmodule_export_rc_ExportDestinationType.html">nmodule/export/rc/ExportDestinationType</a></li><li><a href="module-nmodule_export_rc_Transformer.html">nmodule/export/rc/Transformer</a></li><li><a href="module-nmodule_export_rc_TransformOperation.html">nmodule/export/rc/TransformOperation</a></li><li><a href="module-nmodule_export_rc_TransformOperationProvider.html">nmodule/export/rc/TransformOperationProvider</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	
	











	
	





    <section class="readme-section">
        <article><h1>export</h1>
<p>This module contains resources for performing export operations from the browser, and implementing your own export operations.</p>
<h2>Performing an export</h2>
<p>In Workbench or the HTML5 Hx Profile, there will be an Export button shown in the toolbar. There will also be an Export button in the right-click menu on exportable objects. Either will open the Export Dialog for the selected object.</p>
<p>In the Export Dialog, the Exporter list will show the available exporters on the current object or Widget. Configuration options will be shown for the selected exporter to allow you to choose how the export will be performed.</p>
<p>The Destination list will show the available destinations: where to send the exported data. Configuration options will also be shown for the selected export destination.</p>
<p>In addition, APIs are provided to allow you to implement new ways of extracting data out of your Niagara system.</p>
<h2>Implementing Export behavior in the Station and Workbench</h2>
<h3><code>BExporter</code></h3>
<p>To implement a new exporter, subclass <code>baja:Exporter</code> and implement the <code>export()</code> method. This method receives a Niagara object and converts it to a stream of text or binary data. If the exporter should work only in Workbench, implement <code>workbench:IWbViewExporter</code> and the exporter will export the current <code>workbench:WbView</code>.</p>
<p>See the Bajadoc for <code>baja:Exporter</code> for more details.</p>
<h3><code>BIExportDestinationType</code></h3>
<p>Workbench provides a set of export destinations out of the box, such as Save To File or View In Workbench. If you would like to implement a way of sending exported data to a different destination (e.g. Dropbox, Gmail, a REST endpoint), implement the <code>export:IExportDestinationType</code> interface. This provides a way to define which environments in which the destination is valid, and how the transformed data will be sent there.</p>
<p>See the Bajadoc for <code>export:IExportDestinationType</code> for more details.</p>
<h2>Implementing Export behavior in the browser</h2>
<h3><code>TransformOperationProvider</code></h3>
<p>All <code>baja:Exporter</code> classes that work in the Station will be accessible for use in the browser. Some <code>bajaux</code> Widgets may also export their own data in their own way, using JavaScript. To allow your Widget to hook into the Export Dialog to perform an export right from the browser, implement the <code><a href="module-nmodule_export_rc_TransformOperationProvider.html">TransformOperationProvider</a></code> interface on your top-level Widget. In this way, your Widget can define additional transform operations to be performed when the user clicks the Export button in the toolbar.</p>
<p>Your Widget instance can simply implement the <code><a href="module-nmodule_export_rc_TransformOperationProvider.html#getTransformOperations">getTransformOperations</a></code> method. Each <code><a href="module-nmodule_export_rc_TransformOperation.html">TransformOperation</a></code> encapsulates a transform operation: the object to be transformed, the transformer used to transform it, and the destination to which the transformed data will be sent. See the JSDocs for more details.</p>
<h3><code>ExportDestinationType</code></h3>
<p>Similar to <code>export:IExportDestinationType</code>, the <code><a href="module-nmodule_export_rc_ExportDestinationType.html">ExportDestinationType</a></code> JavaScript module allows transformed data to be sent to a different destination, such as a POST from the browser to a REST endpoint. To implement, simply subclass <code><a href="module-nmodule_export_rc_ExportDestinationType.html">ExportDestinationType</a></code>. See the JSDocs for more details.</p>
<p>For the destination to appear in the destination list in the Export Dialog, its existence must be registered with the framework. To do this, implement a Java class implementing <code>export:IJavaScriptExportDestination</code> and point it at your JavaScript module.</p></article>
    </section>







		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	export Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:56+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>



<!-- Auto-generated Footer NavBar --><p class="navbar">  <a href="/doc/index.html" class="navbar">Index</a> |  <a href="/doc/dashboard.html" class="navbar">Prev</a> |  <a href="/doc/gx.html" class="navbar">Next</a></p>
<!-- Auto-generated copyright note --><p class='copyright'></p>
</body>
</html>
