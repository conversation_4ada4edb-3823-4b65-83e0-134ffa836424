<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.inbound.routing.slot">
<description/>
<class packageName="com.tridiumx.jsonToolkit.inbound.routing.slot" name="BAddSlotDetail"><description>Provides action arguments when adding a new slot to a Json Router.</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.routing.slot" name="BAddSlotTypeEnum"><description>Types of slot that can be added to a Handler</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.routing.slot" name="DynamicSlotUtil" category="interface"><description>Behaviours commons to <PERSON><PERSON>&#x27;s needing readOnly + Summary slots for&#xa; routing/demuxing of payloads</description></class>
</package>
</bajadoc>
