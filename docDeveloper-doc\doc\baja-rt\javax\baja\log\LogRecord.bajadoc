<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.log.LogRecord" name="LogRecord" packageName="javax.baja.log" public="true">
<description>
LogRecord contains the information&#xa; associated with a logged message.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">9 Apr 00</tag>
<tag name="@version">$Revision: 5$ $Date: 4/27/11 12:51:28 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<tag name="@deprecated">as of Niagara 4.0.</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<!-- javax.baja.log.LogRecord(java.lang.String, int, java.lang.String, java.lang.Throwable) -->
<constructor name="LogRecord" public="true">
<parameter name="logName">
<type class="java.lang.String"/>
</parameter>
<parameter name="severity">
<type class="int"/>
</parameter>
<parameter name="message">
<type class="java.lang.String"/>
</parameter>
<parameter name="exception">
<type class="java.lang.Throwable"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.log.LogRecord(java.lang.String, int, java.lang.String, java.lang.Throwable, byte[]) -->
<constructor name="LogRecord" public="true">
<parameter name="logName">
<type class="java.lang.String"/>
</parameter>
<parameter name="severity">
<type class="int"/>
</parameter>
<parameter name="message">
<type class="java.lang.String"/>
</parameter>
<parameter name="exception">
<type class="java.lang.Throwable"/>
</parameter>
<parameter name="buffer">
<type class="byte" dimension="1"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.log.LogRecord.logName -->
<field name="logName"  public="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.log.LogRecord.severity -->
<field name="severity"  public="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.log.LogRecord.message -->
<field name="message"  public="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.log.LogRecord.exception -->
<field name="exception"  public="true" final="true">
<type class="java.lang.Throwable"/>
<description/>
</field>

<!-- javax.baja.log.LogRecord.timestamp -->
<field name="timestamp"  public="true" final="true">
<type class="long"/>
<description/>
</field>

<!-- javax.baja.log.LogRecord.buffer -->
<field name="buffer"  public="true" final="true">
<type class="byte" dimension="1"/>
<description/>
</field>

</class>
</bajadoc>
