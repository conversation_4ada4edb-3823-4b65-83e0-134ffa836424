<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BAlarmScheme" name="BAlarmScheme" packageName="javax.baja.alarm" public="true">
<description>
The alarm scheme provides access to the alarm database.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">22 Sep 2004</tag>
<tag name="@version">$Revision: 2$ $Date: 4/18/05 4:59:00 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.naming.BOrdScheme"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraSingleton"/>
</annotation>
<!-- javax.baja.alarm.BAlarmScheme() -->
<constructor name="BAlarmScheme" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.alarm.BAlarmScheme.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmScheme.parse(java.lang.String) -->
<method name="parse"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
This method gives scheme the chance to return a custom &#xa; subclass of OrdQuery with a scheme specific API.  The&#xa; default implementation returns an instance of BasicQuery.
</description>
<parameter name="queryBody">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdQuery"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmScheme.resolve(javax.baja.naming.OrdTarget, javax.baja.naming.OrdQuery) -->
<method name="resolve"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
This is the subclass hook for resolve after the &#xa; default implementation has mapped the ord to an &#xa; instanceof BSpace.
</description>
<parameter name="base">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<parameter name="query">
<type class="javax.baja.naming.OrdQuery"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdTarget"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmScheme.INSTANCE -->
<field name="INSTANCE"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BAlarmScheme"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmScheme.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
