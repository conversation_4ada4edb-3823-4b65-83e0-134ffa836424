<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.outbound.schema.query">
<description/>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.query" name="BJsonSchemaBoundQueryResult"><description>Binding to the result of a specific query, the resulting columns/values are rendered&#xa; into the json schema according to the chosen json style.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.query" name="BJsonSchemaQuery"><description>A query and associated result columns which is executed on each generation of the json schema.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.query" name="BJsonSchemaQueryFolder"><description>BJsonSchemaQueryFolder introduces an icon and allows easier parent / child legality checks</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.query" name="BRelativeHistoryQuery"><description>Experimental extension to a schema history designed to work in relative&#xa; schemas only.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.query" name="QueryResultHolder"><description>Cache of a bql/neql query result table.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.query" name="QueryRunner"><description>Utility class for running schema queries.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.query" name="QueryFailReasons" category="enum"><description>Enumeration of the various ways query execution can fail.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.query" name="QueryFailException" category="exception"><description>Checked exception for when schema queries fail too execute or timeout.</description></class>
</package>
</bajadoc>
