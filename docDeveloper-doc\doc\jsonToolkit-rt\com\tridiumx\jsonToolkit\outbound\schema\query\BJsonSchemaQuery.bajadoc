<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery" name="BJsonSchemaQuery" packageName="com.tridiumx.jsonToolkit.outbound.schema.query" public="true">
<description>
A query and associated result columns which is executed on each generation of the json schema.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="queryOrd" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;queryOrd&lt;/code&gt; property.
</description>
<tag name="@see">#getQueryOrd</tag>
<tag name="@see">#setQueryOrd</tag>
</property>

<property name="lastResultSize" flags="rtN">
<type class="int"/>
<description>
Slot for the &lt;code&gt;lastResultSize&lt;/code&gt; property.
</description>
<tag name="@see">#getLastResultSize</tag>
<tag name="@see">#setLastResultSize</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery() -->
<constructor name="BJsonSchemaQuery" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery.getQueryOrd() -->
<method name="getQueryOrd"  public="true">
<description>
Get the &lt;code&gt;queryOrd&lt;/code&gt; property.
</description>
<tag name="@see">#queryOrd</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery.setQueryOrd(javax.baja.naming.BOrd) -->
<method name="setQueryOrd"  public="true">
<description>
Set the &lt;code&gt;queryOrd&lt;/code&gt; property.
</description>
<tag name="@see">#queryOrd</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery.getLastResultSize() -->
<method name="getLastResultSize"  public="true">
<description>
Get the &lt;code&gt;lastResultSize&lt;/code&gt; property.
</description>
<tag name="@see">#lastResultSize</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery.setLastResultSize(int) -->
<method name="setLastResultSize"  public="true">
<description>
Set the &lt;code&gt;lastResultSize&lt;/code&gt; property.
</description>
<tag name="@see">#lastResultSize</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description/>
<parameter name="queryString">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery.execute(javax.baja.sys.BComplex, javax.baja.sys.Context) -->
<method name="execute"  public="true">
<description/>
<parameter name="base">
<type class="javax.baja.sys.BComplex"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<parameterizedType class="javax.baja.collection.BITable">
<args>
<wildcardType class="?">
</wildcardType>
</args>
</parameterizedType>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery.getLastResult() -->
<method name="getLastResult"  public="true">
<description/>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.QueryResultHolder"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery.setLastResult(com.tridiumx.jsonToolkit.outbound.schema.query.QueryResultHolder) -->
<method name="setLastResult"  public="true">
<description/>
<parameter name="lastResult">
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.QueryResultHolder"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery.hasLastResult() -->
<method name="hasLastResult"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery.clearLastResult() -->
<method name="clearLastResult"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery.queryOrd -->
<field name="queryOrd"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;queryOrd&lt;/code&gt; property.
</description>
<tag name="@see">#getQueryOrd</tag>
<tag name="@see">#setQueryOrd</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery.lastResultSize -->
<field name="lastResultSize"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lastResultSize&lt;/code&gt; property.
</description>
<tag name="@see">#getLastResultSize</tag>
<tag name="@see">#setLastResultSize</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery"/>
<description/>
</field>

</class>
</bajadoc>
