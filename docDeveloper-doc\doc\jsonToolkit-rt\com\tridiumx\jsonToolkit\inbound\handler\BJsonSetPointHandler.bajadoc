<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler" name="BJsonSetPointHandler" packageName="com.tridiumx.jsonToolkit.inbound.handler" public="true">
<description>
Routes incoming setpoint values to control writable points.&#xa;&#xa; {&#xa;   &#x22;%idKey%&#x22;:&#x22;x&#x22;,&#xa;   &#x22;%valueKey%&#x22;:y,&#xa;   (&#x22;%slotNameKey%&#x22;:&#x22;slotName&#x22;)&#xa; }&#xa;&#xa; This default impl looks up components by handle ord in the form:&#xa;&#xa; &#x22;%idKey%&#x22;:&#x22;323e&#x22;  (all versions)&#xa; or&#xa; &#x22;%idKey%&#x22;:&#x22;h:323e&#x22;  (since Niagara 4.11)&#xa;&#xa; Not currently supported:&#xa;&#xa; * override/duration&#xa; * status parameter
</description>
<tag name="@author">Jason Woollard / Nick Dodd</tag>
<extends>
<type class="com.tridiumx.jsonToolkit.inbound.handler.BJsonHandler"/>
</extends>
<property name="idKey" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;idKey&lt;/code&gt; property.
</description>
<tag name="@see">#getIdKey</tag>
<tag name="@see">#setIdKey</tag>
</property>

<property name="valueKey" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;valueKey&lt;/code&gt; property.
</description>
<tag name="@see">#getValueKey</tag>
<tag name="@see">#setValueKey</tag>
</property>

<property name="slotNameKey" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;slotNameKey&lt;/code&gt; property.
</description>
<tag name="@see">#getSlotNameKey</tag>
<tag name="@see">#setSlotNameKey</tag>
</property>

<property name="defaultWriteSlot" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;defaultWriteSlot&lt;/code&gt; property.
</description>
<tag name="@see">#getDefaultWriteSlot</tag>
<tag name="@see">#setDefaultWriteSlot</tag>
</property>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler() -->
<constructor name="BJsonSetPointHandler" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.getIdKey() -->
<method name="getIdKey"  public="true">
<description>
Get the &lt;code&gt;idKey&lt;/code&gt; property.
</description>
<tag name="@see">#idKey</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.setIdKey(java.lang.String) -->
<method name="setIdKey"  public="true">
<description>
Set the &lt;code&gt;idKey&lt;/code&gt; property.
</description>
<tag name="@see">#idKey</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.getValueKey() -->
<method name="getValueKey"  public="true">
<description>
Get the &lt;code&gt;valueKey&lt;/code&gt; property.
</description>
<tag name="@see">#valueKey</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.setValueKey(java.lang.String) -->
<method name="setValueKey"  public="true">
<description>
Set the &lt;code&gt;valueKey&lt;/code&gt; property.
</description>
<tag name="@see">#valueKey</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.getSlotNameKey() -->
<method name="getSlotNameKey"  public="true">
<description>
Get the &lt;code&gt;slotNameKey&lt;/code&gt; property.
</description>
<tag name="@see">#slotNameKey</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.setSlotNameKey(java.lang.String) -->
<method name="setSlotNameKey"  public="true">
<description>
Set the &lt;code&gt;slotNameKey&lt;/code&gt; property.
</description>
<tag name="@see">#slotNameKey</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.getDefaultWriteSlot() -->
<method name="getDefaultWriteSlot"  public="true">
<description>
Get the &lt;code&gt;defaultWriteSlot&lt;/code&gt; property.
</description>
<tag name="@see">#defaultWriteSlot</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.setDefaultWriteSlot(java.lang.String) -->
<method name="setDefaultWriteSlot"  public="true">
<description>
Set the &lt;code&gt;defaultWriteSlot&lt;/code&gt; property.
</description>
<tag name="@see">#defaultWriteSlot</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.routeValue(javax.baja.sys.BString, javax.baja.sys.Context) -->
<method name="routeValue"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="msg">
<type class="javax.baja.sys.BString"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.inbound.routing.RoutingFailedException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.lookupTarget(javax.baja.sys.BString, java.lang.String) -->
<method name="lookupTarget"  protected="true">
<description>
This base impl uses the handle number as the id, this may be in the form:&#xa; &#x22;h:323e&#x22; (since Niagara 4.11) or &#x22;323e&#x22; without the handle ord prefix.
</description>
<parameter name="msg">
<type class="javax.baja.sys.BString"/>
<description>
the json payload
</description>
</parameter>
<parameter name="id">
<type class="java.lang.String"/>
<description>
the uid of a component
</description>
</parameter>
<return>
<type class="javax.baja.control.BControlPoint"/>
<description>
the target to set the new value upon
</description>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.inbound.routing.RoutingFailedException"/>
<description>
if the target could not be found for any reason
</description>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.verifyNumericBounds(javax.baja.sys.BValue, javax.baja.sys.BFacets) -->
<method name="verifyNumericBounds"  public="true" static="true">
<description>
Verify that value is within min/max if specified.
</description>
<parameter name="v">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="facets">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.inbound.handler.SetpointValueRejectedException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.getRerunTriggers() -->
<method name="getRerunTriggers"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Property" dimension="1"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.idKey -->
<field name="idKey"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;idKey&lt;/code&gt; property.
</description>
<tag name="@see">#getIdKey</tag>
<tag name="@see">#setIdKey</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.valueKey -->
<field name="valueKey"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;valueKey&lt;/code&gt; property.
</description>
<tag name="@see">#getValueKey</tag>
<tag name="@see">#setValueKey</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.slotNameKey -->
<field name="slotNameKey"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;slotNameKey&lt;/code&gt; property.
</description>
<tag name="@see">#getSlotNameKey</tag>
<tag name="@see">#setSlotNameKey</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.defaultWriteSlot -->
<field name="defaultWriteSlot"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;defaultWriteSlot&lt;/code&gt; property.
</description>
<tag name="@see">#getDefaultWriteSlot</tag>
<tag name="@see">#setDefaultWriteSlot</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
