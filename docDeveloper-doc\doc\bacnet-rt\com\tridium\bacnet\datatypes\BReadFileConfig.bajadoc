<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.datatypes.BReadFileConfig" name="BReadFileConfig" packageName="com.tridium.bacnet.datatypes" public="true">
<description>
This class file specifies parameters to constrain a&#xa; read file request.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">13 Aug 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<property name="start" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;start&lt;/code&gt; property.&#xa; This is the starting byte position for stream-access files,&#xa; and the starting record number for record-access files.
</description>
<tag name="@see">#getStart</tag>
<tag name="@see">#setStart</tag>
</property>

<property name="count" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;count&lt;/code&gt; property.&#xa; This is the number of bytes to read for stream-access files,&#xa; and the number of records to read for record-access files.
</description>
<tag name="@see">#getCount</tag>
<tag name="@see">#setCount</tag>
</property>

<property name="appendToLocal" flags="hr">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;appendToLocal&lt;/code&gt; property.&#xa; If true, the file data read will be appended to whatever is&#xa; in the local file.  If false, the local file contents will be&#xa; replaced with whatever is read.
</description>
<tag name="@see">#getAppendToLocal</tag>
<tag name="@see">#setAppendToLocal</tag>
</property>

</class>
</bajadoc>
