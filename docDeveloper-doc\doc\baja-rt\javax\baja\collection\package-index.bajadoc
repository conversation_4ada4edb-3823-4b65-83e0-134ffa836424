<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="baja" runtimeProfile="rt" name="javax.baja.collection">
<description>
&lt;p&gt;Classes used to store collections.&lt;/p&gt;
</description>
<class packageName="javax.baja.collection" name="AbstractCursor"><description>A general purpose implementation of the &lt;code&gt;<see ref="javax.baja.sys.Cursor">Cursor</see>&lt;/code&gt; interface that enforces closed semantics.</description></class>
<class packageName="javax.baja.collection" name="AbstractReverseCursor"><description>A general purpose implementation of the &lt;code&gt;<see ref="javax.baja.sys.Cursor">Cursor</see>&lt;/code&gt; interface that enforces closed semantics.</description></class>
<class packageName="javax.baja.collection" name="AbstractRow"/>
<class packageName="javax.baja.collection" name="AbstractTableCursor"><description>A general implementation of a &lt;code&gt;<see ref="javax.baja.collection.TableCursor">TableCursor</see>&lt;/code&gt;.</description></class>
<class packageName="javax.baja.collection" name="BInMemoryTable"><description>An in memory table stores all rows of the table in memory as a list.</description></class>
<class packageName="javax.baja.collection" name="CompoundIterator"><description>A CompoundIterator concatentates two iterators.</description></class>
<class packageName="javax.baja.collection" name="CursorIterator"><description>Wraps a Cursor in an Iterator implementation.</description></class>
<class packageName="javax.baja.collection" name="FilteredIterator"><description>A FilteredIterator wraps an existing Iterator and filters the results&#xa; using a predicate.</description></class>
<class packageName="javax.baja.collection" name="PropertyMap"><description>PropertyMap represents a BComplex instance as a Map of Property to BValue.</description></class>
<class packageName="javax.baja.collection" name="PropertyMap.PropertySet"/>
<class packageName="javax.baja.collection" name="SlotCursorIterator"><description>Iterates property values in a SlotCursor</description></class>
<class packageName="javax.baja.collection" name="Tables"><description>Utilities for working with a &lt;code&gt;<see ref="javax.baja.collection.BITable">BITable</see>&lt;/code&gt;.</description></class>
<class packageName="javax.baja.collection" name="ValueCollection"><description>ValueCollection represents the property values of a BComplex instance as&#xa; a Collection.</description></class>
<class packageName="javax.baja.collection" name="BIRandomAccessTable" category="interface"><description>A &lt;code&gt;<see ref="javax.baja.collection.BITable">BITable</see>&lt;/code&gt; that supports random access to its cells.</description></class>
<class packageName="javax.baja.collection" name="BITable" category="interface"><description>BITable is a collection members are accessible by&#xa; row and whose members&#x27; elements are accessible by column.</description></class>
<class packageName="javax.baja.collection" name="Column" category="interface"><description>Column is a handle to a column in a BITable.</description></class>
<class packageName="javax.baja.collection" name="ColumnList" category="interface"><description>A list of columns for a &lt;code&gt;<see ref="javax.baja.collection.BITable">BITable</see>&lt;/code&gt;</description></class>
<class packageName="javax.baja.collection" name="Row" category="interface"/>
<class packageName="javax.baja.collection" name="TableCursor" category="interface"><description>TableCursor is a cursor for iterating through the rows in a table.</description></class>
</package>
</bajadoc>
