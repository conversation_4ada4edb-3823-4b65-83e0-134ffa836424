<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="com.tridium.bacnetAws.history.BBacnetEventLogImport" name="BBacnetEventLogImport" packageName="com.tridium.bacnetAws.history" public="true">
<description>
BBacnetEventLogImport defines an archive action for transferring&#xa; one event log from a remote Bacnet source to the local&#xa; destination.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">25 May 2010</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.5</tag>
<extends>
<type class="com.tridium.bacnet.history.BAbstractBacnetHistory"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<property name="localHistoryNames" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;localHistoryNames&lt;/code&gt; property.&#xa; List of history names currently used to store&#xa; imported records.
</description>
<tag name="@see">#getLocalHistoryNames</tag>
<tag name="@see">#setLocalHistoryNames</tag>
</property>

</class>
</bajadoc>
