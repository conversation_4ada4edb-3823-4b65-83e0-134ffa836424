<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetUnsigned" name="BBacnetUnsigned" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BBacnetUnsigned represents an unsigned value in a Bacnet property.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">22 Mar 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BSimple"/>
</extends>
<implements>
<type class="javax.baja.sys.BIComparable"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned(long) -->
<constructor name="BBacnetUnsigned" public="true">
<parameter name="value">
<type class="long"/>
<description>
the unsigned value
</description>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.make(long) -->
<method name="make"  public="true" static="true">
<description>
Factory method.
</description>
<parameter name="value">
<type class="long"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.compareTo(java.lang.Object) -->
<method name="compareTo"  public="true">
<description>
Compares this object with the specified object for&#xa; order. Returns a negative integer, zero, or a positive&#xa; integer as this object is less than, equal to, or greater&#xa; than the specified object.
</description>
<parameter name="obj">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description>
BBacnetUnsigned equality is based on the value.
</description>
<parameter name="obj">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.hashCode() -->
<method name="hashCode"  public="true">
<description>
Hash code.&#xa; The hash code for a BBacnetUnsigned is its unique id.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.encode(java.io.DataOutput) -->
<method name="encode"  public="true">
<description>
BBacnetUnsigned is serialized using writeLong().
</description>
<parameter name="out">
<type class="java.io.DataOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.decode(java.io.DataInput) -->
<method name="decode"  public="true">
<description>
BBacnetUnsigned is unserialized using readLong().
</description>
<parameter name="in">
<type class="java.io.DataInput"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.encodeToString() -->
<method name="encodeToString"  public="true">
<description>
Write the simple in text format.
</description>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.decodeFromString(java.lang.String) -->
<method name="decodeFromString"  public="true">
<description>
Read the simple from text format.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.getInt() -->
<method name="getInt"  public="true">
<description/>
<return>
<type class="int"/>
<description>
the integer value.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.getLong() -->
<method name="getLong"  public="true">
<description/>
<return>
<type class="long"/>
<description>
the unsigned value as a long.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.getUnsigned() -->
<method name="getUnsigned"  public="true">
<description/>
<return>
<type class="long"/>
<description>
the unsigned value as a long.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.getType() -->
<method name="getType"  public="true">
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.MAX_UNSIGNED_VALUE -->
<field name="MAX_UNSIGNED_VALUE"  public="true" static="true" final="true">
<type class="long"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.MIN_UNSIGNED_VALUE -->
<field name="MIN_UNSIGNED_VALUE"  public="true" static="true" final="true">
<type class="long"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.MAX_UNSIGNED16_VALUE -->
<field name="MAX_UNSIGNED16_VALUE"  public="true" static="true" final="true">
<type class="long"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
The default unsigned is 0.
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.MAX_UNSIGNED -->
<field name="MAX_UNSIGNED"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetUnsigned.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
