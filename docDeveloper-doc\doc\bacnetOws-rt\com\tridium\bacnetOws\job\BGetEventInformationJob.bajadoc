<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetOws" runtimeProfile="rt" qualifiedName="com.tridium.bacnetOws.job.BGetEventInformationJob" name="BGetEventInformationJob" packageName="com.tridium.bacnetOws.job" public="true">
<description>
BGetEventInformationJob gets event information from a BacnetWsDevice.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">20 Jul 2005</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.job.BDeviceManagerJob"/>
</extends>
<property name="serviceChoice" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;serviceChoice&lt;/code&gt; property.
</description>
<tag name="@see">#getServiceChoice</tag>
<tag name="@see">#setServiceChoice</tag>
</property>

</class>
</bajadoc>
