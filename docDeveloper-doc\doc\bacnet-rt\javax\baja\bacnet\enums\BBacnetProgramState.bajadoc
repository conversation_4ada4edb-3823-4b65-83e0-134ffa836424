<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetProgramState" name="BBacnetProgramState" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetProgramState represents the BACnetProgramState&#xa; enumeration.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">17 Oct 2005</tag>
<tag name="@since">Niagara 3.1</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;idle&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;loading&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;running&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;waiting&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;halted&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unloading&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetProgramState.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetProgramState"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetProgramState.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetProgramState"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetProgramState.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetProgramState.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetProgramState.IDLE -->
<field name="IDLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for idle.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramState.LOADING -->
<field name="LOADING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for loading.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramState.RUNNING -->
<field name="RUNNING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for running.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramState.WAITING -->
<field name="WAITING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for waiting.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramState.HALTED -->
<field name="HALTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for halted.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramState.UNLOADING -->
<field name="UNLOADING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unloading.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramState.idle -->
<field name="idle"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetProgramState"/>
<description>
BBacnetProgramState constant for idle.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramState.loading -->
<field name="loading"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetProgramState"/>
<description>
BBacnetProgramState constant for loading.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramState.running -->
<field name="running"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetProgramState"/>
<description>
BBacnetProgramState constant for running.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramState.waiting -->
<field name="waiting"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetProgramState"/>
<description>
BBacnetProgramState constant for waiting.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramState.halted -->
<field name="halted"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetProgramState"/>
<description>
BBacnetProgramState constant for halted.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramState.unloading -->
<field name="unloading"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetProgramState"/>
<description>
BBacnetProgramState constant for unloading.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramState.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetProgramState"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramState.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
