<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="andoverInfinity" runtimeProfile="rt" name="com.tridium.andoverInfinity.identify">
<description/>
<class packageName="com.tridium.andoverInfinity.identify" name="BInfinityDeviceDiscoverParams"><description>BInfinityDeviceDiscoverParams&#xa; &#xa; There are no params that a user needs to enter to do a learn, as it&#xa; is a well-defined keystroke sequence that never varies between systems.</description></class>
<class packageName="com.tridium.andoverInfinity.identify" name="BInfinityDeviceDiscoveryObject"><description>BInfinityDeviceDiscoveryObject&#xa; &#xa; This class extends &lt;code&gt;BInfinityDeviceId&lt;/code&gt; to provide an additional&#xa; property to display the &#x22;Online&#x22; or &#x22;Offline&#x22; status of &#xa; a device as it is displayed as a discovery object.</description></class>
<class packageName="com.tridium.andoverInfinity.identify" name="BInfinityDeviceId"/>
<class packageName="com.tridium.andoverInfinity.identify" name="BInfinityDiscoveryPointId"><description>BInfinityDiscoveryPointId extends BInfinityPointId to include properties&#xa; that are to be displayed in the learn view pane, but not in the point display&#xa; pane.</description></class>
<class packageName="com.tridium.andoverInfinity.identify" name="BInfinityPointDiscoverParams"><description>BInfinityPointDiscoverParams</description></class>
<class packageName="com.tridium.andoverInfinity.identify" name="BInfinityPointId"><description>BInfinityPointId adds a &#x22;rawResponse&#x22; field to the point display pane.</description></class>
<class packageName="com.tridium.andoverInfinity.identify" name="BInfinityReadPointParams"><description>BInfinityReadPointParams&#xa; &#xa; All we need to poll a point is the device name and the point name.</description></class>
<class packageName="com.tridium.andoverInfinity.identify" name="BInfinityWritePointParams"><description>BInfinityWritePointParams</description></class>
</package>
</bajadoc>
