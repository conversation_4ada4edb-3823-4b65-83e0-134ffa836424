<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.ext.BAlarmTimestamps" name="BAlarmTimestamps" packageName="javax.baja.alarm.ext" public="true">
<description>
BAlarmTimestamps
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">9 Nov 00</tag>
<tag name="@version">$Revision: 9$ $Date: 4/4/11 4:11:40 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="alarmTime" flags="rd">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;alarmTime&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmTime</tag>
<tag name="@see">#setAlarmTime</tag>
</property>

<property name="ackTime" flags="rd">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;ackTime&lt;/code&gt; property.
</description>
<tag name="@see">#getAckTime</tag>
<tag name="@see">#setAckTime</tag>
</property>

<property name="normalTime" flags="rd">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;normalTime&lt;/code&gt; property.
</description>
<tag name="@see">#getNormalTime</tag>
<tag name="@see">#setNormalTime</tag>
</property>

<property name="count" flags="rd">
<type class="int"/>
<description>
Slot for the &lt;code&gt;count&lt;/code&gt; property.
</description>
<tag name="@see">#getCount</tag>
<tag name="@see">#setCount</tag>
</property>

<!-- javax.baja.alarm.ext.BAlarmTimestamps() -->
<constructor name="BAlarmTimestamps" public="true">
<description/>
</constructor>

<!-- javax.baja.alarm.ext.BAlarmTimestamps.getAlarmTime() -->
<method name="getAlarmTime"  public="true">
<description>
Get the &lt;code&gt;alarmTime&lt;/code&gt; property.
</description>
<tag name="@see">#alarmTime</tag>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmTimestamps.setAlarmTime(javax.baja.sys.BAbsTime) -->
<method name="setAlarmTime"  public="true">
<description>
Set the &lt;code&gt;alarmTime&lt;/code&gt; property.
</description>
<tag name="@see">#alarmTime</tag>
<parameter name="v">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmTimestamps.getAckTime() -->
<method name="getAckTime"  public="true">
<description>
Get the &lt;code&gt;ackTime&lt;/code&gt; property.
</description>
<tag name="@see">#ackTime</tag>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmTimestamps.setAckTime(javax.baja.sys.BAbsTime) -->
<method name="setAckTime"  public="true">
<description>
Set the &lt;code&gt;ackTime&lt;/code&gt; property.
</description>
<tag name="@see">#ackTime</tag>
<parameter name="v">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmTimestamps.getNormalTime() -->
<method name="getNormalTime"  public="true">
<description>
Get the &lt;code&gt;normalTime&lt;/code&gt; property.
</description>
<tag name="@see">#normalTime</tag>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmTimestamps.setNormalTime(javax.baja.sys.BAbsTime) -->
<method name="setNormalTime"  public="true">
<description>
Set the &lt;code&gt;normalTime&lt;/code&gt; property.
</description>
<tag name="@see">#normalTime</tag>
<parameter name="v">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmTimestamps.getCount() -->
<method name="getCount"  public="true">
<description>
Get the &lt;code&gt;count&lt;/code&gt; property.
</description>
<tag name="@see">#count</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmTimestamps.setCount(int) -->
<method name="setCount"  public="true">
<description>
Set the &lt;code&gt;count&lt;/code&gt; property.
</description>
<tag name="@see">#count</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmTimestamps.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmTimestamps.alarmTime -->
<field name="alarmTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmTime&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmTime</tag>
<tag name="@see">#setAlarmTime</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmTimestamps.ackTime -->
<field name="ackTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;ackTime&lt;/code&gt; property.
</description>
<tag name="@see">#getAckTime</tag>
<tag name="@see">#setAckTime</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmTimestamps.normalTime -->
<field name="normalTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;normalTime&lt;/code&gt; property.
</description>
<tag name="@see">#getNormalTime</tag>
<tag name="@see">#setNormalTime</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmTimestamps.count -->
<field name="count"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;count&lt;/code&gt; property.
</description>
<tag name="@see">#getCount</tag>
<tag name="@see">#setCount</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmTimestamps.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
