<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>webEditors Module: nmodule/webEditors/rc/wb/tree/TreeNode</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">webEditors</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-nmodule_webEditors_rc_fe_baja_BaseEditor.html">nmodule/webEditors/rc/fe/baja/BaseEditor</a></li><li><a href="module-nmodule_webEditors_rc_fe_BaseWidget.html">nmodule/webEditors/rc/fe/BaseWidget</a></li><li><a href="module-nmodule_webEditors_rc_fe_fe.html">nmodule/webEditors/rc/fe/fe</a></li><li><a href="module-nmodule_webEditors_rc_fe_feDialogs.html">nmodule/webEditors/rc/fe/feDialogs</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_commands_MgrCommand.html">nmodule/webEditors/rc/wb/mgr/commands/MgrCommand</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_Manager.html">nmodule/webEditors/rc/wb/mgr/Manager</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html">nmodule/webEditors/rc/wb/mgr/MgrLearn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrStateHandler.html">nmodule/webEditors/rc/wb/mgr/MgrStateHandler</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrTypeInfo.html">nmodule/webEditors/rc/wb/mgr/MgrTypeInfo</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_IconMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinPropMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_NameMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyPathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_TypeMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/MgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrModel.html">nmodule/webEditors/rc/wb/mgr/model/MgrModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">nmodule/webEditors/rc/wb/table/model/Column</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_DisplayNameColumn.html">nmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_IconColumn.html">nmodule/webEditors/rc/wb/table/model/columns/IconColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_JsonObjectPropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_PropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_ToStringColumn.html">nmodule/webEditors/rc/wb/table/model/columns/ToStringColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentSource.html">nmodule/webEditors/rc/wb/table/model/ComponentSource</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentTableModel.html">nmodule/webEditors/rc/wb/table/model/ComponentTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">nmodule/webEditors/rc/wb/table/model/Row</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html">nmodule/webEditors/rc/wb/table/model/TableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_Table.html">nmodule/webEditors/rc/wb/table/Table</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeNodeRow.html">nmodule/webEditors/rc/wb/table/tree/TreeNodeRow</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeTableModel.html">nmodule/webEditors/rc/wb/table/tree/TreeTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_tree_TreeNode.html">nmodule/webEditors/rc/wb/tree/TreeNode</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="tutorials.list.html" class="dropdown-toggle" data-toggle="dropdown">Tutorials<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="tutorial-6-managers.html">Managers</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: nmodule/webEditors/rc/wb/tree/TreeNode</h1>
<section>

<header>
    
        
            
        
    
</header>


<article>
    <div class="container-overview">
    
        

        
            
<hr>
<dt>
    <h4 class="name" id="module:nmodule/webEditors/rc/wb/tree/TreeNode"><span class="type-signature"></span>new (require("nmodule/webEditors/rc/wb/tree/TreeNode"))(name, display [, kids])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>API Status: <strong>Development</strong></p>
<p>Represents a single node in a tree.</p>
<p>One node has a number of different properties, as well as a reference to<br>
a backing value this node represents. This backing value could be a<br>
nav node on a station, a file or folder on the file system, etc.</p>
<p>It also maintains a list of children. Note that this list of children will<br>
be lazily, asynchronously requested the first time it is loaded. After<br>
that, the list of children must be kept up to date using the parent<br>
node's mutators (added/removed/etc.).</p>
<p>Please note that any child nodes added to a parent node effectively become<br>
the parent node's &quot;property&quot; and are subject to alteration by the parent.<br>
If the parent node is activated, the child nodes will likewise be<br>
activated, and same for destroying.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>the node name</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>display</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>the node display</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>kids</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="module-nmodule_webEditors_rc_wb_tree_TreeNode.html">module:nmodule/webEditors/rc/wb/tree/TreeNode</a>></span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>an<br>
array of child nodes</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    
        <dt class="mixes">Mixes In:</dt>

        <dd class="mixes"><ul>
        
            <li>tinyevents</li>
        
        </ul></dd>
    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id=".BY_NODE_NAME"><span class="type-signature">&lt;static> </span>BY_NODE_NAME()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Pass this to <code>#reorder</code> to sort all tree nodes by name.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="$loadKids"><span class="type-signature">&lt;abstract> </span>$loadKids( [params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Performs a one-time, asynchronous load of child nodes. On a vanilla<br>
<code>TreeNode</code>, this does nothing but resolve the array of child nodes passed<br>
into the constructor. In subclasses, this should be overridden to perform<br>
any network calls or other asynchronous behavior to load child nodes.</p>
<p>This method is intended to be overridden by subclasses, but not called<br>
directly. It will automatically be used the first time <code>getKids()</code> is<br>
called.</p>
<p>After <code>getKids()</code> is called for the first time, any updates or changes to<br>
the list of nodes should only be done through the <code>add()</code>, <code>remove()</code>,<br>
and other mutator methods.</p>
<p>Do not set the parent of the child nodes created by this method - they<br>
will automatically be parented when <code>getKids()</code> is called.</p>
<p><em>Important contractual note:</em> in some cases, the async operation to load<br>
kids can be batched together if loading a number of nodes at once. If<br>
<code>$loadKids</code> receives a <code>Batch</code> object, it is obligated to ensure that any<br>
<code>progressCallback</code> param passed in will be called with a <code>commitReady</code><br>
progress event to notify the caller that the batch is ready to be committed.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last">
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>batch</code></td>
            

            <td class="type">
            
                
<span class="param-type">baja.comm.Batch</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>optional Batch that may be used<br>
when loading multiple tree nodes. See method description for contract.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>progressCallback</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>optional function that will<br>
receive progress notifications during the load process.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved when all child nodes<br>
have been loaded. It should be resolved with an array of <code>TreeNode</code><br>
instances.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="activate"><span class="type-signature"></span>activate()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Activates the node and all of its child nodes. This method works very<br>
similarly to <code>Widget#initialize()</code> in that it delegates the implementation<br>
of the destruction of each individual node to <code>doActivate()</code>.</p>
<p>Note that child nodes will <em>not</em> be activated if they are not yet loaded.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved when this node and all<br>
child nodes (if loaded) have been activated</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="add"><span class="type-signature"></span>add(kid)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Adds a child node to the end of this parent's list of child nodes. The<br>
child will automatically be parented when it is set. If this node has<br>
been activated, the child node will likewise be activated when it is<br>
added.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>kid</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_tree_TreeNode.html">module:nmodule/webEditors/rc/wb/tree/TreeNode</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved when the child node is<br>
added, or rejected if the child node is already parented, if the list<br>
of children is not yet loaded (<code>getKids()</code> not yet called), or an existing<br>
child with a duplicate name is found</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="destroy"><span class="type-signature"></span>destroy()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Destroys the node and all of its child nodes. This method works very<br>
similarly to <code>Widget#destroy()</code> in that it delegates the implementation<br>
of the destruction of each individual node to <code>doDestroy()</code>.</p>
<p>Note that child nodes will <em>not</em> be destroyed if they are not yet loaded.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved when this node and all<br>
child nodes (if loaded) have been destroyed</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="doActivate"><span class="type-signature"></span>doActivate()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Implementation of <code>activate()</code>. This method should acquire any resources<br>
the node needs to function properly - registering event handlers,<br>
subscribing components, etc. Ensure that all resources acquired are<br>
properly released in <code>doDestroy()</code>. By default, does nothing.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved when activation is<br>
complete - or return undefined if no asynchronous work needs to be done</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="doDestroy"><span class="type-signature"></span>doDestroy()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Implementation of <code>destroy()</code>. This method should release any resources<br>
acquired by the node during <code>doActivate()</code>. By default, does nothing.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved when destruction is<br>
complete - or return undefined if no asynchronous work needs to be done</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="doDrop"><span class="type-signature"></span>doDrop(values)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>A tree node that returned <code>true</code> from <code>isDropTarget</code> can then take an array<br>
of values to perform the drop action.</p>
<p>By default, this function does nothing.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>values</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array</span>



            
            </td>

            

            

            <td class="description last"><p>the values being dropped onto this node</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved when the drop operation<br>
completes, or rejected if the given array does not hold valid data<br>
to perform a drop operation.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="equals"><span class="type-signature"></span>equals(value)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Test to see if this node is equivalent to some value. By default, a node<br>
is equivalent only to itself.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>value</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getDescendent"><span class="type-signature"></span>getDescendent(names)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Retrieves a child by traversing the tree using the names provided. Each name<br>
will traverse a level deeper into the tree.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>names</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;string></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved with the descendent node, or<br>
<code>undefined</code> if not found</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getFullPath"><span class="type-signature"></span>getFullPath()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>The full path of names leading to this node, beginning from the parent<br>
node. Since names must be unique among siblings, each node in a tree will<br>
therefore have a unique full path.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>an array of node names, with the name of the<br>
root node first and this node last</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array.&lt;String></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getIcon"><span class="type-signature"></span>getIcon()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return a list of URIs to image files that represent a display icon for this<br>
node. Typically, this will only return zero or one URI, but may return<br>
several if the node's icon should be layered or have a &quot;badge&quot; applied. By<br>
default, this just returns an empty array.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>an array of URIs to image files</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array.&lt;String></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getKid"><span class="type-signature"></span>getKid(name)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Retrieves a child node by name. If child nodes are not yet loaded, they<br>
will be upon calling this method.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved with the child node<br>
with the given name, or <code>undefined</code> if not found</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getKids"><span class="type-signature"></span>getKids( [params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Resolves all child nodes of this node. If they have already been loaded,<br>
they will be resolved immediately, otherwise they will be asynchronously<br>
loaded in a one-time operation. (The children will not be loaded if the<br>
node was destroyed first.)</p>
<p>After <code>getKids()</code> is called for the first time, any updates or changes to<br>
the list of nodes should only be done through the <code>add()</code>, <code>remove()</code>,<br>
and other mutator methods.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>params object to be passed to <code>$loadKids</code>. This<br>
should be provided if you are calling <code>getKids</code> without being sure you have<br>
called <code>$loadKids</code> first.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved with an array of child<br>
<code>TreeNode</code>s</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getName"><span class="type-signature"></span>getName()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>The name of this node. If this node has siblings, note that names must<br>
be unique among all sibling nodes.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getParent"><span class="type-signature"></span>getParent()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>The parent node. If the node is unparented, will return <code>null</code>.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_tree_TreeNode.html">module:nmodule/webEditors/rc/wb/tree/TreeNode</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isDraggable"><span class="type-signature"></span>isDraggable()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if this tree node is eligible to begin a drag operation.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>false by default</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isDropTarget"><span class="type-signature"></span>isDropTarget()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>A tree node has the option of accepting data from a drag and drop<br>
operation. If a node is to accept drag and drop, this function should be<br>
overridden to examine the currently loaded value (if appropriate) and<br>
determine if it can accept a drop operation.</p>
<p>It is up to the <code>NavTree</code> that holds this node to <code>return false</code> from the<br>
event handler, apply any CSS styles, etc.</p>
<p>Naturally, any node that implements this function should also implement<br>
<code>doDrop</code> to perform the requested drop operation.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p><code>false</code> by default</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isSelectable"><span class="type-signature"></span>isSelectable()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Override this method to return <code>false</code> to prevent this node from being<br>
selected in the tree.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p><code>true</code> by default.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="mayHaveKids"><span class="type-signature">&lt;abstract> </span>mayHaveKids()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return false if you know for a fact that this node has no child nodes.</p>
<p>Why is this different from the <code>bajaui</code> implementation which declares a<br>
<code>getChildCount()</code> method? Remember that retrieving child nodes is<br>
asynchronous, so it's not always possible to count them synchronously.<br>
This function will mainly serve as a hint to UI widgets whether to show<br>
an expander for this node, with the understanding that <code>getKids()</code> may<br>
still resolve zero nodes, even if this function returned true.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="remove"><span class="type-signature"></span>remove(kid)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Removes a child node from this parent's list of child nodes. Note that<br>
child's <code>destroy()</code> will be called when it is removed.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>kid</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_tree_TreeNode.html">module:nmodule/webEditors/rc/wb/tree/TreeNode</a></span>
|

<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>the<br>
node to remove, or the name of the node to remove</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved with the<br>
removed/destroyed child, or rejected if the given node or node name is not<br>
found in the existing list of children, or if the list of children is not<br>
yet loaded (<code>getKids()</code> not yet called)</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="rename"><span class="type-signature"></span>rename(name, newName)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Renames one child node.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>the name of the existing child node to rename</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>newName</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>the new name of the child node</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved when the child is renamed,<br>
or rejected if the child was not found, if the node already has a<br>
sibling by the new name, or if the list of children is not<br>
yet loaded (<code>getKids()</code> not yet called)</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="reorder"><span class="type-signature"></span>reorder(newKids)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Sets the order of this node's children. The input array must contain the<br>
exact same set of children as this node has, but in any order.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>newKids</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="module-nmodule_webEditors_rc_wb_tree_TreeNode.html">module:nmodule/webEditors/rc/wb/tree/TreeNode</a>></span>
|

<span class="param-type">Array.&lt;String></span>
|

<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>the children of this node, in the desired new order. This can be<br>
an array of the actual nodes rearranged, or an array of node names. It can<br>
also be a sort function that takes two tree nodes; the existing nodes will<br>
be reordered according to this function.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved when the child nodes are<br>
reordered, or rejected if the input array contains a different number of<br>
nodes than this node has children, if it contains a node that does not<br>
exist as a child node, or if the list of children is not<br>
yet loaded (<code>getKids()</code> not yet called)</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="toDisplay"><span class="type-signature"></span>toDisplay()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>The display name of this node, to be shown in user interfaces. May make<br>
asynchronous calls to format the display name.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved with the display name</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="toHyperlinkUri"><span class="type-signature"></span>toHyperlinkUri()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>When activated, many tree nodes will instigate a page change. Override<br>
this function to specify the hyperlink target.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved with the hyperlink target.<br>
Bu default, resolves <code>undefined</code>.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="toString"><span class="type-signature"></span>toString()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Returns a string representation of this node. By default, just returns<br>
the name.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="value"><span class="type-signature"></span>value()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Returns the backing value represented by this node. By default, this will<br>
return <code>undefined</code>, since a vanilla <code>TreeNode</code> is really just a<br>
name/display pair. Subclasses of <code>TreeNode</code> intended to represent real-life<br>
values should override this method to return the appropriate value.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">*</span>



    </dd>
</dl>


        

    
</dd>

        </dl>
    

    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	webEditors Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:29:00+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>