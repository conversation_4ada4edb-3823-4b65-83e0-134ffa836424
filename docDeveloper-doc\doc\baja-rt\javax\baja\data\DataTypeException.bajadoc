<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.data.DataTypeException" name="DataTypeException" packageName="javax.baja.data" public="true" category="exception">
<description>
DataTypeException is thrown when a non-DataType type is&#xa; used where a DataType is expected.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">23 Feb 2003</tag>
<tag name="@version">$Revision: 1$ $Date: 2/27/03 9:18:32 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BajaRuntimeException"/>
</extends>
<!-- javax.baja.data.DataTypeException(javax.baja.sys.Type) -->
<constructor name="DataTypeException" public="true">
<parameter name="badType">
<type class="javax.baja.sys.Type"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.data.DataTypeException.getInvalidType() -->
<method name="getInvalidType"  public="true">
<description>
Get the type that was not a DataType
</description>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

</class>
</bajadoc>
