<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="javax.baja.bacnetAws.config.BBacnetAccessDoor" name="BBacnetAccessDoor" packageName="javax.baja.bacnetAws.config" public="true">
<description/>
<extends>
<type class="javax.baja.bacnet.BBacnetObject"/>
</extends>
<property name="presentValue" flags="">
<type class="javax.baja.bacnetAws.enums.BBacnetDoorValue"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</property>

<property name="statusFlags" flags="r">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
Slot for the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#getStatusFlags</tag>
<tag name="@see">#setStatusFlags</tag>
</property>

<property name="eventState" flags="r">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getEventState</tag>
<tag name="@see">#setEventState</tag>
</property>

<property name="reliability" flags="r">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;reliability&lt;/code&gt; property.
</description>
<tag name="@see">#getReliability</tag>
<tag name="@see">#setReliability</tag>
</property>

<property name="outOfService" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#getOutOfService</tag>
<tag name="@see">#setOutOfService</tag>
</property>

<property name="priorityArray" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
<description>
Slot for the &lt;code&gt;priorityArray&lt;/code&gt; property.
</description>
<tag name="@see">#getPriorityArray</tag>
<tag name="@see">#setPriorityArray</tag>
</property>

<property name="relinquishDefault" flags="">
<type class="javax.baja.bacnetAws.enums.BBacnetDoorValue"/>
<description>
Slot for the &lt;code&gt;relinquishDefault&lt;/code&gt; property.
</description>
<tag name="@see">#getRelinquishDefault</tag>
<tag name="@see">#setRelinquishDefault</tag>
</property>

<property name="doorPulseTime" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;doorPulseTime&lt;/code&gt; property.
</description>
<tag name="@see">#getDoorPulseTime</tag>
<tag name="@see">#setDoorPulseTime</tag>
</property>

<property name="doorExtendedPulseTime" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;doorExtendedPulseTime&lt;/code&gt; property.
</description>
<tag name="@see">#getDoorExtendedPulseTime</tag>
<tag name="@see">#setDoorExtendedPulseTime</tag>
</property>

<property name="doorOpenTooLongTime" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;doorOpenTooLongTime&lt;/code&gt; property.
</description>
<tag name="@see">#getDoorOpenTooLongTime</tag>
<tag name="@see">#setDoorOpenTooLongTime</tag>
</property>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor() -->
<constructor name="BBacnetAccessDoor" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.getPresentValue() -->
<method name="getPresentValue"  public="true">
<description>
Get the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#presentValue</tag>
<return>
<type class="javax.baja.bacnetAws.enums.BBacnetDoorValue"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.setPresentValue(javax.baja.bacnetAws.enums.BBacnetDoorValue) -->
<method name="setPresentValue"  public="true">
<description>
Set the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#presentValue</tag>
<parameter name="v">
<type class="javax.baja.bacnetAws.enums.BBacnetDoorValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.getStatusFlags() -->
<method name="getStatusFlags"  public="true">
<description>
Get the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#statusFlags</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.setStatusFlags(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="setStatusFlags"  public="true">
<description>
Set the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#statusFlags</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.getEventState() -->
<method name="getEventState"  public="true">
<description>
Get the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#eventState</tag>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.setEventState(javax.baja.sys.BEnum) -->
<method name="setEventState"  public="true">
<description>
Set the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#eventState</tag>
<parameter name="v">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.getReliability() -->
<method name="getReliability"  public="true">
<description>
Get the &lt;code&gt;reliability&lt;/code&gt; property.
</description>
<tag name="@see">#reliability</tag>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.setReliability(javax.baja.sys.BEnum) -->
<method name="setReliability"  public="true">
<description>
Set the &lt;code&gt;reliability&lt;/code&gt; property.
</description>
<tag name="@see">#reliability</tag>
<parameter name="v">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.getOutOfService() -->
<method name="getOutOfService"  public="true">
<description>
Get the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#outOfService</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.setOutOfService(boolean) -->
<method name="setOutOfService"  public="true">
<description>
Set the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#outOfService</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.getPriorityArray() -->
<method name="getPriorityArray"  public="true">
<description>
Get the &lt;code&gt;priorityArray&lt;/code&gt; property.
</description>
<tag name="@see">#priorityArray</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.setPriorityArray(javax.baja.bacnet.datatypes.BBacnetArray) -->
<method name="setPriorityArray"  public="true">
<description>
Set the &lt;code&gt;priorityArray&lt;/code&gt; property.
</description>
<tag name="@see">#priorityArray</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.getRelinquishDefault() -->
<method name="getRelinquishDefault"  public="true">
<description>
Get the &lt;code&gt;relinquishDefault&lt;/code&gt; property.
</description>
<tag name="@see">#relinquishDefault</tag>
<return>
<type class="javax.baja.bacnetAws.enums.BBacnetDoorValue"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.setRelinquishDefault(javax.baja.bacnetAws.enums.BBacnetDoorValue) -->
<method name="setRelinquishDefault"  public="true">
<description>
Set the &lt;code&gt;relinquishDefault&lt;/code&gt; property.
</description>
<tag name="@see">#relinquishDefault</tag>
<parameter name="v">
<type class="javax.baja.bacnetAws.enums.BBacnetDoorValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.getDoorPulseTime() -->
<method name="getDoorPulseTime"  public="true">
<description>
Get the &lt;code&gt;doorPulseTime&lt;/code&gt; property.
</description>
<tag name="@see">#doorPulseTime</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.setDoorPulseTime(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setDoorPulseTime"  public="true">
<description>
Set the &lt;code&gt;doorPulseTime&lt;/code&gt; property.
</description>
<tag name="@see">#doorPulseTime</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.getDoorExtendedPulseTime() -->
<method name="getDoorExtendedPulseTime"  public="true">
<description>
Get the &lt;code&gt;doorExtendedPulseTime&lt;/code&gt; property.
</description>
<tag name="@see">#doorExtendedPulseTime</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.setDoorExtendedPulseTime(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setDoorExtendedPulseTime"  public="true">
<description>
Set the &lt;code&gt;doorExtendedPulseTime&lt;/code&gt; property.
</description>
<tag name="@see">#doorExtendedPulseTime</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.getDoorOpenTooLongTime() -->
<method name="getDoorOpenTooLongTime"  public="true">
<description>
Get the &lt;code&gt;doorOpenTooLongTime&lt;/code&gt; property.
</description>
<tag name="@see">#doorOpenTooLongTime</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.setDoorOpenTooLongTime(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setDoorOpenTooLongTime"  public="true">
<description>
Set the &lt;code&gt;doorOpenTooLongTime&lt;/code&gt; property.
</description>
<tag name="@see">#doorOpenTooLongTime</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.presentValue -->
<field name="presentValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.statusFlags -->
<field name="statusFlags"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#getStatusFlags</tag>
<tag name="@see">#setStatusFlags</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.eventState -->
<field name="eventState"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getEventState</tag>
<tag name="@see">#setEventState</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.reliability -->
<field name="reliability"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;reliability&lt;/code&gt; property.
</description>
<tag name="@see">#getReliability</tag>
<tag name="@see">#setReliability</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.outOfService -->
<field name="outOfService"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#getOutOfService</tag>
<tag name="@see">#setOutOfService</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.priorityArray -->
<field name="priorityArray"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;priorityArray&lt;/code&gt; property.
</description>
<tag name="@see">#getPriorityArray</tag>
<tag name="@see">#setPriorityArray</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.relinquishDefault -->
<field name="relinquishDefault"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;relinquishDefault&lt;/code&gt; property.
</description>
<tag name="@see">#getRelinquishDefault</tag>
<tag name="@see">#setRelinquishDefault</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.doorPulseTime -->
<field name="doorPulseTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;doorPulseTime&lt;/code&gt; property.
</description>
<tag name="@see">#getDoorPulseTime</tag>
<tag name="@see">#setDoorPulseTime</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.doorExtendedPulseTime -->
<field name="doorExtendedPulseTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;doorExtendedPulseTime&lt;/code&gt; property.
</description>
<tag name="@see">#getDoorExtendedPulseTime</tag>
<tag name="@see">#setDoorExtendedPulseTime</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.doorOpenTooLongTime -->
<field name="doorOpenTooLongTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;doorOpenTooLongTime&lt;/code&gt; property.
</description>
<tag name="@see">#getDoorOpenTooLongTime</tag>
<tag name="@see">#setDoorOpenTooLongTime</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccessDoor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
