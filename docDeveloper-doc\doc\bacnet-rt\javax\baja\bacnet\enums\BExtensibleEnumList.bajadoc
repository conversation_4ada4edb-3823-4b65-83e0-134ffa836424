<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BExtensibleEnumList" name="BExtensibleEnumList" packageName="javax.baja.bacnet.enums" public="true">
<description>
BExtensibleEnumList is a container for managing a device&#x27;s knowledge of&#xa; any proprietary extensions to any of the extensible enumerations&#xa; are defined by Bacnet.  It contains one BDynamicEnum for&#xa; each extensible enumeration type, available for use within the device.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">15 May 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="errorClassFacets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;errorClassFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getErrorClassFacets</tag>
<tag name="@see">#setErrorClassFacets</tag>
</property>

<property name="errorCodeFacets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;errorCodeFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getErrorCodeFacets</tag>
<tag name="@see">#setErrorCodeFacets</tag>
</property>

<property name="abortReasonFacets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;abortReasonFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getAbortReasonFacets</tag>
<tag name="@see">#setAbortReasonFacets</tag>
</property>

<property name="deviceStatusFacets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;deviceStatusFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceStatusFacets</tag>
<tag name="@see">#setDeviceStatusFacets</tag>
</property>

<property name="engineeringUnitsFacets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;engineeringUnitsFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getEngineeringUnitsFacets</tag>
<tag name="@see">#setEngineeringUnitsFacets</tag>
</property>

<property name="eventStateFacets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;eventStateFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getEventStateFacets</tag>
<tag name="@see">#setEventStateFacets</tag>
</property>

<property name="eventTypeFacets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;eventTypeFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getEventTypeFacets</tag>
<tag name="@see">#setEventTypeFacets</tag>
</property>

<property name="lifeSafetyModeFacets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;lifeSafetyModeFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getLifeSafetyModeFacets</tag>
<tag name="@see">#setLifeSafetyModeFacets</tag>
</property>

<property name="lifeSafetyOperationFacets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;lifeSafetyOperationFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getLifeSafetyOperationFacets</tag>
<tag name="@see">#setLifeSafetyOperationFacets</tag>
</property>

<property name="lifeSafetyStateFacets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;lifeSafetyStateFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getLifeSafetyStateFacets</tag>
<tag name="@see">#setLifeSafetyStateFacets</tag>
</property>

<property name="maintenanceFacets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;maintenanceFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getMaintenanceFacets</tag>
<tag name="@see">#setMaintenanceFacets</tag>
</property>

<property name="objectTypeFacets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;objectTypeFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectTypeFacets</tag>
<tag name="@see">#setObjectTypeFacets</tag>
</property>

<property name="programErrorFacets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;programErrorFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getProgramErrorFacets</tag>
<tag name="@see">#setProgramErrorFacets</tag>
</property>

<property name="propertyIdFacets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;propertyIdFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyIdFacets</tag>
<tag name="@see">#setPropertyIdFacets</tag>
</property>

<property name="reliabilityFacets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;reliabilityFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getReliabilityFacets</tag>
<tag name="@see">#setReliabilityFacets</tag>
</property>

<property name="rejectReasonFacets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;rejectReasonFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getRejectReasonFacets</tag>
<tag name="@see">#setRejectReasonFacets</tag>
</property>

<property name="silencedStateFacets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;silencedStateFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getSilencedStateFacets</tag>
<tag name="@see">#setSilencedStateFacets</tag>
</property>

<property name="vtClassFacets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;vtClassFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getVtClassFacets</tag>
<tag name="@see">#setVtClassFacets</tag>
</property>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList() -->
<constructor name="BExtensibleEnumList" public="true">
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList(javax.baja.xml.XElem) -->
<constructor name="BExtensibleEnumList" public="true">
<parameter name="xlm">
<type class="javax.baja.xml.XElem"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getErrorClassFacets() -->
<method name="getErrorClassFacets"  public="true">
<description>
Get the &lt;code&gt;errorClassFacets&lt;/code&gt; property.
</description>
<tag name="@see">#errorClassFacets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.setErrorClassFacets(javax.baja.sys.BFacets) -->
<method name="setErrorClassFacets"  public="true">
<description>
Set the &lt;code&gt;errorClassFacets&lt;/code&gt; property.
</description>
<tag name="@see">#errorClassFacets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getErrorCodeFacets() -->
<method name="getErrorCodeFacets"  public="true">
<description>
Get the &lt;code&gt;errorCodeFacets&lt;/code&gt; property.
</description>
<tag name="@see">#errorCodeFacets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.setErrorCodeFacets(javax.baja.sys.BFacets) -->
<method name="setErrorCodeFacets"  public="true">
<description>
Set the &lt;code&gt;errorCodeFacets&lt;/code&gt; property.
</description>
<tag name="@see">#errorCodeFacets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getAbortReasonFacets() -->
<method name="getAbortReasonFacets"  public="true">
<description>
Get the &lt;code&gt;abortReasonFacets&lt;/code&gt; property.
</description>
<tag name="@see">#abortReasonFacets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.setAbortReasonFacets(javax.baja.sys.BFacets) -->
<method name="setAbortReasonFacets"  public="true">
<description>
Set the &lt;code&gt;abortReasonFacets&lt;/code&gt; property.
</description>
<tag name="@see">#abortReasonFacets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getDeviceStatusFacets() -->
<method name="getDeviceStatusFacets"  public="true">
<description>
Get the &lt;code&gt;deviceStatusFacets&lt;/code&gt; property.
</description>
<tag name="@see">#deviceStatusFacets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.setDeviceStatusFacets(javax.baja.sys.BFacets) -->
<method name="setDeviceStatusFacets"  public="true">
<description>
Set the &lt;code&gt;deviceStatusFacets&lt;/code&gt; property.
</description>
<tag name="@see">#deviceStatusFacets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getEngineeringUnitsFacets() -->
<method name="getEngineeringUnitsFacets"  public="true">
<description>
Get the &lt;code&gt;engineeringUnitsFacets&lt;/code&gt; property.
</description>
<tag name="@see">#engineeringUnitsFacets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.setEngineeringUnitsFacets(javax.baja.sys.BFacets) -->
<method name="setEngineeringUnitsFacets"  public="true">
<description>
Set the &lt;code&gt;engineeringUnitsFacets&lt;/code&gt; property.
</description>
<tag name="@see">#engineeringUnitsFacets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getEventStateFacets() -->
<method name="getEventStateFacets"  public="true">
<description>
Get the &lt;code&gt;eventStateFacets&lt;/code&gt; property.
</description>
<tag name="@see">#eventStateFacets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.setEventStateFacets(javax.baja.sys.BFacets) -->
<method name="setEventStateFacets"  public="true">
<description>
Set the &lt;code&gt;eventStateFacets&lt;/code&gt; property.
</description>
<tag name="@see">#eventStateFacets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getEventTypeFacets() -->
<method name="getEventTypeFacets"  public="true">
<description>
Get the &lt;code&gt;eventTypeFacets&lt;/code&gt; property.
</description>
<tag name="@see">#eventTypeFacets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.setEventTypeFacets(javax.baja.sys.BFacets) -->
<method name="setEventTypeFacets"  public="true">
<description>
Set the &lt;code&gt;eventTypeFacets&lt;/code&gt; property.
</description>
<tag name="@see">#eventTypeFacets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getLifeSafetyModeFacets() -->
<method name="getLifeSafetyModeFacets"  public="true">
<description>
Get the &lt;code&gt;lifeSafetyModeFacets&lt;/code&gt; property.
</description>
<tag name="@see">#lifeSafetyModeFacets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.setLifeSafetyModeFacets(javax.baja.sys.BFacets) -->
<method name="setLifeSafetyModeFacets"  public="true">
<description>
Set the &lt;code&gt;lifeSafetyModeFacets&lt;/code&gt; property.
</description>
<tag name="@see">#lifeSafetyModeFacets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getLifeSafetyOperationFacets() -->
<method name="getLifeSafetyOperationFacets"  public="true">
<description>
Get the &lt;code&gt;lifeSafetyOperationFacets&lt;/code&gt; property.
</description>
<tag name="@see">#lifeSafetyOperationFacets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.setLifeSafetyOperationFacets(javax.baja.sys.BFacets) -->
<method name="setLifeSafetyOperationFacets"  public="true">
<description>
Set the &lt;code&gt;lifeSafetyOperationFacets&lt;/code&gt; property.
</description>
<tag name="@see">#lifeSafetyOperationFacets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getLifeSafetyStateFacets() -->
<method name="getLifeSafetyStateFacets"  public="true">
<description>
Get the &lt;code&gt;lifeSafetyStateFacets&lt;/code&gt; property.
</description>
<tag name="@see">#lifeSafetyStateFacets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.setLifeSafetyStateFacets(javax.baja.sys.BFacets) -->
<method name="setLifeSafetyStateFacets"  public="true">
<description>
Set the &lt;code&gt;lifeSafetyStateFacets&lt;/code&gt; property.
</description>
<tag name="@see">#lifeSafetyStateFacets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getMaintenanceFacets() -->
<method name="getMaintenanceFacets"  public="true">
<description>
Get the &lt;code&gt;maintenanceFacets&lt;/code&gt; property.
</description>
<tag name="@see">#maintenanceFacets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.setMaintenanceFacets(javax.baja.sys.BFacets) -->
<method name="setMaintenanceFacets"  public="true">
<description>
Set the &lt;code&gt;maintenanceFacets&lt;/code&gt; property.
</description>
<tag name="@see">#maintenanceFacets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getObjectTypeFacets() -->
<method name="getObjectTypeFacets"  public="true">
<description>
Get the &lt;code&gt;objectTypeFacets&lt;/code&gt; property.
</description>
<tag name="@see">#objectTypeFacets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.setObjectTypeFacets(javax.baja.sys.BFacets) -->
<method name="setObjectTypeFacets"  public="true">
<description>
Set the &lt;code&gt;objectTypeFacets&lt;/code&gt; property.
</description>
<tag name="@see">#objectTypeFacets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getProgramErrorFacets() -->
<method name="getProgramErrorFacets"  public="true">
<description>
Get the &lt;code&gt;programErrorFacets&lt;/code&gt; property.
</description>
<tag name="@see">#programErrorFacets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.setProgramErrorFacets(javax.baja.sys.BFacets) -->
<method name="setProgramErrorFacets"  public="true">
<description>
Set the &lt;code&gt;programErrorFacets&lt;/code&gt; property.
</description>
<tag name="@see">#programErrorFacets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getPropertyIdFacets() -->
<method name="getPropertyIdFacets"  public="true">
<description>
Get the &lt;code&gt;propertyIdFacets&lt;/code&gt; property.
</description>
<tag name="@see">#propertyIdFacets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.setPropertyIdFacets(javax.baja.sys.BFacets) -->
<method name="setPropertyIdFacets"  public="true">
<description>
Set the &lt;code&gt;propertyIdFacets&lt;/code&gt; property.
</description>
<tag name="@see">#propertyIdFacets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getReliabilityFacets() -->
<method name="getReliabilityFacets"  public="true">
<description>
Get the &lt;code&gt;reliabilityFacets&lt;/code&gt; property.
</description>
<tag name="@see">#reliabilityFacets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.setReliabilityFacets(javax.baja.sys.BFacets) -->
<method name="setReliabilityFacets"  public="true">
<description>
Set the &lt;code&gt;reliabilityFacets&lt;/code&gt; property.
</description>
<tag name="@see">#reliabilityFacets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getRejectReasonFacets() -->
<method name="getRejectReasonFacets"  public="true">
<description>
Get the &lt;code&gt;rejectReasonFacets&lt;/code&gt; property.
</description>
<tag name="@see">#rejectReasonFacets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.setRejectReasonFacets(javax.baja.sys.BFacets) -->
<method name="setRejectReasonFacets"  public="true">
<description>
Set the &lt;code&gt;rejectReasonFacets&lt;/code&gt; property.
</description>
<tag name="@see">#rejectReasonFacets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getSilencedStateFacets() -->
<method name="getSilencedStateFacets"  public="true">
<description>
Get the &lt;code&gt;silencedStateFacets&lt;/code&gt; property.
</description>
<tag name="@see">#silencedStateFacets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.setSilencedStateFacets(javax.baja.sys.BFacets) -->
<method name="setSilencedStateFacets"  public="true">
<description>
Set the &lt;code&gt;silencedStateFacets&lt;/code&gt; property.
</description>
<tag name="@see">#silencedStateFacets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getVtClassFacets() -->
<method name="getVtClassFacets"  public="true">
<description>
Get the &lt;code&gt;vtClassFacets&lt;/code&gt; property.
</description>
<tag name="@see">#vtClassFacets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.setVtClassFacets(javax.baja.sys.BFacets) -->
<method name="setVtClassFacets"  public="true">
<description>
Set the &lt;code&gt;vtClassFacets&lt;/code&gt; property.
</description>
<tag name="@see">#vtClassFacets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getErrorClass() -->
<method name="getErrorClass"  public="true">
<description>
Convenience method for error-class.
</description>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
the error-class enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getErrorCode() -->
<method name="getErrorCode"  public="true">
<description>
Convenience method for error-code.
</description>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
the error-code enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getAbortReason() -->
<method name="getAbortReason"  public="true">
<description>
Convenience method for BacnetAbortReason.
</description>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
the BacnetAbortReason enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getDeviceStatus() -->
<method name="getDeviceStatus"  public="true">
<description>
Convenience method for BacnetDeviceStatus.
</description>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
the BacnetDeviceStatus enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getEngineeringUnits() -->
<method name="getEngineeringUnits"  public="true">
<description>
Convenience method for BacnetEngineeringUnits.
</description>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
the BacnetEngineeringUnits enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getEventState() -->
<method name="getEventState"  public="true">
<description>
Convenience method for BacnetEventState.
</description>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
the BacnetEventState enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getEventType() -->
<method name="getEventType"  public="true">
<description>
Convenience method for BacnetEventType.
</description>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
the BacnetEventType enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getLifeSafetyMode() -->
<method name="getLifeSafetyMode"  public="true">
<description>
Convenience method for BacnetLifeSafetyMode.
</description>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
the BacnetLifeSafetyMode enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getLifeSafetyState() -->
<method name="getLifeSafetyState"  public="true">
<description>
Convenience method for BacnetLifeSafetyState.
</description>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
the BacnetLifeSafetyState enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getLifeSafetyOperation() -->
<method name="getLifeSafetyOperation"  public="true">
<description>
Convenience method for BacnetLifeSafetyOperation.
</description>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
the BacnetLifeSafetyOperation enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getMaintenance() -->
<method name="getMaintenance"  public="true">
<description>
Convenience method for BacnetMaintenance.
</description>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
the BacnetMaintenance enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getObjectType() -->
<method name="getObjectType"  public="true">
<description>
Convenience method for BacnetObjectType.
</description>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
the BacnetObjectType enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getProgramError() -->
<method name="getProgramError"  public="true">
<description>
Convenience method for BacnetProgramError.
</description>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
the BacnetProgramError enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getPropertyId() -->
<method name="getPropertyId"  public="true">
<description>
Convenience method for BacnetPropertyId.
</description>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
the BacnetPropertyId enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getReliability() -->
<method name="getReliability"  public="true">
<description>
Convenience method for BacnetReliability.
</description>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
the BacnetReliability enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getRejectReason() -->
<method name="getRejectReason"  public="true">
<description>
Convenience method for BacnetRejectReason.
</description>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
the BacnetRejectReason enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getSilencedState() -->
<method name="getSilencedState"  public="true">
<description>
Convenience method for BacnetSilencedState.
</description>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
the BacnetSilencedState enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getVtClass() -->
<method name="getVtClass"  public="true">
<description>
Convenience method for BacnetVtClass.
</description>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
the BacnetVtClass enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getErrorClassRange() -->
<method name="getErrorClassRange"  public="true">
<description>
Convenience method for error-class range.
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
the known range for the error-class enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getErrorCodeRange() -->
<method name="getErrorCodeRange"  public="true">
<description>
Convenience method for error-code range.
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
the known range for the error-code enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getAbortReasonRange() -->
<method name="getAbortReasonRange"  public="true">
<description>
Convenience method for BacnetAbortReason range.
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
the known range for the BacnetAbortReason enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getDeviceStatusRange() -->
<method name="getDeviceStatusRange"  public="true">
<description>
Convenience method for BacnetDeviceStatus range.
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
the known range for the BacnetDeviceStatus enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getEngineeringUnitsRange() -->
<method name="getEngineeringUnitsRange"  public="true">
<description>
Convenience method for BacnetEngineeringUnits range.
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
the known range for the BacnetEngineeringUnits enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getEventStateRange() -->
<method name="getEventStateRange"  public="true">
<description>
Convenience method for BacnetEventState range.
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
the known range for the BacnetEventState enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getEventTypeRange() -->
<method name="getEventTypeRange"  public="true">
<description>
Convenience method for BacnetEventType range.
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
the known range for the BacnetEventType enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getLifeSafetyModeRange() -->
<method name="getLifeSafetyModeRange"  public="true">
<description>
Convenience method for BacnetLifeSafetyMode range.
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
the known range for the BacnetLifeSafetyMode enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getLifeSafetyStateRange() -->
<method name="getLifeSafetyStateRange"  public="true">
<description>
Convenience method for BacnetLifeSafetyState range.
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
the known range for the BacnetLifeSafetyState enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getLifeSafetyOperationRange() -->
<method name="getLifeSafetyOperationRange"  public="true">
<description>
Convenience method for BacnetLifeSafetyOperation range.
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
the known range for the BacnetLifeSafetyOperation enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getMaintenanceRange() -->
<method name="getMaintenanceRange"  public="true">
<description>
Convenience method for BacnetMaintenance range.
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
the known range for the BacnetMaintenance enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getObjectTypeRange() -->
<method name="getObjectTypeRange"  public="true">
<description>
Convenience method for BacnetObjectType range.
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
the known range for the BacnetObjectType enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getProgramErrorRange() -->
<method name="getProgramErrorRange"  public="true">
<description>
Convenience method for BacnetProgramError range.
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
the known range for the BacnetProgramError enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getPropertyIdRange() -->
<method name="getPropertyIdRange"  public="true">
<description>
Convenience method for BacnetPropertyId range.
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
the known range for the BacnetPropertyId enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getReliabilityRange() -->
<method name="getReliabilityRange"  public="true">
<description>
Convenience method for BacnetReliability range.
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
the known range for the BacnetReliability enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getRejectReasonRange() -->
<method name="getRejectReasonRange"  public="true">
<description>
Convenience method for BacnetRejectReason range.
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
the known range for the BacnetRejectReason enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getSilencedStateRange() -->
<method name="getSilencedStateRange"  public="true">
<description>
Convenience method for BacnetSilencedState range.
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
the known range for the BacnetSilencedState enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getVtClassRange() -->
<method name="getVtClassRange"  public="true">
<description>
Convenience method for BacnetVtClass range.
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
the known range for the BacnetVtClass enumeration.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.getEnumRange(java.lang.String) -->
<method name="getEnumRange"  public="true">
<description/>
<parameter name="type">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BEnumRange"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewErrorClass(java.lang.String, int) -->
<method name="addNewErrorClass"  public="true">
<description/>
<parameter name="enumName">
<type class="java.lang.String"/>
</parameter>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewErrorCode(java.lang.String, int) -->
<method name="addNewErrorCode"  public="true">
<description/>
<parameter name="enumName">
<type class="java.lang.String"/>
</parameter>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewAbortReason(java.lang.String, int) -->
<method name="addNewAbortReason"  public="true">
<description/>
<parameter name="enumName">
<type class="java.lang.String"/>
</parameter>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewDeviceStatus(java.lang.String, int) -->
<method name="addNewDeviceStatus"  public="true">
<description/>
<parameter name="enumName">
<type class="java.lang.String"/>
</parameter>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewEngineeringUnits(java.lang.String, int) -->
<method name="addNewEngineeringUnits"  public="true">
<description/>
<parameter name="enumName">
<type class="java.lang.String"/>
</parameter>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewEventState(java.lang.String, int) -->
<method name="addNewEventState"  public="true">
<description/>
<parameter name="enumName">
<type class="java.lang.String"/>
</parameter>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewEventType(java.lang.String, int) -->
<method name="addNewEventType"  public="true">
<description/>
<parameter name="enumName">
<type class="java.lang.String"/>
</parameter>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewLifeSafetyMode(java.lang.String, int) -->
<method name="addNewLifeSafetyMode"  public="true">
<description/>
<parameter name="enumName">
<type class="java.lang.String"/>
</parameter>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewLifeSafetyOperation(java.lang.String, int) -->
<method name="addNewLifeSafetyOperation"  public="true">
<description/>
<parameter name="enumName">
<type class="java.lang.String"/>
</parameter>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewLifeSafetyState(java.lang.String, int) -->
<method name="addNewLifeSafetyState"  public="true">
<description/>
<parameter name="enumName">
<type class="java.lang.String"/>
</parameter>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewMaintenance(java.lang.String, int) -->
<method name="addNewMaintenance"  public="true">
<description/>
<parameter name="enumName">
<type class="java.lang.String"/>
</parameter>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewObjectType(java.lang.String, int) -->
<method name="addNewObjectType"  public="true">
<description/>
<parameter name="enumName">
<type class="java.lang.String"/>
</parameter>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewProgramError(java.lang.String, int) -->
<method name="addNewProgramError"  public="true">
<description/>
<parameter name="enumName">
<type class="java.lang.String"/>
</parameter>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewPropertyId(java.lang.String, int) -->
<method name="addNewPropertyId"  public="true">
<description/>
<parameter name="enumName">
<type class="java.lang.String"/>
</parameter>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewReliability(java.lang.String, int) -->
<method name="addNewReliability"  public="true">
<description/>
<parameter name="enumName">
<type class="java.lang.String"/>
</parameter>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewRejectReason(java.lang.String, int) -->
<method name="addNewRejectReason"  public="true">
<description/>
<parameter name="enumName">
<type class="java.lang.String"/>
</parameter>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewSilencedState(java.lang.String, int) -->
<method name="addNewSilencedState"  public="true">
<description/>
<parameter name="enumName">
<type class="java.lang.String"/>
</parameter>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewVtClass(java.lang.String, int) -->
<method name="addNewVtClass"  public="true">
<description/>
<parameter name="enumName">
<type class="java.lang.String"/>
</parameter>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewErrorClass(int) -->
<method name="addNewErrorClass"  public="true">
<description/>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewErrorCode(int) -->
<method name="addNewErrorCode"  public="true">
<description/>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewAbortReason(int) -->
<method name="addNewAbortReason"  public="true">
<description/>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewDeviceStatus(int) -->
<method name="addNewDeviceStatus"  public="true">
<description/>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewEngineeringUnits(int) -->
<method name="addNewEngineeringUnits"  public="true">
<description/>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewEventState(int) -->
<method name="addNewEventState"  public="true">
<description/>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewEventType(int) -->
<method name="addNewEventType"  public="true">
<description/>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewLifeSafetyMode(int) -->
<method name="addNewLifeSafetyMode"  public="true">
<description/>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewLifeSafetyOperation(int) -->
<method name="addNewLifeSafetyOperation"  public="true">
<description/>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewLifeSafetyState(int) -->
<method name="addNewLifeSafetyState"  public="true">
<description/>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewMaintenance(int) -->
<method name="addNewMaintenance"  public="true">
<description/>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewObjectType(int) -->
<method name="addNewObjectType"  public="true">
<description/>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewProgramError(int) -->
<method name="addNewProgramError"  public="true">
<description/>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewPropertyId(int) -->
<method name="addNewPropertyId"  public="true">
<description/>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewReliability(int) -->
<method name="addNewReliability"  public="true">
<description/>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewRejectReason(int) -->
<method name="addNewRejectReason"  public="true">
<description/>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewSilencedState(int) -->
<method name="addNewSilencedState"  public="true">
<description/>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.addNewVtClass(int) -->
<method name="addNewVtClass"  public="true">
<description/>
<parameter name="enumValue">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.merge(javax.baja.bacnet.enums.BExtensibleEnumList) -->
<method name="merge"  public="true">
<description>
Incorporate extensions from list into this object.&#xa; Duplicates are rejected.
</description>
<parameter name="list">
<type class="javax.baja.bacnet.enums.BExtensibleEnumList"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.errorClassFacets -->
<field name="errorClassFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;errorClassFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getErrorClassFacets</tag>
<tag name="@see">#setErrorClassFacets</tag>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.errorCodeFacets -->
<field name="errorCodeFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;errorCodeFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getErrorCodeFacets</tag>
<tag name="@see">#setErrorCodeFacets</tag>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.abortReasonFacets -->
<field name="abortReasonFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;abortReasonFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getAbortReasonFacets</tag>
<tag name="@see">#setAbortReasonFacets</tag>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.deviceStatusFacets -->
<field name="deviceStatusFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;deviceStatusFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceStatusFacets</tag>
<tag name="@see">#setDeviceStatusFacets</tag>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.engineeringUnitsFacets -->
<field name="engineeringUnitsFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;engineeringUnitsFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getEngineeringUnitsFacets</tag>
<tag name="@see">#setEngineeringUnitsFacets</tag>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.eventStateFacets -->
<field name="eventStateFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;eventStateFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getEventStateFacets</tag>
<tag name="@see">#setEventStateFacets</tag>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.eventTypeFacets -->
<field name="eventTypeFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;eventTypeFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getEventTypeFacets</tag>
<tag name="@see">#setEventTypeFacets</tag>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.lifeSafetyModeFacets -->
<field name="lifeSafetyModeFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lifeSafetyModeFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getLifeSafetyModeFacets</tag>
<tag name="@see">#setLifeSafetyModeFacets</tag>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.lifeSafetyOperationFacets -->
<field name="lifeSafetyOperationFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lifeSafetyOperationFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getLifeSafetyOperationFacets</tag>
<tag name="@see">#setLifeSafetyOperationFacets</tag>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.lifeSafetyStateFacets -->
<field name="lifeSafetyStateFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lifeSafetyStateFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getLifeSafetyStateFacets</tag>
<tag name="@see">#setLifeSafetyStateFacets</tag>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.maintenanceFacets -->
<field name="maintenanceFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;maintenanceFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getMaintenanceFacets</tag>
<tag name="@see">#setMaintenanceFacets</tag>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.objectTypeFacets -->
<field name="objectTypeFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectTypeFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectTypeFacets</tag>
<tag name="@see">#setObjectTypeFacets</tag>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.programErrorFacets -->
<field name="programErrorFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;programErrorFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getProgramErrorFacets</tag>
<tag name="@see">#setProgramErrorFacets</tag>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.propertyIdFacets -->
<field name="propertyIdFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;propertyIdFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyIdFacets</tag>
<tag name="@see">#setPropertyIdFacets</tag>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.reliabilityFacets -->
<field name="reliabilityFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;reliabilityFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getReliabilityFacets</tag>
<tag name="@see">#setReliabilityFacets</tag>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.rejectReasonFacets -->
<field name="rejectReasonFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;rejectReasonFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getRejectReasonFacets</tag>
<tag name="@see">#setRejectReasonFacets</tag>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.silencedStateFacets -->
<field name="silencedStateFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;silencedStateFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getSilencedStateFacets</tag>
<tag name="@see">#setSilencedStateFacets</tag>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.vtClassFacets -->
<field name="vtClassFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;vtClassFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getVtClassFacets</tag>
<tag name="@see">#setVtClassFacets</tag>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BExtensibleEnumList.niagaraEnums -->
<field name="niagaraEnums"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BExtensibleEnumList"/>
<description/>
</field>

</class>
</bajadoc>
