<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>bajaux Module: bajaux/mixin/responsiveMixIn</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">bajaux</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-bajaux_commands_Command.html">bajaux/commands/Command</a></li><li><a href="module-bajaux_commands_CommandGroup.html">bajaux/commands/CommandGroup</a></li><li><a href="module-bajaux_commands_ToggleCommand.html">bajaux/commands/ToggleCommand</a></li><li><a href="module-bajaux_commands_ToggleCommandGroup.html">bajaux/commands/ToggleCommandGroup</a></li><li><a href="module-bajaux_container_wb_Clipboard.html">bajaux/container/wb/Clipboard</a></li><li><a href="module-bajaux_container_wb_StringList.html">bajaux/container/wb/StringList</a></li><li><a href="module-bajaux_dragdrop_dragDropUtils.html">bajaux/dragdrop/dragDropUtils</a></li><li><a href="module-bajaux_dragdrop_Envelope.html">bajaux/dragdrop/Envelope</a></li><li><a href="module-bajaux_dragdrop_NavNodeEnvelope.html">bajaux/dragdrop/NavNodeEnvelope</a></li><li><a href="module-bajaux_dragdrop_StringEnvelope.html">bajaux/dragdrop/StringEnvelope</a></li><li><a href="module-bajaux_events.html">bajaux/events</a></li><li><a href="module-bajaux_icon_iconUtils.html">bajaux/icon/iconUtils</a></li><li><a href="module-bajaux_lifecycle_WidgetManager.html">bajaux/lifecycle/WidgetManager</a></li><li><a href="module-bajaux_mixin_batchLoadMixin.html">bajaux/mixin/batchLoadMixin</a></li><li><a href="module-bajaux_mixin_batchSaveMixin.html">bajaux/mixin/batchSaveMixin</a></li><li><a href="module-bajaux_mixin_responsiveMixIn.html">bajaux/mixin/responsiveMixIn</a></li><li><a href="module-bajaux_mixin_subscriberMixIn.html">bajaux/mixin/subscriberMixIn</a></li><li><a href="module-bajaux_Properties.html">bajaux/Properties</a></li><li><a href="module-bajaux_registry_Registry.html">bajaux/registry/Registry</a></li><li><a href="module-bajaux_registry_RegistryEntry.html">bajaux/registry/RegistryEntry</a></li><li><a href="module-bajaux_spandrel.html">bajaux/spandrel</a></li><li><a href="module-bajaux_spandrel_jsx.html">bajaux/spandrel/jsx</a></li><li><a href="module-bajaux_util_CommandButton.html">bajaux/util/CommandButton</a></li><li><a href="module-bajaux_util_CommandButtonGroup.html">bajaux/util/CommandButtonGroup</a></li><li><a href="module-bajaux_util_SaveCommand.html">bajaux/util/SaveCommand</a></li><li><a href="module-bajaux_Validators.html">bajaux/Validators</a></li><li><a href="module-bajaux_Widget.html">bajaux/Widget</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="interfaces.list.html" class="dropdown-toggle" data-toggle="dropdown">Interfaces<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-bajaux_commands_Command-Undoable.html">bajaux/commands/Command~Undoable</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="tutorials.list.html" class="dropdown-toggle" data-toggle="dropdown">Tutorials<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="tutorial-10-mfw-gettingStarted.html">Getting Started - MyFirstWidget</a></li><li><a href="tutorial-20-mfw-modifying.html">Saving Modifications to Station</a></li><li><a href="tutorial-30-mfw-dashboarding.html">Making your Widget Dashboardable</a></li><li><a href="tutorial-40-tipsAndTricks.html">Tips and Tricks</a></li><li><a href="tutorial-50-spandrel.html">Building Composite Widgets With spandrel</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: bajaux/mixin/responsiveMixIn</h1>
<section>

<header>
    
        
            
        
    
</header>


<article>
    <div class="container-overview">
    
        

        
            
<hr>
<dt>
    <h4 class="name" id="module:bajaux/mixin/responsiveMixIn"><span class="type-signature"></span>new (require("bajaux/mixin/responsiveMixIn"))(target, conditions)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Applies the <code>responsive</code> mixin to the target Widget.</p>
<p>This mixin provides responsive layout for a widget based upon its dimensions.<br>
It does this by adding or removing CSS classes to/from the widget's DOM element.</p>
<p>The mixin injects itself by cross cutting the widget's layout behavior.</p>
<p>As of Niagara 4.8, omit the <code>conditions</code> argument to use a set of default<br>
classes:</p>
<ul>
<li><code>phone-only</code>: less than 600px wide</li>
<li><code>tablet-portrait-up</code>: 600px wide or greater</li>
<li><code>tablet-landscape-up</code>: 900px wide or greater</li>
<li><code>desktop-up</code>: 1200px wide or greater</li>
</ul>
    </div>
    

    
        <h5>Extends:</h5>
        


    <ul>
        <li><a href="module-bajaux_Widget.html">module:bajaux/Widget</a></li>
    </ul>


    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>target</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_Widget.html">module:bajaux/Widget</a></span>



            
            </td>

            

            

            <td class="description last"><p>The widget to apply the mixin to.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>conditions</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object.&lt;String, (<a href="module-bajaux_mixin_responsiveMixIn.html#~ResponsiveCondition">module:bajaux/mixin/responsiveMixIn~ResponsiveCondition</a>|<a href="module-bajaux_mixin_responsiveMixIn.html#~ResponsiveCallback">module:bajaux/mixin/responsiveMixIn~ResponsiveCallback</a>)></span>



            
            </td>

            

            

            <td class="description last"><p>An<br>
object that maps class names to set of conditions. If all conditions are<br>
met, the class name will be added to the widget's DOM element. If the<br>
conditions aren't met, the class name will be removed.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
        <h5>Examples</h5>
        
        <p class="code-caption">Apply responsive layout to a widget</p>
    
    <pre class="sunlight-highlight-javascript">var ResponsiveWidget = function () {
  Widget.apply(this, arguments);
  responsiveMixIn(this, {
    &#x27;my-css-class-to-use-when-small&#x27;: { maxHeight: 768, maxWidth: 1024 },
    &#x27;my-css-class-to-use-when-square&#x27;: function (info) { return info.width === info.height; }
  });
};
ResponsiveWidget.prototype = Object.create(Widget.prototype);
ResponsiveWidget.prototype.doInitialize = function (dom) {
  dom.addClass(&#x27;ResponsiveWidget&#x27;);
  dom.html(myHtmlTemplate());
};

//  in css:
// .ResponsiveWidget.my-css-class-to-use-when-small {
//   background-color: white;
// }</pre>

        <p class="code-caption">Use a set of default classes</p>
    
    <pre class="sunlight-highlight-javascript">// ...
responsiveMixIn(this);

// in css:
// .ResponsiveWidget.tablet-portrait-up {
//   display: flex;
//   flex-flow: row wrap;
// }
// .ResponsiveWidget.tablet-landscape-up {
//   flex-flow: row nowrap;
// }</pre>


    
</dd>

        
    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id="applyParams"><span class="type-signature"></span>applyParams( [params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Can re-apply certain params that can also be passed to the constructor.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last">
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>readonly</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>enabled</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    true
                
                </td>
            

            <td class="description last"><p>must explicitly set to false to<br>
disable</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>properties</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.10</li>
		</ul>
	</dd>
	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#applyParams">module:bajaux/Widget#applyParams</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved after any<br>
<code>setEnabled</code>/<code>setReadonly</code> work is done. Note that these functions will not<br>
be called if the value of <code>enabled</code>/<code>readonly</code> is not actually changing.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="canApplyResponsiveClass"><span class="type-signature"></span>canApplyResponsiveClass(className [, mediaInfo])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Returns true if the class can be added/removed to/from a widget's<br>
DOM element.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>className</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>The name of the class to test for.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>mediaInfo</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_mixin_responsiveMixIn.html#~ResponsiveMediaInfo">module:bajaux/mixin/responsiveMixIn~ResponsiveMediaInfo</a></span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    this.getResponsiveMediaInfo()
                
                </td>
            

            <td class="description last"><p>The widget media info.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>Returns true if all the conditions for applying<br>
the class are met.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="changed"><span class="type-signature"></span>changed(name, value)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Called whenever a Widget's Property is changed.</p>
<p>If this Widget is not yet initialized, this is a no-op.</p>
<p>This function should not typically be overridden.<br>
<a href="module-bajaux_Widget.html#doChanged">doChanged()</a> should be overridden<br>
instead.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>The name of the Property that's changed.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>value</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            

            

            <td class="description last"><p>The new Property value.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#changed">module:bajaux/Widget#changed</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Widget.html#properties">module:bajaux/Widget#properties</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="cleanupDom"><span class="type-signature"></span>cleanupDom()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Called to clean up the DOM when the widget is being destroyed.</p>
<p>This method can be overridden if DOM clean up needs to be<br>
handled in a different way.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#cleanupDom">module:bajaux/Widget#cleanupDom</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="destroy"><span class="type-signature"></span>destroy( [params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Indicates that a widget is no longer needed and is in the process of being<br>
removed. In this function, subclasses can deallocate any resources, event<br>
handlers, etc. that they may be holding. Delegates the actual work to<br>
<code>doDestroy</code>.</p>
<p>This method will not typically be overridden. <code>doDestroy()</code> should be<br>
overridden instead.</p>
<p>Triggers a <code>bajaux:destroy</code> or <code>bajaux:destroyfail</code> event, as appropriate.</p>
<p>Please note, after <code>doDestroy</code> has resolved, the DOM will be emptied,<br>
all event handlers will be removed and the 'widget' data stored on the<br>
DOM element will be deleted.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>optional parameters to be passed to <code>doDestroy</code></p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#destroy">module:bajaux/Widget#destroy</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Widget.html#doDestroy">module:bajaux/Widget#doDestroy</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>A promise to be resolved when the widget has been<br>
destroyed</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="doChanged"><span class="type-signature"></span>doChanged(name, value)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Called by <a href="module-bajaux_Widget.html#changed">changed()</a> when a Property<br>
is changed.</p>
<p>This method is designed to be overridden by any subclasses.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>The name of the Property that's changed.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>value</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            

            

            <td class="description last"><p>The new Property value.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#doChanged">module:bajaux/Widget#doChanged</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>
|

<span class="param-type">*</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="doDestroy"><span class="type-signature"></span>doDestroy( [params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Called by <code>destroy</code> so this widget has a chance to clean up after itself<br>
and release any resources it is holding.</p>
<p>Notably, any jQuery event handlers registered on child elements of the<br>
widget's DOM element should be unregistered here. Also, you may want to<br>
remove any CSS classes you've added to the widget's DOM element.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>Optional params object passed to <code>destroy()</code></p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#doDestroy">module:bajaux/Widget#doDestroy</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Widget.html#destroy">module:bajaux/Widget#destroy</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>An optional promise that's resolved once the widget<br>
has been destroyed.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">*</span>
|

<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="doEnabled"><span class="type-signature"></span>doEnabled(enabled)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Called when the widget is enabled/disabled.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>enabled</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>the new enabled state.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#doEnabled">module:bajaux/Widget#doEnabled</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>An optional Promise that can be returned if<br>
the state change is asynchronous.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">*</span>
|

<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="doInitialize"><span class="type-signature"></span>doInitialize(element [, params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Performs the actual work of initializing the DOM element in which this<br>
widget will live. This function should be overridden by subclasses - the<br>
subclass function should append elements to <code>element</code> as<br>
necessary and then optionally return a promise.</p>
<p>Most commonly, this will involve building up the HTML structure<br>
necessary to load in a value. If this widget will display/edit a String,<br>
for example, <code>doInitialize</code> might append a text input element to<br>
the target element. A <code>DynamicEnum</code> might include a<br>
<code>&lt;select&gt;</code> dropdown.</p>
<p>In some cases, no initialization may be required at all. This<br>
might be the case if you are binding the widget to an HTML element that is<br>
already pre-populated with all the necessary structure to load a value,<br>
or maybe <code>doLoad</code> will empty out the element completely<br>
and rebuild it from scratch every time a new value is loaded. In this<br>
case, you do not need to override this method. (However, a widget that<br>
overrides neither <code>doInitialize</code> nor <code>doLoad</code> will not be very useful!)</p>
<p>Tip: the promises returned by <code>setEnabled</code> and <code>setReadonly</code> can<br>
only ever resolve after <code>initialize</code> itself resolves. So<br>
<code>return this.setEnabled(enabled)</code> or <code>return this.setReadonly(readonly)</code><br>
from <code>doInitialize</code> will result in a deadlock that will never resolve.<br>
They may be called, but not returned.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>element</code></td>
            

            <td class="type">
            
                
<span class="param-type">JQuery</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>The element in which this Widget should build its<br>
HTML.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>Optional params object passed into <code>initialize()</code>.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#doInitialize">module:bajaux/Widget#doInitialize</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Widget.html#initialize">module:bajaux/Widget#initialize</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>An optional promise to be resolved once the Widget<br>
has initialized.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">*</span>
|

<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="doLayout"><span class="type-signature"></span>doLayout( [params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Called when the layout of the Widget changes. This method is designed<br>
to be overridden.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>as of Niagara 4.10, any parameters passed to<br>
<code>layout()</code> will also be passed to <code>doLayout()</code>.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#doLayout">module:bajaux/Widget#doLayout</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>This method may optionally return a promise once the<br>
Widget has been laid out.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">*</span>
|

<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="doLoad"><span class="type-signature"></span>doLoad(value [, params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Performs the actual work of populating the widget's HTML to reflect the<br>
input value.</p>
<p>This function should be overridden by subclasses. The subclass function<br>
should manipulate the DOM <a href="module-bajaux_Widget.html#jq">jq()</a> and,<br>
optionally,return a promise to indicate that the work of loading the value<br>
has completed.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>value</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>The value to be loaded.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>Optional params object passed to <code>load()</code></p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#doLoad">module:bajaux/Widget#doLoad</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Widget.html#load">module:bajaux/Widget#load</a></li>
			
			<li><a href="module-bajaux_Widget.html#value">module:bajaux/Widget#value</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>An optional promise that's resolved once the widget has<br>
loaded.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="doModified"><span class="type-signature"></span>doModified(modified)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>The actual implementation for <code>setModified</code>. This function<br>
should do any work necessary when the widget is set to a modified or<br>
&quot;dirty&quot; state - typically enabling a save button, arming a<br>
<code>window.onbeforeunload</code> handler, etc. Likewise, it should do<br>
the opposite when setting modified to false.</p>
<p>Note that this is <em>synchronous</em>, as is <code>setModified</code>. Async work can be<br>
performed, but <code>setModified</code> will not wait for it.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>modified</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#doModified">module:bajaux/Widget#doModified</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Widget.html#setModified">module:bajaux/Widget#setModified</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="doRead"><span class="type-signature"></span>doRead()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Does the work of reading the widget's current representation.</p>
<p>This might mean reading a series of text inputs and assembling their<br>
values into an array. It might mean instantiating a copy of the backing<br>
<code>baja.Component</code> and setting slot values on the new copy.<br>
It might mean simply returning the boolean value of a checkbox. If your<br>
widget is composed of pure text/HTML and is not actually backed by an<br>
external value, it might mean returning <i>nothing</i>.</p>
<p>When saving a modified widget, the output of this function will be passed<br>
directly into this widget's validation process, so all your validation<br>
steps should be expecting to receive this. It will also be passed to<br>
<code>doSave</code>, so your <code>doSave</code> implementation should also expect this value.</p>
<p>The default behavior of <code>doRead</code> is simply to use the widget's current<br>
value.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#doRead">module:bajaux/Widget#doRead</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Widget.html#read">module:bajaux/Widget#read</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The read value, or a promise to be resolved with the<br>
read value</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">*</span>
|

<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="doReadonly"><span class="type-signature"></span>doReadonly(readonly)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Called when the widget is set to readonly or made writable.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>readonly</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>the new readonly state.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#doReadonly">module:bajaux/Widget#doReadonly</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>An optional Promise that can be returned if<br>
the state change is asynchronous.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">*</span>
|

<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="doSave"><span class="type-signature"></span>doSave(validValue [, params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Performs the actual work of saving the widget. This function should<br>
be overridden by subclasses to save the value. The subclass function<br>
should save the value and then, optionally, return a promise to indicate<br>
that the work of saving the widget has completed.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>validValue</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>The value to be used for saving. This value will have<br>
been read from the widget using <code>read()</code> and validated using <code>validate()</code>.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>Optional params object passed to <code>save()</code></p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#doSave">module:bajaux/Widget#doSave</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>An optional promise that's resolved once<br>
the widget has saved.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">*</span>
|

<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="generateId"><span class="type-signature"></span>generateId()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Generate a unique DOM ID. The ID will include the name of this widget's<br>
constructor just for tracing/debugging purposes.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#generateId">module:bajaux/Widget#generateId</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getChildWidgets"><span class="type-signature"></span>getChildWidgets( [params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Returns an array of child widgets living inside this editor's DOM.<br>
This method will specifically <em>not</em> return child widgets of child widgets</p>
<ul>
<li>for instance, if this widget has one child editor for a <code>baja.Facets</code>,<br>
you will only get a single widget back - it won't recurse down and give<br>
you all the tag editors, type editors etc.</li>
</ul>
<p>This is safer and easier than using <code>$.find()</code>, which recurses down,<br>
or carefully managing strings of <code>$.children()</code> calls.</p>
<p>Pass in a <code>jQuery</code> instance to limit the child editor search to<br>
a particular set of elements. Otherwise, will search all child elements<br>
of this editor's DOM.</p>
<p>If this editor has not initialized yet, you'll just get an empty array<br>
back.</p>
<p>The returned array will have some utility functions attached that return<br>
promises. See example for details.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>
|

<span class="param-type">jQuery</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last">
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>dom</code></td>
            

            <td class="type">
            
                
<span class="param-type">JQuery</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    this.jq().children()
                
                </td>
            

            <td class="description last"><p>the dom element to search</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>type</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>the widget type to search for - pass in the<br>
actual constructor, for <code>instanceof</code> checks</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.10</li>
		</ul>
	</dd>
	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#getChildWidgets">module:bajaux/Widget#getChildWidgets</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>an array of child widgets</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array.&lt;<a href="module-bajaux_Widget.html">module:bajaux/Widget</a>></span>



    </dd>
</dl>


        

    
        <h5>Examples</h5>
        
    <pre class="sunlight-highlight-javascript">var kids = ed.getChildWidgets();
  kids.setAllEnabled(false).then(function () {});
  kids.setAllModified(false).then(function () {});
  kids.setAllReadonly(false).then(function () {});
  kids.readAll().then(function (valuesArray) {});
  kids.validateAll().then(function (valuesArray) {});
  kids.saveAll().then(function () {});
  kids.destroyAll().then(function () {});</pre>

    <pre class="sunlight-highlight-javascript">var stringEditors = ed.getChildWidgets({ type: StringEditor });</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getCommandGroup"><span class="type-signature"></span>getCommandGroup()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the widget's command group.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#getCommandGroup">module:bajaux/Widget#getCommandGroup</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_commands_CommandGroup.html">module:bajaux/commands/CommandGroup</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getFormFactor"><span class="type-signature"></span>getFormFactor()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the widget's form-factor. The form-factor<br>
is normally passed in from the Widget's constructor. However,<br>
it can be set from a 'formFactor' property if required.</p>
<p>A widget's form-factor typically doesn't change during a widget's<br>
life-cycle.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#getFormFactor">module:bajaux/Widget#getFormFactor</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li>module:bajaux/Widget.formfactor</li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The form-factor.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getResponsiveMediaInfo"><span class="type-signature"></span>getResponsiveMediaInfo()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return media information for the widget. This is used in deciding<br>
whether to add/remove a CSS class to/from a widget when it's laid out.<br>
The information will come from the widget's container, not the widget<br>
itself. By default, it will look up the DOM hierarchy to find an element<br>
with the class <code>bajaux-widget-container</code>, which will be provided to you<br>
in most profiles (the HTML5 Hx Profile's content window, the element<br>
containing a widget in a Px view, etc.). If none is found, will use the<br>
dimensions of the browser window itself.</p>
<p>This method returns width and height but could contain more properties<br>
in future. Please note, any calculated pixel values should be<br>
floored to the nearest integer.</p>
<p>A developer can override this method to calculate the media information<br>
differently.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The media information for the widget.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_mixin_responsiveMixIn.html#~ResponsiveMediaInfo">module:bajaux/mixin/responsiveMixIn~ResponsiveMediaInfo</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="hasMixIn"><span class="type-signature"></span>hasMixIn(mixin)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if the widget implements the specified MixIn.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>mixin</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>the name of the mixin to test for.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#hasMixIn">module:bajaux/Widget#hasMixIn</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="initialize"><span class="type-signature"></span>initialize(dom [, params] [, layoutParams])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Initializes the DOM element to be bound to this Widget.</p>
<p>In a nutshell, <code>initialize</code> defines the following contract:</p>
<ul>
<li>After <code>initialize</code> completes and resolves its Promise, the target element will be fully<br>
initialized, structured, and ready to load in a value. It will be accessible by calling<br>
<code>this.jq()</code>.</li>
<li>If this is an editor, <code>load</code> may not be called until<br>
<code>initialize</code>'s promise is resolved. Attempting<br>
to load a value prior to initialization will result in failure.</li>
<li>This widget will be set as a jQuery data value on the initialized<br>
DOM element. It can be retrieved by calling <code>Widget.in(element)</code>.</li>
</ul>
<p><code>initialize</code> delegates the actual work of building the<br>
HTML structure (if any) to the <code>doInitialize</code> function. When<br>
subclassing Widget, you should not override <code>initialize</code>.<br>
<code>doInitialize</code> should be overridden.</p>
<p>After <code>initialize</code> completes, an  <code>bajaux:initialize</code> or<br>
<code>bajaux:initializefail</code> event will be triggered, as appropriate.</p>
<p><code>initialize</code> is a one-time operation. It will always reject if the widget has already been<br>
initialized once, or if it has been destroyed.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>dom</code></td>
            

            <td class="type">
            
                
<span class="param-type">JQuery</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>The jQuery DOM element in which this widget should<br>
build its HTML (will be passed directly to <code>doInitialize</code>)</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>optional parameters object to be passed through to<br>
<code>doInitialize</code></p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>layoutParams</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>as of Niagara 4.10, optional parameters object to<br>
be passed through to <code>layout</code></p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#initialize">module:bajaux/Widget#initialize</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Widget.html#doInitialize">module:bajaux/Widget#doInitialize</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>A promise to be resolved once the widget has<br>
initialized</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isDesignTime"><span class="type-signature"></span>isDesignTime()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Returns true if the Widget is in a graphic design editor.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#isDesignTime">module:bajaux/Widget#isDesignTime</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isDestroyed"><span class="type-signature"></span>isDestroyed()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if this Widget has already been destroyed. After destruction,<br>
<code>initialize()</code> will always reject: the widget cannot be reused.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#isDestroyed">module:bajaux/Widget#isDestroyed</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isEnabled"><span class="type-signature"></span>isEnabled()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Returns this widget's enabled state.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#isEnabled">module:bajaux/Widget#isEnabled</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Widget.html#setEnabled">module:bajaux/Widget#setEnabled</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isInitialized"><span class="type-signature"></span>isInitialized()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if this Widget is initialized.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#isInitialized">module:bajaux/Widget#isInitialized</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isLoading"><span class="type-signature"></span>isLoading()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Check if this widget is currently in the process of loading. This will<br>
return <code>true</code> immediately after <code>load</code> is called, and return <code>false</code><br>
after the <code>load</code> promise resolves.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#isLoading">module:bajaux/Widget#isLoading</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isModified"><span class="type-signature"></span>isModified()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Returns this widget's modified state.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#isModified">module:bajaux/Widget#isModified</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isReadonly"><span class="type-signature"></span>isReadonly()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Returns this widget's readonly state.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#isReadonly">module:bajaux/Widget#isReadonly</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Widget.html#setReadonly">module:bajaux/Widget#setReadonly</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="jq"><span class="type-signature"></span>jq()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Returns the jQuery DOM element in which this widget has been initialized.<br>
If <code>initialize()</code> has not yet been called, then this will return <code>null</code>.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#jq">module:bajaux/Widget#jq</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>the DOM element in which this widget has been<br>
initialized, or <code>null</code> if not yet initialized.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">JQuery</span>
|

<span class="param-type">null</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="layout"><span class="type-signature"></span>layout( [params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Overrides the bajaux Widget's <code>layout</code> method. This will add/remove<br>
class names to/from a widget's DOM element depending on whether<br>
its responsive conditions are met.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>as of Niagara 4.10, any parameters passed to<br>
<code>layout()</code> will also be passed to <code>doLayout()</code>.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_mixin_responsiveMixIn.html#layout">module:bajaux/mixin/responsiveMixIn#layout</a>
			</li>
		</ul>
	</dd>
	

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="module-bajaux_Widget.html#layout">module:bajaux/Widget#layout</a>
    </li></ul></dd>
    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Widget.html#layout">module:bajaux/Widget#layout</a></li>
			
			<li><a href="module-bajaux_Widget.html#doLayout">module:bajaux/Widget#doLayout</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="load"><span class="type-signature"></span>load(value [, params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Updates the widget's HTML with the given value. An widget for editing a<br>
string, for example, might load the string into a text input. A view for<br>
editing a <code>DynamicEnum</code> might programmatically set a <code>&lt;select&gt;</code><br>
dropdown's value.</p>
<p><code>load()</code> may not be called until <code>initialize()</code> has completed its work.<br>
If <code>initialize()</code> is not finished, <code>load()</code> will reject.</p>
<p>After <code>load()</code> completes its work, the value loaded will be accessible<br>
via <code>this.value()</code>.</p>
<p><code>load()</code> delegates the work of loading the HTML values to <code>doLoad()</code>.<br>
Subclasses will typically not override <code>load</code>, but more commonly will<br>
override <code>doLoad</code>.</p>
<p>After <code>load()</code> completes, a <code>bajaux:load</code> or <code>bajaux:loadfail</code> event will<br>
be triggered, as appropriate.</p>
<p>While this method is performing its work, <code>this.isLoading()</code> will return<br>
<code>true</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>value</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>The value to be loaded</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>additional parameters to be passed to <code>doLoad()</code></p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#load">module:bajaux/Widget#load</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Widget.html#doLoad">module:bajaux/Widget#doLoad</a></li>
			
			<li><a href="module-bajaux_Widget.html#value">module:bajaux/Widget#value</a></li>
			
			<li><a href="module-bajaux_mixin_batchLoadMixin.html">module:bajaux/mixin/batchLoadMixin</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>A promise to be resolved with the loaded value after<br>
the widget has been loaded, or rejected if the widget fails to load the<br>
value.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="loadAndModify"><span class="type-signature"></span>loadAndModify(value [, params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Loads in a new value, and sets the widget modified as well. Use this<br>
convenience method when you wish to load in a new value as if a user had<br>
done it, thereby triggering the necessary modify event handlers.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>value</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.12</li>
		</ul>
	</dd>
	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#loadAndModify">module:bajaux/Widget#loadAndModify</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise.&lt;*></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="properties"><span class="type-signature"></span>properties()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the Properties for a widget.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#properties">module:bajaux/Widget#properties</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The Properties for a widget.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_Properties.html">module:bajaux/Properties</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="read"><span class="type-signature"></span>read()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Read the current representation of the widget. For instance, if the widget<br>
is made up from two text input boxes, this might resolve an object with<br>
two strings from those text boxes.</p>
<p>Note the word &quot;representation&quot; - this function does not necessarily<br>
return the widget's actual value, but might assemble a different<br>
object, or array, or number, based on current user-entered values.</p>
<p><code>read</code> will not typically be overridden.<br>
<a href="module-bajaux_Widget.html#doRead">doRead()</a> should be overridden instead.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#read">module:bajaux/Widget#read</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Widget.html#doRead">module:bajaux/Widget#doRead</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>A promise that will be resolved with a value read from<br>
the widget as specified by <code>doRead</code>, or rejected if the read fails.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="requestFocus"><span class="type-signature"></span>requestFocus()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Attempts to place the cursor focus on this editor. For instance, if<br>
showing a simple string editor in a dialog, it should request focus so<br>
that the user can simply begin typing without having to move the mouse<br>
over to it and click.</p>
<p>Override this as necessary; by default, will place focus on the first<br>
<code>input</code> or <code>textarea</code> element in this editor's element.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.10</li>
		</ul>
	</dd>
	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#requestFocus">module:bajaux/Widget#requestFocus</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="resolve"><span class="type-signature"></span>resolve(data [, resolveParams])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Resolve a value from some data. Please note, this will not load the value<br>
but will resolve some data that could then be loaded by the widget.</p>
<p>By default, this will treat the data as an ORD so it can be resolved via<br>
BajaScript.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>data</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>
|

<span class="param-type">String</span>
|

<span class="param-type">baja.Ord</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>Specifies some data used to resolve a<br>
load value so <code>load(value)</code> can be called on the widget.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>resolveParams</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>An Object Literal used for ORD resolution.<br>
This parameter is designed to be used internally by bajaux and<br>
shouldn't be used by developers.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#resolve">module:bajaux/Widget#resolve</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>a promise to be resolved with the value resolved from<br>
the given data object</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="save"><span class="type-signature"></span>save( [params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Saves any outstanding user-entered changes to this widget. Triggers a<br>
<code>bajaux:save</code> or <code>bajaux:savefail</code> event, as appropriate.</p>
<p>In order to save the widget, its current value will be validated using<br>
<code>validate()</code>, then the validated value will be passed to <code>doSave()</code>.</p>
<p>This method will not typically be overridden.<br>
<a href="module-bajaux_Widget.html#doSave">doSave()</a> should be overridden instead.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>Additional parameters to be passed to <code>doSave()</code></p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#save">module:bajaux/Widget#save</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Widget.html#doSave">module:bajaux/Widget#doSave</a></li>
			
			<li><a href="module-bajaux_mixin_batchSaveMixin.html">module:bajaux/mixin/batchSaveMixin</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>A promise to be resolved once the widget has been saved,<br>
or rejected if the save fails.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setCommandGroup"><span class="type-signature"></span>setCommandGroup(commandGroup)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set this widget's command group. Triggers a <code>bajaux:changecommandgroup</code><br>
event.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>commandGroup</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_commands_CommandGroup.html">module:bajaux/commands/CommandGroup</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#setCommandGroup">module:bajaux/Widget#setCommandGroup</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setEnabled"><span class="type-signature"></span>setEnabled(enabled)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set this widget's enabled state.</p>
<p>Setting of the internal flag will be synchronous, so <code>isEnabled</code> will<br>
return the expected value immediately after calling this function. However,<br>
the actual work of updating the DOM cannot be performed until after the<br>
widget has finished initializing, so this method will return a promise.</p>
<p>This method will not typically be overridden. <code>doEnabled()</code> should be<br>
overridden instead.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>enabled</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>the new enabled state</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#setEnabled">module:bajaux/Widget#setEnabled</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Widget.html#isEnabled">module:bajaux/Widget#isEnabled</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>A promise to resolve immediately if <code>initialize</code> has<br>
not yet been called, that will resolve once the work of <code>initialize</code><br>
followed by <code>doEnabled</code> have both been completed. It will reject if<br>
<code>initialize</code> or <code>doEnabled</code> fail.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setModified"><span class="type-signature"></span>setModified(modified)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Sets this widget's modified or &quot;dirty&quot; status, to indicate that the user<br>
has made changes to this widget that may need to be saved.</p>
<p>The modification status will only be set if the widget is initialized<br>
and the widget is not loading a new value.</p>
<p>Triggers <code>bajaux:modify</code> or <code>bajaux:unmodify</code> depending on the input value.<br>
Any arguments passed to this function after the first will be passed<br>
through to the triggered event.</p>
<p>This method should not typically be overridden.<br>
<a href="module-bajaux_Widget.html#doModified">doModified()</a> should be overridden<br>
instead.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>modified</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>
|

<span class="param-type">*</span>



            
            </td>

            

            

            <td class="description last"><p>(a non-Boolean will be checked for truthiness)</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#setModified">module:bajaux/Widget#setModified</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Widget.html#doModified">module:bajaux/Widget#doModified</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    

    
        <h5>Example</h5>
        
        <p class="code-caption">Say I have collection of nested widgets in my DOM element.
  Whenever one of those widgets is modified, I want to mark myself
  modified but also provide the originally modified editor. For example,
  when a Property Sheet is modified, I want to know which row caused the
  modification.</p>
    
    <pre class="sunlight-highlight-javascript">var that = this;
  dom.on(events.MODIFY_EVENT, function (e, modifiedEd) {
    that.setModified(true, modifiedEd);
    return false;
  });</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setReadonly"><span class="type-signature"></span>setReadonly(readonly)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set this widget's readonly state.</p>
<p>Setting of the internal flag will be synchronous, so <code>isReadonly</code> will<br>
return the expected value immediately after calling this function. However,<br>
the actual work of updating the DOM cannot be performed until after the<br>
widget has finished initializing, so this method will return a promise.</p>
<p>This method will not typically be overridden. <code>doReadonly()</code> should be<br>
overridden instead.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>readonly</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>the new readonly state.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#setReadonly">module:bajaux/Widget#setReadonly</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Widget.html#isReadonly">module:bajaux/Widget#isReadonly</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>A promise to resolve immediately if <code>initialize</code> has<br>
not yet been called, that will resolve once the work of <code>initialize</code><br>
followed by <code>doReadonly</code> have both been completed. It will reject if<br>
<code>initialize</code> or <code>doReadonly</code> fail.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="toDescription"><span class="type-signature"></span>toDescription()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Access the widget's icon asynchronously.</p>
<p>By default this will attempt to access the widget's icon from<br>
the originating Lexicon. The Lexicon key should be in the format of<br>
<code>keyName.description</code>. If an entry can't be found then a blank string<br>
will be used.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#toDescription">module:bajaux/Widget#toDescription</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>A promise to be resolved with the widget's description</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="toDisplayName"><span class="type-signature"></span>toDisplayName()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Access the widget's display name asynchronously.</p>
<p>By default, this will attempt to access the widget's display name from<br>
the originating Lexicon. The Lexicon key should be in the format of<br>
<code>keyName.displayName</code>. If an entry can't be found then the Type's<br>
name will be used.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#toDisplayName">module:bajaux/Widget#toDisplayName</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>A promise to be resolved with the widget's display name</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="toIcon"><span class="type-signature"></span>toIcon()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Access the widget's icon asynchronously.</p>
<p>By default, this will attempt to access the widget's description from<br>
the originating Lexicon. The Lexicon key should be in the format of<br>
<code>keyName.icon</code>. If an entry can't be found then a blank String will be<br>
returned.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#toIcon">module:bajaux/Widget#toIcon</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>A promise to be resolved with the widget's icon URI.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="trigger"><span class="type-signature"></span>trigger()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Trigger a widget event. By default, this fires a DOM event on the associated<br>
widget's DOM element.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#trigger">module:bajaux/Widget#trigger</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="validate"><span class="type-signature"></span>validate()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Read the current value from the widget and validate it.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#validate">module:bajaux/Widget#validate</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Validators.html#validate">module:bajaux/Validators#validate</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>A promise to be resolved with the value read from the<br>
widget and passed through all validators, or rejected if the value could<br>
not be read or validated.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="validators"><span class="type-signature"></span>validators()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the widget's Validators.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#validators">module:bajaux/Widget#validators</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Validators.html">module:bajaux/Validators</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_Validators.html">module:bajaux/Validators</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="value"><span class="type-signature"></span>value()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Returns the widget's current loaded value. This the value that was last<br>
loaded via <code>load()</code>. To read a widget's current representation, reflecting<br>
any user-entered changes, call <code>read()</code>. If no value has been loaded yet,<br>
<code>null</code> is returned.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_Widget.html#value">module:bajaux/Widget#value</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Widget.html#load">module:bajaux/Widget#load</a></li>
			
			<li><a href="module-bajaux_Widget.html#doLoad">module:bajaux/Widget#doLoad</a></li>
			
			<li><a href="module-bajaux_Widget.html#read">module:bajaux/Widget#read</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>the loaded value, or <code>null</code> if a value hasn't been<br>
loaded yet.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">*</span>
|

<span class="param-type">null</span>



    </dd>
</dl>


        

    
</dd>

        </dl>
    

    
        <h3 class="subsection-title">Type Definitions</h3>

        <dl>
                
<hr>
<dt>
    <h4 class="name" id="~ResponsiveCallback"><span class="type-signature"></span>ResponsiveCallback(info)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>A callback that returns true if the condition is met.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>info</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_mixin_responsiveMixIn.html#~ResponsiveMediaInfo">module:bajaux/mixin/responsiveMixIn~ResponsiveMediaInfo</a></span>



            
            </td>

            

            

            <td class="description last"><p>The<br>
widget's current width, height, and any other associated media information.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>true if the current media info satisfies the condition.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">boolean</span>



    </dd>
</dl>


        

    
</dd>

            
                
<hr>
<dt class="name" id="~ResponsiveCondition">
    <h4 id="~ResponsiveCondition">ResponsiveCondition</h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>An object that defines some conditions for the associated class to be added<br>
to the widget's DOM element.</p>
    </div>
    

    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">Object</span>



            </li>
        </ul>
    

    
<dl class="details">
    

    <h5 class="subsection-title">Properties:</h5>

    <dl>

<table class="props table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>maxWidth</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>The widget's width in pixels must be less<br>
than or equal to this value.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>maxHeight</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>The widget's height in pixels must be less<br>
than or equal to this value.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>minWidth</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>The widget's width in pixels must be greater<br>
than or equal to this value.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>minHeight</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>The widget's height in pixels must be<br>
greater than or equal to this value.</p></td>
        </tr>

    
    </tbody>
</table>
</dl>

    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

            
                
<hr>
<dt class="name" id="~ResponsiveMediaInfo">
    <h4 id="~ResponsiveMediaInfo">ResponsiveMediaInfo</h4>

    
</dt>
<dd>
    

    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">Object</span>



            </li>
        </ul>
    

    
<dl class="details">
    

    <h5 class="subsection-title">Properties:</h5>

    <dl>

<table class="props table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>width</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>The width in pixels to use in responsive layout.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>height</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>The height in pixels to use in responsive layout.</p></td>
        </tr>

    
    </tbody>
</table>
</dl>

    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

            </dl>
    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	bajaux Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:54+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>