<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BIComponentFile" name="BIComponentFile" packageName="javax.baja.file" public="true" interface="true" abstract="true" category="interface">
<description>
BIComponentFile should be implemented by file type objects that&#xa; may be move/copied to a BComponent space but first require conversion&#xa; to a BComponent.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">12 Dec 06</tag>
<tag name="@version">$Revision: 4$ $Date: 6/11/07 12:41:23 PM EDT$</tag>
<tag name="@since">Niagara 3.2</tag>
<implements>
<type class="javax.baja.file.BIFile"/>
</implements>
<!-- javax.baja.file.BIComponentFile.readComponents() -->
<method name="readComponents"  public="true" abstract="true">
<description/>
<return>
<type class="javax.baja.space.Mark"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.file.BIComponentFile.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
