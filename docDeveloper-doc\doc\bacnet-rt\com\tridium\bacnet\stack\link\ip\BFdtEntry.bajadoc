<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.link.ip.BFdtEntry" name="BFdtEntry" packageName="com.tridium.bacnet.stack.link.ip" public="true">
<description>
BFdtEntry represents an entry in the&#xa; Foreign Device Table (FDT) of a Bacnet Broadcast Management&#xa; Device (BBMD).
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">18 Apr 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="bacnetIPAddress" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;bacnetIPAddress&lt;/code&gt; property.&#xa; the device&#x27;s Bacnet/IP address.
</description>
<tag name="@see">#getBacnetIPAddress</tag>
<tag name="@see">#setBacnetIPAddress</tag>
</property>

<property name="timeToLive" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;timeToLive&lt;/code&gt; property.&#xa; the Time-To-Live value supplied when this device registered&#xa; with the BBMD as a foreign device.
</description>
<tag name="@see">#getTimeToLive</tag>
<tag name="@see">#setTimeToLive</tag>
</property>

<property name="purgeTime" flags="rt">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;purgeTime&lt;/code&gt; property.&#xa; the time when this device will be purged from the table.  This is&#xa; not exact; the actual timing is done through a scheduled action.
</description>
<tag name="@see">#getPurgeTime</tag>
<tag name="@see">#setPurgeTime</tag>
</property>

</class>
</bajadoc>
