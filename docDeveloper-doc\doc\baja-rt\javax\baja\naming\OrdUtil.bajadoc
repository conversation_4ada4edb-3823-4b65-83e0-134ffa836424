<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.OrdUtil" name="OrdUtil" packageName="javax.baja.naming" public="true" final="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@creation">6/10/2017</tag>
<tag name="@since">Niagara 4.3U1</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- javax.baja.naming.OrdUtil.replaceBackups(javax.baja.naming.BOrd) -->
<method name="replaceBackups"  public="true" static="true">
<description>
Slot and file path ord queries may contain &#x22;../&#x22; to do relative traversal up the tree.  If&#xa; there is more than one backup, the ord will contain &#x22;/../&#x22;, which will be replaced by the&#xa; browser within a URL by removing other sections.  For example, https://127.0.0.1/a/b/c/d/../e/f&#xa; is converted to https://127.0.0.1/a/b/c/e/f and https://127.0.0.1/a/b/c/d/../../e/f is&#xa; converted to https://127.0.0.1/a/b/c/f.  This will result in unintended behavior in subsequent&#xa; ord resolution with that URL.  Therefore, all but the last &#x22;../&#x22; is replaced with &lt;code&gt;&amp;#x22;&amp;lt;schema&amp;gt;:..|&amp;#x22;&lt;/code&gt;.  This function is replicated in BajaScript by Ord.replaceBackups.
</description>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
<description>
ord that is searched for &#x22;../&#x22; backups
</description>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
<description>
the original ord if no changes are necessary or an updated ord with the necessary&#xa; replacements
</description>
</return>
</method>

<!-- javax.baja.naming.OrdUtil.getViewQuery(javax.baja.naming.BOrd) -->
<method name="getViewQuery"  public="true" static="true">
<description>
Retrieve the ViewQuery for an ord.
</description>
<tag name="@since">Niagara 4.12</tag>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
<description>
The ord to obtain the ViewQuery from.
</description>
</parameter>
<return>
<type class="javax.baja.naming.ViewQuery"/>
<description>
the ViewQuery from the ord.
</description>
</return>
</method>

<!-- javax.baja.naming.OrdUtil.getOrdWithoutViewQuery(javax.baja.naming.BOrd) -->
<method name="getOrdWithoutViewQuery"  public="true" static="true">
<description>
Retrieve the normalized Ord without the ViewQuery.
</description>
<tag name="@since">Niagara 4.12</tag>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
<description>
The ord to remove the ViewQuery from.
</description>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
<description>
The normalized ord without the View Query.
</description>
</return>
</method>

<!-- javax.baja.naming.OrdUtil.getOrdWithoutViewQueryParameters(javax.baja.naming.OrdTarget, java.util.List&lt;java.lang.String&gt;, boolean) -->
<method name="getOrdWithoutViewQueryParameters"  public="true" static="true">
<description>
Obtain the Ord from an OrdTarget without certain View Query parameters.&#xa; Any ViewQuery parameter keys left will be alphabetised to help with caching comparisons.
</description>
<tag name="@since">Niagara 4.12</tag>
<parameter name="target">
<type class="javax.baja.naming.OrdTarget"/>
<description>
This is the OrdTarget to remove View Query parameters from.
</description>
</parameter>
<parameter name="keysToRemove">
<parameterizedType class="java.util.List">
<args>
<type class="java.lang.String"/>
</args>
</parameterizedType>
<description>
The keys to remove from the ViewQuery.
</description>
</parameter>
<parameter name="removeViewId">
<type class="boolean"/>
<description>
Set this to true to also remove the viewId.
</description>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
<description>
The ord without the desired View Query parameters.
</description>
</return>
</method>

<!-- javax.baja.naming.OrdUtil.getOrdWithoutViewQueryParameters(javax.baja.naming.BOrd, java.util.List&lt;java.lang.String&gt;, boolean) -->
<method name="getOrdWithoutViewQueryParameters"  public="true" static="true">
<description/>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<parameter name="keysToRemove">
<parameterizedType class="java.util.List">
<args>
<type class="java.lang.String"/>
</args>
</parameterizedType>
</parameter>
<parameter name="removeViewId">
<type class="boolean"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

</class>
</bajadoc>
