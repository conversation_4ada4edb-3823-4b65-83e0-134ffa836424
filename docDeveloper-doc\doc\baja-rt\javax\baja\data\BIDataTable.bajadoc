<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.data.BIDataTable" name="BIDataTable" packageName="javax.baja.data" public="true" interface="true" abstract="true" category="interface">
<description>
A BIDataTable is a &lt;code&gt;<see ref="javax.baja.collection.BITable">BITable</see>&lt;/code&gt; with &lt;code&gt;<see ref="javax.baja.data.BIDataValue">BIDataValue</see>&lt;/code&gt; values for all cell values.
</description>
<tag name="@author">&lt;a href=&#x22;mailto:<EMAIL>&#x22;&gt;<PERSON>&lt;/a&gt;</tag>
<tag name="@author">&lt;a href=&#x22;mailto:<EMAIL>&#x22;&gt;Matthew Giannini&lt;/a&gt;</tag>
<implements>
<parameterizedType class="javax.baja.collection.BITable">
<args>
<typeVariable class="Row"/>
</args>
</parameterizedType>
</implements>
<typeParameters>
<typeVariable class="Row">
<bounds>
<type class="javax.baja.sys.BIObject"/>
</bounds>
</typeVariable>
</typeParameters>
<!-- javax.baja.data.BIDataTable.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
