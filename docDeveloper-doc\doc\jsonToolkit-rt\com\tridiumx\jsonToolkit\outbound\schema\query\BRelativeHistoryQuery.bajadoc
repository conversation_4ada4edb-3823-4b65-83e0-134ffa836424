<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.query.BRelativeHistoryQuery" name="BRelativeHistoryQuery" packageName="com.tridiumx.jsonToolkit.outbound.schema.query" public="true">
<description>
Experimental extension to a schema history designed to work in relative&#xa; schemas only. Substitutes the string &#x27;%baseHistoryOrd%&#x27; with a&#xa; history ord prefix e.g &#x27;history:/stationName/historyName&#x27; by searching&#xa; the current base item for a HistoryConfig.&#xa;&#xa; This will work if the base query for the schema returns:&#xa;&#xa; * HistoryConfigs&#xa; * HistoryExt&#xa; * ControlPoints with a HistoryExt
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQuery"/>
</extends>
<property name="queryPattern" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;queryPattern&lt;/code&gt; property.
</description>
<tag name="@see">#getQueryPattern</tag>
<tag name="@see">#setQueryPattern</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BRelativeHistoryQuery() -->
<constructor name="BRelativeHistoryQuery" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BRelativeHistoryQuery.getQueryPattern() -->
<method name="getQueryPattern"  public="true">
<description>
Get the &lt;code&gt;queryPattern&lt;/code&gt; property.
</description>
<tag name="@see">#queryPattern</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BRelativeHistoryQuery.setQueryPattern(java.lang.String) -->
<method name="setQueryPattern"  public="true">
<description>
Set the &lt;code&gt;queryPattern&lt;/code&gt; property.
</description>
<tag name="@see">#queryPattern</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BRelativeHistoryQuery.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BRelativeHistoryQuery.execute(javax.baja.sys.BComplex, javax.baja.sys.Context) -->
<method name="execute"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="base">
<type class="javax.baja.sys.BComplex"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<parameterizedType class="javax.baja.collection.BITable">
<args>
<wildcardType class="?">
</wildcardType>
</args>
</parameterizedType>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BRelativeHistoryQuery.queryOrd -->
<field name="queryOrd"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;queryOrd&lt;/code&gt; property.
</description>
<tag name="@see">#getQueryOrd</tag>
<tag name="@see">#setQueryOrd</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BRelativeHistoryQuery.queryPattern -->
<field name="queryPattern"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;queryPattern&lt;/code&gt; property.
</description>
<tag name="@see">#getQueryPattern</tag>
<tag name="@see">#setQueryPattern</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BRelativeHistoryQuery.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
