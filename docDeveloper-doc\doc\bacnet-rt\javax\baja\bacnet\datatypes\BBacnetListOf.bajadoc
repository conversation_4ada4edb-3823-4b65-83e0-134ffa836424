<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetListOf" name="BBacnetListOf" packageName="javax.baja.bacnet.datatypes" public="true">
<description>
BBacnetListOf represents a Bacnet ListOf sequence, which contains a non-indexed&#xa; sequence of objects of a particular Bacnet data type.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">08 May 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="listTypeSpec" flags="h">
<type class="javax.baja.util.BTypeSpec"/>
<description>
Slot for the &lt;code&gt;listTypeSpec&lt;/code&gt; property.
</description>
<tag name="@see">#getListTypeSpec</tag>
<tag name="@see">#setListTypeSpec</tag>
</property>

<topic name="listPropertyChanged" flags="">
<eventType>
<type class="javax.baja.sys.BValue"/>
</eventType><description>
Slot for the &lt;code&gt;listPropertyChanged&lt;/code&gt; topic.
</description>
<tag name="@see">#fireListPropertyChanged</tag>
</topic>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf() -->
<constructor name="BBacnetListOf" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf(javax.baja.sys.Type) -->
<constructor name="BBacnetListOf" public="true">
<parameter name="listType">
<type class="javax.baja.sys.Type"/>
<description>
the type of elements to be contained by this list.
</description>
</parameter>
<description>
Constructor with type specification.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.getListTypeSpec() -->
<method name="getListTypeSpec"  public="true">
<description>
Get the &lt;code&gt;listTypeSpec&lt;/code&gt; property.
</description>
<tag name="@see">#listTypeSpec</tag>
<return>
<type class="javax.baja.util.BTypeSpec"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.setListTypeSpec(javax.baja.util.BTypeSpec) -->
<method name="setListTypeSpec"  public="true">
<description>
Set the &lt;code&gt;listTypeSpec&lt;/code&gt; property.
</description>
<tag name="@see">#listTypeSpec</tag>
<parameter name="v">
<type class="javax.baja.util.BTypeSpec"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.fireListPropertyChanged(javax.baja.sys.BValue) -->
<method name="fireListPropertyChanged"  public="true">
<description>
Fire an event for the &lt;code&gt;listPropertyChanged&lt;/code&gt; topic.
</description>
<tag name="@see">#listPropertyChanged</tag>
<parameter name="event">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.started() -->
<method name="started"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true" final="true">
<description>
Changed.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.added(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="added"  public="true" final="true">
<description>
Added.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.removed(javax.baja.sys.Property, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="removed"  public="true" final="true">
<description>
Removed.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="old">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.subscribed() -->
<method name="subscribed"  public="true" final="true">
<description>
Callback when the component enters the subscribed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.unsubscribed() -->
<method name="unsubscribed"  public="true" final="true">
<description>
Callback when the component leaves the subscribed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.getAppliedCategoryMask() -->
<method name="getAppliedCategoryMask"  public="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.getCategoryMask() -->
<method name="getCategoryMask"  public="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.getPermissions(javax.baja.sys.Context) -->
<method name="getPermissions"  public="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.security.BPermissions"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.addElements(byte[], javax.baja.sys.Context) -->
<method name="addElements"  public="true" final="true">
<description>
Add element(s) to the list.
</description>
<parameter name="encodedElements">
<type class="byte" dimension="1"/>
<description/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ChangeListError"/>
<description>
null if successful, or an appropriate error if not.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.removeElements(byte[], javax.baja.sys.Context) -->
<method name="removeElements"  public="true" final="true">
<description>
Remove element(s) from the list.
</description>
<parameter name="encodedElements">
<type class="byte" dimension="1"/>
<description/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ChangeListError"/>
<description>
true if successful.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.contains(javax.baja.sys.BValue) -->
<method name="contains"  public="true" final="true">
<description>
Does the list contain the given element?
</description>
<parameter name="element">
<type class="javax.baja.sys.BValue"/>
<description/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if there is an element in the list that is &lt;code&gt;equivalent&lt;/code&gt;&#xa; to the given element.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.getListType() -->
<method name="getListType"  public="true" final="true">
<description>
Get the Baja type of objects contained by this list.
</description>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true" final="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true" final="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.addListElement(javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="addListElement"  public="true">
<description/>
<parameter name="listElement">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.sys.Property"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.removeListElement(javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="removeListElement"  public="true">
<description/>
<parameter name="listElement">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.listTypeSpec -->
<field name="listTypeSpec"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;listTypeSpec&lt;/code&gt; property.
</description>
<tag name="@see">#getListTypeSpec</tag>
<tag name="@see">#setListTypeSpec</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.listPropertyChanged -->
<field name="listPropertyChanged"  public="true" static="true" final="true">
<type class="javax.baja.sys.Topic"/>
<description>
Slot for the &lt;code&gt;listPropertyChanged&lt;/code&gt; topic.
</description>
<tag name="@see">#fireListPropertyChanged</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.addActions -->
<field name="addActions"  protected="true">
<type class="boolean"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetListOf.log -->
<field name="log"  protected="true" static="true" final="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

</class>
</bajadoc>
