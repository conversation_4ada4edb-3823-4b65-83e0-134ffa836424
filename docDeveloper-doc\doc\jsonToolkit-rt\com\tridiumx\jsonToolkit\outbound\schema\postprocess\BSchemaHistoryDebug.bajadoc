<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug" name="BSchemaHistoryDebug" packageName="com.tridiumx.jsonToolkit.outbound.schema.postprocess" public="true">
<description>
Util for seeing the history of output from a json schema.&#xa;&#xa; Useful for occasions where the output updates rapidly such as generate json called in quick succession by a link&#xa; or relative schema where the output is set once per base item very quickly.&#xa;&#xa; Use wisely - the historySize allows you to store more but you do not want to fill your heap up with json strings.&#xa; Remove this component once you have finished debugging.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="com.tridiumx.jsonToolkit.outbound.schema.postprocess.BIPostProcessor"/>
</implements>
<property name="enabled" flags="t">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;enabled&lt;/code&gt; property.
</description>
<tag name="@see">#getEnabled</tag>
<tag name="@see">#setEnabled</tag>
</property>

<property name="historyMaxSize" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;historyMaxSize&lt;/code&gt; property.
</description>
<tag name="@see">#getHistoryMaxSize</tag>
<tag name="@see">#setHistoryMaxSize</tag>
</property>

<action name="reset" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;reset&lt;/code&gt; action.
</description>
<tag name="@see">#reset()</tag>
</action>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug() -->
<constructor name="BSchemaHistoryDebug" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug.getEnabled() -->
<method name="getEnabled"  public="true">
<description>
Get the &lt;code&gt;enabled&lt;/code&gt; property.
</description>
<tag name="@see">#enabled</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug.setEnabled(boolean) -->
<method name="setEnabled"  public="true">
<description>
Set the &lt;code&gt;enabled&lt;/code&gt; property.
</description>
<tag name="@see">#enabled</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug.getHistoryMaxSize() -->
<method name="getHistoryMaxSize"  public="true">
<description>
Get the &lt;code&gt;historyMaxSize&lt;/code&gt; property.
</description>
<tag name="@see">#historyMaxSize</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug.setHistoryMaxSize(int) -->
<method name="setHistoryMaxSize"  public="true">
<description>
Set the &lt;code&gt;historyMaxSize&lt;/code&gt; property.
</description>
<tag name="@see">#historyMaxSize</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug.reset() -->
<method name="reset"  public="true">
<description>
Invoke the &lt;code&gt;reset&lt;/code&gt; action.
</description>
<tag name="@see">#reset</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug.postProcess(com.tridiumx.jsonToolkit.outbound.schema.BJsonSchema, java.lang.Exception) -->
<method name="postProcess"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="schema">
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchema"/>
</parameter>
<parameter name="exception">
<type class="java.lang.Exception"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug.historyAt(int) -->
<method name="historyAt"  public="true">
<description/>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug.currentSize() -->
<method name="currentSize"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug.doReset() -->
<method name="doReset"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unused&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug.enabled -->
<field name="enabled"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;enabled&lt;/code&gt; property.
</description>
<tag name="@see">#getEnabled</tag>
<tag name="@see">#setEnabled</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug.historyMaxSize -->
<field name="historyMaxSize"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;historyMaxSize&lt;/code&gt; property.
</description>
<tag name="@see">#getHistoryMaxSize</tag>
<tag name="@see">#setHistoryMaxSize</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug.reset -->
<field name="reset"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;reset&lt;/code&gt; action.
</description>
<tag name="@see">#reset()</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
