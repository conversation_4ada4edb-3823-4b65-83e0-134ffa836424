<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BIRemoteAlarmRecipient" name="BIRemoteAlarmRecipient" packageName="javax.baja.alarm" public="true" interface="true" abstract="true" category="interface">
<description>
RemoteAlarmRecipient iis a marker interface for AlarmRecipients that are remote&#xa; to the station that generates the alarm. &lt;br&gt;&#xa; Methods must be implemented as actions.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">23 Aug 02</tag>
<tag name="@version">$Revision: 3$ $Date: 3/30/05 11:35:59 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<!-- javax.baja.alarm.BIRemoteAlarmRecipient.routeAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="routeAlarm"  public="true" abstract="true">
<description/>
<parameter name="alarm">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.alarm.BIRemoteAlarmRecipient.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
