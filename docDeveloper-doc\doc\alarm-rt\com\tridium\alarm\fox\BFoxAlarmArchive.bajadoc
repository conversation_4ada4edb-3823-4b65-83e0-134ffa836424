<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.fox.BFoxAlarmArchive" name="BFoxAlarmArchive" packageName="com.tridium.alarm.fox" public="true">
<description>
Fox implementation of the Alarm Archive
</description>
<tag name="@since">Niagara 4.11</tag>
<extends>
<type class="javax.baja.alarm.BAlarmArchive"/>
</extends>
<implements>
<type class="com.tridium.fox.sys.BIFoxProxySpace"/>
</implements>
<implements>
<type class="javax.baja.bql.RemoteQueryable"/>
</implements>
<implements>
<type class="com.tridium.alarm.fox.BIFoxAlarmDatabase"/>
</implements>
</class>
</bajadoc>
