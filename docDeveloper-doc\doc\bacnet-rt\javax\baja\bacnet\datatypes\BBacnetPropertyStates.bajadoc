<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetPropertyStates" name="BBacnetPropertyStates" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BBacnetPropertyStates represents the BACnetPropertyStates&#xa; choice.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">23 Apr 04</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="choice" flags="h">
<type class="int"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates() -->
<constructor name="BBacnetPropertyStates" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.getChoice() -->
<method name="getChoice"  public="true">
<description>
Get the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.setChoice(int) -->
<method name="setChoice"  public="true">
<description>
Set the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.makeBinaryPv(boolean) -->
<method name="makeBinaryPv"  public="true" static="true">
<description/>
<parameter name="value">
<type class="boolean"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetPropertyStates"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.makeUnsigned(long) -->
<method name="makeUnsigned"  public="true" static="true">
<description/>
<parameter name="value">
<type class="long"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetPropertyStates"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.subscribed() -->
<method name="subscribed"  public="true" final="true">
<description>
Callback when the component enters the subscribed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.unsubscribed() -->
<method name="unsubscribed"  public="true" final="true">
<description>
Callback when the component leaves the subscribed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.getAppliedCategoryMask() -->
<method name="getAppliedCategoryMask"  public="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.getCategoryMask() -->
<method name="getCategoryMask"  public="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.getPermissions(javax.baja.sys.Context) -->
<method name="getPermissions"  public="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.security.BPermissions"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.choice -->
<field name="choice"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.BOOLEAN_VALUE_TAG -->
<field name="BOOLEAN_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.BINARY_VALUE_TAG -->
<field name="BINARY_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.EVENT_TYPE_TAG -->
<field name="EVENT_TYPE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.POLARITY_TAG -->
<field name="POLARITY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.PROGRAM_CHANGE_TAG -->
<field name="PROGRAM_CHANGE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.PROGRAM_STATE_TAG -->
<field name="PROGRAM_STATE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.REASON_FOR_HALT_TAG -->
<field name="REASON_FOR_HALT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.RELIABILITY_TAG -->
<field name="RELIABILITY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.STATE_TAG -->
<field name="STATE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.SYSTEM_STATUS_TAG -->
<field name="SYSTEM_STATUS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.UNITS_TAG -->
<field name="UNITS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.UNSIGNED_VALUE_TAG -->
<field name="UNSIGNED_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.LIFE_SAFETY_MODE_TAG -->
<field name="LIFE_SAFETY_MODE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.LIFE_SAFETY_STATE_TAG -->
<field name="LIFE_SAFETY_STATE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.RESTART_REASON_TAG -->
<field name="RESTART_REASON_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.DOOR_ALARM_TAG -->
<field name="DOOR_ALARM_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.ACTION_TAG -->
<field name="ACTION_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.DOOR_SECURED_STATUS_TAG -->
<field name="DOOR_SECURED_STATUS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.DOOR_STATUS_TAG -->
<field name="DOOR_STATUS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.DOOR_VALUE_TAG -->
<field name="DOOR_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.FILE_ACCESS_METHOD_TAG -->
<field name="FILE_ACCESS_METHOD_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.LOCK_STATUS_TAG -->
<field name="LOCK_STATUS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.LIFE_SAFETY_OPERATION_TAG -->
<field name="LIFE_SAFETY_OPERATION_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.MAINTENANCE_TAG -->
<field name="MAINTENANCE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.NODE_TYPE_TAG -->
<field name="NODE_TYPE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.NOTIFY_TYPE_TAG -->
<field name="NOTIFY_TYPE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.SECURITY_LEVEL_TAG -->
<field name="SECURITY_LEVEL_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.SHED_STATE_TAG -->
<field name="SHED_STATE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.SILENCED_STATE_TAG -->
<field name="SILENCED_STATE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.RESERVED29_TAG -->
<field name="RESERVED29_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.ACCESS_EVENT_TAG -->
<field name="ACCESS_EVENT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.ZONE_OCCUPANCY_TAG -->
<field name="ZONE_OCCUPANCY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.ACCESS_CREDENTIAL_DISABLE_REASON_TAG -->
<field name="ACCESS_CREDENTIAL_DISABLE_REASON_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.ACCESS_CREDENTIAL_DISABLE_TAG -->
<field name="ACCESS_CREDENTIAL_DISABLE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.AUTHENTICATION_STATUS_TAG -->
<field name="AUTHENTICATION_STATUS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.BACKUP_STATE_TAG -->
<field name="BACKUP_STATE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.WRITE_STATUS_TAG -->
<field name="WRITE_STATUS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.LIGHTING_IN_PROGRESS_TAG -->
<field name="LIGHTING_IN_PROGRESS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.LIGHTING_OPERATION_TAG -->
<field name="LIGHTING_OPERATION_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.LIGHTING_TRANSITION_TAG -->
<field name="LIGHTING_TRANSITION_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.BOOLEAN_VALUE_SLOT_NAME -->
<field name="BOOLEAN_VALUE_SLOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.BINARY_VALUE_SLOT_NAME -->
<field name="BINARY_VALUE_SLOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyStates.UNSIGNED_VALUE_SLOT_NAME -->
<field name="UNSIGNED_VALUE_SLOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

</class>
</bajadoc>
