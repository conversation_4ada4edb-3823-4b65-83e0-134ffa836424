<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.collection.Column" name="Column" packageName="javax.baja.collection" public="true" interface="true" abstract="true" category="interface">
<description>
Column is a handle to a column in a BITable.  It&#xa; also provides access to the column meta data.
</description>
<tag name="@author">&lt;a href=&#x22;mailto:<EMAIL>&#x22;&gt;<PERSON>&lt;/a&gt;</tag>
<!-- javax.baja.collection.Column.getName() -->
<method name="getName"  public="true" abstract="true">
<description>
Get the name of the column.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.collection.Column.getDisplayName(javax.baja.sys.Context) -->
<method name="getDisplayName"  public="true" abstract="true">
<description>
Get the display name for this column.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.collection.Column.getType() -->
<method name="getType"  public="true" abstract="true">
<description>
Get the type of the column values.
</description>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.collection.Column.getFlags() -->
<method name="getFlags"  public="true" abstract="true">
<description>
Get the flags for the column.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.collection.Column.getFacets() -->
<method name="getFacets"  public="true" abstract="true">
<description>
Get the facets for the column.
</description>
<return>
<type class="javax.baja.sys.BFacets"/>
<description>
Returns the facets or BFacets.NULL if no&#xa;   facets are defined for the column.
</description>
</return>
</method>

<!-- javax.baja.collection.Column.NO_COLUMNS -->
<field name="NO_COLUMNS"  public="true" static="true" final="true">
<type class="javax.baja.collection.Column" dimension="1"/>
<description/>
</field>

</class>
</bajadoc>
