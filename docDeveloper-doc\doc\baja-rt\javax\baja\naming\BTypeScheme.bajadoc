<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.BTypeScheme" name="BTypeScheme" packageName="javax.baja.naming" public="true" final="true">
<description>
An ORD scheme that resolves a type spec (moduleName:typeName)&#xa; to a &lt;code&gt;<see ref="javax.baja.util.BTypeSpec">javax.baja.util.BTypeSpec</see>&lt;/code&gt;.
</description>
<tag name="@author"><PERSON> on 17/09/2015.</tag>
<tag name="@since">Niagara 4.1</tag>
<extends>
<type class="javax.baja.naming.BOrdScheme"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraSingleton"/>
</annotation>
<!-- javax.baja.naming.BTypeScheme.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.naming.BTypeScheme.resolve(javax.baja.naming.OrdTarget, javax.baja.naming.OrdQuery) -->
<method name="resolve"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="base">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<parameter name="query">
<type class="javax.baja.naming.OrdQuery"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdTarget"/>
</return>
<throws>
<type class="javax.baja.naming.SyntaxException"/>
</throws>
<throws>
<type class="javax.baja.naming.UnresolvedException"/>
</throws>
</method>

<!-- javax.baja.naming.BTypeScheme.INSTANCE -->
<field name="INSTANCE"  public="true" static="true" final="true">
<type class="javax.baja.naming.BTypeScheme"/>
<description/>
</field>

<!-- javax.baja.naming.BTypeScheme.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
