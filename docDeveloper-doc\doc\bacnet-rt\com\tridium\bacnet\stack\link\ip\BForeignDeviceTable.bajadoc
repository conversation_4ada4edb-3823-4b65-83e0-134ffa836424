<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.link.ip.BForeignDeviceTable" name="BForeignDeviceTable" packageName="com.tridium.bacnet.stack.link.ip" public="true">
<description>
BForeignDeviceTable represents the Foreign&#xa; Device Table (FDT) of a Bacnet Broadcast Management&#xa; Device (BBMD).
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">22 Apr 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<action name="removeEntry" flags="h">
<parameter name="parameter">
<type class="com.tridium.bacnet.stack.link.ip.BFdtEntry"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;removeEntry&lt;/code&gt; action.&#xa; Remove an entry in the table because the timeToLive has expired.
</description>
<tag name="@see">#removeEntry(BFdtEntry parameter)</tag>
</action>

</class>
</bajadoc>
