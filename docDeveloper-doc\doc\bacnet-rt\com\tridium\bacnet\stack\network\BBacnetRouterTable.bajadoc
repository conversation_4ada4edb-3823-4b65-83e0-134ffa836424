<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.network.BBacnetRouterTable" name="BBacnetRouterTable" packageName="com.tridium.bacnet.stack.network" public="true">
<description>
BBacnetRouterTable stores the table of known routers to&#xa; Bacnet networks.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">20 Jun 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<action name="addRouter" flags="">
<parameter name="parameter">
<type class="com.tridium.bacnet.stack.network.BBacnetRouterEntry"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;addRouter&lt;/code&gt; action.
</description>
<tag name="@see">#addRouter(BBacnetRouterEntry parameter)</tag>
</action>

<action name="removeRouter" flags="">
<parameter name="parameter">
<type class="javax.baja.sys.BInteger"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;removeRouter&lt;/code&gt; action.
</description>
<tag name="@see">#removeRouter(BInteger parameter)</tag>
</action>

<action name="purgeExpiredEntries" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;purgeExpiredEntries&lt;/code&gt; action.
</description>
<tag name="@see">#purgeExpiredEntries()</tag>
</action>

<action name="purgeAllEntries" flags="c">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;purgeAllEntries&lt;/code&gt; action.
</description>
<tag name="@see">#purgeAllEntries()</tag>
</action>

</class>
</bajadoc>
