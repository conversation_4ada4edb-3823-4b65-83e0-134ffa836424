<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQueryFolder" name="BJsonSchemaQueryFolder" packageName="com.tridiumx.jsonToolkit.outbound.schema.query" public="true">
<description>
BJsonSchemaQueryFolder introduces an icon and allows easier parent / child legality checks
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.util.BFolder"/>
</extends>
<property name="queriesMaxExecutionTime" flags="h">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;queriesMaxExecutionTime&lt;/code&gt; property.
</description>
<tag name="@see">#getQueriesMaxExecutionTime</tag>
<tag name="@see">#setQueriesMaxExecutionTime</tag>
</property>

<property name="queryInterval" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;queryInterval&lt;/code&gt; property.
</description>
<tag name="@see">#getQueryInterval</tag>
<tag name="@see">#setQueryInterval</tag>
</property>

<property name="lastQueryCompletedTimestamp" flags="r">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;lastQueryCompletedTimestamp&lt;/code&gt; property.
</description>
<tag name="@see">#getLastQueryCompletedTimestamp</tag>
<tag name="@see">#setLastQueryCompletedTimestamp</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQueryFolder() -->
<constructor name="BJsonSchemaQueryFolder" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQueryFolder.getQueriesMaxExecutionTime() -->
<method name="getQueriesMaxExecutionTime"  public="true">
<description>
Get the &lt;code&gt;queriesMaxExecutionTime&lt;/code&gt; property.
</description>
<tag name="@see">#queriesMaxExecutionTime</tag>
<return>
<type class="javax.baja.sys.BRelTime"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQueryFolder.setQueriesMaxExecutionTime(javax.baja.sys.BRelTime) -->
<method name="setQueriesMaxExecutionTime"  public="true">
<description>
Set the &lt;code&gt;queriesMaxExecutionTime&lt;/code&gt; property.
</description>
<tag name="@see">#queriesMaxExecutionTime</tag>
<parameter name="v">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQueryFolder.getQueryInterval() -->
<method name="getQueryInterval"  public="true">
<description>
Get the &lt;code&gt;queryInterval&lt;/code&gt; property.
</description>
<tag name="@see">#queryInterval</tag>
<return>
<type class="javax.baja.sys.BRelTime"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQueryFolder.setQueryInterval(javax.baja.sys.BRelTime) -->
<method name="setQueryInterval"  public="true">
<description>
Set the &lt;code&gt;queryInterval&lt;/code&gt; property.
</description>
<tag name="@see">#queryInterval</tag>
<parameter name="v">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQueryFolder.getLastQueryCompletedTimestamp() -->
<method name="getLastQueryCompletedTimestamp"  public="true">
<description>
Get the &lt;code&gt;lastQueryCompletedTimestamp&lt;/code&gt; property.
</description>
<tag name="@see">#lastQueryCompletedTimestamp</tag>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQueryFolder.setLastQueryCompletedTimestamp(javax.baja.sys.BAbsTime) -->
<method name="setLastQueryCompletedTimestamp"  public="true">
<description>
Set the &lt;code&gt;lastQueryCompletedTimestamp&lt;/code&gt; property.
</description>
<tag name="@see">#lastQueryCompletedTimestamp</tag>
<parameter name="v">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQueryFolder.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQueryFolder.started() -->
<method name="started"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Queries are not cached by relative schema, therefore the query interval timer&#xa; causes a republish, hence renaming the property below to ease user understanding.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQueryFolder.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQueryFolder.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQueryFolder.isChildLegal(javax.baja.sys.BComponent) -->
<method name="isChildLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="child">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQueryFolder.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQueryFolder.queriesMaxExecutionTime -->
<field name="queriesMaxExecutionTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;queriesMaxExecutionTime&lt;/code&gt; property.
</description>
<tag name="@see">#getQueriesMaxExecutionTime</tag>
<tag name="@see">#setQueriesMaxExecutionTime</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQueryFolder.queryInterval -->
<field name="queryInterval"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;queryInterval&lt;/code&gt; property.
</description>
<tag name="@see">#getQueryInterval</tag>
<tag name="@see">#setQueryInterval</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQueryFolder.lastQueryCompletedTimestamp -->
<field name="lastQueryCompletedTimestamp"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lastQueryCompletedTimestamp&lt;/code&gt; property.
</description>
<tag name="@see">#getLastQueryCompletedTimestamp</tag>
<tag name="@see">#setLastQueryCompletedTimestamp</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaQueryFolder.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
