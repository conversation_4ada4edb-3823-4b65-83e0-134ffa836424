<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="axvelocity" runtimeProfile="rt" qualifiedName="javax.baja.velocity.BIVelocityWebProfile" name="BIVelocityWebProfile" packageName="javax.baja.velocity" public="true" interface="true" abstract="true" category="interface">
<description>
Interface for a Velocity Web Profile.&#xa; &lt;p&gt;&#xa; All Velocity Web Profiles must implement this interface.
</description>
<tag name="@author">g<PERSON><PERSON><PERSON></tag>
<tag name="@creation">29 Jul 2011</tag>
<tag name="@version">1</tag>
<tag name="@since">Niagara 3.7</tag>
<implements>
<type class="javax.baja.web.BIWebProfile"/>
</implements>
<!-- javax.baja.velocity.BIVelocityWebProfile.makeVelocityContext(javax.baja.velocity.BVelocityView, javax.baja.web.WebOp) -->
<method name="makeVelocityContext"  public="true" abstract="true">
<description>
Return a new VelocityContext.
</description>
<parameter name="view">
<type class="javax.baja.velocity.BVelocityView"/>
<description>
the view the Context is being created for
</description>
</parameter>
<parameter name="op">
<type class="javax.baja.web.WebOp"/>
<description>
the current request&#x27;s WebOp
</description>
</parameter>
<return>
<type class="org.apache.velocity.VelocityContext"/>
<description>
VelocityContext
</description>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.velocity.BIVelocityWebProfile.write(javax.baja.velocity.BVelocityView, org.apache.velocity.VelocityContext, javax.baja.web.WebOp) -->
<method name="write"  public="true" abstract="true">
<description>
Write the view.
</description>
<parameter name="view">
<type class="javax.baja.velocity.BVelocityView"/>
<description>
the view that needs to be written by the Profile
</description>
</parameter>
<parameter name="vContext">
<type class="org.apache.velocity.VelocityContext"/>
<description>
the Velocity Context used by the template generator
</description>
</parameter>
<parameter name="op">
<type class="javax.baja.web.WebOp"/>
<description>
the current request&#x27;s WebOp
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.velocity.BIVelocityWebProfile.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
