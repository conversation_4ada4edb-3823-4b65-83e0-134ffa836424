<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BBacnetAveraging" name="BBacnetAveraging" packageName="javax.baja.bacnet.config" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">25 Jul 2006</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="javax.baja.bacnet.BBacnetObject"/>
</extends>
<property name="facets" flags="r">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the minimumValue, averageValue,&#xa; and maximumValue properties.
</description>
<tag name="@see">#getFacets</tag>
<tag name="@see">#setFacets</tag>
</property>

<property name="minimumValue" flags="">
<type class="float"/>
<description>
Slot for the &lt;code&gt;minimumValue&lt;/code&gt; property.
</description>
<tag name="@see">#getMinimumValue</tag>
<tag name="@see">#setMinimumValue</tag>
</property>

<property name="averageValue" flags="">
<type class="float"/>
<description>
Slot for the &lt;code&gt;averageValue&lt;/code&gt; property.
</description>
<tag name="@see">#getAverageValue</tag>
<tag name="@see">#setAverageValue</tag>
</property>

<property name="maximumValue" flags="">
<type class="float"/>
<description>
Slot for the &lt;code&gt;maximumValue&lt;/code&gt; property.
</description>
<tag name="@see">#getMaximumValue</tag>
<tag name="@see">#setMaximumValue</tag>
</property>

<property name="attemptedSamples" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;attemptedSamples&lt;/code&gt; property.
</description>
<tag name="@see">#getAttemptedSamples</tag>
<tag name="@see">#setAttemptedSamples</tag>
</property>

<property name="validSamples" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;validSamples&lt;/code&gt; property.
</description>
<tag name="@see">#getValidSamples</tag>
<tag name="@see">#setValidSamples</tag>
</property>

<property name="objectPropertyReference" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
<description>
Slot for the &lt;code&gt;objectPropertyReference&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectPropertyReference</tag>
<tag name="@see">#setObjectPropertyReference</tag>
</property>

<property name="windowInterval" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;windowInterval&lt;/code&gt; property.
</description>
<tag name="@see">#getWindowInterval</tag>
<tag name="@see">#setWindowInterval</tag>
</property>

<property name="windowSamples" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;windowSamples&lt;/code&gt; property.
</description>
<tag name="@see">#getWindowSamples</tag>
<tag name="@see">#setWindowSamples</tag>
</property>

<!-- javax.baja.bacnet.config.BBacnetAveraging() -->
<constructor name="BBacnetAveraging" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetAveraging.getFacets() -->
<method name="getFacets"  public="true">
<description>
Get the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the minimumValue, averageValue,&#xa; and maximumValue properties.
</description>
<tag name="@see">#facets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.setFacets(javax.baja.sys.BFacets) -->
<method name="setFacets"  public="true">
<description>
Set the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the minimumValue, averageValue,&#xa; and maximumValue properties.
</description>
<tag name="@see">#facets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.getMinimumValue() -->
<method name="getMinimumValue"  public="true">
<description>
Get the &lt;code&gt;minimumValue&lt;/code&gt; property.
</description>
<tag name="@see">#minimumValue</tag>
<return>
<type class="float"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.setMinimumValue(float) -->
<method name="setMinimumValue"  public="true">
<description>
Set the &lt;code&gt;minimumValue&lt;/code&gt; property.
</description>
<tag name="@see">#minimumValue</tag>
<parameter name="v">
<type class="float"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.getAverageValue() -->
<method name="getAverageValue"  public="true">
<description>
Get the &lt;code&gt;averageValue&lt;/code&gt; property.
</description>
<tag name="@see">#averageValue</tag>
<return>
<type class="float"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.setAverageValue(float) -->
<method name="setAverageValue"  public="true">
<description>
Set the &lt;code&gt;averageValue&lt;/code&gt; property.
</description>
<tag name="@see">#averageValue</tag>
<parameter name="v">
<type class="float"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.getMaximumValue() -->
<method name="getMaximumValue"  public="true">
<description>
Get the &lt;code&gt;maximumValue&lt;/code&gt; property.
</description>
<tag name="@see">#maximumValue</tag>
<return>
<type class="float"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.setMaximumValue(float) -->
<method name="setMaximumValue"  public="true">
<description>
Set the &lt;code&gt;maximumValue&lt;/code&gt; property.
</description>
<tag name="@see">#maximumValue</tag>
<parameter name="v">
<type class="float"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.getAttemptedSamples() -->
<method name="getAttemptedSamples"  public="true">
<description>
Get the &lt;code&gt;attemptedSamples&lt;/code&gt; property.
</description>
<tag name="@see">#attemptedSamples</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.setAttemptedSamples(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setAttemptedSamples"  public="true">
<description>
Set the &lt;code&gt;attemptedSamples&lt;/code&gt; property.
</description>
<tag name="@see">#attemptedSamples</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.getValidSamples() -->
<method name="getValidSamples"  public="true">
<description>
Get the &lt;code&gt;validSamples&lt;/code&gt; property.
</description>
<tag name="@see">#validSamples</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.setValidSamples(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setValidSamples"  public="true">
<description>
Set the &lt;code&gt;validSamples&lt;/code&gt; property.
</description>
<tag name="@see">#validSamples</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.getObjectPropertyReference() -->
<method name="getObjectPropertyReference"  public="true">
<description>
Get the &lt;code&gt;objectPropertyReference&lt;/code&gt; property.
</description>
<tag name="@see">#objectPropertyReference</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.setObjectPropertyReference(javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference) -->
<method name="setObjectPropertyReference"  public="true">
<description>
Set the &lt;code&gt;objectPropertyReference&lt;/code&gt; property.
</description>
<tag name="@see">#objectPropertyReference</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.getWindowInterval() -->
<method name="getWindowInterval"  public="true">
<description>
Get the &lt;code&gt;windowInterval&lt;/code&gt; property.
</description>
<tag name="@see">#windowInterval</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.setWindowInterval(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setWindowInterval"  public="true">
<description>
Set the &lt;code&gt;windowInterval&lt;/code&gt; property.
</description>
<tag name="@see">#windowInterval</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.getWindowSamples() -->
<method name="getWindowSamples"  public="true">
<description>
Get the &lt;code&gt;windowSamples&lt;/code&gt; property.
</description>
<tag name="@see">#windowSamples</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.setWindowSamples(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setWindowSamples"  public="true">
<description>
Set the &lt;code&gt;windowSamples&lt;/code&gt; property.
</description>
<tag name="@see">#windowSamples</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.getSlotFacets(javax.baja.sys.Slot) -->
<method name="getSlotFacets"  public="true">
<description>
Apply the &#x22;facets&#x22; property to the &#x22;presentValue&#x22; property.
</description>
<parameter name="slot">
<type class="javax.baja.sys.Slot"/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAveraging.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetAveraging.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetAveraging.facets -->
<field name="facets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the minimumValue, averageValue,&#xa; and maximumValue properties.
</description>
<tag name="@see">#getFacets</tag>
<tag name="@see">#setFacets</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetAveraging.minimumValue -->
<field name="minimumValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;minimumValue&lt;/code&gt; property.
</description>
<tag name="@see">#getMinimumValue</tag>
<tag name="@see">#setMinimumValue</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetAveraging.averageValue -->
<field name="averageValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;averageValue&lt;/code&gt; property.
</description>
<tag name="@see">#getAverageValue</tag>
<tag name="@see">#setAverageValue</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetAveraging.maximumValue -->
<field name="maximumValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;maximumValue&lt;/code&gt; property.
</description>
<tag name="@see">#getMaximumValue</tag>
<tag name="@see">#setMaximumValue</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetAveraging.attemptedSamples -->
<field name="attemptedSamples"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;attemptedSamples&lt;/code&gt; property.
</description>
<tag name="@see">#getAttemptedSamples</tag>
<tag name="@see">#setAttemptedSamples</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetAveraging.validSamples -->
<field name="validSamples"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;validSamples&lt;/code&gt; property.
</description>
<tag name="@see">#getValidSamples</tag>
<tag name="@see">#setValidSamples</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetAveraging.objectPropertyReference -->
<field name="objectPropertyReference"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectPropertyReference&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectPropertyReference</tag>
<tag name="@see">#setObjectPropertyReference</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetAveraging.windowInterval -->
<field name="windowInterval"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;windowInterval&lt;/code&gt; property.
</description>
<tag name="@see">#getWindowInterval</tag>
<tag name="@see">#setWindowInterval</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetAveraging.windowSamples -->
<field name="windowSamples"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;windowSamples&lt;/code&gt; property.
</description>
<tag name="@see">#getWindowSamples</tag>
<tag name="@see">#setWindowSamples</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetAveraging.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
