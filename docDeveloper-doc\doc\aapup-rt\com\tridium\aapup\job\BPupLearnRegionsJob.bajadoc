<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.job.BPupLearnRegionsJob" name="BPupLearnRegionsJob" packageName="com.tridium.aapup.job" public="true">
<description>
PUP Region Discovery.&#xa; This job class handles the implementation of the scan for&#xa; regions to which SPL programs can be downloaded&#xa; &lt;p&gt;&#xa; At each address, a Say Hello message is sent for&#xa; the device code.  If an ACK response is received, we&#xa; have found a PUP device.&#xa; &lt;p&gt;&#xa; The discovery process uses its own timeout and retry values,&#xa; of 100ms and 0, respectively.
</description>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@creation">7/21/2005 2:32PM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.91</tag>
<extends>
<type class="javax.baja.job.BSimpleJob"/>
</extends>
<implements>
<type class="com.tridium.aapup.AaPupConst"/>
</implements>
<property name="learnedRegions" flags="">
<type class="javax.baja.util.BFolder"/>
<description>
Slot for the &lt;code&gt;learnedRegions&lt;/code&gt; property.&#xa; the folder where regions discovered are plopped.
</description>
<tag name="@see">#getLearnedRegions</tag>
<tag name="@see">#setLearnedRegions</tag>
</property>

<topic name="regionLearned" flags="">
<eventType>
<type class="javax.baja.sys.BValue"/>
</eventType><description>
Slot for the &lt;code&gt;regionLearned&lt;/code&gt; topic.&#xa; Whenever a region is learned this topic if fired to pass the discovery&#xa; object to the point manager.  If there are n regions in a device, this&#xa; will be fired n times.
</description>
<tag name="@see">#fireRegionLearned</tag>
</topic>

</class>
</bajadoc>
