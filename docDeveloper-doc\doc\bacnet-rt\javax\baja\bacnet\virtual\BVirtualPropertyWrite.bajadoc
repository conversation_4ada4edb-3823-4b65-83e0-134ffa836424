<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.virtual.BVirtualPropertyWrite" name="BVirtualPropertyWrite" packageName="javax.baja.bacnet.virtual" public="true">
<description/>
<extends>
<type class="javax.baja.sys.BAction"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<!-- javax.baja.bacnet.virtual.BVirtualPropertyWrite() -->
<constructor name="BVirtualPropertyWrite" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.virtual.BVirtualPropertyWrite.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BVirtualPropertyWrite.getParameterDefault() -->
<method name="getParameterDefault"  public="true">
<description>
Get the default parameter to use for the&#xa; action, or null if the action takes no&#xa; arguments.
</description>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BVirtualPropertyWrite.getParameterType() -->
<method name="getParameterType"  public="true">
<description>
Get the parameter type for the action, or&#xa; if the action takes no arguments return null.
</description>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BVirtualPropertyWrite.getReturnType() -->
<method name="getReturnType"  public="true">
<description>
Get the return type for the action, or&#xa; null if the action doesn&#x27;t return a value.
</description>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BVirtualPropertyWrite.invoke(javax.baja.sys.BComponent, javax.baja.sys.BValue) -->
<method name="invoke"  public="true">
<description>
Invoke the action on the specified target with&#xa; given argument array.
</description>
<parameter name="target">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<parameter name="arg">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.virtual.BVirtualPropertyWrite.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
