<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="baja" runtimeProfile="rt" name="javax.baja.category">
<description>
&lt;p&gt;Classes for categorizing objects.&lt;/p&gt;
</description>
<class packageName="javax.baja.category" name="BAbstractCategory"><description>BAbstractCategory contains properties for all category types.</description></class>
<class packageName="javax.baja.category" name="BCategory"><description>BCategory models a category.</description></class>
<class packageName="javax.baja.category" name="BCategoryMask"><description>BCategoryMask is a bitmask of category numbers.</description></class>
<class packageName="javax.baja.category" name="BCategoryMode"><description>BCategoryMode defines how a category is mapped into permissions&#xa; for an object.</description></class>
<class packageName="javax.baja.category" name="BCategoryService"><description>BCategoryService maps BCategoryMasks bits to BCategory components.</description></class>
<class packageName="javax.baja.category" name="BOrdToCategoryMap"><description>BOrdToCategoryMap stores a table mapping BOrds to BCategoryMasks.</description></class>
<class packageName="javax.baja.category" name="BICategorizable" category="interface"><description>BICategorizable is implemented by BObjects which can be assigned to&#xa; one or more categories through the use of a BCategoryMask.</description></class>
</package>
</bajadoc>
