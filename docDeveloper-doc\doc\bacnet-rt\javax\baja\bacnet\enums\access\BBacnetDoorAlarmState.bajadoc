<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.access.BBacnetDoorAlarmState" name="BBacnetDoorAlarmState" packageName="javax.baja.bacnet.enums.access" public="true" final="true">
<description>
BBacnetDoorAlarmState represents the Bacnet&#xa; Bacnet Door Alarm State enumeration.&#xa; &lt;p&gt;&#xa; BBacnetAccessAuthenticationFactorDisable is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Joseph Chandler</tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;normal&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;alarm&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;doorOpenForTooLong&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;forcedOpen&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tamper&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;doorFault&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lockDown&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;freeAccess&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;egressOpen&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetDoorAlarmState"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetDoorAlarmState"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.NORMAL -->
<field name="NORMAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for normal.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.ALARM -->
<field name="ALARM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for alarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.DOOR_OPEN_FOR_TOO_LONG -->
<field name="DOOR_OPEN_FOR_TOO_LONG"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for doorOpenForTooLong.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.FORCED_OPEN -->
<field name="FORCED_OPEN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for forcedOpen.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.TAMPER -->
<field name="TAMPER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tamper.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.DOOR_FAULT -->
<field name="DOOR_FAULT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for doorFault.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.LOCK_DOWN -->
<field name="LOCK_DOWN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lockDown.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.FREE_ACCESS -->
<field name="FREE_ACCESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for freeAccess.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.EGRESS_OPEN -->
<field name="EGRESS_OPEN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for egressOpen.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.normal -->
<field name="normal"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetDoorAlarmState"/>
<description>
BBacnetDoorAlarmState constant for normal.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.alarm -->
<field name="alarm"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetDoorAlarmState"/>
<description>
BBacnetDoorAlarmState constant for alarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.doorOpenForTooLong -->
<field name="doorOpenForTooLong"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetDoorAlarmState"/>
<description>
BBacnetDoorAlarmState constant for doorOpenForTooLong.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.forcedOpen -->
<field name="forcedOpen"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetDoorAlarmState"/>
<description>
BBacnetDoorAlarmState constant for forcedOpen.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.tamper -->
<field name="tamper"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetDoorAlarmState"/>
<description>
BBacnetDoorAlarmState constant for tamper.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.doorFault -->
<field name="doorFault"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetDoorAlarmState"/>
<description>
BBacnetDoorAlarmState constant for doorFault.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.lockDown -->
<field name="lockDown"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetDoorAlarmState"/>
<description>
BBacnetDoorAlarmState constant for lockDown.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.freeAccess -->
<field name="freeAccess"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetDoorAlarmState"/>
<description>
BBacnetDoorAlarmState constant for freeAccess.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.egressOpen -->
<field name="egressOpen"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetDoorAlarmState"/>
<description>
BBacnetDoorAlarmState constant for egressOpen.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetDoorAlarmState"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorAlarmState.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
