<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.util.worker.IBacnetAddress" name="IBacnetAddress" packageName="javax.baja.bacnet.util.worker" public="true" interface="true" abstract="true" category="interface">
<description>
The IBacnetAddress interface allows&#xa; a WorkerPool to distribute work&#xa; across a worker pool based on BBacnetAddress.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">26 Aug 13</tag>
<tag name="@since">Niagara 3.8 Bacnet 1.0</tag>
<!-- javax.baja.bacnet.util.worker.IBacnetAddress.getAddress() -->
<method name="getAddress"  public="true" abstract="true">
<description/>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</return>
</method>

</class>
</bajadoc>
