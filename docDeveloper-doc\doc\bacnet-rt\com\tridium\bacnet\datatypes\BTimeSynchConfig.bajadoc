<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.datatypes.BTimeSynchConfig" name="BTimeSynchConfig" packageName="com.tridium.bacnet.datatypes" public="true">
<description>
BTimeSynchConfig represents the choices for the&#xa; user in manually issuing a TimeSynch-Request to a device.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">04 Aug 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.datatypes.BRequestConfig"/>
</extends>
<property name="timeSynchType" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;timeSynchType&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeSynchType</tag>
<tag name="@see">#setTimeSynchType</tag>
</property>

<property name="addressRange" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;addressRange&lt;/code&gt; property.
</description>
<tag name="@see">#getAddressRange</tag>
<tag name="@see">#setAddressRange</tag>
</property>

</class>
</bajadoc>
