<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.data.TypeMismatchException" name="TypeMismatchException" packageName="javax.baja.data" public="true" category="exception">
<description>
TypeMismatchException is thrown when one type is encountered when&#xa; a different type is expected.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">19 Feb 2003</tag>
<tag name="@version">$Revision: 1$ $Date: 2/27/03 9:18:33 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BajaRuntimeException"/>
</extends>
<!-- javax.baja.data.TypeMismatchException(javax.baja.sys.Type, javax.baja.sys.Type) -->
<constructor name="TypeMismatchException" public="true">
<parameter name="expected">
<type class="javax.baja.sys.Type"/>
</parameter>
<parameter name="actual">
<type class="javax.baja.sys.Type"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.data.TypeMismatchException.getExpectedType() -->
<method name="getExpectedType"  public="true">
<description>
Get the type that was expected.
</description>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.data.TypeMismatchException.getActualType() -->
<method name="getActualType"  public="true">
<description>
Get the type that was actually encountered.
</description>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

</class>
</bajadoc>
