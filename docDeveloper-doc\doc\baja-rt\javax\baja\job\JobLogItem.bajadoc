<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.job.JobLogItem" name="JobLogItem" packageName="javax.baja.job" public="true" final="true">
<description>
JobLogItem is a record within a JobLog.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">27 Jul 04</tag>
<tag name="@version">$Revision: 5$ $Date: 8/19/09 11:00:38 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- javax.baja.job.JobLogItem(int, javax.baja.sys.BAbsTime, java.lang.String, java.lang.String) -->
<constructor name="JobLogItem" public="true">
<parameter name="id">
<type class="int"/>
</parameter>
<parameter name="timestamp">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<parameter name="messageFormatPattern">
<type class="java.lang.String"/>
</parameter>
<parameter name="details">
<type class="java.lang.String"/>
</parameter>
<description>
Construct an item with all four fields.
</description>
</constructor>

<!-- javax.baja.job.JobLogItem(int, java.lang.String, java.lang.Throwable) -->
<constructor name="JobLogItem" public="true">
<parameter name="id">
<type class="int"/>
</parameter>
<parameter name="messageFormatPattern">
<type class="java.lang.String"/>
</parameter>
<parameter name="exception">
<type class="java.lang.Throwable"/>
</parameter>
<description>
Construct with timestamp set to current time. The &#xa; specified exception&#x27;s dump is used for the details.
</description>
</constructor>

<!-- javax.baja.job.JobLogItem(int, java.lang.String, java.lang.String, java.lang.String[], java.lang.Throwable) -->
<constructor name="JobLogItem" public="true">
<parameter name="id">
<type class="int"/>
</parameter>
<parameter name="lexModule">
<type class="java.lang.String"/>
</parameter>
<parameter name="lexKey">
<type class="java.lang.String"/>
</parameter>
<parameter name="lexArgPatterns">
<type class="java.lang.String" dimension="1"/>
</parameter>
<parameter name="exception">
<type class="java.lang.Throwable"/>
</parameter>
<description>
Construct with timestamp set to current time. The &#xa; specified exception&#x27;s dump is used for the details.
</description>
<tag name="@since">Niagara 3.5</tag>
</constructor>

<!-- javax.baja.job.JobLogItem(int, java.lang.String, java.lang.String) -->
<constructor name="JobLogItem" public="true">
<parameter name="id">
<type class="int"/>
</parameter>
<parameter name="messageFormatPattern">
<type class="java.lang.String"/>
</parameter>
<parameter name="details">
<type class="java.lang.String"/>
</parameter>
<description>
Construct with timestamp set to current time.
</description>
</constructor>

<!-- javax.baja.job.JobLogItem(int, java.lang.String, java.lang.String, java.lang.String[], java.lang.String) -->
<constructor name="JobLogItem" public="true">
<parameter name="id">
<type class="int"/>
</parameter>
<parameter name="lexModule">
<type class="java.lang.String"/>
</parameter>
<parameter name="lexKey">
<type class="java.lang.String"/>
</parameter>
<parameter name="lexArgPatterns">
<type class="java.lang.String" dimension="1"/>
</parameter>
<parameter name="details">
<type class="java.lang.String"/>
</parameter>
<description>
Construct with timestamp set to current time.
</description>
<tag name="@since">Niagara 3.5</tag>
</constructor>

<!-- javax.baja.job.JobLogItem(int, java.lang.String) -->
<constructor name="JobLogItem" public="true">
<parameter name="id">
<type class="int"/>
</parameter>
<parameter name="messageFormatPattern">
<type class="java.lang.String"/>
</parameter>
<description>
Construct with timestamp set to current time and no details.
</description>
</constructor>

<!-- javax.baja.job.JobLogItem(int, java.lang.String, java.lang.String, java.lang.String[]) -->
<constructor name="JobLogItem" public="true">
<parameter name="id">
<type class="int"/>
</parameter>
<parameter name="lexModule">
<type class="java.lang.String"/>
</parameter>
<parameter name="lexKey">
<type class="java.lang.String"/>
</parameter>
<parameter name="lexArgPatterns">
<type class="java.lang.String" dimension="1"/>
</parameter>
<description>
Construct with timestamp set to current time and no details.
</description>
<tag name="@since">Niagara 3.5</tag>
</constructor>

<!-- javax.baja.job.JobLogItem.getId() -->
<method name="getId"  public="true">
<description>
Get the id constant.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.job.JobLogItem.getIdString() -->
<method name="getIdString"  public="true">
<description>
Get the id as a string.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.job.JobLogItem.getIdStringPattern() -->
<method name="getIdStringPattern"  public="true">
<description>
Get the BFormat pattern for the item&#x27;s ID string
</description>
<tag name="@since">Niagara 3.5</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.job.JobLogItem.getIdString(javax.baja.sys.Context) -->
<method name="getIdString"  public="true">
<description>
Get the id as a localized String
</description>
<tag name="@since">Niagara 3.5</tag>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.job.JobLogItem.getTimestamp() -->
<method name="getTimestamp"  public="true">
<description>
Get the timestamp of the item.
</description>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.job.JobLogItem.getMessage() -->
<method name="getMessage"  public="true">
<description>
Get the message string.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.job.JobLogItem.getMessagePattern() -->
<method name="getMessagePattern"  public="true">
<description>
Get the BFormat pattern for the item&#x27;s message
</description>
<tag name="@since">Niagara 3.5</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.job.JobLogItem.getMessage(javax.baja.sys.Context) -->
<method name="getMessage"  public="true">
<description>
Get the message as a localized string
</description>
<tag name="@since">Niagara 3.5</tag>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.job.JobLogItem.getDetails() -->
<method name="getDetails"  public="true">
<description>
Get the details or return null if no details are available.&#xa; Typically details contain a stack dump of an exception.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.job.JobLogItem.getDetailsPattern() -->
<method name="getDetailsPattern"  public="true">
<description>
Get the BFormat pattern for the item&#x27;s details, or null if no details are&#xa; available.
</description>
<tag name="@since">Niagara 3.5</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.job.JobLogItem.getDetails(javax.baja.sys.Context) -->
<method name="getDetails"  public="true">
<description>
Get the details as a localized String or return null if no details are available.&#xa; Typically details contain a stack dump of an exception.
</description>
<tag name="@since">Niagara 3.5</tag>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.job.JobLogItem.toString() -->
<method name="toString"  public="true">
<description>
Return a string for the item.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.job.JobLogItem.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
Return a localized string for the item.
</description>
<tag name="@since">Niagara 3.5</tag>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.job.JobLogItem.encode() -->
<method name="encode"  public="true">
<description>
Encode into a string line.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.job.JobLogItem.decode(java.lang.String) -->
<method name="decode"  public="true" static="true">
<description>
Decode from a string line.
</description>
<parameter name="line">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.job.JobLogItem"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.job.JobLogItem.MESSAGE -->
<field name="MESSAGE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.job.JobLogItem.RUNNING -->
<field name="RUNNING"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.job.JobLogItem.CANCELED -->
<field name="CANCELED"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.job.JobLogItem.SUCCESS -->
<field name="SUCCESS"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.job.JobLogItem.FAILED -->
<field name="FAILED"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
