<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetAddressBinding" name="BBacnetAddressBinding" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BBacnetAddressBinding represents the BacnetAddressBinding&#xa; sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">13 Aug 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="deviceObjectId" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;deviceObjectId&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceObjectId</tag>
<tag name="@see">#setDeviceObjectId</tag>
</property>

<property name="deviceAddress" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
Slot for the &lt;code&gt;deviceAddress&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceAddress</tag>
<tag name="@see">#setDeviceAddress</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetAddressBinding() -->
<constructor name="BBacnetAddressBinding" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetAddressBinding(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, javax.baja.bacnet.datatypes.BBacnetAddress) -->
<constructor name="BBacnetAddressBinding" public="true">
<parameter name="deviceObjectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<description>
Standard constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetAddressBinding.getDeviceObjectId() -->
<method name="getDeviceObjectId"  public="true">
<description>
Get the &lt;code&gt;deviceObjectId&lt;/code&gt; property.
</description>
<tag name="@see">#deviceObjectId</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetAddressBinding.setDeviceObjectId(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="setDeviceObjectId"  public="true">
<description>
Set the &lt;code&gt;deviceObjectId&lt;/code&gt; property.
</description>
<tag name="@see">#deviceObjectId</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetAddressBinding.getDeviceAddress() -->
<method name="getDeviceAddress"  public="true">
<description>
Get the &lt;code&gt;deviceAddress&lt;/code&gt; property.
</description>
<tag name="@see">#deviceAddress</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetAddressBinding.setDeviceAddress(javax.baja.bacnet.datatypes.BBacnetAddress) -->
<method name="setDeviceAddress"  public="true">
<description>
Set the &lt;code&gt;deviceAddress&lt;/code&gt; property.
</description>
<tag name="@see">#deviceAddress</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetAddressBinding.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetAddressBinding.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetAddressBinding.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetAddressBinding.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetAddressBinding.deviceObjectId -->
<field name="deviceObjectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;deviceObjectId&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceObjectId</tag>
<tag name="@see">#setDeviceObjectId</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetAddressBinding.deviceAddress -->
<field name="deviceAddress"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;deviceAddress&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceAddress</tag>
<tag name="@see">#setDeviceAddress</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetAddressBinding.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetAddressBinding.MAX_ENCODED_SIZE -->
<field name="MAX_ENCODED_SIZE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
