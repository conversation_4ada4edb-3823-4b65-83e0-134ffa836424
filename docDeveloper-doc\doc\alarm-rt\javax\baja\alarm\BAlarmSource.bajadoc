<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BAlarmSource" name="BAlarmSource" packageName="javax.baja.alarm" public="true">
<description>
An AlarmSource represents a source for in the Niagara alarm database.  It&#xa; is a wrapper for the alarm source ord list and can be used as the target&#xa; of agent relationships.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">05 Jan 2011</tag>
<tag name="@version">$Revision: 2$ $Date: 1/11/11 2:32:18 PM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BObject"/>
</extends>
<!-- javax.baja.alarm.BAlarmSource(javax.baja.naming.BOrdList, javax.baja.alarm.BAlarmRecord) -->
<constructor name="BAlarmSource" public="true">
<parameter name="sourceOrdList">
<type class="javax.baja.naming.BOrdList"/>
</parameter>
<parameter name="currentAlarm">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.alarm.BAlarmSource.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSource.getSourceOrdList() -->
<method name="getSourceOrdList"  public="true">
<description/>
<return>
<type class="javax.baja.naming.BOrdList"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSource.getCurrentAlarm() -->
<method name="getCurrentAlarm"  public="true">
<description/>
<return>
<type class="javax.baja.alarm.BAlarmRecord"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSource.setCurrentAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="setCurrentAlarm"  public="true">
<description/>
<parameter name="rec">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSource.hashCode() -->
<method name="hashCode"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSource.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description/>
<parameter name="other">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSource.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSource.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
