<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetEventType" name="BBacnetEventType" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetEventType represents the Bacnet Event Type&#xa; enumeration.  BBacnetEventType is an &#x22;extensible&#x22; enumeration.&#xa; &lt;p&gt;&#xa; BBacnetEventType is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Craig Gemmill</tag>
<tag name="@version">$Revision: 4$ $Date: 11/28/01 6:14:21 AM$</tag>
<tag name="@creation">07 Aug 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;changeOfBitstring&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;changeOfState&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;changeOfValue&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;commandFailure&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;floatingLimit&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;outOfRange&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;complexEventType&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bufferReadyDeprecated&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;changeOfLifeSafety&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;extended&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bufferReady&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unsignedRange&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;reserved&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accessEvent&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;doubleOutOfRange&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;signedOutOfRange&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unsignedOutOfRange&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;changeOfCharacterstring&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;changeOfStatusFlags&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;changeOfReliability&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;none&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetEventType.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventType.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventType.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventType.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventType.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventType.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventType.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventType.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventType.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventType.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
String representation of this BEnum.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventType.CHANGE_OF_BITSTRING -->
<field name="CHANGE_OF_BITSTRING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for changeOfBitstring.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.CHANGE_OF_STATE -->
<field name="CHANGE_OF_STATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for changeOfState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.CHANGE_OF_VALUE -->
<field name="CHANGE_OF_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for changeOfValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.COMMAND_FAILURE -->
<field name="COMMAND_FAILURE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for commandFailure.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.FLOATING_LIMIT -->
<field name="FLOATING_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for floatingLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.OUT_OF_RANGE -->
<field name="OUT_OF_RANGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for outOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.COMPLEX_EVENT_TYPE -->
<field name="COMPLEX_EVENT_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for complexEventType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.BUFFER_READY_DEPRECATED -->
<field name="BUFFER_READY_DEPRECATED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bufferReadyDeprecated.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.CHANGE_OF_LIFE_SAFETY -->
<field name="CHANGE_OF_LIFE_SAFETY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for changeOfLifeSafety.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.EXTENDED -->
<field name="EXTENDED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for extended.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.BUFFER_READY -->
<field name="BUFFER_READY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bufferReady.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.UNSIGNED_RANGE -->
<field name="UNSIGNED_RANGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unsignedRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.RESERVED -->
<field name="RESERVED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for reserved.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.ACCESS_EVENT -->
<field name="ACCESS_EVENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accessEvent.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.DOUBLE_OUT_OF_RANGE -->
<field name="DOUBLE_OUT_OF_RANGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for doubleOutOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.SIGNED_OUT_OF_RANGE -->
<field name="SIGNED_OUT_OF_RANGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for signedOutOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.UNSIGNED_OUT_OF_RANGE -->
<field name="UNSIGNED_OUT_OF_RANGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unsignedOutOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.CHANGE_OF_CHARACTERSTRING -->
<field name="CHANGE_OF_CHARACTERSTRING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for changeOfCharacterstring.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.CHANGE_OF_STATUS_FLAGS -->
<field name="CHANGE_OF_STATUS_FLAGS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for changeOfStatusFlags.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.CHANGE_OF_RELIABILITY -->
<field name="CHANGE_OF_RELIABILITY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for changeOfReliability.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.NONE -->
<field name="NONE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for none.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.changeOfBitstring -->
<field name="changeOfBitstring"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for changeOfBitstring.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.changeOfState -->
<field name="changeOfState"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for changeOfState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.changeOfValue -->
<field name="changeOfValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for changeOfValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.commandFailure -->
<field name="commandFailure"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for commandFailure.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.floatingLimit -->
<field name="floatingLimit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for floatingLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.outOfRange -->
<field name="outOfRange"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for outOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.complexEventType -->
<field name="complexEventType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for complexEventType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.bufferReadyDeprecated -->
<field name="bufferReadyDeprecated"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for bufferReadyDeprecated.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.changeOfLifeSafety -->
<field name="changeOfLifeSafety"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for changeOfLifeSafety.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.extended -->
<field name="extended"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for extended.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.bufferReady -->
<field name="bufferReady"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for bufferReady.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.unsignedRange -->
<field name="unsignedRange"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for unsignedRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.reserved -->
<field name="reserved"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for reserved.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.accessEvent -->
<field name="accessEvent"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for accessEvent.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.doubleOutOfRange -->
<field name="doubleOutOfRange"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for doubleOutOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.signedOutOfRange -->
<field name="signedOutOfRange"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for signedOutOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.unsignedOutOfRange -->
<field name="unsignedOutOfRange"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for unsignedOutOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.changeOfCharacterstring -->
<field name="changeOfCharacterstring"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for changeOfCharacterstring.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.changeOfStatusFlags -->
<field name="changeOfStatusFlags"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for changeOfStatusFlags.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.changeOfReliability -->
<field name="changeOfReliability"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for changeOfReliability.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.none -->
<field name="none"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
BBacnetEventType constant for none.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventType.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
