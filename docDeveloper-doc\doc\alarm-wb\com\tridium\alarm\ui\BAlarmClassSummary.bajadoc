<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.ui.BAlarmClassSummary" name="BAlarmClassSummary" packageName="com.tridium.alarm.ui" public="true">
<description>
BAlarmClassManager is a plugin for managing all alarm classes&#xa; in a station.  It is registered as a view on the BAlarmService.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">09 Dec 2003</tag>
<tag name="@version">$Revision: 6$ $Date: 5/28/10 1:39:23 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.workbench.view.BWbComponentView"/>
</extends>
<action name="updateCommands" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;updateCommands&lt;/code&gt; action.
</description>
<tag name="@see">#updateCommands()</tag>
</action>

</class>
</bajadoc>
