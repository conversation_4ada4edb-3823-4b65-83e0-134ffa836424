<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetChannelValue" name="BBacnetChannelValue" packageName="javax.baja.bacnet.datatypes" public="true">
<description>
BBacnetChannelValue represents the BACnetChannelValue&#xa; choice.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="choice" flags="h">
<type class="int"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue() -->
<constructor name="BBacnetChannelValue" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.getChoice() -->
<method name="getChoice"  public="true">
<description>
Get the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.setChoice(int) -->
<method name="setChoice"  public="true">
<description>
Set the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.choice -->
<field name="choice"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.NULL_TAG -->
<field name="NULL_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.REAL_TAG -->
<field name="REAL_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.ENUMERATED_TAG -->
<field name="ENUMERATED_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.UNSIGNED_TAG -->
<field name="UNSIGNED_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.BOOLEAN_TAG -->
<field name="BOOLEAN_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.SIGNED_TAG -->
<field name="SIGNED_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.DOUBLE_TAG -->
<field name="DOUBLE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.TIME_TAG -->
<field name="TIME_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.CHARACTERSTRING_TAG -->
<field name="CHARACTERSTRING_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.OCTETSTRING_TAG -->
<field name="OCTETSTRING_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.BITSTRING_TAG -->
<field name="BITSTRING_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.DATE_TAG -->
<field name="DATE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.OBJECTID_TAG -->
<field name="OBJECTID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.LIGHTCOMMAND_TAG -->
<field name="LIGHTCOMMAND_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetChannelValue.LIGHTCOMMAND_SUB_TAG -->
<field name="LIGHTCOMMAND_SUB_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
