<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaNumericProperty" name="BJsonSchemaNumericProperty" packageName="com.tridiumx.jsonToolkit.outbound.schema.property" public="true">
<description>
A fixed numeric key / value json pair. The name and value are governed by user input.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<parameterizedType class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaProperty">
<args>
<type class="javax.baja.sys.BNumber"/>
</args>
</parameterizedType>
</extends>
<property name="numericValue" flags="">
<type class="double"/>
<description>
Slot for the &lt;code&gt;numericValue&lt;/code&gt; property.
</description>
<tag name="@see">#getNumericValue</tag>
<tag name="@see">#setNumericValue</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaNumericProperty() -->
<constructor name="BJsonSchemaNumericProperty" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaNumericProperty.getNumericValue() -->
<method name="getNumericValue"  public="true">
<description>
Get the &lt;code&gt;numericValue&lt;/code&gt; property.
</description>
<tag name="@see">#numericValue</tag>
<return>
<type class="double"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaNumericProperty.setNumericValue(double) -->
<method name="setNumericValue"  public="true">
<description>
Set the &lt;code&gt;numericValue&lt;/code&gt; property.
</description>
<tag name="@see">#numericValue</tag>
<parameter name="v">
<type class="double"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaNumericProperty.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaNumericProperty.make(javax.baja.sys.BNumber) -->
<method name="make"  public="true" static="true">
<description/>
<parameter name="value">
<type class="javax.baja.sys.BNumber"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaNumericProperty"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaNumericProperty.getJsonValue() -->
<method name="getJsonValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BDouble"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaNumericProperty.numericValue -->
<field name="numericValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;numericValue&lt;/code&gt; property.
</description>
<tag name="@see">#getNumericValue</tag>
<tag name="@see">#setNumericValue</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaNumericProperty.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
