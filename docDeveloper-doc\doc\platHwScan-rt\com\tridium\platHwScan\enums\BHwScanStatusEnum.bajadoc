<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="platHwScan" runtimeProfile="rt" qualifiedName="com.tridium.platHwScan.enums.BHwScanStatusEnum" name="BHwScanStatusEnum" packageName="com.tridium.platHwScan.enums" public="true" final="true">
<description>
BHwScanStatusEnum&#xa;&#xa;    available       - This indicates the port may be used by an application.  It does not reflect&#xa;                      whether an appliation has claimed the port.&#xa;    dedicated       - The port has a pre-assigned specific function based on associated hardware.&#xa;                      An example is the second port of a GPRS modem, which is used for control.&#xa;    disabled        - The port has been disabled.&#xa;    empty           - The option card slot or usb port is empty.&#xa;    incompatible    - The option card is electrically incompatible with the slot.&#xa;    inUse           - Exclusive control of the port has been taken over by some other process.&#xa;                      An example would be the ModbusAsyncNetwork using base board&#x27;s COM1.&#xa;    normalOperation - The hardware jumper is in the normal operation position.&#xa;    notLicensed     - The port&#x27;s functionality must be enabled via a feature in the license.&#xa;    serialShell     - The hardware jumper is in the serial shell position.&#xa;    unavailable     - Control of the port has been taken over by some other process.&#xa;                      An example is the Modem option card can take control of the base board&#x27;s COM1.&#xa;    unknown         - The status can not be determined.&#xa;    none            - This is the default status for BHardwareRef&#xa;    devicePresent   - There is a device present in the USB port.
</description>
<tag name="@author">Frank Smith</tag>
<tag name="@creation">24 Jun 11</tag>
<tag name="@version">$Revision: 1$ $Date: 7/22/11 5:19:28 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;available&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;dedicated&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;disabled&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;empty&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;incompatible&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;inUse&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;normalOperation&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;notLicensed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;serialShell&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unavailable&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknown&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;none&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;devicePresent&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="com.tridium.platHwScan.enums.BHwScanStatusEnum"/>
</return>
</method>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridium.platHwScan.enums.BHwScanStatusEnum"/>
</return>
</method>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.AVAILABLE -->
<field name="AVAILABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for available.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.DEDICATED -->
<field name="DEDICATED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for dedicated.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.DISABLED -->
<field name="DISABLED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for disabled.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.EMPTY -->
<field name="EMPTY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for empty.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.INCOMPATIBLE -->
<field name="INCOMPATIBLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for incompatible.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.IN_USE -->
<field name="IN_USE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for inUse.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.NORMAL_OPERATION -->
<field name="NORMAL_OPERATION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for normalOperation.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.NOT_LICENSED -->
<field name="NOT_LICENSED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for notLicensed.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.SERIAL_SHELL -->
<field name="SERIAL_SHELL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for serialShell.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.UNAVAILABLE -->
<field name="UNAVAILABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unavailable.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.UNKNOWN -->
<field name="UNKNOWN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknown.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.NONE -->
<field name="NONE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for none.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.DEVICE_PRESENT -->
<field name="DEVICE_PRESENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for devicePresent.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.available -->
<field name="available"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BHwScanStatusEnum"/>
<description>
BHwScanStatusEnum constant for available.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.dedicated -->
<field name="dedicated"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BHwScanStatusEnum"/>
<description>
BHwScanStatusEnum constant for dedicated.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.disabled -->
<field name="disabled"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BHwScanStatusEnum"/>
<description>
BHwScanStatusEnum constant for disabled.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.empty -->
<field name="empty"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BHwScanStatusEnum"/>
<description>
BHwScanStatusEnum constant for empty.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.incompatible -->
<field name="incompatible"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BHwScanStatusEnum"/>
<description>
BHwScanStatusEnum constant for incompatible.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.inUse -->
<field name="inUse"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BHwScanStatusEnum"/>
<description>
BHwScanStatusEnum constant for inUse.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.normalOperation -->
<field name="normalOperation"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BHwScanStatusEnum"/>
<description>
BHwScanStatusEnum constant for normalOperation.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.notLicensed -->
<field name="notLicensed"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BHwScanStatusEnum"/>
<description>
BHwScanStatusEnum constant for notLicensed.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.serialShell -->
<field name="serialShell"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BHwScanStatusEnum"/>
<description>
BHwScanStatusEnum constant for serialShell.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.unavailable -->
<field name="unavailable"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BHwScanStatusEnum"/>
<description>
BHwScanStatusEnum constant for unavailable.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.unknown -->
<field name="unknown"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BHwScanStatusEnum"/>
<description>
BHwScanStatusEnum constant for unknown.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.none -->
<field name="none"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BHwScanStatusEnum"/>
<description>
BHwScanStatusEnum constant for none.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.devicePresent -->
<field name="devicePresent"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BHwScanStatusEnum"/>
<description>
BHwScanStatusEnum constant for devicePresent.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BHwScanStatusEnum"/>
<description/>
</field>

<!-- com.tridium.platHwScan.enums.BHwScanStatusEnum.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
