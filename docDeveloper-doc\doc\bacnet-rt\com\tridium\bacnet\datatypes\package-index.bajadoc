<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="com.tridium.bacnet.datatypes">
<description/>
<class packageName="com.tridium.bacnet.datatypes" name="BAddArrayElementAction"><description>BAddArrayElementAction is used to create the dynamic action&#xa; for adding elements to a BBacnetArray.</description></class>
<class packageName="com.tridium.bacnet.datatypes" name="BAddListElementAction"><description>BAddListElementAction is used to create the dynamic action&#xa; for adding list elements to a BBacnetListOf.</description></class>
<class packageName="com.tridium.bacnet.datatypes" name="BChangeDeviceIdConfig"/>
<class packageName="com.tridium.bacnet.datatypes" name="BDeviceDiscoveryConfig"><description>This class file specifies parameters to constrain a&#xa; network device discovery request.</description></class>
<class packageName="com.tridium.bacnet.datatypes" name="BDiscoveryNetworks"><description>Networks to be interrogated in a device discovery command.</description></class>
<class packageName="com.tridium.bacnet.datatypes" name="BEventSaver"/>
<class packageName="com.tridium.bacnet.datatypes" name="BNcRecipientList"/>
<class packageName="com.tridium.bacnet.datatypes" name="BNextInstArgs"><description>This class file specifies parameters to an invocation&#xa; of the BBacnetExportTable.getNextInst Action.</description></class>
<class packageName="com.tridium.bacnet.datatypes" name="BPriorityFilter"><description>BPriorityFilter represents the structure for restricting the types&#xa; of event-initiating objects that shall be included in a response to a&#xa; GetEnrollmentSummary-Request, based on their priority.</description></class>
<class packageName="com.tridium.bacnet.datatypes" name="BReadFileConfig"><description>This class file specifies parameters to constrain a&#xa; read file request.</description></class>
<class packageName="com.tridium.bacnet.datatypes" name="BRemoveArrayElementAction"><description>BRemoveArrayElementAction is used to create the dynamic action&#xa; for removing elements from a BBacnetArray.</description></class>
<class packageName="com.tridium.bacnet.datatypes" name="BRemoveListElementAction"><description>BRemoveListElementAction is used to create the dynamic action&#xa; for removing list elements from a BBacnetListOf.</description></class>
<class packageName="com.tridium.bacnet.datatypes" name="BRequestConfig"><description>This class file specifies parameters to constrain a&#xa; network device request.</description></class>
<class packageName="com.tridium.bacnet.datatypes" name="BSvoSubordinate"/>
<class packageName="com.tridium.bacnet.datatypes" name="BTimeSynchConfig"><description>BTimeSynchConfig represents the choices for the&#xa; user in manually issuing a TimeSynch-Request to a device.</description></class>
<class packageName="com.tridium.bacnet.datatypes" name="BTrendEvent"><description>BTrendEvent is the wrapper class for expressing trend log events&#xa; (such as failures, time changes, log status) as Java primitive&#xa; long objects.</description></class>
<class packageName="com.tridium.bacnet.datatypes" name="BWhoHasConfig"><description>This class file specifies parameters to constrain a&#xa; Who-Has request.</description></class>
<class packageName="com.tridium.bacnet.datatypes" name="BWriteFileConfig"><description>This class file specifies parameters to constrain a&#xa; read file request.</description></class>
</package>
</bajadoc>
