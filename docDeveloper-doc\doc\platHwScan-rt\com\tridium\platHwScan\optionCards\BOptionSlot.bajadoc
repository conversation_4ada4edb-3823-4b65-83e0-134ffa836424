<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="platHwScan" runtimeProfile="rt" qualifiedName="com.tridium.platHwScan.optionCards.BOptionSlot" name="BOptionSlot" packageName="com.tridium.platHwScan.optionCards" public="true">
<description>
BOptionSlot represents the electrical connection for JACE option cards
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">11 Aug 10</tag>
<tag name="@version">$Revision: 1$ $Date: 7/22/11 5:19:32 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="com.tridium.platHwScan.BCardSlot"/>
</extends>
<property name="slotNumber" flags="r">
<type class="int"/>
<description>
Slot for the &lt;code&gt;slotNumber&lt;/code&gt; property.&#xa; Defines the slot number
</description>
<tag name="@see">#getSlotNumber</tag>
<tag name="@see">#setSlotNumber</tag>
</property>

<!-- com.tridium.platHwScan.optionCards.BOptionSlot() -->
<constructor name="BOptionSlot" public="true">
<description>
Constructors
</description>
</constructor>

<!-- com.tridium.platHwScan.optionCards.BOptionSlot(int) -->
<constructor name="BOptionSlot" public="true">
<parameter name="slot">
<type class="int"/>
</parameter>
<description/>
</constructor>

<!-- com.tridium.platHwScan.optionCards.BOptionSlot(int, com.tridium.platHwScan.optionCards.BOptionCard) -->
<constructor name="BOptionSlot" public="true">
<parameter name="slot">
<type class="int"/>
</parameter>
<parameter name="optionCard">
<type class="com.tridium.platHwScan.optionCards.BOptionCard"/>
</parameter>
<description/>
</constructor>

<!-- com.tridium.platHwScan.optionCards.BOptionSlot.getSlotNumber() -->
<method name="getSlotNumber"  public="true">
<description>
Get the &lt;code&gt;slotNumber&lt;/code&gt; property.&#xa; Defines the slot number
</description>
<tag name="@see">#slotNumber</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridium.platHwScan.optionCards.BOptionSlot.setSlotNumber(int) -->
<method name="setSlotNumber"  public="true">
<description>
Set the &lt;code&gt;slotNumber&lt;/code&gt; property.&#xa; Defines the slot number
</description>
<tag name="@see">#slotNumber</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.platHwScan.optionCards.BOptionSlot.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.platHwScan.optionCards.BOptionSlot.addCard(com.tridium.platHwScan.optionCards.BOptionCard) -->
<method name="addCard"  public="true">
<description/>
<parameter name="optionCard">
<type class="com.tridium.platHwScan.optionCards.BOptionCard"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.platHwScan.optionCards.BOptionSlot.slotNumber -->
<field name="slotNumber"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;slotNumber&lt;/code&gt; property.&#xa; Defines the slot number
</description>
<tag name="@see">#getSlotNumber</tag>
<tag name="@see">#setSlotNumber</tag>
</field>

<!-- com.tridium.platHwScan.optionCards.BOptionSlot.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
