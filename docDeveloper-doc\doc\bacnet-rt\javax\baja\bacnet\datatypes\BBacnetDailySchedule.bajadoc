<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetDailySchedule" name="BBacnetDailySchedule" packageName="javax.baja.bacnet.datatypes" public="true">
<description>
BBacnetDailySchedule represents the BacnetDailySchedule sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">6 June 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<action name="addTimeValue" flags="">
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetTimeValue"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;addTimeValue&lt;/code&gt; action.
</description>
<tag name="@see">#addTimeValue(BBacnetTimeValue parameter)</tag>
</action>

<action name="removeTimeValue" flags="">
<parameter name="parameter">
<type class="javax.baja.sys.BString"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;removeTimeValue&lt;/code&gt; action.
</description>
<tag name="@see">#removeTimeValue(BString parameter)</tag>
</action>

<topic name="dailyScheduleChanged" flags="">
<eventType>
<type class="javax.baja.sys.BValue"/>
</eventType><description>
Slot for the &lt;code&gt;dailyScheduleChanged&lt;/code&gt; topic.
</description>
<tag name="@see">#fireDailyScheduleChanged</tag>
</topic>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule() -->
<constructor name="BBacnetDailySchedule" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.addTimeValue(javax.baja.bacnet.datatypes.BBacnetTimeValue) -->
<method name="addTimeValue"  public="true">
<description>
Invoke the &lt;code&gt;addTimeValue&lt;/code&gt; action.
</description>
<tag name="@see">#addTimeValue</tag>
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetTimeValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.removeTimeValue(javax.baja.sys.BString) -->
<method name="removeTimeValue"  public="true">
<description>
Invoke the &lt;code&gt;removeTimeValue&lt;/code&gt; action.
</description>
<tag name="@see">#removeTimeValue</tag>
<parameter name="parameter">
<type class="javax.baja.sys.BString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.fireDailyScheduleChanged(javax.baja.sys.BValue) -->
<method name="fireDailyScheduleChanged"  public="true">
<description>
Fire an event for the &lt;code&gt;dailyScheduleChanged&lt;/code&gt; topic.
</description>
<tag name="@see">#dailyScheduleChanged</tag>
<parameter name="event">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.doAddTimeValue(javax.baja.bacnet.datatypes.BBacnetTimeValue) -->
<method name="doAddTimeValue"  public="true" final="true">
<description>
Add a Time-Value pair to this daily schedule.
</description>
<parameter name="tv">
<type class="javax.baja.bacnet.datatypes.BBacnetTimeValue"/>
<description>
the BacnetTimeValue to add.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.doRemoveTimeValue(javax.baja.sys.BString) -->
<method name="doRemoveTimeValue"  public="true" final="true">
<description>
Remove the named Time-Value pair.
</description>
<parameter name="tvName">
<type class="javax.baja.sys.BString"/>
<description>
the name of the BacnetTimeValue to remove.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true" final="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true" final="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.started() -->
<method name="started"  public="true">
<description>
Started.&#xa; Subclasses &lt;b&gt;MUST&lt;/b&gt; call &lt;code&gt;super.started()&lt;/code&gt;&#xa; to ensure that this code is executed.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.added(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="added"  public="true">
<description>
Property added.&#xa; Subclasses &lt;b&gt;MUST&lt;/b&gt; call &lt;code&gt;super.added()&lt;/code&gt;&#xa; to ensure that this code is executed.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
<description>
the property.
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
the context.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.removed(javax.baja.sys.Property, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="removed"  public="true">
<description>
Property removed.&#xa; Subclasses &lt;b&gt;MUST&lt;/b&gt; call &lt;code&gt;super.removed()&lt;/code&gt;&#xa; to ensure that this code is executed.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
<description>
the property.
</description>
</parameter>
<parameter name="v">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
the context.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<description>
Property changed.&#xa; Subclasses &lt;b&gt;MUST&lt;/b&gt; call &lt;code&gt;super.changed()&lt;/code&gt;&#xa; to ensure that this code is executed.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
<description>
the property.
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
the context.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.subscribed() -->
<method name="subscribed"  public="true" final="true">
<description>
Callback when the component enters the subscribed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.unsubscribed() -->
<method name="unsubscribed"  public="true" final="true">
<description>
Callback when the component leaves the subscribed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.getAppliedCategoryMask() -->
<method name="getAppliedCategoryMask"  public="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.getCategoryMask() -->
<method name="getCategoryMask"  public="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.getPermissions(javax.baja.sys.Context) -->
<method name="getPermissions"  public="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.security.BPermissions"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.getValue(javax.baja.sys.BTime) -->
<method name="getValue"  public="true" final="true">
<description>
Get the value at a particular time.&#xa; This scans through all of the time-values.  For each time-value,&#xa; check if it is before the given time, and after the last&#xa; time encountered so far.  If so, mark it as the last time.&#xa; When all pairs have been checked, return the value of the last&#xa; time-value pair.
</description>
<parameter name="at">
<type class="javax.baja.sys.BTime"/>
<description>
the time at which the value of the daily schedule is desired.
</description>
</parameter>
<return>
<type class="javax.baja.sys.BSimple"/>
<description>
the value at the specified time.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.addTimeValue -->
<field name="addTimeValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;addTimeValue&lt;/code&gt; action.
</description>
<tag name="@see">#addTimeValue(BBacnetTimeValue parameter)</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.removeTimeValue -->
<field name="removeTimeValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;removeTimeValue&lt;/code&gt; action.
</description>
<tag name="@see">#removeTimeValue(BString parameter)</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.dailyScheduleChanged -->
<field name="dailyScheduleChanged"  public="true" static="true" final="true">
<type class="javax.baja.sys.Topic"/>
<description>
Slot for the &lt;code&gt;dailyScheduleChanged&lt;/code&gt; topic.
</description>
<tag name="@see">#fireDailyScheduleChanged</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDailySchedule.DAY_SCHEDULE_TAG -->
<field name="DAY_SCHEDULE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
