<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BIScopedFileSpace" name="BIScopedFileSpace" packageName="javax.baja.file" public="true" interface="true" abstract="true" category="interface">
<description>
File space that encompasses only the files within a certain scope.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">2013-12-03</tag>
<tag name="@since">Niagara 4.0</tag>
<implements>
<type class="javax.baja.file.BIFileSpace"/>
</implements>
<!-- javax.baja.file.BIScopedFileSpace.getScope() -->
<method name="getScope"  public="true" abstract="true">
<description>
Scope directory path.
</description>
<return>
<type class="javax.baja.file.FilePath"/>
</return>
</method>

<!-- javax.baja.file.BIScopedFileSpace.inScope(javax.baja.file.FilePath) -->
<method name="inScope"  public="true" abstract="true">
<description>
Is the given FilePath contained within this scoped file system?
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BIScopedFileSpace.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
