<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.network.BBacnetRouterEntry" name="BBacnetRouterEntry" packageName="com.tridium.bacnet.stack.network" public="true">
<description>
BBacnetRouterEntry represents an entry in the&#xa; router table of the Bacnet network layer.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">18 Apr 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="dnet" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;dnet&lt;/code&gt; property.&#xa; the destination network to which this router is connected.
</description>
<tag name="@see">#getDnet</tag>
<tag name="@see">#setDnet</tag>
</property>

<property name="routerAddress" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
Slot for the &lt;code&gt;routerAddress&lt;/code&gt; property.&#xa; the router&#x27;s BacnetAddress.
</description>
<tag name="@see">#getRouterAddress</tag>
<tag name="@see">#setRouterAddress</tag>
</property>

<property name="portId" flags="r">
<type class="int"/>
<description>
Slot for the &lt;code&gt;portId&lt;/code&gt; property.&#xa; the port on which this router is located.
</description>
<tag name="@see">#getPortId</tag>
<tag name="@see">#setPortId</tag>
</property>

<property name="portInfo" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;portInfo&lt;/code&gt; property.&#xa; the port information.
</description>
<tag name="@see">#getPortInfo</tag>
<tag name="@see">#setPortInfo</tag>
</property>

<property name="routerStatus" flags="r">
<type class="com.tridium.bacnet.stack.network.BRouterStatus"/>
<description>
Slot for the &lt;code&gt;routerStatus&lt;/code&gt; property.&#xa; the current health of the router connection.
</description>
<tag name="@see">#getRouterStatus</tag>
<tag name="@see">#setRouterStatus</tag>
</property>

</class>
</bajadoc>
