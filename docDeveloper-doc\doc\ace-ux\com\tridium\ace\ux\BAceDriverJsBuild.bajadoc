<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="ux" qualifiedName="com.tridium.ace.ux.BAceDriverJsBuild" name="BAceDriverJsBuild" packageName="com.tridium.ace.ux" public="true" final="true">
<description>
JavaScript build for Ace ux manager views.
</description>
<tag name="@since">Niagara 4.8</tag>
<extends>
<type class="javax.baja.web.js.BJsBuild"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraSingleton"/>
</annotation>
<!-- com.tridium.ace.ux.BAceDriverJsBuild.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.ace.ux.BAceDriverJsBuild.INSTANCE -->
<field name="INSTANCE"  public="true" static="true" final="true">
<type class="com.tridium.ace.ux.BAceDriverJsBuild"/>
<description/>
</field>

<!-- com.tridium.ace.ux.BAceDriverJsBuild.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
