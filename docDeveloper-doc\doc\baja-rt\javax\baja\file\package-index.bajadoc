<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="baja" runtimeProfile="rt" name="javax.baja.file">
<description>
&lt;p&gt;APIs for access to local and remote file systems.&lt;/p&gt;
</description>
<class packageName="javax.baja.file" name="BAbstractFile"><description>BAbstractFile provides a default base class upon which &#xa; to build BIFile implementations.</description></class>
<class packageName="javax.baja.file" name="BAbstractFileStore"><description>BAbstractFileStore provides a default base class upon &#xa; which to build BIFileStore implementations.</description></class>
<class packageName="javax.baja.file" name="BajaFileUtil"><description>FileUtil provides handy utility methods for&#xa; implementation of BIFile.</description></class>
<class packageName="javax.baja.file" name="BDataFile"><description>BDataFile is a BIFile used when no specified file &#xa; type is known.</description></class>
<class packageName="javax.baja.file" name="BDirectory"><description>BDirectory is the BIFile type used to represent directories &#xa; in file space implementations.</description></class>
<class packageName="javax.baja.file" name="BExporter"><description>BExporter is a component designed to export a BObject to&#xa; a file stream.</description></class>
<class packageName="javax.baja.file" name="BFileScheme"><description>BFileScheme manages the &#x22;file&#x22; scheme with a FilePath query.</description></class>
<class packageName="javax.baja.file" name="BFileSpace"><description>BFileSpace defines a tree of BIFiles using a specific&#xa; implementation of BIFilesStore.</description></class>
<class packageName="javax.baja.file" name="BFileSystem"><description>BFileSystem is a BFileSpace for the local machine&#x27;s&#xa; file system.</description></class>
<class packageName="javax.baja.file" name="BIDeployable.Step"/>
<class packageName="javax.baja.file" name="BLocalFileStore"><description>BLocalStore is a BIFileStore implementation for&#xa; local files using java.io.File.</description></class>
<class packageName="javax.baja.file" name="BLocalizedFileSpace"><description>Abstract for a file space that can translate paths to a localized file system.</description></class>
<class packageName="javax.baja.file" name="BMemoryFileStore"><description>BMemoryStore is a BIFileStore implementation which uses&#xa; a memory buffer for reading and writing.</description></class>
<class packageName="javax.baja.file" name="BScopedFileSpace"><description>BScopedFileSpace is a file space for the local machine&#x27;s&#xa; file system which is scoped down to a particular directory.</description></class>
<class packageName="javax.baja.file" name="BSubSpaceFile"><description>BSubSpaceFile is a data file that contains a sub-space with&#xa; its own object hierarchy and navigation scheme.</description></class>
<class packageName="javax.baja.file" name="ExportOp"><description>ExportOp encapsulates all the information needed to process&#xa; a source BObject accessed via &lt;code&gt;get()&lt;/code&gt; into a file&#xa; using &lt;code&gt;getOutputStream()&lt;/code&gt;.</description></class>
<class packageName="javax.baja.file" name="FilePath"><description>FilePath is a specialization of OrdScheme for file queries.</description></class>
<class packageName="javax.baja.file" name="BajaFileUtil.BajaFileWriter" category="interface"><description>Public interface for file system operations.</description></class>
<class packageName="javax.baja.file" name="BIComponentFile" category="interface"><description>BIComponentFile should be implemented by file type objects that&#xa; may be move/copied to a BComponent space but first require conversion&#xa; to a BComponent.</description></class>
<class packageName="javax.baja.file" name="BIDataFile" category="interface"><description>BIDataFile is a BIFile that contains data.</description></class>
<class packageName="javax.baja.file" name="BIDeployable" category="interface"><description>BIDeployable should be implemented by objects that can be deployed&#xa; to a BSpace in multiple copy operations.</description></class>
<class packageName="javax.baja.file" name="BIDirectory" category="interface"><description>BIDirectory is a container of BIFiles</description></class>
<class packageName="javax.baja.file" name="BIFile" category="interface"><description>&lt;p&gt;&#xa; BIFile is the interface implemented by BObjects which&#xa; have file like semantics.</description></class>
<class packageName="javax.baja.file" name="BIFileSpace" category="interface"><description>File space interface.</description></class>
<class packageName="javax.baja.file" name="BIFileStore" category="interface"><description>BIFileStore is a pluggable implementation of file storage.</description></class>
<class packageName="javax.baja.file" name="BIScopedFileSpace" category="interface"><description>File space that encompasses only the files within a certain scope.</description></class>
<class packageName="javax.baja.file" name="BITemplate" category="interface"><description>BITemplate is implemented by template files.</description></class>
<class packageName="javax.baja.file" name="IExtFileFilter" category="interface"><description>An interface for file filters that support filtering based on one or more file extensions.</description></class>
<class packageName="javax.baja.file" name="IFileFilter" category="interface"><description>IFileFilter.</description></class>
</package>
</bajadoc>
