<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetOws" runtimeProfile="wb" qualifiedName="com.tridium.bacnetOws.ui.device.BBacnetOwsDeviceManager" name="BBacnetOwsDeviceManager" packageName="com.tridium.bacnetOws.ui.device" public="true">
<description>
BBacnetOwsDeviceManager is the specialization of BBacnetOwsDeviceManager to&#xa; handle Workstation-specific behavior.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">31 Jan 2005</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.ui.device.BBacnetDeviceManager"/>
</extends>
</class>
</bajadoc>
