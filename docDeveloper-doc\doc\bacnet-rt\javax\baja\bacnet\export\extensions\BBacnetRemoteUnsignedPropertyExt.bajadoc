<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.extensions.BBacnetRemoteUnsignedPropertyExt" name="BBacnetRemoteUnsignedPropertyExt" packageName="javax.baja.bacnet.export.extensions" public="true">
<description>
Point extension for BACnet Remote Unsigned Properties.
</description>
<tag name="@author"><PERSON><PERSON><PERSON> on 19/02/2019</tag>
<extends>
<type class="javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt"/>
</extends>
<implements>
<type class="javax.baja.bacnet.util.BIBacnetPollable"/>
</implements>
<property name="deviceId" flags="r">
<type class="int"/>
<description>
Slot for the &lt;code&gt;deviceId&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceId</tag>
<tag name="@see">#setDeviceId</tag>
</property>

<!-- javax.baja.bacnet.export.extensions.BBacnetRemoteUnsignedPropertyExt() -->
<constructor name="BBacnetRemoteUnsignedPropertyExt" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.extensions.BBacnetRemoteUnsignedPropertyExt(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int) -->
<constructor name="BBacnetRemoteUnsignedPropertyExt" public="true">
<parameter name="oid">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="propertyId">
<type class="int"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.export.extensions.BBacnetRemoteUnsignedPropertyExt.getDeviceId() -->
<method name="getDeviceId"  public="true">
<description>
Get the &lt;code&gt;deviceId&lt;/code&gt; property.
</description>
<tag name="@see">#deviceId</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetRemoteUnsignedPropertyExt.setDeviceId(int) -->
<method name="setDeviceId"  public="true">
<description>
Set the &lt;code&gt;deviceId&lt;/code&gt; property.
</description>
<tag name="@see">#deviceId</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetRemoteUnsignedPropertyExt.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetRemoteUnsignedPropertyExt.started() -->
<method name="started"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetRemoteUnsignedPropertyExt.stopped() -->
<method name="stopped"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetRemoteUnsignedPropertyExt.device() -->
<method name="device"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetRemoteUnsignedPropertyExt.getPollableType() -->
<method name="getPollableType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetRemoteUnsignedPropertyExt.poll() -->
<method name="poll"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetRemoteUnsignedPropertyExt.readFail(java.lang.String) -->
<method name="readFail"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="failureMsg">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetRemoteUnsignedPropertyExt.fromEncodedValue(byte[], javax.baja.status.BStatus, javax.baja.sys.Context) -->
<method name="fromEncodedValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="encodedValue">
<type class="byte" dimension="1"/>
</parameter>
<parameter name="status">
<type class="javax.baja.status.BStatus"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetRemoteUnsignedPropertyExt.getPollListEntries() -->
<method name="getPollListEntries"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.bacnet.util.PollListEntry" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetRemoteUnsignedPropertyExt.getPollFrequency() -->
<method name="getPollFrequency"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.driver.util.BPollFrequency"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetRemoteUnsignedPropertyExt.deviceId -->
<field name="deviceId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;deviceId&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceId</tag>
<tag name="@see">#setDeviceId</tag>
</field>

<!-- javax.baja.bacnet.export.extensions.BBacnetRemoteUnsignedPropertyExt.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
