<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.dataRecovery.DataRecoveryTooLargeException" name="DataRecoveryTooLargeException" packageName="javax.baja.dataRecovery" public="true" category="exception">
<description>
DataRecoveryException thrown when data recovery Service is asked to store&#xa; an amount of data for which it would never have enough space for.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">7 July 09</tag>
<tag name="@version">Original</tag>
<tag name="@since">Niagara 3.6</tag>
<extends>
<type class="javax.baja.dataRecovery.DataRecoveryException"/>
</extends>
<!-- javax.baja.dataRecovery.DataRecoveryTooLargeException(int, int, java.lang.String) -->
<constructor name="DataRecoveryTooLargeException" public="true">
<parameter name="attempted">
<type class="int"/>
</parameter>
<parameter name="max">
<type class="int"/>
</parameter>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.dataRecovery.DataRecoveryTooLargeException.attemptedSize -->
<field name="attemptedSize"  public="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.dataRecovery.DataRecoveryTooLargeException.maxSize -->
<field name="maxSize"  public="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
