<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnetAws" runtimeProfile="rt" name="javax.baja.bacnetAws.enums">
<description/>
<class packageName="javax.baja.bacnetAws.enums" name="BAccumulatorStatus"><description>- Insert description here.</description></class>
<class packageName="javax.baja.bacnetAws.enums" name="BBacnetDoorValue"/>
<class packageName="javax.baja.bacnetAws.enums" name="BBacnetShedState"><description>Represents the BBacnetShedState define in the Bacnet spec.</description></class>
</package>
</bajadoc>
