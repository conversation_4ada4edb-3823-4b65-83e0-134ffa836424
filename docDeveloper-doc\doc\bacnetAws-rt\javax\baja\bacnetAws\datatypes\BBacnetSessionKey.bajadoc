<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="javax.baja.bacnetAws.datatypes.BBacnetSessionKey" name="BBacnetSessionKey" packageName="javax.baja.bacnetAws.datatypes" public="true" final="true">
<description>
BBacnetSessionKey represents the BacnetSessionKey data type.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">May 25, 2010</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.6 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="sessionKey" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetOctetString"/>
<description>
Slot for the &lt;code&gt;sessionKey&lt;/code&gt; property.
</description>
<tag name="@see">#getSessionKey</tag>
<tag name="@see">#setSessionKey</tag>
</property>

<property name="peerAddress" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
Slot for the &lt;code&gt;peerAddress&lt;/code&gt; property.
</description>
<tag name="@see">#getPeerAddress</tag>
<tag name="@see">#setPeerAddress</tag>
</property>

<!-- javax.baja.bacnetAws.datatypes.BBacnetSessionKey() -->
<constructor name="BBacnetSessionKey" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnetAws.datatypes.BBacnetSessionKey.getSessionKey() -->
<method name="getSessionKey"  public="true">
<description>
Get the &lt;code&gt;sessionKey&lt;/code&gt; property.
</description>
<tag name="@see">#sessionKey</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetOctetString"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetSessionKey.setSessionKey(javax.baja.bacnet.datatypes.BBacnetOctetString) -->
<method name="setSessionKey"  public="true">
<description>
Set the &lt;code&gt;sessionKey&lt;/code&gt; property.
</description>
<tag name="@see">#sessionKey</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetOctetString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetSessionKey.getPeerAddress() -->
<method name="getPeerAddress"  public="true">
<description>
Get the &lt;code&gt;peerAddress&lt;/code&gt; property.
</description>
<tag name="@see">#peerAddress</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetSessionKey.setPeerAddress(javax.baja.bacnet.datatypes.BBacnetAddress) -->
<method name="setPeerAddress"  public="true">
<description>
Set the &lt;code&gt;peerAddress&lt;/code&gt; property.
</description>
<tag name="@see">#peerAddress</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetSessionKey.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetSessionKey.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetSessionKey.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetSessionKey.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetSessionKey.sessionKey -->
<field name="sessionKey"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;sessionKey&lt;/code&gt; property.
</description>
<tag name="@see">#getSessionKey</tag>
<tag name="@see">#setSessionKey</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetSessionKey.peerAddress -->
<field name="peerAddress"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;peerAddress&lt;/code&gt; property.
</description>
<tag name="@see">#getPeerAddress</tag>
<tag name="@see">#setPeerAddress</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetSessionKey.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
