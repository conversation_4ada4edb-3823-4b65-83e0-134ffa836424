<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.BacnetAlarmConst" name="BacnetAlarmConst" packageName="javax.baja.bacnet" public="true" interface="true" abstract="true" category="interface">
<description>
Standard keys for the AlarmData portion of a&#xa; BAlarmRecord referencing a Bacnet alarm.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 2$ $Date: 12/19/01 4:35:44 PM$</tag>
<tag name="@creation">07 Nov 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_PROCESS_ID -->
<field name="BAC_PROCESS_ID"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_DEVICE_ID -->
<field name="BAC_DEVICE_ID"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_OBJECT_ID -->
<field name="BAC_OBJECT_ID"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_TIMESTAMP -->
<field name="BAC_TIMESTAMP"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_NOTIFICATION_CLASS -->
<field name="BAC_NOTIFICATION_CLASS"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_EVENT_TYPE -->
<field name="BAC_EVENT_TYPE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_EVENT_VALUES -->
<field name="BAC_EVENT_VALUES"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_ACK_TIME -->
<field name="BAC_ACK_TIME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_PRIORITY -->
<field name="BAC_PRIORITY"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_ACK_REQUIRED -->
<field name="BAC_ACK_REQUIRED"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_NOTIFY_TYPE -->
<field name="BAC_NOTIFY_TYPE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_STALE_ACK -->
<field name="BAC_STALE_ACK"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_REFERENCED_BITSTRING -->
<field name="BAC_REFERENCED_BITSTRING"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_STATUS_FLAGS -->
<field name="BAC_STATUS_FLAGS"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_NEW_STATE -->
<field name="BAC_NEW_STATE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_NEW_VALUE -->
<field name="BAC_NEW_VALUE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_CHANGED_VALUE -->
<field name="BAC_CHANGED_VALUE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_ALARM_VALUE -->
<field name="BAC_ALARM_VALUE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_CHANGED_BITS -->
<field name="BAC_CHANGED_BITS"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_COMMAND_VALUE -->
<field name="BAC_COMMAND_VALUE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_FEEDBACK_VALUE -->
<field name="BAC_FEEDBACK_VALUE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_REFERENCE_VALUE -->
<field name="BAC_REFERENCE_VALUE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_SETPOINT_VALUE -->
<field name="BAC_SETPOINT_VALUE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_ERROR_LIMIT -->
<field name="BAC_ERROR_LIMIT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_EXCEEDING_VALUE -->
<field name="BAC_EXCEEDING_VALUE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_DEADBAND -->
<field name="BAC_DEADBAND"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_EXCEEDED_LIMIT -->
<field name="BAC_EXCEEDED_LIMIT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_COMPLEX_EVENT_VALUE -->
<field name="BAC_COMPLEX_EVENT_VALUE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_BUFFER_DEVICE -->
<field name="BAC_BUFFER_DEVICE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_BUFFER_OBJECT -->
<field name="BAC_BUFFER_OBJECT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_PREVIOUS_NOTIFICATION -->
<field name="BAC_PREVIOUS_NOTIFICATION"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_CURRENT_NOTIFICATION -->
<field name="BAC_CURRENT_NOTIFICATION"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_NEW_MODE -->
<field name="BAC_NEW_MODE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_OPERATION_EXPECTED -->
<field name="BAC_OPERATION_EXPECTED"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_BUFFER_PROPERTY -->
<field name="BAC_BUFFER_PROPERTY"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_TIME_DELAY -->
<field name="BAC_TIME_DELAY"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_LOW_LIMIT -->
<field name="BAC_LOW_LIMIT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_HIGH_LIMIT -->
<field name="BAC_HIGH_LIMIT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_LIFE_SAFETY_ALARM_VALUES -->
<field name="BAC_LIFE_SAFETY_ALARM_VALUES"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_ALARM_VALUES -->
<field name="BAC_ALARM_VALUES"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_VENDOR_ID -->
<field name="BAC_VENDOR_ID"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_EXTENDED_EVENT_TYPE -->
<field name="BAC_EXTENDED_EVENT_TYPE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_PARAMETERS -->
<field name="BAC_PARAMETERS"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_ACCESS_EVENT -->
<field name="BAC_ACCESS_EVENT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_ACCESS_EVENT_TAG -->
<field name="BAC_ACCESS_EVENT_TAG"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_ACCESS_EVENT_TIME -->
<field name="BAC_ACCESS_EVENT_TIME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_ACCESS_CREDENTIAL -->
<field name="BAC_ACCESS_CREDENTIAL"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_AUTH_FACTOR -->
<field name="BAC_AUTH_FACTOR"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_PRESENT_VALUE -->
<field name="BAC_PRESENT_VALUE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_RELIABILITY -->
<field name="BAC_RELIABILITY"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_PROPERTY_VALUES -->
<field name="BAC_PROPERTY_VALUES"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_TO_OFFNORMAL_MSG_TEXT -->
<field name="BAC_TO_OFFNORMAL_MSG_TEXT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
User defined message text key
</description>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_TO_FAULT_MSG_TEXT -->
<field name="BAC_TO_FAULT_MSG_TEXT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_TO_NORMAL_MSG_TEXT -->
<field name="BAC_TO_NORMAL_MSG_TEXT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_NORMAL_PRIORITY -->
<field name="BAC_NORMAL_PRIORITY"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_FAULT_PRIORITY -->
<field name="BAC_FAULT_PRIORITY"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_OFFNORMAL_PRIORITY -->
<field name="BAC_OFFNORMAL_PRIORITY"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_OFFNORMAL_TO_STATE -->
<field name="BAC_OFFNORMAL_TO_STATE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_OFFNORMAL_ACKED -->
<field name="BAC_OFFNORMAL_ACKED"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_STATE_ACKED -->
<field name="BAC_STATE_ACKED"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_DEST_PROC_ID_PREFIX -->
<field name="BAC_DEST_PROC_ID_PREFIX"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_CONFIRMED_FLAG -->
<field name="BAC_CONFIRMED_FLAG"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_NOTIFY_LIST -->
<field name="BAC_NOTIFY_LIST"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_ESUM_EVENT_STATE -->
<field name="BAC_ESUM_EVENT_STATE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_ESUM_ACKED_TRANSITIONS -->
<field name="BAC_ESUM_ACKED_TRANSITIONS"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_ESUM_EVENT_TIMESTAMPS -->
<field name="BAC_ESUM_EVENT_TIMESTAMPS"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_ESUM_EVENT_ENABLE -->
<field name="BAC_ESUM_EVENT_ENABLE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.BAC_ESUM_EVENT_PRIORITIES -->
<field name="BAC_ESUM_EVENT_PRIORITIES"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.TO_OFFNORMAL_BIT -->
<field name="TO_OFFNORMAL_BIT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.TO_FAULT_BIT -->
<field name="TO_FAULT_BIT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.TO_NORMAL_BIT -->
<field name="TO_NORMAL_BIT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.TO_OFFNORMAL_INDEX -->
<field name="TO_OFFNORMAL_INDEX"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.TO_FAULT_INDEX -->
<field name="TO_FAULT_INDEX"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetAlarmConst.TO_NORMAL_INDEX -->
<field name="TO_NORMAL_INDEX"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
