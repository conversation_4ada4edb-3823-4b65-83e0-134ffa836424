<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.dataRecovery.BIDataRecoveryService" name="BIDataRecoveryService" packageName="javax.baja.dataRecovery" public="true" interface="true" abstract="true" category="interface">
<description>
BIDataRecoveryService defines the behavior that all data recovery&#xa; services, that is providers of data recovery storage, should demonstrate.&#xa; Data can be recorded and restored from the data recovery storage managed&#xa; by the BIDataRecoveryService.  The BIDataRecoveryService should&#xa; perform all of its initialization in the serviceStarted() method,&#xa; since BIDataRecoveryService&#x27;s are guaranteed to start first on station&#xa; startup, and end last on station shutdown.  Data in the data recovery&#xa; storage is identifiable by a BIDataRecoverySource and BIEncodable key pair.&#xa;&#xa; data recovery content is anything that is stored persistently by the station&#xa; during a station save.  So data recovery records during the normal operation&#xa; of a station, and purges when the station is successfully saved.  If a station&#xa; does not save successfully on station shutdown (ie. due to a power loss), then&#xa; any data in the data recovery storage will be restored on the next station&#xa; startup in order to preserve the last known station state.
</description>
<tag name="@author">Mike James</tag>
<tag name="@creation">Jul 14, 2009</tag>
<tag name="@version">Original</tag>
<tag name="@since">Niagara 3.6</tag>
<implements>
<type class="javax.baja.sys.BIService"/>
</implements>
<!-- javax.baja.dataRecovery.BIDataRecoveryService.isEnabled() -->
<method name="isEnabled"  public="true" abstract="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.dataRecovery.BIDataRecoveryService.append(javax.baja.dataRecovery.BIDataRecoverySource, javax.baja.io.BIEncodable, byte[], int, int) -->
<method name="append"  public="true" abstract="true">
<description>
Append data to the data recovery storage, identifiable by the given&#xa; source and key.  If data recovery already exists for&#xa; the given source and key pair, then this method behaves like&#xa; an update() call.  However, if you know that it is a new source/key&#xa; pair, this method is quicker than calling update().
</description>
<parameter name="source">
<type class="javax.baja.dataRecovery.BIDataRecoverySource"/>
</parameter>
<parameter name="key">
<type class="javax.baja.io.BIEncodable"/>
</parameter>
<parameter name="data">
<type class="byte" dimension="1"/>
</parameter>
<parameter name="offset">
<type class="int"/>
</parameter>
<parameter name="length">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
</return>
<throws>
<type class="javax.baja.sys.BajaRuntimeException"/>
</throws>
</method>

<!-- javax.baja.dataRecovery.BIDataRecoveryService.append(javax.baja.dataRecovery.BIDataRecoverySource, javax.baja.io.BIEncodable, byte[]) -->
<method name="append"  public="true" abstract="true">
<description>
Convenience for append(source, key, data, 0, data.length)
</description>
<parameter name="source">
<type class="javax.baja.dataRecovery.BIDataRecoverySource"/>
</parameter>
<parameter name="key">
<type class="javax.baja.io.BIEncodable"/>
</parameter>
<parameter name="data">
<type class="byte" dimension="1"/>
</parameter>
<return>
<type class="boolean"/>
</return>
<throws>
<type class="javax.baja.sys.BajaRuntimeException"/>
</throws>
</method>

<!-- javax.baja.dataRecovery.BIDataRecoveryService.hasRecoveryData() -->
<method name="hasRecoveryData"  public="true" abstract="true">
<description>
If there is any data recovery in the data recovery storage&#xa; available to restore(), return true.  Otherwise, return false&#xa; if it is empty.
</description>
<return>
<type class="boolean"/>
</return>
<throws>
<type class="javax.baja.sys.BajaRuntimeException"/>
</throws>
</method>

<!-- javax.baja.dataRecovery.BIDataRecoveryService.saveStarted() -->
<method name="saveStarted"  public="true" abstract="true">
<description>
This callback is given to the BIDataRecoveryService by the framework&#xa; when a station save is about to begin.  It is always called before any&#xa; station save activities have begun.  This gives the BIDataRecoveryService&#xa; an opportunity to mark the data recovery as ready to purge - data that can&#xa; be safely purged on a future call to saveFinished().
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.sys.BajaRuntimeException"/>
</throws>
</method>

<!-- javax.baja.dataRecovery.BIDataRecoveryService.saveFinished() -->
<method name="saveFinished"  public="true" abstract="true">
<description>
This callback is given to the BIDataRecoveryService by the framework&#xa; when a station save has successfully completed, thus indicating that it&#xa; is safe to purge applicable data recovery.  It is always preceded&#xa; by a saveStarted() call, so data recovery should have already been marked as&#xa; ready to purge.  This call tells the BIDataRecoveryService that it is safe&#xa; to purge that data.  Remember that events can be recorded concurrently during&#xa; the station save process, so only those records that were in the dataRecovery&#xa; storage at the time that saveStarted() was called should be eligible for&#xa; purging when this saveFinished() call is received.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.sys.BajaRuntimeException"/>
</throws>
</method>

<!-- javax.baja.dataRecovery.BIDataRecoveryService.saveFailed(java.lang.Throwable) -->
<method name="saveFailed"  public="true" abstract="true">
<description>
This callback is given to the BIDataRecoveryService by the framework&#xa; when a station save attempt failed (for the reason passed in as the cause).&#xa; This call is always preceded by a saveStarted() call, so data recovery should&#xa; have already been marked as ready to purge.  This call tells the&#xa; BIDataRecoveryService that it is not safe to purge that data, since the failure&#xa; means that some persistent station data was not able to be successfully saved.&#xa; So usually this callback indicates that the data marked as ready to purge should&#xa; be unmarked.
</description>
<parameter name="cause">
<type class="java.lang.Throwable"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.dataRecovery.BIDataRecoveryService.shutdownStarted() -->
<method name="shutdownStarted"  public="true" abstract="true">
<description>
This callback is given to the BIDataRecoveryService by the framework&#xa; when a station is gracefully shutting down.  It will be called immediately after&#xa; a synchronous station save has occurred.  This gives the BIDataRecoveryService&#xa; an opportunity to clear out any remaining data recovery (since a graceful station&#xa; shutdown save has already occurred), and also indicates that no new data recovery&#xa; events should be recorded (new events may still be passed to the BIDataRecoveryService&#xa; after this call, but those events should be treated as no ops).  On normal station &#xa; shutdown, this call will be followed later by a serviceStopped() callback indicating&#xa; it is safe to stop the BIDataRecoveryService altogether.  However, it is not safe&#xa; to stop the BIDataRecoveryService altogether on this shutdownStarted() callback,&#xa; it is simply used to indicate that the station shutdown process has begun.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.sys.BajaRuntimeException"/>
</throws>
</method>

<!-- javax.baja.dataRecovery.BIDataRecoveryService.checkForStationFault() -->
<method name="checkForStationFault"  public="true" abstract="true">
<description>
Determine if the service is currently in a fault condition that jeopardizes the station.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.dataRecovery.BIDataRecoveryService.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.dataRecovery.BIDataRecoveryService.DATA_RECOVERY_LOG_PREFIX -->
<field name="DATA_RECOVERY_LOG_PREFIX"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Create a common logger prefix to accurately identify data recovery logs
</description>
</field>

</class>
</bajadoc>
