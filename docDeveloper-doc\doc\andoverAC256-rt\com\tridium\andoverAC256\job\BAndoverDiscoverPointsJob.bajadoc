<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.job.BAndoverDiscoverPointsJob" name="BAndoverDiscoverPointsJob" packageName="com.tridium.andoverAC256.job" public="true">
<description>
Andover AC256 Point Discovery.&#xa; This job class handles the implementation of the scan for&#xa; points in an AC256/AC8 panel or sub-panel&#xa; device.&lt;p&gt;&#xa; *
</description>
<tag name="@author">C<PERSON><PERSON></tag>
<tag name="@creation">4/27/2005 10:41AM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.76</tag>
<extends>
<type class="javax.baja.job.BJob"/>
</extends>
<implements>
<type class="com.tridium.andoverAC256.messages.AndoverMessageConst"/>
</implements>
<implements>
<type class="java.lang.Runnable"/>
</implements>
</class>
</bajadoc>
