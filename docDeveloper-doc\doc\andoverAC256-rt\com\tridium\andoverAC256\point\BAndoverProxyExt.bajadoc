<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.point.BAndoverProxyExt" name="BAndoverProxyExt" packageName="com.tridium.andoverAC256.point" public="true">
<description>
AndoverAC256 implementation of BProxyExt
</description>
<tag name="@author">Autogenerated by R3DriverConv</tag>
<tag name="@creation">28 Sep 04</tag>
<tag name="@version">$Revision: 1$ $Date: 9/28/2004 11:35:43 AM$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="com.tridium.basicdriver.point.BBasicProxyExt"/>
</extends>
<implements>
<type class="com.tridium.basicdriver.util.BIBasicPollable"/>
</implements>
<implements>
<type class="com.tridium.andoverAC256.messages.AndoverMessageConst"/>
</implements>
<property name="assignedName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;assignedName&lt;/code&gt; property.&#xa; This is the name assigned to the point in the&#xa; andover panel configuration, or may be a simple&#xa; reference to an unassigned table value
</description>
<tag name="@see">#getAssignedName</tag>
<tag name="@see">#setAssignedName</tag>
</property>

<property name="rawValue" flags="rht">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;rawValue&lt;/code&gt; property.&#xa; The value portion of the string returned from the&#xa; via the Print command.  This is the part of the&#xa; response to the right of the &#x22;=&#x22; sign, but before&#xa; the cr/lf and prompt characters.
</description>
<tag name="@see">#getRawValue</tag>
<tag name="@see">#setRawValue</tag>
</property>

<property name="pointOrigin" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;pointOrigin&lt;/code&gt; property.&#xa; If learned instead of manually configured, this property&#xa; will contain the name of the AC256 table or the LCU that&#xa; originated the point.  If not learned, will contain the&#xa; text &#x22;unknown&#x22;
</description>
<tag name="@see">#getPointOrigin</tag>
<tag name="@see">#setPointOrigin</tag>
</property>

<property name="pointNotes" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;pointNotes&lt;/code&gt; property.&#xa; If learned instead of manually configured, this property&#xa; may contain extra information such as the native I/O type&#xa; in an LCU (thermistor input, voltage input, etc) or the&#xa; point terminals.  If not learned, will contain the&#xa; text &#x22;&#x22;, but can edited to add user defined info.
</description>
<tag name="@see">#getPointNotes</tag>
<tag name="@see">#setPointNotes</tag>
</property>

<property name="sendDisableCommand" flags="">
<type class="com.tridium.andoverAC256.enums.BAndoverSendDisableEnum"/>
<description>
Slot for the &lt;code&gt;sendDisableCommand&lt;/code&gt; property.&#xa; Used to configure when to send the DISABLE command to this point&#xa; in the andover controller
</description>
<tag name="@see">#getSendDisableCommand</tag>
<tag name="@see">#setSendDisableCommand</tag>
</property>

<property name="readEnabledState" flags="">
<type class="com.tridium.andoverAC256.enums.BAndoverReadEnabledStateEnum"/>
<description>
Slot for the &lt;code&gt;readEnabledState&lt;/code&gt; property.&#xa; Used to configure when to read the ENABLE or DISABLE state of&#xa; this point in the Andover controller.  Note that if set&#xa; to &#x22;onlyOnFirstRead&#x22;, then point state is read under the following&#xa; conditions:  startup, point enters subscribed state, or assignedName&#xa; changes.
</description>
<tag name="@see">#getReadEnabledState</tag>
<tag name="@see">#setReadEnabledState</tag>
</property>

<property name="nativeEnableState" flags="rt">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;nativeEnableState&lt;/code&gt; property.&#xa; actual enabled or disabled state of point in the Andover&#xa; controller.  Necessary to know because a writable point must be&#xa; in a &#x22;disabled&#x22; state internal to the Andover panel&#xa; before it can be written to.  (&#x22;Disabled&#x22; in Andover-ese&#xa; means the point is not controlled by a drum program, but&#xa; is instead able to be affected/overwritten by a terminal&#xa; connected to the C-Port or S-Port.  &#x22;Enabled&#x22; means the&#xa; point is under control of a drum program)
</description>
<tag name="@see">#getNativeEnableState</tag>
<tag name="@see">#setNativeEnableState</tag>
</property>

<property name="pollFrequency" flags="">
<type class="javax.baja.driver.util.BPollFrequency"/>
<description>
Slot for the &lt;code&gt;pollFrequency&lt;/code&gt; property.&#xa; Poll frequency bucket
</description>
<tag name="@see">#getPollFrequency</tag>
<tag name="@see">#setPollFrequency</tag>
</property>

</class>
</bajadoc>
