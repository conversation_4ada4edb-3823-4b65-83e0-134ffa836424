<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.BAlarmFilterSet" name="BAlarmFilterSet" packageName="com.tridium.alarm" public="true">
<description>
BAlarmFilterSet is a set of BIFilters customized to incorporate &#xa; the specific alarm filter requirements.&#xa; &lt;p&gt;&#xa; From Niagara 4.6 onwards, this class was moved from the&#xa; alarm-wb &#x27;com.tridium.alarm.ui&#x27; package to here so it can&#xa; be reused by the UX alarm console.&#xa; &lt;/p&gt;
</description>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@creation">30 Sep 2015</tag>
<tag name="@since">Niagara 4.1</tag>
<extends>
<type class="com.tridium.bql.filter.BFilterSet"/>
</extends>
</class>
</bajadoc>
