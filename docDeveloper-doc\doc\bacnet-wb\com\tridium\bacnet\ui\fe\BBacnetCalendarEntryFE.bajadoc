<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="wb" qualifiedName="com.tridium.bacnet.ui.fe.BBacnetCalendarEntryFE" name="BBacnetCalendarEntryFE" packageName="com.tridium.bacnet.ui.fe" public="true">
<description>
BBacnetCalendarEntryFE allows viewing and editing of a BBacnetCalendarEntry.
</description>
<tag name="@author"><PERSON> on 09 Sep 03</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.workbench.fieldeditor.BWbFieldEditor"/>
</extends>
<implements>
<type class="javax.baja.workbench.fieldeditor.BWbFieldEditor$IDialogContentProvider"/>
</implements>
<action name="loadValueEditor" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;loadValueEditor&lt;/code&gt; action.
</description>
<tag name="@see">#loadValueEditor()</tag>
</action>

</class>
</bajadoc>
