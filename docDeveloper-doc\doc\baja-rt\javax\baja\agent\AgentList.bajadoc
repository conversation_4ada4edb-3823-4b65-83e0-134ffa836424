<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.agent.AgentList" name="AgentList" packageName="javax.baja.agent" public="true" interface="true" abstract="true" category="interface">
<description>
AgentList encapsulates an ordered list of agent AgentInfos.
</description>
<tag name="@author"><PERSON> on 23 Dec 02</tag>
<tag name="@version">$Revision: 8$ $Date: 3/28/05 9:22:53 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<implements>
<parameterizedType class="java.lang.Iterable">
<args>
<type class="javax.baja.agent.AgentInfo"/>
</args>
</parameterizedType>
</implements>
<!-- javax.baja.agent.AgentList.size() -->
<method name="size"  public="true" abstract="true">
<description>
Get the list size.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.getDefault() -->
<method name="getDefault"  public="true" abstract="true">
<description>
Get the default agent which is the agent at index zero.  If &#xa; the agent list is a size of zero, then throw NoSuchAgentException.
</description>
<return>
<type class="javax.baja.agent.AgentInfo"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.get(int) -->
<method name="get"  public="true" abstract="true">
<description>
Get the agent info at the specified index.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.agent.AgentInfo"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.get(java.lang.String) -->
<method name="get"  public="true" abstract="true">
<description>
Get the agent info by the specified id or &#xa; return null if no agent in list by given id.
</description>
<parameter name="id">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.agent.AgentInfo"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.list() -->
<method name="list"  public="true" abstract="true">
<description>
Get the list of all agents.
</description>
<return>
<type class="javax.baja.agent.AgentInfo" dimension="1"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.indexOf(java.lang.String) -->
<method name="indexOf"  public="true" abstract="true">
<description>
Get the index of the specified agent id or &#xa; return -1 if no matching id in list.
</description>
<parameter name="id">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.indexOf(javax.baja.agent.AgentInfo) -->
<method name="indexOf"  public="true" abstract="true">
<description>
Get the index of the specified AgentInfo or &#xa; return -1 if the AgentInfo is not in the list.
</description>
<parameter name="info">
<type class="javax.baja.agent.AgentInfo"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.clone() -->
<method name="clone"  public="true" abstract="true">
<description>
Make a copy of this AgentList.
</description>
<return>
<type class="java.lang.Object"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.filter(javax.baja.agent.AgentFilter) -->
<method name="filter"  public="true" abstract="true">
<description>
Run the specified filter against this agent list to&#xa; produce a new agent list which only includes agent types &#xa; which return true for &lt;code&gt;filter.include()&lt;/code&gt;.
</description>
<parameter name="filter">
<type class="javax.baja.agent.AgentFilter"/>
</parameter>
<return>
<type class="javax.baja.agent.AgentList"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.filter(java.util.function.Predicate&lt;javax.baja.agent.AgentInfo&gt;) -->
<method name="filter"  public="true" abstract="true">
<description>
Run the specified predicate against this agent list to&#xa; produce a new agent list which only includes agent types.
</description>
<tag name="@since">Niagara 4.0</tag>
<parameter name="predicate">
<parameterizedType class="java.util.function.Predicate">
<args>
<type class="javax.baja.agent.AgentInfo"/>
</args>
</parameterizedType>
<description>
The predicate used to filter the agent list.
</description>
</parameter>
<return>
<type class="javax.baja.agent.AgentList"/>
<description>
The filtered agent list.
</description>
</return>
</method>

<!-- javax.baja.agent.AgentList.add(java.lang.String) -->
<method name="add"  public="true" abstract="true">
<description>
Convenience for &lt;code&gt;add(registry.getType(typeSpec).getAgentInfo())&lt;/code&gt;.&#xa; If the typespec is not found, then this method is silently ignored.
</description>
<parameter name="typeSpec">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.add(javax.baja.agent.AgentInfo) -->
<method name="add"  public="true" abstract="true">
<description>
Add the specified agent to the top of list.  If the&#xa; AgentInfo is already in the list then it is removed first.
</description>
<parameter name="info">
<type class="javax.baja.agent.AgentInfo"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.add(int, java.lang.String) -->
<method name="add"  public="true" abstract="true">
<description>
Convenience for &lt;code&gt;add(index, registry.getType(typeSpec).getAgentInfo())&lt;/code&gt;.&#xa; If the typespec is not found, then this method is silently ignored.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<parameter name="typeSpec">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.add(int, javax.baja.agent.AgentInfo) -->
<method name="add"  public="true" abstract="true">
<description>
Add the specified agent to the list at the given index.  &#xa; If the AgentInfo is already in the list then it is &#xa; removed first.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<parameter name="info">
<type class="javax.baja.agent.AgentInfo"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.remove(java.lang.String) -->
<method name="remove"  public="true" abstract="true">
<description>
Remove an agent by its id string.  If the id &#xa; is not in the list then this call is ignored.
</description>
<parameter name="id">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.remove(javax.baja.agent.AgentInfo) -->
<method name="remove"  public="true" abstract="true">
<description>
Remove the specified agent AgentInfo instance.  If &#xa; the agent is not in the list this call is ignored.
</description>
<parameter name="info">
<type class="javax.baja.agent.AgentInfo"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.remove(int) -->
<method name="remove"  public="true" abstract="true">
<description>
Remove an AgentInfo by its index.  If the index &#xa; is out of range then this call is ignored.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.remove(javax.baja.agent.AgentList) -->
<method name="remove"  public="true" abstract="true">
<description>
Remove all the agents in the specified list&#xa; which are found in this list.
</description>
<parameter name="list">
<type class="javax.baja.agent.AgentList"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.toTop(java.lang.String) -->
<method name="toTop"  public="true" abstract="true">
<description>
Move the specified agent to the top of the list.&#xa; If the list doesn&#x27;t contain the specified id, then&#xa; this call is ignored.
</description>
<parameter name="id">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.toTop(javax.baja.agent.AgentInfo) -->
<method name="toTop"  public="true" abstract="true">
<description>
Move the specified agent to the top of the list.&#xa; If the agent isn&#x27;t in the list this call is ignored.
</description>
<parameter name="info">
<type class="javax.baja.agent.AgentInfo"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.toTop(int) -->
<method name="toTop"  public="true" abstract="true">
<description>
Move the agent at the specified index to the top of the &#xa; list.  If the index is out of range this call is ignored.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.toTop(javax.baja.agent.AgentFilter) -->
<method name="toTop"  public="true" default="true">
<description>
Move the agents that match the given filter to the top&#xa; of the list.
</description>
<tag name="@since">Niagara 4.0</tag>
<parameter name="filter">
<type class="javax.baja.agent.AgentFilter"/>
<description>
The filter to select agents that should move&#xa;               to the top of this agent list.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.toTop(java.util.function.Predicate&lt;javax.baja.agent.AgentInfo&gt;) -->
<method name="toTop"  public="true" default="true">
<description>
Move the agents that match the given predicate to the top&#xa; of the list.
</description>
<tag name="@since">Niagara 4.0</tag>
<parameter name="predicate">
<parameterizedType class="java.util.function.Predicate">
<args>
<type class="javax.baja.agent.AgentInfo"/>
</args>
</parameterizedType>
<description>
The predicate to select agents that should move&#xa;                  to the top of this agent list.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.toBottom(java.lang.String) -->
<method name="toBottom"  public="true" abstract="true">
<description>
Move the specified agent to the bottom of the list.  &#xa; If this list doesn&#x27;t contain the specified id, then&#xa; this call is ignored.
</description>
<parameter name="id">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.toBottom(javax.baja.agent.AgentInfo) -->
<method name="toBottom"  public="true" abstract="true">
<description>
Move the specified agent to the bottom of the list.&#xa; If the agent isn&#x27;t in the list this call is ignored.
</description>
<parameter name="info">
<type class="javax.baja.agent.AgentInfo"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.toBottom(int) -->
<method name="toBottom"  public="true" abstract="true">
<description>
Move the type at the specified index to the bottom of the &#xa; list. If the index is out of range this call is ignored.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.toBottom(javax.baja.agent.AgentFilter) -->
<method name="toBottom"  public="true" default="true">
<description>
Move the agents that match the given filter to the bottom&#xa; of the list.
</description>
<tag name="@since">Niagara 4.0</tag>
<parameter name="filter">
<type class="javax.baja.agent.AgentFilter"/>
<description>
The filter to select agents that should move&#xa;               to the bottom of this agent list.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.toBottom(java.util.function.Predicate&lt;javax.baja.agent.AgentInfo&gt;) -->
<method name="toBottom"  public="true" default="true">
<description>
Move the agents that match the given predicate to the bottom&#xa; of the list.
</description>
<tag name="@since">Niagara 4.0</tag>
<parameter name="predicate">
<parameterizedType class="java.util.function.Predicate">
<args>
<type class="javax.baja.agent.AgentInfo"/>
</args>
</parameterizedType>
<description>
The predicate to select agents that should move&#xa;                  to the bottom of this agent list.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.swap(int, int) -->
<method name="swap"  public="true" abstract="true">
<description>
Swap the placement of the agents at the specified indices.
</description>
<parameter name="index1">
<type class="int"/>
</parameter>
<parameter name="index2">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.AgentList.iterator() -->
<method name="iterator"  public="true" default="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<parameterizedType class="java.util.Iterator">
<args>
<type class="javax.baja.agent.AgentInfo"/>
</args>
</parameterizedType>
</return>
</method>

</class>
</bajadoc>
