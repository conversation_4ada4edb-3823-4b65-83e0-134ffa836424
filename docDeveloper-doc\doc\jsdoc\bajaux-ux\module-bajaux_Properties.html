<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>bajaux Module: bajaux/Properties</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">bajaux</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-bajaux_commands_Command.html">bajaux/commands/Command</a></li><li><a href="module-bajaux_commands_CommandGroup.html">bajaux/commands/CommandGroup</a></li><li><a href="module-bajaux_commands_ToggleCommand.html">bajaux/commands/ToggleCommand</a></li><li><a href="module-bajaux_commands_ToggleCommandGroup.html">bajaux/commands/ToggleCommandGroup</a></li><li><a href="module-bajaux_container_wb_Clipboard.html">bajaux/container/wb/Clipboard</a></li><li><a href="module-bajaux_container_wb_StringList.html">bajaux/container/wb/StringList</a></li><li><a href="module-bajaux_dragdrop_dragDropUtils.html">bajaux/dragdrop/dragDropUtils</a></li><li><a href="module-bajaux_dragdrop_Envelope.html">bajaux/dragdrop/Envelope</a></li><li><a href="module-bajaux_dragdrop_NavNodeEnvelope.html">bajaux/dragdrop/NavNodeEnvelope</a></li><li><a href="module-bajaux_dragdrop_StringEnvelope.html">bajaux/dragdrop/StringEnvelope</a></li><li><a href="module-bajaux_events.html">bajaux/events</a></li><li><a href="module-bajaux_icon_iconUtils.html">bajaux/icon/iconUtils</a></li><li><a href="module-bajaux_lifecycle_WidgetManager.html">bajaux/lifecycle/WidgetManager</a></li><li><a href="module-bajaux_mixin_batchLoadMixin.html">bajaux/mixin/batchLoadMixin</a></li><li><a href="module-bajaux_mixin_batchSaveMixin.html">bajaux/mixin/batchSaveMixin</a></li><li><a href="module-bajaux_mixin_responsiveMixIn.html">bajaux/mixin/responsiveMixIn</a></li><li><a href="module-bajaux_mixin_subscriberMixIn.html">bajaux/mixin/subscriberMixIn</a></li><li><a href="module-bajaux_Properties.html">bajaux/Properties</a></li><li><a href="module-bajaux_registry_Registry.html">bajaux/registry/Registry</a></li><li><a href="module-bajaux_registry_RegistryEntry.html">bajaux/registry/RegistryEntry</a></li><li><a href="module-bajaux_spandrel.html">bajaux/spandrel</a></li><li><a href="module-bajaux_spandrel_jsx.html">bajaux/spandrel/jsx</a></li><li><a href="module-bajaux_util_CommandButton.html">bajaux/util/CommandButton</a></li><li><a href="module-bajaux_util_CommandButtonGroup.html">bajaux/util/CommandButtonGroup</a></li><li><a href="module-bajaux_util_SaveCommand.html">bajaux/util/SaveCommand</a></li><li><a href="module-bajaux_Validators.html">bajaux/Validators</a></li><li><a href="module-bajaux_Widget.html">bajaux/Widget</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="interfaces.list.html" class="dropdown-toggle" data-toggle="dropdown">Interfaces<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-bajaux_commands_Command-Undoable.html">bajaux/commands/Command~Undoable</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="tutorials.list.html" class="dropdown-toggle" data-toggle="dropdown">Tutorials<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="tutorial-10-mfw-gettingStarted.html">Getting Started - MyFirstWidget</a></li><li><a href="tutorial-20-mfw-modifying.html">Saving Modifications to Station</a></li><li><a href="tutorial-30-mfw-dashboarding.html">Making your Widget Dashboardable</a></li><li><a href="tutorial-40-tipsAndTricks.html">Tips and Tricks</a></li><li><a href="tutorial-50-spandrel.html">Building Composite Widgets With spandrel</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: bajaux/Properties</h1>
<section>

<header>
    
        
            
        
    
</header>


<article>
    <div class="container-overview">
    
        
            <div class="description"><p>Configurable Properties for Widgets.</p></div>
        

        
            
<hr>
<dt>
    <h4 class="name" id="module:bajaux/Properties"><span class="type-signature"></span>new (require("bajaux/Properties"))( [obj])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>The properties for a Widget. This are configurable properties<br>
that can be configured by a user.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>obj</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>
|

<span class="param-type">Array.&lt;Object></span>
|

<span class="param-type"><a href="module-bajaux_Properties.html">module:bajaux/Properties</a></span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>an initial<br>
set of properties with which to initialize this Properties instance. This<br>
can be an object literal, an array of object literals, or another<br>
Properties instance.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
        <h5>Examples</h5>
        
        <p class="code-caption">Create a Properties instance with an object literal.</p>
    
    <pre class="sunlight-highlight-javascript">var props = new Properties({
  myProp: &#x27;value&#x27;,
  myHiddenProp: { value: &#x27;hiddenValue&#x27;, hidden: true }
});
props.getValue(&#x27;myProp&#x27;); // &#x27;value&#x27;
props.getValue(&#x27;myHiddenProp&#x27;); // &#x27;hiddenValue&#x27;
props.get(&#x27;myHiddenProp&#x27;).hidden); // true</pre>

        <p class="code-caption">Create a Properties instance with an array. Equivalent to the
above.</p>
    
    <pre class="sunlight-highlight-javascript">var props = new Properties([
  { name: &#x27;myProp&#x27;, value: &#x27;value&#x27; },
  { name: &#x27;myHiddenProp&#x27;, value: &#x27;hiddenValue&#x27;, hidden: true }
]);</pre>

        <p class="code-caption">Create a Properties instance with a defaultValue for `myProp`.
</p>
    
    <pre class="sunlight-highlight-javascript">var props = new Properties([
  { name: &#x27;myProp&#x27;, value: &#x27;value&#x27;, defaultValue: &#x27;this is default&#x27; },
  { name: &#x27;myHiddenProp&#x27;, value: &#x27;hiddenValue&#x27; }
]);

props.getValue(&#x27;myProp&#x27;); // &#x27;value&#x27;
props.getDefaultValue(&#x27;myProp&#x27;); // &#x27;this is default&#x27;

props.setValue(&#x27;myProp&#x27;, undefined); // make the property value undefined

props.getValue(&#x27;myProp&#x27;); // &#x27;this is default&#x27;

props.setValue(&#x27;myProp&#x27;, null);
props.getValue(&#x27;myProp&#x27;); // null

props.getValue(&#x27;propThatDoesNotExist&#x27;);  // null</pre>


    
</dd>

        
    
    </div>

    

    

    

    

    

    
        <h3 class="subsection-title">Members</h3>

        <dl>
            
<hr>
<dt class="name" id="toObject">
    <h4 id="toObject"><span class="type-signature"></span>toObject<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Convert this Properties instance into a new raw object literal. The object<br>
keys will be the property names, and the values will be the property<br>
values (as returned by <code>#getValue()</code>). Note that any metadata about each<br>
Property will be lost; to preserve metadata, use <code>toObject()</code>.</p>
<p>This function will be useful for converting Properties into a context<br>
object.</p>
    </div>
    

    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.9 (replaces toValueMap, which still works)</li>
		</ul>
	</dd>
	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_Properties.html#toObject">module:bajaux/Properties#toObject</a></li>
			</ul>
	</dd>
	

	
</dl>


    
        <h5>Example</h5>
        
        <p class="code-caption">Property Sheet converts slot facets into Widget properties. I
need to use those facets in my field editor for number formatting
purposes.</p>
    
    <pre class="sunlight-highlight-javascript">MyFieldEditor.prototype.numberToString = function (number) {
  var cx = this.properties().toValueMap();
  if (typeof cx.precision !== &#x27;number&#x27;) {
    cx.precision = 2;
  }
  return number.toString(cx);
};</pre>


    
</dd>

        </dl>
    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id=".extend"><span class="type-signature">&lt;static> </span>extend()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Create a new Properties instance containing the merged properties of<br>
one or more other Properties instances. Properties of instances later in<br>
the argument list will override properties of earlier instances.</p>
<p>Each argument can be of any type acceptable to the Properties constructor<br>
(<code>Object</code>, <code>Array</code>, or <code>Properties</code>).</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_Properties.html">module:bajaux/Properties</a></span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
    <pre class="sunlight-highlight-javascript">var mergedProps = Properties.extend(
  { myProp: &#x27;a&#x27; },
  new Properties({ myProp: { value: &#x27;a2&#x27;, hidden: true } })
);
mergedProps.getValue(&#x27;myProp&#x27;); // &#x27;a2&#x27;
mergedProps.get(&#x27;myProp&#x27;).hidden; // true</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="add"><span class="type-signature"></span>add(prop [, value])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Add a Property.</p>
<p>Please note, if the Property isn't transient, it's value may be saved and loaded<br>
elsewhere (for example, in the case of Px, reloaded from a Px file).</p>
<p>If the property does not already exist on this Properties instance, this<br>
will emit a <code>PROPERTY_ADDED</code> event, with an array (of length 1) of the<br>
property names added.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>prop</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>The Property object to be added or the name of the Property.</p>
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>The name of the Property being added or if the first argument<br>
is a String, this is the value.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>value</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>The current value of the Property.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>displayName</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>The display name of the Property. For translated values,<br>
this can be a format string. For example, <code>%lexicon(moduleName:keyName)%</code>.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>transient</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>A hint to an external editor that it doesn't need to save<br>
the state of this Property.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>hidden</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>A hint to an external editor to hide this Property.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>readonly</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>A hint to an external editor to make the editor for<br>
this Property readonly.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>typeSpec</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>A hint to Niagara on what Simple Niagara Type to use when<br>
encoding/decoding the Property. If the Type is a FrozenEnum, the tag name of the Enum<br>
should be used.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    

        <tr>
            
                <td class="name"><code>value</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>if passing a string name as the first argument, pass the<br>
value here as the second.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>returns the Properties instance.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_Properties.html">module:bajaux/Properties</a></span>



    </dd>
</dl>


        

    
        <h5>Examples</h5>
        
        <p class="code-caption">Add a Property</p>
    
    <pre class="sunlight-highlight-javascript">widget.properties().add(&quot;foo&quot;, true);</pre>

        <p class="code-caption">Add a hidden Property</p>
    
    <pre class="sunlight-highlight-javascript">widget.properties().add({
    name: &quot;foo&quot;,
    value: true,
    hidden: true
  });</pre>

        <p class="code-caption">Add a transient, readonly, hidden Property</p>
    
    <pre class="sunlight-highlight-javascript">widget.properties().add({
    name: &quot;foo&quot;,
    value: true,
    hidden: true,
    transient: true,
    readonly: true
  });</pre>

        <p class="code-caption">Add a Property that maps to the baja:Weekday FrozenEnum in Niagara</p>
    
    <pre class="sunlight-highlight-javascript">widget.properties().add({
    name: &quot;weekday&quot;,
    value: &quot;tuesday&quot;,
    typeSpec: &quot;baja:Weekday&quot;
  });</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="addAll"><span class="type-signature"></span>addAll(arr)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Add a number of properties at once. The object literal configuration<br>
for each property is the same as for <code>add()</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>arr</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;Object></span>
|

<span class="param-type"><a href="module-bajaux_Properties.html">module:bajaux/Properties</a></span>



            
            </td>

            

            

            <td class="description last"><p>an array of object<br>
literals to become properties, or a Properties instance to copy onto this<br>
one</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>this</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_Properties.html">module:bajaux/Properties</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="clone"><span class="type-signature"></span>clone()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return a clone of this Properties object that can be modified without<br>
changing the original.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_Properties.html">module:bajaux/Properties</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="each"><span class="type-signature"></span>each(func)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Iterate through each Property.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>func</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>The function to be called for each Property<br>
found in the array. This function will have the index, name and value of<br>
the Property passed to it. The Context of the function callback will be<br>
the Properties instance. If iteration needs to stop prematurely then<br>
the function can return false.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>Returns the Properties instance.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_Properties.html">module:bajaux/Properties</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="get"><span class="type-signature"></span>get( [name] [, attrName] [, defAttrValue])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>If no arguments are specified, a copy of the internal Properties array will be returned.<br>
If only the name is specified, return a copy of the Property for the given name or index.<br>
If a name/index and an attribute name is specified, then return the attribute of a Property.<br>
If no particular value can be found then return null;</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>The name or index of the Property to look up. If not<br>
specified, a copy of the internal Property array will be returned.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>attrName</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>If specified, this will retrieve a specific attribute<br>
of the Property. For example, specifying 'value' will get the value of the Property.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>defAttrValue</code></td>
            

            <td class="type">
            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>If specified, this value will be returned if the attribute name<br>
can't be found.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>A copy of the Property object or null if the Property can't be found.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Object</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getDefaultValue"><span class="type-signature"></span>getDefaultValue( [name])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>If a name/index is specified then return a Property's default value or null if nothing can be found.<br>
If no name is specified then return an object containing all the Property names and default values.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>If specified, the name of the Property to return or the Property's index.<br>
If this parameter is not specified, an object containing all of the property default values will be returned.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The Properties default value or null if nothing is found.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">*</span>
|

<span class="param-type">null</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getIndex"><span class="type-signature"></span>getIndex(name)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return a Property's index via its name or -1 if it can't be found.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>The name of the Property to look up the index number for.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>Returns the index number of the Property.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getMetadata"><span class="type-signature"></span>getMetadata( [name])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>If no arguments are specified, a copy of the internal Properties array will be returned.<br>
If only the name is specified, return a the corresponding meta data object literal for the Property</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>The name or index of the Property to look up. If not<br>
specified, a copy of the internal Property array will be returned.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The meta data object literal or null if the Property could not be found</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Object</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getValue"><span class="type-signature"></span>getValue( [name] [, defVal])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>If no name is specified then return an object containing all the Property names and values.<br>
If a name/index is specified then return a Property's value or null if nothing can be found.</p>
<p>Property values will fallback to defaultValue if value is not set.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>If specified, the name of the Property to return or the Property's index.<br>
If this parameter is not specified, an object containing all of the property values will be returned.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>defVal</code></td>
            

            <td class="type">
            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>If specified, this will return if a value can't be found providing the first argument is<br>
a String. This will override the defaultValue for the given property.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The Properties value or null if nothing is found.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">*</span>
|

<span class="param-type">null</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="has"><span class="type-signature"></span>has(name)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if the Property can be found via its name of index.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>The name or index of the Property to look up.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>returns true if the Property is found.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="off"><span class="type-signature"></span>off(name [, func])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Called to detach any event handlers from a Property or to<br>
stop listening to all Property change events.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>The Property name to remove.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>func</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>The event handler to remove.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The Properties instance.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_Properties.html">module:bajaux/Properties</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="remove"><span class="type-signature"></span>remove(name)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Remove a Property.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>The name of the Property to remove.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>returns the Properties instance.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_Properties.html">module:bajaux/Properties</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setMetadata"><span class="type-signature"></span>setMetadata(name, metadata [, options])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>If an object is specified as the first argument, it will be iterated<br>
through with object's properties being set as metadata</p>
<p>If a name/index is specified along with a value, the metadata for the<br>
particular name/index will be set.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>The name of the Property we're going to set or a<br>
an object containing several metadata values to be set. The value for each property name in the object<br>
will be set as corresponding metadata for that property</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>metadata</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>The metadata to be set.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>An optional parameter that is passed down into any changed<br>
callbacks or event handlers.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.4</li>
		</ul>
	</dd>
	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>Return false if at least one of the properties wasn't found.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setValue"><span class="type-signature"></span>setValue(name, value [, options])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>If an object is specified as the first argument, it will be iterated<br>
through with object's properties being set as values.</p>
<p>If a name/index is specified along with a value, the value for the<br>
particular value will be set.</p>
<p>A Widget can detect Property changes by implemented a method called<br>
<code>changed</code>. The changed call back will have the Property name and new value<br>
passed to it. A developer can then override 'doChanged' to handle any<br>
callbacks in their own widget subclasses.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>The name of the Property we're going to set or a<br>
an object containing many values to be set.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>value</code></td>
            

            <td class="type">
            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>The value to be set.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>An optional parameter that is passed down into any changed<br>
callbacks or event handlers.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>Return false if at least one of the properties wasn't found.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="size"><span class="type-signature"></span>size()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the total number of Properties.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>returns the total number of Properties.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="subset"><span class="type-signature"></span>subset(keys)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Build a new Properties instance consisting of a subset of the properties<br>
contained within this one. Useful for propagating a specific set of<br>
properties down to a child widget.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>keys</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;String></span>



            
            </td>

            

            

            <td class="description last"><p>which keys to include in the subset.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_Properties.html">module:bajaux/Properties</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="toDisplayName"><span class="type-signature"></span>toDisplayName(name)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return a promise that will resolve once the display name of the Property<br>
has been resolved.</p>
<p>If the Property doesn't have a display name, the Property's name will<br>
be used instead.</p>
<p>Please note, a display name can be in the format of a Lexicon format. For instance,<br>
<code>%lexicon(moduleName:keyName)%</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>The name or index of the Property.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The display name of the Property.</p>
</div>




        

    
</dd>

        </dl>
    

    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	bajaux Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:54+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>