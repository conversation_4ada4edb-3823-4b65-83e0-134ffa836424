<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.req.BInfinityReloadLineRequest" name="BInfinityReloadLineRequest" packageName="com.tridium.andoverInfinity.comm.req" public="true">
<description>
Used to send a single line from a reload file to the controller in reload mode
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.comm.req.BDdfRequest"/>
</extends>
<implements>
<type class="com.tridium.andoverInfinity.comm.Vt100Const"/>
</implements>
<implements>
<type class="com.tridium.andoverInfinity.comm.req.RequiresNetworkAccess"/>
</implements>
<implements>
<type class="com.tridium.ddf.comm.req.BIDdfCustomRequest"/>
</implements>
<!-- com.tridium.andoverInfinity.comm.req.BInfinityReloadLineRequest() -->
<constructor name="BInfinityReloadLineRequest" public="true">
<description>
Framework use only
</description>
</constructor>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReloadLineRequest.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReloadLineRequest.processReceive(com.tridium.ddf.comm.IDdfDataFrame) -->
<method name="processReceive"  public="true">
<description/>
<parameter name="iDevDataFrame">
<type class="com.tridium.ddf.comm.IDdfDataFrame"/>
</parameter>
<return>
<type class="com.tridium.ddf.comm.rsp.BIDdfResponse"/>
</return>
<throws>
<type class="com.tridium.ddf.comm.rsp.DdfResponseException"/>
</throws>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReloadLineRequest.toByteArray() -->
<method name="toByteArray"  public="true">
<description/>
<return>
<type class="byte" dimension="1"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReloadLineRequest.processErrorResponse(com.tridium.ddf.comm.rsp.DdfResponseException) -->
<method name="processErrorResponse"  public="true">
<description/>
<parameter name="errorRsp">
<type class="com.tridium.ddf.comm.rsp.DdfResponseException"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReloadLineRequest.processLateResponse(com.tridium.ddf.comm.rsp.BIDdfResponse) -->
<method name="processLateResponse"  public="true">
<description/>
<parameter name="ddfRsp">
<type class="com.tridium.ddf.comm.rsp.BIDdfResponse"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReloadLineRequest.processResponse(com.tridium.ddf.comm.rsp.BIDdfResponse) -->
<method name="processResponse"  public="true">
<description/>
<parameter name="ddfRsp">
<type class="com.tridium.ddf.comm.rsp.BIDdfResponse"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReloadLineRequest.processTimeout() -->
<method name="processTimeout"  public="true">
<description>
During reloads, we intentionally time out between lines because the&#xa; infinity panel does not send anything (other than occasional error&#xa; messges) during a reload.
</description>
<tag name="@see">com.tridium.ddf.comm.req.BIDdfCustomRequest#processTimeout()</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReloadLineRequest.setNetwork(com.tridium.andoverInfinity.BInfinityNetwork) -->
<method name="setNetwork"  public="true">
<description>
Implementation of RequiresNetworkAccess interface
</description>
<tag name="@see">com.tridium.andoverInfinity.comm.req.RequiresNetworkAccess#setNetwork(com.tridium.andoverInfinity.BInfinityNetwork)</tag>
<parameter name="network">
<type class="com.tridium.andoverInfinity.BInfinityNetwork"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReloadLineRequest.responseTimeout -->
<field name="responseTimeout"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;responseTimeout&lt;/code&gt; property.&#xa; This is the amount of time that the BIDdfCommunicator will&#xa; Wait for a response before retrying or ultimately timing out.
</description>
<tag name="@see">#getResponseTimeout</tag>
<tag name="@see">#setResponseTimeout</tag>
</field>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReloadLineRequest.remainingRetryCount -->
<field name="remainingRetryCount"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;remainingRetryCount&lt;/code&gt; property.&#xa; This is the remaining number of retry counts that the BIDdfCommunicator&#xa; Will attempt before timing out completely.
</description>
<tag name="@see">#getRemainingRetryCount</tag>
<tag name="@see">#setRemainingRetryCount</tag>
</field>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReloadLineRequest.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
