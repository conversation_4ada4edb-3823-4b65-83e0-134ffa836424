<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.BacnetConfirmedServiceChoice" name="BacnetConfirmedServiceChoice" packageName="javax.baja.bacnet" public="true" interface="true" abstract="true" category="interface">
<description>
This interface contains constants which represent the values&#xa; of the BacnetConfirmedServiceChoice iteration.  For more&#xa; information, see Bacnet spec, Clause 21, Confirmed Service&#xa; Productions.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 2$ $Date: 12/19/01 4:35:45 PM$</tag>
<tag name="@creation">01 Jul 97</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.ACKNOWLEDGE_ALARM -->
<field name="ACKNOWLEDGE_ALARM"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.CONFIRMED_COV_NOTIFICATION -->
<field name="CONFIRMED_COV_NOTIFICATION"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.CONFIRMED_EVENT_NOTIFICATION -->
<field name="CONFIRMED_EVENT_NOTIFICATION"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.GET_ALARM_SUMMARY -->
<field name="GET_ALARM_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.GET_ENROLLMENT_SUMMARY -->
<field name="GET_ENROLLMENT_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.GET_EVENT_INFORMATION -->
<field name="GET_EVENT_INFORMATION"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.SUBSCRIBE_COV -->
<field name="SUBSCRIBE_COV"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.SUBSCRIBE_COV_PROPERTY -->
<field name="SUBSCRIBE_COV_PROPERTY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.LIFE_SAFTEY_OPERATION -->
<field name="LIFE_SAFTEY_OPERATION"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.ATOMIC_READ_FILE -->
<field name="ATOMIC_READ_FILE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.ATOMIC_WRITE_FILE -->
<field name="ATOMIC_WRITE_FILE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.ADD_LIST_ELEMENT -->
<field name="ADD_LIST_ELEMENT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.REMOVE_LIST_ELEMENT -->
<field name="REMOVE_LIST_ELEMENT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.CREATE_OBJECT -->
<field name="CREATE_OBJECT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.DELETE_OBJECT -->
<field name="DELETE_OBJECT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.READ_PROPERTY -->
<field name="READ_PROPERTY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.READ_PROPERTY_CONDITIONAL -->
<field name="READ_PROPERTY_CONDITIONAL"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.READ_PROPERTY_MULTIPLE -->
<field name="READ_PROPERTY_MULTIPLE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.READ_RANGE -->
<field name="READ_RANGE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.WRITE_PROPERTY -->
<field name="WRITE_PROPERTY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.WRITE_PROPERTY_MULTIPLE -->
<field name="WRITE_PROPERTY_MULTIPLE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.DEVICE_COMMUNICATION_CONTROL -->
<field name="DEVICE_COMMUNICATION_CONTROL"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.CONFIRMED_PRIVATE_TRANSFER -->
<field name="CONFIRMED_PRIVATE_TRANSFER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.CONFIRMED_TEXT_MESSAGE -->
<field name="CONFIRMED_TEXT_MESSAGE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.REINITIALIZE_DEVICE -->
<field name="REINITIALIZE_DEVICE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.VT_OPEN -->
<field name="VT_OPEN"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.VT_CLOSE -->
<field name="VT_CLOSE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.VT_DATA -->
<field name="VT_DATA"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.AUTHENTICATE -->
<field name="AUTHENTICATE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.REQUEST_KEY -->
<field name="REQUEST_KEY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConfirmedServiceChoice.TAGS -->
<field name="TAGS"  public="true" static="true" final="true">
<type class="java.lang.String" dimension="1"/>
<description/>
</field>

</class>
</bajadoc>
