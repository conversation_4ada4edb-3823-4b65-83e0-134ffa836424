<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>bajaux Module: bajaux/commands/ToggleCommandGroup</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">bajaux</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-bajaux_commands_Command.html">bajaux/commands/Command</a></li><li><a href="module-bajaux_commands_CommandGroup.html">bajaux/commands/CommandGroup</a></li><li><a href="module-bajaux_commands_ToggleCommand.html">bajaux/commands/ToggleCommand</a></li><li><a href="module-bajaux_commands_ToggleCommandGroup.html">bajaux/commands/ToggleCommandGroup</a></li><li><a href="module-bajaux_container_wb_Clipboard.html">bajaux/container/wb/Clipboard</a></li><li><a href="module-bajaux_container_wb_StringList.html">bajaux/container/wb/StringList</a></li><li><a href="module-bajaux_dragdrop_dragDropUtils.html">bajaux/dragdrop/dragDropUtils</a></li><li><a href="module-bajaux_dragdrop_Envelope.html">bajaux/dragdrop/Envelope</a></li><li><a href="module-bajaux_dragdrop_NavNodeEnvelope.html">bajaux/dragdrop/NavNodeEnvelope</a></li><li><a href="module-bajaux_dragdrop_StringEnvelope.html">bajaux/dragdrop/StringEnvelope</a></li><li><a href="module-bajaux_events.html">bajaux/events</a></li><li><a href="module-bajaux_icon_iconUtils.html">bajaux/icon/iconUtils</a></li><li><a href="module-bajaux_lifecycle_WidgetManager.html">bajaux/lifecycle/WidgetManager</a></li><li><a href="module-bajaux_mixin_batchLoadMixin.html">bajaux/mixin/batchLoadMixin</a></li><li><a href="module-bajaux_mixin_batchSaveMixin.html">bajaux/mixin/batchSaveMixin</a></li><li><a href="module-bajaux_mixin_responsiveMixIn.html">bajaux/mixin/responsiveMixIn</a></li><li><a href="module-bajaux_mixin_subscriberMixIn.html">bajaux/mixin/subscriberMixIn</a></li><li><a href="module-bajaux_Properties.html">bajaux/Properties</a></li><li><a href="module-bajaux_registry_Registry.html">bajaux/registry/Registry</a></li><li><a href="module-bajaux_registry_RegistryEntry.html">bajaux/registry/RegistryEntry</a></li><li><a href="module-bajaux_spandrel.html">bajaux/spandrel</a></li><li><a href="module-bajaux_spandrel_jsx.html">bajaux/spandrel/jsx</a></li><li><a href="module-bajaux_util_CommandButton.html">bajaux/util/CommandButton</a></li><li><a href="module-bajaux_util_CommandButtonGroup.html">bajaux/util/CommandButtonGroup</a></li><li><a href="module-bajaux_util_SaveCommand.html">bajaux/util/SaveCommand</a></li><li><a href="module-bajaux_Validators.html">bajaux/Validators</a></li><li><a href="module-bajaux_Widget.html">bajaux/Widget</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="interfaces.list.html" class="dropdown-toggle" data-toggle="dropdown">Interfaces<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-bajaux_commands_Command-Undoable.html">bajaux/commands/Command~Undoable</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="tutorials.list.html" class="dropdown-toggle" data-toggle="dropdown">Tutorials<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="tutorial-10-mfw-gettingStarted.html">Getting Started - MyFirstWidget</a></li><li><a href="tutorial-20-mfw-modifying.html">Saving Modifications to Station</a></li><li><a href="tutorial-30-mfw-dashboarding.html">Making your Widget Dashboardable</a></li><li><a href="tutorial-40-tipsAndTricks.html">Tips and Tricks</a></li><li><a href="tutorial-50-spandrel.html">Building Composite Widgets With spandrel</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: bajaux/commands/ToggleCommandGroup</h1>
<section>

<header>
    
        
            
        
    
</header>


<article>
    <div class="container-overview">
    
        

        
            
<hr>
<dt>
    <h4 class="name" id="module:bajaux/commands/ToggleCommandGroup"><span class="type-signature"></span>new (require("bajaux/commands/ToggleCommandGroup"))(params)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>ToggleCommandGroup is a special CommandGroup that behaves like a radio button group.</p>
    </div>
    

    
        <h5>Extends:</h5>
        


    <ul>
        <li><a href="module-bajaux_commands_CommandGroup.html">module:bajaux/commands/CommandGroup</a></li>
    </ul>


    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>



            
            </td>

            

            

            <td class="description last">
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>onChange</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>provide a callback function that can work with the selected value</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>commands</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array</span>



            
            </td>

            

            

            <td class="description last"><p>An array of objects, each of which will pass through to making a<br>
ToggleCommand. See <a href="module-bajaux_commands_Command.html">module:bajaux/commands/Command</a> for all acceptable parameters.<br>
Pass an optional &quot;value&quot; parameter to a command configuration object to associate a value with it.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
        <h5>Examples</h5>
        
    <pre class="sunlight-highlight-javascript">new ToggleCommandGroup({
  commands: [
    {
      module: &quot;mymodule&quot;,
      lex: &quot;mycommand1&quot;,
      value: 0
    },
    {
      module: &quot;mymodule&quot;,
      lex: &quot;mycommand2&quot;,
      value: 1
    },
    {
      module: &quot;mymodule&quot;,
      lex: &quot;mycommand3&quot;,
      value: 2
    }
  ],
  onChange: function (value) {
    // receives the values specified above.
    switch (value) {
      case 0:
      case 1:
      case 2:
    }
  }
});</pre>

        <p class="code-caption">Use a flat style for your toggle button group.</p>
    
    <pre class="sunlight-highlight-javascript">var buttonGroup = new CommandButtonGroup();
return buttonGroup.initialize(dom, { toggleGroup: true })
  .then(function () {
    return buttonGroup.load(toggleCommandGroup);
  });</pre>


    
</dd>

        
    
    </div>

    

    

    

    

    

    
        <h3 class="subsection-title">Members</h3>

        <dl>
            
<hr>
<dt class="name" id="off">
    <h4 id="off"><span class="type-signature"></span>off<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Unregister a function callback handler for the specified event.</p>
    </div>
    

    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.12</li>
		</ul>
	</dd>
	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#off">module:bajaux/commands/CommandGroup#off</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id="on">
    <h4 id="on"><span class="type-signature"></span>on<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Register a function callback handler for the specified event.</p>
    </div>
    

    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.12</li>
		</ul>
	</dd>
	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#on">module:bajaux/commands/CommandGroup#on</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        </dl>
    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id="add"><span class="type-signature"></span>add(command)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Add a command to this group. Triggers a<br>
<code>bajaux:changecommandgroup</code> event. Multiple arguments can be specified to<br>
add many commands at once.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>command</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_commands_Command.html">module:bajaux/commands/Command</a></span>



            
            </td>

            

            

            <td class="description last"><p>The command to add.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#add">module:bajaux/commands/CommandGroup#add</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>Return the CommandGroup instance.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_commands_CommandGroup.html">module:bajaux/commands/CommandGroup</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="doSort"><span class="type-signature"></span>doSort(kids)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Does the work of sorting this command group. By default, does nothing.</p>
<p>When overriding this method, be sure to return the sorted array as a<br>
parameter to <code>deferred.resolve</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>kids</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array</span>



            
            </td>

            

            

            <td class="description last"><p>An array of the command group's children - this may<br>
include commands or sub-CommandGroups.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#doSort">module:bajaux/commands/CommandGroup#doSort</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>an array containing the sorted children, or<br>
a promise to be resolved with same</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array</span>
|

<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="filter"><span class="type-signature"></span>filter(options)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Filters the commands in this command group based on the results of<br>
a test function, and returns a new, filtered CommandGroup instance.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>An options object literal</p>
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>include</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>A function that will receive a<br>
command object and return true or false depending on whether to include<br>
that command in the filtered results. If omitted, defaults to always<br>
return true.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>flags</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>A number containing flag bits to test on<br>
each command. If omitted, defaults to Command.flags.ALL.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#filter">module:bajaux/commands/CommandGroup#filter</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>A filtered command group.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_commands_CommandGroup.html">module:bajaux/commands/CommandGroup</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="findCommand"><span class="type-signature"></span>findCommand(id)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Find a Command in this CommandGroup (or sub CommandGroup) via its id and return it.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>id</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>
|

<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>an id, filter function, or Command constructor. A filter<br>
function receives a Command instance and returns a boolean result - the first Command passing<br>
the filter will be returned. If passing a Command constructor, the first Command that is an<br>
instance of that constructor will be returned.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#findCommand">module:bajaux/commands/CommandGroup#findCommand</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>return the found Command (or null if nothing found).</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_commands_Command.html">module:bajaux/commands/Command</a></span>
|

<span class="param-type">null</span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
    <pre class="sunlight-highlight-javascript">group.findCommand(DeleteCommand); // returns the first instance of DeleteCommand in the group if present.</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="flatten"><span class="type-signature"></span>flatten(options)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return a listing of the commands (not command groups) contained within<br>
this group. Can optionally recursively visit down through any<br>
sub-CommandGroups contained in this group (depth first) to return all<br>
commands. Can optionally filter commands by flags as well.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>An options object.</p>
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>flags</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>flags Used to filter commands. If omitted,<br>
includes all commands regardless of flags.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>recurse</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p><code>true</code> if should recurse down through any<br>
sub-groups.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#flatten">module:bajaux/commands/CommandGroup#flatten</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>An array of Commands.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array</span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
    <pre class="sunlight-highlight-javascript">cmdGroup.flatten(); // all child commands
  cmdGroup.flatten(Command.flags.MENU); // only children with MENU flag
  cmdGroup.flatten({ flags: Command.flags.MENU, recurse: true });</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="get"><span class="type-signature"></span>get(index)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return a Command/CommandGroup based upon the index or<br>
null if nothing can be found.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>index</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#get">module:bajaux/commands/CommandGroup#get</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_commands_Command.html">module:bajaux/commands/Command</a></span>
|

<span class="param-type"><a href="module-bajaux_commands_CommandGroup.html">module:bajaux/commands/CommandGroup</a></span>
|

<span class="param-type">null</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getChildren"><span class="type-signature"></span>getChildren()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Returns a defensive copy of this group's array of children.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#getChildren">module:bajaux/commands/CommandGroup#getChildren</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getDisplayNameFormat"><span class="type-signature"></span>getDisplayNameFormat()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the format display name of this command group.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#getDisplayNameFormat">module:bajaux/commands/CommandGroup#getDisplayNameFormat</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getFlags"><span class="type-signature"></span>getFlags()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Returns this group's flags.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#getFlags">module:bajaux/commands/CommandGroup#getFlags</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getIcon"><span class="type-signature"></span>getIcon()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the CommandGroup's icon URI</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.12</li>
		</ul>
	</dd>
	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#getIcon">module:bajaux/commands/CommandGroup#getIcon</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getSelected"><span class="type-signature"></span>getSelected()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the selected command</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The selected ToggleCommand</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_commands_ToggleCommand.html">module:bajaux/commands/ToggleCommand</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getSelectedValue"><span class="type-signature"></span>getSelectedValue()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the selected value if available</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>If a 'value' parameter is set it will be returned</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">any</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="hasFlags"><span class="type-signature"></span>hasFlags(flags)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Check to see if this group's flags match any of the bits of the<br>
input flags.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>flags</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>The flags to check against</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#hasFlags">module:bajaux/commands/CommandGroup#hasFlags</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isCommand"><span class="type-signature"></span>isCommand()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Always returns false.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#isCommand">module:bajaux/commands/CommandGroup#isCommand</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isEmpty"><span class="type-signature"></span>isEmpty()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if this CommandGroup doesn't contain any children.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#isEmpty">module:bajaux/commands/CommandGroup#isEmpty</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>true if empty.</p>
</div>




        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isLoading"><span class="type-signature"></span>isLoading()</h4>
    
    
</dt>
<dd>

    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#isLoading">module:bajaux/commands/CommandGroup#isLoading</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>true if any of the Commands are still loading.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isToggleCommand"><span class="type-signature"></span>isToggleCommand()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Always returns false.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#isToggleCommand">module:bajaux/commands/CommandGroup#isToggleCommand</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="jq"><span class="type-signature"></span>jq( [jqDom])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>If a jQuery DOM argument is specified, this will set the DOM.<br>
If not specified then no DOM will be set.<br>
This method will always return the jQuery DOM associated with this CommandGroup.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>jqDom</code></td>
            

            <td class="type">
            
                
<span class="param-type">JQuery</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>If specified, this will set the jQuery DOM.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#jq">module:bajaux/commands/CommandGroup#jq</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>A jQuery DOM object for firing events on.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">JQuery</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="loading"><span class="type-signature"></span>loading()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return promise that will be resolved once all the Commands in this<br>
group have been loaded.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#loading">module:bajaux/commands/CommandGroup#loading</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>A promise that will be resolved once all of the<br>
child Commands have loaded.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="merge"><span class="type-signature"></span>merge(group [, params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Merges an input command group with this one, and returns a new<br>
command group with the merged results.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>group</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_commands_CommandGroup.html">module:bajaux/commands/CommandGroup</a></span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>The group to merge with<br>
this one</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last">
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>mergeCommands</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    true
                
                </td>
            

            <td class="description last"><p>set to false to cause commands<br>
to be merged by a simple instanceof check, and all child commands of this<br>
group will be included; otherwise, only commands whose <code>merge()</code> function<br>
returns a new Command will be included</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#merge">module:bajaux/commands/CommandGroup#merge</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>the merged group, or<br>
null if the merge could not be completed</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_commands_CommandGroup.html">module:bajaux/commands/CommandGroup</a></span>
|

<span class="param-type">null</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="remove"><span class="type-signature"></span>remove(command)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Remove a command from this group. Triggers a<br>
<code>bajaux:changecommandgroup</code> event.<br>
Multiple arguments<br>
can be specified so multiple commands can be removed at once.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>command</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_commands_Command.html">module:bajaux/commands/Command</a></span>
|

<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>The command or index<br>
of the Command to remove.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#remove">module:bajaux/commands/CommandGroup#remove</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>Return the CommandGroup instance.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_commands_CommandGroup.html">module:bajaux/commands/CommandGroup</a></span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
    <pre class="sunlight-highlight-javascript">// Passing an id/index (Number)
 commandGroup.remove(0); // Removes the 0 index kid from the group
 // Passing a command (module:bajaux/commands/Command)
 commandGroup.remove(command); // Removes the matching &#x27;command&#x27; from the group
// Passing multiple commands (module:bajaux/commands/Command)
 commandGroup.remove(command1, command2); // Removes the matching commands from the group </pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="removeAll"><span class="type-signature"></span>removeAll(options)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Remove all children of this command group that match the input flag,<br>
optionally emptying out child groups as well. Triggers a<br>
<code>bajaux:changecommandgroup</code> event.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>An options object</p>
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>flags</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>Flags to use to filter commands. Only<br>
children that match one of these flags will be removed.<br>
If omitted, defaults to <code>Command.flags.ALL</code> meaning all<br>
children will be removed.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>recurse</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p><code>true</code> if should also empty out any<br>
sub-groups.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#removeAll">module:bajaux/commands/CommandGroup#removeAll</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
        <h5>Example</h5>
        
    <pre class="sunlight-highlight-javascript">cmdGroup.removeAll();
  cmdGroup.removeAll(Command.flags.MENU_BAR);
  cmdGroup.removeAll({ flags: Command.flags.MENU_BAR, recurse: true });</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setChildren"><span class="type-signature"></span>setChildren(children)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Sets this group's array of commands/groups wholesale. Triggers a<br>
<code>bajaux:changecommandgroup</code> event.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>children</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#setChildren">module:bajaux/commands/CommandGroup#setChildren</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setDisplayNameFormat"><span class="type-signature"></span>setDisplayNameFormat(displayName)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set the display name format of the command group. Triggers a<br>
<code>bajaux:changecommandgroup</code> event.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>displayName</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>display name - supports baja Format syntax</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#setDisplayNameFormat">module:bajaux/commands/CommandGroup#setDisplayNameFormat</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setFlags"><span class="type-signature"></span>setFlags(flags)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Sets this group's flags. Triggers a <code>bajaux:changecommandgroup</code> event.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>flags</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#setFlags">module:bajaux/commands/CommandGroup#setFlags</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setIcon"><span class="type-signature"></span>setIcon(icon)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Sets the icon for this CommandGroup. Triggers a <code>bajaux:changecommandgroup</code> event.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>icon</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>The CommandButton's icon (either a URI or a module:// ORD string)</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.12</li>
		</ul>
	</dd>
	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#setIcon">module:bajaux/commands/CommandGroup#setIcon</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="size"><span class="type-signature"></span>size()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the number of children the CommandGroup has<br>
(this covers both Commands and CommandGroups).</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#size">module:bajaux/commands/CommandGroup#size</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="sort"><span class="type-signature"></span>sort()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Sorts this command group. Typically will not be overridden; override<br>
<code>doSort</code> instead. Triggers a <code>bajaux:changecommandgroup</code> event.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#sort">module:bajaux/commands/CommandGroup#sort</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>A promise that will be invoked once the CommandGroup<br>
has been sorted.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="toDisplayName"><span class="type-signature"></span>toDisplayName()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Formats the command's display name.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#toDisplayName">module:bajaux/commands/CommandGroup#toDisplayName</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>Promise to be resolved with the display name</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise.&lt;String></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="trigger"><span class="type-signature"></span>trigger(name)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Triggers an event from this CommandGroup.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#trigger">module:bajaux/commands/CommandGroup#trigger</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="visit"><span class="type-signature"></span>visit(func)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Visit through all of the Commands and CommandGroups.</p>
<p>The function passed it will be called for every Command and CommandGroup<br>
found (including this CommandGroup).</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>func</code></td>
            

            <td class="type">
            
            </td>

            

            

            <td class="description last"><p>Called for every CommandGroup and Command found. Each<br>
Command/CommandGroup is passed in as the first argument to the function.<br>
If the function returns a false value then iteration stops.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-bajaux_commands_CommandGroup.html#visit">module:bajaux/commands/CommandGroup#visit</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        </dl>
    

    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	bajaux Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:53+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>