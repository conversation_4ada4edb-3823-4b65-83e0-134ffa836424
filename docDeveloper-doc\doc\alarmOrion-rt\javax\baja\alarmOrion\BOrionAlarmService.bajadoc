<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarmOrion" runtimeProfile="rt" qualifiedName="javax.baja.alarmOrion.BOrionAlarmService" name="BOrionAlarmService" packageName="javax.baja.alarmOrion" public="true">
<description>
An Orion based implementation of an alarm service.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">March 18, 2009</tag>
<extends>
<type class="javax.baja.alarm.BAlarmService"/>
</extends>
<implements>
<type class="com.tridium.orion.BIOrionApp"/>
</implements>
<property name="database" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;database&lt;/code&gt; property.&#xa; The ord to the database used by this application.
</description>
<tag name="@see">#getDatabase</tag>
<tag name="@see">#setDatabase</tag>
</property>

<!-- javax.baja.alarmOrion.BOrionAlarmService() -->
<constructor name="BOrionAlarmService" public="true">
<description/>
</constructor>

<!-- javax.baja.alarmOrion.BOrionAlarmService.getDatabase() -->
<method name="getDatabase"  public="true">
<description>
Get the &lt;code&gt;database&lt;/code&gt; property.&#xa; The ord to the database used by this application.
</description>
<tag name="@see">#database</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmService.setDatabase(javax.baja.naming.BOrd) -->
<method name="setDatabase"  public="true">
<description>
Set the &lt;code&gt;database&lt;/code&gt; property.&#xa; The ord to the database used by this application.
</description>
<tag name="@see">#database</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmService.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmService.createAlarmDb() -->
<method name="createAlarmDb"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.alarm.BAlarmDatabase"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmService.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="prop">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmService.serviceStarted() -->
<method name="serviceStarted"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmService.orionReady(com.tridium.orion.BOrionDatabase) -->
<method name="orionReady"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="db">
<type class="com.tridium.orion.BOrionDatabase"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmService.getOrionTypes() -->
<method name="getOrionTypes"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="com.tridium.orion.OrionType" dimension="1"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmService.getSchemaVersion() -->
<method name="getSchemaVersion"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="com.tridium.orion.BSchemaVersion"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmService.performSchemaUpgrade(com.tridium.orion.BLocalOrionDatabase, com.tridium.orion.BSchemaVersion) -->
<method name="performSchemaUpgrade"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="db">
<type class="com.tridium.orion.BLocalOrionDatabase"/>
</parameter>
<parameter name="oldVersion">
<type class="com.tridium.orion.BSchemaVersion"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmService.getServiceTypes() -->
<method name="getServiceTypes"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type" dimension="1"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmService.getApplicationIds() -->
<method name="getApplicationIds"  public="true">
<description>
The OrionAlarmService should not go into stations by default.
</description>
<return>
<type class="java.lang.String" dimension="1"/>
<description>
an empty list.
</description>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmService.status -->
<field name="status"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmService.faultCause -->
<field name="faultCause"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmService.database -->
<field name="database"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;database&lt;/code&gt; property.&#xa; The ord to the database used by this application.
</description>
<tag name="@see">#getDatabase</tag>
<tag name="@see">#setDatabase</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmService.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
