<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BBacnetProgram" name="BBacnetProgram" packageName="javax.baja.bacnet.config" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">17 Oct 2005</tag>
<tag name="@since">Niagara 3.1</tag>
<extends>
<type class="javax.baja.bacnet.BBacnetObject"/>
</extends>
<property name="programState" flags="tr">
<type class="javax.baja.bacnet.enums.BBacnetProgramState"/>
<description>
Slot for the &lt;code&gt;programState&lt;/code&gt; property.
</description>
<tag name="@see">#getProgramState</tag>
<tag name="@see">#setProgramState</tag>
</property>

<property name="programChange" flags="">
<type class="javax.baja.bacnet.enums.BBacnetProgramRequest"/>
<description>
Slot for the &lt;code&gt;programChange&lt;/code&gt; property.
</description>
<tag name="@see">#getProgramChange</tag>
<tag name="@see">#setProgramChange</tag>
</property>

<property name="statusFlags" flags="r">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
Slot for the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#getStatusFlags</tag>
<tag name="@see">#setStatusFlags</tag>
</property>

<property name="outOfService" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#getOutOfService</tag>
<tag name="@see">#setOutOfService</tag>
</property>

<!-- javax.baja.bacnet.config.BBacnetProgram() -->
<constructor name="BBacnetProgram" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetProgram.getProgramState() -->
<method name="getProgramState"  public="true">
<description>
Get the &lt;code&gt;programState&lt;/code&gt; property.
</description>
<tag name="@see">#programState</tag>
<return>
<type class="javax.baja.bacnet.enums.BBacnetProgramState"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetProgram.setProgramState(javax.baja.bacnet.enums.BBacnetProgramState) -->
<method name="setProgramState"  public="true">
<description>
Set the &lt;code&gt;programState&lt;/code&gt; property.
</description>
<tag name="@see">#programState</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.BBacnetProgramState"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetProgram.getProgramChange() -->
<method name="getProgramChange"  public="true">
<description>
Get the &lt;code&gt;programChange&lt;/code&gt; property.
</description>
<tag name="@see">#programChange</tag>
<return>
<type class="javax.baja.bacnet.enums.BBacnetProgramRequest"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetProgram.setProgramChange(javax.baja.bacnet.enums.BBacnetProgramRequest) -->
<method name="setProgramChange"  public="true">
<description>
Set the &lt;code&gt;programChange&lt;/code&gt; property.
</description>
<tag name="@see">#programChange</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.BBacnetProgramRequest"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetProgram.getStatusFlags() -->
<method name="getStatusFlags"  public="true">
<description>
Get the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#statusFlags</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetProgram.setStatusFlags(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="setStatusFlags"  public="true">
<description>
Set the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#statusFlags</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetProgram.getOutOfService() -->
<method name="getOutOfService"  public="true">
<description>
Get the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#outOfService</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetProgram.setOutOfService(boolean) -->
<method name="setOutOfService"  public="true">
<description>
Set the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#outOfService</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetProgram.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetProgram.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetProgram.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetProgram.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetProgram.programState -->
<field name="programState"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;programState&lt;/code&gt; property.
</description>
<tag name="@see">#getProgramState</tag>
<tag name="@see">#setProgramState</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetProgram.programChange -->
<field name="programChange"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;programChange&lt;/code&gt; property.
</description>
<tag name="@see">#getProgramChange</tag>
<tag name="@see">#setProgramChange</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetProgram.statusFlags -->
<field name="statusFlags"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#getStatusFlags</tag>
<tag name="@see">#setStatusFlags</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetProgram.outOfService -->
<field name="outOfService"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#getOutOfService</tag>
<tag name="@see">#setOutOfService</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetProgram.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
