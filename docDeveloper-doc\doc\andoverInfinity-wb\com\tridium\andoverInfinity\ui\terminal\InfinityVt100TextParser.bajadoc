<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="wb" qualifiedName="com.tridium.andoverInfinity.ui.terminal.InfinityVt100TextParser" name="InfinityVt100TextParser" packageName="com.tridium.andoverInfinity.ui.terminal" public="true">
<description>
InfinityVt100TextParser&#xa; &#xa; Custom parser for VT100 manager view.  An array of strings of text content&#xa; for the TextEditor and an array of strings for text format are parsed into&#xa; segments and packaged into a Line[] that the TextEditor can then display in&#xa; multiple colors.  The VT100 attributes of Normal, Bold, Reverse, and Graphics&#xa; are displayed as diffenent color text in this view.
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 22, 2007</tag>
<tag name="@version">$Revision$ $May 22, 2007 10:13:56 AM$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="javax.baja.ui.text.TextParser"/>
</extends>
<!-- com.tridium.andoverInfinity.ui.terminal.InfinityVt100TextParser() -->
<constructor name="InfinityVt100TextParser" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.ui.terminal.InfinityVt100TextParser.parse(java.lang.String[], java.lang.String[]) -->
<method name="parse"  public="true" synchronized="true">
<description>
Parse an array of content lines and and an array of format lines into an &#xa; array of &lt;code&gt;Line&lt;/code&gt;s that can be displayed by the model.
</description>
<parameter name="textLines">
<type class="java.lang.String" dimension="1"/>
<description>
from a VT100 screen buffer
</description>
</parameter>
<parameter name="formatLines">
<type class="java.lang.String" dimension="1"/>
<description>
from a VT100 screen buffer
</description>
</parameter>
<return>
<type class="javax.baja.ui.text.Line" dimension="1"/>
<description>
an array of &lt;code&gt;Line&lt;/code&gt;
</description>
</return>
</method>

</class>
</bajadoc>
