<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.point.BBacnetPointFolder" name="BBacnetPointFolder" packageName="javax.baja.bacnet.point" public="true">
<description>
BBacnetPointFolder.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">30 Jun 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.driver.point.BPointFolder"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BIBacnetObjectContainer"/>
</implements>
<!-- javax.baja.bacnet.point.BBacnetPointFolder() -->
<constructor name="BBacnetPointFolder" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.point.BBacnetPointFolder.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointFolder.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<description>
BBacnetPointFolder may only be placed under a BBacnetPointDeviceExt or BBacnetPointFolder.
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointFolder.lookupBacnetObject(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int, java.lang.String) -->
<method name="lookupBacnetObject"  public="true">
<description/>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="propertyId">
<type class="int"/>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
</parameter>
<parameter name="domain">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointFolder.device() -->
<method name="device"  public="true" final="true">
<description/>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
<description>
the BBacnetDevice containing this BBacnetPointDeviceExt.
</description>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointFolder.findPoint(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int) -->
<method name="findPoint"  public="true" final="true">
<description>
Find a proxy point based on objectId, propertyId,&#xa; and propertyArrayIndex.
</description>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<parameter name="propertyId">
<type class="int"/>
<description/>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
<description/>
</parameter>
<return>
<type class="javax.baja.control.BControlPoint"/>
<description>
the BControlPoint in this point device extension that has a&#xa; BBacnetProxyExt with these parameters, or null if no&#xa; point has these parameters.
</description>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointFolder.findPoints(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="findPoints"  public="true" final="true">
<description>
Find all proxy points with a given objectId.
</description>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<return>
<type class="javax.baja.control.BControlPoint" dimension="1"/>
<description>
an array of BControlPoints in this point device extension that have a&#xa; BBacnetProxyExt with this objectId, or null if no&#xa; point has these parameters.
</description>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointFolder.getAgents(javax.baja.sys.Context) -->
<method name="getAgents"  public="true">
<description>
Get the agent list.  Remove Device Manager and Network Summary.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.agent.AgentList"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointFolder.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointFolder.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
