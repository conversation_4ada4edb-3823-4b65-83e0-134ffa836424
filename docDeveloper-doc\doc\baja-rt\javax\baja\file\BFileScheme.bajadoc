<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BFileScheme" name="BFileScheme" packageName="javax.baja.file" public="true">
<description>
BFileScheme manages the &#x22;file&#x22; scheme with a FilePath query.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">4 Jan 03</tag>
<tag name="@version">$Revision: 7$ $Date: 12/17/09 1:13:31 PM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.space.BSpaceScheme"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraSingleton"/>
</annotation>
<!-- javax.baja.file.BFileScheme(java.lang.String) -->
<constructor name="BFileScheme" protected="true">
<parameter name="schemeId">
<type class="java.lang.String"/>
</parameter>
<description>
Private constructor.
</description>
</constructor>

<!-- javax.baja.file.BFileScheme.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.file.BFileScheme.parse(java.lang.String) -->
<method name="parse"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return an instance of FilePath.
</description>
<parameter name="queryBody">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdQuery"/>
</return>
</method>

<!-- javax.baja.file.BFileScheme.getSpaceType() -->
<method name="getSpaceType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return BFileSpace.TYPE.
</description>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.file.BFileScheme.resolve(javax.baja.naming.OrdTarget, javax.baja.naming.OrdQuery) -->
<method name="resolve"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
See documentation in class header for how resolve works.
</description>
<parameter name="base">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<parameter name="query">
<type class="javax.baja.naming.OrdQuery"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdTarget"/>
</return>
<throws>
<type class="javax.baja.naming.SyntaxException"/>
</throws>
<throws>
<type class="javax.baja.naming.UnresolvedException"/>
</throws>
</method>

<!-- javax.baja.file.BFileScheme.resolve(javax.baja.naming.OrdTarget, javax.baja.naming.OrdQuery, javax.baja.space.BSpace) -->
<method name="resolve"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Map to &lt;code&gt;BFileSpace.resolveFile(FilePath)&lt;/code&gt;
</description>
<parameter name="base">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<parameter name="query">
<type class="javax.baja.naming.OrdQuery"/>
</parameter>
<parameter name="space">
<type class="javax.baja.space.BSpace"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdTarget"/>
</return>
</method>

<!-- javax.baja.file.BFileScheme.localizeStationPath(javax.baja.naming.BOrd, javax.baja.file.FilePath) -->
<method name="localizeStationPath"  public="true" static="true">
<description/>
<parameter name="baseOrd">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.FilePath"/>
</return>
</method>

<!-- javax.baja.file.BFileScheme.INSTANCE -->
<field name="INSTANCE"  public="true" static="true" final="true">
<type class="javax.baja.file.BFileScheme"/>
<description/>
</field>

<!-- javax.baja.file.BFileScheme.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
