<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.inbound">
<description/>
<class packageName="com.tridiumx.jsonToolkit.inbound" name="BJsonInbound"><description>Base class for all incoming json routers/handlers/selectors etc</description></class>
</package>
</bajadoc>
