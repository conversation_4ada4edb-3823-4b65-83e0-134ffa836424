<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.BInfinityNetworkDevice" name="BInfinityNetworkDevice" packageName="com.tridium.andoverInfinity" public="true">
<description>
This class models the &#x22;top-level&#x22; device in a network of Infinity devices.&#xa; The &#x22;network device&#x22; is the device that the RS-232 cable connects to.  This&#xa; class adds properties to the base BInfinetDevice class to store login &#xa; parameters.&#xa;&#xa; A BInfinityNetworkDevice is included here as a frozen slot on the &#xa; BInfinityNetwork. &#xa; &#xa; BInfinityNetworkDevice typically models a CMX240,&#xa; Continuum NetController, CX9400, etc.
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.andoverInfinity.BInfinetDevice"/>
</extends>
<property name="userName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;userName&lt;/code&gt; property.
</description>
<tag name="@see">#getUserName</tag>
<tag name="@see">#setUserName</tag>
</property>

<property name="password" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;password&lt;/code&gt; property.
</description>
<tag name="@see">#getPassword</tag>
<tag name="@see">#setPassword</tag>
</property>

<!-- com.tridium.andoverInfinity.BInfinityNetworkDevice() -->
<constructor name="BInfinityNetworkDevice" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.BInfinityNetworkDevice.getUserName() -->
<method name="getUserName"  public="true">
<description>
Get the &lt;code&gt;userName&lt;/code&gt; property.
</description>
<tag name="@see">#userName</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.BInfinityNetworkDevice.setUserName(java.lang.String) -->
<method name="setUserName"  public="true">
<description>
Set the &lt;code&gt;userName&lt;/code&gt; property.
</description>
<tag name="@see">#userName</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.BInfinityNetworkDevice.getPassword() -->
<method name="getPassword"  public="true">
<description>
Get the &lt;code&gt;password&lt;/code&gt; property.
</description>
<tag name="@see">#password</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.BInfinityNetworkDevice.setPassword(java.lang.String) -->
<method name="setPassword"  public="true">
<description>
Set the &lt;code&gt;password&lt;/code&gt; property.
</description>
<tag name="@see">#password</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.BInfinityNetworkDevice.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.BInfinityNetworkDevice.pingOk() -->
<method name="pingOk"  public="true">
<description>
The pingOk() method inherited from BDevice is overridden here in order to&#xa; perform extra tasks whenever the transistion from off-line to on-line&#xa; occurs.  For Infinity devices, a time sync is sent on every transition to on-line, &#xa; and the network is also pingOk&#x27;d.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.BInfinityNetworkDevice.pingFail(java.lang.String) -->
<method name="pingFail"  public="true">
<description>
The pingFail() method inherited from BDevice is overridden here in order to&#xa; perform extra tasks whenever the transition from on-line to off-line&#xa; occurs.  For infinity, the network is also ping fail&#x27;d.
</description>
<tag name="@see">javax.baja.driver.BDevice#pingFail(java.lang.String)</tag>
<parameter name="cause">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.BInfinityNetworkDevice.userName -->
<field name="userName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;userName&lt;/code&gt; property.
</description>
<tag name="@see">#getUserName</tag>
<tag name="@see">#setUserName</tag>
</field>

<!-- com.tridium.andoverInfinity.BInfinityNetworkDevice.password -->
<field name="password"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;password&lt;/code&gt; property.
</description>
<tag name="@see">#getPassword</tag>
<tag name="@see">#setPassword</tag>
</field>

<!-- com.tridium.andoverInfinity.BInfinityNetworkDevice.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
