<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.outbound.schema.config.folder">
<description/>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.config.folder" name="BJsonSchemaConfigFolder"><description>Root location for Schema config props</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.config.folder" name="BJsonSchemaDebugFolder"><description>Contains metrics and historical debug tools</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.config.folder" name="BJsonSchemaOverrideFolder"><description>Contains json type overrides</description></class>
</package>
</bajadoc>
