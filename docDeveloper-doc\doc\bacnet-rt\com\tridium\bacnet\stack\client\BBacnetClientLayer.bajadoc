<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.client.BBacnetClientLayer" name="BBacnetClientLayer" packageName="com.tridium.bacnet.stack.client" public="true">
<description>
Tridium Client Application Layer Implementation.&#xa; &lt;p&gt;&#xa; BBacnetClientLayer represents the client side of the application&#xa; layer of the Bacnet communications stack.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">19 Apr 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<implements>
<type class="com.tridium.bacnet.stack.BacnetStackErrorCodes"/>
</implements>
</class>
</bajadoc>
