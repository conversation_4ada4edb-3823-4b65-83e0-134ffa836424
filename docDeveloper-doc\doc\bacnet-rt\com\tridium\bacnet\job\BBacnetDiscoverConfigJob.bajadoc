<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.job.BBacnetDiscoverConfigJob" name="BBacnetDiscoverConfigJob" packageName="com.tridium.bacnet.job" public="true">
<description>
BBacnetDiscoverConfigJob is the task that manages discovery of BACnet objects&#xa; for the Config Manager.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">01 Dec 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.job.BBacnetDiscoverJob"/>
</extends>
</class>
</bajadoc>
