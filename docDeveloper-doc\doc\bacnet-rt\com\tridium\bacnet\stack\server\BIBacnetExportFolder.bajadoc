<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.server.BIBacnetExportFolder" name="BIBacnetExportFolder" packageName="com.tridium.bacnet.stack.server" public="true" interface="true" abstract="true" category="interface">
<description>
BIBacnetExportFolder is the common interface for&#xa; BLocalBacnetDevice and BBacnetExportFolder.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">19 Nov 2004</tag>
<tag name="@since">Niagara 3 BACnet 1.0</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
</class>
</bajadoc>
