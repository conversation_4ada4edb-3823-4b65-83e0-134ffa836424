<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.access.BBacnetAssignedAccessRights" name="BBacnetAssignedAccessRights" packageName="javax.baja.bacnet.datatypes.access" public="true" final="true">
<description>
BBacnetAssignedAccessRights represents the BACnetAssignedAccessRights&#xa; sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="assignedAccessRights" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference"/>
<description>
Slot for the &lt;code&gt;assignedAccessRights&lt;/code&gt; property.
</description>
<tag name="@see">#getAssignedAccessRights</tag>
<tag name="@see">#setAssignedAccessRights</tag>
</property>

<property name="enable" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;enable&lt;/code&gt; property.
</description>
<tag name="@see">#getEnable</tag>
<tag name="@see">#setEnable</tag>
</property>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAssignedAccessRights() -->
<constructor name="BBacnetAssignedAccessRights" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAssignedAccessRights(javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference, boolean) -->
<constructor name="BBacnetAssignedAccessRights" public="true">
<parameter name="deviceObjectReference">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference"/>
</parameter>
<parameter name="enable">
<type class="boolean"/>
</parameter>
<description>
Standard constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAssignedAccessRights.getAssignedAccessRights() -->
<method name="getAssignedAccessRights"  public="true">
<description>
Get the &lt;code&gt;assignedAccessRights&lt;/code&gt; property.
</description>
<tag name="@see">#assignedAccessRights</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAssignedAccessRights.setAssignedAccessRights(javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference) -->
<method name="setAssignedAccessRights"  public="true">
<description>
Set the &lt;code&gt;assignedAccessRights&lt;/code&gt; property.
</description>
<tag name="@see">#assignedAccessRights</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAssignedAccessRights.getEnable() -->
<method name="getEnable"  public="true">
<description>
Get the &lt;code&gt;enable&lt;/code&gt; property.
</description>
<tag name="@see">#enable</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAssignedAccessRights.setEnable(boolean) -->
<method name="setEnable"  public="true">
<description>
Set the &lt;code&gt;enable&lt;/code&gt; property.
</description>
<tag name="@see">#enable</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAssignedAccessRights.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAssignedAccessRights.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAssignedAccessRights.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAssignedAccessRights.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAssignedAccessRights.assignedAccessRights -->
<field name="assignedAccessRights"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;assignedAccessRights&lt;/code&gt; property.
</description>
<tag name="@see">#getAssignedAccessRights</tag>
<tag name="@see">#setAssignedAccessRights</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAssignedAccessRights.enable -->
<field name="enable"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;enable&lt;/code&gt; property.
</description>
<tag name="@see">#getEnable</tag>
<tag name="@see">#setEnable</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAssignedAccessRights.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAssignedAccessRights.ASSIGNED_ACCESS_RIGHTS_TAG -->
<field name="ASSIGNED_ACCESS_RIGHTS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAssignedAccessRights.ENABLE_TAG -->
<field name="ENABLE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAssignedAccessRights.MAX_ENCODED_SIZE -->
<field name="MAX_ENCODED_SIZE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
