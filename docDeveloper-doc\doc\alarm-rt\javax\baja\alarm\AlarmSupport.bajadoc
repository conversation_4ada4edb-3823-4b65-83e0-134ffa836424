<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.AlarmSupport" name="AlarmSupport" packageName="javax.baja.alarm" public="true">
<description>
AlarmSupport is a support class to enabled easy alarm generation and alarm&#xa; handling for BIAlarmSources.&#xa; &lt;p&gt;&#xa; The BIAlarmSource should have a single instance of AlarmSupport.&#xa; newOffnormalAlarm() is used to generate new alarms. toNormal() should be called&#xa; when the source goes back to its normal condition. And ackAlarm should be&#xa; called from the doAckAlarm() method.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- javax.baja.alarm.AlarmSupport(javax.baja.alarm.BIAlarmSource, java.lang.String) -->
<constructor name="AlarmSupport" public="true">
<parameter name="comp">
<type class="javax.baja.alarm.BIAlarmSource"/>
</parameter>
<parameter name="alarmClassName">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.alarm.AlarmSupport(javax.baja.alarm.BIAlarmSource, javax.baja.alarm.BAlarmSourceInfo) -->
<constructor name="AlarmSupport" public="true">
<parameter name="comp">
<type class="javax.baja.alarm.BIAlarmSource"/>
</parameter>
<parameter name="info">
<type class="javax.baja.alarm.BAlarmSourceInfo"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.alarm.AlarmSupport.setAlarmClass(java.lang.String) -->
<method name="setAlarmClass"  public="true">
<description>
Set the AlarmClass for alarms generated by this object.
</description>
<parameter name="alarmClassName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.AlarmSupport.getSourceOrd() -->
<method name="getSourceOrd"  public="true">
<description/>
<return>
<type class="javax.baja.naming.BOrdList"/>
<description>
the ord for the source used in the AlarmRecord.
</description>
</return>
</method>

<!-- javax.baja.alarm.AlarmSupport.getAlarmClassName() -->
<method name="getAlarmClassName"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.alarm.AlarmSupport.isAckRequired(javax.baja.alarm.BSourceState) -->
<method name="isAckRequired"  public="true">
<description/>
<parameter name="trans">
<type class="javax.baja.alarm.BSourceState"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the AlarmClass requires an ack for the given transition.
</description>
</return>
</method>

<!-- javax.baja.alarm.AlarmSupport.newAlert() -->
<method name="newAlert"  public="true">
<description>
Generate an alert using alarmData from BAlarmSourceInfo.
</description>
<return>
<type class="javax.baja.alarm.BAlarmRecord"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.alarm.AlarmSupport.newAlert(javax.baja.sys.BFacets) -->
<method name="newAlert"  public="true">
<description>
An Alert is an alarm that has no toNormal transition.
</description>
<parameter name="alarmData">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmRecord"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.alarm.AlarmSupport.newOffnormalAlarm() -->
<method name="newOffnormalAlarm"  public="true">
<description>
Generate an offnormal alarm using alarmData from BAlarmSourceInfo..
</description>
<return>
<type class="javax.baja.alarm.BAlarmRecord"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.alarm.AlarmSupport.newOffnormalAlarm(javax.baja.sys.BFacets) -->
<method name="newOffnormalAlarm"  public="true">
<description>
Generate and route a new offnormal alarm.
</description>
<parameter name="alarmData">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmRecord"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.alarm.AlarmSupport.newFaultAlarm() -->
<method name="newFaultAlarm"  public="true">
<description>
Generate a fault alarm using alarmData from BAlarmSourceInfo.
</description>
<return>
<type class="javax.baja.alarm.BAlarmRecord"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.alarm.AlarmSupport.newFaultAlarm(javax.baja.sys.BFacets) -->
<method name="newFaultAlarm"  public="true">
<description>
Generate and route a new fault alarm.
</description>
<parameter name="alarmData">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmRecord"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.alarm.AlarmSupport.newAlarm(javax.baja.alarm.BSourceState, javax.baja.sys.BFacets) -->
<method name="newAlarm"  protected="true">
<description>
Generate a new Alarm based on the AlarmState.
</description>
<parameter name="state">
<type class="javax.baja.alarm.BSourceState"/>
</parameter>
<parameter name="alarmData">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmRecord"/>
<description>
BAlarmRecord that was sent to AlarmService.
</description>
</return>
</method>

<!-- javax.baja.alarm.AlarmSupport.toNormal(javax.baja.sys.Context) -->
<method name="toNormal"  public="true" synchronized="true">
<description>
Generate an toNormal notification using alarmData from BAlarmSourceInfo.&#xa; This method makes use of the BAlarmDatabase.toNormal method when needed,&#xa; allowing for potential optimization.&#xa; &lt;p&gt;&#xa; This method generates the alarm transition asynchronously to the current thread.
</description>
<tag name="@since">Niagara 3.6</tag>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
unused
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.alarm.AlarmSupport.toNormal(javax.baja.sys.BFacets, javax.baja.sys.Context) -->
<method name="toNormal"  public="true" synchronized="true">
<description>
Generate and route toNormal notifications for all current offnormal and&#xa; fault alarms. This method makes use of the BAlarmDatabase.toNormal method&#xa; when needed, allowing for potential optimization.&#xa; &lt;p&gt;&#xa; This method generates the alarm transition asynchronously to the current thread.
</description>
<tag name="@since">Niagara 3.6</tag>
<parameter name="newFacets">
<type class="javax.baja.sys.BFacets"/>
<description>
to add the BAlarmRecord
</description>
</parameter>
<parameter name="ignored">
<type class="javax.baja.sys.Context"/>
<description>
unused
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.alarm.AlarmSupport.ackAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="ackAlarm"  public="true">
<description/>
<parameter name="alarm">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if alarm acked (clear ack bit in status), false if stale ack
</description>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.alarm.AlarmSupport.isValidNormalAck(javax.baja.alarm.BAlarmRecord) -->
<method name="isValidNormalAck"  public="true">
<description/>
<parameter name="ackRequest">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.AlarmSupport.getLastNormal() -->
<method name="getLastNormal"  public="true">
<description>
Return the most recently generated normal alarm,&#xa; null if no normal alarms have been generated since station start.
</description>
<tag name="@since">Niagara 4.10u5</tag>
<tag name="@since">Niagara 4.12u2</tag>
<tag name="@since">Niagara 4.13</tag>
<return>
<type class="javax.baja.alarm.BAlarmRecord"/>
</return>
</method>

<!-- javax.baja.alarm.AlarmSupport.getLastOffnormal() -->
<method name="getLastOffnormal"  public="true">
<description>
Return the most recently generated offnormal alarm,&#xa; null if no offnormal alarms have been generated since station start.
</description>
<return>
<type class="javax.baja.alarm.BAlarmRecord"/>
</return>
</method>

<!-- javax.baja.alarm.AlarmSupport.getLastFault() -->
<method name="getLastFault"  public="true">
<description>
Return the most recently generated fault alarm,&#xa; null if no fault alarms have been generated since station start.
</description>
<return>
<type class="javax.baja.alarm.BAlarmRecord"/>
</return>
</method>

<!-- javax.baja.alarm.AlarmSupport.getLastAlert() -->
<method name="getLastAlert"  public="true">
<description>
Return the most recently generated alert,&#xa; null if no alert have been generated since station start.
</description>
<return>
<type class="javax.baja.alarm.BAlarmRecord"/>
</return>
</method>

<!-- javax.baja.alarm.AlarmSupport.USER_OVERRIDE_FACET -->
<field name="USER_OVERRIDE_FACET"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

</class>
</bajadoc>
