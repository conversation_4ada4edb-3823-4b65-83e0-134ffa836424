<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="javax.baja.bacnetAws.datatypes.BBacnetShedLevel" name="BBacnetShedLevel" packageName="javax.baja.bacnetAws.datatypes" public="true" final="true">
<description>
BBacnetScale represents the BACnetScale data type.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">May 24, 2010</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.6 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="choice" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</property>

<property name="percent" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;percent&lt;/code&gt; property.
</description>
<tag name="@see">#getPercent</tag>
<tag name="@see">#setPercent</tag>
</property>

<property name="level" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;level&lt;/code&gt; property.
</description>
<tag name="@see">#getLevel</tag>
<tag name="@see">#setLevel</tag>
</property>

<property name="amount" flags="">
<type class="float"/>
<description>
Slot for the &lt;code&gt;amount&lt;/code&gt; property.
</description>
<tag name="@see">#getAmount</tag>
<tag name="@see">#setAmount</tag>
</property>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel() -->
<constructor name="BBacnetShedLevel" public="true">
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.getChoice() -->
<method name="getChoice"  public="true">
<description>
Get the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.setChoice(int) -->
<method name="setChoice"  public="true">
<description>
Set the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.getPercent() -->
<method name="getPercent"  public="true">
<description>
Get the &lt;code&gt;percent&lt;/code&gt; property.
</description>
<tag name="@see">#percent</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.setPercent(int) -->
<method name="setPercent"  public="true">
<description>
Set the &lt;code&gt;percent&lt;/code&gt; property.
</description>
<tag name="@see">#percent</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.getLevel() -->
<method name="getLevel"  public="true">
<description>
Get the &lt;code&gt;level&lt;/code&gt; property.
</description>
<tag name="@see">#level</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.setLevel(int) -->
<method name="setLevel"  public="true">
<description>
Set the &lt;code&gt;level&lt;/code&gt; property.
</description>
<tag name="@see">#level</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.getAmount() -->
<method name="getAmount"  public="true">
<description>
Get the &lt;code&gt;amount&lt;/code&gt; property.
</description>
<tag name="@see">#amount</tag>
<return>
<type class="float"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.setAmount(float) -->
<method name="setAmount"  public="true">
<description>
Set the &lt;code&gt;amount&lt;/code&gt; property.
</description>
<tag name="@see">#amount</tag>
<parameter name="v">
<type class="float"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.CHOICE -->
<field name="CHOICE"  public="true" static="true" final="true">
<type class="javax.baja.sys.BEnumRange"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.choice -->
<field name="choice"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.percent -->
<field name="percent"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;percent&lt;/code&gt; property.
</description>
<tag name="@see">#getPercent</tag>
<tag name="@see">#setPercent</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.level -->
<field name="level"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;level&lt;/code&gt; property.
</description>
<tag name="@see">#getLevel</tag>
<tag name="@see">#setLevel</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.amount -->
<field name="amount"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;amount&lt;/code&gt; property.
</description>
<tag name="@see">#getAmount</tag>
<tag name="@see">#setAmount</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.PERCENT_TAG -->
<field name="PERCENT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.LEVEL_TAG -->
<field name="LEVEL_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetShedLevel.AMOUNT_TAG -->
<field name="AMOUNT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
