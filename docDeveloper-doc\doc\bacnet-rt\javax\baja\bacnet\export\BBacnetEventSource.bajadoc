<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetEventSource" name="BBacnetEventSource" packageName="javax.baja.bacnet.export" public="true" abstract="true">
<description>
BBacnetEventSource is the base class for all BACnet export descriptors&#xa; that represent event-initiating BACnet objects.  A particular descriptor&#xa; class will implement the class regardless of whether the specific&#xa; object which it exposes actually supports alarming.  If the object&#xa; is not configured for alarming, the event source will simply return&#xa; null for the requested values.&#xa; &lt;p&gt;&#xa; This interface is used by the server layer to respond properly to&#xa; GetEventInformation requests.
</description>
<tag name="@author"><PERSON> on 11 Aug 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.export.BIBacnetExportObject"/>
</implements>
<property name="status" flags="trd">
<type class="javax.baja.status.BStatus"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.&#xa; the status for Niagara server-side behavior.
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</property>

<property name="faultCause" flags="rt">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.&#xa; Provides a description of a fault with server-side behavior.
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</property>

<property name="eventDetectionEnable" flags="sd">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;eventDetectionEnable&lt;/code&gt; property.&#xa; indicates whether (TRUE) or not (FALSE) intrinsic/algorithmic reporting is enabled
</description>
<tag name="@see">#getEventDetectionEnable</tag>
<tag name="@see">#setEventDetectionEnable</tag>
</property>

<property name="dynamicallyCreated" flags="sdhr">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;dynamicallyCreated&lt;/code&gt; property.&#xa; indicates whether (TRUE) or not (FALSE) the object was dynamically created.
</description>
<tag name="@see">#getDynamicallyCreated</tag>
<tag name="@see">#setDynamicallyCreated</tag>
</property>

<property name="bacnetStatusFlags" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
Slot for the &lt;code&gt;bacnetStatusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#getBacnetStatusFlags</tag>
<tag name="@see">#setBacnetStatusFlags</tag>
</property>

<!-- javax.baja.bacnet.export.BBacnetEventSource() -->
<constructor name="BBacnetEventSource" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetEventSource.getStatus() -->
<method name="getStatus"  public="true">
<description>
Get the &lt;code&gt;status&lt;/code&gt; property.&#xa; the status for Niagara server-side behavior.
</description>
<tag name="@see">#status</tag>
<return>
<type class="javax.baja.status.BStatus"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.setStatus(javax.baja.status.BStatus) -->
<method name="setStatus"  public="true">
<description>
Set the &lt;code&gt;status&lt;/code&gt; property.&#xa; the status for Niagara server-side behavior.
</description>
<tag name="@see">#status</tag>
<parameter name="v">
<type class="javax.baja.status.BStatus"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.getFaultCause() -->
<method name="getFaultCause"  public="true">
<description>
Get the &lt;code&gt;faultCause&lt;/code&gt; property.&#xa; Provides a description of a fault with server-side behavior.
</description>
<tag name="@see">#faultCause</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.setFaultCause(java.lang.String) -->
<method name="setFaultCause"  public="true">
<description>
Set the &lt;code&gt;faultCause&lt;/code&gt; property.&#xa; Provides a description of a fault with server-side behavior.
</description>
<tag name="@see">#faultCause</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.getEventDetectionEnable() -->
<method name="getEventDetectionEnable"  public="true">
<description>
Get the &lt;code&gt;eventDetectionEnable&lt;/code&gt; property.&#xa; indicates whether (TRUE) or not (FALSE) intrinsic/algorithmic reporting is enabled
</description>
<tag name="@see">#eventDetectionEnable</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.setEventDetectionEnable(boolean) -->
<method name="setEventDetectionEnable"  public="true">
<description>
Set the &lt;code&gt;eventDetectionEnable&lt;/code&gt; property.&#xa; indicates whether (TRUE) or not (FALSE) intrinsic/algorithmic reporting is enabled
</description>
<tag name="@see">#eventDetectionEnable</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.getDynamicallyCreated() -->
<method name="getDynamicallyCreated"  public="true">
<description>
Get the &lt;code&gt;dynamicallyCreated&lt;/code&gt; property.&#xa; indicates whether (TRUE) or not (FALSE) the object was dynamically created.
</description>
<tag name="@see">#dynamicallyCreated</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.setDynamicallyCreated(boolean) -->
<method name="setDynamicallyCreated"  public="true">
<description>
Set the &lt;code&gt;dynamicallyCreated&lt;/code&gt; property.&#xa; indicates whether (TRUE) or not (FALSE) the object was dynamically created.
</description>
<tag name="@see">#dynamicallyCreated</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.getBacnetStatusFlags() -->
<method name="getBacnetStatusFlags"  public="true">
<description>
Get the &lt;code&gt;bacnetStatusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#bacnetStatusFlags</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.setBacnetStatusFlags(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="setBacnetStatusFlags"  public="true">
<description>
Set the &lt;code&gt;bacnetStatusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#bacnetStatusFlags</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.started() -->
<method name="started"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Started.&#xa; Initialize the point name subscriber and check the export configuration.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.bacnetStatusFlagChanged() -->
<method name="bacnetStatusFlagChanged"  protected="true">
<description>
When &#x27;bacnet Status flag&#x27; property is changed, if BBacnetStatusAlarm ext is added to the point,&#xa; notify the extension about the property changed.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.getSvo() -->
<method name="getSvo"  protected="true">
<description>
Get the containing export folder if any.
</description>
<return>
<type class="com.tridium.bacnet.stack.server.BBacnetExportFolder"/>
<description>
bacnetExportFolder
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.isValidAlarmExt(javax.baja.alarm.BIAlarmSource) -->
<method name="isValidAlarmExt"  public="true" abstract="true">
<description>
Is the given alarm source ext a valid extension for&#xa; exporting BACnet alarm properties?  This determines if the&#xa; given alarm source extension follows the appropriate algorithm&#xa; defined for the intrinsic alarming of a particular object&#xa; type as required by the BACnet specification.
</description>
<parameter name="ext">
<type class="javax.baja.alarm.BIAlarmSource"/>
<description/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if valid, otherwise false.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.updateAlarmInhibit() -->
<method name="updateAlarmInhibit"  protected="true" abstract="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
inhibit the event notification based on eventDetectionEnable flag
</description>
<tag name="@deprecated">since Niagara 4.10u2: this method is no longer; the alarmInhibit property of a&#xa; BAlarmSourceExt should not be based on the eventDetectionEnable property but on the&#xa; eventAlgorithmInhibit and/or eventAlgorithmInhibitRef properties.</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.isEventInitiationEnabled() -->
<method name="isEventInitiationEnabled"  public="true" abstract="true">
<description>
Is this object currently configured to support event initiation?&#xa; This will return false if the exported object does not have an&#xa; appropriate alarm extension configured to allow Bacnet event initiation.
</description>
<return>
<type class="boolean"/>
<description>
true if this object can initiate Bacnet events.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.getObjectId() -->
<method name="getObjectId"  public="true" abstract="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the object identifier.
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the objectId, or null if event initiation is not enabled.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.getEventState() -->
<method name="getEventState"  public="true" abstract="true">
<description>
Get the current Event_State of the object.&#xa; If this object does not support event initiation,&#xa; this will return null.&#xa; If the object supports event initiation, this will return&#xa; an appropriate event state, either as a &lt;code&gt;BBacnetEventState&lt;/code&gt;,&#xa; or as a BDynamicEnum if it uses additionally defined event states.
</description>
<return>
<type class="javax.baja.sys.BEnum"/>
<description>
the object&#x27;s event state, or null.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.getPoint() -->
<method name="getPoint"  public="true" abstract="true">
<description>
Get the point the descriptor is pointing to.&#xa; If the Descriptor doesn&#x27;t have point reference, then return null
</description>
<return>
<type class="javax.baja.control.BControlPoint"/>
<description>
the control point
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.getAckedTransitions() -->
<method name="getAckedTransitions"  public="true" abstract="true">
<description>
Get the current Acked_Transitions property of the object.&#xa; If this object does not support event initiation,&#xa; this will return null.&#xa; If the object supports event initiation, this will return&#xa; a bit string representing the currently acknowledged transitions.
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
the object&#x27;s acknowledged transitions, or null.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.getEventTimeStamps() -->
<method name="getEventTimeStamps"  public="true" abstract="true">
<description>
Get the event time stamps.
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetTimeStamp" dimension="1"/>
<description>
the event time stamps, or null if event initiation is not enabled.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.getNotifyType() -->
<method name="getNotifyType"  public="true" abstract="true">
<description>
Get the notify type.
</description>
<return>
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
<description>
the notify type, or null if event initiation is not enabled.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.getEventEnable() -->
<method name="getEventEnable"  public="true" abstract="true">
<description>
Get the event enable bits.
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
the event enable bits, or null if event initiation is not enabled.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.getEventPriorities() -->
<method name="getEventPriorities"  public="true" abstract="true">
<description>
Get the event priorities.
</description>
<return>
<type class="int" dimension="1"/>
<description>
the event priorities, or null if event initiation is not enabled.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.getNotificationClass() -->
<method name="getNotificationClass"  public="true" abstract="true">
<description>
Get the Notification Class object for this event source.
</description>
<return>
<type class="javax.baja.bacnet.export.BBacnetNotificationClassDescriptor"/>
<description>
the &lt;code&gt;BacnetNotificationClassDescriptor&lt;/code&gt; for this object.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.getEventType() -->
<method name="getEventType"  public="true" abstract="true">
<description>
Get the BACnetEventType reported by this object.
</description>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.isFatalFault() -->
<method name="isFatalFault"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is this component in a fatal fault condition?
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.readEventMessageTexts(int) -->
<method name="readEventMessageTexts"  protected="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;fallthrough&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<parameter name="ndx">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.readEventMessageTextsConfig(java.lang.String, java.lang.String, java.lang.String, int) -->
<method name="readEventMessageTextsConfig"  protected="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;fallthrough&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<parameter name="toOffnormalText">
<type class="java.lang.String"/>
</parameter>
<parameter name="toFaultText">
<type class="java.lang.String"/>
</parameter>
<parameter name="toNormalText">
<type class="java.lang.String"/>
</parameter>
<parameter name="ndx">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.writeEventMessageTextsConfig(int, byte[], javax.baja.alarm.ext.BAlarmSourceExt) -->
<method name="writeEventMessageTextsConfig"  protected="true" static="true">
<description/>
<parameter name="ndx">
<type class="int"/>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
</parameter>
<parameter name="almExt">
<type class="javax.baja.alarm.ext.BAlarmSourceExt"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.resetOutOfRangeTexts(javax.baja.alarm.ext.BAlarmSourceExt) -->
<method name="resetOutOfRangeTexts"  protected="true" static="true">
<description>
If the offnormal algorithm is an outOfRange algorithm, reset the high and low limit text&#xa; because these would overwrite the MSG_TEXT alarm data field and the toOffnormalText value&#xa; written through BACnet would not be used.
</description>
<parameter name="almExt">
<type class="javax.baja.alarm.ext.BAlarmSourceExt"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.readEventTimeStamps(javax.baja.sys.BAbsTime, javax.baja.sys.BAbsTime, javax.baja.sys.BAbsTime, int) -->
<method name="readEventTimeStamps"  protected="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;fallthrough&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Read the Event_Time_Stamps property from our&#xa; BAlarmSourceExt.
</description>
<parameter name="lastOffnormalTime">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<parameter name="lastFaultTime">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<parameter name="lastToNormalTime">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<parameter name="ndx">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.readEventTransition(javax.baja.alarm.BAlarmTransitionBits) -->
<method name="readEventTransition"  protected="true">
<description>
Read the Event Transition from Alarm Transition Bits only if that event(offnormal,&#xa; fault or normal) present in event buffer
</description>
<parameter name="alarmTransitionBits">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.statusChanged() -->
<method name="statusChanged"  public="true">
<description>
Override this method to update the bacnetStatusFlags slot
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.removeEventFromEventBuffer() -->
<method name="removeEventFromEventBuffer"  protected="true">
<description>
remove event from all event buffer for an event source.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventSource.status -->
<field name="status"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.&#xa; the status for Niagara server-side behavior.
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetEventSource.faultCause -->
<field name="faultCause"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.&#xa; Provides a description of a fault with server-side behavior.
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetEventSource.eventDetectionEnable -->
<field name="eventDetectionEnable"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;eventDetectionEnable&lt;/code&gt; property.&#xa; indicates whether (TRUE) or not (FALSE) intrinsic/algorithmic reporting is enabled
</description>
<tag name="@see">#getEventDetectionEnable</tag>
<tag name="@see">#setEventDetectionEnable</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetEventSource.dynamicallyCreated -->
<field name="dynamicallyCreated"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;dynamicallyCreated&lt;/code&gt; property.&#xa; indicates whether (TRUE) or not (FALSE) the object was dynamically created.
</description>
<tag name="@see">#getDynamicallyCreated</tag>
<tag name="@see">#setDynamicallyCreated</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetEventSource.bacnetStatusFlags -->
<field name="bacnetStatusFlags"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;bacnetStatusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#getBacnetStatusFlags</tag>
<tag name="@see">#setBacnetStatusFlags</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetEventSource.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.export.BBacnetEventSource.ACKED_TRANS_DEFAULT -->
<field name="ACKED_TRANS_DEFAULT"  protected="true" static="true" final="true">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description/>
</field>

<!-- javax.baja.bacnet.export.BBacnetEventSource.MESSAGE_TEXTS_COUNT -->
<field name="MESSAGE_TEXTS_COUNT"  protected="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
