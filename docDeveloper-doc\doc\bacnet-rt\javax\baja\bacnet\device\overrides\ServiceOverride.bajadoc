<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.device.overrides.ServiceOverride" name="ServiceOverride" packageName="javax.baja.bacnet.device.overrides" public="true" interface="true" abstract="true" category="interface">
<description>
Allow the creation of components to&#xa; prevent the use of certain services&#xa; to certain devices.
</description>
<tag name="@author"><PERSON></tag>
<implements>
<type class="javax.baja.bacnet.device.overrides.DeviceOverride"/>
</implements>
<!-- javax.baja.bacnet.device.overrides.ServiceOverride.getProtocolServicesSupported(javax.baja.bacnet.config.BBacnetDeviceObject, javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="getProtocolServicesSupported"  public="true" abstract="true">
<description/>
<parameter name="device">
<type class="javax.baja.bacnet.config.BBacnetDeviceObject"/>
</parameter>
<parameter name="claimed">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

</class>
</bajadoc>
