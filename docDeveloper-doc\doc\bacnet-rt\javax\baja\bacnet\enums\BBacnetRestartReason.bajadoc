<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetRestartReason" name="BBacnetRestartReason" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetRestartReason represents the BACnetRestartReason&#xa; enumeration.&#xa; &lt;p&gt;&#xa; BBacnetRestartReason is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-255 (0xFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Craig Gemmill</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">20 Jan 2009</tag>
<tag name="@since">Niagara 3.5</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknown&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;coldstart&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;warmstart&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;detectedPowerLost&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;detectedPowerOff&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;hardwareWatchdog&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;softwareWatchdog&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;suspended&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetRestartReason.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetRestartReason"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetRestartReason"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
String representation of this BEnum.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.UNKNOWN -->
<field name="UNKNOWN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknown.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.COLDSTART -->
<field name="COLDSTART"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for coldstart.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.WARMSTART -->
<field name="WARMSTART"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for warmstart.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.DETECTED_POWER_LOST -->
<field name="DETECTED_POWER_LOST"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for detectedPowerLost.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.DETECTED_POWER_OFF -->
<field name="DETECTED_POWER_OFF"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for detectedPowerOff.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.HARDWARE_WATCHDOG -->
<field name="HARDWARE_WATCHDOG"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for hardwareWatchdog.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.SOFTWARE_WATCHDOG -->
<field name="SOFTWARE_WATCHDOG"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for softwareWatchdog.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.SUSPENDED -->
<field name="SUSPENDED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for suspended.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.unknown -->
<field name="unknown"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRestartReason"/>
<description>
BBacnetRestartReason constant for unknown.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.coldstart -->
<field name="coldstart"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRestartReason"/>
<description>
BBacnetRestartReason constant for coldstart.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.warmstart -->
<field name="warmstart"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRestartReason"/>
<description>
BBacnetRestartReason constant for warmstart.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.detectedPowerLost -->
<field name="detectedPowerLost"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRestartReason"/>
<description>
BBacnetRestartReason constant for detectedPowerLost.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.detectedPowerOff -->
<field name="detectedPowerOff"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRestartReason"/>
<description>
BBacnetRestartReason constant for detectedPowerOff.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.hardwareWatchdog -->
<field name="hardwareWatchdog"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRestartReason"/>
<description>
BBacnetRestartReason constant for hardwareWatchdog.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.softwareWatchdog -->
<field name="softwareWatchdog"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRestartReason"/>
<description>
BBacnetRestartReason constant for softwareWatchdog.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.suspended -->
<field name="suspended"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRestartReason"/>
<description>
BBacnetRestartReason constant for suspended.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRestartReason"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRestartReason.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
