<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.query.QueryRunner" name="QueryRunner" packageName="com.tridiumx.jsonToolkit.outbound.schema.query" public="true">
<description>
Utility class for running schema queries.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- com.tridiumx.jsonToolkit.outbound.schema.query.QueryRunner(com.tridiumx.jsonToolkit.outbound.schema.BJsonSchema) -->
<constructor name="QueryRunner" public="true">
<parameter name="schema">
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchema"/>
</parameter>
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.QueryRunner.executeQueries(javax.baja.sys.BComplex, javax.baja.sys.Context) -->
<method name="executeQueries"  public="true">
<description/>
<parameter name="base">
<type class="javax.baja.sys.BComplex"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.QueryFailException"/>
</throws>
</method>

</class>
</bajadoc>
