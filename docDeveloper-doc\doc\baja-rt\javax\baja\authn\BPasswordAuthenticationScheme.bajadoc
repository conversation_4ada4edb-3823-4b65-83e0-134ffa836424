<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.authn.BPasswordAuthenticationScheme" name="BPasswordAuthenticationScheme" packageName="javax.baja.authn" public="true" abstract="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@creation">8/20/2014</tag>
<tag name="@since">Niagara 4.0</tag>
<extends>
<type class="javax.baja.authn.BAuthenticationScheme"/>
</extends>
<property name="globalPasswordConfiguration" flags="">
<type class="com.tridium.user.BGlobalPasswordConfiguration"/>
<description>
Slot for the &lt;code&gt;globalPasswordConfiguration&lt;/code&gt; property.
</description>
<tag name="@see">#getGlobalPasswordConfiguration</tag>
<tag name="@see">#setGlobalPasswordConfiguration</tag>
</property>

<!-- javax.baja.authn.BPasswordAuthenticationScheme() -->
<constructor name="BPasswordAuthenticationScheme" public="true">
<description/>
</constructor>

<!-- javax.baja.authn.BPasswordAuthenticationScheme.getGlobalPasswordConfiguration() -->
<method name="getGlobalPasswordConfiguration"  public="true">
<description>
Get the &lt;code&gt;globalPasswordConfiguration&lt;/code&gt; property.
</description>
<tag name="@see">#globalPasswordConfiguration</tag>
<return>
<type class="com.tridium.user.BGlobalPasswordConfiguration"/>
</return>
</method>

<!-- javax.baja.authn.BPasswordAuthenticationScheme.setGlobalPasswordConfiguration(com.tridium.user.BGlobalPasswordConfiguration) -->
<method name="setGlobalPasswordConfiguration"  public="true">
<description>
Set the &lt;code&gt;globalPasswordConfiguration&lt;/code&gt; property.
</description>
<tag name="@see">#globalPasswordConfiguration</tag>
<parameter name="v">
<type class="com.tridium.user.BGlobalPasswordConfiguration"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.authn.BPasswordAuthenticationScheme.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.authn.BPasswordAuthenticationScheme.isDuplicatePassword(java.lang.String, javax.baja.user.BUser) -->
<method name="isDuplicatePassword"  public="true">
<description>
Check the password history to see if the specified password is a duplicate for the specified user
</description>
<parameter name="password">
<type class="java.lang.String"/>
<description>
the password to check
</description>
</parameter>
<parameter name="user">
<type class="javax.baja.user.BUser"/>
<description>
the user to check against
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the password is a duplicate
</description>
</return>
<throws>
<type class="java.lang.Exception"/>
<description/>
</throws>
</method>

<!-- javax.baja.authn.BPasswordAuthenticationScheme.isDuplicatePassword(javax.baja.security.BPassword, javax.baja.user.BUser) -->
<method name="isDuplicatePassword"  public="true">
<description>
Check the password history to see if the specified password is a duplicate for the specified user
</description>
<tag name="@since">Niagara 4.6</tag>
<parameter name="password">
<type class="javax.baja.security.BPassword"/>
<description>
the password to check
</description>
</parameter>
<parameter name="user">
<type class="javax.baja.user.BUser"/>
<description>
the user to check against
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the password is a duplicate
</description>
</return>
<throws>
<type class="java.lang.Exception"/>
<description/>
</throws>
</method>

<!-- javax.baja.authn.BPasswordAuthenticationScheme.checkPassword(java.lang.String) -->
<method name="checkPassword"  public="true">
<description>
Checks the password against the password strength requirements
</description>
<parameter name="password">
<type class="java.lang.String"/>
<description>
the password to check
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
<description>
if the password does not meet strength requirements
</description>
</throws>
</method>

<!-- javax.baja.authn.BPasswordAuthenticationScheme.checkPassword(javax.baja.security.BPassword) -->
<method name="checkPassword"  public="true">
<description>
Checks the password against the password strength requirements
</description>
<tag name="@since">Niagara 4.6</tag>
<parameter name="password">
<type class="javax.baja.security.BPassword"/>
<description>
the password to check
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
<description>
if the password does not meet strength requirements
</description>
</throws>
</method>

<!-- javax.baja.authn.BPasswordAuthenticationScheme.getDefaultAuthenticator() -->
<method name="getDefaultAuthenticator"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.security.BAbstractAuthenticator"/>
</return>
</method>

<!-- javax.baja.authn.BPasswordAuthenticationScheme.globalPasswordConfiguration -->
<field name="globalPasswordConfiguration"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;globalPasswordConfiguration&lt;/code&gt; property.
</description>
<tag name="@see">#getGlobalPasswordConfiguration</tag>
<tag name="@see">#setGlobalPasswordConfiguration</tag>
</field>

<!-- javax.baja.authn.BPasswordAuthenticationScheme.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.authn.BPasswordAuthenticationScheme.log -->
<field name="log"  public="true" static="true" final="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

</class>
</bajadoc>
