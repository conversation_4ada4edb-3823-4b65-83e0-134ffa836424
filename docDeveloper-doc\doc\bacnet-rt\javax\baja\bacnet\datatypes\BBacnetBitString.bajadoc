<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetBitString" name="BBacnetBitString" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BBacnetBitString represents a bit string value in a Bacnet property.&#xa; It is represented in Niagara as an array of boolean values.  Facets can&#xa; be applied to a slot containing a BBacnetBitString to provide names for&#xa; the various bits.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 14$ $Date: 12/13/01 3:37:26 PM$</tag>
<tag name="@creation">20 Jun 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BSimple"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
<!-- javax.baja.bacnet.datatypes.BBacnetBitString.make(boolean[], javax.baja.sys.BFacets) -->
<method name="make"  public="true" static="true">
<description>
Factory method.
</description>
<parameter name="bits">
<type class="boolean" dimension="1"/>
<description>
the array of boolean bit values.
</description>
</parameter>
<parameter name="tags">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
a BBacnetBitString with these bits set.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.make(boolean[]) -->
<method name="make"  public="true" static="true">
<description>
Factory method.
</description>
<parameter name="bits">
<type class="boolean" dimension="1"/>
<description>
the array of boolean bit values.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
a BBacnetBitString with these bits set.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.make(javax.baja.bacnet.datatypes.BBacnetBitString, int, boolean) -->
<method name="make"  public="true" static="true">
<description>
Factory method for &#x22;setBit&#x22; functionality.&#xa; Makes a new bit string exactly like the current one, but with the&#xa; given bit set to the specified state.
</description>
<parameter name="bs">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<parameter name="index">
<type class="int"/>
<description>
the bit to be set.
</description>
</parameter>
<parameter name="newState">
<type class="boolean"/>
<description>
the new state of the bit.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
a new bit string with the specified bit set.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description>
BBacnetBitString equality is based on all bits being equal.&#xa; The tags are not included in the comparison.
</description>
<parameter name="obj">
<type class="java.lang.Object"/>
<description>
the comparison object.
</description>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.&#xa; The standard serialization of BBacnetBitString supports facets inclusion.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.hashCode() -->
<method name="hashCode"  public="true">
<description>
Hash code.&#xa; The hash code for a BBacnetBitString is calculated by&#xa; calculating the integer formed by setting all of the corresponding&#xa; bits in an int.  For bit strings longer than 32 bits, the&#xa; low 32 bits are XOR&#x27;d with the next 32 bits.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.encode(java.io.DataOutput) -->
<method name="encode"  public="true">
<description>
BBacnetBitString is serialized using writeBoolean().
</description>
<parameter name="out">
<type class="java.io.DataOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.decode(java.io.DataInput) -->
<method name="decode"  public="true">
<description>
BBacnetBitString is unserialized using readBoolean().
</description>
<parameter name="in">
<type class="java.io.DataInput"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.encodeToString() -->
<method name="encodeToString"  public="true">
<description>
Write the simple in text format.
</description>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.decodeFromString(java.lang.String) -->
<method name="decodeFromString"  public="true">
<description>
Read the simple from text format.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.length() -->
<method name="length"  public="true">
<description/>
<return>
<type class="int"/>
<description>
the bit string length.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.getBit(int) -->
<method name="getBit"  public="true">
<description/>
<parameter name="index">
<type class="int"/>
<description>
the location of the bit to get.
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
the boolean value of the bit.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.getBits() -->
<method name="getBits"  public="true">
<description>
Get (a copy of) the bits array.
</description>
<return>
<type class="boolean" dimension="1"/>
<description>
a boolean array indicating the status of each bit.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.getActiveTags(javax.baja.sys.Context) -->
<method name="getActiveTags"  public="true">
<description>
Get the list of tags for the active bits.  This uses the&#xa; supplied context, or if null, it attempts to use the local&#xa; context.  If both are null, it simply displays the bitstring.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
<description>
the tag context.
</description>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag names for the active bits in this bitstring.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.getCompleteTagList(javax.baja.sys.Context) -->
<method name="getCompleteTagList"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.getCompleteString() -->
<method name="getCompleteString"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.emptyBitString(int) -->
<method name="emptyBitString"  public="true" static="true">
<description>
Create an empty bit string of the given length.
</description>
<parameter name="len">
<type class="int"/>
<description/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
empty bit string.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.getType() -->
<method name="getType"  public="true">
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetBitString.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
