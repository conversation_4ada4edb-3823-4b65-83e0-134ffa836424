<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BScopedFileSpace" name="BScopedFileSpace" packageName="javax.baja.file" public="true">
<description>
BScopedFileSpace is a file space for the local machine&#x27;s&#xa; file system which is scoped down to a particular directory.&#xa; All access is restricted to files residing within the scope.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">9 Dec 09</tag>
<tag name="@version">$Revision: 3$ $Date: 2/25/10 4:44:57 PM EST$</tag>
<tag name="@since">Niagara 3.5</tag>
<extends>
<type class="javax.baja.file.BFileSpace"/>
</extends>
<implements>
<type class="javax.baja.file.BIScopedFileSpace"/>
</implements>
<!-- javax.baja.file.BScopedFileSpace(javax.baja.file.FilePath, java.lang.String, javax.baja.util.LexiconText) -->
<constructor name="BScopedFileSpace" public="true">
<parameter name="scope">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="lexText">
<type class="javax.baja.util.LexiconText"/>
</parameter>
<description>
Constructor, the scope of this file system is specified.&#xa; The scope must resolve to an existing directory within the full&#xa; file system.  Therefore the provided scope must be a full absolute&#xa; FilePath resolving to a directory within the full (non-scoped) file system.
</description>
</constructor>

<!-- javax.baja.file.BScopedFileSpace.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.file.BScopedFileSpace.inScope(javax.baja.file.FilePath) -->
<method name="inScope"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is the given full (non-scoped) FilePath contained within this scoped file system?
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BScopedFileSpace.isBlacklisted(javax.baja.file.BIFile) -->
<method name="isBlacklisted"  public="true">
<description>
Is the given file blacklisted within this scoped file system?
</description>
<parameter name="file">
<type class="javax.baja.file.BIFile"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BScopedFileSpace.scopedPathToAbsPath(javax.baja.file.FilePath) -->
<method name="scopedPathToAbsPath"  protected="true">
<description>
Given a FilePath relative to the scope of this file space,&#xa; convert it to a full absolute FilePath valid within the full (non-scoped) file system.&#xa; If the given FilePath can&#x27;t be merged with the scope to create an absolute FilePath&#xa; within the full (non-scoped) file system, null is returned.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.FilePath"/>
</return>
</method>

<!-- javax.baja.file.BScopedFileSpace.getScope() -->
<method name="getScope"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Returns the scope of this file space.&#xa; The FilePath returned is valid within the full (non-scoped) file system.
</description>
<return>
<type class="javax.baja.file.FilePath"/>
</return>
</method>

<!-- javax.baja.file.BScopedFileSpace.makeDir(javax.baja.file.FilePath, javax.baja.sys.Context) -->
<method name="makeDir"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Make a directory for the specified path or return&#xa; the existing directory.  This creates zero or more&#xa; directories as needed.&#xa; The given file path will be resolved relative to the&#xa; scope of this file space.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.file.BDirectory"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BScopedFileSpace.makeFile(javax.baja.file.FilePath, javax.baja.sys.Context) -->
<method name="makeFile"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Make a file for the specified path or return the&#xa; existing file.  This creates zero or more directories&#xa; as needed.&#xa; The given file path will be resolved relative to the&#xa; scope of this file space.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BScopedFileSpace.move(javax.baja.file.FilePath, javax.baja.file.FilePath, javax.baja.sys.Context) -->
<method name="move"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Move/rename the specified file.  If the &#x22;to&#x22; path is not&#xa; absolute, then it is relative to the from.getParent().&#xa; The given file paths will be resolved relative to the&#xa; scope of this file space.
</description>
<parameter name="from">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="to">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BScopedFileSpace.delete(javax.baja.file.FilePath, javax.baja.sys.Context) -->
<method name="delete"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Delete the specified file store.&#xa; The given file path will be resolved relative to the&#xa; scope of this file space.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BScopedFileSpace.listFiles() -->
<method name="listFiles"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return root files of this scoped file space.
</description>
<return>
<type class="javax.baja.file.BIFile" dimension="1"/>
</return>
</method>

<!-- javax.baja.file.BScopedFileSpace.findFile(javax.baja.file.FilePath) -->
<method name="findFile"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Map the file path to an instance of BLocalFileStore&#xa; or return null if it doesn&#x27;t exist.&#xa; The given file path will be resolved relative to the&#xa; scope of this file space.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
</method>

<!-- javax.baja.file.BScopedFileSpace.findStore(javax.baja.file.FilePath) -->
<method name="findStore"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Map the file path to an instance of BLocalFileStore&#xa; or return null if it doesn&#x27;t exist.&#xa; The given file path will be resolved relative to the&#xa; scope of this file space.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.BIFileStore"/>
</return>
</method>

<!-- javax.baja.file.BScopedFileSpace.getChild(javax.baja.file.BIFile, java.lang.String) -->
<method name="getChild"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the child of the specified directory or return null.&#xa; The child must reside within the scope of this file space,&#xa; otherwise null will be returned.
</description>
<parameter name="dir">
<type class="javax.baja.file.BIFile"/>
</parameter>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
</method>

<!-- javax.baja.file.BScopedFileSpace.getChildren(javax.baja.file.BIFile) -->
<method name="getChildren"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the children of the specified directory or&#xa; return an empty array.  An empty array will also&#xa; be returned if the directory doesn&#x27;t reside within&#xa; the scope of this file space.
</description>
<parameter name="dir">
<type class="javax.baja.file.BIFile"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile" dimension="1"/>
</return>
</method>

<!-- javax.baja.file.BScopedFileSpace.hasNavChildren() -->
<method name="hasNavChildren"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return true.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BScopedFileSpace.getNavChild(java.lang.String) -->
<method name="getNavChild"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get a root by name.
</description>
<parameter name="navName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.nav.BINavNode"/>
</return>
</method>

<!-- javax.baja.file.BScopedFileSpace.getNavChildren() -->
<method name="getNavChildren"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the scoped file space roots.
</description>
<return>
<type class="javax.baja.nav.BINavNode" dimension="1"/>
</return>
</method>

<!-- javax.baja.file.BScopedFileSpace.getOrdInHost() -->
<method name="getOrdInHost"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the ord in host.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.file.BScopedFileSpace.getOrdInSession() -->
<method name="getOrdInSession"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the ord in session.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.file.BScopedFileSpace.getCategoryMask() -->
<method name="getCategoryMask"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
FileSpaces are mapped to categories by ord in &lt;code&gt;CategoryService.ordMap&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.file.BScopedFileSpace.getAppliedCategoryMask() -->
<method name="getAppliedCategoryMask"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
FileSpaces are mapped to categories by ord in &lt;code&gt;CategoryService.ordMap&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.file.BScopedFileSpace.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.file.BScopedFileSpace.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.file.BScopedFileSpace.SYS_HOME -->
<field name="SYS_HOME"  public="true" static="true" final="true">
<type class="javax.baja.file.BScopedFileSpace"/>
<description/>
</field>

<!-- javax.baja.file.BScopedFileSpace.USER_HOME -->
<field name="USER_HOME"  public="true" static="true" final="true">
<type class="javax.baja.file.BScopedFileSpace"/>
<description/>
</field>

<!-- javax.baja.file.BScopedFileSpace.STATION_HOME -->
<field name="STATION_HOME"  public="true" static="true" final="true">
<type class="javax.baja.file.BScopedFileSpace"/>
<description/>
</field>

<!-- javax.baja.file.BScopedFileSpace.PROTECTED_STATION_HOME -->
<field name="PROTECTED_STATION_HOME"  public="true" static="true" final="true">
<type class="javax.baja.file.BScopedFileSpace"/>
<description/>
</field>

<!-- javax.baja.file.BScopedFileSpace.FILE_OUT_OF_SCOPE_ERROR -->
<field name="FILE_OUT_OF_SCOPE_ERROR"  protected="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

</class>
</bajadoc>
