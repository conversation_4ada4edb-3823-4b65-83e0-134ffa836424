<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.log.ConsoleLogHandler" name="ConsoleLogHandler" packageName="javax.baja.log" public="true">
<description>
DefaultLogHandler provides a simple implementation&#xa; of the LogHandler to dump log messages to standard&#xa; output.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">9 Apr 00</tag>
<tag name="@version">$Revision: 11$ $Date: 4/27/11 11:58:54 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<tag name="@deprecated">As of Niagara 4.0.</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="javax.baja.log.LogHandler"/>
</implements>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<!-- javax.baja.log.ConsoleLogHandler() -->
<constructor name="ConsoleLogHandler" public="true">
<description/>
</constructor>

<!-- javax.baja.log.ConsoleLogHandler.publish(javax.baja.log.LogRecord) -->
<method name="publish"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Print a log record to standard output.
</description>
<parameter name="record">
<type class="javax.baja.log.LogRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.log.ConsoleLogHandler.write(java.io.PrintStream, javax.baja.log.LogRecord) -->
<method name="write"  public="true">
<description>
Write record to the specified stream.
</description>
<parameter name="out">
<type class="java.io.PrintStream"/>
</parameter>
<parameter name="record">
<type class="javax.baja.log.LogRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
