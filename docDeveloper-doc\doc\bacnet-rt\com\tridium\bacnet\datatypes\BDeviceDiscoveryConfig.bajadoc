<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.datatypes.BDeviceDiscoveryConfig" name="BDeviceDiscoveryConfig" packageName="com.tridium.bacnet.datatypes" public="true">
<description>
This class file specifies parameters to constrain a&#xa; network device discovery request.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">03 Jan 2005</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.datatypes.BRequestConfig"/>
</extends>
<property name="deviceLowLimit" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;deviceLowLimit&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceLowLimit</tag>
<tag name="@see">#setDeviceLowLimit</tag>
</property>

<property name="deviceHighLimit" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;deviceHighLimit&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceHighLimit</tag>
<tag name="@see">#setDeviceHighLimit</tag>
</property>

<property name="networks" flags="">
<type class="com.tridium.bacnet.datatypes.BDiscoveryNetworks"/>
<description>
Slot for the &lt;code&gt;networks&lt;/code&gt; property.
</description>
<tag name="@see">#getNetworks</tag>
<tag name="@see">#setNetworks</tag>
</property>

<property name="waitResponseTime" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;waitResponseTime&lt;/code&gt; property.&#xa; time in seconds to wait for device responses.
</description>
<tag name="@see">#getWaitResponseTime</tag>
<tag name="@see">#setWaitResponseTime</tag>
</property>

</class>
</bajadoc>
