<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.ExportOp" name="ExportOp" packageName="javax.baja.file" public="true" abstract="true">
<description>
ExportOp encapsulates all the information needed to process&#xa; a source BObject accessed via &lt;code&gt;get()&lt;/code&gt; into a file&#xa; using &lt;code&gt;getOutputStream()&lt;/code&gt;.
</description>
<tag name="@author"><PERSON> on 22 May 04</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.naming.OrdTarget"/>
</extends>
<!-- javax.baja.file.ExportOp(javax.baja.naming.OrdTarget) -->
<constructor name="ExportOp" protected="true">
<parameter name="base">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<description>
Constructor with source OrdTarget.
</description>
</constructor>

<!-- javax.baja.file.ExportOp.make(javax.baja.naming.OrdTarget, java.io.OutputStream) -->
<method name="make"  public="true" static="true">
<description>
Create a new instance of an ExportOp.
</description>
<parameter name="from">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<parameter name="to">
<type class="java.io.OutputStream"/>
</parameter>
<return>
<type class="javax.baja.file.ExportOp"/>
</return>
</method>

<!-- javax.baja.file.ExportOp.getOutputStream() -->
<method name="getOutputStream"  public="true" abstract="true">
<description>
Get the output stream used to write the file.
</description>
<return>
<type class="java.io.OutputStream"/>
</return>
</method>

<!-- javax.baja.file.ExportOp.getFacets() -->
<method name="getFacets"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the facets which originates from context passed to BOrd.resolve().
</description>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.file.ExportOp.mergeFacets(javax.baja.sys.BFacets) -->
<method name="mergeFacets"  public="true">
<description>
Mutate the existing ExportOp by merging additional facets and return the existing ExportOp.
</description>
<tag name="@since">Niagara 4.8</tag>
<parameter name="newFacets">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="javax.baja.file.ExportOp"/>
</return>
</method>

</class>
</bajadoc>
