<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BIBacnetDataType" name="BIBacnetDataType" packageName="javax.baja.bacnet.datatypes" public="true" interface="true" abstract="true" category="interface">
<description>
BIBacnetDataType is the interface implemented by Bacnet constructed data types.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">22 Mar 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<!-- javax.baja.bacnet.datatypes.BIBacnetDataType.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true" abstract="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BIBacnetDataType.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true" abstract="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BIBacnetDataType.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
