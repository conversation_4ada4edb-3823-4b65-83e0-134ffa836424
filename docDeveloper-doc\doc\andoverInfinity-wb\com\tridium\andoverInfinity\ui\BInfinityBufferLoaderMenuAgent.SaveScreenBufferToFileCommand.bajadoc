<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="wb" qualifiedName="com.tridium.andoverInfinity.ui.BInfinityBufferLoaderMenuAgent$SaveScreenBufferToFileCommand" name="BInfinityBufferLoaderMenuAgent.SaveScreenBufferToFileCommand" packageName="com.tridium.andoverInfinity.ui" public="true" innerClass="true">
<description/>
<extends>
<type class="javax.baja.ui.Command"/>
</extends>
<!-- com.tridium.andoverInfinity.ui.BInfinityBufferLoaderMenuAgent.SaveScreenBufferToFileCommand(javax.baja.ui.BWidget, com.tridium.andoverInfinity.BInfinityNetwork) -->
<constructor name="SaveScreenBufferToFileCommand" public="true">
<parameter name="owner">
<type class="javax.baja.ui.BWidget"/>
</parameter>
<parameter name="target">
<type class="com.tridium.andoverInfinity.BInfinityNetwork"/>
</parameter>
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.ui.BInfinityBufferLoaderMenuAgent.SaveScreenBufferToFileCommand.doInvoke() -->
<method name="doInvoke"  public="true">
<description/>
<return>
<type class="javax.baja.ui.CommandArtifact"/>
</return>
</method>

</class>
</bajadoc>
