<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.OrdTarget" name="OrdTarget" packageName="javax.baja.naming" public="true">
<description>
OrdTarget is the result of resolving a BOrd.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">10 Feb 03</tag>
<tag name="@version">$Revision: 38$ $Date: 9/8/09 1:59:07 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="javax.baja.sys.Context"/>
</implements>
<!-- javax.baja.naming.OrdTarget(javax.baja.naming.OrdTarget, javax.baja.sys.BObject) -->
<constructor name="OrdTarget" public="true">
<parameter name="base">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<parameter name="object">
<type class="javax.baja.sys.BObject"/>
</parameter>
<description>
Constructor used during BOrdScheme.resolve().
</description>
</constructor>

<!-- javax.baja.naming.OrdTarget(javax.baja.naming.OrdTarget) -->
<constructor name="OrdTarget" public="true">
<parameter name="base">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<description>
Construct an OrdTarget which wraps another OrdTarget.
</description>
</constructor>

<!-- javax.baja.naming.OrdTarget(javax.baja.naming.OrdTarget, javax.baja.sys.BComponent, javax.baja.sys.BObject, javax.baja.sys.Slot, javax.baja.sys.Property[]) -->
<constructor name="OrdTarget" public="true">
<parameter name="base">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<parameter name="component">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<parameter name="object">
<type class="javax.baja.sys.BObject"/>
</parameter>
<parameter name="slot">
<type class="javax.baja.sys.Slot"/>
</parameter>
<parameter name="propertyPath">
<type class="javax.baja.sys.Property" dimension="1"/>
</parameter>
<description>
Package scoped constructor to SlotPath.
</description>
</constructor>

<!-- javax.baja.naming.OrdTarget.makeWithFacets(javax.baja.naming.OrdTarget, javax.baja.sys.BObject, javax.baja.sys.BFacets) -->
<method name="makeWithFacets"  public="true" static="true">
<description>
Make an OrdTarget with the specified base and object that has the specified additional&#xa; facets.
</description>
<parameter name="base">
<type class="javax.baja.naming.OrdTarget"/>
<description>
The base OrdTarget.
</description>
</parameter>
<parameter name="object">
<type class="javax.baja.sys.BObject"/>
<description>
The target object of the OrdTarget.
</description>
</parameter>
<parameter name="facets">
<type class="javax.baja.sys.BFacets"/>
<description>
The facets that will be merged with the base facets.
</description>
</parameter>
<return>
<type class="javax.baja.naming.OrdTarget"/>
<description>
Returns a new OrdTarget.
</description>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.makeWithFacetsAndLanguage(javax.baja.naming.OrdTarget, javax.baja.sys.BObject, javax.baja.sys.BFacets, java.lang.String) -->
<method name="makeWithFacetsAndLanguage"  public="true" static="true">
<description>
Make an OrdTarget with the specified base and object that has the specified additional&#xa; facets and language.
</description>
<tag name="@since">Niagara 4.2U2</tag>
<parameter name="base">
<type class="javax.baja.naming.OrdTarget"/>
<description>
The base OrdTarget.
</description>
</parameter>
<parameter name="object">
<type class="javax.baja.sys.BObject"/>
<description>
The target object of the OrdTarget.
</description>
</parameter>
<parameter name="facets">
<type class="javax.baja.sys.BFacets"/>
<description>
The facets that will be merged with the base facets.
</description>
</parameter>
<parameter name="language">
<type class="java.lang.String"/>
<description>
The language that will be used if provided
</description>
</parameter>
<return>
<type class="javax.baja.naming.OrdTarget"/>
<description>
Returns a new OrdTarget.
</description>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.makeWithViewQuery(javax.baja.naming.OrdTarget, javax.baja.naming.ViewQuery) -->
<method name="makeWithViewQuery"  public="true" static="true">
<description>
Make an OrdTarget with the specified base and&#xa; additional ViewQuery.
</description>
<tag name="@since">Niagara 4.11</tag>
<parameter name="base">
<type class="javax.baja.naming.OrdTarget"/>
<description>
The base OrdTarget.
</description>
</parameter>
<parameter name="viewQuery">
<type class="javax.baja.naming.ViewQuery"/>
<description>
The additional ViewQuery.
</description>
</parameter>
<return>
<type class="javax.baja.naming.OrdTarget"/>
<description>
Returns a new OrdTarget.
</description>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.makeWithFacets(javax.baja.naming.OrdTarget, javax.baja.sys.BFacets) -->
<method name="makeWithFacets"  public="true" static="true">
<description>
Make an OrdTarget with the specified base and additional facets.
</description>
<parameter name="base">
<type class="javax.baja.naming.OrdTarget"/>
<description>
The base OrdTarget.
</description>
</parameter>
<parameter name="facets">
<type class="javax.baja.sys.BFacets"/>
<description>
The facets that will be merged with the base facets.
</description>
</parameter>
<return>
<type class="javax.baja.naming.OrdTarget"/>
<description>
Returns a new OrdTarget.
</description>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.unmounted(javax.baja.sys.BObject) -->
<method name="unmounted"  public="true" static="true">
<description>
Convenience for &lt;code&gt;unmounted(target, null)&lt;/code&gt;
</description>
<tag name="@see">#unmounted(BObject, Context)</tag>
<tag name="@since">Niagara 3.5</tag>
<parameter name="target">
<type class="javax.baja.sys.BObject"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdTarget"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.unmounted(javax.baja.sys.BObject, javax.baja.sys.Context) -->
<method name="unmounted"  public="true" static="true">
<description>
Creates an unmounted OrdTarget. An unmounted target has no&#xa; base, OrdQuerys, or BOrd associated with it; it just wraps&#xa; a BObject.
</description>
<tag name="@since">Niagara 3.5</tag>
<parameter name="target">
<type class="javax.baja.sys.BObject"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdTarget"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getUser() -->
<method name="getUser"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get user which originates from context passed to BOrd.resolve().
</description>
<return>
<type class="javax.baja.user.BUser"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getLanguage() -->
<method name="getLanguage"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get language which originates from context passed to BOrd.resolve().
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getFacets() -->
<method name="getFacets"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the facets which originates from context passed to BOrd.resolve().
</description>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getFacet(java.lang.String) -->
<method name="getFacet"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Convenience for &lt;code&gt;getFacets().get(name)&lt;/code&gt;.
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getBase() -->
<method name="getBase"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the base Context or null if this maps to the root query.
</description>
<return>
<type class="javax.baja.sys.Context"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getBaseOrdTarget() -->
<method name="getBaseOrdTarget"  public="true">
<description>
If the &lt;code&gt;getBase()&lt;/code&gt; is an OrdTarget return it, otherwise null.
</description>
<return>
<type class="javax.baja.naming.OrdTarget"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getOrd() -->
<method name="getOrd"  public="true">
<description>
Get the ord used to resolve the target.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getSpace() -->
<method name="getSpace"  public="true">
<description>
Get the space for this target.
</description>
<return>
<type class="javax.baja.space.BSpace"/>
<description>
Returns the space that target object belongs to&#xa;   or null if the object is not associated with a space.
</description>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.get() -->
<method name="get"  public="true">
<description>
Get the value of the resolved ord.
</description>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getComponent() -->
<method name="getComponent"  public="true">
<description>
If the resolved value is a BComponent then return &#xa; &lt;code&gt;get()&lt;/code&gt; cast to a BComponent.  If the value&#xa; is a slot in a BComponent, then return the parent&#xa; BComponent.  Otherwise return null.
</description>
<return>
<type class="javax.baja.sys.BComponent"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getParent() -->
<method name="getParent"  public="true">
<description>
If the ord resolves to a property within a component,&#xa; then return the parent of get().  If the ord resolved&#xa; to a BComponent itself or something else then return null.&#xa; &lt;code&gt;getParent().get(getPropertyInParent()) == get()&amp;#xa; &lt;/code&gt;
</description>
<return>
<type class="javax.baja.sys.BComplex"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getSlotInComponent() -->
<method name="getSlotInComponent"  public="true">
<description>
If the ord resolves to a slot within a component then&#xa; return the slot of &lt;code&gt;getComponent()&lt;/code&gt;.  If not&#xa; applicable then return null.
</description>
<return>
<type class="javax.baja.sys.Slot"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getPropertyPathInComponent() -->
<method name="getPropertyPathInComponent"  public="true">
<description>
If the ord resolves to a property value within a component &#xa; then return the path of properties which identify the value&#xa; within the component.  If not applicable then return null.
</description>
<return>
<type class="javax.baja.sys.Property" dimension="1"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getPropertyInParent() -->
<method name="getPropertyInParent"  public="true">
<description>
If the ord resolves to a property value within a component,&#xa; then return &lt;code&gt;path[path.length-1]&lt;/code&gt; which is the&#xa; value&#x27;s property in &lt;code&gt;getParent()&lt;/code&gt;.  Otherwise&#xa; return null.
</description>
<return>
<type class="javax.baja.sys.Property"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getSecurityTarget() -->
<method name="getSecurityTarget"  public="true">
<description>
Get the IProtected object to use for security&#xa; checks.  If the target itself is an IProtected then&#xa; return it.  If this is a child slot of a BComponent&#xa; then return the BComponent.  Otherwise return &#xa; &lt;code&gt;getBase().getSecurityTarget()&lt;/code&gt;  If all&#xa; else fails, then return null.
</description>
<return>
<type class="javax.baja.security.BIProtected"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getPermissionsForTarget() -->
<method name="getPermissionsForTarget"  public="true">
<description>
Get the permissions the BUser has enabled on the target &#xa; object.  This method is the cached result of calling&#xa; &lt;code&gt;getSecurityTarget().getPermissions(this)&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.security.BPermissions"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.canRead() -->
<method name="canRead"  public="true">
<description>
Convenience for &lt;code&gt;getSecurityTarget().canRead(this)&lt;/code&gt;.&#xa; If &lt;code&gt;getSecurityTarget()&lt;/code&gt; is null, then return true.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.canWrite() -->
<method name="canWrite"  public="true">
<description>
Convenience for &lt;code&gt;getSecurityTarget().canWrite(this)&lt;/code&gt;.&#xa; If &lt;code&gt;getSecurityTarget()&lt;/code&gt; is null, then return true.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.canInvoke() -->
<method name="canInvoke"  public="true">
<description>
Convenience for &lt;code&gt;getSecurityTarget().canInvoke(this)&lt;/code&gt;.&#xa; If &lt;code&gt;getSecurityTarget()&lt;/code&gt; is null, then return true.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.depth() -->
<method name="depth"  public="true">
<description>
Get the length of the query list that was used to&#xa; resolve this target.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.queryAt(int) -->
<method name="queryAt"  public="true">
<description>
Get the query at the specified index.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdQuery"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getOrdQueries() -->
<method name="getOrdQueries"  public="true">
<description>
Get the parsed queries of the target ord.
</description>
<return>
<type class="javax.baja.naming.OrdQuery" dimension="1"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getFilePath() -->
<method name="getFilePath"  public="true">
<description>
Search from the last query down to the first query looking&#xa; for an instance of FilePath.  If not found return null.
</description>
<return>
<type class="javax.baja.file.FilePath"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getFilePathFragment() -->
<method name="getFilePathFragment"  public="true">
<description>
Return &lt;code&gt;getFilePath().getFragment()&lt;/code&gt;.  If&#xa; &lt;code&gt;getFilePath()&lt;/code&gt; returns null, then return null.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getViewQuery() -->
<method name="getViewQuery"  public="true">
<description>
If there was a view query in the ord then return it,&#xa; otherwise return null.
</description>
<return>
<type class="javax.baja.naming.ViewQuery"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getViewParameter(java.lang.String, java.lang.String) -->
<method name="getViewParameter"  public="true">
<description>
If there is a ViewQuery, then return &#xa; &lt;code&gt;getViewQuery().getParameter(name, def)&lt;/code&gt;,&#xa; otherwise return def.
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="def">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.getOrdWithoutViewQuery() -->
<method name="getOrdWithoutViewQuery"  public="true">
<description>
If there is a view query, get the ord without it.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.toString() -->
<method name="toString"  public="true">
<description>
To string.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.OrdTarget.dump() -->
<method name="dump"  public="true">
<description>
Dump all the gory details to std out.
</description>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
