<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.req.BInfinitySaveInfinetRequest" name="BInfinitySaveInfinetRequest" packageName="com.tridium.andoverInfinity.comm.req" public="true">
<description>
Used to send message to put the Infinity panel into backup mode, and &#xa; send the backup bytes to the client side when done
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.comm.req.BDdfRequest"/>
</extends>
<implements>
<type class="com.tridium.andoverInfinity.comm.Vt100Const"/>
</implements>
<implements>
<type class="com.tridium.andoverInfinity.comm.req.RequiresNetworkAccess"/>
</implements>
<implements>
<type class="com.tridium.ddf.comm.req.BIDdfCustomRequest"/>
</implements>
<!-- com.tridium.andoverInfinity.comm.req.BInfinitySaveInfinetRequest() -->
<constructor name="BInfinitySaveInfinetRequest" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.comm.req.BInfinitySaveInfinetRequest.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinitySaveInfinetRequest.processReceive(com.tridium.ddf.comm.IDdfDataFrame) -->
<method name="processReceive"  public="true">
<description/>
<parameter name="iDevDataFrame">
<type class="com.tridium.ddf.comm.IDdfDataFrame"/>
</parameter>
<return>
<type class="com.tridium.ddf.comm.rsp.BIDdfResponse"/>
</return>
<throws>
<type class="com.tridium.ddf.comm.rsp.DdfResponseException"/>
</throws>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinitySaveInfinetRequest.toByteArray() -->
<method name="toByteArray"  public="true">
<description/>
<return>
<type class="byte" dimension="1"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinitySaveInfinetRequest.setNetwork(com.tridium.andoverInfinity.BInfinityNetwork) -->
<method name="setNetwork"  public="true">
<description>
Implementation of RequiresNetworkAccess interface
</description>
<tag name="@see">com.tridium.andoverInfinity.comm.req.RequiresNetworkAccess#setNetwork(com.tridium.andoverInfinity.BInfinityNetwork)</tag>
<parameter name="network">
<type class="com.tridium.andoverInfinity.BInfinityNetwork"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinitySaveInfinetRequest.processErrorResponse(com.tridium.ddf.comm.rsp.DdfResponseException) -->
<method name="processErrorResponse"  public="true">
<description/>
<parameter name="errorRsp">
<type class="com.tridium.ddf.comm.rsp.DdfResponseException"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinitySaveInfinetRequest.processLateResponse(com.tridium.ddf.comm.rsp.BIDdfResponse) -->
<method name="processLateResponse"  public="true">
<description/>
<parameter name="ddfRsp">
<type class="com.tridium.ddf.comm.rsp.BIDdfResponse"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinitySaveInfinetRequest.processResponse(com.tridium.ddf.comm.rsp.BIDdfResponse) -->
<method name="processResponse"  public="true">
<description/>
<parameter name="ddfRsp">
<type class="com.tridium.ddf.comm.rsp.BIDdfResponse"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinitySaveInfinetRequest.processTimeout() -->
<method name="processTimeout"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinitySaveInfinetRequest.remainingRetryCount -->
<field name="remainingRetryCount"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;remainingRetryCount&lt;/code&gt; property.&#xa; This is the remaining number of retry counts that the BIDdfCommunicator&#xa; Will attempt before timing out completely.
</description>
<tag name="@see">#getRemainingRetryCount</tag>
<tag name="@see">#setRemainingRetryCount</tag>
</field>

<!-- com.tridium.andoverInfinity.comm.req.BInfinitySaveInfinetRequest.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
