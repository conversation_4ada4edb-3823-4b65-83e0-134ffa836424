<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.authn.AuthenticationUtil" name="AuthenticationUtil" packageName="javax.baja.authn" public="true" final="true">
<description>
This utility class offers support for authentication&#xa; related tasks.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">2014-01-03</tag>
<tag name="@since">Niagara 4.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- javax.baja.authn.AuthenticationUtil.debug(java.util.logging.Level, java.lang.String, java.lang.Throwable) -->
<method name="debug"  public="true" static="true">
<description>
If the Debug flag is on, prints an error message at the specified&#xa; level. If the Throwable t parameter is not null, the stack trace&#xa; will also be printed.
</description>
<parameter name="level">
<type class="java.util.logging.Level"/>
<description/>
</parameter>
<parameter name="message">
<type class="java.lang.String"/>
<description/>
</parameter>
<parameter name="t">
<type class="java.lang.Throwable"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.authn.AuthenticationUtil.SECURE_REQUIRED_MSG -->
<field name="SECURE_REQUIRED_MSG"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.authn.AuthenticationUtil.LOCKOUT_MSG -->
<field name="LOCKOUT_MSG"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.authn.AuthenticationUtil.INVALID_CREDENTIALS_MSG -->
<field name="INVALID_CREDENTIALS_MSG"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.authn.AuthenticationUtil.LOGIN_FAILED_MSG -->
<field name="LOGIN_FAILED_MSG"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

</class>
</bajadoc>
