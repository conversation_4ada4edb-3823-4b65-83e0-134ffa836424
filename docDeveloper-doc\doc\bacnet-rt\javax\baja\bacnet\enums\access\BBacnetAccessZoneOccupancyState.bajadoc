<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState" name="BBacnetAccessZoneOccupancyState" packageName="javax.baja.bacnet.enums.access" public="true" final="true">
<description>
BBacnetAccessZoneOccupancyState represents the Bacnet&#xa; Access Zone Occupancy State enumeration.&#xa; &lt;p&gt;&#xa; BBacnetAccessZoneOccupancyState is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Joseph Chandler</tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;normal&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>0</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;belowLowerLimit&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>1</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;atLowerLimit&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>2</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;atUpperLimit&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>3</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;aboveUpperLimit&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>4</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;disabled&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>5</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;notSupported&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>6</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.NORMAL -->
<field name="NORMAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for normal.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.BELOW_LOWER_LIMIT -->
<field name="BELOW_LOWER_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for belowLowerLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.AT_LOWER_LIMIT -->
<field name="AT_LOWER_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for atLowerLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.AT_UPPER_LIMIT -->
<field name="AT_UPPER_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for atUpperLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.ABOVE_UPPER_LIMIT -->
<field name="ABOVE_UPPER_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for aboveUpperLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.DISABLED -->
<field name="DISABLED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for disabled.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.NOT_SUPPORTED -->
<field name="NOT_SUPPORTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for notSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.normal -->
<field name="normal"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState"/>
<description>
BBacnetAccessZoneOccupancyState constant for normal.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.belowLowerLimit -->
<field name="belowLowerLimit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState"/>
<description>
BBacnetAccessZoneOccupancyState constant for belowLowerLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.atLowerLimit -->
<field name="atLowerLimit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState"/>
<description>
BBacnetAccessZoneOccupancyState constant for atLowerLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.atUpperLimit -->
<field name="atUpperLimit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState"/>
<description>
BBacnetAccessZoneOccupancyState constant for atUpperLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.aboveUpperLimit -->
<field name="aboveUpperLimit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState"/>
<description>
BBacnetAccessZoneOccupancyState constant for aboveUpperLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.disabled -->
<field name="disabled"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState"/>
<description>
BBacnetAccessZoneOccupancyState constant for disabled.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.notSupported -->
<field name="notSupported"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState"/>
<description>
BBacnetAccessZoneOccupancyState constant for notSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessZoneOccupancyState.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
