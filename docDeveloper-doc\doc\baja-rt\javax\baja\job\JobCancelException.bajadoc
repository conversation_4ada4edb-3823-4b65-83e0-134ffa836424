<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.job.JobCancelException" name="JobCancelException" packageName="javax.baja.job" public="true" category="exception">
<description>
JobCancelException is the desired exception to used to &#xa; exit out of a method during a BJob.cancel.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">22 Jul 04</tag>
<tag name="@version">$Revision: 2$ $Date: 9/5/07 10:18:56 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BajaRuntimeException"/>
</extends>
<!-- javax.baja.job.JobCancelException(java.lang.String) -->
<constructor name="JobCancelException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<description>
Construct a JobCancelException with the given message.
</description>
</constructor>

<!-- javax.baja.job.JobCancelException(java.lang.Throwable) -->
<constructor name="JobCancelException" public="true">
<parameter name="cause">
<type class="java.lang.Throwable"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.job.JobCancelException() -->
<constructor name="JobCancelException" public="true">
<description>
Construct a JobCancelException.
</description>
</constructor>

</class>
</bajadoc>
