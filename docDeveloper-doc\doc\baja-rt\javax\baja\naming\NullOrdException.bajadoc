<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.NullOrdException" name="NullOrdException" packageName="javax.baja.naming" public="true" category="exception">
<description>
NullOrdException is thrown when attempting a BOrd&#xa; operation on the null ord.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">28 Sept 00</tag>
<tag name="@version">$Revision: 1$ $Date: 12/12/02 10:20:28 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BajaRuntimeException"/>
</extends>
<!-- javax.baja.naming.NullOrdException(java.lang.String, java.lang.Throwable) -->
<constructor name="NullOrdException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="cause">
<type class="java.lang.Throwable"/>
</parameter>
<description>
Constructor with specified detailed message and cause.
</description>
</constructor>

<!-- javax.baja.naming.NullOrdException(java.lang.String) -->
<constructor name="NullOrdException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<description>
Constructor with specified detailed message.
</description>
</constructor>

<!-- javax.baja.naming.NullOrdException() -->
<constructor name="NullOrdException" public="true">
<description>
Constructor with no message.
</description>
</constructor>

</class>
</bajadoc>
