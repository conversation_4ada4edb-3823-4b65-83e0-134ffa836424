<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>js Module: nmodule/js/rc/log/Log</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">js</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-dialogs.html">dialogs</a></li><li><a href="module-lex.html">lex</a></li><li><a href="module-log.html">log</a></li><li><a href="module-nmodule_js_rc_csrf_csrfUtil.html">nmodule/js/rc/csrf/csrfUtil</a></li><li><a href="module-nmodule_js_rc_jasmine_promiseUtils.html">nmodule/js/rc/jasmine/promiseUtils</a></li><li><a href="module-nmodule_js_rc_lex_lex.html">nmodule/js/rc/lex/lex</a></li><li><a href="module-nmodule_js_rc_log_Level.html">nmodule/js/rc/log/Level</a></li><li><a href="module-nmodule_js_rc_log_Log.html">nmodule/js/rc/log/Log</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="classes.list.html" class="dropdown-toggle" data-toggle="dropdown">Classes<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-dialogs-Dialog.html">dialogs~Dialog</a></li><li><a href="module-nmodule_js_rc_lex_lex-Lexicon.html">nmodule/js/rc/lex/lex~Lexicon</a></li><li><a href="module-nmodule_js_rc_log_Log.Level.html">nmodule/js/rc/log/Log.Level</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: nmodule/js/rc/log/Log</h1>
<section>

<header>
    
        
            
        
    
</header>


<article>
    <div class="container-overview">
    
        

        
            
<hr>
<dt>
    <h4 class="name" id="module:nmodule/js/rc/log/Log"><span class="type-signature"></span>new (require("nmodule/js/rc/log/Log"))()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Class for logging messages throughout Niagara JS apps. Do not instantiate<br>
this class directly: rather use the <a href="module-nmodule_js_rc_log_Log.html#.getLogger">getLogger()</a> function.</p>
<p>Logs support SLF4J-style parameterization. See example.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-log.html">module:log</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    

    
        <h5>Examples</h5>
        
        <p class="code-caption">Supports SLF4J-style format anchors.</p>
    
    <pre class="sunlight-highlight-javascript">return Log.getLogger(&#x27;my.package.name&#x27;)
  .then(function (log) {
    return log.log(Log.Level.INFO, &#x27;foo was {} and bar was {}&#x27;, &#x27;foo&#x27;, &#x27;bar&#x27;);
  });
  </pre>

        <p class="code-caption">Supports a trailing Error argument.</p>
    
    <pre class="sunlight-highlight-javascript">doSomethingAsync()
  .catch(function (err) {
    return Log.logMessage(&#x27;my.package.name&#x27;, Log.Level.SEVERE,
      &#x27;{} rejected with error&#x27;, &#x27;doSomethingAsync&#x27;, err);
  });</pre>

        <p class="code-caption">Has convenience methods for behaving like the console.</p>
    
    <pre class="sunlight-highlight-javascript">define([&#x27;nmodule/js/rc/log/Log&#x27;], function (console) {
  //Note that all of these create and return Promises behind the scenes.
  //The log name will be browser.console.
  console.log(&#x27;this logs at&#x27;, &#x27;FINE&#x27;, &#x27;level&#x27;);
  console.info(&#x27;this logs at&#x27;, &#x27;INFO&#x27;, &#x27;level&#x27;);
  console.warn(&#x27;this logs at&#x27;, &#x27;WARNING&#x27;, &#x27;level&#x27;);
  console.error(&#x27;this logs at&#x27;, &#x27;SEVERE&#x27;, &#x27;level&#x27;);
});</pre>


    
</dd>

        
    
    </div>

    

    

    
        <h3 class="subsection-title">Classes</h3>

        <dl>
            <dt><a href="module-nmodule_js_rc_log_Log.Level.html">Level</a></dt>
            <dd></dd>
        </dl>
    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id=".error"><span class="type-signature">&lt;static> </span>error()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Logs a message to the <code>browser.console</code> log at <code>SEVERE</code> level.<br>
This matches a browser's <code>console.error</code> API.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
    <pre class="sunlight-highlight-javascript">Log.error(&#x27;this&#x27;, &#x27;is&#x27;, &#x27;an&#x27;, &#x27;error&#x27;, &#x27;message&#x27;);</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".getLogger"><span class="type-signature">&lt;static> </span>getLogger(name)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Resolve a Log instance with the given name.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            

            

            <td class="description last"><p>name for the log to retrieve. Calling <code>getLogger()</code><br>
twice for the same name will resolve the same Log instance.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise.&lt;<a href="module-nmodule_js_rc_log_Log.html">module:nmodule/js/rc/log/Log</a>></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".info"><span class="type-signature">&lt;static> </span>info()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Logs a message to the <code>browser.console</code> log at <code>INFO</code> level.<br>
This matches a browser's <code>console.info</code> API.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
    <pre class="sunlight-highlight-javascript">Log.info(&#x27;this&#x27;, &#x27;is&#x27;, &#x27;an&#x27;, &#x27;info&#x27;, &#x27;message&#x27;);</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".log"><span class="type-signature">&lt;static> </span>log()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Logs a message to the <code>browser.console</code> log at <code>FINE</code> level.<br>
This matches a browser's <code>console.log</code> API.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
    <pre class="sunlight-highlight-javascript">Log.log(&#x27;this&#x27;, &#x27;is&#x27;, &#x27;a&#x27;, &#x27;fine&#x27;, &#x27;message&#x27;);</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".logMessage"><span class="type-signature">&lt;static> </span>logMessage(name, level, msg [, args])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Convenience method to stop you from having to resolve the <code>Log.getLogger()</code><br>
promise every time you wish to log a message. This will combine the lookup<br>
and log into one step.</p>
<p>Note that you cannot perform an <code>isLoggable()</code> check with this method, so<br>
if your log message is expensive to generate, you may want to fully resolve<br>
the logger first.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>level</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_js_rc_log_Log.Level.html">module:nmodule/js/rc/log/Log.Level</a></span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>msg</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>log message</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>args</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                    &lt;repeatable><br>
                
                </td>
            

            

            <td class="description last"><p>additional arguments to use for parameterization</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved when the message has been logged</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".warn"><span class="type-signature">&lt;static> </span>warn()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Logs a message to the <code>browser.console</code> log at <code>WARNING</code> level.<br>
This matches a browser's <code>console.warn</code> API.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
    <pre class="sunlight-highlight-javascript">Log.warn(&#x27;this&#x27;, &#x27;is&#x27;, &#x27;a&#x27;, &#x27;warning&#x27;, &#x27;message&#x27;);</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="addHandler"><span class="type-signature"></span>addHandler(handler)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Add a new handler to this Log. The same handler instance cannot be added<br>
to the same log more than once.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>handler</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_js_rc_log_Log.html#~Handler">module:nmodule/js/rc/log/Log~Handler</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="config"><span class="type-signature"></span>config(msg [, args])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Logs the given message with level <code>CONFIG</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>msg</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>log message</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>args</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                    &lt;repeatable><br>
                
                </td>
            

            

            <td class="description last"><p>additional arguments to use for parameterization</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-nmodule_js_rc_log_Log.html#log">module:nmodule/js/rc/log/Log#log</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="fine"><span class="type-signature"></span>fine(msg [, args])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Logs the given message with level <code>FINE</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>msg</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>log message</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>args</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                    &lt;repeatable><br>
                
                </td>
            

            

            <td class="description last"><p>additional arguments to use for parameterization</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-nmodule_js_rc_log_Log.html#log">module:nmodule/js/rc/log/Log#log</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="finer"><span class="type-signature"></span>finer(msg [, args])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Logs the given message with level <code>FINER</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>msg</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>log message</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>args</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                    &lt;repeatable><br>
                
                </td>
            

            

            <td class="description last"><p>additional arguments to use for parameterization</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-nmodule_js_rc_log_Log.html#log">module:nmodule/js/rc/log/Log#log</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="finest"><span class="type-signature"></span>finest(msg [, args])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Logs the given message with level <code>FINEST</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>msg</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>log message</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>args</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                    &lt;repeatable><br>
                
                </td>
            

            

            <td class="description last"><p>additional arguments to use for parameterization</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-nmodule_js_rc_log_Log.html#log">module:nmodule/js/rc/log/Log#log</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getLevel"><span class="type-signature"></span>getLevel()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the level configured for this log.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-nmodule_js_rc_log_Log.Level.html">module:nmodule/js/rc/log/Log.Level</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getName"><span class="type-signature"></span>getName()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the log's name.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">string</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="info"><span class="type-signature"></span>info(msg [, args])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Logs the given message with level <code>INFO</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>msg</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>log message</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>args</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                    &lt;repeatable><br>
                
                </td>
            

            

            <td class="description last"><p>additional arguments to use for parameterization</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-nmodule_js_rc_log_Log.html#log">module:nmodule/js/rc/log/Log#log</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isLoggable"><span class="type-signature"></span>isLoggable(level)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if a log message at the given log level will actually be logged<br>
by this logger. Use this to improve performance if a log message would be<br>
expensive to create.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>level</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_js_rc_log_Log.Level.html">module:nmodule/js/rc/log/Log.Level</a></span>
|

<span class="param-type">string</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="log"><span class="type-signature"></span>log(level, msg [, args])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Log the given message, giving all <code>Handler</code>s attached to this Log a chance<br>
to publish it.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>level</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_js_rc_log_Log.Level.html">module:nmodule/js/rc/log/Log.Level</a></span>
|

<span class="param-type">string</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>Log level object,<br>
or the name of it as a string</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>msg</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>log message</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>args</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                    &lt;repeatable><br>
                
                </td>
            

            

            <td class="description last"><p>additional arguments to use for parameterization. If<br>
the final argument is an Error, it will be logged by itself.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved when all handlers are finished<br>
publishing</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
    <pre class="sunlight-highlight-javascript">const name = promptForName();
log.log(Level.FINE, &#x27;Hello, {}!&#x27;, name);

try {
  doSomething();
} catch (err) {
  log.log(&#x27;SEVERE&#x27;, &#x27;An error occurred at {}&#x27;, new Date(), err);
}</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setLevel"><span class="type-signature"></span>setLevel(level)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Sets the logging level configured on this Log instance.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>level</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_js_rc_log_Log.Level.html">module:nmodule/js/rc/log/Log.Level</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="severe"><span class="type-signature"></span>severe(msg [, args])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Logs the given message with level <code>SEVERE</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>msg</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>log message</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>args</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                    &lt;repeatable><br>
                
                </td>
            

            

            <td class="description last"><p>additional arguments to use for parameterization</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-nmodule_js_rc_log_Log.html#log">module:nmodule/js/rc/log/Log#log</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="time"><span class="type-signature"></span>time(id [, level] [, msg] [, args])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Starts timing a particular operation.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>id</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>a &quot;unique&quot; ID to describe this operation.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>level</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_js_rc_log_Log.Level.html">module:nmodule/js/rc/log/Log.Level</a></span>
|

<span class="param-type">string</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    INFO
                
                </td>
            

            <td class="description last"><p>Log level<br>
object, or the name of it as a string</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>msg</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>a message to log. If omitted, will simply log the ID</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>args</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                    &lt;repeatable><br>
                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>additional arguments to format the message</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    
    <h5>Throws:</h5>
    
            

<dl>
    <dt>
        <div class="param-desc">
        <p>if ID not provided</p>
        </div>
    </dt>
    <dt>
        <dl>
            <dt>
                Type
            </dt>
            <dd>
                
<span class="param-type">Error</span>



            </dd>
        </dl>
    </dt>
</dl>


        

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>a ticket that may be passed to <code>timeEnd</code></p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">number</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="timeEnd"><span class="type-signature"></span>timeEnd( [id] [, msg] [, args])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Stops timing a particular operation.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>id</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>
|

<span class="param-type">number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>the ID passed to <code>time()</code> (if duplicates are<br>
found, this will stop the least recently started timer). Or, pass the exact<br>
ticket returned from <code>time()</code>. If omitted, this call will be a no-op (this<br>
makes it safe to wrap <code>time()</code> calls in isLoggable checks).</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>msg</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    "id: {}ms"
                
                </td>
            

            <td class="description last"><p>a message to log. For format arguments,<br>
the number of milliseconds elapsed is always the last.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>args</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                    &lt;repeatable><br>
                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>any additional arguments to use to format the message</p>
<ul>
<li>remember elapsed time will always be added</li>
</ul></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="warning"><span class="type-signature"></span>warning(msg [, args])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Logs the given message with level <code>WARNING</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>msg</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>log message</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>args</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                    &lt;repeatable><br>
                
                </td>
            

            

            <td class="description last"><p>additional arguments to use for parameterization</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-nmodule_js_rc_log_Log.html#log">module:nmodule/js/rc/log/Log#log</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        </dl>
    

    
        <h3 class="subsection-title">Type Definitions</h3>

        <dl>
                
<hr>
<dt class="name" id="~Handler">
    <h4 id="~Handler">Handler</h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Responsible for actually publishing a log message to some destination<br>
(console, <code>baja.outln</code>, logfile, etc).</p>
    </div>
    

    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">Object</span>



            </li>
        </ul>
    

    
<dl class="details">
    

    <h5 class="subsection-title">Properties:</h5>

    <dl>

<table class="props table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>publish</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_js_rc_log_Log.html#~PublishCallback">module:nmodule/js/rc/log/Log~PublishCallback</a></span>



            
            </td>

            

            

            <td class="description last"><p>implements<br>
how log messages will be handled</p></td>
        </tr>

    
    </tbody>
</table>
</dl>

    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

            
                
<hr>
<dt>
    <h4 class="name" id="~PublishCallback"><span class="type-signature"></span>PublishCallback(name, level, msg)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>When adding a custom Handler, implement the <code>publish</code> function to define<br>
how log messages will be handled.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            

            

            <td class="description last"><p>log name</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>level</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_js_rc_log_Log.Level.html">module:nmodule/js/rc/log/Log.Level</a></span>



            
            </td>

            

            

            <td class="description last"><p>log level</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>msg</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            

            

            <td class="description last"><p>an already fully-formatted log message.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

            </dl>
    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	js Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:57+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>