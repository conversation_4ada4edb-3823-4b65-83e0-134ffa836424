<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.AlarmSpaceConnection" name="AlarmSpaceConnection" packageName="javax.baja.alarm" public="true" interface="true" abstract="true" category="interface">
<description>
AlarmSpaceConnection provides access to a BIAlarmSpace. All reads, writes,&#xa; and deletes are done through the AlarmSpaceConnection.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">11 June 2014</tag>
<tag name="@since">Niagara 4.0</tag>
<implements>
<type class="java.lang.AutoCloseable"/>
</implements>
<!-- javax.baja.alarm.AlarmSpaceConnection.flush() -->
<method name="flush"  public="true" abstract="true">
<description>
Commit any outstanding changes.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.AlarmSpaceConnection.close() -->
<method name="close"  public="true" abstract="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Close the connection.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.AlarmSpaceConnection.append(javax.baja.alarm.BAlarmRecord) -->
<method name="append"  public="true" abstract="true">
<description>
Archive alarm callback.&#xa; Also update the totalAlarmCount, unackedAlarmCount, openAlarmCount,&#xa; and inAlarmCount properties on the record&#x27;s BAlarmClass.
</description>
<parameter name="record">
<type class="javax.baja.alarm.BAlarmRecord"/>
<description>
The alarm record to store.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
<throws>
<type class="javax.baja.alarm.AlarmException"/>
</throws>
</method>

<!-- javax.baja.alarm.AlarmSpaceConnection.update(javax.baja.alarm.BAlarmRecord) -->
<method name="update"  public="true" abstract="true">
<description>
Update an alarm with new information.&#xa; Also update the totalAlarmCount, unackedAlarmCount, openAlarmCount,&#xa; and inAlarmCount properties on the record&#x27;s BAlarmClass.
</description>
<parameter name="record">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
<throws>
<type class="javax.baja.alarm.AlarmException"/>
</throws>
</method>

<!-- javax.baja.alarm.AlarmSpaceConnection.getRecordCount() -->
<method name="getRecordCount"  public="true" abstract="true">
<description>
Get the number of records in the database.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarm.AlarmSpaceConnection.getRecord(javax.baja.util.BUuid) -->
<method name="getRecord"  public="true" abstract="true">
<description>
Get a record by uuid.
</description>
<parameter name="uuid">
<type class="javax.baja.util.BUuid"/>
<description>
The uuid of the record to retrieve.
</description>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmRecord"/>
<description>
Returns the target record or null if no&#xa;   record is found with a matching uuid.
</description>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarm.AlarmSpaceConnection.getOpenAlarmSources() -->
<method name="getOpenAlarmSources"  public="true" abstract="true">
<description>
Get the list of alarm sources that currently have open alarms.&#xa; The result is a Cursor whose elements are of type BAlarmSource.
</description>
<return>
<parameterizedType class="javax.baja.sys.Cursor">
<args>
<type class="javax.baja.alarm.BAlarmSource"/>
</args>
</parameterizedType>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarm.AlarmSpaceConnection.getOpenAlarms() -->
<method name="getOpenAlarms"  public="true" abstract="true">
<description>
Get the open alarms in the database.  An alarm is considered open when:&#xa; &lt;p&gt;&#xa; not (acked and normal) and not (acked and alert)&#xa; &lt;p&gt;&#xa; The alarm reference may be reused so to store a copy of the BAlarmRecord, you must use newCopy().
</description>
<return>
<parameterizedType class="javax.baja.sys.Cursor">
<args>
<type class="javax.baja.alarm.BAlarmRecord"/>
</args>
</parameterizedType>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarm.AlarmSpaceConnection.getAckPendingAlarms() -->
<method name="getAckPendingAlarms"  public="true" abstract="true">
<description>
Get the ackPending alarms in the database.&#xa; &lt;p&gt;&#xa; The alarm reference may be reused so to store a copy of the BAlarmRecord, you must use newCopy().
</description>
<return>
<parameterizedType class="javax.baja.sys.Cursor">
<args>
<type class="javax.baja.alarm.BAlarmRecord"/>
</args>
</parameterizedType>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarm.AlarmSpaceConnection.getAlarmsForSource(javax.baja.naming.BOrdList) -->
<method name="getAlarmsForSource"  public="true" abstract="true">
<description>
Get all alarms for the specified source.  The result&#xa; will be sorted in timestamp order with the oldest alarm first.&#xa; &lt;p&gt;&#xa; The alarm reference may be reused so to store a copy of the BAlarmRecord, you must use newCopy().
</description>
<parameter name="alarmSource">
<type class="javax.baja.naming.BOrdList"/>
</parameter>
<return>
<parameterizedType class="javax.baja.sys.Cursor">
<args>
<type class="javax.baja.alarm.BAlarmRecord"/>
</args>
</parameterizedType>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarm.AlarmSpaceConnection.getOpenAlarmsForSource(javax.baja.naming.BOrdList) -->
<method name="getOpenAlarmsForSource"  public="true" default="true">
<description>
Get the open alarms for the specified source.  The result&#xa; will be sorted in timestamp order with the oldest alarm first.&#xa; &lt;p&gt;&#xa; The alarm reference may be reused so to store a copy of the BAlarmRecord, you must use newCopy().
</description>
<tag name="@since">Niagara 4.4</tag>
<parameter name="alarmSource">
<type class="javax.baja.naming.BOrdList"/>
</parameter>
<return>
<parameterizedType class="javax.baja.sys.Cursor">
<args>
<type class="javax.baja.alarm.BAlarmRecord"/>
</args>
</parameterizedType>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarm.AlarmSpaceConnection.scan() -->
<method name="scan"  public="true" abstract="true">
<description>
Get a cursor for iterating through all record in the database.&#xa; &lt;p&gt;&#xa; The alarm reference may be reused so to store a copy of the BAlarmRecord, you must use newCopy().
</description>
<return>
<parameterizedType class="javax.baja.sys.Cursor">
<args>
<type class="javax.baja.alarm.BAlarmRecord"/>
</args>
</parameterizedType>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
<throws>
<type class="javax.baja.alarm.AlarmException"/>
</throws>
</method>

<!-- javax.baja.alarm.AlarmSpaceConnection.timeQuery(javax.baja.sys.BAbsTime, javax.baja.sys.BAbsTime) -->
<method name="timeQuery"  public="true" abstract="true">
<description>
Get a cursor for iterating through all records between&#xa; the specified start and end times inclusive.&#xa; &lt;p&gt;&#xa; The alarm reference may be reused so to store a copy of the BAlarmRecord, you must use newCopy().
</description>
<parameter name="start">
<type class="javax.baja.sys.BAbsTime"/>
<description>
The earliest timestamp that will be included&#xa;   in the result.
</description>
</parameter>
<parameter name="end">
<type class="javax.baja.sys.BAbsTime"/>
<description>
The latest timestamp that will be included in&#xa;  the result.
</description>
</parameter>
<return>
<parameterizedType class="javax.baja.sys.Cursor">
<args>
<type class="javax.baja.alarm.BAlarmRecord"/>
</args>
</parameterizedType>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
<throws>
<type class="javax.baja.alarm.AlarmException"/>
</throws>
</method>

</class>
</bajadoc>
