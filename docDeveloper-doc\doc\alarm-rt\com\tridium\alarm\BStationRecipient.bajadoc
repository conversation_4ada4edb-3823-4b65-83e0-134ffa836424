<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.BStationRecipient" name="BStationRecipient" packageName="com.tridium.alarm" public="true">
<description>
Recipient for another station.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">27 Jun 02</tag>
<tag name="@version">$Revision: 26$ $Date: 11/15/06 1:36:48 PM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.alarm.BAlarmRecipient"/>
</extends>
<property name="remoteStation" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;remoteStation&lt;/code&gt; property.
</description>
<tag name="@see">#getRemoteStation</tag>
<tag name="@see">#setRemoteStation</tag>
</property>

</class>
</bajadoc>
