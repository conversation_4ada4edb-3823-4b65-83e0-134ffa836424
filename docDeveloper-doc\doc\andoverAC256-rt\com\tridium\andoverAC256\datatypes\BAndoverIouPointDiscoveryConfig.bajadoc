<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.datatypes.BAndoverIouPointDiscoveryConfig" name="BAndoverIouPointDiscoveryConfig" packageName="com.tridium.andoverAC256.datatypes" public="true">
<description>
AndoverAC256 Device Discovery Config, used to govern what&#xa; tables or sub-panel are learned during the learn process
</description>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@creation">4/26/2005 11:15AM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.76</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="iouPoints" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;iouPoints&lt;/code&gt; property.
</description>
<tag name="@see">#getIouPoints</tag>
<tag name="@see">#setIouPoints</tag>
</property>

<property name="startAddress" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;startAddress&lt;/code&gt; property.
</description>
<tag name="@see">#getStartAddress</tag>
<tag name="@see">#setStartAddress</tag>
</property>

<property name="stopAddress" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;stopAddress&lt;/code&gt; property.
</description>
<tag name="@see">#getStopAddress</tag>
<tag name="@see">#setStopAddress</tag>
</property>

</class>
</bajadoc>
