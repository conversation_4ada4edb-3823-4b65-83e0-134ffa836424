<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="alarmOrion" runtimeProfile="rt" name="com.tridium.alarmOrion.conversion">
<description/>
<class packageName="com.tridium.alarmOrion.conversion" name="BAlarmConversion"><description>This is an abstract class that creates a common component&#xa; interface for alarm conversion objects.</description></class>
<class packageName="com.tridium.alarmOrion.conversion" name="BAlarmConversionJob"><description>This is an abstract class that creates a common interface &#xa; for alarm conversion jobs.</description></class>
</package>
</bajadoc>
