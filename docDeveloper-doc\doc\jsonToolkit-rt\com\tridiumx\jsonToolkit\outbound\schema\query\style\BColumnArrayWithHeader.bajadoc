<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.query.style.BColumnArrayWithHeader" name="BColumnArrayWithHeader" packageName="com.tridiumx.jsonToolkit.outbound.schema.query.style" public="true">
<description>
A query result writer as per the &lt;code&gt;<see ref="com.tridiumx.jsonToolkit.outbound.schema.query.style.BColumnArray">BColumnArray</see>&lt;/code&gt; with the column&#xa;  name as the first entry in each array.&#xa;&#xa;  [&#xa;   [a,a1,a2,a3],&#xa;   [b,b1,b2,b3],&#xa;   [c,c1,c2,c3]&#xa;  ]
</description>
<tag name="@author"><PERSON> <PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.style.BColumnArray"/>
</extends>
<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BColumnArrayWithHeader() -->
<constructor name="BColumnArrayWithHeader" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BColumnArrayWithHeader.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BColumnArrayWithHeader.previewText() -->
<method name="previewText"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BString"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BColumnArrayWithHeader.includeHeader() -->
<method name="includeHeader"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BColumnArrayWithHeader.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
