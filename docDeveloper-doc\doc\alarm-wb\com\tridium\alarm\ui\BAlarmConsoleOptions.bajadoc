<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.ui.BAlarmConsoleOptions" name="BAlarmConsoleOptions" packageName="com.tridium.alarm.ui" public="true">
<description>
BAlarmConsoleOptions wraps all configuration&#xa; for an alarm console.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">23 Jul 01</tag>
<tag name="@version">$Revision: 38$ $Date: 12/17/09 11:35:58 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.ui.options.BUserOptions"/>
</extends>
<property name="notesRequiredOnAck" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;notesRequiredOnAck&lt;/code&gt; property.&#xa; prompt the user to enter notes upon acking an alarm
</description>
<tag name="@see">#getNotesRequiredOnAck</tag>
<tag name="@see">#setNotesRequiredOnAck</tag>
</property>

<property name="soundsEnabled" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;soundsEnabled&lt;/code&gt; property.&#xa; If this flag is true, alarms will sound when they&#xa; are received.
</description>
<tag name="@see">#getSoundsEnabled</tag>
<tag name="@see">#setSoundsEnabled</tag>
</property>

<property name="defaultSoundFile" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;defaultSoundFile&lt;/code&gt; property.&#xa; The name of the sound file to play&#xa; when an alarm arrives at the console.&#xa; The file must be of a format supported by&#xa; javax.sound: AIFF, AU, WAV, MIDI 0 and 1.
</description>
<tag name="@see">#getDefaultSoundFile</tag>
<tag name="@see">#setDefaultSoundFile</tag>
</property>

<property name="continuousAlarm" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;continuousAlarm&lt;/code&gt; property.&#xa; If this flag is true, the alarm sound will play&#xa; continuously until silenced.
</description>
<tag name="@see">#getContinuousAlarm</tag>
<tag name="@see">#setContinuousAlarm</tag>
</property>

<property name="continuousAlarmDelay" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;continuousAlarmDelay&lt;/code&gt; property.&#xa; Pause between repeat soudings of a continuous alarm.
</description>
<tag name="@see">#getContinuousAlarmDelay</tag>
<tag name="@see">#setContinuousAlarmDelay</tag>
</property>

<property name="alarmFilters" flags="h">
<type class="com.tridium.bql.filter.BFilterSet"/>
<description>
Slot for the &lt;code&gt;alarmFilters&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmFilters</tag>
<tag name="@see">#setAlarmFilters</tag>
</property>

<property name="lowPriorityColor" flags="">
<type class="javax.baja.gx.BColor"/>
<description>
Slot for the &lt;code&gt;lowPriorityColor&lt;/code&gt; property.
</description>
<tag name="@see">#getLowPriorityColor</tag>
<tag name="@see">#setLowPriorityColor</tag>
</property>

<property name="midPriorityColor" flags="">
<type class="javax.baja.gx.BColor"/>
<description>
Slot for the &lt;code&gt;midPriorityColor&lt;/code&gt; property.
</description>
<tag name="@see">#getMidPriorityColor</tag>
<tag name="@see">#setMidPriorityColor</tag>
</property>

<property name="highPriorityColor" flags="">
<type class="javax.baja.gx.BColor"/>
<description>
Slot for the &lt;code&gt;highPriorityColor&lt;/code&gt; property.
</description>
<tag name="@see">#getHighPriorityColor</tag>
<tag name="@see">#setHighPriorityColor</tag>
</property>

<property name="timeZoneDisplay" flags="">
<type class="com.tridium.alarm.ui.BTimeZoneDisplay"/>
<description>
Slot for the &lt;code&gt;timeZoneDisplay&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeZoneDisplay</tag>
<tag name="@see">#setTimeZoneDisplay</tag>
</property>

<property name="displayColumns" flags="h">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;displayColumns&lt;/code&gt; property.
</description>
<tag name="@see">#getDisplayColumns</tag>
<tag name="@see">#setDisplayColumns</tag>
</property>

<property name="sortColumn" flags="h">
<type class="int"/>
<description>
Slot for the &lt;code&gt;sortColumn&lt;/code&gt; property.
</description>
<tag name="@see">#getSortColumn</tag>
<tag name="@see">#setSortColumn</tag>
</property>

<property name="sortAscending" flags="h">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;sortAscending&lt;/code&gt; property.
</description>
<tag name="@see">#getSortAscending</tag>
<tag name="@see">#setSortAscending</tag>
</property>

<property name="alarmDataCols" flags="h">
<type class="javax.baja.sys.BVector"/>
<description>
Slot for the &lt;code&gt;alarmDataCols&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmDataCols</tag>
<tag name="@see">#setAlarmDataCols</tag>
</property>

<property name="alarmClassMapping" flags="">
<type class="com.tridium.alarm.ui.BAlarmClassMapping"/>
<description>
Slot for the &lt;code&gt;alarmClassMapping&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmClassMapping</tag>
<tag name="@see">#setAlarmClassMapping</tag>
</property>

<property name="alarmAckResponses" flags="">
<type class="javax.baja.sys.BVector"/>
<description>
Slot for the &lt;code&gt;alarmAckResponses&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmAckResponses</tag>
<tag name="@see">#setAlarmAckResponses</tag>
</property>

<property name="viewInstructions" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;viewInstructions&lt;/code&gt; property.
</description>
<tag name="@see">#getViewInstructions</tag>
<tag name="@see">#setViewInstructions</tag>
</property>

<property name="instructionsDividerPosition" flags="h">
<type class="double"/>
<description>
Slot for the &lt;code&gt;instructionsDividerPosition&lt;/code&gt; property.
</description>
<tag name="@see">#getInstructionsDividerPosition</tag>
<tag name="@see">#setInstructionsDividerPosition</tag>
</property>

</class>
</bajadoc>
