<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector" name="BJsonArrayForEachSelector" packageName="com.tridiumx.jsonToolkit.inbound.selector" public="true">
<description>
JSON selector which passes each value of a JSON array to it&#x27;s output&#xa; slots in sequence, with an intermediate delay. An internal engine cycle&#xa; queue buffers the incoming values.&#xa;&#xa; This component has different string/boolean/numeric output slots for convenience&#xa; in wiresheet logic. Any values not parsable to a numeric are discarded. Any&#xa; values which are not true or String &#x22;true&#x22; are treated as false.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@since">Niagara 4.11</tag>
<extends>
<type class="com.tridiumx.jsonToolkit.inbound.selector.BJsonStringSelector"/>
</extends>
<property name="numericOut" flags="r">
<type class="double"/>
<description>
Slot for the &lt;code&gt;numericOut&lt;/code&gt; property.
</description>
<tag name="@see">#getNumericOut</tag>
<tag name="@see">#setNumericOut</tag>
</property>

<property name="booleanOut" flags="r">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;booleanOut&lt;/code&gt; property.
</description>
<tag name="@see">#getBooleanOut</tag>
<tag name="@see">#setBooleanOut</tag>
</property>

<property name="defaultBetweenItems" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;defaultBetweenItems&lt;/code&gt; property.
</description>
<tag name="@see">#getDefaultBetweenItems</tag>
<tag name="@see">#setDefaultBetweenItems</tag>
</property>

<property name="queue" flags="">
<type class="com.tridiumx.jsonToolkit.util.BEngineCycleMessageQueue"/>
<description>
Slot for the &lt;code&gt;queue&lt;/code&gt; property.
</description>
<tag name="@see">#getQueue</tag>
<tag name="@see">#setQueue</tag>
</property>

<action name="selectValue" flags="h">
<parameter name="parameter">
<type class="javax.baja.sys.BString"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;selectValue&lt;/code&gt; action.
</description>
<tag name="@see">#selectValue(BString parameter)</tag>
</action>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector() -->
<constructor name="BJsonArrayForEachSelector" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.getNumericOut() -->
<method name="getNumericOut"  public="true">
<description>
Get the &lt;code&gt;numericOut&lt;/code&gt; property.
</description>
<tag name="@see">#numericOut</tag>
<return>
<type class="double"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.setNumericOut(double) -->
<method name="setNumericOut"  public="true">
<description>
Set the &lt;code&gt;numericOut&lt;/code&gt; property.
</description>
<tag name="@see">#numericOut</tag>
<parameter name="v">
<type class="double"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.getBooleanOut() -->
<method name="getBooleanOut"  public="true">
<description>
Get the &lt;code&gt;booleanOut&lt;/code&gt; property.
</description>
<tag name="@see">#booleanOut</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.setBooleanOut(boolean) -->
<method name="setBooleanOut"  public="true">
<description>
Set the &lt;code&gt;booleanOut&lt;/code&gt; property.
</description>
<tag name="@see">#booleanOut</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.getDefaultBetweenItems() -->
<method name="getDefaultBetweenItems"  public="true">
<description>
Get the &lt;code&gt;defaultBetweenItems&lt;/code&gt; property.
</description>
<tag name="@see">#defaultBetweenItems</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.setDefaultBetweenItems(boolean) -->
<method name="setDefaultBetweenItems"  public="true">
<description>
Set the &lt;code&gt;defaultBetweenItems&lt;/code&gt; property.
</description>
<tag name="@see">#defaultBetweenItems</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.getQueue() -->
<method name="getQueue"  public="true">
<description>
Get the &lt;code&gt;queue&lt;/code&gt; property.
</description>
<tag name="@see">#queue</tag>
<return>
<type class="com.tridiumx.jsonToolkit.util.BEngineCycleMessageQueue"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.setQueue(com.tridiumx.jsonToolkit.util.BEngineCycleMessageQueue) -->
<method name="setQueue"  public="true">
<description>
Set the &lt;code&gt;queue&lt;/code&gt; property.
</description>
<tag name="@see">#queue</tag>
<parameter name="v">
<type class="com.tridiumx.jsonToolkit.util.BEngineCycleMessageQueue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.selectValue(javax.baja.sys.BString) -->
<method name="selectValue"  public="true">
<description>
Invoke the &lt;code&gt;selectValue&lt;/code&gt; action.
</description>
<tag name="@see">#selectValue</tag>
<parameter name="parameter">
<type class="javax.baja.sys.BString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.started() -->
<method name="started"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.routeValue(javax.baja.sys.BString, javax.baja.sys.Context) -->
<method name="routeValue"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="message">
<type class="javax.baja.sys.BString"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.inbound.routing.RoutingFailedException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.doSelectValue(javax.baja.sys.BString) -->
<method name="doSelectValue"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unused&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Action which should only be used by internal queue logic, passes the current&#xa; JSON value in as a parameter, which is then assigned to the output slots.
</description>
<parameter name="value">
<type class="javax.baja.sys.BString"/>
<description>
String wrapping the current json value.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.implOutputsToClear() -->
<method name="implOutputsToClear"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<parameterizedType class="java.util.List">
<args>
<type class="javax.baja.sys.Property"/>
</args>
</parameterizedType>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.numericOut -->
<field name="numericOut"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;numericOut&lt;/code&gt; property.
</description>
<tag name="@see">#getNumericOut</tag>
<tag name="@see">#setNumericOut</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.booleanOut -->
<field name="booleanOut"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;booleanOut&lt;/code&gt; property.
</description>
<tag name="@see">#getBooleanOut</tag>
<tag name="@see">#setBooleanOut</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.defaultBetweenItems -->
<field name="defaultBetweenItems"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;defaultBetweenItems&lt;/code&gt; property.
</description>
<tag name="@see">#getDefaultBetweenItems</tag>
<tag name="@see">#setDefaultBetweenItems</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.queue -->
<field name="queue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;queue&lt;/code&gt; property.
</description>
<tag name="@see">#getQueue</tag>
<tag name="@see">#setQueue</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.selectValue -->
<field name="selectValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;selectValue&lt;/code&gt; action.
</description>
<tag name="@see">#selectValue(BString parameter)</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonArrayForEachSelector.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
