<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="alarm" runtimeProfile="rt" name="com.tridium.alarm">
<description/>
<class packageName="com.tridium.alarm" name="BAlarmConsoleChannel"><description>BAlarmConsoleChannel is used by BConsoleRecipient to handle messages between&#xa; the BConsoleRecipient and BAlarmConsole.</description></class>
<class packageName="com.tridium.alarm" name="BAlarmExtStatusJob"><description>BAlarmExtStatusJob is used to enable/disable a group of&#xa; BAlarmSourceExts.</description></class>
<class packageName="com.tridium.alarm" name="BAlarmFilterSet"><description>BAlarmFilterSet is a set of BIFilters customized to incorporate &#xa; the specific alarm filter requirements.</description></class>
<class packageName="com.tridium.alarm" name="BAlarmInputTable"><description>BAlarmTable is a table of alarm records.</description></class>
<class packageName="com.tridium.alarm" name="BConsoleRecipient"><description>Recipient for an alarm console.</description></class>
<class packageName="com.tridium.alarm" name="BStationRecipient"><description>Recipient for another station.</description></class>
<class packageName="com.tridium.alarm" name="BTextCustomizer"><description>Basic rules based text customization.</description></class>
<class packageName="com.tridium.alarm" name="BTextOp"/>
<class packageName="com.tridium.alarm" name="BIAlarmRecordDecorator" category="interface"><description>THIS CLASS IS INTENDED FOR FRAMEWORK USE ONLY! ANY UNEXPECTED IMPLEMENTATIONS WILL BE REJECTED&#xa; WHEN ATTEMPTING TO REGISTER VIA &lt;code&gt;<see ref="com.tridium.alarm.BIAlarmRecordDecorator#registerAlarmRecordDecorator(com.tridium.alarm.BIAlarmRecordDecorator)">#registerAlarmRecordDecorator(BIAlarmRecordDecorator)</see>&lt;/code&gt;.</description></class>
<class packageName="com.tridium.alarm" name="BIUpdatableAlarmSource" category="interface"><description>The BIUpdatableAlarmSource interface must be implemented by all&#xa; BObjects capable of handling updated alarms</description></class>
</package>
</bajadoc>
