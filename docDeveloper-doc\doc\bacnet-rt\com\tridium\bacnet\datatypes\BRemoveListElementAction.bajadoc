<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.datatypes.BRemoveListElementAction" name="BRemoveListElementAction" packageName="com.tridium.bacnet.datatypes" public="true">
<description>
BRemoveListElementAction is used to create the dynamic action&#xa; for removing list elements from a BBacnetListOf.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">09 Sep 03</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BAction"/>
</extends>
<property name="parameterTypeSpec" flags="hr">
<type class="javax.baja.util.BTypeSpec"/>
<description>
Slot for the &lt;code&gt;parameterTypeSpec&lt;/code&gt; property.
</description>
<tag name="@see">#getParameterTypeSpec</tag>
<tag name="@see">#setParameterTypeSpec</tag>
</property>

</class>
</bajadoc>
