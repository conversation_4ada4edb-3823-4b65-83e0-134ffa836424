<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.BAceIpcNetwork" name="BAceIpcNetwork" packageName="com.tridium.ace" public="true" abstract="true">
<description>
BAceIpcNetwork is BAceNetwork implementation for ACE instance running in same platform os.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">1/15/2018</tag>
<extends>
<type class="com.tridium.ace.BAceNetwork"/>
</extends>
<property name="local" flags="">
<type class="com.tridium.ace.BAceDevice"/>
<description>
Slot for the &lt;code&gt;local&lt;/code&gt; property.
</description>
<tag name="@see">#getLocal</tag>
<tag name="@see">#setLocal</tag>
</property>

<action name="heartBeatTmo" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;heartBeatTmo&lt;/code&gt; action.
</description>
<tag name="@see">#heartBeatTmo()</tag>
</action>

</class>
</bajadoc>
