<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.BSession" name="BSession" packageName="javax.baja.naming" public="true" abstract="true">
<description>
BSession
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 May 03</tag>
<tag name="@version">$Revision: 4$ $Date: 3/28/05 9:23:01 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.nav.BNavContainer"/>
</extends>
<implements>
<type class="javax.baja.naming.BISession"/>
</implements>
<!-- javax.baja.naming.BSession(java.lang.String, javax.baja.util.LexiconText) -->
<constructor name="BSession" protected="true">
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="lexText">
<type class="javax.baja.util.LexiconText"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.naming.BSession(java.lang.String) -->
<constructor name="BSession" protected="true">
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.naming.BSession.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.naming.BSession.getSessionContext() -->
<method name="getSessionContext"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the Context to use for this session.  Default &#xa; implementation returns null.
</description>
<return>
<type class="javax.baja.sys.Context"/>
</return>
</method>

<!-- javax.baja.naming.BSession.getSessionInfo() -->
<method name="getSessionInfo"  public="true">
<description/>
<return>
<type class="com.tridium.util.BSessionInfo"/>
</return>
</method>

<!-- javax.baja.naming.BSession.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
