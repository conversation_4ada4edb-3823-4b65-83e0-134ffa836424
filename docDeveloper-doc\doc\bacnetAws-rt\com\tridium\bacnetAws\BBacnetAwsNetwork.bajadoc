<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="com.tridium.bacnetAws.BBacnetAwsNetwork" name="BBacnetAwsNetwork" packageName="com.tridium.bacnetAws" public="true">
<description>
BBacnetAwsNetwork is the base container for the Tridium Bacnet Advanced Workstation&#xa; service.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON> on 09 Sep 2009</tag>
<tag name="@since">Niagara 3.5</tag>
<extends>
<type class="com.tridium.bacnetOws.BBacnetOwsNetwork"/>
</extends>
</class>
</bajadoc>
