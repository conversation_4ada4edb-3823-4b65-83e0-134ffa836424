<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetPositiveIntegerValueDescriptor" name="BBacnetPositiveIntegerValueDescriptor" packageName="javax.baja.bacnet.export" public="true">
<description>
BBacnetPositiveIntegerValueDescriptor exposes a ControlPoint as a Bacnet&#xa; Positive Integer Value Descriptor.
</description>
<tag name="@author"><PERSON> on 15 Apr 15</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.export.BBacnetAnalogValueDescriptor"/>
</extends>
<!-- javax.baja.bacnet.export.BBacnetPositiveIntegerValueDescriptor() -->
<constructor name="BBacnetPositiveIntegerValueDescriptor" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetPositiveIntegerValueDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetPositiveIntegerValueDescriptor.validate() -->
<method name="validate"  protected="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the BACnet status flags to fault if the Niagara&#xa; value is disallowed for the exposed BACnet object type.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetPositiveIntegerValueDescriptor.convertToAsn(double) -->
<method name="convertToAsn"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Override hook for IntegerValue&#xa; This is a lossy conversion
</description>
<parameter name="value">
<type class="double"/>
<description>
double value to convert to asn.1
</description>
</parameter>
<return>
<type class="byte" dimension="1"/>
<description>
byte[] containing the required asn.1 formatted numeric value
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetPositiveIntegerValueDescriptor.convertFromAsn(byte[]) -->
<method name="convertFromAsn"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Override hook for IntegerValue&#xa; &lt;p&gt;&#xa; This is a limited conversion to 2^53
</description>
<parameter name="value">
<type class="byte" dimension="1"/>
<description>
asn.1 byte array containing a number
</description>
</parameter>
<return>
<type class="double"/>
<description>
the number decoded from the byte[]
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
<description>
if the array does not contain a properly
</description>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetPositiveIntegerValueDescriptor.addRequiredProps(java.util.Vector) -->
<method name="addRequiredProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Subclass override method to add required properties.&#xa; outOfService property is required for AnalogPointDescriptor but not for IntegerValue&#xa; Remove those optional properties from required
</description>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
Vector containing required propertyIds.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetPositiveIntegerValueDescriptor.addOptionalProps(java.util.Vector) -->
<method name="addOptionalProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Subclass override method to add optional properties.&#xa; NOTE: You MUST call super.addOptionalProps(v) first!
</description>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
Vector containing optional propertyIds.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetPositiveIntegerValueDescriptor.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this point is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetPositiveIntegerValueDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
