<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotDetail" name="BAddSlotDetail" packageName="com.tridiumx.jsonToolkit.inbound.routing.slot" public="true">
<description>
Provides action arguments when adding a new slot to a Json Router.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="slotName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;slotName&lt;/code&gt; property.
</description>
<tag name="@see">#getSlotName</tag>
<tag name="@see">#setSlotName</tag>
</property>

<property name="slotType" flags="">
<type class="com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum"/>
<description>
Slot for the &lt;code&gt;slotType&lt;/code&gt; property.
</description>
<tag name="@see">#getSlotType</tag>
<tag name="@see">#setSlotType</tag>
</property>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotDetail() -->
<constructor name="BAddSlotDetail" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotDetail.getSlotName() -->
<method name="getSlotName"  public="true">
<description>
Get the &lt;code&gt;slotName&lt;/code&gt; property.
</description>
<tag name="@see">#slotName</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotDetail.setSlotName(java.lang.String) -->
<method name="setSlotName"  public="true">
<description>
Set the &lt;code&gt;slotName&lt;/code&gt; property.
</description>
<tag name="@see">#slotName</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotDetail.getSlotType() -->
<method name="getSlotType"  public="true">
<description>
Get the &lt;code&gt;slotType&lt;/code&gt; property.
</description>
<tag name="@see">#slotType</tag>
<return>
<type class="com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotDetail.setSlotType(com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum) -->
<method name="setSlotType"  public="true">
<description>
Set the &lt;code&gt;slotType&lt;/code&gt; property.
</description>
<tag name="@see">#slotType</tag>
<parameter name="v">
<type class="com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotDetail.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotDetail.make(java.lang.String, com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum) -->
<method name="make"  public="true" static="true">
<description/>
<parameter name="slotName">
<type class="java.lang.String"/>
</parameter>
<parameter name="type">
<type class="com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotDetail"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotDetail.slotName -->
<field name="slotName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;slotName&lt;/code&gt; property.
</description>
<tag name="@see">#getSlotName</tag>
<tag name="@see">#setSlotName</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotDetail.slotType -->
<field name="slotType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;slotType&lt;/code&gt; property.
</description>
<tag name="@see">#getSlotType</tag>
<tag name="@see">#setSlotType</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotDetail.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
