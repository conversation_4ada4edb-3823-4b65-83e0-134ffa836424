<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportDeregistrationHandler" name="BJsonExportDeregistrationHandler" packageName="com.tridiumx.jsonToolkit.inbound.handler.exportMarker" public="true">
<description>
Allows the cloud platform to remove it&#x27;s identifier from export marked points,&#xa; effectively &#x22;unregistering&#x22; them.&#xa;&#xa;  {&#xa;    &#x22;messageType&#x22; : &#x22;deregisterId&#x22;&#xa;    &#x22;platformId&#x22; : &#x22;mooseForce123&#x22;,&#xa;  }
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonT<PERSON>kit.inbound.handler.BJsonHandler"/>
</extends>
<property name="remoteKey" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;remoteKey&lt;/code&gt; property.
</description>
<tag name="@see">#getRemoteKey</tag>
<tag name="@see">#setRemoteKey</tag>
</property>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportDeregistrationHandler() -->
<constructor name="BJsonExportDeregistrationHandler" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportDeregistrationHandler.getRemoteKey() -->
<method name="getRemoteKey"  public="true">
<description>
Get the &lt;code&gt;remoteKey&lt;/code&gt; property.
</description>
<tag name="@see">#remoteKey</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportDeregistrationHandler.setRemoteKey(java.lang.String) -->
<method name="setRemoteKey"  public="true">
<description>
Set the &lt;code&gt;remoteKey&lt;/code&gt; property.
</description>
<tag name="@see">#remoteKey</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportDeregistrationHandler.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportDeregistrationHandler.routeValue(javax.baja.sys.BString, javax.baja.sys.Context) -->
<method name="routeValue"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="message">
<type class="javax.baja.sys.BString"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportDeregistrationHandler.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportDeregistrationHandler.getRerunTriggers() -->
<method name="getRerunTriggers"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Property" dimension="1"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportDeregistrationHandler.remoteKey -->
<field name="remoteKey"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;remoteKey&lt;/code&gt; property.
</description>
<tag name="@see">#getRemoteKey</tag>
<tag name="@see">#setRemoteKey</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportDeregistrationHandler.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportDeregistrationHandler.log -->
<field name="log"  protected="true" static="true" final="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

</class>
</bajadoc>
