<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="javax.baja.bacnetAws.config.BBacnetCommand" name="BBacnetCommand" packageName="javax.baja.bacnetAws.config" public="true">
<description/>
<extends>
<type class="javax.baja.bacnet.BBacnetObject"/>
</extends>
<property name="presentValue" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</property>

<property name="facets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.&#xa; They can be used to describe the various actions available.
</description>
<tag name="@see">#getFacets</tag>
<tag name="@see">#setFacets</tag>
</property>

<property name="inProcess" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;inProcess&lt;/code&gt; property.
</description>
<tag name="@see">#getInProcess</tag>
<tag name="@see">#setInProcess</tag>
</property>

<property name="allWritesSuccessful" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;allWritesSuccessful&lt;/code&gt; property.
</description>
<tag name="@see">#getAllWritesSuccessful</tag>
<tag name="@see">#setAllWritesSuccessful</tag>
</property>

<property name="action" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
<description>
Slot for the &lt;code&gt;action&lt;/code&gt; property.
</description>
<tag name="@see">#getAction</tag>
<tag name="@see">#setAction</tag>
</property>

<!-- javax.baja.bacnetAws.config.BBacnetCommand() -->
<constructor name="BBacnetCommand" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.getPresentValue() -->
<method name="getPresentValue"  public="true">
<description>
Get the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#presentValue</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.setPresentValue(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setPresentValue"  public="true">
<description>
Set the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#presentValue</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.getFacets() -->
<method name="getFacets"  public="true">
<description>
Get the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.&#xa; They can be used to describe the various actions available.
</description>
<tag name="@see">#facets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.setFacets(javax.baja.sys.BFacets) -->
<method name="setFacets"  public="true">
<description>
Set the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.&#xa; They can be used to describe the various actions available.
</description>
<tag name="@see">#facets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.getInProcess() -->
<method name="getInProcess"  public="true">
<description>
Get the &lt;code&gt;inProcess&lt;/code&gt; property.
</description>
<tag name="@see">#inProcess</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.setInProcess(boolean) -->
<method name="setInProcess"  public="true">
<description>
Set the &lt;code&gt;inProcess&lt;/code&gt; property.
</description>
<tag name="@see">#inProcess</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.getAllWritesSuccessful() -->
<method name="getAllWritesSuccessful"  public="true">
<description>
Get the &lt;code&gt;allWritesSuccessful&lt;/code&gt; property.
</description>
<tag name="@see">#allWritesSuccessful</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.setAllWritesSuccessful(boolean) -->
<method name="setAllWritesSuccessful"  public="true">
<description>
Set the &lt;code&gt;allWritesSuccessful&lt;/code&gt; property.
</description>
<tag name="@see">#allWritesSuccessful</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.getAction() -->
<method name="getAction"  public="true">
<description>
Get the &lt;code&gt;action&lt;/code&gt; property.
</description>
<tag name="@see">#action</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.setAction(javax.baja.bacnet.datatypes.BBacnetArray) -->
<method name="setAction"  public="true">
<description>
Set the &lt;code&gt;action&lt;/code&gt; property.
</description>
<tag name="@see">#action</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.presentValue -->
<field name="presentValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.facets -->
<field name="facets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.&#xa; They can be used to describe the various actions available.
</description>
<tag name="@see">#getFacets</tag>
<tag name="@see">#setFacets</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.inProcess -->
<field name="inProcess"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;inProcess&lt;/code&gt; property.
</description>
<tag name="@see">#getInProcess</tag>
<tag name="@see">#setInProcess</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.allWritesSuccessful -->
<field name="allWritesSuccessful"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;allWritesSuccessful&lt;/code&gt; property.
</description>
<tag name="@see">#getAllWritesSuccessful</tag>
<tag name="@see">#setAllWritesSuccessful</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.action -->
<field name="action"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;action&lt;/code&gt; property.
</description>
<tag name="@see">#getAction</tag>
<tag name="@see">#setAction</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetCommand.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
