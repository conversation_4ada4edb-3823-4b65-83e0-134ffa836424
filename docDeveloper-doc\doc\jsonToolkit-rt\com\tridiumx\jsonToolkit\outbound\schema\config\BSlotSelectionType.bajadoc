<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType" name="BSlotSelectionType" packageName="com.tridiumx.jsonToolkit.outbound.schema.config" public="true" final="true">
<description>
The different options for which slots from the ord target&#xa; to include in a bound container such as a json object or array.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;allSlots&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;allVisibleSlots&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;summarySlots&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;selectedSlots&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
<elementValue name="defaultValue">
<annotationValue kind="expr">
<expression>&#x22;allVisibleSlots&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType.ALL_SLOTS -->
<field name="ALL_SLOTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for allSlots.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType.ALL_VISIBLE_SLOTS -->
<field name="ALL_VISIBLE_SLOTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for allVisibleSlots.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType.SUMMARY_SLOTS -->
<field name="SUMMARY_SLOTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for summarySlots.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType.SELECTED_SLOTS -->
<field name="SELECTED_SLOTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for selectedSlots.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType.allSlots -->
<field name="allSlots"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType"/>
<description>
BSlotSelectionType constant for allSlots.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType.allVisibleSlots -->
<field name="allVisibleSlots"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType"/>
<description>
BSlotSelectionType constant for allVisibleSlots.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType.summarySlots -->
<field name="summarySlots"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType"/>
<description>
BSlotSelectionType constant for summarySlots.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType.selectedSlots -->
<field name="selectedSlots"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType"/>
<description>
BSlotSelectionType constant for selectedSlots.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BSlotSelectionType.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
