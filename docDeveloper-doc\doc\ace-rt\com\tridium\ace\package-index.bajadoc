<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="ace" runtimeProfile="rt" name="com.tridium.ace">
<description/>
<class packageName="com.tridium.ace" name="BAceDevice"><description>BAceDevice models a single device</description></class>
<class packageName="com.tridium.ace" name="BAceDeviceFolder"><description>BAceDeviceFolder is a folder for BAceDevice.</description></class>
<class packageName="com.tridium.ace" name="BAceIpcNetwork"><description>BAceIpcNetwork is BAceNetwork implementation for ACE instance running in same platform os.</description></class>
<class packageName="com.tridium.ace" name="BAceNetwork"><description>BAceNetwork is base class for network of Ace devices.</description></class>
</package>
</bajadoc>
