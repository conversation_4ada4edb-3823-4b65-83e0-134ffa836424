<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.req.BInfinityReadPointRequest" name="BInfinityReadPointRequest" packageName="com.tridium.andoverInfinity.comm.req" public="true">
<description>
Message to read a points value
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.comm.req.BDdfReadRequest"/>
</extends>
<implements>
<type class="com.tridium.andoverInfinity.comm.req.RequiresNetworkAccess"/>
</implements>
<implements>
<type class="com.tridium.andoverInfinity.comm.Vt100Const"/>
</implements>
<implements>
<type class="com.tridium.ddf.comm.req.BIDdfPingRequest"/>
</implements>
<!-- com.tridium.andoverInfinity.comm.req.BInfinityReadPointRequest() -->
<constructor name="BInfinityReadPointRequest" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReadPointRequest.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReadPointRequest.processReceive(com.tridium.ddf.comm.IDdfDataFrame) -->
<method name="processReceive"  public="true">
<description>
This is a bogus comment
</description>
<tag name="@see">com.tridium.devDriver.comm.req.BIDdfRequest#processReceive(com.tridium.devDriver.comm.IDevDataFrame)</tag>
<parameter name="iDevDataFrame">
<type class="com.tridium.ddf.comm.IDdfDataFrame"/>
</parameter>
<return>
<type class="com.tridium.ddf.comm.rsp.BIDdfResponse"/>
</return>
<throws>
<type class="com.tridium.ddf.comm.rsp.DdfResponseException"/>
</throws>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReadPointRequest.toByteArray() -->
<method name="toByteArray"  public="true">
<description/>
<return>
<type class="byte" dimension="1"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReadPointRequest.getAutoPingFailOnError() -->
<method name="getAutoPingFailOnError"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReadPointRequest.getAutoPingFailOnTimeout() -->
<method name="getAutoPingFailOnTimeout"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReadPointRequest.getAutoPingOk() -->
<method name="getAutoPingOk"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReadPointRequest.getAutoPingOkLate() -->
<method name="getAutoPingOkLate"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReadPointRequest.getPingableSource() -->
<method name="getPingableSource"  public="true">
<description/>
<return>
<type class="com.tridium.ddf.comm.req.IDdfPingable" dimension="1"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReadPointRequest.setPingableSource(com.tridium.ddf.comm.req.IDdfPingable[]) -->
<method name="setPingableSource"  public="true">
<description/>
<parameter name="pingableSource">
<type class="com.tridium.ddf.comm.req.IDdfPingable" dimension="1"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReadPointRequest.setNetwork(com.tridium.andoverInfinity.BInfinityNetwork) -->
<method name="setNetwork"  public="true">
<description>
Implementation of RequiresNetworkAccess interface
</description>
<tag name="@see">com.tridium.andoverInfinity.comm.req.RequiresNetworkAccess#setNetwork(com.tridium.andoverInfinity.BInfinityNetwork)</tag>
<parameter name="network">
<type class="com.tridium.andoverInfinity.BInfinityNetwork"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityReadPointRequest.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
