<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.firewall.BServerPort" name="BServerPort" packageName="javax.baja.firewall" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@creation">11/18/2014</tag>
<tag name="@since">Niagara 4.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.sys.BIUnlinkableSlotsContainer"/>
</implements>
<property name="publicServerPort" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;publicServerPort&lt;/code&gt; property.&#xa; This represents the external port that would be used for connections to this device.
</description>
<tag name="@see">#getPublicServerPort</tag>
<tag name="@see">#setPublicServerPort</tag>
</property>

<property name="localServerPort" flags="h">
<type class="int"/>
<description>
Slot for the &lt;code&gt;localServerPort&lt;/code&gt; property.&#xa; This represents the port that the local process may bind for incoming connections. 0 means to use the calculated value of serverPort + 8000
</description>
<tag name="@see">#getLocalServerPort</tag>
<tag name="@see">#setLocalServerPort</tag>
</property>

<property name="ipProtocol" flags="r">
<type class="javax.baja.firewall.BIpProtocolEnum"/>
<description>
Slot for the &lt;code&gt;ipProtocol&lt;/code&gt; property.&#xa; This is an enumeration that identifies what protocol is allowed, values are tcp, udp, any.
</description>
<tag name="@see">#getIpProtocol</tag>
<tag name="@see">#setIpProtocol</tag>
</property>

<property name="adapter" flags="rh">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;adapter&lt;/code&gt; property.&#xa; This represents the adapter to bind to the external port. Currently only &#x27;any&#x27; is supported.
</description>
<tag name="@see">#getAdapter</tag>
<tag name="@see">#setAdapter</tag>
</property>

<!-- javax.baja.firewall.BServerPort() -->
<constructor name="BServerPort" public="true">
<description/>
</constructor>

<!-- javax.baja.firewall.BServerPort(int) -->
<constructor name="BServerPort" public="true">
<parameter name="publicServerPort">
<type class="int"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.firewall.BServerPort(int, com.tridium.nre.firewall.IpProtocol) -->
<constructor name="BServerPort" public="true">
<parameter name="publicServerPort">
<type class="int"/>
</parameter>
<parameter name="protocol">
<type class="com.tridium.nre.firewall.IpProtocol"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.firewall.BServerPort(int, int, com.tridium.nre.firewall.IpProtocol) -->
<constructor name="BServerPort" public="true">
<parameter name="publicServerPort">
<type class="int"/>
</parameter>
<parameter name="localServerPort">
<type class="int"/>
</parameter>
<parameter name="protocol">
<type class="com.tridium.nre.firewall.IpProtocol"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.firewall.BServerPort.getPublicServerPort() -->
<method name="getPublicServerPort"  public="true">
<description>
Get the &lt;code&gt;publicServerPort&lt;/code&gt; property.&#xa; This represents the external port that would be used for connections to this device.
</description>
<tag name="@see">#publicServerPort</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.firewall.BServerPort.setPublicServerPort(int) -->
<method name="setPublicServerPort"  public="true">
<description>
Set the &lt;code&gt;publicServerPort&lt;/code&gt; property.&#xa; This represents the external port that would be used for connections to this device.
</description>
<tag name="@see">#publicServerPort</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.firewall.BServerPort.getLocalServerPort() -->
<method name="getLocalServerPort"  public="true">
<description>
Get the &lt;code&gt;localServerPort&lt;/code&gt; property.&#xa; This represents the port that the local process may bind for incoming connections. 0 means to use the calculated value of serverPort + 8000
</description>
<tag name="@see">#localServerPort</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.firewall.BServerPort.setLocalServerPort(int) -->
<method name="setLocalServerPort"  public="true">
<description>
Set the &lt;code&gt;localServerPort&lt;/code&gt; property.&#xa; This represents the port that the local process may bind for incoming connections. 0 means to use the calculated value of serverPort + 8000
</description>
<tag name="@see">#localServerPort</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.firewall.BServerPort.getIpProtocol() -->
<method name="getIpProtocol"  public="true">
<description>
Get the &lt;code&gt;ipProtocol&lt;/code&gt; property.&#xa; This is an enumeration that identifies what protocol is allowed, values are tcp, udp, any.
</description>
<tag name="@see">#ipProtocol</tag>
<return>
<type class="javax.baja.firewall.BIpProtocolEnum"/>
</return>
</method>

<!-- javax.baja.firewall.BServerPort.setIpProtocol(javax.baja.firewall.BIpProtocolEnum) -->
<method name="setIpProtocol"  public="true">
<description>
Set the &lt;code&gt;ipProtocol&lt;/code&gt; property.&#xa; This is an enumeration that identifies what protocol is allowed, values are tcp, udp, any.
</description>
<tag name="@see">#ipProtocol</tag>
<parameter name="v">
<type class="javax.baja.firewall.BIpProtocolEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.firewall.BServerPort.getAdapter() -->
<method name="getAdapter"  public="true">
<description>
Get the &lt;code&gt;adapter&lt;/code&gt; property.&#xa; This represents the adapter to bind to the external port. Currently only &#x27;any&#x27; is supported.
</description>
<tag name="@see">#adapter</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.firewall.BServerPort.setAdapter(java.lang.String) -->
<method name="setAdapter"  public="true">
<description>
Set the &lt;code&gt;adapter&lt;/code&gt; property.&#xa; This represents the adapter to bind to the external port. Currently only &#x27;any&#x27; is supported.
</description>
<tag name="@see">#adapter</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.firewall.BServerPort.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.firewall.BServerPort.started() -->
<method name="started"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.firewall.BServerPort.stopped() -->
<method name="stopped"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.firewall.BServerPort.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.firewall.BServerPort.getBindToLoopback() -->
<method name="getBindToLoopback"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.firewall.BServerPort.getBindingPort() -->
<method name="getBindingPort"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.firewall.BServerPort.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.firewall.BServerPort.getRuleList() -->
<method name="getRuleList"  public="true" static="true">
<description/>
<return>
<type class="com.tridium.nre.firewall.FirewallRule" dimension="1"/>
</return>
</method>

<!-- javax.baja.firewall.BServerPort.getFirewallName() -->
<method name="getFirewallName"  public="true" static="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.firewall.BServerPort.getFirewallDescription() -->
<method name="getFirewallDescription"  public="true" static="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.firewall.BServerPort.getUnlinkableTargetSlots(javax.baja.sys.Context) -->
<method name="getUnlinkableTargetSlots"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Overridden to specify properties that are not allowed to be the target of a link.
</description>
<tag name="@since">Niagara 4.10u8</tag>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<parameterizedType class="java.util.Set">
<args>
<type class="javax.baja.sys.Slot"/>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.firewall.BServerPort.publicServerPort -->
<field name="publicServerPort"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;publicServerPort&lt;/code&gt; property.&#xa; This represents the external port that would be used for connections to this device.
</description>
<tag name="@see">#getPublicServerPort</tag>
<tag name="@see">#setPublicServerPort</tag>
</field>

<!-- javax.baja.firewall.BServerPort.localServerPort -->
<field name="localServerPort"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;localServerPort&lt;/code&gt; property.&#xa; This represents the port that the local process may bind for incoming connections. 0 means to use the calculated value of serverPort + 8000
</description>
<tag name="@see">#getLocalServerPort</tag>
<tag name="@see">#setLocalServerPort</tag>
</field>

<!-- javax.baja.firewall.BServerPort.ipProtocol -->
<field name="ipProtocol"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;ipProtocol&lt;/code&gt; property.&#xa; This is an enumeration that identifies what protocol is allowed, values are tcp, udp, any.
</description>
<tag name="@see">#getIpProtocol</tag>
<tag name="@see">#setIpProtocol</tag>
</field>

<!-- javax.baja.firewall.BServerPort.adapter -->
<field name="adapter"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;adapter&lt;/code&gt; property.&#xa; This represents the adapter to bind to the external port. Currently only &#x27;any&#x27; is supported.
</description>
<tag name="@see">#getAdapter</tag>
<tag name="@see">#setAdapter</tag>
</field>

<!-- javax.baja.firewall.BServerPort.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.firewall.BServerPort.LOCALHOST_INTERFACE -->
<field name="LOCALHOST_INTERFACE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

</class>
</bajadoc>
