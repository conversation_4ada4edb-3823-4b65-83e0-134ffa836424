<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="com.tridium.bacnetAws.datatypes.BCommControlConfig" name="BCommControlConfig" packageName="com.tridium.bacnetAws.datatypes" public="true">
<description>
This class file specifies parameters to constrain a&#xa; Device Communication Control request.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">13 Nov 02</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.datatypes.BRequestConfig"/>
</extends>
<property name="deviceAddress" flags="h">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
Slot for the &lt;code&gt;deviceAddress&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceAddress</tag>
<tag name="@see">#setDeviceAddress</tag>
</property>

<property name="characterSet" flags="h">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
<description>
Slot for the &lt;code&gt;characterSet&lt;/code&gt; property.
</description>
<tag name="@see">#getCharacterSet</tag>
<tag name="@see">#setCharacterSet</tag>
</property>

<property name="enableDisable" flags="">
<type class="javax.baja.bacnet.enums.BBacnetCommControl"/>
<description>
Slot for the &lt;code&gt;enableDisable&lt;/code&gt; property.
</description>
<tag name="@see">#getEnableDisable</tag>
<tag name="@see">#setEnableDisable</tag>
</property>

<property name="duration" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;duration&lt;/code&gt; property.
</description>
<tag name="@see">#getDuration</tag>
<tag name="@see">#setDuration</tag>
</property>

<property name="password" flags="">
<type class="javax.baja.security.BPassword"/>
<description>
Slot for the &lt;code&gt;password&lt;/code&gt; property.
</description>
<tag name="@see">#getPassword</tag>
<tag name="@see">#setPassword</tag>
</property>

</class>
</bajadoc>
