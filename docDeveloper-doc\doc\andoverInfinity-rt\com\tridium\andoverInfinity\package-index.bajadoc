<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="andoverInfinity" runtimeProfile="rt" name="com.tridium.andoverInfinity">
<description/>
<class packageName="com.tridium.andoverInfinity" name="BInfinetDevice"><description>BInfinetDevice is the base class for Infinity devices.</description></class>
<class packageName="com.tridium.andoverInfinity" name="BInfinityDeviceFolder"><description>BInfinityDeviceFolder</description></class>
<class packageName="com.tridium.andoverInfinity" name="BInfinityNetwork"><description>BInfinityNetwork extends BDdfSerialNetwork to wrap a custom communicator&#xa; and includes a single frozen slot for a BInfinityNetworkDevice which is&#xa; always present in a system.</description></class>
<class packageName="com.tridium.andoverInfinity" name="BInfinityNetworkDevice"><description>This class models the &#x22;top-level&#x22; device in a network of Infinity devices.</description></class>
<class packageName="com.tridium.andoverInfinity" name="BInfinityPointDeviceExt"><description>BInfinityPointDeviceExt</description></class>
</package>
</bajadoc>
