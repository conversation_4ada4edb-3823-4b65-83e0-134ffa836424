<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.IFileFilter" name="IFileFilter" packageName="javax.baja.file" public="true" interface="true" abstract="true" category="interface">
<description>
IFileFilter.
</description>
<tag name="@author"><PERSON> Frank</tag>
<tag name="@creation">14 Nov 02</tag>
<tag name="@version">$Revision: 3$ $Date: 4/29/03 2:34:54 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<!-- javax.baja.file.IFileFilter.accept(javax.baja.file.BIFile) -->
<method name="accept"  public="true" abstract="true">
<description>
Accept the specified FileNode.
</description>
<parameter name="file">
<type class="javax.baja.file.BIFile"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.IFileFilter.getDescription(javax.baja.sys.Context) -->
<method name="getDescription"  public="true" abstract="true">
<description>
Return the description of this filter.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.IFileFilter.directory -->
<field name="directory"  public="true" static="true" final="true">
<type class="javax.baja.file.IFileFilter"/>
<description>
Accept only directories.
</description>
</field>

<!-- javax.baja.file.IFileFilter.all -->
<field name="all"  public="true" static="true" final="true">
<type class="javax.baja.file.IFileFilter"/>
<description>
Accept all files except directories.
</description>
</field>

</class>
</bajadoc>
