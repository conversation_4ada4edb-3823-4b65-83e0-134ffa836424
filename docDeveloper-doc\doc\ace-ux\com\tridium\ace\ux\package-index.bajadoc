<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="ace" runtimeProfile="ux" name="com.tridium.ace.ux">
<description/>
<class packageName="com.tridium.ace.ux" name="BAceComponentJavaScriptMenuAgent"><description>BAceComponentJavaScriptMenuAgent to remove&#xa; commands for BAceComponents.</description></class>
<class packageName="com.tridium.ace.ux" name="BAceDriverJsBuild"><description>JavaScript build for Ace ux manager views.</description></class>
<class packageName="com.tridium.ace.ux" name="BAcePointUxManager"><description>The AcePointUxManager is an agent on AcePointDeviceExt and AcePointFolder.</description></class>
<class packageName="com.tridium.ace.ux" name="BAcePointUxManagerJavaScriptMenuAgent"><description>A JavaScript Menu Agent for the AcePointUxManager.</description></class>
</package>
</bajadoc>
