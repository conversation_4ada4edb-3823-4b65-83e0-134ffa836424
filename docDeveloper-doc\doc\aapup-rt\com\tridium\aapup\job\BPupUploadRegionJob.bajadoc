<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.job.BPupUploadRegionJob" name="BPupUploadRegionJob" packageName="com.tridium.aapup.job" public="true">
<description>
PUP Upload Region Job&#xa; This job class handles the implementation of upload&#xa; of SPL programs from a given region in a controller to a file&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@creation">8/18/2005 11:07AM</tag>
<tag name="@version">$Revision$ $Date:8/18/2005 11:07AM$</tag>
<tag name="@since">3.0.93</tag>
<extends>
<type class="javax.baja.job.BSimpleJob"/>
</extends>
<implements>
<type class="com.tridium.aapup.AaPupConst"/>
</implements>
</class>
</bajadoc>
