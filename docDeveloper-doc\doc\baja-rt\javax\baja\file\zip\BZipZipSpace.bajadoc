<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.zip.BZipZipSpace" name="BZipZipSpace" packageName="javax.baja.file.zip" public="true">
<description>
BZipZipSpace - space for a zip file in a zip file.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">11/6/13</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.file.zip.BZipSpace"/>
</extends>
<!-- javax.baja.file.zip.BZipZipSpace(javax.baja.file.zip.BZipFile) -->
<constructor name="BZipZipSpace" public="true">
<parameter name="file">
<type class="javax.baja.file.zip.BZipFile"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.file.zip.BZipZipSpace.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipZipSpace.getInputStream(java.util.zip.ZipEntry) -->
<method name="getInputStream"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return stream position at specified zipentry
</description>
<parameter name="zipEntry">
<type class="java.util.zip.ZipEntry"/>
</parameter>
<return>
<type class="java.io.InputStream"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.zip.BZipZipSpace.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
