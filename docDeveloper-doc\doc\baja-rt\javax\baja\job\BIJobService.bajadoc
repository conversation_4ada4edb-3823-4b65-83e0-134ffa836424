<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.job.BIJobService" name="BIJobService" packageName="javax.baja.job" public="true" interface="true" abstract="true" category="interface">
<description>
IJobService provides a common interface used for managing jobs&#xa; consistently in both a station and workbench environment.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">30 Apr 03</tag>
<tag name="@version">$Revision: 3$ $Date: 3/28/05 9:22:58 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<!-- javax.baja.job.BIJobService.getJobs() -->
<method name="getJobs"  public="true" abstract="true">
<description>
Get all the child jobs under this service.
</description>
<return>
<type class="javax.baja.job.BJob" dimension="1"/>
</return>
</method>

<!-- javax.baja.job.BIJobService.submit(javax.baja.job.BJob, javax.baja.sys.Context) -->
<method name="submit"  public="true" abstract="true">
<description>
Submit a job and run it!
</description>
<parameter name="job">
<type class="javax.baja.job.BJob"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.job.BIJobService.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
