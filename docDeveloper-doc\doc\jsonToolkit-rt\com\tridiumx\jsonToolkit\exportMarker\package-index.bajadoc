<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.exportMarker">
<description/>
<class packageName="com.tridiumx.jsonToolkit.exportMarker" name="BJsonExportMarker"><description>This component in added to components which should be found via BQL and exported to a Relative Schema</description></class>
</package>
</bajadoc>
