<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="javax.baja.bacnet.alarm">
<description/>
<class packageName="javax.baja.bacnet.alarm" name="BBacnetAlarmDeviceExt"><description>BBacnetAlarmDeviceExt.</description></class>
<class packageName="javax.baja.bacnet.alarm" name="BBacnetEventProcessor"/>
<class packageName="javax.baja.bacnet.alarm" name="BBacnetStatusAlgorithm"><description>BBacnetStatusAlgorithm defines the offnormal algorithm&#xa; for the &lt;code&gt; BacnetBitStringUtil.BACNET_STATUS_FLAGS &lt;/code&gt; bits of a point.</description></class>
</package>
</bajadoc>
