<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="javax.baja.bacnet.enums">
<description/>
<class packageName="javax.baja.bacnet.enums" name="BBacnetAbortReason"><description>BBacnetAbortReason represents the Bacnet Abort Reason&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetAction"><description>BBacnetAction represents the BACnetAction enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetBackupState"><description>BBacnetBackupState represents the possible states&#xa; of a BACnet device with respect to backup and restore&#xa; procedures.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetBinaryPv"><description>BBacnetBinaryPv represents the Bacnet Binary present value&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetCommControl"><description>BBacnetCommControl represents the communication control enumeration&#xa; defined in the DeviceCommunicationControl-Request definition.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetDeviceStatus"><description>BBacnetDeviceStatus represents the BACnetDeviceStatus&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetEngineeringUnits"><description>BBacnetEngineeringUnits represents the Bacnet&#xa; Engineering Units enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetErrorClass"><description>BBacnetErrorClass represents the error-class portion of the&#xa; BACnet Error sequence.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetErrorCode"><description>BBacnetErrorCode represents the error-code portion of the&#xa; BACnet Error sequence.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetEventState"><description>BBacnetEventState represents the Bacnet Event State&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetEventType"><description>BBacnetEventType represents the Bacnet Event Type&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetFaultType"><description>BBacnetFaultType represents the BACnetFaultType&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetFileAccessMethod"><description>BBacnetFileAccessMethod represents the BacnetFileAccessMethod&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetLifeSafetyMode"><description>BBacnetLifeSafetyMode represents the Bacnet Life Safety Mode&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetLifeSafetyOperation"><description>BBacnetLifeSafetyOperation represents the Bacnet Life Safety Operation&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetLifeSafetyState"><description>BBacnetLifeSafetyState represents the Bacnet Life Safety State&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetLoggingType"><description>BBacnetLoggingType represents the BACnetLoggingType&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetMaintenance"><description>BBacnetMaintenance represents the Bacnet Maintenance&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetNodeType"><description>BBacnetNodeType represents the BACnetNodeType enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetNotifyType"><description>BBacnetNotifyType represents the Bacnet Notify Type&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetObjectType"><description>BBacnetObjectType represents the BACnetObjectType&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetPolarity"><description>BBacnetPolarity represents the Bacnet Polarity&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetProgramError"><description>BBacnetProgramError represents the Bacnet Program Error&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetProgramRequest"><description>BBacnetProgramRequest represents the BACnetProgramRequest&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetProgramState"><description>BBacnetProgramState represents the BACnetProgramState&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetPropertyIdentifier"><description>BBacnetPropertyIdentifier represents the BACnetPropertyIdentifier&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetReinitializedDeviceState"><description>BBacnetReinitializedDeviceState represents the state of a device after&#xa; receiving a ReinitializeDevice-Request.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetRejectReason"><description>BBacnetRejectReason represents the Bacnet Abort Reason&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetReliability"><description>BBacnetReliability represents the BACnetReliability&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetRestartReason"><description>BBacnetRestartReason represents the BACnetRestartReason&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetSegmentation"><description>BBacnetSegmentation represents the Bacnet Segmentation&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetShedState"><description>BBacnetShedState represents the Bacnet Shed State&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetSilencedState"><description>BBacnetSilencedState represents the Bacnet Silenced State&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetVtClass"><description>BBacnetVtClass represents the Bacnet VT Class&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BBacnetWriteStatus"><description>BBacnetWriteStatus represents the BACnetWriteStatus&#xa; enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BCharacterSetEncoding"><description>BCharacterSetEncoding represents the enumeration of character sets&#xa; defined by Bacnet for string encoding.</description></class>
<class packageName="javax.baja.bacnet.enums" name="BExtensibleEnumList"><description>BExtensibleEnumList is a container for managing a device&#x27;s knowledge of&#xa; any proprietary extensions to any of the extensible enumerations&#xa; are defined by Bacnet.</description></class>
</package>
</bajadoc>
