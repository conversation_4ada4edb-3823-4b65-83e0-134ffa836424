<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.BAlarmInputTable" name="BAlarmInputTable" packageName="com.tridium.alarm" public="true">
<description>
BAlarmTable is a table of alarm records.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.sys.BObject"/>
</extends>
<implements>
<parameterizedType class="javax.baja.collection.BITable">
<args>
<type class="javax.baja.alarm.BAlarmRecord"/>
</args>
</parameterizedType>
</implements>
</class>
</bajadoc>
