<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="platHwScan" runtimeProfile="rt" qualifiedName="com.tridium.platHwScan.enums.BPortTypeEnum" name="BPortTypeEnum" packageName="com.tridium.platHwScan.enums" public="true" final="true">
<description>
BPortTypeEnum&#xa;&#xa;    control  - This port is used for control rather than data, such as the second port associated with a GPRS modem.&#xa;    ethernet - This identifies an Ethernet port&#xa;    gprs     - This identifies the data port for a GPRS modem.&#xa;    lon      - This identifies a LON port.&#xa;    modem    - This port is associated with a modem.&#xa;    ndio     - This signifies a NDIO port is present.&#xa;    nrio     - This signifies a NRIO port is present.&#xa;    rs232    - This port supports RS-232 serial communications.&#xa;    rs485    - This port supports RS-485 serial communications.&#xa;    wireless - This port communicates via wireless technology.&#xa;    zwave    - This port communicates via Z-Wave technology.&#xa;    unknown  - This represents an unknown port type.&#xa;    future   - This identifies a port that may be present but is not currently supported.&#xa;    noPort   - This signifies no port exists.
</description>
<tag name="@author">Frank Smith</tag>
<tag name="@creation">24 Jun 11</tag>
<tag name="@version">$Revision: 1$ $Date: 7/22/11 5:19:28 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;control&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ethernet&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;gprs&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lon&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;modem&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ndio&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;nrio&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;rs232&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;rs485&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;wireless&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;zwave&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknown&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;future&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;noPort&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;usb&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;wifi&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
<elementValue name="defaultValue">
<annotationValue kind="expr">
<expression>&#x22;unknown&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
<!-- com.tridium.platHwScan.enums.BPortTypeEnum.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
</return>
</method>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
</return>
</method>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.CONTROL -->
<field name="CONTROL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for control.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.ETHERNET -->
<field name="ETHERNET"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ethernet.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.GPRS -->
<field name="GPRS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for gprs.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.LON -->
<field name="LON"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lon.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.MODEM -->
<field name="MODEM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for modem.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.NDIO -->
<field name="NDIO"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ndio.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.NRIO -->
<field name="NRIO"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for nrio.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.RS_232 -->
<field name="RS_232"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for rs232.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.RS_485 -->
<field name="RS_485"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for rs485.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.WIRELESS -->
<field name="WIRELESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for wireless.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.ZWAVE -->
<field name="ZWAVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for zwave.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.UNKNOWN -->
<field name="UNKNOWN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknown.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.FUTURE -->
<field name="FUTURE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for future.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.NO_PORT -->
<field name="NO_PORT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for noPort.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.USB -->
<field name="USB"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for usb.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.WIFI -->
<field name="WIFI"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for wifi.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.control -->
<field name="control"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
<description>
BPortTypeEnum constant for control.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.ethernet -->
<field name="ethernet"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
<description>
BPortTypeEnum constant for ethernet.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.gprs -->
<field name="gprs"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
<description>
BPortTypeEnum constant for gprs.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.lon -->
<field name="lon"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
<description>
BPortTypeEnum constant for lon.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.modem -->
<field name="modem"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
<description>
BPortTypeEnum constant for modem.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.ndio -->
<field name="ndio"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
<description>
BPortTypeEnum constant for ndio.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.nrio -->
<field name="nrio"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
<description>
BPortTypeEnum constant for nrio.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.rs232 -->
<field name="rs232"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
<description>
BPortTypeEnum constant for rs232.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.rs485 -->
<field name="rs485"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
<description>
BPortTypeEnum constant for rs485.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.wireless -->
<field name="wireless"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
<description>
BPortTypeEnum constant for wireless.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.zwave -->
<field name="zwave"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
<description>
BPortTypeEnum constant for zwave.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.unknown -->
<field name="unknown"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
<description>
BPortTypeEnum constant for unknown.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.future -->
<field name="future"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
<description>
BPortTypeEnum constant for future.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.noPort -->
<field name="noPort"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
<description>
BPortTypeEnum constant for noPort.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.usb -->
<field name="usb"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
<description>
BPortTypeEnum constant for usb.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.wifi -->
<field name="wifi"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
<description>
BPortTypeEnum constant for wifi.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BPortTypeEnum"/>
<description/>
</field>

<!-- com.tridium.platHwScan.enums.BPortTypeEnum.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
