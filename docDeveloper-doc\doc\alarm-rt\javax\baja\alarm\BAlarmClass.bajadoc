<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BAlarmClass" name="BAlarmClass" packageName="javax.baja.alarm" public="true">
<description>
A BAlarmClass object is used to group alarms that have&#xa; the same routing/handling characteristics.&#xa; &lt;p&gt;&#xa; All AlarmSource objects within the framework implicitly&#xa; reference a BAlarmClass instance.  If the BAlarmClass&#xa; instance is invalid or missing, the default BAlarmClass&#xa; will be used.
</description>
<tag name="@author">Dan Giorgis</tag>
<tag name="@creation">19 Feb 01</tag>
<tag name="@version">$Revision: 84$ $Date: 8/31/10 10:14:25 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="ackRequired" flags="">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
<description>
Slot for the &lt;code&gt;ackRequired&lt;/code&gt; property.&#xa; Alarm transition types that require a user&#xa; acknowledgement
</description>
<tag name="@see">#getAckRequired</tag>
<tag name="@see">#setAckRequired</tag>
</property>

<property name="priority" flags="">
<type class="javax.baja.alarm.BAlarmPriorities"/>
<description>
Slot for the &lt;code&gt;priority&lt;/code&gt; property.&#xa; Priority assigned to each transition type
</description>
<tag name="@see">#getPriority</tag>
<tag name="@see">#setPriority</tag>
</property>

<property name="totalAlarmCount" flags="rdt">
<type class="int"/>
<description>
Slot for the &lt;code&gt;totalAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#getTotalAlarmCount</tag>
<tag name="@see">#setTotalAlarmCount</tag>
</property>

<property name="openAlarmCount" flags="rdt">
<type class="int"/>
<description>
Slot for the &lt;code&gt;openAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#getOpenAlarmCount</tag>
<tag name="@see">#setOpenAlarmCount</tag>
</property>

<property name="inAlarmCount" flags="rdt">
<type class="int"/>
<description>
Slot for the &lt;code&gt;inAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#getInAlarmCount</tag>
<tag name="@see">#setInAlarmCount</tag>
</property>

<property name="unackedAlarmCount" flags="rdt">
<type class="int"/>
<description>
Slot for the &lt;code&gt;unackedAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#getUnackedAlarmCount</tag>
<tag name="@see">#setUnackedAlarmCount</tag>
</property>

<property name="timeOfLastAlarm" flags="rd">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;timeOfLastAlarm&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeOfLastAlarm</tag>
<tag name="@see">#setTimeOfLastAlarm</tag>
</property>

<property name="escalationLevel1Enabled" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;escalationLevel1Enabled&lt;/code&gt; property.
</description>
<tag name="@see">#getEscalationLevel1Enabled</tag>
<tag name="@see">#setEscalationLevel1Enabled</tag>
</property>

<property name="escalationLevel1Delay" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;escalationLevel1Delay&lt;/code&gt; property.
</description>
<tag name="@see">#getEscalationLevel1Delay</tag>
<tag name="@see">#setEscalationLevel1Delay</tag>
</property>

<property name="escalationLevel2Enabled" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;escalationLevel2Enabled&lt;/code&gt; property.
</description>
<tag name="@see">#getEscalationLevel2Enabled</tag>
<tag name="@see">#setEscalationLevel2Enabled</tag>
</property>

<property name="escalationLevel2Delay" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;escalationLevel2Delay&lt;/code&gt; property.
</description>
<tag name="@see">#getEscalationLevel2Delay</tag>
<tag name="@see">#setEscalationLevel2Delay</tag>
</property>

<property name="escalationLevel3Enabled" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;escalationLevel3Enabled&lt;/code&gt; property.
</description>
<tag name="@see">#getEscalationLevel3Enabled</tag>
<tag name="@see">#setEscalationLevel3Enabled</tag>
</property>

<property name="escalationLevel3Delay" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;escalationLevel3Delay&lt;/code&gt; property.
</description>
<tag name="@see">#getEscalationLevel3Delay</tag>
<tag name="@see">#setEscalationLevel3Delay</tag>
</property>

<action name="routeAlarm" flags="ha">
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;routeAlarm&lt;/code&gt; action.&#xa; Route an alarm record
</description>
<tag name="@see">#routeAlarm(BAlarmRecord parameter)</tag>
</action>

<topic name="alarm" flags="s">
<eventType>
<type class="javax.baja.alarm.BAlarmRecord"/>
</eventType><description>
Slot for the &lt;code&gt;alarm&lt;/code&gt; topic.
</description>
<tag name="@see">#fireAlarm</tag>
</topic>

<topic name="escalatedAlarm1" flags="">
<eventType>
<type class="javax.baja.alarm.BAlarmRecord"/>
</eventType><description>
Slot for the &lt;code&gt;escalatedAlarm1&lt;/code&gt; topic.
</description>
<tag name="@see">#fireEscalatedAlarm1</tag>
</topic>

<topic name="escalatedAlarm2" flags="">
<eventType>
<type class="javax.baja.alarm.BAlarmRecord"/>
</eventType><description>
Slot for the &lt;code&gt;escalatedAlarm2&lt;/code&gt; topic.
</description>
<tag name="@see">#fireEscalatedAlarm2</tag>
</topic>

<topic name="escalatedAlarm3" flags="">
<eventType>
<type class="javax.baja.alarm.BAlarmRecord"/>
</eventType><description>
Slot for the &lt;code&gt;escalatedAlarm3&lt;/code&gt; topic.
</description>
<tag name="@see">#fireEscalatedAlarm3</tag>
</topic>

<!-- javax.baja.alarm.BAlarmClass() -->
<constructor name="BAlarmClass" public="true">
<description/>
</constructor>

<!-- javax.baja.alarm.BAlarmClass.getAckRequired() -->
<method name="getAckRequired"  public="true">
<description>
Get the &lt;code&gt;ackRequired&lt;/code&gt; property.&#xa; Alarm transition types that require a user&#xa; acknowledgement
</description>
<tag name="@see">#ackRequired</tag>
<return>
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.setAckRequired(javax.baja.alarm.BAlarmTransitionBits) -->
<method name="setAckRequired"  public="true">
<description>
Set the &lt;code&gt;ackRequired&lt;/code&gt; property.&#xa; Alarm transition types that require a user&#xa; acknowledgement
</description>
<tag name="@see">#ackRequired</tag>
<parameter name="v">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.getPriority() -->
<method name="getPriority"  public="true">
<description>
Get the &lt;code&gt;priority&lt;/code&gt; property.&#xa; Priority assigned to each transition type
</description>
<tag name="@see">#priority</tag>
<return>
<type class="javax.baja.alarm.BAlarmPriorities"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.setPriority(javax.baja.alarm.BAlarmPriorities) -->
<method name="setPriority"  public="true">
<description>
Set the &lt;code&gt;priority&lt;/code&gt; property.&#xa; Priority assigned to each transition type
</description>
<tag name="@see">#priority</tag>
<parameter name="v">
<type class="javax.baja.alarm.BAlarmPriorities"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.getTotalAlarmCount() -->
<method name="getTotalAlarmCount"  public="true">
<description>
Get the &lt;code&gt;totalAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#totalAlarmCount</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.setTotalAlarmCount(int) -->
<method name="setTotalAlarmCount"  public="true">
<description>
Set the &lt;code&gt;totalAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#totalAlarmCount</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.getOpenAlarmCount() -->
<method name="getOpenAlarmCount"  public="true">
<description>
Get the &lt;code&gt;openAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#openAlarmCount</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.setOpenAlarmCount(int) -->
<method name="setOpenAlarmCount"  public="true">
<description>
Set the &lt;code&gt;openAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#openAlarmCount</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.getInAlarmCount() -->
<method name="getInAlarmCount"  public="true">
<description>
Get the &lt;code&gt;inAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#inAlarmCount</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.setInAlarmCount(int) -->
<method name="setInAlarmCount"  public="true">
<description>
Set the &lt;code&gt;inAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#inAlarmCount</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.getUnackedAlarmCount() -->
<method name="getUnackedAlarmCount"  public="true">
<description>
Get the &lt;code&gt;unackedAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#unackedAlarmCount</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.setUnackedAlarmCount(int) -->
<method name="setUnackedAlarmCount"  public="true">
<description>
Set the &lt;code&gt;unackedAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#unackedAlarmCount</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.getTimeOfLastAlarm() -->
<method name="getTimeOfLastAlarm"  public="true">
<description>
Get the &lt;code&gt;timeOfLastAlarm&lt;/code&gt; property.
</description>
<tag name="@see">#timeOfLastAlarm</tag>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.setTimeOfLastAlarm(javax.baja.sys.BAbsTime) -->
<method name="setTimeOfLastAlarm"  public="true">
<description>
Set the &lt;code&gt;timeOfLastAlarm&lt;/code&gt; property.
</description>
<tag name="@see">#timeOfLastAlarm</tag>
<parameter name="v">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.getEscalationLevel1Enabled() -->
<method name="getEscalationLevel1Enabled"  public="true">
<description>
Get the &lt;code&gt;escalationLevel1Enabled&lt;/code&gt; property.
</description>
<tag name="@see">#escalationLevel1Enabled</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.setEscalationLevel1Enabled(boolean) -->
<method name="setEscalationLevel1Enabled"  public="true">
<description>
Set the &lt;code&gt;escalationLevel1Enabled&lt;/code&gt; property.
</description>
<tag name="@see">#escalationLevel1Enabled</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.getEscalationLevel1Delay() -->
<method name="getEscalationLevel1Delay"  public="true">
<description>
Get the &lt;code&gt;escalationLevel1Delay&lt;/code&gt; property.
</description>
<tag name="@see">#escalationLevel1Delay</tag>
<return>
<type class="javax.baja.sys.BRelTime"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.setEscalationLevel1Delay(javax.baja.sys.BRelTime) -->
<method name="setEscalationLevel1Delay"  public="true">
<description>
Set the &lt;code&gt;escalationLevel1Delay&lt;/code&gt; property.
</description>
<tag name="@see">#escalationLevel1Delay</tag>
<parameter name="v">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.getEscalationLevel2Enabled() -->
<method name="getEscalationLevel2Enabled"  public="true">
<description>
Get the &lt;code&gt;escalationLevel2Enabled&lt;/code&gt; property.
</description>
<tag name="@see">#escalationLevel2Enabled</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.setEscalationLevel2Enabled(boolean) -->
<method name="setEscalationLevel2Enabled"  public="true">
<description>
Set the &lt;code&gt;escalationLevel2Enabled&lt;/code&gt; property.
</description>
<tag name="@see">#escalationLevel2Enabled</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.getEscalationLevel2Delay() -->
<method name="getEscalationLevel2Delay"  public="true">
<description>
Get the &lt;code&gt;escalationLevel2Delay&lt;/code&gt; property.
</description>
<tag name="@see">#escalationLevel2Delay</tag>
<return>
<type class="javax.baja.sys.BRelTime"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.setEscalationLevel2Delay(javax.baja.sys.BRelTime) -->
<method name="setEscalationLevel2Delay"  public="true">
<description>
Set the &lt;code&gt;escalationLevel2Delay&lt;/code&gt; property.
</description>
<tag name="@see">#escalationLevel2Delay</tag>
<parameter name="v">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.getEscalationLevel3Enabled() -->
<method name="getEscalationLevel3Enabled"  public="true">
<description>
Get the &lt;code&gt;escalationLevel3Enabled&lt;/code&gt; property.
</description>
<tag name="@see">#escalationLevel3Enabled</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.setEscalationLevel3Enabled(boolean) -->
<method name="setEscalationLevel3Enabled"  public="true">
<description>
Set the &lt;code&gt;escalationLevel3Enabled&lt;/code&gt; property.
</description>
<tag name="@see">#escalationLevel3Enabled</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.getEscalationLevel3Delay() -->
<method name="getEscalationLevel3Delay"  public="true">
<description>
Get the &lt;code&gt;escalationLevel3Delay&lt;/code&gt; property.
</description>
<tag name="@see">#escalationLevel3Delay</tag>
<return>
<type class="javax.baja.sys.BRelTime"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.setEscalationLevel3Delay(javax.baja.sys.BRelTime) -->
<method name="setEscalationLevel3Delay"  public="true">
<description>
Set the &lt;code&gt;escalationLevel3Delay&lt;/code&gt; property.
</description>
<tag name="@see">#escalationLevel3Delay</tag>
<parameter name="v">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.routeAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="routeAlarm"  public="true">
<description>
Invoke the &lt;code&gt;routeAlarm&lt;/code&gt; action.&#xa; Route an alarm record
</description>
<tag name="@see">#routeAlarm</tag>
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.fireAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="fireAlarm"  public="true">
<description>
Fire an event for the &lt;code&gt;alarm&lt;/code&gt; topic.
</description>
<tag name="@see">#alarm</tag>
<parameter name="event">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.fireEscalatedAlarm1(javax.baja.alarm.BAlarmRecord) -->
<method name="fireEscalatedAlarm1"  public="true">
<description>
Fire an event for the &lt;code&gt;escalatedAlarm1&lt;/code&gt; topic.
</description>
<tag name="@see">#escalatedAlarm1</tag>
<parameter name="event">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.fireEscalatedAlarm2(javax.baja.alarm.BAlarmRecord) -->
<method name="fireEscalatedAlarm2"  public="true">
<description>
Fire an event for the &lt;code&gt;escalatedAlarm2&lt;/code&gt; topic.
</description>
<tag name="@see">#escalatedAlarm2</tag>
<parameter name="event">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.fireEscalatedAlarm3(javax.baja.alarm.BAlarmRecord) -->
<method name="fireEscalatedAlarm3"  public="true">
<description>
Fire an event for the &lt;code&gt;escalatedAlarm3&lt;/code&gt; topic.
</description>
<tag name="@see">#escalatedAlarm3</tag>
<parameter name="event">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.started() -->
<method name="started"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.post(javax.baja.sys.Action, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="post"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="action">
<type class="javax.baja.sys.Action"/>
</parameter>
<parameter name="argument">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.util.IFuture"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.doRouteAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="doRouteAlarm"  public="true">
<description>
Route this alarm to all interested recipients
</description>
<parameter name="alarm">
<type class="javax.baja.alarm.BAlarmRecord"/>
<description>
The alarm to route.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.getAlarmService() -->
<method name="getAlarmService"  protected="true">
<description/>
<return>
<type class="javax.baja.alarm.BAlarmService"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the icon.
</description>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClass.ackRequired -->
<field name="ackRequired"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;ackRequired&lt;/code&gt; property.&#xa; Alarm transition types that require a user&#xa; acknowledgement
</description>
<tag name="@see">#getAckRequired</tag>
<tag name="@see">#setAckRequired</tag>
</field>

<!-- javax.baja.alarm.BAlarmClass.priority -->
<field name="priority"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;priority&lt;/code&gt; property.&#xa; Priority assigned to each transition type
</description>
<tag name="@see">#getPriority</tag>
<tag name="@see">#setPriority</tag>
</field>

<!-- javax.baja.alarm.BAlarmClass.totalAlarmCount -->
<field name="totalAlarmCount"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;totalAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#getTotalAlarmCount</tag>
<tag name="@see">#setTotalAlarmCount</tag>
</field>

<!-- javax.baja.alarm.BAlarmClass.openAlarmCount -->
<field name="openAlarmCount"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;openAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#getOpenAlarmCount</tag>
<tag name="@see">#setOpenAlarmCount</tag>
</field>

<!-- javax.baja.alarm.BAlarmClass.inAlarmCount -->
<field name="inAlarmCount"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;inAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#getInAlarmCount</tag>
<tag name="@see">#setInAlarmCount</tag>
</field>

<!-- javax.baja.alarm.BAlarmClass.unackedAlarmCount -->
<field name="unackedAlarmCount"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;unackedAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#getUnackedAlarmCount</tag>
<tag name="@see">#setUnackedAlarmCount</tag>
</field>

<!-- javax.baja.alarm.BAlarmClass.timeOfLastAlarm -->
<field name="timeOfLastAlarm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;timeOfLastAlarm&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeOfLastAlarm</tag>
<tag name="@see">#setTimeOfLastAlarm</tag>
</field>

<!-- javax.baja.alarm.BAlarmClass.escalationLevel1Enabled -->
<field name="escalationLevel1Enabled"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;escalationLevel1Enabled&lt;/code&gt; property.
</description>
<tag name="@see">#getEscalationLevel1Enabled</tag>
<tag name="@see">#setEscalationLevel1Enabled</tag>
</field>

<!-- javax.baja.alarm.BAlarmClass.escalationLevel1Delay -->
<field name="escalationLevel1Delay"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;escalationLevel1Delay&lt;/code&gt; property.
</description>
<tag name="@see">#getEscalationLevel1Delay</tag>
<tag name="@see">#setEscalationLevel1Delay</tag>
</field>

<!-- javax.baja.alarm.BAlarmClass.escalationLevel2Enabled -->
<field name="escalationLevel2Enabled"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;escalationLevel2Enabled&lt;/code&gt; property.
</description>
<tag name="@see">#getEscalationLevel2Enabled</tag>
<tag name="@see">#setEscalationLevel2Enabled</tag>
</field>

<!-- javax.baja.alarm.BAlarmClass.escalationLevel2Delay -->
<field name="escalationLevel2Delay"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;escalationLevel2Delay&lt;/code&gt; property.
</description>
<tag name="@see">#getEscalationLevel2Delay</tag>
<tag name="@see">#setEscalationLevel2Delay</tag>
</field>

<!-- javax.baja.alarm.BAlarmClass.escalationLevel3Enabled -->
<field name="escalationLevel3Enabled"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;escalationLevel3Enabled&lt;/code&gt; property.
</description>
<tag name="@see">#getEscalationLevel3Enabled</tag>
<tag name="@see">#setEscalationLevel3Enabled</tag>
</field>

<!-- javax.baja.alarm.BAlarmClass.escalationLevel3Delay -->
<field name="escalationLevel3Delay"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;escalationLevel3Delay&lt;/code&gt; property.
</description>
<tag name="@see">#getEscalationLevel3Delay</tag>
<tag name="@see">#setEscalationLevel3Delay</tag>
</field>

<!-- javax.baja.alarm.BAlarmClass.routeAlarm -->
<field name="routeAlarm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;routeAlarm&lt;/code&gt; action.&#xa; Route an alarm record
</description>
<tag name="@see">#routeAlarm(BAlarmRecord parameter)</tag>
</field>

<!-- javax.baja.alarm.BAlarmClass.alarm -->
<field name="alarm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Topic"/>
<description>
Slot for the &lt;code&gt;alarm&lt;/code&gt; topic.
</description>
<tag name="@see">#fireAlarm</tag>
</field>

<!-- javax.baja.alarm.BAlarmClass.escalatedAlarm1 -->
<field name="escalatedAlarm1"  public="true" static="true" final="true">
<type class="javax.baja.sys.Topic"/>
<description>
Slot for the &lt;code&gt;escalatedAlarm1&lt;/code&gt; topic.
</description>
<tag name="@see">#fireEscalatedAlarm1</tag>
</field>

<!-- javax.baja.alarm.BAlarmClass.escalatedAlarm2 -->
<field name="escalatedAlarm2"  public="true" static="true" final="true">
<type class="javax.baja.sys.Topic"/>
<description>
Slot for the &lt;code&gt;escalatedAlarm2&lt;/code&gt; topic.
</description>
<tag name="@see">#fireEscalatedAlarm2</tag>
</field>

<!-- javax.baja.alarm.BAlarmClass.escalatedAlarm3 -->
<field name="escalatedAlarm3"  public="true" static="true" final="true">
<type class="javax.baja.sys.Topic"/>
<description>
Slot for the &lt;code&gt;escalatedAlarm3&lt;/code&gt; topic.
</description>
<tag name="@see">#fireEscalatedAlarm3</tag>
</field>

<!-- javax.baja.alarm.BAlarmClass.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmClass.log -->
<field name="log"  public="true" static="true" final="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmClass.ESCALATED -->
<field name="ESCALATED"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmClass.LEVEL_1 -->
<field name="LEVEL_1"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmClass.LEVEL_2 -->
<field name="LEVEL_2"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmClass.LEVEL_3 -->
<field name="LEVEL_3"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

</class>
</bajadoc>
