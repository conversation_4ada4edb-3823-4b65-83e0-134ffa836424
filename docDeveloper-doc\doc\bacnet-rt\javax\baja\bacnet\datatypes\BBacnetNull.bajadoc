<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetNull" name="BBacnetNull" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BBacnetNull represents a null value in a Bacnet property.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 8$ $Date: 12/19/01 4:35:52 PM$</tag>
<tag name="@creation">09 Aug 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BSimple"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
<!-- javax.baja.bacnet.datatypes.BBacnetNull.isNull() -->
<method name="isNull"  public="true">
<description>
Some types of BObjects are used to indicate&#xa; a null value.  This method allows those types to&#xa; declare their null status by overriding this common&#xa; method.  The default is to return false.
</description>
<return>
<type class="boolean"/>
<description>
true because this represents a null value.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetNull.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description/>
<parameter name="obj">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetNull.encode(java.io.DataOutput) -->
<method name="encode"  public="true">
<description>
Encode the simple type using a binary format&#xa; that can be translated using decode.
</description>
<parameter name="out">
<type class="java.io.DataOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetNull.decode(java.io.DataInput) -->
<method name="decode"  public="true">
<description>
Decode the simple using the same binary format&#xa; that was written using encode, and return the new&#xa; instance.  Under no circumstances should this&#xa; instance be modified.
</description>
<parameter name="in">
<type class="java.io.DataInput"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetNull.encodeToString() -->
<method name="encodeToString"  public="true">
<description>
Encode the simple using a String format&#xa; that can be translated using decodeFromString.
</description>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetNull.decodeFromString(java.lang.String) -->
<method name="decodeFromString"  public="true">
<description>
Decode the simple using the same String format&#xa; that was written using encodeToString, and return&#xa; the new instance.  Under no circumstances should&#xa; this instance me modified.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetNull.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetNull.hashCode() -->
<method name="hashCode"  public="true">
<description>
Hash code.&#xa; The hash code for a BBacnetNull is its unique id.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetNull.getType() -->
<method name="getType"  public="true">
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetNull.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.datatypes.BBacnetNull"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetNull.NULL_STR -->
<field name="NULL_STR"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetNull.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
