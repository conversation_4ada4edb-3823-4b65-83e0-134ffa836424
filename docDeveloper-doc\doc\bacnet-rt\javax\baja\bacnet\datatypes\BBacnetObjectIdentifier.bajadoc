<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier" name="BBacnetObjectIdentifier" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BBacnetObjectIdentifier represents an object-identifier value in a Bacnet property.&#xa; For convenience,
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">25 Mar 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BSimple"/>
</extends>
<implements>
<type class="javax.baja.sys.BIComparable"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method.
</description>
<parameter name="objectType">
<type class="int"/>
<description/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.make(int, int) -->
<method name="make"  public="true" static="true">
<description>
Fully specified constructor with object type.
</description>
<parameter name="objectType">
<type class="int"/>
<description>
int
</description>
</parameter>
<parameter name="instanceNumber">
<type class="int"/>
<description/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.make(javax.baja.bacnet.enums.BBacnetObjectType, int) -->
<method name="make"  public="true" static="true">
<description>
Fully specified constructor with object type.
</description>
<tag name="@since">Niagara 4.10u8</tag>
<tag name="@since">Niagara 4.13u3</tag>
<tag name="@since">Niagara 4.14</tag>
<parameter name="objectType">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
</parameter>
<parameter name="instanceNumber">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.makeId(int) -->
<method name="makeId"  public="true" static="true">
<description>
Factory method.
</description>
<parameter name="objectId">
<type class="int"/>
<description/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description>
BBacnetObjectIdentifier equality is based on the type and instance.
</description>
<parameter name="obj">
<type class="java.lang.Object"/>
<description>
comparison object.
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the objects represent the same Bacnet Object Identifier.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.toShortString() -->
<method name="toShortString"  public="true">
<description>
Get a short-form string for this object (&#x22;AO1&#x22;, &#x22;BO3&#x22;, etc)
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.hashCode() -->
<method name="hashCode"  public="true">
<description>
Hash code.&#xa; The hash code for a BBacnetObjectIdentifier is its integer value.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.encode(java.io.DataOutput) -->
<method name="encode"  public="true">
<description>
BBacnetObjectIdentifier is serialized using calls to writeInt().
</description>
<parameter name="out">
<type class="java.io.DataOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.decode(java.io.DataInput) -->
<method name="decode"  public="true">
<description>
BBacnetObjectIdentifier is unserialized using calls to readInt().
</description>
<parameter name="in">
<type class="java.io.DataInput"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.encodeToString() -->
<method name="encodeToString"  public="true">
<description>
Write the simple in text format.
</description>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.decodeFromString(java.lang.String) -->
<method name="decodeFromString"  public="true">
<description>
Read the simple from text format.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.isValid() -->
<method name="isValid"  public="true">
<description>
Is this id valid?&#xa; Validity test is a check that both the object type and&#xa; instance number are nonegative.
</description>
<return>
<type class="boolean"/>
<description>
true if the id is valid, or false otherwise.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.getObjectType() -->
<method name="getObjectType"  public="true">
<description>
Get the object type.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.getInstanceNumber() -->
<method name="getInstanceNumber"  public="true">
<description>
Get the instance number.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.getId() -->
<method name="getId"  public="true">
<description>
Get the integer id value.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.newId(int) -->
<method name="newId"  public="true">
<description>
Get a new id of the same type, but with the given instance number.
</description>
<parameter name="newInstanceNumber">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.compareTo(java.lang.Object) -->
<method name="compareTo"  public="true">
<description/>
<parameter name="o">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.OBJECT_TYPE_MASK -->
<field name="OBJECT_TYPE_MASK"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.OBJECT_TYPE_MASK_SHIFTED -->
<field name="OBJECT_TYPE_MASK_SHIFTED"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.INSTANCE_NUMBER_MASK -->
<field name="INSTANCE_NUMBER_MASK"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.OBJECT_TYPE_SHIFT -->
<field name="OBJECT_TYPE_SHIFT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.MIN_INSTANCE_NUMBER -->
<field name="MIN_INSTANCE_NUMBER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.MAX_INSTANCE_NUMBER -->
<field name="MAX_INSTANCE_NUMBER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.UNCONFIGURED_INSTANCE_NUMBER -->
<field name="UNCONFIGURED_INSTANCE_NUMBER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
The default objectId is analog_input:0.
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.DEFAULT_DEVICE -->
<field name="DEFAULT_DEVICE"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetObjectIdentifier.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
