<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarmOrion" runtimeProfile="rt" qualifiedName="javax.baja.alarmOrion.BOrionAlarmSourceOrder" name="BOrionAlarmSourceOrder" packageName="javax.baja.alarmOrion" public="true">
<description/>
<extends>
<type class="com.tridium.orion.BOrionObject"/>
</extends>
<annotation><type class="com.tridium.orion.annotations.NiagaraOrionType"/>
</annotation>
<annotation><type class="com.tridium.orion.annotations.OrionProperties"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="com.tridium.orion.annotations.OrionProperty"/>
<elementValue name="name">
<annotationValue kind="expr">
<expression>&#x22;alarm&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="refType">
<annotationValue kind="expr">
<expression>&#x22;alarmOrion:OrionAlarmRecord&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="flags">
<annotationValue kind="expr">
<expression>8</expression>
</annotationValue>
</elementValue>
<elementValue name="facets">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Facet"/>
<elementValue name="name">
<annotationValue kind="expr">
<expression>&#x22;INDEXED&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;true&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Facet"/>
<elementValue name="name">
<annotationValue kind="expr">
<expression>&#x22;CLUSTERED&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;true&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Facet"/>
<elementValue name="name">
<annotationValue kind="expr">
<expression>&#x22;DESCENDING&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;true&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Facet"/>
<elementValue name="name">
<annotationValue kind="expr">
<expression>&#x22;ON_DELETE&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;BOnDelete.CASCADE&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="com.tridium.orion.annotations.OrionProperty"/>
<elementValue name="name">
<annotationValue kind="expr">
<expression>&#x22;alarmSource&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="refType">
<annotationValue kind="expr">
<expression>&#x22;alarmOrion:OrionAlarmSource&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="flags">
<annotationValue kind="expr">
<expression>8</expression>
</annotationValue>
</elementValue>
<elementValue name="facets">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Facet"/>
<elementValue name="name">
<annotationValue kind="expr">
<expression>&#x22;ON_DELETE&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;BOnDelete.CASCADE&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Facet"/>
<elementValue name="name">
<annotationValue kind="expr">
<expression>&#x22;AUTO_RESOLVE&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;true&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<property name="id" flags="rs">
<type class="int"/>
<description>
Slot for the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#getId</tag>
<tag name="@see">#setId</tag>
</property>

<property name="alarm" flags="s">
<type class="com.tridium.orion.BRef"/>
<description>
Slot for the &lt;code&gt;alarm&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarm</tag>
<tag name="@see">#setAlarm</tag>
</property>

<property name="alarmSource" flags="s">
<type class="com.tridium.orion.BRef"/>
<description>
Slot for the &lt;code&gt;alarmSource&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmSource</tag>
<tag name="@see">#setAlarmSource</tag>
</property>

<property name="sourceOrder" flags="s">
<type class="int"/>
<description>
Slot for the &lt;code&gt;sourceOrder&lt;/code&gt; property.&#xa; The path to the source of the alarm.
</description>
<tag name="@see">#getSourceOrder</tag>
<tag name="@see">#setSourceOrder</tag>
</property>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder() -->
<constructor name="BOrionAlarmSourceOrder" public="true">
<description/>
</constructor>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder.getId() -->
<method name="getId"  public="true">
<description>
Get the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#id</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder.setId(int) -->
<method name="setId"  public="true">
<description>
Set the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#id</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder.getAlarm() -->
<method name="getAlarm"  public="true">
<description>
Get the &lt;code&gt;alarm&lt;/code&gt; property.
</description>
<tag name="@see">#alarm</tag>
<return>
<type class="com.tridium.orion.BRef"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder.setAlarm(com.tridium.orion.BRef) -->
<method name="setAlarm"  public="true">
<description>
Set the &lt;code&gt;alarm&lt;/code&gt; property.
</description>
<tag name="@see">#alarm</tag>
<parameter name="v">
<type class="com.tridium.orion.BRef"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder.resolveAlarm(com.tridium.orion.OrionSession) -->
<method name="resolveAlarm"  public="true">
<description>
Resolve the &lt;code&gt;alarm&lt;/code&gt; property.
</description>
<tag name="@see">#alarm</tag>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="javax.baja.alarmOrion.BOrionAlarmRecord"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder.getAlarmSource() -->
<method name="getAlarmSource"  public="true">
<description>
Get the &lt;code&gt;alarmSource&lt;/code&gt; property.
</description>
<tag name="@see">#alarmSource</tag>
<return>
<type class="com.tridium.orion.BRef"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder.setAlarmSource(com.tridium.orion.BRef) -->
<method name="setAlarmSource"  public="true">
<description>
Set the &lt;code&gt;alarmSource&lt;/code&gt; property.
</description>
<tag name="@see">#alarmSource</tag>
<parameter name="v">
<type class="com.tridium.orion.BRef"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder.resolveAlarmSource(com.tridium.orion.OrionSession) -->
<method name="resolveAlarmSource"  public="true">
<description>
Resolve the &lt;code&gt;alarmSource&lt;/code&gt; property.
</description>
<tag name="@see">#alarmSource</tag>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="javax.baja.alarmOrion.BOrionAlarmSource"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder.getSourceOrder() -->
<method name="getSourceOrder"  public="true">
<description>
Get the &lt;code&gt;sourceOrder&lt;/code&gt; property.&#xa; The path to the source of the alarm.
</description>
<tag name="@see">#sourceOrder</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder.setSourceOrder(int) -->
<method name="setSourceOrder"  public="true">
<description>
Set the &lt;code&gt;sourceOrder&lt;/code&gt; property.&#xa; The path to the source of the alarm.
</description>
<tag name="@see">#sourceOrder</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder.make(javax.baja.naming.BOrd, int, javax.baja.alarmOrion.BOrionAlarmRecord, com.tridium.orion.OrionSession) -->
<method name="make"  public="true" static="true">
<description>
Create a BOrionAlarmSourceOrder object for the specified alarm.
</description>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<parameter name="index">
<type class="int"/>
</parameter>
<parameter name="alarm">
<type class="javax.baja.alarmOrion.BOrionAlarmRecord"/>
</parameter>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="javax.baja.alarmOrion.BOrionAlarmSourceOrder"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder.id -->
<field name="id"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#getId</tag>
<tag name="@see">#setId</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder.alarm -->
<field name="alarm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarm&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarm</tag>
<tag name="@see">#setAlarm</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder.alarmSource -->
<field name="alarmSource"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmSource&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmSource</tag>
<tag name="@see">#setAlarmSource</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder.sourceOrder -->
<field name="sourceOrder"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;sourceOrder&lt;/code&gt; property.&#xa; The path to the source of the alarm.
</description>
<tag name="@see">#getSourceOrder</tag>
<tag name="@see">#setSourceOrder</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmSourceOrder.ORION_TYPE -->
<field name="ORION_TYPE"  public="true" static="true" final="true">
<type class="com.tridium.orion.OrionType"/>
<description/>
</field>

</class>
</bajadoc>
