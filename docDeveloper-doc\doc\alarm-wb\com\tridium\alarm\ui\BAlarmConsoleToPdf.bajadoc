<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.ui.BAlarmConsoleToPdf" name="BAlarmConsoleToPdf" packageName="com.tridium.alarm.ui" public="true">
<description>
alarm:AlarmConsole -&gt; file:PdfFile
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">September 6, 2007</tag>
<tag name="@version">$Revision: 4$ $Date: 11/26/08 10:08:24 AM EST$</tag>
<tag name="@since">Baja 3.3</tag>
<extends>
<type class="javax.baja.pdf.BPdfExporter"/>
</extends>
<implements>
<type class="javax.baja.workbench.view.BIWbViewExporter"/>
</implements>
<property name="showAllColumns" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;showAllColumns&lt;/code&gt; property.&#xa; show all columns regardless of their visibility
</description>
<tag name="@see">#getShowAllColumns</tag>
<tag name="@see">#setShowAllColumns</tag>
</property>

<property name="showOnlySelectedRows" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;showOnlySelectedRows&lt;/code&gt; property.&#xa; show selected rows only
</description>
<tag name="@see">#getShowOnlySelectedRows</tag>
<tag name="@see">#setShowOnlySelectedRows</tag>
</property>

</class>
</bajadoc>
