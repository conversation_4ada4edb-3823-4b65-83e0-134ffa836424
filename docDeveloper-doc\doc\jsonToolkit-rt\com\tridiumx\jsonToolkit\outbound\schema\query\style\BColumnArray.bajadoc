<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.query.style.BColumnArray" name="BColumnArray" packageName="com.tridiumx.jsonToolkit.outbound.schema.query.style" public="true">
<description>
A query result writer which renders the results as an array of arrays,&#xa; where each array contains the values for a given column.&#xa; For example if we queried name, out.value - then array1 contains all the names, array2 all the out.value&#x27;s.&#xa;&#xa;  [&#xa;   [a1,a2,a3],&#xa;   [b1,b2,b3],&#xa;   [c1,c2,c3]&#xa;  ]
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.style.BQueryResultWriter"/>
</extends>
<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BColumnArray() -->
<constructor name="BColumnArray" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BColumnArray.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BColumnArray.previewText() -->
<method name="previewText"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BString"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BColumnArray.appendJson(com.tridium.json.JSONWriter, com.tridiumx.jsonToolkit.outbound.schema.query.QueryResultHolder) -->
<method name="appendJson"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="json">
<type class="com.tridium.json.JSONWriter"/>
</parameter>
<parameter name="result">
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.QueryResultHolder"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BColumnArray.appendColumn(com.tridium.json.JSONWriter, java.lang.String, com.tridiumx.jsonToolkit.outbound.schema.query.QueryResultHolder) -->
<method name="appendColumn"  protected="true">
<description/>
<parameter name="json">
<type class="com.tridium.json.JSONWriter"/>
</parameter>
<parameter name="columnName">
<type class="java.lang.String"/>
</parameter>
<parameter name="result">
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.QueryResultHolder"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BColumnArray.includeHeader() -->
<method name="includeHeader"  protected="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BColumnArray.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
