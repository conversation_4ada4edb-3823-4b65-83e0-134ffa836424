<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.point.BInfinityProxyExt" name="BInfinityProxyExt" packageName="com.tridium.andoverInfinity.point" public="true">
<description>
BInfinityProxyExt
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 22, 2007</tag>
<tag name="@version">$Revision$ $May 22, 2007 10:01:24 AM$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.point.BDdfProxyExt"/>
</extends>
<implements>
<type class="javax.baja.driver.util.BIPollable"/>
</implements>
<property name="pollFrequency" flags="">
<type class="javax.baja.driver.util.BPollFrequency"/>
<description>
Slot for the &lt;code&gt;pollFrequency&lt;/code&gt; property.&#xa; the poll frequency bucket this extension is assigned to
</description>
<tag name="@see">#getPollFrequency</tag>
<tag name="@see">#setPollFrequency</tag>
</property>

<action name="sendEnableCommand" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;sendEnableCommand&lt;/code&gt; action.
</description>
<tag name="@see">#sendEnableCommand()</tag>
</action>

<action name="sendDisableCommand" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;sendDisableCommand&lt;/code&gt; action.
</description>
<tag name="@see">#sendDisableCommand()</tag>
</action>

<!-- com.tridium.andoverInfinity.point.BInfinityProxyExt() -->
<constructor name="BInfinityProxyExt" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.point.BInfinityProxyExt.getPollFrequency() -->
<method name="getPollFrequency"  public="true">
<description>
Get the &lt;code&gt;pollFrequency&lt;/code&gt; property.&#xa; the poll frequency bucket this extension is assigned to
</description>
<tag name="@see">#pollFrequency</tag>
<return>
<type class="javax.baja.driver.util.BPollFrequency"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.point.BInfinityProxyExt.setPollFrequency(javax.baja.driver.util.BPollFrequency) -->
<method name="setPollFrequency"  public="true">
<description>
Set the &lt;code&gt;pollFrequency&lt;/code&gt; property.&#xa; the poll frequency bucket this extension is assigned to
</description>
<tag name="@see">#pollFrequency</tag>
<parameter name="v">
<type class="javax.baja.driver.util.BPollFrequency"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.point.BInfinityProxyExt.sendEnableCommand() -->
<method name="sendEnableCommand"  public="true">
<description>
Invoke the &lt;code&gt;sendEnableCommand&lt;/code&gt; action.
</description>
<tag name="@see">#sendEnableCommand</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.point.BInfinityProxyExt.sendDisableCommand() -->
<method name="sendDisableCommand"  public="true">
<description>
Invoke the &lt;code&gt;sendDisableCommand&lt;/code&gt; action.
</description>
<tag name="@see">#sendDisableCommand</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.point.BInfinityProxyExt.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.point.BInfinityProxyExt.getDeviceExtType() -->
<method name="getDeviceExtType"  public="true">
<description>
Infinity uses a &lt;code&gt;BInfinityPointDeviceExt.TYPE&lt;/code&gt;
</description>
<tag name="@see">javax.baja.driver.point.BProxyExt#getDeviceExtType()</tag>
<return>
<type class="javax.baja.sys.Type"/>
<description>
&lt;code&gt;BInfinityPointDeviceExt.TYPE&lt;/code&gt;
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.point.BInfinityProxyExt.getInfinityNetwork() -->
<method name="getInfinityNetwork"  public="true">
<description>
Convenience for getting InfinityNetwork
</description>
<return>
<type class="com.tridium.andoverInfinity.BInfinityNetwork"/>
<description>
the &lt;code&gt;BInfinityNetwork&lt;/code&gt;
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.point.BInfinityProxyExt.makePollRequest() -->
<method name="makePollRequest"  public="true">
<description/>
<return>
<type class="com.tridium.ddf.comm.req.BIDdfReadRequest"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.point.BInfinityProxyExt.doSendEnableCommand() -->
<method name="doSendEnableCommand"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.point.BInfinityProxyExt.doSendDisableCommand() -->
<method name="doSendDisableCommand"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.point.BInfinityProxyExt.readParameters -->
<field name="readParameters"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;readParameters&lt;/code&gt; property.
</description>
<tag name="@see">#getReadParameters</tag>
<tag name="@see">#setReadParameters</tag>
</field>

<!-- com.tridium.andoverInfinity.point.BInfinityProxyExt.writeParameters -->
<field name="writeParameters"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;writeParameters&lt;/code&gt; property.
</description>
<tag name="@see">#getWriteParameters</tag>
<tag name="@see">#setWriteParameters</tag>
</field>

<!-- com.tridium.andoverInfinity.point.BInfinityProxyExt.pointId -->
<field name="pointId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;pointId&lt;/code&gt; property.
</description>
<tag name="@see">#getPointId</tag>
<tag name="@see">#setPointId</tag>
</field>

<!-- com.tridium.andoverInfinity.point.BInfinityProxyExt.pollFrequency -->
<field name="pollFrequency"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;pollFrequency&lt;/code&gt; property.&#xa; the poll frequency bucket this extension is assigned to
</description>
<tag name="@see">#getPollFrequency</tag>
<tag name="@see">#setPollFrequency</tag>
</field>

<!-- com.tridium.andoverInfinity.point.BInfinityProxyExt.sendEnableCommand -->
<field name="sendEnableCommand"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;sendEnableCommand&lt;/code&gt; action.
</description>
<tag name="@see">#sendEnableCommand()</tag>
</field>

<!-- com.tridium.andoverInfinity.point.BInfinityProxyExt.sendDisableCommand -->
<field name="sendDisableCommand"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;sendDisableCommand&lt;/code&gt; action.
</description>
<tag name="@see">#sendDisableCommand()</tag>
</field>

<!-- com.tridium.andoverInfinity.point.BInfinityProxyExt.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
