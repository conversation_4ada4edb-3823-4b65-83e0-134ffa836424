<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.point.PointCmd" name="PointCmd" packageName="javax.baja.bacnet.point" public="true">
<description/>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="java.lang.Runnable"/>
</implements>
<implements>
<type class="javax.baja.util.ICoalesceable"/>
</implements>
<!-- javax.baja.bacnet.point.PointCmd(int, javax.baja.bacnet.point.BBacnetProxyExt) -->
<constructor name="PointCmd" public="true">
<parameter name="cmd">
<type class="int"/>
</parameter>
<parameter name="px">
<type class="javax.baja.bacnet.point.BBacnetProxyExt"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.point.PointCmd(int, javax.baja.bacnet.point.BBacnetProxyExt, java.lang.Object) -->
<constructor name="PointCmd" public="true">
<parameter name="cmd">
<type class="int"/>
</parameter>
<parameter name="px">
<type class="javax.baja.bacnet.point.BBacnetProxyExt"/>
</parameter>
<parameter name="arg">
<type class="java.lang.Object"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.point.PointCmd(int, javax.baja.bacnet.point.BBacnetProxyExt, java.lang.Object, int, int) -->
<constructor name="PointCmd" public="true">
<parameter name="cmd">
<type class="int"/>
</parameter>
<parameter name="px">
<type class="javax.baja.bacnet.point.BBacnetProxyExt"/>
</parameter>
<parameter name="arg">
<type class="java.lang.Object"/>
</parameter>
<parameter name="clr">
<type class="int"/>
</parameter>
<parameter name="lvl">
<type class="int"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.point.PointCmd.toString() -->
<method name="toString"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.point.PointCmd.getCoalesceKey() -->
<method name="getCoalesceKey"  public="true">
<description/>
<return>
<type class="java.lang.Object"/>
</return>
</method>

<!-- javax.baja.bacnet.point.PointCmd.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description/>
<parameter name="o">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.PointCmd.hashCode() -->
<method name="hashCode"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.point.PointCmd.coalesce(javax.baja.util.ICoalesceable) -->
<method name="coalesce"  public="true">
<description/>
<parameter name="c">
<type class="javax.baja.util.ICoalesceable"/>
</parameter>
<return>
<type class="javax.baja.util.ICoalesceable"/>
</return>
</method>

<!-- javax.baja.bacnet.point.PointCmd.run() -->
<method name="run"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.PointCmd.SUBSCRIBE_COVP_POINT -->
<field name="SUBSCRIBE_COVP_POINT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.PointCmd.READ_POINT -->
<field name="READ_POINT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.PointCmd.SUBSCRIBE_COV_POINT -->
<field name="SUBSCRIBE_COV_POINT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.PointCmd.WRITE_POINT -->
<field name="WRITE_POINT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.PointCmd.READ_META_DATA -->
<field name="READ_META_DATA"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
