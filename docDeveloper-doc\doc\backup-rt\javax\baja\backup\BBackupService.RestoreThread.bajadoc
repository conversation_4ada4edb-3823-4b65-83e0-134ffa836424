<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="backup" runtimeProfile="rt" qualifiedName="javax.baja.backup.BBackupService$RestoreThread" name="BBackupService.RestoreThread" packageName="javax.baja.backup" public="true" innerClass="true">
<description/>
<extends>
<type class="java.lang.Thread"/>
</extends>
<!-- javax.baja.backup.BBackupService.RestoreThread(javax.baja.backup.BBackupService.RestoreOp) -->
<constructor name="RestoreThread" public="true">
<parameter name="op">
<type class="javax.baja.backup.BBackupService$RestoreOp"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.backup.BBackupService.RestoreThread.run() -->
<method name="run"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
