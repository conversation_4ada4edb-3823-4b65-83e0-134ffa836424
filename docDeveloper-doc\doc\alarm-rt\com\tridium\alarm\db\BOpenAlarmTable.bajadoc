<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.db.BOpenAlarmTable" name="BOpenAlarmTable" packageName="com.tridium.alarm.db" public="true">
<description>
BOpenAlarmTable presents the list of open alarms as a table.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridium.alarm.db.BAlarmTable"/>
</extends>
<implements>
<type class="javax.baja.bql.Queryable"/>
</implements>
</class>
</bajadoc>
