<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.rsp.BInfinityPointAutoResponse" name="BInfinityPointAutoResponse" packageName="com.tridium.andoverInfinity.comm.rsp" public="true">
<description>
The only purpose in life for this class is to satisfy the success of the &#xa; BInfinityPointAutoRequest, BInfinityPointEnableRequest, or BInfinityPointDisableRequest
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.comm.rsp.BDdfResponse"/>
</extends>
<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityPointAutoResponse() -->
<constructor name="BInfinityPointAutoResponse" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityPointAutoResponse.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityPointAutoResponse.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
