<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.io.ErrorException" name="ErrorException" packageName="javax.baja.bacnet.io" public="true" category="exception">
<description>
ErrorExceptions are thrown when an error is encountered during&#xa; a Bacnet transaction.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 2$ $Date: 11/29/01 1:24:00 PM$</tag>
<tag name="@creation">26 Nov 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.BacnetException"/>
</extends>
<!-- javax.baja.bacnet.io.ErrorException(javax.baja.bacnet.io.ErrorType) -->
<constructor name="ErrorException" public="true">
<parameter name="errorType">
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
the Bacnet ErrorType associated with this error.
</description>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.io.ErrorException(javax.baja.bacnet.io.ErrorType, java.lang.Object[]) -->
<constructor name="ErrorException" public="true">
<parameter name="errorType">
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
the Bacnet ErrorType associated with this error.
</description>
</parameter>
<parameter name="errorParameters">
<type class="java.lang.Object" dimension="1"/>
<description>
the additional error parameters.
</description>
</parameter>
<description>
Constructor for compound errors such as WritePropertyMultipleError.
</description>
</constructor>

<!-- javax.baja.bacnet.io.ErrorException.getErrorType() -->
<method name="getErrorType"  public="true">
<description>
Returns the ErrorType for this ErrorException.
</description>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
the Bacnet ErrorType associated with this exception.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.ErrorException.getErrorParameters() -->
<method name="getErrorParameters"  public="true">
<description/>
<return>
<type class="java.lang.Object" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.io.ErrorException.toString() -->
<method name="toString"  public="true">
<description>
To String.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

</class>
</bajadoc>
