<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.history.BBacnetTrendLogRemoteExt" name="BBacnetTrendLogRemoteExt" packageName="com.tridium.bacnet.history" public="true" abstract="true">
<description/>
<extends>
<type class="javax.baja.history.ext.BIntervalHistoryExt"/>
</extends>
<implements>
<type class="com.tridium.bacnet.history.BIBacnetTrendLogExt"/>
</implements>
<property name="changeTolerance" flags="">
<type class="double"/>
<description>
Slot for the &lt;code&gt;changeTolerance&lt;/code&gt; property.
</description>
<tag name="@see">#getChangeTolerance</tag>
<tag name="@see">#setChangeTolerance</tag>
</property>

<property name="totalRecordCount" flags="">
<type class="long"/>
<description>
Slot for the &lt;code&gt;totalRecordCount&lt;/code&gt; property.
</description>
<tag name="@see">#getTotalRecordCount</tag>
<tag name="@see">#setTotalRecordCount</tag>
</property>

<property name="precision" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;precision&lt;/code&gt; property.
</description>
<tag name="@see">#getPrecision</tag>
<tag name="@see">#setPrecision</tag>
</property>

<property name="minRolloverValue" flags="">
<type class="javax.baja.history.BRolloverValue"/>
<description>
Slot for the &lt;code&gt;minRolloverValue&lt;/code&gt; property.
</description>
<tag name="@see">#getMinRolloverValue</tag>
<tag name="@see">#setMinRolloverValue</tag>
</property>

<property name="maxRolloverValue" flags="">
<type class="javax.baja.history.BRolloverValue"/>
<description>
Slot for the &lt;code&gt;maxRolloverValue&lt;/code&gt; property.
</description>
<tag name="@see">#getMaxRolloverValue</tag>
<tag name="@see">#setMaxRolloverValue</tag>
</property>

<property name="covResubscriptionInterval" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;covResubscriptionInterval&lt;/code&gt; property.
</description>
<tag name="@see">#getCovResubscriptionInterval</tag>
<tag name="@see">#setCovResubscriptionInterval</tag>
</property>

<property name="objectId" flags="hr">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="propertyId" flags="hr">
<type class="int"/>
<description>
Slot for the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyId</tag>
<tag name="@see">#setPropertyId</tag>
</property>

<property name="arrayIndex" flags="hr">
<type class="int"/>
<description>
Slot for the &lt;code&gt;arrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#getArrayIndex</tag>
<tag name="@see">#setArrayIndex</tag>
</property>

<action name="subscribeOnCoVSubscriptionInterval" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;subscribeOnCoVSubscriptionInterval&lt;/code&gt; action.
</description>
<tag name="@see">#subscribeOnCoVSubscriptionInterval()</tag>
</action>

<action name="startLogging" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;startLogging&lt;/code&gt; action.
</description>
<tag name="@see">#startLogging()</tag>
</action>

<action name="stopLogging" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;stopLogging&lt;/code&gt; action.
</description>
<tag name="@see">#stopLogging()</tag>
</action>

</class>
</bajadoc>
