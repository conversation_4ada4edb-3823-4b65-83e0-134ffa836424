<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.inbound.selector">
<description/>
<class packageName="com.tridiumx.jsonToolkit.inbound.selector" name="BJsonArrayForEachSelector"><description>JSON selector which passes each value of a JSON array to it&#x27;s output&#xa; slots in sequence, with an intermediate delay.</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.selector" name="BJsonAtArrayIndex"><description>Allows selection of Json array values by index.</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.selector" name="BJsonBooleanSelector"><description>Selectors that output a boolean as their result</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.selector" name="BJsonContainsKey"><description>Returns a boolean if the specified key is present in the payload</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.selector" name="BJsonFindAllSelector"><description>JSON selector which returns an array of all the values found in the routed&#xa; JSON string with matching key name.</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.selector" name="BJsonIndexOfKeySelector"><description>Selects the index of the given key within a json object.</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.selector" name="BJsonLengthSelector"/>
<class packageName="com.tridiumx.jsonToolkit.inbound.selector" name="BJsonNumericSelector"><description>Numeric Selectors have a double out slot</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.selector" name="BJsonPath"><description>Adds support for JsonPath so customers can more easily extract parts of a schema incoming.</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.selector" name="BJsonSelector"><description>Base class for all json inbound classes which take a payload, apply some selection criteria&#xa; and set the result in an out slot.</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.selector" name="BJsonStringSelector"><description>Selectors that output a string or json array/object as their result</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.selector" name="BJsonSumSelector"/>
</package>
</bajadoc>
