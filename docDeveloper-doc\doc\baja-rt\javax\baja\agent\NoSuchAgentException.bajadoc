<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.agent.NoSuchAgentException" name="NoSuchAgentException" packageName="javax.baja.agent" public="true" category="exception">
<description>
NoSuchAgentException is thrown when expecting a specific&#xa; agent, but one is not found.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Nov 02</tag>
<tag name="@version">$Revision: 1$ $Date: 12/26/02 1:32:56 PM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BajaRuntimeException"/>
</extends>
<!-- javax.baja.agent.NoSuchAgentException(java.lang.String, java.lang.Throwable) -->
<constructor name="NoSuchAgentException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="cause">
<type class="java.lang.Throwable"/>
</parameter>
<description>
Constructor with specified message and cause.
</description>
</constructor>

<!-- javax.baja.agent.NoSuchAgentException(java.lang.String) -->
<constructor name="NoSuchAgentException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<description>
Constructor with specified message.
</description>
</constructor>

<!-- javax.baja.agent.NoSuchAgentException(java.lang.Throwable) -->
<constructor name="NoSuchAgentException" public="true">
<parameter name="cause">
<type class="java.lang.Throwable"/>
</parameter>
<description>
Constructor with specified cause.
</description>
</constructor>

<!-- javax.baja.agent.NoSuchAgentException() -->
<constructor name="NoSuchAgentException" public="true">
<description>
Default constructor.
</description>
</constructor>

</class>
</bajadoc>
