<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.BacnetConst" name="BacnetConst" packageName="javax.baja.bacnet" public="true" interface="true" abstract="true" category="interface">
<description>
BacnetConst contains all constants needed for use in the&#xa; javax.baja.bacnet module.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 1$ $Date: 12/17/01 9:07:56 AM$</tag>
<tag name="@creation">15 Aug 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<!-- javax.baja.bacnet.BacnetConst.bacnetLexicon -->
<field name="bacnetLexicon"  public="true" static="true" final="true">
<type class="javax.baja.util.Lexicon"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.VENDOR_ID_TRIDIUM -->
<field name="VENDOR_ID_TRIDIUM"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.PROPERTY_ID_NOT_USED -->
<field name="PROPERTY_ID_NOT_USED"  public="true" static="true" final="true">
<type class="int"/>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description/>
<tag name="@deprecated">As of 3.5.  Use &lt;code&gt;NOT_USED&lt;/code&gt; instead.</tag>
</field>

<!-- javax.baja.bacnet.BacnetConst.PROPERTY_ARRAY_INDEX_NOT_USED -->
<field name="PROPERTY_ARRAY_INDEX_NOT_USED"  public="true" static="true" final="true">
<type class="int"/>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description/>
<tag name="@deprecated">As of 3.5.  Use &lt;code&gt;NOT_USED&lt;/code&gt; instead.</tag>
</field>

<!-- javax.baja.bacnet.BacnetConst.NO_PRIORITY -->
<field name="NO_PRIORITY"  public="true" static="true" final="true">
<type class="int"/>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description/>
<tag name="@deprecated">As of 3.5.  Use &lt;code&gt;NOT_USED&lt;/code&gt; instead.</tag>
</field>

<!-- javax.baja.bacnet.BacnetConst.RANGE_NOT_USED -->
<field name="RANGE_NOT_USED"  public="true" static="true" final="true">
<type class="int"/>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description/>
<tag name="@deprecated">As of 3.5.  Use &lt;code&gt;NOT_USED&lt;/code&gt; instead.</tag>
</field>

<!-- javax.baja.bacnet.BacnetConst.SEQUENCE_NUMBER_NOT_USED -->
<field name="SEQUENCE_NUMBER_NOT_USED"  public="true" static="true" final="true">
<type class="int"/>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description/>
<tag name="@deprecated">As of 3.5.  Use &lt;code&gt;NOT_USED&lt;/code&gt; instead.</tag>
</field>

<!-- javax.baja.bacnet.BacnetConst.REFERENCE_INDEX_NOT_USED -->
<field name="REFERENCE_INDEX_NOT_USED"  public="true" static="true" final="true">
<type class="int"/>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description/>
<tag name="@deprecated">As of 3.5.  Use &lt;code&gt;NOT_USED&lt;/code&gt; instead.</tag>
</field>

<!-- javax.baja.bacnet.BacnetConst.NOT_USED -->
<field name="NOT_USED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Generic flag to indicate an optional parameter is not being used.
</description>
</field>

<!-- javax.baja.bacnet.BacnetConst.BACNET_SF_MASK -->
<field name="BACNET_SF_MASK"  public="true" static="true" final="true">
<type class="int"/>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description/>
<tag name="@deprecated">as of 3.5</tag>
</field>

<!-- javax.baja.bacnet.BacnetConst.NIAGARA_SF_MASK -->
<field name="NIAGARA_SF_MASK"  public="true" static="true" final="true">
<type class="int"/>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description/>
<tag name="@deprecated">as of 3.5</tag>
</field>

<!-- javax.baja.bacnet.BacnetConst.BACNET_SBITS_MASK -->
<field name="BACNET_SBITS_MASK"  public="true" static="true" final="true">
<type class="int"/>
<description>
BStatus bits used in BACnetStatusFlags
</description>
</field>

<!-- javax.baja.bacnet.BacnetConst.MAX_BACNET_UNSIGNED -->
<field name="MAX_BACNET_UNSIGNED"  public="true" static="true" final="true">
<type class="long"/>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Maximum Unsigned32 value
</description>
<tag name="@deprecated">as of 3.6; use BBacnetUnsigned.MAX_UNSIGNED_VALUE instead.</tag>
</field>

<!-- javax.baja.bacnet.BacnetConst.PROPRIETARY_PREFIX -->
<field name="PROPRIETARY_PREFIX"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASHRAE_PREFIX -->
<field name="ASHRAE_PREFIX"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.PROPRIETARY_PREFIX_LENGTH -->
<field name="PROPRIETARY_PREFIX_LENGTH"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASHRAE_PREFIX_LENGTH -->
<field name="ASHRAE_PREFIX_LENGTH"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_JANUARY -->
<field name="BAC_JANUARY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Months
</description>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_FEBRUARY -->
<field name="BAC_FEBRUARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_MARCH -->
<field name="BAC_MARCH"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_APRIL -->
<field name="BAC_APRIL"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_MAY -->
<field name="BAC_MAY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_JUNE -->
<field name="BAC_JUNE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_JULY -->
<field name="BAC_JULY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_AUGUST -->
<field name="BAC_AUGUST"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_SEPTEMBER -->
<field name="BAC_SEPTEMBER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_OCTOBER -->
<field name="BAC_OCTOBER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_NOVEMBER -->
<field name="BAC_NOVEMBER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_DECEMBER -->
<field name="BAC_DECEMBER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.FIRST_WEEK -->
<field name="FIRST_WEEK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Weeks
</description>
</field>

<!-- javax.baja.bacnet.BacnetConst.SECOND_WEEK -->
<field name="SECOND_WEEK"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.THIRD_WEEK -->
<field name="THIRD_WEEK"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.FOURTH_WEEK -->
<field name="FOURTH_WEEK"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.FIFTH_WEEK -->
<field name="FIFTH_WEEK"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.LAST_SEVEN_DAYS -->
<field name="LAST_SEVEN_DAYS"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_MONDAY -->
<field name="BAC_MONDAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Days
</description>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_TUESDAY -->
<field name="BAC_TUESDAY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_WEDNESDAY -->
<field name="BAC_WEDNESDAY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_THURSDAY -->
<field name="BAC_THURSDAY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_FRIDAY -->
<field name="BAC_FRIDAY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_SATURDAY -->
<field name="BAC_SATURDAY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.BAC_SUNDAY -->
<field name="BAC_SUNDAY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.NIAGARA_SUNDAY -->
<field name="NIAGARA_SUNDAY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_NULL -->
<field name="ASN_NULL"  public="true" static="true" final="true">
<type class="int"/>
<description>
see Bacnet Spec, clause ********
</description>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_BOOLEAN -->
<field name="ASN_BOOLEAN"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_UNSIGNED -->
<field name="ASN_UNSIGNED"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_INTEGER -->
<field name="ASN_INTEGER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_REAL -->
<field name="ASN_REAL"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_DOUBLE -->
<field name="ASN_DOUBLE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_OCTET_STRING -->
<field name="ASN_OCTET_STRING"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_CHARACTER_STRING -->
<field name="ASN_CHARACTER_STRING"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_BIT_STRING -->
<field name="ASN_BIT_STRING"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_ENUMERATED -->
<field name="ASN_ENUMERATED"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_DATE -->
<field name="ASN_DATE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_TIME -->
<field name="ASN_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_OBJECT_IDENTIFIER -->
<field name="ASN_OBJECT_IDENTIFIER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASHRAE_RESERVED_13 -->
<field name="ASHRAE_RESERVED_13"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASHRAE_RESERVED_14 -->
<field name="ASHRAE_RESERVED_14"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASHRAE_RESERVED_15 -->
<field name="ASHRAE_RESERVED_15"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_CONSTRUCTED_DATA -->
<field name="ASN_CONSTRUCTED_DATA"  public="true" static="true" final="true">
<type class="int"/>
<description>
Special data types
</description>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_BACNET_ARRAY -->
<field name="ASN_BACNET_ARRAY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_BACNET_LIST -->
<field name="ASN_BACNET_LIST"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_ANY -->
<field name="ASN_ANY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_CHOICE -->
<field name="ASN_CHOICE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_UNKNOWN_PROPRIETARY -->
<field name="ASN_UNKNOWN_PROPRIETARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ASN_PRIMITIVE_TAGS -->
<field name="ASN_PRIMITIVE_TAGS"  public="true" static="true" final="true">
<type class="java.lang.String" dimension="1"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.DYNAMIC_OBJECTS_FOLDER_NAME -->
<field name="DYNAMIC_OBJECTS_FOLDER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.DYNAMIC_POINTS_CREATED_FOR_EVENT_ENROLLMENT -->
<field name="DYNAMIC_POINTS_CREATED_FOR_EVENT_ENROLLMENT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ZERO -->
<field name="ZERO"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.ONE -->
<field name="ONE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.TWO -->
<field name="TWO"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.THREE -->
<field name="THREE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.FOUR -->
<field name="FOUR"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.FIVE -->
<field name="FIVE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.THOUSAND -->
<field name="THOUSAND"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.COV_LIFETIME_LIMIT -->
<field name="COV_LIFETIME_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetConst.noWrite -->
<field name="noWrite"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description>
Context used when database values set by&#xa; reading from the physical device - used to&#xa; short circuit writing the data back to the device.
</description>
</field>

<!-- javax.baja.bacnet.BacnetConst.nameContext -->
<field name="nameContext"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description>
Name context: If used, the object should make sure its toString returns&#xa; a String suitable for use as a Baja name.&#xa; &lt;p&gt;&#xa; Note to the caller: This is only a SUGGESTION - it is not guaranteed by&#xa; compilation rules that the implementing class will have followed this.
</description>
</field>

<!-- javax.baja.bacnet.BacnetConst.facetsContext -->
<field name="facetsContext"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description>
Facets context: If used, the object should make sure its toString returns&#xa; a String suitable for inclusion in a Facets list.&#xa; &lt;p&gt;&#xa; Note to the caller: This is only a SUGGESTION - it is not guaranteed by&#xa; compilation rules that the implementing class will have followed this.
</description>
</field>

<!-- javax.baja.bacnet.BacnetConst.objectIdContext -->
<field name="objectIdContext"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description>
ObjectID context: Used to indicate an Object ID that has an editable&#xa; instance number, but a read-only object type.
</description>
</field>

<!-- javax.baja.bacnet.BacnetConst.fallback -->
<field name="fallback"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description>
Fallback context: Used when setting an object property back to its&#xa; previous value after a property change to an invalid value.
</description>
</field>

<!-- javax.baja.bacnet.BacnetConst.deviceRegistryContext -->
<field name="deviceRegistryContext"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description>
Debug context: Used when setting a property or invoking an action&#xa; where debug information should be displayed about the operation.
</description>
</field>

<!-- javax.baja.bacnet.BacnetConst.debugContext -->
<field name="debugContext"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description>
Debug context: Used when setting a property or invoking an action&#xa; where debug information should be displayed about the operation.
</description>
</field>

</class>
</bajadoc>
