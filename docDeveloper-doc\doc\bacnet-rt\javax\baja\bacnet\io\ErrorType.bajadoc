<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.io.ErrorType" name="ErrorType" packageName="javax.baja.bacnet.io" public="true" interface="true" abstract="true" category="interface">
<description>
ErrorType is an interface representing the Bacnet Error sequence.&#xa; It is called ErrorType instead of Error to avoid conflict with&#xa; java.lang.Error.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">14 Mar 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<!-- javax.baja.bacnet.io.ErrorType.getErrorClass() -->
<method name="getErrorClass"  public="true" abstract="true">
<description>
Get the error class.
</description>
<return>
<type class="int"/>
<description>
an int representing a value in the BBacnetErrorClass&#xa; enumeration indicating the class of failure,&#xa; or null if this is a success.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.ErrorType.getErrorCode() -->
<method name="getErrorCode"  public="true" abstract="true">
<description>
Get the error code.
</description>
<return>
<type class="int"/>
<description>
an int representing a value in the BBacnetErrorCode&#xa; enumeration indicating the reason for failure,&#xa; or null if this is a success.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.ErrorType.writeEncoded(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeEncoded"  public="true" abstract="true">
<description>
Encode the property value data to Asn.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the Asn encoder.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.ErrorType.readEncoded(javax.baja.bacnet.io.AsnInput) -->
<method name="readEncoded"  public="true" abstract="true">
<description>
Decode the property value data from Asn.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the Asn decoder.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
<description>
if there is an Asn error.
</description>
</throws>
</method>

</class>
</bajadoc>
