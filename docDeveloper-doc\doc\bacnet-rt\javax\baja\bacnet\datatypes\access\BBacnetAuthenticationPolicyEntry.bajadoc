<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicyEntry" name="BBacnetAuthenticationPolicyEntry" packageName="javax.baja.bacnet.datatypes.access" public="true" final="true">
<description>
BBacnetAuthenticationPolicy represents the BBacnetAuthenticationPolicy&#xa; sequence.&#xa; &lt;p&gt;&#xa; &lt;p&gt;&#xa; policy ::= SEQUENCE {&#xa; credential-data-input [0] BACnetDeviceObjectReference,&#xa; index                 [1] Unsigned&#xa; }
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="credentialDataInput" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference"/>
<description>
Slot for the &lt;code&gt;credentialDataInput&lt;/code&gt; property.
</description>
<tag name="@see">#getCredentialDataInput</tag>
<tag name="@see">#setCredentialDataInput</tag>
</property>

<property name="index" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;index&lt;/code&gt; property.
</description>
<tag name="@see">#getIndex</tag>
<tag name="@see">#setIndex</tag>
</property>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicyEntry() -->
<constructor name="BBacnetAuthenticationPolicyEntry" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicyEntry(javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference, int) -->
<constructor name="BBacnetAuthenticationPolicyEntry" public="true">
<parameter name="deviceObjectReference">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference"/>
</parameter>
<parameter name="index">
<type class="int"/>
</parameter>
<description>
Standard constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicyEntry.getCredentialDataInput() -->
<method name="getCredentialDataInput"  public="true">
<description>
Get the &lt;code&gt;credentialDataInput&lt;/code&gt; property.
</description>
<tag name="@see">#credentialDataInput</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicyEntry.setCredentialDataInput(javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference) -->
<method name="setCredentialDataInput"  public="true">
<description>
Set the &lt;code&gt;credentialDataInput&lt;/code&gt; property.
</description>
<tag name="@see">#credentialDataInput</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicyEntry.getIndex() -->
<method name="getIndex"  public="true">
<description>
Get the &lt;code&gt;index&lt;/code&gt; property.
</description>
<tag name="@see">#index</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicyEntry.setIndex(int) -->
<method name="setIndex"  public="true">
<description>
Set the &lt;code&gt;index&lt;/code&gt; property.
</description>
<tag name="@see">#index</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicyEntry.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicyEntry.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicyEntry.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicyEntry.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicyEntry.credentialDataInput -->
<field name="credentialDataInput"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;credentialDataInput&lt;/code&gt; property.
</description>
<tag name="@see">#getCredentialDataInput</tag>
<tag name="@see">#setCredentialDataInput</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicyEntry.index -->
<field name="index"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;index&lt;/code&gt; property.
</description>
<tag name="@see">#getIndex</tag>
<tag name="@see">#setIndex</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicyEntry.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicyEntry.CREDENTIAL_DATA_INPUT_TAG -->
<field name="CREDENTIAL_DATA_INPUT_TAG"  public="true" static="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicyEntry.INDEX_TAG -->
<field name="INDEX_TAG"  public="true" static="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
