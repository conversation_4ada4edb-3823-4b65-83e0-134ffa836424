<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetEngineeringUnits" name="BBacnetEngineeringUnits" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetEngineeringUnits represents the Bacnet&#xa; Engineering Units enumeration.&#xa; &lt;p&gt;&#xa; BBacnetEngineeringUnits is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-255 are reserved for use by ASHRAE.&#xa; Values from 256-65535&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.&#xa; &lt;p&gt;&#xa; Bacnet Engineering Units differ from Niagara Engineering Units.&#xa; The &lt;code&gt;niagaraName&lt;/code&gt; field provides the String name to&#xa; be passed to the Niagara unit database to get the correct &lt;code&gt;BUnit&lt;/code&gt;.
</description>
<tag name="@author">Craig Gemmill</tag>
<tag name="@version">$Revision: 5$ $Date: 12/19/01 4:35:56 PM$</tag>
<tag name="@creation">07 Aug 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;squareMeters&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;squareFeet&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;milliamperes&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;amperes&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ohms&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;volts&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilovolts&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;megavolts&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;voltAmperes&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilovoltAmperes&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;megavoltAmperes&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;voltAmperesReactive&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilovoltAmperesReactive&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;megavoltAmperesReactive&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;degreesPhase&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;powerFactor&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;joules&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilojoules&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;wattHours&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilowattHours&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;btus&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;therms&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tonHours&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;joulesPerKilogramDryAir&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;btusPerPoundDryAir&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;cyclesPerHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;cyclesPerMinute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;hertz&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;gramsOfWaterPerKilogramDryAir&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;percentRelativeHumidity&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;millimeters&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;meters&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;inches&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;feet&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;wattsPerSquareFoot&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;wattsPerSquareMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lumens&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;luxes&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;footCandles&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilograms&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;poundsMass&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tons&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilogramsPerSecond&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilogramsPerMinute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilogramsPerHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;poundsMassPerMinute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;poundsMassPerHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;watts&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilowatts&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;megawatts&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;btusPerHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;horsepower&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tonsRefrigeration&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;pascals&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilopascals&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bars&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;poundsForcePerSquareInch&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;centimetersOfWater&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;inchesOfWater&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;millimetersOfMercury&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;centimetersOfMercury&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;inchesOfMercury&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;degreesCelsius&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;degreesKelvin&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;degreesFahrenheit&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;degreeDaysCelsius&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;degreeDaysFahrenheit&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;years&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;months&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;weeks&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;days&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;hours&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;minutes&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;seconds&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;metersPerSecond&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilometersPerHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;feetPerSecond&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;feetPerMinute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;milesPerHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;cubicFeet&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;cubicMeters&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;imperialGallons&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;liters&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;usGallons&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;cubicFeetPerMinute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;cubicMetersPerSecond&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;imperialGallonsPerMinute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;litersPerSecond&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;litersPerMinute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;usGallonsPerMinute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;degreesAngular&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;degreesCelsiusPerHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;degreesCelsiusPerMinute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;degreesFahrenheitPerHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;degreesFahrenheitPerMinute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;noUnits&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;partsPerMillion&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;partsPerBillion&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;percent&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;percentPerSecond&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;perMinute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;perSecond&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;psiPerDegreeFahrenheit&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;radians&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;revolutionsPerMinute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;currency1&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;currency2&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;currency3&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;currency4&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;currency5&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;currency6&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;currency7&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;currency8&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;currency9&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;currency10&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;squareInches&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;squareCentimeters&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;btusPerPound&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;centimeters&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;poundsMassPerSecond&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deltaDegreesFahrenheit&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deltaDegreesKelvin&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilohms&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;megohms&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;millivolts&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilojoulesPerKilogram&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;megajoules&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;joulesPerDegreeKelvin&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;joulesPerKilogramDegreeKelvin&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilohertz&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;megahertz&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;perHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;milliwatts&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;hectopascals&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;millibars&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;cubicMetersPerHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;litersPerHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilowattHoursPerSquareMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilowattHoursPerSquareFoot&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;megajoulesPerSquareMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;megajoulesPerSquareFoot&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;wattsPerSquareMeterDegreeKelvin&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;cubicFeetPerSecond&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;percentObscurationPerFoot&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;percentObscurationPerMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;milliohms&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;megawattHours&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kiloBtus&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;megaBtus&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilojoulesPerKilogramDryAir&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;megajoulesPerKilogramDryAir&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilojoulesPerDegreeKelvin&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;megajoulesPerDegreeKelvin&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;newton&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;gramsPerSecond&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;gramsPerMinute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tonsPerHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kiloBtusPerHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;hundredthsSeconds&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;milliseconds&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;newtonMeters&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;millimetersPerSecond&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;millimetersPerMinute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;metersPerMinute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;metersPerHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;cubicMetersPerMinute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;metersPerSecondPerSecond&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;amperesPerMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;amperesPerSquareMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ampereSquareMeters&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;farads&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;henrys&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ohmMeters&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;siemens&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;siemensPerMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;teslas&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;voltsPerDegreeKelvin&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;voltsPerMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;webers&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;candelas&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;candelasPerSquareMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;degreesKelvinPerHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;degreesKelvinPerMinute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;jouleSeconds&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;radiansPerSecond&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;squareMetersPerNewton&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilogramsPerCubicMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;newtonSeconds&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;newtonsPerMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;wattsPerMeterPerDegreeKelvin&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;microsiemens&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;cubicFeetPerHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;usGallonsPerHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilometers&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;micrometers&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;grams&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;milligrams&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;milliliters&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;millilitersPerSecond&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;decibels&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;decibelsMillivolt&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;decibelsVolt&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;millisiemens&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;wattHourReactive&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilowattHoursReactive&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;megawattHoursReactive&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;millilitersOfWater&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;perMile&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;gramsPerGram&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilogramsPerKilogram&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;gramsPerKilogram&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;milligramsPerGram&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;milligramsPerKilogram&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;gramsPerMilliliter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;gramsPerLiter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;milligramsPerLiter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;microgramsPerLiter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;gramsPerCubicMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;milligramsPerCubicMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;microgramsPerCubicMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;nanogramsPerCubicMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;gramsPerCubicCentimeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;becquerels&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilobecquerels&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;megabecquerels&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;gray&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;milligray&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;microgray&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;sieverts&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;millisieverts&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;microsieverts&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;microsievertsPerHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;decibelsA&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;nephelometricTurbidityUnit&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ph&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;gramsPerSquareMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;minutesPerDegreeKelvin&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ohmMeterSquaredPerMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ampereSeconds&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;voltAmpereHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilovoltAmpereHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;megavoltAmpereHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;voltAmpereReactiveHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;kilovoltAmpereReactiveHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;megavoltAmpereReactiveHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;voltSquaredHours&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ampereSquaredHours&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;joulesPerHour&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;cubicFeetPerDay&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;cubicMetersPerDay&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;wattHourPerCubicMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;joulesPerCubicMeter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;molesPercent&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;pascalSecond&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;millionStandardCubicFeetPerMinute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;standardCubicFeetPerDay&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>47808</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;millionStandardCubicFeetPerDay&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>47809</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;thousandCubicFeetPerDay&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>47810</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;thousandStandardCubicFeetPerDay&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>47811</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;poundsMassPerDay&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>47812</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;millirems&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>47814</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;milliremsPerHour&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>47815</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.make(javax.baja.units.BUnit) -->
<method name="make"  public="true" static="true">
<description>
Factory method with BUnit.
</description>
<parameter name="niagaraUnits">
<type class="javax.baja.units.BUnit"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.isAshraeExtended(int) -->
<method name="isAshraeExtended"  public="true" static="true">
<description>
Is this an ASHRAE Extended Reserved id.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE Extended Reversed.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.getNiagaraUnits() -->
<method name="getNiagaraUnits"  public="true">
<description>
Get the corresponding Niagara &lt;code&gt;BUnit&lt;/code&gt; for this units type.
</description>
<return>
<type class="javax.baja.units.BUnit"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
String representation of this BEnum.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.getNiagaraUnits(int) -->
<method name="getNiagaraUnits"  public="true" static="true">
<description>
Get the corresponding Niagara &lt;code&gt;BUnit&lt;/code&gt; for the&#xa; units type with the given ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.units.BUnit"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.SQUARE_METERS -->
<field name="SQUARE_METERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for squareMeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.SQUARE_FEET -->
<field name="SQUARE_FEET"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for squareFeet.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIAMPERES -->
<field name="MILLIAMPERES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for milliamperes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.AMPERES -->
<field name="AMPERES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for amperes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.OHMS -->
<field name="OHMS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ohms.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.VOLTS -->
<field name="VOLTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for volts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOVOLTS -->
<field name="KILOVOLTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilovolts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAVOLTS -->
<field name="MEGAVOLTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for megavolts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.VOLT_AMPERES -->
<field name="VOLT_AMPERES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for voltAmperes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOVOLT_AMPERES -->
<field name="KILOVOLT_AMPERES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilovoltAmperes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAVOLT_AMPERES -->
<field name="MEGAVOLT_AMPERES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for megavoltAmperes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.VOLT_AMPERES_REACTIVE -->
<field name="VOLT_AMPERES_REACTIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for voltAmperesReactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOVOLT_AMPERES_REACTIVE -->
<field name="KILOVOLT_AMPERES_REACTIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilovoltAmperesReactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAVOLT_AMPERES_REACTIVE -->
<field name="MEGAVOLT_AMPERES_REACTIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for megavoltAmperesReactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_PHASE -->
<field name="DEGREES_PHASE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for degreesPhase.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.POWER_FACTOR -->
<field name="POWER_FACTOR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for powerFactor.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.JOULES -->
<field name="JOULES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for joules.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOJOULES -->
<field name="KILOJOULES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilojoules.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WATT_HOURS -->
<field name="WATT_HOURS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for wattHours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOWATT_HOURS -->
<field name="KILOWATT_HOURS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilowattHours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.BTUS -->
<field name="BTUS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for btus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.THERMS -->
<field name="THERMS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for therms.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.TON_HOURS -->
<field name="TON_HOURS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tonHours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.JOULES_PER_KILOGRAM_DRY_AIR -->
<field name="JOULES_PER_KILOGRAM_DRY_AIR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for joulesPerKilogramDryAir.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.BTUS_PER_POUND_DRY_AIR -->
<field name="BTUS_PER_POUND_DRY_AIR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for btusPerPoundDryAir.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CYCLES_PER_HOUR -->
<field name="CYCLES_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for cyclesPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CYCLES_PER_MINUTE -->
<field name="CYCLES_PER_MINUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for cyclesPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.HERTZ -->
<field name="HERTZ"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for hertz.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_OF_WATER_PER_KILOGRAM_DRY_AIR -->
<field name="GRAMS_OF_WATER_PER_KILOGRAM_DRY_AIR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for gramsOfWaterPerKilogramDryAir.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PERCENT_RELATIVE_HUMIDITY -->
<field name="PERCENT_RELATIVE_HUMIDITY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for percentRelativeHumidity.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIMETERS -->
<field name="MILLIMETERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for millimeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.METERS -->
<field name="METERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for meters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.INCHES -->
<field name="INCHES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for inches.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.FEET -->
<field name="FEET"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for feet.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WATTS_PER_SQUARE_FOOT -->
<field name="WATTS_PER_SQUARE_FOOT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for wattsPerSquareFoot.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WATTS_PER_SQUARE_METER -->
<field name="WATTS_PER_SQUARE_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for wattsPerSquareMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.LUMENS -->
<field name="LUMENS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lumens.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.LUXES -->
<field name="LUXES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for luxes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.FOOT_CANDLES -->
<field name="FOOT_CANDLES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for footCandles.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOGRAMS -->
<field name="KILOGRAMS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilograms.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.POUNDS_MASS -->
<field name="POUNDS_MASS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for poundsMass.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.TONS -->
<field name="TONS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tons.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOGRAMS_PER_SECOND -->
<field name="KILOGRAMS_PER_SECOND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilogramsPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOGRAMS_PER_MINUTE -->
<field name="KILOGRAMS_PER_MINUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilogramsPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOGRAMS_PER_HOUR -->
<field name="KILOGRAMS_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilogramsPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.POUNDS_MASS_PER_MINUTE -->
<field name="POUNDS_MASS_PER_MINUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for poundsMassPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.POUNDS_MASS_PER_HOUR -->
<field name="POUNDS_MASS_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for poundsMassPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WATTS -->
<field name="WATTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for watts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOWATTS -->
<field name="KILOWATTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilowatts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAWATTS -->
<field name="MEGAWATTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for megawatts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.BTUS_PER_HOUR -->
<field name="BTUS_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for btusPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.HORSEPOWER -->
<field name="HORSEPOWER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for horsepower.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.TONS_REFRIGERATION -->
<field name="TONS_REFRIGERATION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tonsRefrigeration.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PASCALS -->
<field name="PASCALS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for pascals.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOPASCALS -->
<field name="KILOPASCALS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilopascals.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.BARS -->
<field name="BARS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bars.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.POUNDS_FORCE_PER_SQUARE_INCH -->
<field name="POUNDS_FORCE_PER_SQUARE_INCH"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for poundsForcePerSquareInch.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CENTIMETERS_OF_WATER -->
<field name="CENTIMETERS_OF_WATER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for centimetersOfWater.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.INCHES_OF_WATER -->
<field name="INCHES_OF_WATER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for inchesOfWater.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIMETERS_OF_MERCURY -->
<field name="MILLIMETERS_OF_MERCURY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for millimetersOfMercury.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CENTIMETERS_OF_MERCURY -->
<field name="CENTIMETERS_OF_MERCURY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for centimetersOfMercury.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.INCHES_OF_MERCURY -->
<field name="INCHES_OF_MERCURY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for inchesOfMercury.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_CELSIUS -->
<field name="DEGREES_CELSIUS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for degreesCelsius.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_KELVIN -->
<field name="DEGREES_KELVIN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for degreesKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_FAHRENHEIT -->
<field name="DEGREES_FAHRENHEIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for degreesFahrenheit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREE_DAYS_CELSIUS -->
<field name="DEGREE_DAYS_CELSIUS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for degreeDaysCelsius.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREE_DAYS_FAHRENHEIT -->
<field name="DEGREE_DAYS_FAHRENHEIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for degreeDaysFahrenheit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.YEARS -->
<field name="YEARS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for years.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MONTHS -->
<field name="MONTHS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for months.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WEEKS -->
<field name="WEEKS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for weeks.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DAYS -->
<field name="DAYS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for days.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.HOURS -->
<field name="HOURS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for hours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MINUTES -->
<field name="MINUTES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for minutes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.SECONDS -->
<field name="SECONDS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for seconds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.METERS_PER_SECOND -->
<field name="METERS_PER_SECOND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for metersPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOMETERS_PER_HOUR -->
<field name="KILOMETERS_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilometersPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.FEET_PER_SECOND -->
<field name="FEET_PER_SECOND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for feetPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.FEET_PER_MINUTE -->
<field name="FEET_PER_MINUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for feetPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILES_PER_HOUR -->
<field name="MILES_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for milesPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_FEET -->
<field name="CUBIC_FEET"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for cubicFeet.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_METERS -->
<field name="CUBIC_METERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for cubicMeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.IMPERIAL_GALLONS -->
<field name="IMPERIAL_GALLONS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for imperialGallons.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.LITERS -->
<field name="LITERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for liters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.US_GALLONS -->
<field name="US_GALLONS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for usGallons.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_FEET_PER_MINUTE -->
<field name="CUBIC_FEET_PER_MINUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for cubicFeetPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_METERS_PER_SECOND -->
<field name="CUBIC_METERS_PER_SECOND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for cubicMetersPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.IMPERIAL_GALLONS_PER_MINUTE -->
<field name="IMPERIAL_GALLONS_PER_MINUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for imperialGallonsPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.LITERS_PER_SECOND -->
<field name="LITERS_PER_SECOND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for litersPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.LITERS_PER_MINUTE -->
<field name="LITERS_PER_MINUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for litersPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.US_GALLONS_PER_MINUTE -->
<field name="US_GALLONS_PER_MINUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for usGallonsPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_ANGULAR -->
<field name="DEGREES_ANGULAR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for degreesAngular.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_CELSIUS_PER_HOUR -->
<field name="DEGREES_CELSIUS_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for degreesCelsiusPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_CELSIUS_PER_MINUTE -->
<field name="DEGREES_CELSIUS_PER_MINUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for degreesCelsiusPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_FAHRENHEIT_PER_HOUR -->
<field name="DEGREES_FAHRENHEIT_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for degreesFahrenheitPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_FAHRENHEIT_PER_MINUTE -->
<field name="DEGREES_FAHRENHEIT_PER_MINUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for degreesFahrenheitPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.NO_UNITS -->
<field name="NO_UNITS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for noUnits.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PARTS_PER_MILLION -->
<field name="PARTS_PER_MILLION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for partsPerMillion.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PARTS_PER_BILLION -->
<field name="PARTS_PER_BILLION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for partsPerBillion.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PERCENT -->
<field name="PERCENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for percent.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PERCENT_PER_SECOND -->
<field name="PERCENT_PER_SECOND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for percentPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PER_MINUTE -->
<field name="PER_MINUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for perMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PER_SECOND -->
<field name="PER_SECOND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for perSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PSI_PER_DEGREE_FAHRENHEIT -->
<field name="PSI_PER_DEGREE_FAHRENHEIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for psiPerDegreeFahrenheit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.RADIANS -->
<field name="RADIANS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for radians.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.REVOLUTIONS_PER_MINUTE -->
<field name="REVOLUTIONS_PER_MINUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for revolutionsPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_1 -->
<field name="CURRENCY_1"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for currency1.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_2 -->
<field name="CURRENCY_2"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for currency2.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_3 -->
<field name="CURRENCY_3"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for currency3.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_4 -->
<field name="CURRENCY_4"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for currency4.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_5 -->
<field name="CURRENCY_5"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for currency5.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_6 -->
<field name="CURRENCY_6"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for currency6.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_7 -->
<field name="CURRENCY_7"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for currency7.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_8 -->
<field name="CURRENCY_8"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for currency8.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_9 -->
<field name="CURRENCY_9"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for currency9.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_10 -->
<field name="CURRENCY_10"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for currency10.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.SQUARE_INCHES -->
<field name="SQUARE_INCHES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for squareInches.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.SQUARE_CENTIMETERS -->
<field name="SQUARE_CENTIMETERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for squareCentimeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.BTUS_PER_POUND -->
<field name="BTUS_PER_POUND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for btusPerPound.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CENTIMETERS -->
<field name="CENTIMETERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for centimeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.POUNDS_MASS_PER_SECOND -->
<field name="POUNDS_MASS_PER_SECOND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for poundsMassPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DELTA_DEGREES_FAHRENHEIT -->
<field name="DELTA_DEGREES_FAHRENHEIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deltaDegreesFahrenheit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DELTA_DEGREES_KELVIN -->
<field name="DELTA_DEGREES_KELVIN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deltaDegreesKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOHMS -->
<field name="KILOHMS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilohms.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGOHMS -->
<field name="MEGOHMS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for megohms.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIVOLTS -->
<field name="MILLIVOLTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for millivolts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOJOULES_PER_KILOGRAM -->
<field name="KILOJOULES_PER_KILOGRAM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilojoulesPerKilogram.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAJOULES -->
<field name="MEGAJOULES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for megajoules.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.JOULES_PER_DEGREE_KELVIN -->
<field name="JOULES_PER_DEGREE_KELVIN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for joulesPerDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.JOULES_PER_KILOGRAM_DEGREE_KELVIN -->
<field name="JOULES_PER_KILOGRAM_DEGREE_KELVIN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for joulesPerKilogramDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOHERTZ -->
<field name="KILOHERTZ"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilohertz.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAHERTZ -->
<field name="MEGAHERTZ"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for megahertz.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PER_HOUR -->
<field name="PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for perHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIWATTS -->
<field name="MILLIWATTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for milliwatts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.HECTOPASCALS -->
<field name="HECTOPASCALS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for hectopascals.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIBARS -->
<field name="MILLIBARS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for millibars.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_METERS_PER_HOUR -->
<field name="CUBIC_METERS_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for cubicMetersPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.LITERS_PER_HOUR -->
<field name="LITERS_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for litersPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOWATT_HOURS_PER_SQUARE_METER -->
<field name="KILOWATT_HOURS_PER_SQUARE_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilowattHoursPerSquareMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOWATT_HOURS_PER_SQUARE_FOOT -->
<field name="KILOWATT_HOURS_PER_SQUARE_FOOT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilowattHoursPerSquareFoot.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAJOULES_PER_SQUARE_METER -->
<field name="MEGAJOULES_PER_SQUARE_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for megajoulesPerSquareMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAJOULES_PER_SQUARE_FOOT -->
<field name="MEGAJOULES_PER_SQUARE_FOOT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for megajoulesPerSquareFoot.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WATTS_PER_SQUARE_METER_DEGREE_KELVIN -->
<field name="WATTS_PER_SQUARE_METER_DEGREE_KELVIN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for wattsPerSquareMeterDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_FEET_PER_SECOND -->
<field name="CUBIC_FEET_PER_SECOND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for cubicFeetPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PERCENT_OBSCURATION_PER_FOOT -->
<field name="PERCENT_OBSCURATION_PER_FOOT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for percentObscurationPerFoot.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PERCENT_OBSCURATION_PER_METER -->
<field name="PERCENT_OBSCURATION_PER_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for percentObscurationPerMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIOHMS -->
<field name="MILLIOHMS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for milliohms.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAWATT_HOURS -->
<field name="MEGAWATT_HOURS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for megawattHours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILO_BTUS -->
<field name="KILO_BTUS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kiloBtus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGA_BTUS -->
<field name="MEGA_BTUS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for megaBtus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOJOULES_PER_KILOGRAM_DRY_AIR -->
<field name="KILOJOULES_PER_KILOGRAM_DRY_AIR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilojoulesPerKilogramDryAir.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAJOULES_PER_KILOGRAM_DRY_AIR -->
<field name="MEGAJOULES_PER_KILOGRAM_DRY_AIR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for megajoulesPerKilogramDryAir.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOJOULES_PER_DEGREE_KELVIN -->
<field name="KILOJOULES_PER_DEGREE_KELVIN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilojoulesPerDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAJOULES_PER_DEGREE_KELVIN -->
<field name="MEGAJOULES_PER_DEGREE_KELVIN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for megajoulesPerDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.NEWTON -->
<field name="NEWTON"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for newton.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_PER_SECOND -->
<field name="GRAMS_PER_SECOND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for gramsPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_PER_MINUTE -->
<field name="GRAMS_PER_MINUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for gramsPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.TONS_PER_HOUR -->
<field name="TONS_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tonsPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILO_BTUS_PER_HOUR -->
<field name="KILO_BTUS_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kiloBtusPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.HUNDREDTHS_SECONDS -->
<field name="HUNDREDTHS_SECONDS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for hundredthsSeconds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLISECONDS -->
<field name="MILLISECONDS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for milliseconds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.NEWTON_METERS -->
<field name="NEWTON_METERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for newtonMeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIMETERS_PER_SECOND -->
<field name="MILLIMETERS_PER_SECOND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for millimetersPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIMETERS_PER_MINUTE -->
<field name="MILLIMETERS_PER_MINUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for millimetersPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.METERS_PER_MINUTE -->
<field name="METERS_PER_MINUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for metersPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.METERS_PER_HOUR -->
<field name="METERS_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for metersPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_METERS_PER_MINUTE -->
<field name="CUBIC_METERS_PER_MINUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for cubicMetersPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.METERS_PER_SECOND_PER_SECOND -->
<field name="METERS_PER_SECOND_PER_SECOND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for metersPerSecondPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.AMPERES_PER_METER -->
<field name="AMPERES_PER_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for amperesPerMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.AMPERES_PER_SQUARE_METER -->
<field name="AMPERES_PER_SQUARE_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for amperesPerSquareMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.AMPERE_SQUARE_METERS -->
<field name="AMPERE_SQUARE_METERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ampereSquareMeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.FARADS -->
<field name="FARADS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for farads.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.HENRYS -->
<field name="HENRYS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for henrys.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.OHM_METERS -->
<field name="OHM_METERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ohmMeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.SIEMENS -->
<field name="SIEMENS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for siemens.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.SIEMENS_PER_METER -->
<field name="SIEMENS_PER_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for siemensPerMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.TESLAS -->
<field name="TESLAS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for teslas.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.VOLTS_PER_DEGREE_KELVIN -->
<field name="VOLTS_PER_DEGREE_KELVIN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for voltsPerDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.VOLTS_PER_METER -->
<field name="VOLTS_PER_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for voltsPerMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WEBERS -->
<field name="WEBERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for webers.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CANDELAS -->
<field name="CANDELAS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for candelas.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CANDELAS_PER_SQUARE_METER -->
<field name="CANDELAS_PER_SQUARE_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for candelasPerSquareMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_KELVIN_PER_HOUR -->
<field name="DEGREES_KELVIN_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for degreesKelvinPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_KELVIN_PER_MINUTE -->
<field name="DEGREES_KELVIN_PER_MINUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for degreesKelvinPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.JOULE_SECONDS -->
<field name="JOULE_SECONDS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for jouleSeconds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.RADIANS_PER_SECOND -->
<field name="RADIANS_PER_SECOND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for radiansPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.SQUARE_METERS_PER_NEWTON -->
<field name="SQUARE_METERS_PER_NEWTON"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for squareMetersPerNewton.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOGRAMS_PER_CUBIC_METER -->
<field name="KILOGRAMS_PER_CUBIC_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilogramsPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.NEWTON_SECONDS -->
<field name="NEWTON_SECONDS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for newtonSeconds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.NEWTONS_PER_METER -->
<field name="NEWTONS_PER_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for newtonsPerMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WATTS_PER_METER_PER_DEGREE_KELVIN -->
<field name="WATTS_PER_METER_PER_DEGREE_KELVIN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for wattsPerMeterPerDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MICROSIEMENS -->
<field name="MICROSIEMENS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for microsiemens.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_FEET_PER_HOUR -->
<field name="CUBIC_FEET_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for cubicFeetPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.US_GALLONS_PER_HOUR -->
<field name="US_GALLONS_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for usGallonsPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOMETERS -->
<field name="KILOMETERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilometers.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MICROMETERS -->
<field name="MICROMETERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for micrometers.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS -->
<field name="GRAMS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for grams.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIGRAMS -->
<field name="MILLIGRAMS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for milligrams.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLILITERS -->
<field name="MILLILITERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for milliliters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLILITERS_PER_SECOND -->
<field name="MILLILITERS_PER_SECOND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for millilitersPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DECIBELS -->
<field name="DECIBELS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for decibels.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DECIBELS_MILLIVOLT -->
<field name="DECIBELS_MILLIVOLT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for decibelsMillivolt.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DECIBELS_VOLT -->
<field name="DECIBELS_VOLT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for decibelsVolt.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLISIEMENS -->
<field name="MILLISIEMENS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for millisiemens.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WATT_HOUR_REACTIVE -->
<field name="WATT_HOUR_REACTIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for wattHourReactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOWATT_HOURS_REACTIVE -->
<field name="KILOWATT_HOURS_REACTIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilowattHoursReactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAWATT_HOURS_REACTIVE -->
<field name="MEGAWATT_HOURS_REACTIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for megawattHoursReactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLILITERS_OF_WATER -->
<field name="MILLILITERS_OF_WATER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for millilitersOfWater.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PER_MILE -->
<field name="PER_MILE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for perMile.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_PER_GRAM -->
<field name="GRAMS_PER_GRAM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for gramsPerGram.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOGRAMS_PER_KILOGRAM -->
<field name="KILOGRAMS_PER_KILOGRAM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilogramsPerKilogram.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_PER_KILOGRAM -->
<field name="GRAMS_PER_KILOGRAM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for gramsPerKilogram.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIGRAMS_PER_GRAM -->
<field name="MILLIGRAMS_PER_GRAM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for milligramsPerGram.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIGRAMS_PER_KILOGRAM -->
<field name="MILLIGRAMS_PER_KILOGRAM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for milligramsPerKilogram.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_PER_MILLILITER -->
<field name="GRAMS_PER_MILLILITER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for gramsPerMilliliter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_PER_LITER -->
<field name="GRAMS_PER_LITER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for gramsPerLiter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIGRAMS_PER_LITER -->
<field name="MILLIGRAMS_PER_LITER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for milligramsPerLiter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MICROGRAMS_PER_LITER -->
<field name="MICROGRAMS_PER_LITER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for microgramsPerLiter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_PER_CUBIC_METER -->
<field name="GRAMS_PER_CUBIC_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for gramsPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIGRAMS_PER_CUBIC_METER -->
<field name="MILLIGRAMS_PER_CUBIC_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for milligramsPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MICROGRAMS_PER_CUBIC_METER -->
<field name="MICROGRAMS_PER_CUBIC_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for microgramsPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.NANOGRAMS_PER_CUBIC_METER -->
<field name="NANOGRAMS_PER_CUBIC_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for nanogramsPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_PER_CUBIC_CENTIMETER -->
<field name="GRAMS_PER_CUBIC_CENTIMETER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for gramsPerCubicCentimeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.BECQUERELS -->
<field name="BECQUERELS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for becquerels.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOBECQUERELS -->
<field name="KILOBECQUERELS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilobecquerels.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGABECQUERELS -->
<field name="MEGABECQUERELS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for megabecquerels.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAY -->
<field name="GRAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for gray.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIGRAY -->
<field name="MILLIGRAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for milligray.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MICROGRAY -->
<field name="MICROGRAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for microgray.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.SIEVERTS -->
<field name="SIEVERTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for sieverts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLISIEVERTS -->
<field name="MILLISIEVERTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for millisieverts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MICROSIEVERTS -->
<field name="MICROSIEVERTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for microsieverts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MICROSIEVERTS_PER_HOUR -->
<field name="MICROSIEVERTS_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for microsievertsPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DECIBELS_A -->
<field name="DECIBELS_A"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for decibelsA.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.NEPHELOMETRIC_TURBIDITY_UNIT -->
<field name="NEPHELOMETRIC_TURBIDITY_UNIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for nephelometricTurbidityUnit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PH -->
<field name="PH"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ph.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_PER_SQUARE_METER -->
<field name="GRAMS_PER_SQUARE_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for gramsPerSquareMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MINUTES_PER_DEGREE_KELVIN -->
<field name="MINUTES_PER_DEGREE_KELVIN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for minutesPerDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.OHM_METER_SQUARED_PER_METER -->
<field name="OHM_METER_SQUARED_PER_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ohmMeterSquaredPerMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.AMPERE_SECONDS -->
<field name="AMPERE_SECONDS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ampereSeconds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.VOLT_AMPERE_HOUR -->
<field name="VOLT_AMPERE_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for voltAmpereHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOVOLT_AMPERE_HOUR -->
<field name="KILOVOLT_AMPERE_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilovoltAmpereHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAVOLT_AMPERE_HOUR -->
<field name="MEGAVOLT_AMPERE_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for megavoltAmpereHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.VOLT_AMPERE_REACTIVE_HOUR -->
<field name="VOLT_AMPERE_REACTIVE_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for voltAmpereReactiveHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOVOLT_AMPERE_REACTIVE_HOUR -->
<field name="KILOVOLT_AMPERE_REACTIVE_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for kilovoltAmpereReactiveHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAVOLT_AMPERE_REACTIVE_HOUR -->
<field name="MEGAVOLT_AMPERE_REACTIVE_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for megavoltAmpereReactiveHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.VOLT_SQUARED_HOURS -->
<field name="VOLT_SQUARED_HOURS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for voltSquaredHours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.AMPERE_SQUARED_HOURS -->
<field name="AMPERE_SQUARED_HOURS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ampereSquaredHours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.JOULES_PER_HOUR -->
<field name="JOULES_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for joulesPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_FEET_PER_DAY -->
<field name="CUBIC_FEET_PER_DAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for cubicFeetPerDay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_METERS_PER_DAY -->
<field name="CUBIC_METERS_PER_DAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for cubicMetersPerDay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WATT_HOUR_PER_CUBIC_METER -->
<field name="WATT_HOUR_PER_CUBIC_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for wattHourPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.JOULES_PER_CUBIC_METER -->
<field name="JOULES_PER_CUBIC_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for joulesPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MOLES_PERCENT -->
<field name="MOLES_PERCENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for molesPercent.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PASCAL_SECOND -->
<field name="PASCAL_SECOND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for pascalSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLION_STANDARD_CUBIC_FEET_PER_MINUTE -->
<field name="MILLION_STANDARD_CUBIC_FEET_PER_MINUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for millionStandardCubicFeetPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.STANDARD_CUBIC_FEET_PER_DAY -->
<field name="STANDARD_CUBIC_FEET_PER_DAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for standardCubicFeetPerDay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLION_STANDARD_CUBIC_FEET_PER_DAY -->
<field name="MILLION_STANDARD_CUBIC_FEET_PER_DAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for millionStandardCubicFeetPerDay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.THOUSAND_CUBIC_FEET_PER_DAY -->
<field name="THOUSAND_CUBIC_FEET_PER_DAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for thousandCubicFeetPerDay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.THOUSAND_STANDARD_CUBIC_FEET_PER_DAY -->
<field name="THOUSAND_STANDARD_CUBIC_FEET_PER_DAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for thousandStandardCubicFeetPerDay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.POUNDS_MASS_PER_DAY -->
<field name="POUNDS_MASS_PER_DAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for poundsMassPerDay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIREMS -->
<field name="MILLIREMS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for millirems.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIREMS_PER_HOUR -->
<field name="MILLIREMS_PER_HOUR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for milliremsPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.SQUARE_METERS_NAME -->
<field name="SQUARE_METERS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for squareMeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.SQUARE_FEET_NAME -->
<field name="SQUARE_FEET_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for squareFeet.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIAMPERES_NAME -->
<field name="MILLIAMPERES_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for milliamperes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.AMPERES_NAME -->
<field name="AMPERES_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for amperes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.OHMS_NAME -->
<field name="OHMS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for ohms.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.VOLTS_NAME -->
<field name="VOLTS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for volts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOVOLTS_NAME -->
<field name="KILOVOLTS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilovolts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAVOLTS_NAME -->
<field name="MEGAVOLTS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for megavolts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.VOLT_AMPERES_NAME -->
<field name="VOLT_AMPERES_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for voltAmperes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOVOLT_AMPERES_NAME -->
<field name="KILOVOLT_AMPERES_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilovoltAmperes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAVOLT_AMPERES_NAME -->
<field name="MEGAVOLT_AMPERES_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for megavoltAmperes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.VOLT_AMPERES_REACTIVE_NAME -->
<field name="VOLT_AMPERES_REACTIVE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for voltAmperesReactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOVOLT_AMPERES_REACTIVE_NAME -->
<field name="KILOVOLT_AMPERES_REACTIVE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilovoltAmperesReactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAVOLT_AMPERES_REACTIVE_NAME -->
<field name="MEGAVOLT_AMPERES_REACTIVE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for megavoltAmperesReactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_PHASE_NAME -->
<field name="DEGREES_PHASE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for degreesPhase.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.POWER_FACTOR_NAME -->
<field name="POWER_FACTOR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for powerFactor.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.JOULES_NAME -->
<field name="JOULES_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for joules.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOJOULES_NAME -->
<field name="KILOJOULES_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilojoules.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WATT_HOURS_NAME -->
<field name="WATT_HOURS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for wattHours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOWATT_HOURS_NAME -->
<field name="KILOWATT_HOURS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilowattHours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.BTUS_NAME -->
<field name="BTUS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for btus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.THERMS_NAME -->
<field name="THERMS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for therms.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.TON_HOURS_NAME -->
<field name="TON_HOURS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for tonHours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.JOULES_PER_KILOGRAM_DRY_AIR_NAME -->
<field name="JOULES_PER_KILOGRAM_DRY_AIR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for joulesPerKilogramDryAir.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.BTUS_PER_POUND_DRY_AIR_NAME -->
<field name="BTUS_PER_POUND_DRY_AIR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for btusPerPoundDryAir.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CYCLES_PER_HOUR_NAME -->
<field name="CYCLES_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for cyclesPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CYCLES_PER_MINUTE_NAME -->
<field name="CYCLES_PER_MINUTE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for cyclesPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.HERTZ_NAME -->
<field name="HERTZ_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for hertz.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_OF_WATER_PER_KILOGRAM_DRY_AIR_NAME -->
<field name="GRAMS_OF_WATER_PER_KILOGRAM_DRY_AIR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for gramsOfWaterPerKilogramDryAir.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PERCENT_RELATIVE_HUMIDITY_NAME -->
<field name="PERCENT_RELATIVE_HUMIDITY_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for percentRelativeHumidity.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIMETERS_NAME -->
<field name="MILLIMETERS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for millimeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.METERS_NAME -->
<field name="METERS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for meters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.INCHES_NAME -->
<field name="INCHES_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for inches.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.FEET_NAME -->
<field name="FEET_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for feet.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WATTS_PER_SQUARE_FOOT_NAME -->
<field name="WATTS_PER_SQUARE_FOOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for wattsPerSquareFoot.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WATTS_PER_SQUARE_METER_NAME -->
<field name="WATTS_PER_SQUARE_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for wattsPerSquareMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.LUMENS_NAME -->
<field name="LUMENS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for lumens.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.LUXES_NAME -->
<field name="LUXES_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for luxes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.FOOT_CANDLES_NAME -->
<field name="FOOT_CANDLES_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for footCandles.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOGRAMS_NAME -->
<field name="KILOGRAMS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilograms.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.POUNDS_MASS_NAME -->
<field name="POUNDS_MASS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for poundsMass.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.TONS_NAME -->
<field name="TONS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for tons.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOGRAMS_PER_SECOND_NAME -->
<field name="KILOGRAMS_PER_SECOND_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilogramsPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOGRAMS_PER_MINUTE_NAME -->
<field name="KILOGRAMS_PER_MINUTE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilogramsPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOGRAMS_PER_HOUR_NAME -->
<field name="KILOGRAMS_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilogramsPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.POUNDS_MASS_PER_MINUTE_NAME -->
<field name="POUNDS_MASS_PER_MINUTE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for poundsMassPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.POUNDS_MASS_PER_HOUR_NAME -->
<field name="POUNDS_MASS_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for poundsMassPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WATTS_NAME -->
<field name="WATTS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for watts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOWATTS_NAME -->
<field name="KILOWATTS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilowatts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAWATTS_NAME -->
<field name="MEGAWATTS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for megawatts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.BTUS_PER_HOUR_NAME -->
<field name="BTUS_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for btusPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.HORSEPOWER_NAME -->
<field name="HORSEPOWER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for horsepower.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.TONS_REFRIGERATION_NAME -->
<field name="TONS_REFRIGERATION_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for tonsRefrigeration.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PASCALS_NAME -->
<field name="PASCALS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for pascals.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOPASCALS_NAME -->
<field name="KILOPASCALS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilopascals.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.BARS_NAME -->
<field name="BARS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for bars.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.POUNDS_FORCE_PER_SQUARE_INCH_NAME -->
<field name="POUNDS_FORCE_PER_SQUARE_INCH_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for poundsForcePerSquareInch.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CENTIMETERS_OF_WATER_NAME -->
<field name="CENTIMETERS_OF_WATER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for centimetersOfWater.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.INCHES_OF_WATER_NAME -->
<field name="INCHES_OF_WATER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for inchesOfWater.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIMETERS_OF_MERCURY_NAME -->
<field name="MILLIMETERS_OF_MERCURY_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for millimetersOfMercury.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CENTIMETERS_OF_MERCURY_NAME -->
<field name="CENTIMETERS_OF_MERCURY_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for centimetersOfMercury.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.INCHES_OF_MERCURY_NAME -->
<field name="INCHES_OF_MERCURY_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for inchesOfMercury.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_CELSIUS_NAME -->
<field name="DEGREES_CELSIUS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for degreesCelsius.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_KELVIN_NAME -->
<field name="DEGREES_KELVIN_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for degreesKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_FAHRENHEIT_NAME -->
<field name="DEGREES_FAHRENHEIT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for degreesFahrenheit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREE_DAYS_CELSIUS_NAME -->
<field name="DEGREE_DAYS_CELSIUS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for degreeDaysCelsius.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREE_DAYS_FAHRENHEIT_NAME -->
<field name="DEGREE_DAYS_FAHRENHEIT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for degreeDaysFahrenheit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.YEARS_NAME -->
<field name="YEARS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for years.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MONTHS_NAME -->
<field name="MONTHS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for months.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WEEKS_NAME -->
<field name="WEEKS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for weeks.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DAYS_NAME -->
<field name="DAYS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for days.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.HOURS_NAME -->
<field name="HOURS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for hours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MINUTES_NAME -->
<field name="MINUTES_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for minutes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.SECONDS_NAME -->
<field name="SECONDS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for seconds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.METERS_PER_SECOND_NAME -->
<field name="METERS_PER_SECOND_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for metersPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOMETERS_PER_HOUR_NAME -->
<field name="KILOMETERS_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilometersPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.FEET_PER_SECOND_NAME -->
<field name="FEET_PER_SECOND_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for feetPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.FEET_PER_MINUTE_NAME -->
<field name="FEET_PER_MINUTE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for feetPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILES_PER_HOUR_NAME -->
<field name="MILES_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for milesPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_FEET_NAME -->
<field name="CUBIC_FEET_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for cubicFeet.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_METERS_NAME -->
<field name="CUBIC_METERS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for cubicMeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.IMPERIAL_GALLONS_NAME -->
<field name="IMPERIAL_GALLONS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for imperialGallons.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.LITERS_NAME -->
<field name="LITERS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for liters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.US_GALLONS_NAME -->
<field name="US_GALLONS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for usGallons.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_FEET_PER_MINUTE_NAME -->
<field name="CUBIC_FEET_PER_MINUTE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for cubicFeetPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_METERS_PER_SECOND_NAME -->
<field name="CUBIC_METERS_PER_SECOND_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for cubicMetersPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.IMPERIAL_GALLONS_PER_MINUTE_NAME -->
<field name="IMPERIAL_GALLONS_PER_MINUTE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for imperialGallonsPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.LITERS_PER_SECOND_NAME -->
<field name="LITERS_PER_SECOND_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for litersPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.LITERS_PER_MINUTE_NAME -->
<field name="LITERS_PER_MINUTE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for litersPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.US_GALLONS_PER_MINUTE_NAME -->
<field name="US_GALLONS_PER_MINUTE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for usGallonsPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_ANGULAR_NAME -->
<field name="DEGREES_ANGULAR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for degreesAngular.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_CELSIUS_PER_HOUR_NAME -->
<field name="DEGREES_CELSIUS_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for degreesCelsiusPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_CELSIUS_PER_MINUTE_NAME -->
<field name="DEGREES_CELSIUS_PER_MINUTE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for degreesCelsiusPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_FAHRENHEIT_PER_HOUR_NAME -->
<field name="DEGREES_FAHRENHEIT_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for degreesFahrenheitPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_FAHRENHEIT_PER_MINUTE_NAME -->
<field name="DEGREES_FAHRENHEIT_PER_MINUTE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for degreesFahrenheitPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.NO_UNITS_NAME -->
<field name="NO_UNITS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for noUnits.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PARTS_PER_MILLION_NAME -->
<field name="PARTS_PER_MILLION_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for partsPerMillion.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PARTS_PER_BILLION_NAME -->
<field name="PARTS_PER_BILLION_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for partsPerBillion.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PERCENT_NAME -->
<field name="PERCENT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for percent.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PERCENT_PER_SECOND_NAME -->
<field name="PERCENT_PER_SECOND_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for percentPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PER_MINUTE_NAME -->
<field name="PER_MINUTE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for perMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PER_SECOND_NAME -->
<field name="PER_SECOND_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for perSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PSI_PER_DEGREE_FAHRENHEIT_NAME -->
<field name="PSI_PER_DEGREE_FAHRENHEIT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for psiPerDegreeFahrenheit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.RADIANS_NAME -->
<field name="RADIANS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for radians.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.REVOLUTIONS_PER_MINUTE_NAME -->
<field name="REVOLUTIONS_PER_MINUTE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for revolutionsPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_1_NAME -->
<field name="CURRENCY_1_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for currency1.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_2_NAME -->
<field name="CURRENCY_2_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for currency2.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_3_NAME -->
<field name="CURRENCY_3_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for currency3.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_4_NAME -->
<field name="CURRENCY_4_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for currency4.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_5_NAME -->
<field name="CURRENCY_5_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for currency5.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_6_NAME -->
<field name="CURRENCY_6_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for currency6.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_7_NAME -->
<field name="CURRENCY_7_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for currency7.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_8_NAME -->
<field name="CURRENCY_8_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for currency8.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_9_NAME -->
<field name="CURRENCY_9_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for currency9.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CURRENCY_10_NAME -->
<field name="CURRENCY_10_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for currency10.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.SQUARE_INCHES_NAME -->
<field name="SQUARE_INCHES_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for squareInches.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.SQUARE_CENTIMETERS_NAME -->
<field name="SQUARE_CENTIMETERS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for squareCentimeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.BTUS_PER_POUND_NAME -->
<field name="BTUS_PER_POUND_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for btusPerPound.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CENTIMETERS_NAME -->
<field name="CENTIMETERS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for centimeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.POUNDS_MASS_PER_SECOND_NAME -->
<field name="POUNDS_MASS_PER_SECOND_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for poundsMassPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DELTA_DEGREES_FAHRENHEIT_NAME -->
<field name="DELTA_DEGREES_FAHRENHEIT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for deltaDegreesFahrenheit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DELTA_DEGREES_KELVIN_NAME -->
<field name="DELTA_DEGREES_KELVIN_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for deltaDegreesKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOHMS_NAME -->
<field name="KILOHMS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilohms.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGOHMS_NAME -->
<field name="MEGOHMS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for megohms.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIVOLTS_NAME -->
<field name="MILLIVOLTS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for millivolts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOJOULES_PER_KILOGRAM_NAME -->
<field name="KILOJOULES_PER_KILOGRAM_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilojoulesPerKilogram.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAJOULES_NAME -->
<field name="MEGAJOULES_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for megajoules.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.JOULES_PER_DEGREE_KELVIN_NAME -->
<field name="JOULES_PER_DEGREE_KELVIN_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for joulesPerDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.JOULES_PER_KILOGRAM_DEGREE_KELVIN_NAME -->
<field name="JOULES_PER_KILOGRAM_DEGREE_KELVIN_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for joulesPerKilogramDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOHERTZ_NAME -->
<field name="KILOHERTZ_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilohertz.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAHERTZ_NAME -->
<field name="MEGAHERTZ_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for megahertz.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PER_HOUR_NAME -->
<field name="PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for perHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIWATTS_NAME -->
<field name="MILLIWATTS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for milliwatts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.HECTOPASCALS_NAME -->
<field name="HECTOPASCALS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for hectopascals.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIBARS_NAME -->
<field name="MILLIBARS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for millibars.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_METERS_PER_HOUR_NAME -->
<field name="CUBIC_METERS_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for cubicMetersPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.LITERS_PER_HOUR_NAME -->
<field name="LITERS_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for litersPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOWATT_HOURS_PER_SQUARE_METER_NAME -->
<field name="KILOWATT_HOURS_PER_SQUARE_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilowattHoursPerSquareMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOWATT_HOURS_PER_SQUARE_FOOT_NAME -->
<field name="KILOWATT_HOURS_PER_SQUARE_FOOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilowattHoursPerSquareFoot.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAJOULES_PER_SQUARE_METER_NAME -->
<field name="MEGAJOULES_PER_SQUARE_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for megajoulesPerSquareMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAJOULES_PER_SQUARE_FOOT_NAME -->
<field name="MEGAJOULES_PER_SQUARE_FOOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for megajoulesPerSquareFoot.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WATTS_PER_SQUARE_METER_DEGREE_KELVIN_NAME -->
<field name="WATTS_PER_SQUARE_METER_DEGREE_KELVIN_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for wattsPerSquareMeterDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_FEET_PER_SECOND_NAME -->
<field name="CUBIC_FEET_PER_SECOND_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for cubicFeetPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PERCENT_OBSCURATION_PER_FOOT_NAME -->
<field name="PERCENT_OBSCURATION_PER_FOOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for percentObscurationPerFoot.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PERCENT_OBSCURATION_PER_METER_NAME -->
<field name="PERCENT_OBSCURATION_PER_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for percentObscurationPerMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIOHMS_NAME -->
<field name="MILLIOHMS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for milliohms.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAWATT_HOURS_NAME -->
<field name="MEGAWATT_HOURS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for megawattHours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILO_BTUS_NAME -->
<field name="KILO_BTUS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kiloBtus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGA_BTUS_NAME -->
<field name="MEGA_BTUS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for megaBtus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOJOULES_PER_KILOGRAM_DRY_AIR_NAME -->
<field name="KILOJOULES_PER_KILOGRAM_DRY_AIR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilojoulesPerKilogramDryAir.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAJOULES_PER_KILOGRAM_DRY_AIR_NAME -->
<field name="MEGAJOULES_PER_KILOGRAM_DRY_AIR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for megajoulesPerKilogramDryAir.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOJOULES_PER_DEGREE_KELVIN_NAME -->
<field name="KILOJOULES_PER_DEGREE_KELVIN_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilojoulesPerDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAJOULES_PER_DEGREE_KELVIN_NAME -->
<field name="MEGAJOULES_PER_DEGREE_KELVIN_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for megajoulesPerDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.NEWTON_NAME -->
<field name="NEWTON_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for newton.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_PER_SECOND_NAME -->
<field name="GRAMS_PER_SECOND_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for gramsPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_PER_MINUTE_NAME -->
<field name="GRAMS_PER_MINUTE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for gramsPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.TONS_PER_HOUR_NAME -->
<field name="TONS_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for tonsPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILO_BTUS_PER_HOUR_NAME -->
<field name="KILO_BTUS_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kiloBtusPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.HUNDREDTHS_SECONDS_NAME -->
<field name="HUNDREDTHS_SECONDS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for hundredthsSeconds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLISECONDS_NAME -->
<field name="MILLISECONDS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for milliseconds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.NEWTON_METERS_NAME -->
<field name="NEWTON_METERS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for newtonMeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIMETERS_PER_SECOND_NAME -->
<field name="MILLIMETERS_PER_SECOND_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for millimetersPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIMETERS_PER_MINUTE_NAME -->
<field name="MILLIMETERS_PER_MINUTE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for millimetersPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.METERS_PER_MINUTE_NAME -->
<field name="METERS_PER_MINUTE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for metersPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.METERS_PER_HOUR_NAME -->
<field name="METERS_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for metersPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_METERS_PER_MINUTE_NAME -->
<field name="CUBIC_METERS_PER_MINUTE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for cubicMetersPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.METERS_PER_SECOND_PER_SECOND_NAME -->
<field name="METERS_PER_SECOND_PER_SECOND_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for metersPerSecondPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.AMPERES_PER_METER_NAME -->
<field name="AMPERES_PER_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for amperesPerMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.AMPERES_PER_SQUARE_METER_NAME -->
<field name="AMPERES_PER_SQUARE_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for amperesPerSquareMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.AMPERE_SQUARE_METERS_NAME -->
<field name="AMPERE_SQUARE_METERS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for ampereSquareMeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.FARADS_NAME -->
<field name="FARADS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for farads.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.HENRYS_NAME -->
<field name="HENRYS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for henrys.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.OHM_METERS_NAME -->
<field name="OHM_METERS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for ohmMeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.SIEMENS_NAME -->
<field name="SIEMENS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for siemens.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.SIEMENS_PER_METER_NAME -->
<field name="SIEMENS_PER_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for siemensPerMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.TESLAS_NAME -->
<field name="TESLAS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for teslas.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.VOLTS_PER_DEGREE_KELVIN_NAME -->
<field name="VOLTS_PER_DEGREE_KELVIN_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for voltsPerDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.VOLTS_PER_METER_NAME -->
<field name="VOLTS_PER_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for voltsPerMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WEBERS_NAME -->
<field name="WEBERS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for webers.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CANDELAS_NAME -->
<field name="CANDELAS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for candelas.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CANDELAS_PER_SQUARE_METER_NAME -->
<field name="CANDELAS_PER_SQUARE_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for candelasPerSquareMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_KELVIN_PER_HOUR_NAME -->
<field name="DEGREES_KELVIN_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for degreesKelvinPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEGREES_KELVIN_PER_MINUTE_NAME -->
<field name="DEGREES_KELVIN_PER_MINUTE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for degreesKelvinPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.JOULE_SECONDS_NAME -->
<field name="JOULE_SECONDS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for jouleSeconds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.RADIANS_PER_SECOND_NAME -->
<field name="RADIANS_PER_SECOND_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for radiansPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.SQUARE_METERS_PER_NEWTON_NAME -->
<field name="SQUARE_METERS_PER_NEWTON_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for squareMetersPerNewton.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOGRAMS_PER_CUBIC_METER_NAME -->
<field name="KILOGRAMS_PER_CUBIC_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilogramsPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.NEWTON_SECONDS_NAME -->
<field name="NEWTON_SECONDS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for newtonSeconds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.NEWTONS_PER_METER_NAME -->
<field name="NEWTONS_PER_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for newtonsPerMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WATTS_PER_METER_PER_DEGREE_KELVIN_NAME -->
<field name="WATTS_PER_METER_PER_DEGREE_KELVIN_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for wattsPerMeterPerDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MICROSIEMENS_NAME -->
<field name="MICROSIEMENS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for microSiemens.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_FEET_PER_HOUR_NAME -->
<field name="CUBIC_FEET_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for cubicFeetPerHour,.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.US_GALLONS_PER_HOUR_NAME -->
<field name="US_GALLONS_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for usGallonsPerHour,.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOMETERS_NAME -->
<field name="KILOMETERS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilometers.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MICROMETERS_NAME -->
<field name="MICROMETERS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for micrometers.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_NAME -->
<field name="GRAMS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for grams.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIGRAMS_NAME -->
<field name="MILLIGRAMS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for milligrams.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLILITERS_NAME -->
<field name="MILLILITERS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for microSiemens.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLILITERS_PER_SECOND_NAME -->
<field name="MILLILITERS_PER_SECOND_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for millilitersPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DECIBELS_NAME -->
<field name="DECIBELS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for decibels.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DECIBELS_MILLIVOLT_NAME -->
<field name="DECIBELS_MILLIVOLT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for decibelsMillivolt.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DECIBELS_VOLT_NAME -->
<field name="DECIBELS_VOLT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for decibelsVolt.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLISIEMENS_NAME -->
<field name="MILLISIEMENS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for millisiemens.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WATT_HOUR_REACTIVE_NAME -->
<field name="WATT_HOUR_REACTIVE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for wattHourReactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOWATT_HOURS_REACTIVE_NAME -->
<field name="KILOWATT_HOURS_REACTIVE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilowattHoursReactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAWATT_HOURS_REACTIVE_NAME -->
<field name="MEGAWATT_HOURS_REACTIVE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for megawattHoursReactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLILITERS_OF_WATER_NAME -->
<field name="MILLILITERS_OF_WATER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for millilitersOfWater.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PER_MILE_NAME -->
<field name="PER_MILE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for perMile.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_PER_GRAM_NAME -->
<field name="GRAMS_PER_GRAM_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for gramsPerGram.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOGRAMS_PER_KILOGRAM_NAME -->
<field name="KILOGRAMS_PER_KILOGRAM_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilogramsPerKilogram.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_PER_KILOGRAM_NAME -->
<field name="GRAMS_PER_KILOGRAM_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for gramsPerKilogram.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIGRAMS_PER_GRAM_NAME -->
<field name="MILLIGRAMS_PER_GRAM_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for milligramsPerGram.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIGRAMS_PER_KILOGRAM_NAME -->
<field name="MILLIGRAMS_PER_KILOGRAM_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for milligramsPerKilogram.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_PER_MILLILITER_NAME -->
<field name="GRAMS_PER_MILLILITER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for gramsPerMilliliter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_PER_LITER_NAME -->
<field name="GRAMS_PER_LITER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for gramsPerLiter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIGRAMS_PER_LITER_NAME -->
<field name="MILLIGRAMS_PER_LITER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for milligramsPerLiter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MICROGRAMS_PER_LITER_NAME -->
<field name="MICROGRAMS_PER_LITER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for microgramsPerLiter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_PER_CUBIC_METER_NAME -->
<field name="GRAMS_PER_CUBIC_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for gramsPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIGRAMS_PER_CUBIC_METER_NAME -->
<field name="MILLIGRAMS_PER_CUBIC_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for gramsPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MICROGRAMS_PER_CUBIC_METER_NAME -->
<field name="MICROGRAMS_PER_CUBIC_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for gramsPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.NANOGRAMS_PER_CUBIC_METER_NAME -->
<field name="NANOGRAMS_PER_CUBIC_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for nanogramsPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_PER_CUBIC_CENTIMETER_NAME -->
<field name="GRAMS_PER_CUBIC_CENTIMETER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for gramsPerCubicCentimeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.BECQUERELS_NAME -->
<field name="BECQUERELS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for becquerels.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOBECQUERELS_NAME -->
<field name="KILOBECQUERELS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilobecquerels.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGABECQUERELS_NAME -->
<field name="MEGABECQUERELS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for megabecquerels.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAY_NAME -->
<field name="GRAY_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for gray.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIGRAY_NAME -->
<field name="MILLIGRAY_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for milligray.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MICROGRAY_NAME -->
<field name="MICROGRAY_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for microgray.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.SIEVERTS_NAME -->
<field name="SIEVERTS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for sieverts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLISIEVERTS_NAME -->
<field name="MILLISIEVERTS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for millisieverts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MICROSIEVERTS_NAME -->
<field name="MICROSIEVERTS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for microsieverts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MICROSIEVERTS_PER_HOUR_NAME -->
<field name="MICROSIEVERTS_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for microsievertsPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DECIBELS_A_NAME -->
<field name="DECIBELS_A_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for decibelsA.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.NEPHELOMETRIC_TURBIDITY_UNIT_NAME -->
<field name="NEPHELOMETRIC_TURBIDITY_UNIT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for nephelometricTurbidityUnit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PH_NAME -->
<field name="PH_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for ph.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.GRAMS_PER_SQUARE_METER_NAME -->
<field name="GRAMS_PER_SQUARE_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for gramsPerSquareMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MINUTES_PER_DEGREE_KELVIN_NAME -->
<field name="MINUTES_PER_DEGREE_KELVIN_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for minutesPerDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.OHM_METER_SQUARED_PER_METER_NAME -->
<field name="OHM_METER_SQUARED_PER_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for ohm meter squared per meter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.AMPERE_SECONDS_NAME -->
<field name="AMPERE_SECONDS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for ampere seconds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.VOLT_AMPERE_HOUR_NAME -->
<field name="VOLT_AMPERE_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for volt ampere hour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOVOLT_AMPERE_HOUR_NAME -->
<field name="KILOVOLT_AMPERE_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilovolt ampere hour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAVOLT_AMPERE_HOUR_NAME -->
<field name="MEGAVOLT_AMPERE_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for megavolt ampere hour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.VOLT_AMPERE_REACTIVE_HOUR_NAME -->
<field name="VOLT_AMPERE_REACTIVE_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for volt ampere reactive hour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.KILOVOLT_AMPERE_REACTIVE_HOUR_NAME -->
<field name="KILOVOLT_AMPERE_REACTIVE_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for kilovolt ampere reactive hour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MEGAVOLT_AMPERE_REACTIVE_HOUR_NAME -->
<field name="MEGAVOLT_AMPERE_REACTIVE_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for megavolt ampere reactive hour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.VOLT_SQUARED_HOURS_NAME -->
<field name="VOLT_SQUARED_HOURS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for volt squared hours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.AMPERE_SQUARED_HOURS_NAME -->
<field name="AMPERE_SQUARED_HOURS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for ampere squared hours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.JOULES_PER_HOUR_NAME -->
<field name="JOULES_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for joules per hour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_FEET_PER_DAY_NAME -->
<field name="CUBIC_FEET_PER_DAY_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for cubic feet per day.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.CUBIC_METERS_PER_DAY_NAME -->
<field name="CUBIC_METERS_PER_DAY_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for cubic meters per day.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.WATT_HOUR_PER_CUBIC_METER_NAME -->
<field name="WATT_HOUR_PER_CUBIC_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for watt hour per cubic meter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.JOULES_PER_CUBIC_METER_NAME -->
<field name="JOULES_PER_CUBIC_METER_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for joules per cubic meter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MOLES_PERCENT_NAME -->
<field name="MOLES_PERCENT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for moles percent.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.PASCAL_SECOND_NAME -->
<field name="PASCAL_SECOND_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for pascal Second.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLION_STANDARD_CUBIC_FEET_PER_MINUTE_NAME -->
<field name="MILLION_STANDARD_CUBIC_FEET_PER_MINUTE_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for million standard cubic feet per minute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.STANDARD_CUBIC_FEET_PER_DAY_NAME -->
<field name="STANDARD_CUBIC_FEET_PER_DAY_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for standard cubic feet per day.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLION_STANDARD_CUBIC_FEET_PER_DAY_NAME -->
<field name="MILLION_STANDARD_CUBIC_FEET_PER_DAY_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for million standard cubic feet per day.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.THOUSAND_CUBIC_FEET_PER_DAY_NAME -->
<field name="THOUSAND_CUBIC_FEET_PER_DAY_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for thousand cubic feet per day.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.THOUSAND_STANDARD_CUBIC_FEET_PER_DAY_NAME -->
<field name="THOUSAND_STANDARD_CUBIC_FEET_PER_DAY_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for thousand standard cubic feet per day.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.POUNDS_MASS_PER_DAY_NAME -->
<field name="POUNDS_MASS_PER_DAY_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for pounds mass per day.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIREMS_NAME -->
<field name="MILLIREMS_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for millirems.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MILLIREMS_PER_HOUR_NAME -->
<field name="MILLIREMS_PER_HOUR_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Niagara unit name for millirems per hour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.squareMeters -->
<field name="squareMeters"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for squareMeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.squareFeet -->
<field name="squareFeet"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for squareFeet.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.milliamperes -->
<field name="milliamperes"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for milliamperes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.amperes -->
<field name="amperes"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for amperes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.ohms -->
<field name="ohms"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for ohms.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.volts -->
<field name="volts"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for volts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilovolts -->
<field name="kilovolts"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilovolts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.megavolts -->
<field name="megavolts"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for megavolts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.voltAmperes -->
<field name="voltAmperes"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for voltAmperes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilovoltAmperes -->
<field name="kilovoltAmperes"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilovoltAmperes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.megavoltAmperes -->
<field name="megavoltAmperes"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for megavoltAmperes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.voltAmperesReactive -->
<field name="voltAmperesReactive"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for voltAmperesReactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilovoltAmperesReactive -->
<field name="kilovoltAmperesReactive"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilovoltAmperesReactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.megavoltAmperesReactive -->
<field name="megavoltAmperesReactive"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for megavoltAmperesReactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.degreesPhase -->
<field name="degreesPhase"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for degreesPhase.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.powerFactor -->
<field name="powerFactor"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for powerFactor.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.joules -->
<field name="joules"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for joules.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilojoules -->
<field name="kilojoules"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilojoules.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.wattHours -->
<field name="wattHours"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for wattHours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilowattHours -->
<field name="kilowattHours"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilowattHours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.btus -->
<field name="btus"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for btus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.therms -->
<field name="therms"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for therms.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.tonHours -->
<field name="tonHours"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for tonHours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.joulesPerKilogramDryAir -->
<field name="joulesPerKilogramDryAir"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for joulesPerKilogramDryAir.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.btusPerPoundDryAir -->
<field name="btusPerPoundDryAir"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for btusPerPoundDryAir.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.cyclesPerHour -->
<field name="cyclesPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for cyclesPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.cyclesPerMinute -->
<field name="cyclesPerMinute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for cyclesPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.hertz -->
<field name="hertz"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for hertz.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.gramsOfWaterPerKilogramDryAir -->
<field name="gramsOfWaterPerKilogramDryAir"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for gramsOfWaterPerKilogramDryAir.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.percentRelativeHumidity -->
<field name="percentRelativeHumidity"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for percentRelativeHumidity.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.millimeters -->
<field name="millimeters"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for millimeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.meters -->
<field name="meters"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for meters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.inches -->
<field name="inches"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for inches.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.feet -->
<field name="feet"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for feet.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.wattsPerSquareFoot -->
<field name="wattsPerSquareFoot"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for wattsPerSquareFoot.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.wattsPerSquareMeter -->
<field name="wattsPerSquareMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for wattsPerSquareMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.lumens -->
<field name="lumens"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for lumens.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.luxes -->
<field name="luxes"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for luxes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.footCandles -->
<field name="footCandles"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for footCandles.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilograms -->
<field name="kilograms"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilograms.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.poundsMass -->
<field name="poundsMass"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for poundsMass.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.tons -->
<field name="tons"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for tons.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilogramsPerSecond -->
<field name="kilogramsPerSecond"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilogramsPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilogramsPerMinute -->
<field name="kilogramsPerMinute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilogramsPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilogramsPerHour -->
<field name="kilogramsPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilogramsPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.poundsMassPerMinute -->
<field name="poundsMassPerMinute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for poundsMassPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.poundsMassPerHour -->
<field name="poundsMassPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for poundsMassPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.watts -->
<field name="watts"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for watts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilowatts -->
<field name="kilowatts"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilowatts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.megawatts -->
<field name="megawatts"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for megawatts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.btusPerHour -->
<field name="btusPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for btusPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.horsepower -->
<field name="horsepower"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for horsepower.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.tonsRefrigeration -->
<field name="tonsRefrigeration"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for tonsRefrigeration.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.pascals -->
<field name="pascals"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for pascals.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilopascals -->
<field name="kilopascals"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilopascals.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.bars -->
<field name="bars"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for bars.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.poundsForcePerSquareInch -->
<field name="poundsForcePerSquareInch"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for poundsForcePerSquareInch.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.centimetersOfWater -->
<field name="centimetersOfWater"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for centimetersOfWater.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.inchesOfWater -->
<field name="inchesOfWater"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for inchesOfWater.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.millimetersOfMercury -->
<field name="millimetersOfMercury"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for millimetersOfMercury.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.centimetersOfMercury -->
<field name="centimetersOfMercury"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for centimetersOfMercury.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.inchesOfMercury -->
<field name="inchesOfMercury"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for inchesOfMercury.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.degreesCelsius -->
<field name="degreesCelsius"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for degreesCelsius.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.degreesKelvin -->
<field name="degreesKelvin"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for degreesKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.degreesFahrenheit -->
<field name="degreesFahrenheit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for degreesFahrenheit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.degreeDaysCelsius -->
<field name="degreeDaysCelsius"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for degreeDaysCelsius.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.degreeDaysFahrenheit -->
<field name="degreeDaysFahrenheit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for degreeDaysFahrenheit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.years -->
<field name="years"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for years.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.months -->
<field name="months"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for months.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.weeks -->
<field name="weeks"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for weeks.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.days -->
<field name="days"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for days.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.hours -->
<field name="hours"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for hours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.minutes -->
<field name="minutes"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for minutes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.seconds -->
<field name="seconds"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for seconds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.metersPerSecond -->
<field name="metersPerSecond"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for metersPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilometersPerHour -->
<field name="kilometersPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilometersPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.feetPerSecond -->
<field name="feetPerSecond"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for feetPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.feetPerMinute -->
<field name="feetPerMinute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for feetPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.milesPerHour -->
<field name="milesPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for milesPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.cubicFeet -->
<field name="cubicFeet"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for cubicFeet.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.cubicMeters -->
<field name="cubicMeters"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for cubicMeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.imperialGallons -->
<field name="imperialGallons"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for imperialGallons.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.liters -->
<field name="liters"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for liters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.usGallons -->
<field name="usGallons"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for usGallons.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.cubicFeetPerMinute -->
<field name="cubicFeetPerMinute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for cubicFeetPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.cubicMetersPerSecond -->
<field name="cubicMetersPerSecond"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for cubicMetersPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.imperialGallonsPerMinute -->
<field name="imperialGallonsPerMinute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for imperialGallonsPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.litersPerSecond -->
<field name="litersPerSecond"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for litersPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.litersPerMinute -->
<field name="litersPerMinute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for litersPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.usGallonsPerMinute -->
<field name="usGallonsPerMinute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for usGallonsPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.degreesAngular -->
<field name="degreesAngular"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for degreesAngular.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.degreesCelsiusPerHour -->
<field name="degreesCelsiusPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for degreesCelsiusPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.degreesCelsiusPerMinute -->
<field name="degreesCelsiusPerMinute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for degreesCelsiusPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.degreesFahrenheitPerHour -->
<field name="degreesFahrenheitPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for degreesFahrenheitPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.degreesFahrenheitPerMinute -->
<field name="degreesFahrenheitPerMinute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for degreesFahrenheitPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.noUnits -->
<field name="noUnits"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for noUnits.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.partsPerMillion -->
<field name="partsPerMillion"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for partsPerMillion.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.partsPerBillion -->
<field name="partsPerBillion"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for partsPerBillion.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.percent -->
<field name="percent"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for percent.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.percentPerSecond -->
<field name="percentPerSecond"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for percentPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.perMinute -->
<field name="perMinute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for perMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.perSecond -->
<field name="perSecond"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for perSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.psiPerDegreeFahrenheit -->
<field name="psiPerDegreeFahrenheit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for psiPerDegreeFahrenheit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.radians -->
<field name="radians"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for radians.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.revolutionsPerMinute -->
<field name="revolutionsPerMinute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for revolutionsPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.currency1 -->
<field name="currency1"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for currency1.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.currency2 -->
<field name="currency2"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for currency2.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.currency3 -->
<field name="currency3"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for currency3.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.currency4 -->
<field name="currency4"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for currency4.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.currency5 -->
<field name="currency5"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for currency5.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.currency6 -->
<field name="currency6"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for currency6.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.currency7 -->
<field name="currency7"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for currency7.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.currency8 -->
<field name="currency8"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for currency8.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.currency9 -->
<field name="currency9"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for currency9.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.currency10 -->
<field name="currency10"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for currency10.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.squareInches -->
<field name="squareInches"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for squareInches.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.squareCentimeters -->
<field name="squareCentimeters"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for squareCentimeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.btusPerPound -->
<field name="btusPerPound"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for btusPerPound.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.centimeters -->
<field name="centimeters"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for centimeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.poundsMassPerSecond -->
<field name="poundsMassPerSecond"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for poundsMassPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.deltaDegreesFahrenheit -->
<field name="deltaDegreesFahrenheit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for deltaDegreesFahrenheit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.deltaDegreesKelvin -->
<field name="deltaDegreesKelvin"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for deltaDegreesKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilohms -->
<field name="kilohms"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilohms.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.megohms -->
<field name="megohms"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for megohms.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.millivolts -->
<field name="millivolts"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for millivolts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilojoulesPerKilogram -->
<field name="kilojoulesPerKilogram"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilojoulesPerKilogram.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.megajoules -->
<field name="megajoules"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for megajoules.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.joulesPerDegreeKelvin -->
<field name="joulesPerDegreeKelvin"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for joulesPerDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.joulesPerKilogramDegreeKelvin -->
<field name="joulesPerKilogramDegreeKelvin"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for joulesPerKilogramDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilohertz -->
<field name="kilohertz"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilohertz.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.megahertz -->
<field name="megahertz"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for megahertz.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.perHour -->
<field name="perHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for perHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.milliwatts -->
<field name="milliwatts"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for milliwatts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.hectopascals -->
<field name="hectopascals"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for hectopascals.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.millibars -->
<field name="millibars"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for millibars.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.cubicMetersPerHour -->
<field name="cubicMetersPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for cubicMetersPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.litersPerHour -->
<field name="litersPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for litersPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilowattHoursPerSquareMeter -->
<field name="kilowattHoursPerSquareMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilowattHoursPerSquareMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilowattHoursPerSquareFoot -->
<field name="kilowattHoursPerSquareFoot"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilowattHoursPerSquareFoot.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.megajoulesPerSquareMeter -->
<field name="megajoulesPerSquareMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for megajoulesPerSquareMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.megajoulesPerSquareFoot -->
<field name="megajoulesPerSquareFoot"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for megajoulesPerSquareFoot.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.wattsPerSquareMeterDegreeKelvin -->
<field name="wattsPerSquareMeterDegreeKelvin"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for wattsPerSquareMeterDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.cubicFeetPerSecond -->
<field name="cubicFeetPerSecond"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for cubicFeetPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.percentObscurationPerFoot -->
<field name="percentObscurationPerFoot"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for percentObscurationPerFoot.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.percentObscurationPerMeter -->
<field name="percentObscurationPerMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for percentObscurationPerMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.milliohms -->
<field name="milliohms"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for milliohms.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.megawattHours -->
<field name="megawattHours"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for megawattHours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kiloBtus -->
<field name="kiloBtus"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kiloBtus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.megaBtus -->
<field name="megaBtus"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for megaBtus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilojoulesPerKilogramDryAir -->
<field name="kilojoulesPerKilogramDryAir"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilojoulesPerKilogramDryAir.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.megajoulesPerKilogramDryAir -->
<field name="megajoulesPerKilogramDryAir"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for megajoulesPerKilogramDryAir.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilojoulesPerDegreeKelvin -->
<field name="kilojoulesPerDegreeKelvin"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilojoulesPerDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.megajoulesPerDegreeKelvin -->
<field name="megajoulesPerDegreeKelvin"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for megajoulesPerDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.newton -->
<field name="newton"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for newton.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.gramsPerSecond -->
<field name="gramsPerSecond"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for gramsPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.gramsPerMinute -->
<field name="gramsPerMinute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for gramsPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.tonsPerHour -->
<field name="tonsPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for tonsPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kiloBtusPerHour -->
<field name="kiloBtusPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kiloBtusPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.hundredthsSeconds -->
<field name="hundredthsSeconds"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for hundredthsSeconds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.milliseconds -->
<field name="milliseconds"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for milliseconds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.newtonMeters -->
<field name="newtonMeters"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for newtonMeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.millimetersPerSecond -->
<field name="millimetersPerSecond"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for millimetersPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.millimetersPerMinute -->
<field name="millimetersPerMinute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for millimetersPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.metersPerMinute -->
<field name="metersPerMinute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for metersPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.metersPerHour -->
<field name="metersPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for metersPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.cubicMetersPerMinute -->
<field name="cubicMetersPerMinute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for cubicMetersPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.metersPerSecondPerSecond -->
<field name="metersPerSecondPerSecond"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for metersPerSecondPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.amperesPerMeter -->
<field name="amperesPerMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for amperesPerMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.amperesPerSquareMeter -->
<field name="amperesPerSquareMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for amperesPerSquareMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.ampereSquareMeters -->
<field name="ampereSquareMeters"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for ampereSquareMeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.farads -->
<field name="farads"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for farads.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.henrys -->
<field name="henrys"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for henrys.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.ohmMeters -->
<field name="ohmMeters"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for ohmMeters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.siemens -->
<field name="siemens"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for siemens.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.siemensPerMeter -->
<field name="siemensPerMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for siemensPerMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.teslas -->
<field name="teslas"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for teslas.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.voltsPerDegreeKelvin -->
<field name="voltsPerDegreeKelvin"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for voltsPerDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.voltsPerMeter -->
<field name="voltsPerMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for voltsPerMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.webers -->
<field name="webers"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for webers.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.candelas -->
<field name="candelas"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for candelas.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.candelasPerSquareMeter -->
<field name="candelasPerSquareMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for candelasPerSquareMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.degreesKelvinPerHour -->
<field name="degreesKelvinPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for degreesKelvinPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.degreesKelvinPerMinute -->
<field name="degreesKelvinPerMinute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for degreesKelvinPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.jouleSeconds -->
<field name="jouleSeconds"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for jouleSeconds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.radiansPerSecond -->
<field name="radiansPerSecond"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for radiansPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.squareMetersPerNewton -->
<field name="squareMetersPerNewton"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for squareMetersPerNewton.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilogramsPerCubicMeter -->
<field name="kilogramsPerCubicMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilogramsPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.newtonSeconds -->
<field name="newtonSeconds"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for newtonSeconds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.newtonsPerMeter -->
<field name="newtonsPerMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for newtonsPerMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.wattsPerMeterPerDegreeKelvin -->
<field name="wattsPerMeterPerDegreeKelvin"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for wattsPerMeterPerDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.microSiemens -->
<field name="microSiemens"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for microSiemens.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.cubicFeetPerHour -->
<field name="cubicFeetPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for cubicFeetPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.usGallonsPerHour -->
<field name="usGallonsPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for usGallonsPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilometers -->
<field name="kilometers"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilometers.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.micrometers -->
<field name="micrometers"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for micrometers.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.grams -->
<field name="grams"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for grams.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.milligrams -->
<field name="milligrams"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for milligrams.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.milliliters -->
<field name="milliliters"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for milliliters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.millilitersPerSecond -->
<field name="millilitersPerSecond"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for millilitersPerSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.decibels -->
<field name="decibels"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for decibels.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.decibelsMillivolt -->
<field name="decibelsMillivolt"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for decibelsMillivolt.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.decibelsVolt -->
<field name="decibelsVolt"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for decibelsVolt.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.millisiemens -->
<field name="millisiemens"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for millisiemens.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.wattHourReactive -->
<field name="wattHourReactive"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for wattHourReactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilowattHoursReactive -->
<field name="kilowattHoursReactive"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilowattHoursReactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.megawattHoursReactive -->
<field name="megawattHoursReactive"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for megawattHoursReactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.millilitersOfWater -->
<field name="millilitersOfWater"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for millilitersOfWater.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.perMile -->
<field name="perMile"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for perMile.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.gramsPerGram -->
<field name="gramsPerGram"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for gramsPerGram.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilogramsPerKilogram -->
<field name="kilogramsPerKilogram"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilogramsPerKilogram.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.gramsPerKilogram -->
<field name="gramsPerKilogram"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for gramsPerKilogram.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.milligramsPerGram -->
<field name="milligramsPerGram"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for milligramsPerGram.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.milligramsPerKilogram -->
<field name="milligramsPerKilogram"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for milligramsPerKilogram.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.gramsPerMilliliter -->
<field name="gramsPerMilliliter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for gramsPerMilliliter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.gramsPerLiter -->
<field name="gramsPerLiter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for gramsPerLiter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.milligramsPerLiter -->
<field name="milligramsPerLiter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for milligramsPerLiter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.microgramsPerLiter -->
<field name="microgramsPerLiter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for microgramsPerLiter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.gramsPerCubicMeter -->
<field name="gramsPerCubicMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for gramsPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.milligramsPerCubicMeter -->
<field name="milligramsPerCubicMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for milligramsPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.microgramsPerCubicMeter -->
<field name="microgramsPerCubicMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for microgramsPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.nanogramsPerCubicMeter -->
<field name="nanogramsPerCubicMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for nanogramsPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.gramsPerCubicCentimeter -->
<field name="gramsPerCubicCentimeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for gramsPerCubicCentimeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.becquerels -->
<field name="becquerels"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for becquerels.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilobecquerels -->
<field name="kilobecquerels"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilobecquerels.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.megabecquerels -->
<field name="megabecquerels"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for megabecquerels.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.gray -->
<field name="gray"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for gray.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.milligray -->
<field name="milligray"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for milligray.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.microgray -->
<field name="microgray"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for microgray.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.sieverts -->
<field name="sieverts"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for sieverts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.millisieverts -->
<field name="millisieverts"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for millisieverts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.microsieverts -->
<field name="microsieverts"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for microsieverts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.microsievertsPerHour -->
<field name="microsievertsPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for microsievertsPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.decibelsA -->
<field name="decibelsA"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for decibelsA.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.nephelometricTurbidityUnit -->
<field name="nephelometricTurbidityUnit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for nephelometricTurbidityUnit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.ph -->
<field name="ph"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for ph.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.gramsPerSquareMeter -->
<field name="gramsPerSquareMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for gramsPerSquareMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.minutesPerDegreeKelvin -->
<field name="minutesPerDegreeKelvin"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for minutesPerDegreeKelvin.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.ohmMeterSquaredPerMeter -->
<field name="ohmMeterSquaredPerMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for ohmMeterSquaredPerMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.ampereSeconds -->
<field name="ampereSeconds"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for ampereSeconds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.voltAmpereHour -->
<field name="voltAmpereHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for voltAmpereHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilovoltAmpereHour -->
<field name="kilovoltAmpereHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilovoltAmpereHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.megavoltAmpereHour -->
<field name="megavoltAmpereHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for megavoltAmpereHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.voltAmpereReactiveHour -->
<field name="voltAmpereReactiveHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for voltAmpereReactiveHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.kilovoltAmpereReactiveHour -->
<field name="kilovoltAmpereReactiveHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for kilovoltAmpereReactiveHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.megavoltAmpereReactiveHour -->
<field name="megavoltAmpereReactiveHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for megavoltAmpereReactiveHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.voltSquaredHours -->
<field name="voltSquaredHours"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for voltSquaredHours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.ampereSquaredHours -->
<field name="ampereSquaredHours"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for ampereSquaredHours.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.joulesPerHour -->
<field name="joulesPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for joulesPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.cubicFeetPerDay -->
<field name="cubicFeetPerDay"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for cubicFeetPerDay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.cubicMetersPerDay -->
<field name="cubicMetersPerDay"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for cubicMetersPerDay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.wattHourPerCubicMeter -->
<field name="wattHourPerCubicMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for wattHourPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.joulesPerCubicMeter -->
<field name="joulesPerCubicMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for joulesPerCubicMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.molesPercent -->
<field name="molesPercent"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for molesPercent.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.pascalSecond -->
<field name="pascalSecond"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for pascalSecond.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.millionStandardCubicFeetPerMinute -->
<field name="millionStandardCubicFeetPerMinute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for millionStandardCubicFeetPerMinute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.standardCubicFeetPerDay -->
<field name="standardCubicFeetPerDay"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for standardCubicFeetPerDay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.millionStandardCubicFeetPerDay -->
<field name="millionStandardCubicFeetPerDay"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for millionStandardCubicFeetPerDay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.thousandCubicFeetPerDay -->
<field name="thousandCubicFeetPerDay"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for thousandCubicFeetPerDay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.thousandStandardCubicFeetPerDay -->
<field name="thousandStandardCubicFeetPerDay"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for thousandStandardCubicFeetPerDay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.poundsMassPerDay -->
<field name="poundsMassPerDay"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for poundsMassPerDay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.millirems -->
<field name="millirems"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for millirems.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.milliremsPerHour -->
<field name="milliremsPerHour"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
BBacnetEngineeringUnits constant for milliremsPerHour.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEngineeringUnits"/>
<description>
Private constructor.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.ASHRAE_RESERVED_RANGE_MIN -->
<field name="ASHRAE_RESERVED_RANGE_MIN"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEngineeringUnits.ASHRAE_RESERVED_RANGE_MAX -->
<field name="ASHRAE_RESERVED_RANGE_MAX"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
