<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource" name="BJsonSchemaNameSource" packageName="com.tridiumx.jsonToolkit.outbound.schema.config" public="true" final="true">
<description>
Different available options for what name to use for json keys when bound to a station component.&#xa;&#xa; displayName - uses the displayName of the bound member itself&#xa; targetName - the name of the slot which is the ord target&#xa; targetDisplayName - the display name of the slot which is the ord target
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;displayName&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;targetName&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;targetDisplayName&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;targetParentName&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;targetPath&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource.DISPLAY_NAME -->
<field name="DISPLAY_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for displayName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource.TARGET_NAME -->
<field name="TARGET_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for targetName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource.TARGET_DISPLAY_NAME -->
<field name="TARGET_DISPLAY_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for targetDisplayName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource.TARGET_PARENT_NAME -->
<field name="TARGET_PARENT_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for targetParentName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource.TARGET_PATH -->
<field name="TARGET_PATH"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for targetPath.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource.displayName -->
<field name="displayName"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource"/>
<description>
BJsonSchemaNameSource constant for displayName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource.targetName -->
<field name="targetName"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource"/>
<description>
BJsonSchemaNameSource constant for targetName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource.targetDisplayName -->
<field name="targetDisplayName"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource"/>
<description>
BJsonSchemaNameSource constant for targetDisplayName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource.targetParentName -->
<field name="targetParentName"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource"/>
<description>
BJsonSchemaNameSource constant for targetParentName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource.targetPath -->
<field name="targetPath"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource"/>
<description>
BJsonSchemaNameSource constant for targetPath.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSource.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
