<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="baja" runtimeProfile="rt" name="javax.baja.license">
<description>
This package contains classes used to access the licensing framework.
</description>
<class packageName="javax.baja.license" name="BILicensed" category="interface"><description>BILicensed is the interface used to represent object&#xa; that are licensed using the standard licensing&#xa; mechanism.</description></class>
<class packageName="javax.baja.license" name="Feature" category="interface"><description>Feature encapsulates a feature specification for the &#xa; licensing framework.</description></class>
<class packageName="javax.baja.license" name="LicenseManager" category="interface"><description>LicenseManager is used to access the Baja licensing framework.</description></class>
<class packageName="javax.baja.license" name="FeatureLicenseExpiredException" category="exception"><description>FeatureLicenseExpiredException indicates an attempt to use&#xa; a feature which has expired.</description></class>
<class packageName="javax.baja.license" name="FeatureNotLicensedException" category="exception"><description>FeatureNotLicensedException indicates an attempt to use&#xa; a feature which not currently licensed for this machine.</description></class>
<class packageName="javax.baja.license" name="LicenseDatabaseException" category="exception"><description>LicenseDatabaseException indicates that the machine&#xa; has an invalid license database.</description></class>
<class packageName="javax.baja.license" name="LicenseException" category="exception"><description>LicenseException indicates a licensing problem.</description></class>
</package>
</bajadoc>
