<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BIFileSpace" name="BIFileSpace" packageName="javax.baja.file" public="true" interface="true" abstract="true" category="interface">
<description>
File space interface.
</description>
<tag name="@author">Dan Heine</tag>
<tag name="@creation">2013-06-27</tag>
<tag name="@since">Niagara 4.0</tag>
<implements>
<type class="javax.baja.space.BISpace"/>
</implements>
<implements>
<type class="javax.baja.agent.BIAgent"/>
</implements>
<implements>
<type class="javax.baja.file.BIDirectory"/>
</implements>
<implements>
<type class="javax.baja.security.BIProtected"/>
</implements>
<!-- javax.baja.file.BIFileSpace.makeDir(javax.baja.file.FilePath) -->
<method name="makeDir"  public="true" abstract="true">
<description>
Convenience for &lt;code&gt;makeDir(path, null)&lt;/code&gt;
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.BDirectory"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BIFileSpace.makeFile(javax.baja.file.FilePath) -->
<method name="makeFile"  public="true" abstract="true">
<description>
Convenience for &lt;code&gt;makeFile(path, null)&lt;/code&gt;
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BIFileSpace.move(javax.baja.file.FilePath, javax.baja.file.FilePath) -->
<method name="move"  public="true" abstract="true">
<description>
Convenience for &lt;code&gt;move(from, to, null)&lt;/code&gt;
</description>
<parameter name="from">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="to">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BIFileSpace.delete(javax.baja.file.FilePath) -->
<method name="delete"  public="true" abstract="true">
<description>
Convenience for &lt;code&gt;delete(path, null)&lt;/code&gt;
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BIFileSpace.getAbsoluteOrd(javax.baja.file.FilePath) -->
<method name="getAbsoluteOrd"  public="true" abstract="true">
<description>
Get an absolute ord for a file path within this space.
</description>
<parameter name="filePath">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.file.BIFileSpace.getOrdInHost(javax.baja.file.FilePath) -->
<method name="getOrdInHost"  public="true" abstract="true">
<description>
Get an ord relative to the host for a file path within this space.
</description>
<parameter name="filePath">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.file.BIFileSpace.getOrdInSession(javax.baja.file.FilePath) -->
<method name="getOrdInSession"  public="true" abstract="true">
<description>
Get an ord relative to the session for a file path within this space.
</description>
<parameter name="filePath">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.file.BIFileSpace.findFile(javax.baja.file.FilePath) -->
<method name="findFile"  public="true" abstract="true">
<description>
Routes to findStore() and makeFile().
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
</method>

<!-- javax.baja.file.BIFileSpace.resolveFile(javax.baja.file.FilePath) -->
<method name="resolveFile"  public="true" abstract="true">
<description>
This method calls findFile(path).  If null is&#xa; returned then UnresolvedException() is thrown.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
</method>

<!-- javax.baja.file.BIFileSpace.makeFile(javax.baja.file.BIFileStore) -->
<method name="makeFile"  public="true" abstract="true">
<description>
Given an implementation of file store, create the proper&#xa; type of BIFile.  The standard implementation of this method&#xa; uses the registry to map the store&#x27;s file extension to&#xa; a type of BIFile.  Once the instance is created, the store&#xa; is set using BIFile.setStore().  If the store is a directory&#xa; then return an instance of BDirectory.  If the file has&#xa; no extension, then return an instance of BDataFile.
</description>
<parameter name="store">
<type class="javax.baja.file.BIFileStore"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
</method>

<!-- javax.baja.file.BIFileSpace.getPermissionsFor(javax.baja.file.FilePath, javax.baja.sys.Context) -->
<method name="getPermissionsFor"  public="true" abstract="true">
<description>
Get the permissions for a FilePath.  This method works&#xa; for both existing paths and non-existing paths making&#xa; it useful to pre-check operations which are going to create&#xa; new files.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.security.BPermissions"/>
</return>
</method>

<!-- javax.baja.file.BIFileSpace.checkReadPermission(javax.baja.file.FilePath, javax.baja.sys.Context) -->
<method name="checkReadPermission"  public="true" abstract="true">
<description>
If the result of &lt;code&gt;getPermissionsFor(path, cx)&lt;/code&gt;&#xa; doesn&#x27;t contain operator read then throw a PermissionException.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.file.BIFileSpace.checkWritePermission(javax.baja.file.FilePath, javax.baja.sys.Context) -->
<method name="checkWritePermission"  public="true" abstract="true">
<description>
If the result of &lt;code&gt;getPermissionsFor(path, cx)&lt;/code&gt;&#xa; doesn&#x27;t contain operator write then throw a PermissionException.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.file.BIFileSpace.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
