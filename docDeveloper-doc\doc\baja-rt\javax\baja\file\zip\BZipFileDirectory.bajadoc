<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.zip.BZipFileDirectory" name="BZipFileDirectory" packageName="javax.baja.file.zip" public="true">
<description>
BZipFileDirectory is an in-memory file store of a &#xa; directory within a zip file.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">24 Jan 03</tag>
<tag name="@version">$Revision: 4$ $Date: 3/28/05 9:22:57 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.file.BAbstractFileStore"/>
</extends>
<!-- javax.baja.file.zip.BZipFileDirectory(javax.baja.file.zip.BZipSpace, javax.baja.file.FilePath) -->
<constructor name="BZipFileDirectory" public="true">
<parameter name="space">
<type class="javax.baja.file.zip.BZipSpace"/>
</parameter>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<description>
Construct.
</description>
</constructor>

<!-- javax.baja.file.zip.BZipFileDirectory.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFileDirectory.add(javax.baja.file.BIFile) -->
<method name="add"  public="true">
<description/>
<parameter name="file">
<type class="javax.baja.file.BIFile"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFileDirectory.isDirectory() -->
<method name="isDirectory"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return true.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFileDirectory.isReadonly() -->
<method name="isReadonly"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return true.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFileDirectory.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
