<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat" name="BBacnetAuthenticationFactorFormat" packageName="javax.baja.bacnet.datatypes.access" public="true" final="true">
<description>
BBacnetAuthenticationFactorFormat represents the BBacnetAuthenticationFactorFormat&#xa; sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="formatType" flags="">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
Slot for the &lt;code&gt;formatType&lt;/code&gt; property.
</description>
<tag name="@see">#getFormatType</tag>
<tag name="@see">#setFormatType</tag>
</property>

<property name="vendorId" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;vendorId&lt;/code&gt; property.
</description>
<tag name="@see">#getVendorId</tag>
<tag name="@see">#setVendorId</tag>
</property>

<property name="vendorFormat" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;vendorFormat&lt;/code&gt; property.
</description>
<tag name="@see">#getVendorFormat</tag>
<tag name="@see">#setVendorFormat</tag>
</property>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat() -->
<constructor name="BBacnetAuthenticationFactorFormat" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat(javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType, int, int) -->
<constructor name="BBacnetAuthenticationFactorFormat" public="true">
<parameter name="formatType">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
</parameter>
<parameter name="vendorId">
<type class="int"/>
</parameter>
<parameter name="vendorFormat">
<type class="int"/>
</parameter>
<description>
Standard constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat.getFormatType() -->
<method name="getFormatType"  public="true">
<description>
Get the &lt;code&gt;formatType&lt;/code&gt; property.
</description>
<tag name="@see">#formatType</tag>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat.setFormatType(javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType) -->
<method name="setFormatType"  public="true">
<description>
Set the &lt;code&gt;formatType&lt;/code&gt; property.
</description>
<tag name="@see">#formatType</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat.getVendorId() -->
<method name="getVendorId"  public="true">
<description>
Get the &lt;code&gt;vendorId&lt;/code&gt; property.
</description>
<tag name="@see">#vendorId</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat.setVendorId(int) -->
<method name="setVendorId"  public="true">
<description>
Set the &lt;code&gt;vendorId&lt;/code&gt; property.
</description>
<tag name="@see">#vendorId</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat.getVendorFormat() -->
<method name="getVendorFormat"  public="true">
<description>
Get the &lt;code&gt;vendorFormat&lt;/code&gt; property.
</description>
<tag name="@see">#vendorFormat</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat.setVendorFormat(int) -->
<method name="setVendorFormat"  public="true">
<description>
Set the &lt;code&gt;vendorFormat&lt;/code&gt; property.
</description>
<tag name="@see">#vendorFormat</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat.formatType -->
<field name="formatType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;formatType&lt;/code&gt; property.
</description>
<tag name="@see">#getFormatType</tag>
<tag name="@see">#setFormatType</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat.vendorId -->
<field name="vendorId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;vendorId&lt;/code&gt; property.
</description>
<tag name="@see">#getVendorId</tag>
<tag name="@see">#setVendorId</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat.vendorFormat -->
<field name="vendorFormat"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;vendorFormat&lt;/code&gt; property.
</description>
<tag name="@see">#getVendorFormat</tag>
<tag name="@see">#setVendorFormat</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat.FORMAT_TYPE_TAG -->
<field name="FORMAT_TYPE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat.VENDOR_ID_TAG -->
<field name="VENDOR_ID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactorFormat.VENDOR_FORMAT_TAG -->
<field name="VENDOR_FORMAT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
