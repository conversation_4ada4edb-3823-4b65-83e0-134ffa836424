<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetFaultType" name="BBacnetFaultType" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetFaultType represents the BACnetFaultType&#xa; enumeration.&#xa; &lt;p&gt;&#xa; BBacnetFaultType is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0xFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Joseph Chandler</tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;none&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;faultCharacterstring&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;faultExtended&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;faultLifeSafety&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;faultState&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;faultStatusFlags&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetFaultType.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetFaultType"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetFaultType"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
String representation of this BEnum.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.NONE -->
<field name="NONE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for none.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.FAULT_CHARACTERSTRING -->
<field name="FAULT_CHARACTERSTRING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for faultCharacterstring.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.FAULT_EXTENDED -->
<field name="FAULT_EXTENDED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for faultExtended.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.FAULT_LIFE_SAFETY -->
<field name="FAULT_LIFE_SAFETY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for faultLifeSafety.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.FAULT_STATE -->
<field name="FAULT_STATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for faultState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.FAULT_STATUS_FLAGS -->
<field name="FAULT_STATUS_FLAGS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for faultStatusFlags.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.none -->
<field name="none"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetFaultType"/>
<description>
BBacnetFaultType constant for none.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.faultCharacterstring -->
<field name="faultCharacterstring"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetFaultType"/>
<description>
BBacnetFaultType constant for faultCharacterstring.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.faultExtended -->
<field name="faultExtended"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetFaultType"/>
<description>
BBacnetFaultType constant for faultExtended.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.faultLifeSafety -->
<field name="faultLifeSafety"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetFaultType"/>
<description>
BBacnetFaultType constant for faultLifeSafety.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.faultState -->
<field name="faultState"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetFaultType"/>
<description>
BBacnetFaultType constant for faultState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.faultStatusFlags -->
<field name="faultStatusFlags"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetFaultType"/>
<description>
BBacnetFaultType constant for faultStatusFlags.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetFaultType"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFaultType.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
