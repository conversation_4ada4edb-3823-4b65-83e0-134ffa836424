<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetProperty" name="BJsonSchemaFacetProperty" packageName="com.tridiumx.jsonToolkit.outbound.schema.property" public="true">
<description>
BJsonSchemaTagProperty allows a single Facet value from a bound component&#xa; to be inserted in the Schema output
</description>
<tag name="@author"><PERSON></tag>
<extends>
<parameterizedType class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaProperty">
<args>
<type class="javax.baja.sys.BObject"/>
</args>
</parameterizedType>
</extends>
<property name="ord" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;ord&lt;/code&gt; property.
</description>
<tag name="@see">#getOrd</tag>
<tag name="@see">#setOrd</tag>
</property>

<property name="facetKey" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;facetKey&lt;/code&gt; property.
</description>
<tag name="@see">#getFacetKey</tag>
<tag name="@see">#setFacetKey</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetProperty() -->
<constructor name="BJsonSchemaFacetProperty" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetProperty.getOrd() -->
<method name="getOrd"  public="true">
<description>
Get the &lt;code&gt;ord&lt;/code&gt; property.
</description>
<tag name="@see">#ord</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetProperty.setOrd(javax.baja.naming.BOrd) -->
<method name="setOrd"  public="true">
<description>
Set the &lt;code&gt;ord&lt;/code&gt; property.
</description>
<tag name="@see">#ord</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetProperty.getFacetKey() -->
<method name="getFacetKey"  public="true">
<description>
Get the &lt;code&gt;facetKey&lt;/code&gt; property.
</description>
<tag name="@see">#facetKey</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetProperty.setFacetKey(java.lang.String) -->
<method name="setFacetKey"  public="true">
<description>
Set the &lt;code&gt;facetKey&lt;/code&gt; property.
</description>
<tag name="@see">#facetKey</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetProperty.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetProperty.make(javax.baja.naming.BOrd, java.lang.String) -->
<method name="make"  public="true" static="true">
<description/>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<parameter name="facetKey">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetProperty"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetProperty.getJsonValue() -->
<method name="getJsonValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetProperty.ord -->
<field name="ord"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;ord&lt;/code&gt; property.
</description>
<tag name="@see">#getOrd</tag>
<tag name="@see">#setOrd</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetProperty.facetKey -->
<field name="facetKey"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;facetKey&lt;/code&gt; property.
</description>
<tag name="@see">#getFacetKey</tag>
<tag name="@see">#setFacetKey</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetProperty.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
