<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.BInfinetDevice" name="BInfinetDevice" packageName="com.tridium.andoverInfinity" public="true">
<description>
BInfinetDevice is the base class for Infinity devices.
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddfSerial.BDdfSerialDevice"/>
</extends>
<property name="points" flags="">
<type class="com.tridium.andoverInfinity.BInfinityPointDeviceExt"/>
<description>
Slot for the &lt;code&gt;points&lt;/code&gt; property.
</description>
<tag name="@see">#getPoints</tag>
<tag name="@see">#setPoints</tag>
</property>

<!-- com.tridium.andoverInfinity.BInfinetDevice() -->
<constructor name="BInfinetDevice" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.BInfinetDevice.getPoints() -->
<method name="getPoints"  public="true">
<description>
Get the &lt;code&gt;points&lt;/code&gt; property.
</description>
<tag name="@see">#points</tag>
<return>
<type class="com.tridium.andoverInfinity.BInfinityPointDeviceExt"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.BInfinetDevice.setPoints(com.tridium.andoverInfinity.BInfinityPointDeviceExt) -->
<method name="setPoints"  public="true">
<description>
Set the &lt;code&gt;points&lt;/code&gt; property.
</description>
<tag name="@see">#points</tag>
<parameter name="v">
<type class="com.tridium.andoverInfinity.BInfinityPointDeviceExt"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.BInfinetDevice.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.BInfinetDevice.started() -->
<method name="started"  public="true">
<description>
Override to hide the upload/download actions
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- com.tridium.andoverInfinity.BInfinetDevice.getNetworkType() -->
<method name="getNetworkType"  public="true">
<description>
BInfinityNetwork.TYPE is the network type
</description>
<tag name="@see">javax.baja.driver.BDevice#getNetworkType()</tag>
<return>
<type class="javax.baja.sys.Type"/>
<description>
&lt;code&gt;BInfinityNetwork.TYPE&lt;/code&gt;
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.BInfinetDevice.doPing() -->
<method name="doPing"  public="true">
<description>
Override doPing from BDdfDevice to short-circuit to&#xa; pingFail if the network level device is down
</description>
<tag name="@see">com.tridium.ddf.BDdfDevice#doPing()</tag>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- com.tridium.andoverInfinity.BInfinetDevice.deviceId -->
<field name="deviceId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;deviceId&lt;/code&gt; property.&#xa; This plugs in an instance of yourDriver&#x27;s&#xa; device id as this device&#x27;s deviceId
</description>
<tag name="@see">#getDeviceId</tag>
<tag name="@see">#setDeviceId</tag>
</field>

<!-- com.tridium.andoverInfinity.BInfinetDevice.points -->
<field name="points"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;points&lt;/code&gt; property.
</description>
<tag name="@see">#getPoints</tag>
<tag name="@see">#setPoints</tag>
</field>

<!-- com.tridium.andoverInfinity.BInfinetDevice.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
