<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.BIAlarmRecordDecorator" name="BIAlarmRecordDecorator" packageName="com.tridium.alarm" public="true" interface="true" abstract="true" category="interface">
<description>
THIS CLASS IS INTENDED FOR FRAMEWORK USE ONLY! ANY UNEXPECTED IMPLEMENTATIONS WILL BE REJECTED&#xa; WHEN ATTEMPTING TO REGISTER VIA &lt;code&gt;<see ref="com.tridium.alarm.BIAlarmRecordDecorator#registerAlarmRecordDecorator(com.tridium.alarm.BIAlarmRecordDecorator)">#registerAlarmRecordDecorator(BIAlarmRecordDecorator)</see>&lt;/code&gt;.&#xa;&#xa; Implementations of this interface can be used to enhance a &lt;code&gt;<see ref="javax.baja.alarm.BAlarmRecord">BAlarmRecord</see>&lt;/code&gt;&#xa; instance that is currently being read or written from/to its serialized form.&#xa; For example, refer to the implementations of &lt;code&gt;<see ref="javax.baja.alarm.BAlarmRecord#read(java.io.DataInput, javax.baja.sys.Context)">BAlarmRecord#read(DataInput, Context)</see>&lt;/code&gt; and&#xa; &lt;code&gt;<see ref="javax.baja.alarm.BAlarmRecord#write(java.io.DataOutput, javax.baja.sys.Context)">BAlarmRecord#write(DataOutput, Context)</see>&lt;/code&gt; to see how this interface is used.
</description>
<tag name="@author">Scott Hoye on 05/18/2022</tag>
<tag name="@since">Niagara 4.13</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
</class>
</bajadoc>
