<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="andoverAC256" runtimeProfile="rt" name="com.tridium.andoverAC256.datatypes">
<description/>
<class packageName="com.tridium.andoverAC256.datatypes" name="BAndoverBackupConfig"><description>BAndoverBackupConfig holds data for the BAndoverBackupJob</description></class>
<class packageName="com.tridium.andoverAC256.datatypes" name="BAndoverBackupRecord"><description>BAndoverBackupRecord is a dynamic slot added to the BAndoverDevice</description></class>
<class packageName="com.tridium.andoverAC256.datatypes" name="BAndoverIouPointDiscoveryConfig"><description>AndoverAC256 Device Discovery Config, used to govern what&#xa; tables or sub-panel are learned during the learn process</description></class>
<class packageName="com.tridium.andoverAC256.datatypes" name="BAndoverLcuPointDiscoveryConfig"><description>AndoverAC256 Device Discovery Config, used to govern what&#xa; tables or sub-panel are learned during the learn process</description></class>
<class packageName="com.tridium.andoverAC256.datatypes" name="BAndoverPointDiscoveryConfig"><description>AndoverAC256 Device Discovery Config, used to govern what&#xa; tables or sub-panel are learned during the learn process</description></class>
<class packageName="com.tridium.andoverAC256.datatypes" name="BAndoverRestoreConfig"><description>BAndoverRestoreConfig holds data for the BAndoverBackupJob</description></class>
<class packageName="com.tridium.andoverAC256.datatypes" name="BAndoverSetOptionsConfig"><description>AndoverAC256 Set Options Config, used to set options in the&#xa; andover panel using the BAndoverSetOptionsJob.</description></class>
<class packageName="com.tridium.andoverAC256.datatypes" name="BPrintStatusReadings"><description>BPrintStatusReadings - Component that encapsulates all relevant&#xa; data from the Andover PRINT STATUS response</description></class>
<class packageName="com.tridium.andoverAC256.datatypes" name="BProgramReloadSettings"><description>BProgramReloadSettings: Component that encapsulates all relevant&#xa; data used to re-set the number of IOUs and LCUs in the Andover&#xa; configuration.</description></class>
</package>
</bajadoc>
