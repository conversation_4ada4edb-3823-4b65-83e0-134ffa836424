<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="bacnetAws" runtimeProfile="wb" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>Niagara BACnet AWS Driver</description>
<package name="com.tridium.bacnetAws.ui.history"/>
<package name="com.tridium.bacnetAws.ui.device"/>
<package name="com.tridium.bacnetAws.ui.config"/>
<class packageName="com.tridium.bacnetAws.ui.config" name="BBacnetAwsConfigManager"><description>BBacnetAwsConfigManager allows the user to create and manage config&#xa; objects within a BACnet Aws device.</description></class>
<class packageName="com.tridium.bacnetAws.ui.device" name="BBacnetAwsDeviceManager"><description>BBacnetWsDeviceManager is the specialization of BBacnetWsDeviceManager to&#xa; handle Workstation-specific behavior.</description></class>
<class packageName="com.tridium.bacnetAws.ui.history" name="BBacnetAwsHistoryImportManager"><description>BBacnetAwsHistoryImportManager augments BBacnetOwsHistoryImportManager to also&#xa; handle EventLogs.</description></class>
</module>
</bajadoc>
