<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="com.tridium.bacnetAws.job.BReinitializeDeviceJob" name="BReinitializeDeviceJob" packageName="com.tridium.bacnetAws.job" public="true">
<description>
BReinitializeDeviceJob reinitializes a BacnetWsDevice.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">19 Jul 2005</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.job.BDeviceManagerJob"/>
</extends>
</class>
</bajadoc>
