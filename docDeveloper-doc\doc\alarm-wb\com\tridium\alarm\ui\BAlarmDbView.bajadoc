<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.ui.BAlarmDbView" name="BAlarmDbView" packageName="com.tridium.alarm.ui" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@creation">07 Nov 06</tag>
<tag name="@version">$Revision: 7$ $Date: 5/21/10 3:05:26 PM EDT$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="javax.baja.workbench.view.BWbComponentView"/>
</extends>
<implements>
<type class="com.tridium.alarm.ui.BIAlarmServiceView"/>
</implements>
<property name="defaultTimeRange" flags="">
<type class="com.tridium.bql.util.BDynamicTimeRange"/>
<description>
Slot for the &lt;code&gt;defaultTimeRange&lt;/code&gt; property.
</description>
<tag name="@see">#getDefaultTimeRange</tag>
<tag name="@see">#setDefaultTimeRange</tag>
</property>

<property name="timeZoneDisplay" flags="">
<type class="com.tridium.alarm.ui.BTimeZoneDisplay"/>
<description>
Slot for the &lt;code&gt;timeZoneDisplay&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeZoneDisplay</tag>
<tag name="@see">#setTimeZoneDisplay</tag>
</property>

<action name="query" flags="a">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;query&lt;/code&gt; action.
</description>
<tag name="@see">#query()</tag>
</action>

</class>
</bajadoc>
