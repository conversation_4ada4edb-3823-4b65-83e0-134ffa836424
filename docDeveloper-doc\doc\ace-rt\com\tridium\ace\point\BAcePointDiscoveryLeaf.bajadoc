<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.point.BAcePointDiscoveryLeaf" name="BAcePointDiscoveryLeaf" packageName="com.tridium.ace.point" public="true">
<description>
BAcePointDiscoveryLeaf is container class for point elements to display in&#xa; point discovery pane and pass to new point callback.
</description>
<tag name="@author"><PERSON> on 02-Sep-16</tag>
<extends>
<type class="com.tridium.ndriver.discover.BNPointDiscoveryLeaf"/>
</extends>
<implements>
<type class="com.tridium.ndriver.discover.BINDiscoveryIcon"/>
</implements>
<property name="statusValue" flags="r">
<type class="javax.baja.status.BStatusValue"/>
<description>
Slot for the &lt;code&gt;statusValue&lt;/code&gt; property.
</description>
<tag name="@see">#getStatusValue</tag>
<tag name="@see">#setStatusValue</tag>
</property>

<property name="point" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;point&lt;/code&gt; property.
</description>
<tag name="@see">#getPoint</tag>
<tag name="@see">#setPoint</tag>
</property>

<property name="compName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;compName&lt;/code&gt; property.
</description>
<tag name="@see">#getCompName</tag>
<tag name="@see">#setCompName</tag>
</property>

<property name="propName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;propName&lt;/code&gt; property.
</description>
<tag name="@see">#getPropName</tag>
<tag name="@see">#setPropName</tag>
</property>

<property name="compId" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;compId&lt;/code&gt; property.
</description>
<tag name="@see">#getCompId</tag>
<tag name="@see">#setCompId</tag>
</property>

<property name="propId" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;propId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropId</tag>
<tag name="@see">#setPropId</tag>
</property>

<property name="facets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;facets&lt;/code&gt; property.
</description>
<tag name="@see">#getFacets</tag>
<tag name="@see">#setFacets</tag>
</property>

<property name="dataType" flags="">
<type class="com.tridium.ace.enums.BAcePrimitiveTypeEnum"/>
<description>
Slot for the &lt;code&gt;dataType&lt;/code&gt; property.
</description>
<tag name="@see">#getDataType</tag>
<tag name="@see">#setDataType</tag>
</property>

<property name="slotType" flags="">
<type class="com.tridium.ace.enums.BAceSlotTypeEnum"/>
<description>
Slot for the &lt;code&gt;slotType&lt;/code&gt; property.
</description>
<tag name="@see">#getSlotType</tag>
<tag name="@see">#setSlotType</tag>
</property>

<property name="config" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;config&lt;/code&gt; property.
</description>
<tag name="@see">#getConfig</tag>
<tag name="@see">#setConfig</tag>
</property>

<property name="hasDefault" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;hasDefault&lt;/code&gt; property.
</description>
<tag name="@see">#getHasDefault</tag>
<tag name="@see">#setHasDefault</tag>
</property>

<property name="defaultValue" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;defaultValue&lt;/code&gt; property.
</description>
<tag name="@see">#getDefaultValue</tag>
<tag name="@see">#setDefaultValue</tag>
</property>

<property name="rangeVal" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;rangeVal&lt;/code&gt; property.
</description>
<tag name="@see">#getRangeVal</tag>
<tag name="@see">#setRangeVal</tag>
</property>

<property name="isLinkTarget" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;isLinkTarget&lt;/code&gt; property.
</description>
<tag name="@see">#getIsLinkTarget</tag>
<tag name="@see">#setIsLinkTarget</tag>
</property>

</class>
</bajadoc>
