<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.virtual.BBacnetVirtualObject" name="BBacnetVirtualObject" packageName="javax.baja.bacnet.virtual" public="true">
<description>
BBacnetVirtualObject is the virtual representation of a BACnet&#xa; object.  It contains the objectId, tuning policy name, priority for&#xa; writing, and any point facets that configure the display of property&#xa; data.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">05 Dec 2007</tag>
<tag name="@since">NiagaraAX 3.3</tag>
<extends>
<type class="javax.baja.virtual.BVirtualComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<property name="objectId" flags="hr">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="facets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;facets&lt;/code&gt; property.
</description>
<tag name="@see">#getFacets</tag>
<tag name="@see">#setFacets</tag>
</property>

<property name="tuningPolicyName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;tuningPolicyName&lt;/code&gt; property.&#xa; References the TuningPolicy component by name.
</description>
<tag name="@see">#getTuningPolicyName</tag>
<tag name="@see">#setTuningPolicyName</tag>
</property>

<property name="writePriority" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;writePriority&lt;/code&gt; property.
</description>
<tag name="@see">#getWritePriority</tag>
<tag name="@see">#setWritePriority</tag>
</property>

<property name="prioritizedPoint" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;prioritizedPoint&lt;/code&gt; property.
</description>
<tag name="@see">#getPrioritizedPoint</tag>
<tag name="@see">#setPrioritizedPoint</tag>
</property>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject() -->
<constructor name="BBacnetVirtualObject" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject(java.lang.String) -->
<constructor name="BBacnetVirtualObject" public="true">
<parameter name="virtualPathName">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.getObjectId() -->
<method name="getObjectId"  public="true">
<description>
Get the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#objectId</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.setObjectId(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="setObjectId"  public="true">
<description>
Set the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#objectId</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.getFacets() -->
<method name="getFacets"  public="true">
<description>
Get the &lt;code&gt;facets&lt;/code&gt; property.
</description>
<tag name="@see">#facets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.setFacets(javax.baja.sys.BFacets) -->
<method name="setFacets"  public="true">
<description>
Set the &lt;code&gt;facets&lt;/code&gt; property.
</description>
<tag name="@see">#facets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.getTuningPolicyName() -->
<method name="getTuningPolicyName"  public="true">
<description>
Get the &lt;code&gt;tuningPolicyName&lt;/code&gt; property.&#xa; References the TuningPolicy component by name.
</description>
<tag name="@see">#tuningPolicyName</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.setTuningPolicyName(java.lang.String) -->
<method name="setTuningPolicyName"  public="true">
<description>
Set the &lt;code&gt;tuningPolicyName&lt;/code&gt; property.&#xa; References the TuningPolicy component by name.
</description>
<tag name="@see">#tuningPolicyName</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.getWritePriority() -->
<method name="getWritePriority"  public="true">
<description>
Get the &lt;code&gt;writePriority&lt;/code&gt; property.
</description>
<tag name="@see">#writePriority</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.setWritePriority(int) -->
<method name="setWritePriority"  public="true">
<description>
Set the &lt;code&gt;writePriority&lt;/code&gt; property.
</description>
<tag name="@see">#writePriority</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.getPrioritizedPoint() -->
<method name="getPrioritizedPoint"  public="true">
<description>
Get the &lt;code&gt;prioritizedPoint&lt;/code&gt; property.
</description>
<tag name="@see">#prioritizedPoint</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.setPrioritizedPoint(boolean) -->
<method name="setPrioritizedPoint"  public="true">
<description>
Set the &lt;code&gt;prioritizedPoint&lt;/code&gt; property.
</description>
<tag name="@see">#prioritizedPoint</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.started() -->
<method name="started"  public="true">
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.isChildLegal(javax.baja.sys.BComponent) -->
<method name="isChildLegal"  public="true">
<description/>
<parameter name="child">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.device() -->
<method name="device"  public="true">
<description>
Get the containing BBacnetDevice.&#xa; This method will not work for the &#x22;local&#x22; version.
</description>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
<description>
the BBacnetDevice in which this object resides.
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.updateStatus() -->
<method name="updateStatus"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.getPolicy() -->
<method name="getPolicy"  public="true">
<description>
Get the BTuningPolicy configured by policyName. If the policyName doesn&#x27;t&#xa; map to a valid policy then log a warning and use the defaultPolicy.&#xa; &lt;p&gt;&#xa; Note that all virtual components are polled at this time.
</description>
<return>
<type class="javax.baja.bacnet.point.BBacnetTuningPolicy"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.discoverFacets() -->
<method name="discoverFacets"  protected="true">
<description>
Discover the facets to be used for the properties that use them.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.facets -->
<field name="facets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;facets&lt;/code&gt; property.
</description>
<tag name="@see">#getFacets</tag>
<tag name="@see">#setFacets</tag>
</field>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.tuningPolicyName -->
<field name="tuningPolicyName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;tuningPolicyName&lt;/code&gt; property.&#xa; References the TuningPolicy component by name.
</description>
<tag name="@see">#getTuningPolicyName</tag>
<tag name="@see">#setTuningPolicyName</tag>
</field>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.writePriority -->
<field name="writePriority"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;writePriority&lt;/code&gt; property.
</description>
<tag name="@see">#getWritePriority</tag>
<tag name="@see">#setWritePriority</tag>
</field>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.prioritizedPoint -->
<field name="prioritizedPoint"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;prioritizedPoint&lt;/code&gt; property.
</description>
<tag name="@see">#getPrioritizedPoint</tag>
<tag name="@see">#setPrioritizedPoint</tag>
</field>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualObject.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
