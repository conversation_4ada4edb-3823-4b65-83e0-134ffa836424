<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>js Module: nmodule/js/rc/jasmine/promiseUtils</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">js</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-dialogs.html">dialogs</a></li><li><a href="module-lex.html">lex</a></li><li><a href="module-log.html">log</a></li><li><a href="module-nmodule_js_rc_csrf_csrfUtil.html">nmodule/js/rc/csrf/csrfUtil</a></li><li><a href="module-nmodule_js_rc_jasmine_promiseUtils.html">nmodule/js/rc/jasmine/promiseUtils</a></li><li><a href="module-nmodule_js_rc_lex_lex.html">nmodule/js/rc/lex/lex</a></li><li><a href="module-nmodule_js_rc_log_Level.html">nmodule/js/rc/log/Level</a></li><li><a href="module-nmodule_js_rc_log_Log.html">nmodule/js/rc/log/Log</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="classes.list.html" class="dropdown-toggle" data-toggle="dropdown">Classes<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-dialogs-Dialog.html">dialogs~Dialog</a></li><li><a href="module-nmodule_js_rc_lex_lex-Lexicon.html">nmodule/js/rc/lex/lex~Lexicon</a></li><li><a href="module-nmodule_js_rc_log_Log.Level.html">nmodule/js/rc/log/Log.Level</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: nmodule/js/rc/jasmine/promiseUtils</h1>
<section>

<header>
    
</header>


<article>
    <div class="container-overview">
    
        
            <div class="description"><p>API Status: <strong>Private</strong></p>
<p>Module with utility functions for running async Jasmine specs.</p></div>
        

        
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


        
    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id=".addCustomMatchers"><span class="type-signature">&lt;static> </span>addCustomMatchers( [spec])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Adds <a href="CustomMatchers.html">custom matchers</a> to the Jasmine instance<br>
(what is bound to <code>this</code> in a <code>beforeEach</code> function, for instance).</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>spec</code></td>
            

            <td class="type">
            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>The current Jasmine spec; if not given,<br>
<code>jasmine.getEnv().currentSpec</code> will be used</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="CustomMatchers.html">CustomMatchers</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    

    
        <h5>Example</h5>
        
    <pre class="sunlight-highlight-javascript">beforeEach(function () {
    promiseUtils.addCustomMatchers(this);
  });
  //or
  beforeEach(promiseUtils.addCustomMatchers);</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".doPromise"><span class="type-signature">&lt;static> </span>doPromise(promise [, timeoutMessage] [, timeout])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Runs a promise, using the Jasmine <code>runs/waitsFor</code> functions to ensure its<br>
completion. This function will verify that the promise is resolved -<br>
failing the promise will fail the test.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>promise</code></td>
            

            <td class="type">
            
                
<span class="param-type">Promise</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>timeoutMessage</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>optional message to present if timeout occurs</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>timeout</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    5000
                
                </td>
            

            <td class="description last"><p>optional timeout in milliseconds</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise that is verified to have been resolved<br>
(if the input promise rejects, the test will fail).</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
    <pre class="sunlight-highlight-javascript">promiseUtils.doPromise(editor.read()
    .then(function (result) {
      expect(result).toBe(&#x27;my expected read value&#x27;);
    }, function (err) {
      //not necessary to assert anything here - failing the promise will
      //automatically fail the test.
      //if you want to verify fail behavior, use toBeRejected() or
      //toBeRejectedWith() custom matchers.
    }));</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".enforcePromiseAPI"><span class="type-signature">&lt;static> </span>enforcePromiseAPI()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Ensure that the following contract is followed when using <code>doPromise</code> and<br>
<code>executePromise</code>:</p>
<ul>
<li>You may not call <code>.then()</code> on the result of <code>doPromise()</code>.</li>
</ul>
<p>This ensures that <code>doPromise()</code> works correctly with the <code>runs/waits</code> async<br>
API presented by Jasmine 1.3.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".executePromise"><span class="type-signature">&lt;static> </span>executePromise(promise [, timeoutMessage] [, within])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Runs a promise, using the Jasmine <code>runs/waitsFor</code> functions to ensure its<br>
completion. This method only cares that the promise is settled (resolved<br>
or rejected) - if you wish to assert that the promise resolves<br>
successfully, use <code>doPromise</code> instead.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>promise</code></td>
            

            <td class="type">
            
                
<span class="param-type">Promise</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>timeoutMessage</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>optional message to present if timeout occurs</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>within</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    5000
                
                </td>
            

            <td class="description last"><p>optional timeout in milliseconds</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise that may be resolved or rejected</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".isTrackingReturnValues"><span class="type-signature">&lt;static> </span>isTrackingReturnValues(spy)</h4>
    
    
</dt>
<dd>

    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>spy</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.13</li>
		</ul>
	</dd>
	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>true if this is a spy function that is tracking return values</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".noConflict"><span class="type-signature">&lt;static> </span>noConflict()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>By default, promiseUtils augments Jasmine block execution to support<br>
returning promises from blocks and validating manual calls to<br>
<code>doPromise</code>, <code>executePromise</code>, and promise matchers.</p>
<p>Call this to restore original Jasmine block execution. There will not<br>
typically be a reason to call this in practice, but it is provided just in<br>
case.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".prettyPrintBajaObjects"><span class="type-signature">&lt;static> </span>prettyPrintBajaObjects()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>when doing expect(method).toHaveBeenCalledWith(component), jasmine JSON<br>
stringifies the component to print the error. a component's JSON structure<br>
is so huge that this will actually lock up the browser and kill tests. i<br>
found myself having to do<br>
expect(method.mostRecentCall.args[0] === component).toBe(true).<br>
yuck. let's simplify the pretty printing a bit.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".trackSpyReturnValues"><span class="type-signature">&lt;static> </span>trackSpyReturnValues()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Jasmine does not store return values by default. Patch it in so that each call object, in<br>
addition to <code>object</code> and <code>args</code>, stores a <code>result</code> property.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.13</li>
		</ul>
	</dd>
	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".waitForCalled"><span class="type-signature">&lt;static> </span>waitForCalled(func [, times] [, timeout])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return a promise that resolves after the given Jasmine spy has been called<br>
the specified number of times.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>func</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>a Jasmine spy</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>times</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    1
                
                </td>
            

            <td class="description last"><p>the number of times to expect the function to<br>
have been called. Defaults to 1.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>timeout</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    5000
                
                </td>
            

            <td class="description last"><p>the time, in milliseconds, after which to give<br>
up waiting and reject.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".waitForResolved"><span class="type-signature">&lt;static> </span>waitForResolved(func [, times] [, timeout])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>This will both wait for a spy function to be called, and for the promise it returns to be<br>
resolved.</p>
<p>The spy must be tracking return values already - use <code>.andTrackReturnValues()</code>. This is so the<br>
matcher can monitor the resolution status of the returned promises.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>func</code></td>
            

            <td class="type">
            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>times</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    1
                
                </td>
            

            <td class="description last"><p>the number of times to expect the function to<br>
have been called. Defaults to 1.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>timeout</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    5000
                
                </td>
            

            <td class="description last"><p>the time, in milliseconds, after which to give<br>
up waiting and reject.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>if <code>times</code> is omitted, this will resolve to the result<br>
of the first call to the function. Otherwise, this will resolve to an array (of <code>times</code> length)<br>
of that many calls to the function.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise.&lt;(*|Array.&lt;*>)></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".waitForReturnedPromises"><span class="type-signature">&lt;static> </span>waitForReturnedPromises()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Alters the default behavior of <code>it()</code>. If a <code>Promise</code> is returned from an<br>
<code>it()</code> call, Jasmine will wait for that promise to resolve (up to the<br>
default timeout) before completing the spec.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
        <h5>Example</h5>
        
    <pre class="sunlight-highlight-javascript">promiseUtils.waitForReturnedPromises();

it(&#x27;waits for a returned promise to resolve&#x27;, function () {
  return promiseUtils.waitInterval(1000)
    .then(function () {
      expect(&#x27;a&#x27;).toBe(&#x27;b&#x27;); //correctly fails, because Jasmine waited
    });
});</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".waitForTrue"><span class="type-signature">&lt;static> </span>waitForTrue(func [, msg] [, timeout])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Run a promise, using <code>setTimeout</code> to check for the truthiness of the<br>
condition function. This will not use <code>waitsFor/runs</code> and as such can be<br>
used in conjunction with <code>doPromise/executePromise</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>func</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>resolve the promise when this function returns a<br>
truthy value, or a promise that resolves to a truthy value</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>msg</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>the message to reject with upon timeout</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>timeout</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>the time, in milliseconds, after which to give<br>
up waiting and reject</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".waitInterval"><span class="type-signature">&lt;static> </span>waitInterval( [interval])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return a promise that waits a certain number of milliseconds before<br>
resolving. This will not use <code>waitsFor/runs</code> and as such can be<br>
used in conjunction with <code>doPromise/executePromise</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>interval</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    0
                
                </td>
            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        </dl>
    

    
        <h3 class="subsection-title">Type Definitions</h3>

        <dl>
                
<hr>
<dt class="name" id="~PromiseResolutionParams">
    <h4 id="~PromiseResolutionParams">PromiseResolutionParams</h4>

    
</dt>
<dd>
    

    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">object</span>



            </li>
        </ul>
    

    
<dl class="details">
    

    <h5 class="subsection-title">Properties:</h5>

    <dl>

<table class="props table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>within</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>promise must settle within this many milliseconds</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>timeoutMessage</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>fail the spec with this message if the promise does not<br>
settle in time</p></td>
        </tr>

    
    </tbody>
</table>
</dl>

    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

            </dl>
    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	js Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:57+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>