<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor" name="BBacnetAnalogWritableDescriptor" packageName="javax.baja.bacnet.export" public="true" abstract="true">
<description>
BBacnetAnalogWritableDescriptor exposes a ControlPoint as a&#xa; commandable analog point.  It is the superclass for Analog Output&#xa; and commandable Analog Value points.
</description>
<tag name="@author"><PERSON> on 19 Feb 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.export.BBacnetAnalogPointDescriptor"/>
</extends>
<implements>
<type class="javax.baja.bacnet.export.BacnetWritableDescriptor"/>
</implements>
<property name="bacnetWritable" flags="rh">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;bacnetWritable&lt;/code&gt; property.
</description>
<tag name="@see">#getBacnetWritable</tag>
<tag name="@see">#setBacnetWritable</tag>
</property>

<!-- javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor() -->
<constructor name="BBacnetAnalogWritableDescriptor" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor.getBacnetWritable() -->
<method name="getBacnetWritable"  public="true">
<description>
Get the &lt;code&gt;bacnetWritable&lt;/code&gt; property.
</description>
<tag name="@see">#bacnetWritable</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor.setBacnetWritable(java.lang.String) -->
<method name="setBacnetWritable"  public="true">
<description>
Set the &lt;code&gt;bacnetWritable&lt;/code&gt; property.
</description>
<tag name="@see">#bacnetWritable</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor.isPointTypeLegal(javax.baja.control.BControlPoint) -->
<method name="isPointTypeLegal"  protected="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
BBacnetAnalogWritableDescriptors may only expose BNumericWritables.
</description>
<parameter name="pt">
<type class="javax.baja.control.BControlPoint"/>
<description>
the exposed point
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the Niagara point type is legal for this point type.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor.isCommandable() -->
<method name="isCommandable"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is this export descriptor representing a BACnet object&#xa; with a Commandable Present_Value property (per the Clause 19&#xa; prioritization procedure)?&lt;p&gt;&#xa; Writable descriptors must override this to return true.
</description>
<return>
<type class="boolean"/>
<description>
true if commmandable, otherwise false
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor.commandabilityRequired() -->
<method name="commandabilityRequired"  protected="true">
<description>
The priority array and relinquish default properties&#xa; are required for analog outputs but optional for analog values.
</description>
<return>
<type class="boolean"/>
<description>
true if the priority array and relinquish default properties&#xa; should be setup as required properties
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor.readProperty(int, int) -->
<method name="readProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor.writeProperty(int, int, byte[], int) -->
<method name="writeProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor.addRequiredProps(java.util.Vector) -->
<method name="addRequiredProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Subclass override method to add required properties.&#xa; NOTE: You MUST call super.addRequiredProps(v) first!
</description>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
Vector containing required propertyIds.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor.addOptionalProps(java.util.Vector) -->
<method name="addOptionalProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Subclass override method to add optional properties.&#xa; NOTE: You MUST call super.addOptionalProps(v) first!
</description>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
Vector containing optional propertyIds.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor.readPriorityArray(int) -->
<method name="readPriorityArray"  protected="true">
<description>
Read the priority array.
</description>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index:&#xa;            -1: return the entire array&#xa;            0: return the array size (16)&#xa;            n: return the value in slot n
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue, containing the encoded value or an appropriate error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor.knobAdded(javax.baja.sys.Knob, javax.baja.sys.Context) -->
<method name="knobAdded"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="knob">
<type class="javax.baja.sys.Knob"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor.knobRemoved(javax.baja.sys.Knob, javax.baja.sys.Context) -->
<method name="knobRemoved"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="knob">
<type class="javax.baja.sys.Knob"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor.doMakeWritable(javax.baja.sys.BValue) -->
<method name="doMakeWritable"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="writable">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor.writeOptionalProperty(int, int, byte[], int) -->
<method name="writeOptionalProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the value of an optional property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor.bacnetWritable -->
<field name="bacnetWritable"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;bacnetWritable&lt;/code&gt; property.
</description>
<tag name="@see">#getBacnetWritable</tag>
<tag name="@see">#setBacnetWritable</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
