<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.dataRecovery.IDataRecoveryRecord" name="IDataRecoveryRecord" packageName="javax.baja.dataRecovery" public="true" interface="true" abstract="true" category="interface">
<description>
IDataRecoveryRecord defines the contract that all restored&#xa; data recovery must obey.  IDataRecoveryRecord instances are always&#xa; returned when the BIDataRecoveryService is asked to restore(), or &#xa; replay, data recovery records.
</description>
<tag name="@author">Mike <PERSON></tag>
<tag name="@creation">Jul 14, 2009</tag>
<tag name="@version">Original</tag>
<tag name="@since">Niagara 3.6</tag>
<!-- javax.baja.dataRecovery.IDataRecoveryRecord.getData() -->
<method name="getData"  public="true" abstract="true">
<description>
The raw byte data stored for this data recovery record.
</description>
<return>
<type class="byte" dimension="1"/>
</return>
</method>

<!-- javax.baja.dataRecovery.IDataRecoveryRecord.getKey() -->
<method name="getKey"  public="true" abstract="true">
<description>
The raw bytes of the key for this data recovery record.
</description>
<return>
<type class="byte" dimension="1"/>
</return>
</method>

<!-- javax.baja.dataRecovery.IDataRecoveryRecord.getDataRecoverySourceIdentifier() -->
<method name="getDataRecoverySourceIdentifier"  public="true" abstract="true">
<description>
The unique byte that was assigned as a handle to the source of this record.
</description>
<return>
<type class="byte"/>
</return>
</method>

</class>
</bajadoc>
