<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.datatypes.BAceReadParams" name="BAceReadParams" packageName="com.tridium.ace.datatypes" public="true">
<description>
BAceReadParams - is container arguments needed to read an ACE component property
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">4/26/2019</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="objectId" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="propId" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;propId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropId</tag>
<tag name="@see">#setPropId</tag>
</property>

</class>
</bajadoc>
