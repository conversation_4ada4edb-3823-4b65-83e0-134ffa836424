<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.device.overrides.ApduSizeOverride" name="ApduSizeOverride" packageName="javax.baja.bacnet.device.overrides" public="true" interface="true" abstract="true" category="interface">
<description>
Allow manual and persistent definition&#xa; APDU sizes that will be observed while&#xa; communicating with a device.&#xa; &lt;p&gt;&#xa; The primary use will be to identify&#xa; APDU size bottleneck on the network,&#xa; and operate within those constraints.
</description>
<tag name="@author"><PERSON></tag>
<implements>
<type class="javax.baja.bacnet.device.overrides.DeviceOverride"/>
</implements>
<!-- javax.baja.bacnet.device.overrides.ApduSizeOverride.getMaxAPDULengthAccepted(javax.baja.bacnet.config.BBacnetDeviceObject) -->
<method name="getMaxAPDULengthAccepted"  public="true" abstract="true">
<description>
Each implementation of ApduSizeOverride should typically&#xa; reduce the size of the claimed APDU size passed in.
</description>
<parameter name="device">
<type class="javax.baja.bacnet.config.BBacnetDeviceObject"/>
</parameter>
<return>
<type class="int"/>
<description>
a more restrictive APDU size, or the claimed size&#xa; if it is smaller.
</description>
</return>
</method>

</class>
</bajadoc>
