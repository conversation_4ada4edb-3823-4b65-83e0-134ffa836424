<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BBacnetCreatableObject" name="BBacnetCreatableObject" packageName="javax.baja.bacnet.config" public="true" abstract="true">
<description/>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">05 Jun 2019</tag>
<tag name="@since">Niagara 4.9</tag>
<extends>
<type class="javax.baja.bacnet.BBacnetObject"/>
</extends>
<property name="description" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#getDescription</tag>
<tag name="@see">#setDescription</tag>
</property>

<action name="createObject" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;createObject&lt;/code&gt; action.
</description>
<tag name="@see">#createObject()</tag>
</action>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject() -->
<constructor name="BBacnetCreatableObject" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.getDescription() -->
<method name="getDescription"  public="true">
<description>
Get the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#description</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.setDescription(java.lang.String) -->
<method name="setDescription"  public="true">
<description>
Set the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#description</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.createObject() -->
<method name="createObject"  public="true">
<description>
Invoke the &lt;code&gt;createObject&lt;/code&gt; action.
</description>
<tag name="@see">#createObject</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.started() -->
<method name="started"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.addDescription(javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addDescription"  protected="true">
<description/>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.addObjectName(javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addObjectName"  protected="true">
<description/>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.addEventParameter(javax.baja.bacnet.datatypes.BBacnetEventParameter, javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addEventParameter"  protected="true">
<description/>
<parameter name="eventParameter">
<type class="javax.baja.bacnet.datatypes.BBacnetEventParameter"/>
</parameter>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.addEventEnable(javax.baja.bacnet.datatypes.BBacnetBitString, javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addEventEnable"  protected="true">
<description/>
<parameter name="eventEnable">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.addNotificationClass(javax.baja.bacnet.datatypes.BBacnetUnsigned, javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addNotificationClass"  protected="true">
<description/>
<parameter name="notificationClass">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.addLogDeviceObjectPropertyReference(javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference, javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addLogDeviceObjectPropertyReference"  protected="true">
<description/>
<parameter name="objectPropertyReference">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
</parameter>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.addObjectPropertyReference(javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference, javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addObjectPropertyReference"  protected="true">
<description/>
<parameter name="objectPropertyReference">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
</parameter>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.addEventType(javax.baja.sys.BEnum, javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addEventType"  protected="true">
<description/>
<parameter name="evType">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.addNotifyType(javax.baja.bacnet.enums.BBacnetNotifyType, javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addNotifyType"  protected="true">
<description/>
<parameter name="notifyType">
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
</parameter>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.addRecipientist(javax.baja.sys.Property, javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addRecipientist"  protected="true">
<description/>
<parameter name="recipientList">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.addListOfObjectPropertyReferences(javax.baja.sys.Property, javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addListOfObjectPropertyReferences"  protected="true">
<description/>
<parameter name="listOfObjectPropertyReferences">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.addPriorityForWriting(javax.baja.bacnet.datatypes.BBacnetUnsigned, javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addPriorityForWriting"  protected="true">
<description/>
<parameter name="priorityForWriting">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.addScheduleDefault(javax.baja.sys.Property, javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addScheduleDefault"  protected="true">
<description/>
<parameter name="scheduleDefault">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="listOfInitialValue">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.doCreateObject() -->
<method name="doCreateObject"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.addInitialValues(javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addInitialValues"  protected="true">
<description/>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.addPriority(javax.baja.sys.Property, javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addPriority"  protected="true">
<description/>
<parameter name="priority">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.addAckRequired(javax.baja.sys.Property, javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addAckRequired"  protected="true">
<description/>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.addObjectInitialValues(javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addObjectInitialValues"  protected="true" abstract="true">
<description/>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.description -->
<field name="description"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#getDescription</tag>
<tag name="@see">#setDescription</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.createObject -->
<field name="createObject"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;createObject&lt;/code&gt; action.
</description>
<tag name="@see">#createObject()</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetCreatableObject.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
