<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.BAndoverScript" name="BAndoverScript" packageName="com.tridium.andoverAC256" public="true">
<description>
The BAndoverScript class can be used to send multiple command lines&#xa;  to an andover panel.  Last executed command should always return&#xa;  the panel to dom 0 (R&gt; prompt).
</description>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="script" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;script&lt;/code&gt; property.&#xa; The commands to execute on positive transition&#xa; Note that behind the scenes, the last command&#xa; executed in the script, the command DOM 0 will be&#xa; sent to the controller.
</description>
<tag name="@see">#getScript</tag>
<tag name="@see">#setScript</tag>
</property>

<property name="scriptResults" flags="rt">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;scriptResults&lt;/code&gt; property.&#xa; The commands to execute on positive transition
</description>
<tag name="@see">#getScriptResults</tag>
<tag name="@see">#setScriptResults</tag>
</property>

<property name="responseTimeout" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;responseTimeout&lt;/code&gt; property.&#xa; the time the script job will wait for a complete response to&#xa; a single script line.  Note that some commands to the Andover&#xa; panel can take a long time to execute, so be sure to set the&#xa; timeout here long enough to cover the longest command response.&#xa; For example, PRINT SV returns a large table of data, so the&#xa; timeout needs to be long enough to get the complete response.
</description>
<tag name="@see">#getResponseTimeout</tag>
<tag name="@see">#setResponseTimeout</tag>
</property>

<action name="run" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;run&lt;/code&gt; action.&#xa; The run action spawns a job to send the script commands to the&#xa; Andover panel.&#xa; &lt;p&gt;  Some helpful hints:&#xa; &lt;p&gt;  If you want to run an action once on station start-up,&#xa; then link the BAndoverDevice&#x27;s commInitialized property to this action.&#xa; &lt;p&gt;  If you want to run an action on every transition of device down to&#xa; device up, then link the BAndoverDevice&#x27;s logonSuccessful topic&#xa; to this action.
</description>
<tag name="@see">#run()</tag>
</action>

</class>
</bajadoc>
