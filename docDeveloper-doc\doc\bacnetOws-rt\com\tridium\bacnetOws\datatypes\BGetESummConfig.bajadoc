<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetOws" runtimeProfile="rt" qualifiedName="com.tridium.bacnetOws.datatypes.BGetESummConfig" name="BGetESummConfig" packageName="com.tridium.bacnetOws.datatypes" public="true">
<description>
BGetESummConfig represents the choices for the&#xa; user in manually issuing a GetEnrollmentSummary-Request to a device.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">24 Sep 2009</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.5</tag>
<extends>
<type class="com.tridium.bacnet.datatypes.BRequestConfig"/>
</extends>
<property name="deviceAddress" flags="h">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
Slot for the &lt;code&gt;deviceAddress&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceAddress</tag>
<tag name="@see">#setDeviceAddress</tag>
</property>

<property name="acknowledgment" flags="">
<type class="com.tridium.bacnet.enums.BAcknowledgmentFilter"/>
<description>
Slot for the &lt;code&gt;acknowledgment&lt;/code&gt; property.
</description>
<tag name="@see">#getAcknowledgment</tag>
<tag name="@see">#setAcknowledgment</tag>
</property>

<property name="enrollment" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetRecipientProcess"/>
<description>
Slot for the &lt;code&gt;enrollment&lt;/code&gt; property.
</description>
<tag name="@see">#getEnrollment</tag>
<tag name="@see">#setEnrollment</tag>
</property>

<property name="eventState" flags="">
<type class="com.tridium.bacnet.enums.BEventStateFilter"/>
<description>
Slot for the &lt;code&gt;eventState&lt;/code&gt; property.
</description>
<tag name="@see">#getEventState</tag>
<tag name="@see">#setEventState</tag>
</property>

<property name="eventType" flags="">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;eventType&lt;/code&gt; property.
</description>
<tag name="@see">#getEventType</tag>
<tag name="@see">#setEventType</tag>
</property>

<property name="priority" flags="">
<type class="com.tridium.bacnet.datatypes.BPriorityFilter"/>
<description>
Slot for the &lt;code&gt;priority&lt;/code&gt; property.
</description>
<tag name="@see">#getPriority</tag>
<tag name="@see">#setPriority</tag>
</property>

<property name="notificationClass" flags="">
<type class="long"/>
<description>
Slot for the &lt;code&gt;notificationClass&lt;/code&gt; property.
</description>
<tag name="@see">#getNotificationClass</tag>
<tag name="@see">#setNotificationClass</tag>
</property>

</class>
</bajadoc>
