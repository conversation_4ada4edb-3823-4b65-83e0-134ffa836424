<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>js Module: nmodule/js/rc/lex/lex</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">js</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-dialogs.html">dialogs</a></li><li><a href="module-lex.html">lex</a></li><li><a href="module-log.html">log</a></li><li><a href="module-nmodule_js_rc_csrf_csrfUtil.html">nmodule/js/rc/csrf/csrfUtil</a></li><li><a href="module-nmodule_js_rc_jasmine_promiseUtils.html">nmodule/js/rc/jasmine/promiseUtils</a></li><li><a href="module-nmodule_js_rc_lex_lex.html">nmodule/js/rc/lex/lex</a></li><li><a href="module-nmodule_js_rc_log_Level.html">nmodule/js/rc/log/Level</a></li><li><a href="module-nmodule_js_rc_log_Log.html">nmodule/js/rc/log/Log</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="classes.list.html" class="dropdown-toggle" data-toggle="dropdown">Classes<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-dialogs-Dialog.html">dialogs~Dialog</a></li><li><a href="module-nmodule_js_rc_lex_lex-Lexicon.html">nmodule/js/rc/lex/lex~Lexicon</a></li><li><a href="module-nmodule_js_rc_log_Log.Level.html">nmodule/js/rc/log/Log.Level</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: nmodule/js/rc/lex/lex</h1>
<section>

<header>
    
</header>


<article>
    <div class="container-overview">
    
        
            <div class="description"><p>A JavaScript library used to access translated Lexicon values from the Niagara<br>
Framework.</p>
<p>This library will make network calls back to a Web Server to access translated<br>
values.</p>
<p>Attempts will also be made to use local storage to cache recorded Lexicon<br>
values. If a user logs on with a different locale or the registry has been updated,<br>
this storage will be automatically cleared.</p>
<p>Please try out the examples from the <code>BajauxExamples</code> folder available from the<br>
<code>docDeveloper</code> palette to see how this gets used. Also there are some more code<br>
examples embedded into the method comments below.</p>
<p>RequireJS configuration options can be specified for this library...</p>
<ul>
<li><strong>noStorage</strong>: if truthy, no attempt at using storage will be used.</li>
<li><strong>forceInit</strong>: if truthy, an 'init' network call will be always be made<br>
the first time this library loads.</li>
<li><strong>lang</strong>: if specified, the user locale for the Lexicons. If this<br>
isn't specified the library will make a network call<br>
for it when it first loads.</li>
<li><strong>storageId</strong>: if specified, the id that helps determine whether<br>
the storage database currently being used is out of date.</li>
</ul>
<p>This library is designed to be used through the RequireJS Lexicon plugin...</p></div>
        

        
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-lex.html">module:lex</a></li>
			</ul>
	</dd>
	

	
</dl>


        
            <h3>Examples</h3>
            
        <p class="code-caption">
  Access the Lexicon JS library using RequireJS.
</p>
    
    <pre class="sunlight-highlight-javascript">require([&quot;lex!&quot;], function (lexjs) {
  lexjs.module(&quot;js&quot;)
       .then(function (lex) {
         console.log(&quot;The Dialog OK button text: &quot; + lex.get(&quot;dialogs.ok&quot;));
       });
});</pre>

        <p class="code-caption">
  Directly access a module's Lexicon using the plug-in syntax for RequireJS
</p>
    
    <pre class="sunlight-highlight-javascript">require([&quot;lex!js,bajaui&quot;], function (lexicons) {
  // The lexicon&#x27;s array holds the Lexicon for both the js and bajaui modules.
  console.log(&quot;The Dialog OK button text: &quot; + lexicons[0].get(&quot;dialogs.ok&quot;));
});</pre>


        
    
    </div>

    

    
        <h3 class="subsection-title">Requires</h3>

        <ul>
            <li>module:Promise,</li>
        </ul>
    

    
        <h3 class="subsection-title">Classes</h3>

        <dl>
            <dt><a href="module-nmodule_js_rc_lex_lex-Lexicon.html">Lexicon</a></dt>
            <dd></dd>
        </dl>
    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id=".format"><span class="type-signature">&lt;static> </span>format(str)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Asynchronously format a String using Niagara's BFormat conventions.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>str</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>the string that contains the BFormat style text<br>
to use. The syntax should be <code>%lexicon(moduleName:keyName)%</code> or<br>
<code>%lexicon(moduleName:keyName:formatString1:formatString2)%</code>.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
    <pre class="sunlight-highlight-javascript">lexjs.format(&quot;%lexicon(bajaui:dialog.ok)% and %lexicon(bajaui:menu.new.label)%&quot;)
       .then(function (str) {
         // Prints: &quot;OK and New&quot;
         console.log(str);
       });
  lexjs.format(&quot;%lexicon(bajaui:fileSearch.scanningFiles:arg1:arg2)%&quot;)
       .then(function (str) {
         // Prints: &quot;Scanning files (found arg1 of arg2)...&quot;
         console.log(str);
       });</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".getLexiconFromCache"><span class="type-signature">&lt;static> </span>getLexiconFromCache(moduleName)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>If the Lexicon is loaded and cached then return it. Otherwise return null.<br>
Please note, this will not result in any network calls.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>moduleName</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>The name of the module.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The Lexicon or null if it can't be found.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Lexicon</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".module"><span class="type-signature">&lt;static> </span>module(moduleName)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Asynchronously resolve a Lexicon via module name. A promise is returned and resolved once<br>
Lexicon has been found. If the Lexicon can't be found or there's a network error,<br>
the promise will reject.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>moduleName</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>the name of the module being requested.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
        <p class="code-caption">Access a Lexicon via its module name</p>
    
    <pre class="sunlight-highlight-javascript">lexjs.module(&quot;myModule&quot;)
       .then(function (lex) {
         // Access the Lexicon entry called &#x27;foo&#x27; from &#x27;myModule&#x27;
         console.log(lex.get(&quot;foo&quot;));
       });</pre>


    
</dd>

        </dl>
    

    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	js Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:57+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>