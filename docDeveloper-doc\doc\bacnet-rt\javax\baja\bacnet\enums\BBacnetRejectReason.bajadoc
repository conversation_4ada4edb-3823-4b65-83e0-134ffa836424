<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetRejectReason" name="BBacnetRejectReason" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetRejectReason represents the Bacnet Abort Reason&#xa; enumeration.&#xa; &lt;p&gt;&#xa; BBacnetAbortReason is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Craig Gemmill</tag>
<tag name="@version">$Revision: 7$ $Date: 12/19/01 4:36:01 PM$</tag>
<tag name="@creation">10 Aug 00</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;other&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bufferOverflow&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;inconsistentParameters&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;invalidParameterDataType&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;invalidTag&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;missingRequiredParameter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;parameterOutOfRange&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tooManyArguments&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;undefinedEnumeration&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unrecognizedService&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetRejectReason.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetRejectReason"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetRejectReason"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.OTHER -->
<field name="OTHER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for other.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.BUFFER_OVERFLOW -->
<field name="BUFFER_OVERFLOW"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bufferOverflow.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.INCONSISTENT_PARAMETERS -->
<field name="INCONSISTENT_PARAMETERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for inconsistentParameters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.INVALID_PARAMETER_DATA_TYPE -->
<field name="INVALID_PARAMETER_DATA_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for invalidParameterDataType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.INVALID_TAG -->
<field name="INVALID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for invalidTag.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.MISSING_REQUIRED_PARAMETER -->
<field name="MISSING_REQUIRED_PARAMETER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for missingRequiredParameter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.PARAMETER_OUT_OF_RANGE -->
<field name="PARAMETER_OUT_OF_RANGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for parameterOutOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.TOO_MANY_ARGUMENTS -->
<field name="TOO_MANY_ARGUMENTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tooManyArguments.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.UNDEFINED_ENUMERATION -->
<field name="UNDEFINED_ENUMERATION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for undefinedEnumeration.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.UNRECOGNIZED_SERVICE -->
<field name="UNRECOGNIZED_SERVICE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unrecognizedService.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.other -->
<field name="other"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRejectReason"/>
<description>
BBacnetRejectReason constant for other.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.bufferOverflow -->
<field name="bufferOverflow"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRejectReason"/>
<description>
BBacnetRejectReason constant for bufferOverflow.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.inconsistentParameters -->
<field name="inconsistentParameters"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRejectReason"/>
<description>
BBacnetRejectReason constant for inconsistentParameters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.invalidParameterDataType -->
<field name="invalidParameterDataType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRejectReason"/>
<description>
BBacnetRejectReason constant for invalidParameterDataType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.invalidTag -->
<field name="invalidTag"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRejectReason"/>
<description>
BBacnetRejectReason constant for invalidTag.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.missingRequiredParameter -->
<field name="missingRequiredParameter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRejectReason"/>
<description>
BBacnetRejectReason constant for missingRequiredParameter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.parameterOutOfRange -->
<field name="parameterOutOfRange"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRejectReason"/>
<description>
BBacnetRejectReason constant for parameterOutOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.tooManyArguments -->
<field name="tooManyArguments"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRejectReason"/>
<description>
BBacnetRejectReason constant for tooManyArguments.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.undefinedEnumeration -->
<field name="undefinedEnumeration"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRejectReason"/>
<description>
BBacnetRejectReason constant for undefinedEnumeration.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.unrecognizedService -->
<field name="unrecognizedService"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRejectReason"/>
<description>
BBacnetRejectReason constant for unrecognizedService.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetRejectReason"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetRejectReason.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
