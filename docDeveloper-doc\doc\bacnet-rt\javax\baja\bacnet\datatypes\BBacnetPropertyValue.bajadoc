<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetPropertyValue" name="BBacnetPropertyValue" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
This class represents the BacnetPropertyValue sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">29 Jul 2005</tag>
<tag name="@since">Niagara 3.1</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</implements>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="propertyId" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyId</tag>
<tag name="@see">#setPropertyId</tag>
</property>

<property name="propertyArrayIndex" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyArrayIndex</tag>
<tag name="@see">#setPropertyArrayIndex</tag>
</property>

<property name="value" flags="">
<type class="javax.baja.sys.BValue"/>
<description>
Slot for the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#getValue</tag>
<tag name="@see">#setValue</tag>
</property>

<property name="priority" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;priority&lt;/code&gt; property.
</description>
<tag name="@see">#getPriority</tag>
<tag name="@see">#setPriority</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue() -->
<constructor name="BBacnetPropertyValue" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue(int, javax.baja.sys.BSimple) -->
<constructor name="BBacnetPropertyValue" public="true">
<parameter name="propertyId">
<type class="int"/>
<description>
the property-identifier to be referenced.
</description>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BSimple"/>
<description>
the property value.
</description>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue(int, int, javax.baja.sys.BSimple) -->
<constructor name="BBacnetPropertyValue" public="true">
<parameter name="propertyId">
<type class="int"/>
<description>
the property-identifier to be referenced.
</description>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
<description>
the array index.
</description>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BSimple"/>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue(int, javax.baja.sys.BSimple, int) -->
<constructor name="BBacnetPropertyValue" public="true">
<parameter name="propertyId">
<type class="int"/>
<description>
the property-identifier to be referenced.
</description>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BSimple"/>
</parameter>
<parameter name="priority">
<type class="int"/>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue(int, int, javax.baja.sys.BSimple, int) -->
<constructor name="BBacnetPropertyValue" public="true">
<parameter name="propertyId">
<type class="int"/>
<description>
the property-identifier to be referenced.
</description>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
<description>
the array index.
</description>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BSimple"/>
</parameter>
<parameter name="priority">
<type class="int"/>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.getPropertyId() -->
<method name="getPropertyId"  public="true">
<description>
Get the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#propertyId</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.setPropertyId(int) -->
<method name="setPropertyId"  public="true">
<description>
Set the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#propertyId</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.getPropertyArrayIndex() -->
<method name="getPropertyArrayIndex"  public="true">
<description>
Get the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#propertyArrayIndex</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.setPropertyArrayIndex(int) -->
<method name="setPropertyArrayIndex"  public="true">
<description>
Set the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#propertyArrayIndex</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.getValue() -->
<method name="getValue"  public="true">
<description>
Get the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#value</tag>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.setValue(javax.baja.sys.BValue) -->
<method name="setValue"  public="true">
<description>
Set the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#value</tag>
<parameter name="v">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.getPriority() -->
<method name="getPriority"  public="true">
<description>
Get the &lt;code&gt;priority&lt;/code&gt; property.
</description>
<tag name="@see">#priority</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.setPriority(int) -->
<method name="setPriority"  public="true">
<description>
Set the &lt;code&gt;priority&lt;/code&gt; property.
</description>
<tag name="@see">#priority</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.getPropertyValue() -->
<method name="getPropertyValue"  public="true">
<description>
Get the encoded value.
</description>
<return>
<type class="byte" dimension="1"/>
<description>
a byte array containing the Asn-encoded value,&#xa; or null if this is a failure.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.getPropertyAccessError() -->
<method name="getPropertyAccessError"  public="true">
<description>
Get the error.
</description>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
an ErrorType if this is an error result,&#xa; or null if this is a success.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.getErrorClass() -->
<method name="getErrorClass"  public="true">
<description>
Get the error class.
</description>
<return>
<type class="int"/>
<description>
an int representing a value in the BBacnetErrorClass&#xa; enumeration indicating the class of failure,&#xa; or null if this is a success.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.getErrorCode() -->
<method name="getErrorCode"  public="true">
<description>
Get the error code.
</description>
<return>
<type class="int"/>
<description>
an int representing a value in the BBacnetErrorCode&#xa; enumeration indicating the reason for failure,&#xa; or null if this is a success.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.isError() -->
<method name="isError"  public="true">
<description>
Is this a failure result?
</description>
<return>
<type class="boolean"/>
<description>
TRUE if this is an error result, or FALSE if it is a success.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.isPropertyArrayIndexUsed() -->
<method name="isPropertyArrayIndexUsed"  public="true">
<description/>
<return>
<type class="boolean"/>
<description>
true if the property array index is used.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.isPriorityUsed() -->
<method name="isPriorityUsed"  public="true">
<description/>
<return>
<type class="boolean"/>
<description>
true if the property array index is used.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
a descriptive string.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.propertyId -->
<field name="propertyId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyId</tag>
<tag name="@see">#setPropertyId</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.propertyArrayIndex -->
<field name="propertyArrayIndex"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyArrayIndex</tag>
<tag name="@see">#setPropertyArrayIndex</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.value -->
<field name="value"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#getValue</tag>
<tag name="@see">#setValue</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.priority -->
<field name="priority"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;priority&lt;/code&gt; property.
</description>
<tag name="@see">#getPriority</tag>
<tag name="@see">#setPriority</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.PROPERTY_ID_TAG -->
<field name="PROPERTY_ID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
BacnetPropertyValue Asn Context Tags&#xa; See Bacnet Clause 21.
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.PROPERTY_ARRAY_INDEX_TAG -->
<field name="PROPERTY_ARRAY_INDEX_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.VALUE_TAG -->
<field name="VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyValue.PRIORITY_TAG -->
<field name="PRIORITY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
