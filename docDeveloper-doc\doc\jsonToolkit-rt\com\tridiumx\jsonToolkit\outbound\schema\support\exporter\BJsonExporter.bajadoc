<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.support.exporter.BJsonExporter" name="BJsonExporter" packageName="com.tridiumx.jsonToolkit.outbound.schema.support.exporter" public="true">
<description>
Json Exporter creates a file with the current output of the schema being viewed.&#xa; &lt;p&gt;&#xa; To access use either the button on the workbench toolbar, or  File &gt; Export &gt; Json Exporter&#xa; &lt;p&gt;&#xa; Appended to an ORD referencing a Schema will provide the output String:&#xa; %7Cview:jsonToolkit:JsonExporter
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.file.BExporter"/>
</extends>
<!-- com.tridiumx.jsonToolkit.outbound.schema.support.exporter.BJsonExporter() -->
<constructor name="BJsonExporter" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.support.exporter.BJsonExporter.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.support.exporter.BJsonExporter.getFileType() -->
<method name="getFileType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.registry.TypeInfo"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.support.exporter.BJsonExporter.getFileExtension() -->
<method name="getFileExtension"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.support.exporter.BJsonExporter.export(javax.baja.file.ExportOp) -->
<method name="export"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="op">
<type class="javax.baja.file.ExportOp"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.support.exporter.BJsonExporter.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.support.exporter.BJsonExporter.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
