<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="alarm" runtimeProfile="rt" name="javax.baja.alarm.ext.fault">
<description>
&lt;p&gt;This package provides classes for fault alarm algorithms for control points.&lt;/p&gt;
</description>
<class packageName="javax.baja.alarm.ext.fault" name="BEnumFaultAlgorithm"><description>BEnumFaultAlgorithm implements a change of state&#xa; fault detection algorithm for enum objects as described&#xa; in BACnet Clause 13.3.2.</description></class>
<class packageName="javax.baja.alarm.ext.fault" name="BOutOfRangeFaultAlgorithm"><description>BOutOfRangeFaultAlgorithm implements a standard out-of-range&#xa; alarming algorithm</description></class>
<class packageName="javax.baja.alarm.ext.fault" name="BStatusFaultAlgorithm"><description>BStatusAlgorithm allows alarming based on the ControlsPoint&#x27;s status value.</description></class>
<class packageName="javax.baja.alarm.ext.fault" name="BTwoStateFaultAlgorithm"><description>BTwoStateFaultAlgorithm implements a generic algorithm for&#xa; objects with only an normal / fault states (vs.</description></class>
</package>
</bajadoc>
