<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="ace" runtimeProfile="ux" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>BajaUX UI implementation of Ace driver</description>
<package name="com.tridium.ace.ux"/>
<class packageName="com.tridium.ace.ux" name="BAceComponentJavaScriptMenuAgent"><description>BAceComponentJavaScriptMenuAgent to remove&#xa; commands for BAceComponents.</description></class>
<class packageName="com.tridium.ace.ux" name="BAceDriverJsBuild"><description>JavaScript build for Ace ux manager views.</description></class>
<class packageName="com.tridium.ace.ux" name="BAcePointUxManager"><description>The AcePointUxManager is an agent on AcePointDeviceExt and AcePointFolder.</description></class>
<class packageName="com.tridium.ace.ux" name="BAcePointUxManagerJavaScriptMenuAgent"><description>A JavaScript Menu Agent for the AcePointUxManager.</description></class>
</module>
</bajadoc>
