<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference" name="BBacnetDeviceObjectPropertyReference" packageName="javax.baja.bacnet.datatypes" public="true">
<description>
This class represents the BBacnetDeviceObjectPropertyReference sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">6 June 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<implements>
<type class="javax.baja.bacnet.io.PropertyReference"/>
</implements>
<property name="objectId" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="propertyId" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyId</tag>
<tag name="@see">#setPropertyId</tag>
</property>

<property name="propertyArrayIndex" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyArrayIndex</tag>
<tag name="@see">#setPropertyArrayIndex</tag>
</property>

<property name="deviceId" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;deviceId&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceId</tag>
<tag name="@see">#setDeviceId</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference() -->
<constructor name="BBacnetDeviceObjectPropertyReference" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<constructor name="BBacnetDeviceObjectPropertyReference" public="true">
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int) -->
<constructor name="BBacnetDeviceObjectPropertyReference" public="true">
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="propertyId">
<type class="int"/>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int) -->
<constructor name="BBacnetDeviceObjectPropertyReference" public="true">
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="propertyId">
<type class="int"/>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<constructor name="BBacnetDeviceObjectPropertyReference" public="true">
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="propertyId">
<type class="int"/>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
</parameter>
<parameter name="deviceId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.getObjectId() -->
<method name="getObjectId"  public="true">
<description>
Get the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#objectId</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.setObjectId(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="setObjectId"  public="true">
<description>
Set the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#objectId</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.getPropertyId() -->
<method name="getPropertyId"  public="true">
<description>
Get the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#propertyId</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.setPropertyId(int) -->
<method name="setPropertyId"  public="true">
<description>
Set the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#propertyId</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.getPropertyArrayIndex() -->
<method name="getPropertyArrayIndex"  public="true">
<description>
Get the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#propertyArrayIndex</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.setPropertyArrayIndex(int) -->
<method name="setPropertyArrayIndex"  public="true">
<description>
Set the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#propertyArrayIndex</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.getDeviceId() -->
<method name="getDeviceId"  public="true">
<description>
Get the &lt;code&gt;deviceId&lt;/code&gt; property.
</description>
<tag name="@see">#deviceId</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.setDeviceId(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="setDeviceId"  public="true">
<description>
Set the &lt;code&gt;deviceId&lt;/code&gt; property.
</description>
<tag name="@see">#deviceId</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.isPropertyArrayIndexUsed() -->
<method name="isPropertyArrayIndexUsed"  public="true" final="true">
<description/>
<return>
<type class="boolean"/>
<description>
true if the property array index is used.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.isDeviceIdUsed() -->
<method name="isDeviceIdUsed"  public="true" final="true">
<description/>
<return>
<type class="boolean"/>
<description>
true if the deviceId is used.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true" final="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true" final="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
a descriptive string.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.toDebugString() -->
<method name="toDebugString"  public="true" final="true">
<description>
Debug string.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.encodeToString() -->
<method name="encodeToString"  public="true" final="true">
<description>
Write the simple in text format.
</description>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
<description/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.decodeFromString(java.lang.String) -->
<method name="decodeFromString"  public="true" final="true">
<description>
Read the simple from text format.&#xa; The parameter s must be in the form:&#xa; &lt;p&gt;&#xa; objectType_instanceNumber_propertyId_propertyArrayIndex_deviceObjectType_deviceInstance&#xa; Example: 0_0_85_8_10 (AI0, PV in Dev10)&#xa; Example: 1_3_87_10 (AO3, PA[10] in local device)&#xa; Example: 1_3_87_10_8_10 (AO3, PA[10] in Dev10)
</description>
<parameter name="s">
<type class="java.lang.String"/>
<description/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.fromString(java.lang.String) -->
<method name="fromString"  public="true" static="true" final="true">
<description/>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.propertyId -->
<field name="propertyId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyId</tag>
<tag name="@see">#setPropertyId</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.propertyArrayIndex -->
<field name="propertyArrayIndex"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyArrayIndex</tag>
<tag name="@see">#setPropertyArrayIndex</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.deviceId -->
<field name="deviceId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;deviceId&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceId</tag>
<tag name="@see">#setDeviceId</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.MAX_ENCODED_SIZE -->
<field name="MAX_ENCODED_SIZE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.OBJECT_ID_TAG -->
<field name="OBJECT_ID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
BBacnetDeviceObjectPropertyReference Asn Context Tags&#xa; See Bacnet Clause 21.
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.PROPERTY_ID_TAG -->
<field name="PROPERTY_ID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.PROPERTY_ARRAY_INDEX_TAG -->
<field name="PROPERTY_ARRAY_INDEX_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference.DEVICE_ID_TAG -->
<field name="DEVICE_ID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
