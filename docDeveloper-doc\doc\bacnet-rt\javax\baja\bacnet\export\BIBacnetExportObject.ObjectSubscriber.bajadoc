<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BIBacnetExportObject$ObjectSubscriber" name="BIBacnetExportObject.ObjectSubscriber" packageName="javax.baja.bacnet.export" public="true" static="true" innerClass="true">
<description/>
<extends>
<type class="javax.baja.sys.Subscriber"/>
</extends>
<!-- javax.baja.bacnet.export.BIBacnetExportObject.ObjectSubscriber() -->
<constructor name="ObjectSubscriber" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.BIBacnetExportObject.ObjectSubscriber.subscribe(javax.baja.bacnet.export.BIBacnetExportObject, javax.baja.sys.BComponent) -->
<method name="subscribe"  public="true">
<description/>
<parameter name="export">
<type class="javax.baja.bacnet.export.BIBacnetExportObject"/>
</parameter>
<parameter name="src">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BIBacnetExportObject.ObjectSubscriber.unsubscribe(javax.baja.bacnet.export.BIBacnetExportObject, javax.baja.sys.BComponent) -->
<method name="unsubscribe"  public="true">
<description/>
<parameter name="export">
<type class="javax.baja.bacnet.export.BIBacnetExportObject"/>
</parameter>
<parameter name="src">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BIBacnetExportObject.ObjectSubscriber.event(javax.baja.sys.BComponentEvent) -->
<method name="event"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="event">
<type class="javax.baja.sys.BComponentEvent"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
