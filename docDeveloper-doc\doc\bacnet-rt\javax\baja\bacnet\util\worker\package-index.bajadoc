<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="javax.baja.bacnet.util.worker">
<description/>
<class packageName="javax.baja.bacnet.util.worker" name="BBacnetAddressWorkerPool"><description>BBacnetAddressWorkerPool balances incoming work across a&#xa; pool of thread pools using the BBacnetAddress hashcode() method.</description></class>
<class packageName="javax.baja.bacnet.util.worker" name="BBacnetWorkerPool"><description>BBacnetWorkerPool is a thin wrapper around&#xa; a ThreadPoolWorker, that can be added as a child&#xa; to any IWorkerPoolAware BComponent (e.g.</description></class>
<class packageName="javax.baja.bacnet.util.worker" name="IBacnetAddress" category="interface"><description>The IBacnetAddress interface allows&#xa; a WorkerPool to distribute work&#xa; across a worker pool based on BBacnetAddress.</description></class>
<class packageName="javax.baja.bacnet.util.worker" name="IWorkerPool" category="interface"><description>IBacnetWorker provides a mechanism inject a customized&#xa; worker implementation to the BBacnetServerLayer.</description></class>
<class packageName="javax.baja.bacnet.util.worker" name="IWorkerPoolAware" category="interface"><description>IWorkerAware is an interface&#xa; to allow services to have child&#xa; IBacnetWorkers dynamically added / removed.</description></class>
</package>
</bajadoc>
