<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.BHost" name="BHost" packageName="javax.baja.naming" public="true" abstract="true">
<description>
BHost is the navigation node used to organize navigation &#xa; by hosts. Hosts represent a physical box. The BLocalHost.INSTANCE singleton&#xa; is used to represent the local machine. BHosts default to be a child of the&#xa; first level of the navigation tree mounted directly under BNavRoot.INSTANCE.&#xa; Starting in Niagara 4.13, they can also live inside a BNavFolder for better&#xa; organization of the main NavTree.
</description>
<tag name="@author"><PERSON> on 14 Jan 03</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.nav.BNavContainer"/>
</extends>
<!-- javax.baja.naming.BHost(java.lang.String) -->
<constructor name="BHost" protected="true">
<parameter name="hostname">
<type class="java.lang.String"/>
</parameter>
<description>
Constructor with the hostname.
</description>
</constructor>

<!-- javax.baja.naming.BHost.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.naming.BHost.getHost(java.lang.String) -->
<method name="getHost"  public="true" static="true">
<description>
Get a BHost by its hostname.  Hostname is case &#xa; insensitive.  Return null if no matching hostname &#xa; is found.
</description>
<parameter name="hostname">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.naming.BHost"/>
</return>
</method>

<!-- javax.baja.naming.BHost.getAllHosts() -->
<method name="getAllHosts"  public="true" static="true">
<description>
Get all the hosts which is BLocalHost.INSTANCE + getRemoteHosts().
</description>
<return>
<type class="javax.baja.naming.BHost" dimension="1"/>
</return>
</method>

<!-- javax.baja.naming.BHost.getRemoteHosts() -->
<method name="getRemoteHosts"  public="true" static="true">
<description>
Get the remote hosts.  This is all the mounted &#xa; hosts exclusing the BLocalHost.INSTANCE.
</description>
<return>
<type class="javax.baja.naming.BHost" dimension="1"/>
</return>
</method>

<!-- javax.baja.naming.BHost.mount(javax.baja.naming.BHost) -->
<method name="mount"  public="true" static="true">
<description>
Mount a host into the nav tree under BNavRoot.INSTANCE and &#xa; add it to the host cache by hostname.
</description>
<parameter name="host">
<type class="javax.baja.naming.BHost"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.BHost.unmount(javax.baja.naming.BHost) -->
<method name="unmount"  public="true" static="true">
<description>
Unmount a host from the cache and from its Nav Parent.
</description>
<parameter name="host">
<type class="javax.baja.naming.BHost"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.BHost.move(javax.baja.naming.BHost, javax.baja.nav.BNavContainer) -->
<method name="move"  public="true" static="true">
<description>
Move a host to a new Nav Parent. In workbench, this relationship&#xa; will be persisted to the navTree xml.
</description>
<tag name="@since">Niagara 4.13</tag>
<parameter name="host">
<type class="javax.baja.naming.BHost"/>
</parameter>
<parameter name="newParent">
<type class="javax.baja.nav.BNavContainer"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.BHost.getHostname() -->
<method name="getHostname"  public="true">
<description>
Get the hostname.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.BHost.setNavDisplayName(java.lang.String) -->
<method name="setNavDisplayName"  public="true">
<description>
Set a Display Name for the host as viewed in the Nav Tree.&#xa; Exiting the workbench will save this to the navTree xml file.
</description>
<tag name="@since">Niagara 4.8</tag>
<parameter name="navDisplayName">
<type class="java.lang.String"/>
<description>
A string or a BFormat string
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.BHost.getNavDisplayName(javax.baja.sys.Context) -->
<method name="getNavDisplayName"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the nav display name set by the user or the default name
</description>
<tag name="@since">Niagara 4.8</tag>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description/>
</parameter>
<return>
<type class="java.lang.String"/>
<description/>
</return>
</method>

<!-- javax.baja.naming.BHost.getDefaultNavDisplayName(javax.baja.sys.Context) -->
<method name="getDefaultNavDisplayName"  public="true">
<description>
A default display name to show in the nav tree for a host
</description>
<tag name="@since">Niagara 4.8</tag>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
String - Default display name to appear on the nav tree
</description>
</return>
</method>

<!-- javax.baja.naming.BHost.getNavDisplayFormat() -->
<method name="getNavDisplayFormat"  public="true">
<description>
Get the BFormat backed by the nav display name for this host
</description>
<tag name="@since">Niagara 4.8</tag>
<return>
<type class="java.lang.String"/>
<description>
The nav display name BFormat string
</description>
</return>
</method>

<!-- javax.baja.naming.BHost.getStationName() -->
<method name="getStationName"  public="true">
<description>
Get the station name of the first station connected to this host
</description>
<tag name="@since">Niagara 4.8</tag>
<return>
<type class="java.lang.String"/>
<description>
The station name string or return null
</description>
</return>
</method>

<!-- javax.baja.naming.BHost.getChildSessions() -->
<method name="getChildSessions"  public="true">
<description>
Get all the children sessions mounted under this host.
</description>
<return>
<type class="javax.baja.naming.BISession" dimension="1"/>
</return>
</method>

<!-- javax.baja.naming.BHost.openSocket(int) -->
<method name="openSocket"  public="true" abstract="true">
<description>
Open a TCP socket to this host on the specified port.
</description>
<parameter name="port">
<type class="int"/>
</parameter>
<return>
<type class="java.net.Socket"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.naming.BHost.openSocket(int, int) -->
<method name="openSocket"  public="true">
<description>
Open a TCP socket to this host on the specified port.  Subclasses&#xa; should override this method with implementation specific timeout&#xa; behavior.
</description>
<tag name="@since">Niagara 3.6</tag>
<parameter name="port">
<type class="int"/>
</parameter>
<parameter name="timeout">
<type class="int"/>
<description>
in milliseconds
</description>
</parameter>
<return>
<type class="java.net.Socket"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.naming.BHost.openSocket(int, javax.net.SocketFactory) -->
<method name="openSocket"  public="true" abstract="true">
<description>
Open a TCP socket to this host on the specified port&#xa; using the provided socket factory.
</description>
<tag name="@since">Niagara 3.7</tag>
<parameter name="port">
<type class="int"/>
</parameter>
<parameter name="socketFactory">
<type class="javax.net.SocketFactory"/>
</parameter>
<return>
<type class="java.net.Socket"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.naming.BHost.openSocket(int, javax.net.SocketFactory, int) -->
<method name="openSocket"  public="true" abstract="true">
<description>
Open a TCP socket to this host on the specified port.  Subclasses&#xa; should override this method with implementation specific timeout&#xa; behavior.
</description>
<tag name="@since">Niagara 3.7</tag>
<parameter name="port">
<type class="int"/>
</parameter>
<parameter name="socketFactory">
<type class="javax.net.SocketFactory"/>
</parameter>
<parameter name="timeout">
<type class="int"/>
<description>
in milliseconds
</description>
</parameter>
<return>
<type class="java.net.Socket"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.naming.BHost.openDatagramSocket(int) -->
<method name="openDatagramSocket"  public="true" abstract="true">
<description>
Open a UDP datagram socket to this host on the specified port.
</description>
<parameter name="port">
<type class="int"/>
</parameter>
<return>
<type class="java.net.DatagramSocket"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.naming.BHost.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return hostname.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.BHost.isConnected() -->
<method name="isConnected"  public="true">
<description>
The default returns &lt;code&gt;getConnectedSessionCount() &amp;gt; 0&lt;/code&gt;
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.BHost.getConnectedSessionCount() -->
<method name="getConnectedSessionCount"  public="true">
<description>
Get the number of sessions which currently return&#xa; true for &lt;code&gt;BISession.isConnected()&lt;/code&gt;.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.naming.BHost.disconnect() -->
<method name="disconnect"  public="true" final="true">
<description>
Disconnect this host:&#xa; &lt;ul&gt;&#xa; &lt;li&gt;disconnectAllSessions()&lt;/li&gt;&#xa; &lt;li&gt;doDisconnect()&lt;/li&gt;&#xa; &lt;/ul&gt;
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.BHost.doDisconnect() -->
<method name="doDisconnect"  protected="true">
<description>
Subclass hook for disconnect.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.BHost.disconnectAllSessions() -->
<method name="disconnectAllSessions"  public="true">
<description>
Call &lt;code&gt;disconnect&lt;/code&gt; all the child sessions.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.BHost.close() -->
<method name="close"  public="true" final="true">
<description>
Close this host:&#xa; &lt;ul&gt;&#xa; &lt;li&gt;closeAllSessions()&lt;/li&gt;&#xa; &lt;li&gt;doClose()&lt;/li&gt;&#xa; &lt;li&gt;unmount(this)&lt;/li&gt;&#xa; &lt;/ul&gt;
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.BHost.doClose() -->
<method name="doClose"  protected="true">
<description>
Subclass hook for close.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.BHost.closeAllSessions() -->
<method name="closeAllSessions"  public="true">
<description>
Call &lt;code&gt;close&lt;/code&gt; all the child sessions.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.BHost.getAbsoluteOrd() -->
<method name="getAbsoluteOrd"  public="true" abstract="true">
<description>
Get host&#x27;s ord.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.naming.BHost.getNavOrd() -->
<method name="getNavOrd"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getAbsoluteOrd()&lt;/code&gt;
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.naming.BHost.addNavChild(javax.baja.nav.BINavNode) -->
<method name="addNavChild"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="child">
<type class="javax.baja.nav.BINavNode"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.BHost.removeNavChild(javax.baja.nav.BINavNode) -->
<method name="removeNavChild"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="child">
<type class="javax.baja.nav.BINavNode"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.BHost.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the icon.
</description>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.naming.BHost.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.naming.BHost.DEFAULT_NAVDISPLAY_FORMAT -->
<field name="DEFAULT_NAVDISPLAY_FORMAT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
The BFormat compatible string that formats to the default name of a BHost&#xa; as seen in the nav tree.
</description>
<tag name="@since">Niagara 4.8</tag>
</field>

</class>
</bajadoc>
