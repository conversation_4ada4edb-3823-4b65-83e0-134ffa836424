<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.virtual.BLocalBacnetVirtualObject" name="BLocalBacnetVirtualObject" packageName="javax.baja.bacnet.virtual" public="true">
<description/>
<extends>
<type class="javax.baja.bacnet.virtual.BBacnetVirtualObject"/>
</extends>
<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualObject() -->
<constructor name="BLocalBacnetVirtualObject" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualObject(javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway, java.lang.String) -->
<constructor name="BLocalBacnetVirtualObject" public="true">
<parameter name="lgw">
<type class="javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway"/>
</parameter>
<parameter name="virtualPathName">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualObject(javax.baja.bacnet.export.BIBacnetExportObject) -->
<constructor name="BLocalBacnetVirtualObject" public="true">
<parameter name="o">
<type class="javax.baja.bacnet.export.BIBacnetExportObject"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualObject.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualObject.isChildLegal(javax.baja.sys.BComponent) -->
<method name="isChildLegal"  public="true">
<description/>
<parameter name="child">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualObject.device() -->
<method name="device"  public="true">
<description/>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualObject.discoverFacets() -->
<method name="discoverFacets"  protected="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualObject.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
