<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.io.ValueDocEncoder" name="ValueDocEncoder" packageName="javax.baja.io" public="true">
<description>
ValueDocEncoder is used to encode a BValue tree to a document&#xa; &lt;p&gt;&#xa; By default, the format can be BOG (XML) or BSON (JSON) but&#xa; can be extended further by implementing IEncoderPlugin&#xa; &lt;p&gt;&#xa; This class is designed to replace the existing BogEncoder that is&#xa; solely XML based&#xa; &lt;p&gt;&#xa; Note, that care has been taken to ensure that exactly the same XML produced&#xa; by <PERSON>gEncoder is also produced by this class since we don&#x27;t want unreadable&#xa; or larger BOG files impacting customer&#x27;s file systems
</description>
<tag name="@see">IEncoderPlugin</tag>
<tag name="@see">ValueDocDecoder</tag>
<tag name="@author">Gareth Johnson</tag>
<tag name="@creation">07 Jan 11</tag>
<tag name="@since">Niagara 3.7</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="java.lang.AutoCloseable"/>
</implements>
<!-- javax.baja.io.ValueDocEncoder(javax.baja.io.ValueDocEncoder.IEncoderPlugin) -->
<constructor name="ValueDocEncoder" public="true">
<parameter name="plugin">
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</parameter>
<throws>
<type class="java.io.IOException"/>
</throws>
<description>
Construct encoder with plugin and encodeTransients set to false.
</description>
</constructor>

<!-- javax.baja.io.ValueDocEncoder(javax.baja.io.ValueDocEncoder.IEncoderPlugin, javax.baja.sys.Context) -->
<constructor name="ValueDocEncoder" public="true">
<parameter name="plugin">
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<throws>
<type class="java.io.IOException"/>
</throws>
<description>
Construct encoder with plugin and encodeTransients set to false.
</description>
</constructor>

<!-- javax.baja.io.ValueDocEncoder(java.io.File) -->
<constructor name="ValueDocEncoder" public="true">
<parameter name="file">
<type class="java.io.File"/>
</parameter>
<throws>
<type class="java.io.IOException"/>
</throws>
<description>
Construct a BOG XML encoder with file and encodeTransients set to false.
</description>
</constructor>

<!-- javax.baja.io.ValueDocEncoder(java.io.OutputStream) -->
<constructor name="ValueDocEncoder" public="true">
<parameter name="out">
<type class="java.io.OutputStream"/>
</parameter>
<throws>
<type class="java.io.IOException"/>
</throws>
<description>
Construct a BOG XML encoder with output stream and encodeTransients set to false.
</description>
</constructor>

<!-- javax.baja.io.ValueDocEncoder(java.io.File, javax.baja.sys.Context) -->
<constructor name="ValueDocEncoder" public="true">
<parameter name="file">
<type class="java.io.File"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<throws>
<type class="java.io.IOException"/>
</throws>
<description>
Construct a BOG XML encoder with file and encodeTransients set to false.
</description>
</constructor>

<!-- javax.baja.io.ValueDocEncoder(java.io.OutputStream, javax.baja.sys.Context) -->
<constructor name="ValueDocEncoder" public="true">
<parameter name="out">
<type class="java.io.OutputStream"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<throws>
<type class="java.io.IOException"/>
</throws>
<description>
Construct a BOG XML encoder with output stream and encodeTransients set to false.
</description>
</constructor>

<!-- javax.baja.io.ValueDocEncoder.marshal(javax.baja.sys.BValue) -->
<method name="marshal"  public="true" static="true">
<description>
Marshal the specified value into an XML String.&#xa; This XML is not a full document - see encode().
</description>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.marshal(javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="marshal"  public="true" static="true">
<description>
Marshal the specified value into an XML String.&#xa; This XML is not a full document - see encode().
</description>
<tag name="@since">Niagara 4.0</tag>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.getContext() -->
<method name="getContext"  public="true">
<description>
Get the context passed to the constructor.
</description>
<return>
<type class="javax.baja.sys.Context"/>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.getMergedContext(javax.baja.sys.BComplex, javax.baja.sys.Slot, javax.baja.sys.Context) -->
<method name="getMergedContext"  public="true" static="true">
<description>
Return a merged Context from the parent and slot.
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComplex"/>
</parameter>
<parameter name="slot">
<type class="javax.baja.sys.Slot"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.sys.Context"/>
<description>
Context
</description>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.isSyncEncoder() -->
<method name="isSyncEncoder"  public="true">
<description>
Is this a SyncEncoder?
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.isEncodeTransients() -->
<method name="isEncodeTransients"  public="true">
<description>
Is the encode transients flag set.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.setEncodeTransients(boolean) -->
<method name="setEncodeTransients"  public="true">
<description>
Set the encode transients flag.  This flag is used to&#xa; control whether all properties are serialized, or only&#xa; non-transient properties.
</description>
<parameter name="encodeTransients">
<type class="boolean"/>
</parameter>
<return>
<type class="boolean"/>
<description>
the old value of encode transients
</description>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.isEncodeComments() -->
<method name="isEncodeComments"  public="true">
<description>
Is the encode comments flag set.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.setEncodeComments(boolean) -->
<method name="setEncodeComments"  public="true">
<description>
Set the encode encodeComments flag.   If set to true, then&#xa; comments are included to help navigate its structure.
</description>
<parameter name="encodeComments">
<type class="boolean"/>
</parameter>
<return>
<type class="boolean"/>
<description>
the old value of encode transients
</description>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.getPlugin() -->
<method name="getPlugin"  public="true" final="true">
<description/>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
<description>
the encoder plug-in
</description>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.getUnhandledEncodingExceptions() -->
<method name="getUnhandledEncodingExceptions"  public="true">
<description>
Return a list of exceptions encountered during the most recent encoding
</description>
<tag name="@since">Niagara 4.0</tag>
<return>
<parameterizedType class="java.util.stream.Stream">
<args>
<type class="java.lang.Exception"/>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.encodeDocument(javax.baja.sys.BValue) -->
<method name="encodeDocument"  public="true">
<description>
Encode a complete document which serializes the&#xa; value.  The generated document includes a prolog and&#xa; the document start and end tags.  See encode()&#xa; for details about the encoding of the value.
</description>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.encode(javax.baja.sys.BValue) -->
<method name="encode"  public="true">
<description>
Convenience for &lt;code&gt;encode(null, value, Integer.MAX_VALUE)&lt;/code&gt;.
</description>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.encode(java.lang.String, javax.baja.sys.BValue, int) -->
<method name="encode"  public="true">
<description>
Encode the specified value to the document stream.  This&#xa; method does not generate a complete document, but&#xa; rather only a single element.  The depth parameter&#xa; is used to specify how deep to serialize the BComponent&#xa; tree.  A value of zero means component itself, one is&#xa; children components, two is grandchildren components, etc.&#xa; Simples and Structs are always serialized in their&#xa; entirety.&#xa; &lt;p&gt;&#xa; If the encoder&#x27;s context contains a valid user, then&#xa; security permissions are applied during encoding.
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="depth">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.flush() -->
<method name="flush"  public="true" final="true">
<description>
Flush the encoder
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.close() -->
<method name="close"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Close the encoder
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.start(java.lang.String) -->
<method name="start"  public="true" final="true">
<description>
Start an element
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.startArray(java.lang.String) -->
<method name="startArray"  public="true" final="true">
<description>
Start an array (used in JSON encoding)
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.endArray() -->
<method name="endArray"  public="true" final="true">
<description>
End an array (used in JSON encoding)
</description>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.end() -->
<method name="end"  public="true" final="true">
<description>
End an element
</description>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.end(java.lang.String) -->
<method name="end"  public="true" final="true">
<description>
End an element with a name
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.endAttr() -->
<method name="endAttr"  public="true" final="true">
<description>
End the list of attributes (used in XML encoding)
</description>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.key(java.lang.String) -->
<method name="key"  public="true" final="true">
<description>
Create a key for a value (used in JSON encoding)
</description>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.attr(java.lang.String, boolean) -->
<method name="attr"  public="true" final="true">
<description>
Create a boolean key/value pair attribute
</description>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<parameter name="val">
<type class="boolean"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.attr(java.lang.String, double) -->
<method name="attr"  public="true" final="true">
<description>
Create a double key/value pair attribute
</description>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<parameter name="val">
<type class="double"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.attr(java.lang.String, java.lang.String) -->
<method name="attr"  public="true" final="true">
<description>
Create a String key/value pair attribute
</description>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<parameter name="str">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.attrSafe(java.lang.String, java.lang.String) -->
<method name="attrSafe"  public="true" final="true">
<description>
Create a safe key/value pair attribute
</description>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<parameter name="str">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.comment(java.lang.String) -->
<method name="comment"  public="true" final="true">
<description>
Create a comment (used in XML encoding)
</description>
<parameter name="text">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.incrementIndent() -->
<method name="incrementIndent"  public="true" final="true">
<description>
Increment the current indent count (used in XML encoding)
</description>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.decrementIndent() -->
<method name="decrementIndent"  public="true" final="true">
<description>
Decrement the current indent count (used in XML encoding)
</description>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.indent() -->
<method name="indent"  public="true" final="true">
<description>
Indent according to the current indent count (used in XML encoding)
</description>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.getIndent() -->
<method name="getIndent"  public="true" final="true">
<description/>
<return>
<type class="int"/>
<description>
the current indent count
</description>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.newLine() -->
<method name="newLine"  public="true" final="true">
<description>
Create new line (used in XML encoding)
</description>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.isZipped() -->
<method name="isZipped"  public="true">
<description>
Return if this encoder is being used to generate a PKZIP file&#xa; containing the XML document. See &lt;code&gt;setZipped()&lt;/code&gt;&#xa; &lt;p&gt;&#xa; This feature applies to XML encoding
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.setZipped(boolean) -->
<method name="setZipped"  public="true">
<description>
If set to true, then the encoder generates a compressed PKZIP&#xa; file with one entry called &#x22;file.xml&#x22;.  This method cannot be&#xa; called once bytes have been written.  Zipped encoders should&#xa; only be used with stand alone files, it should not be used in&#xa; streams mixed with other data.  This feature is used in conjunction&#xa; with XParser, which automatically detects plain text XML versus&#xa; PKZIP documents.&#xa; &lt;p&gt;&#xa; This feature applies to XML encoding
</description>
<parameter name="zipped">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.encodingComponent(javax.baja.sys.BComponent) -->
<method name="encodingComponent"  protected="true">
<description>
This callback is made after encoding a BComponent, but&#xa; before the end tag is written.  It gives a subclass a&#xa; chance to encode additional information.  Subclasses&#xa; should not use any element names which would collide&#xa; with the standard encoding.
</description>
<parameter name="c">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.encodingComponentStub(javax.baja.sys.BComponent) -->
<method name="encodingComponentStub"  protected="true">
<description>
This callback is made after encoding a BComponent&#xa; start element for a stubbed component.  A stub&#xa; is written when the max depth has been reached&#xa; and this encoder is not recursing into the slots&#xa; of the specified component.
</description>
<parameter name="c">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.encodingValue(javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="encodingValue"  protected="true">
<description>
This callback is made just before a value is encoded.
</description>
<parameter name="val">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.encodingSlot(javax.baja.sys.BComplex, javax.baja.sys.Slot) -->
<method name="encodingSlot"  protected="true">
<description>
This callback is made when a Slot is encoded.
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComplex"/>
</parameter>
<parameter name="slot">
<type class="javax.baja.sys.Slot"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.encodingFacets(javax.baja.sys.BFacets) -->
<method name="encodingFacets"  protected="true">
<description>
This callback is made when facets are encoded.
</description>
<parameter name="facets">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.encodePropertyValue(javax.baja.sys.BComplex, javax.baja.sys.Property, int, javax.baja.security.BPermissions, javax.baja.sys.Context) -->
<method name="encodePropertyValue"  protected="true">
<description>
This callback is made to allow the subclass to encode the slot (and&#xa; children if any). Returning true tells the caller that the subclass has&#xa; encoded the children and that the caller should not do so.
</description>
<tag name="@since">Niagara 4.0</tag>
<parameter name="parent">
<type class="javax.baja.sys.BComplex"/>
</parameter>
<parameter name="prop">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="depth">
<type class="int"/>
</parameter>
<parameter name="permissions">
<type class="javax.baja.security.BPermissions"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if encoding the value has been handled by the subclass
</description>
</return>
<throws>
<type class="java.io.IOException"/>
<description/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.doClose() -->
<method name="doClose"  protected="true">
<description>
This callback is made after an encoder is closed, to allow&#xa; subclasses to release implementation specific resources.
</description>
<tag name="@since">Niagara 4.10u3</tag>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
<description/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.canSkipEncodingProperty(javax.baja.sys.BComplex, javax.baja.sys.Property, javax.baja.sys.BValue, javax.baja.security.BPermissions) -->
<method name="canSkipEncodingProperty"  protected="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unused&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Return true if encoding the property can be skipped.
</description>
<parameter name="complex">
<type class="javax.baja.sys.BComplex"/>
<description>
The complex the property is a member of.
</description>
</parameter>
<parameter name="prop">
<type class="javax.baja.sys.Property"/>
<description>
The property to test.
</description>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
<description>
The value of the property.
</description>
</parameter>
<parameter name="permissions">
<type class="javax.baja.security.BPermissions"/>
<description>
The security permissions.
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
Returns true if the property should be skipped.
</description>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.getPermissionsFor(javax.baja.sys.BComponent) -->
<method name="getPermissionsFor"  public="true">
<description>
Given a BComponent, return the set of permissions&#xa; available based on the current context.  If context&#xa; doesn&#x27;t contain a user, then this method returns&#xa; &lt;code&gt;BPermissions.all&lt;/code&gt;.
</description>
<parameter name="c">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="javax.baja.security.BPermissions"/>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.encodeSimple(javax.baja.sys.BSimple) -->
<method name="encodeSimple"  protected="true">
<description/>
<parameter name="simple">
<type class="javax.baja.sys.BSimple"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.isTypeBlackListed(javax.baja.sys.Type) -->
<method name="isTypeBlackListed"  public="true">
<description>
Return true if the Type is blacklisted.&#xa; &lt;p&gt;&#xa; Currently this is limited to Simple Types.
</description>
<parameter name="type">
<type class="javax.baja.sys.Type"/>
<description>
the Type to check.
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the value is black listed and shouldn&#x27;t be encoded.
</description>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.BOG_VERSION_1 -->
<field name="BOG_VERSION_1"  public="true" static="true" final="true">
<type class="javax.baja.util.Version"/>
<description/>
</field>

<!-- javax.baja.io.ValueDocEncoder.BOG_VERSION_4 -->
<field name="BOG_VERSION_4"  public="true" static="true" final="true">
<type class="javax.baja.util.Version"/>
<description/>
</field>

<!-- javax.baja.io.ValueDocEncoder.plugin -->
<field name="plugin"  protected="true" final="true">
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
<description/>
</field>

</class>
</bajadoc>
