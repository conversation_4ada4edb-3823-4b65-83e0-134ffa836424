<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.network.BNetworkPort" name="BNetworkPort" packageName="com.tridium.bacnet.stack.network" public="true">
<description>
Handles sending and receiving messages to and from the&#xa; link layer.  A router will have at least two BNetworkPorts&#xa; defined, one for each network to which it is connected.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">19 Apr 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="com.tridium.bacnet.stack.link.LinkListener"/>
</implements>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<implements>
<type class="com.tridium.bacnet.stack.network.wiretap.WiretapAware"/>
</implements>
<property name="networkNumber" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;networkNumber&lt;/code&gt; property.
</description>
<tag name="@see">#getNetworkNumber</tag>
<tag name="@see">#setNetworkNumber</tag>
</property>

<property name="link" flags="">
<type class="com.tridium.bacnet.stack.link.BBacnetLinkLayer"/>
<description>
Slot for the &lt;code&gt;link&lt;/code&gt; property.
</description>
<tag name="@see">#getLink</tag>
<tag name="@see">#setLink</tag>
</property>

<property name="status" flags="trd">
<type class="javax.baja.status.BStatus"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.&#xa; the status for this port.
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</property>

<property name="faultCause" flags="rt">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.&#xa; Provides a description of a fault with this port.
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</property>

<property name="multiPoll" flags="h">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;multiPoll&lt;/code&gt; property.
</description>
<tag name="@see">#getMultiPoll</tag>
<tag name="@see">#setMultiPoll</tag>
</property>

<property name="pollService" flags="">
<type class="com.tridium.bacnet.stack.BBacnetPoll"/>
<description>
Slot for the &lt;code&gt;pollService&lt;/code&gt; property.
</description>
<tag name="@see">#getPollService</tag>
<tag name="@see">#setPollService</tag>
</property>

<property name="maxDevices" flags="r">
<type class="int"/>
<description>
Slot for the &lt;code&gt;maxDevices&lt;/code&gt; property.
</description>
<tag name="@see">#getMaxDevices</tag>
<tag name="@see">#setMaxDevices</tag>
</property>

<property name="enabled" flags="d">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;enabled&lt;/code&gt; property.
</description>
<tag name="@see">#getEnabled</tag>
<tag name="@see">#setEnabled</tag>
</property>

<property name="portId" flags="rd">
<type class="int"/>
<description>
Slot for the &lt;code&gt;portId&lt;/code&gt; property.
</description>
<tag name="@see">#getPortId</tag>
<tag name="@see">#setPortId</tag>
</property>

<property name="portInfo" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;portInfo&lt;/code&gt; property.
</description>
<tag name="@see">#getPortInfo</tag>
<tag name="@see">#setPortInfo</tag>
</property>

<action name="enable" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;enable&lt;/code&gt; action.
</description>
<tag name="@see">#enable()</tag>
</action>

<action name="disable" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;disable&lt;/code&gt; action.
</description>
<tag name="@see">#disable()</tag>
</action>

</class>
</bajadoc>
