<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.io.RejectException" name="RejectException" packageName="javax.baja.bacnet.io" public="true" category="exception">
<description>
RejectExceptions are thrown when an error is encountered that&#xa; should result in a transaction being rejected.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 2$ $Date: 11/29/01 1:24:01 PM$</tag>
<tag name="@creation">31 Jul 00</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.BacnetException"/>
</extends>
<!-- javax.baja.bacnet.io.RejectException(int) -->
<constructor name="RejectException" public="true">
<parameter name="rejectReason">
<type class="int"/>
<description>
the Bacnet Reject Reason associated with this exception.
</description>
</parameter>
<description>
Constructor
</description>
</constructor>

<!-- javax.baja.bacnet.io.RejectException.getRejectReason() -->
<method name="getRejectReason"  public="true">
<description>
Returns the BBacnetRejectReason
</description>
<return>
<type class="int"/>
<description>
the Bacnet Reject Reason associated with this exception.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.RejectException.toString() -->
<method name="toString"  public="true">
<description>
To String.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

</class>
</bajadoc>
