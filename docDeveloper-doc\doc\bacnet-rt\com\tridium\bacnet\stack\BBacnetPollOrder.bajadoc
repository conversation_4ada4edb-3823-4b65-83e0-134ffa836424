<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.BBacnetPollOrder" name="BBacnetPollOrder" packageName="com.tridium.bacnet.stack" public="true" abstract="true">
<description>
BBacnetPollOrder provides an override hook allowing&#xa; different strategies of poll ordering.&#xa; &lt;p&gt;&#xa; note: this ordering only determines the sequence of poll lists already&#xa; due to be polled, and does not affect poll frequency.&#xa; &lt;p&gt;&#xa; The implementing component is responsible for returning an collection&#xa; of poll lists that contains all the provided poll lists.&#xa; &lt;p&gt;&#xa; Any BBacnetPollOrder that does not return the exact number of entries&#xa; that were provided may be excluded from future sort operations.
</description>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="enabled" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;enabled&lt;/code&gt; property.
</description>
<tag name="@see">#getEnabled</tag>
<tag name="@see">#setEnabled</tag>
</property>

<property name="faultCause" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</property>

</class>
</bajadoc>
