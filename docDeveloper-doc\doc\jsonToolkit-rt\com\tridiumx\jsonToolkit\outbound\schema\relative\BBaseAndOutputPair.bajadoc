<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseAndOutputPair" name="BBaseAndOutputPair" packageName="com.tridiumx.jsonToolkit.outbound.schema.relative" public="true">
<description>
Pojo which holds a pair of associated schema generations values:&#xa;&#xa; the output string&#xa; the base item which the schema was generated against
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseAndOutputPair() -->
<constructor name="BBaseAndOutputPair" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseAndOutputPair(javax.baja.sys.BComplex, java.lang.String) -->
<constructor name="BBaseAndOutputPair" public="true">
<parameter name="baseItem">
<type class="javax.baja.sys.BComplex"/>
</parameter>
<parameter name="schemaOutput">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseAndOutputPair.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseAndOutputPair.getBaseItem() -->
<method name="getBaseItem"  public="true">
<description/>
<return>
<type class="javax.baja.sys.BComplex"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseAndOutputPair.getSchemaOutput() -->
<method name="getSchemaOutput"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseAndOutputPair.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseAndOutputPair.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
