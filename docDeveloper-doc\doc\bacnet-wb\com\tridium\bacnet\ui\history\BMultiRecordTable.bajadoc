<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="wb" qualifiedName="com.tridium.bacnet.ui.history.BMultiRecordTable" name="BMultiRecordTable" packageName="com.tridium.bacnet.ui.history" public="true">
<description>
BMultiRecordTable is a table of BacnetTrendRecords from histories&#xa; created by a single BacnetTrendLogMultipleImport.  Each row is&#xa; a single timesstamp.
</description>
<tag name="@author"><PERSON>  original code from BHistoryTable</tag>
<tag name="@creation">15 April 2010</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BObject"/>
</extends>
<implements>
<parameterizedType class="javax.baja.collection.BIRandomAccessTable">
<args>
<type class="com.tridium.bacnet.ui.history.BMultiRecord"/>
</args>
</parameterizedType>
</implements>
</class>
</bajadoc>
