<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>bajaux Module: bajaux/commands/Command</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">bajaux</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-bajaux_commands_Command.html">bajaux/commands/Command</a></li><li><a href="module-bajaux_commands_CommandGroup.html">bajaux/commands/CommandGroup</a></li><li><a href="module-bajaux_commands_ToggleCommand.html">bajaux/commands/ToggleCommand</a></li><li><a href="module-bajaux_commands_ToggleCommandGroup.html">bajaux/commands/ToggleCommandGroup</a></li><li><a href="module-bajaux_container_wb_Clipboard.html">bajaux/container/wb/Clipboard</a></li><li><a href="module-bajaux_container_wb_StringList.html">bajaux/container/wb/StringList</a></li><li><a href="module-bajaux_dragdrop_dragDropUtils.html">bajaux/dragdrop/dragDropUtils</a></li><li><a href="module-bajaux_dragdrop_Envelope.html">bajaux/dragdrop/Envelope</a></li><li><a href="module-bajaux_dragdrop_NavNodeEnvelope.html">bajaux/dragdrop/NavNodeEnvelope</a></li><li><a href="module-bajaux_dragdrop_StringEnvelope.html">bajaux/dragdrop/StringEnvelope</a></li><li><a href="module-bajaux_events.html">bajaux/events</a></li><li><a href="module-bajaux_icon_iconUtils.html">bajaux/icon/iconUtils</a></li><li><a href="module-bajaux_lifecycle_WidgetManager.html">bajaux/lifecycle/WidgetManager</a></li><li><a href="module-bajaux_mixin_batchLoadMixin.html">bajaux/mixin/batchLoadMixin</a></li><li><a href="module-bajaux_mixin_batchSaveMixin.html">bajaux/mixin/batchSaveMixin</a></li><li><a href="module-bajaux_mixin_responsiveMixIn.html">bajaux/mixin/responsiveMixIn</a></li><li><a href="module-bajaux_mixin_subscriberMixIn.html">bajaux/mixin/subscriberMixIn</a></li><li><a href="module-bajaux_Properties.html">bajaux/Properties</a></li><li><a href="module-bajaux_registry_Registry.html">bajaux/registry/Registry</a></li><li><a href="module-bajaux_registry_RegistryEntry.html">bajaux/registry/RegistryEntry</a></li><li><a href="module-bajaux_spandrel.html">bajaux/spandrel</a></li><li><a href="module-bajaux_spandrel_jsx.html">bajaux/spandrel/jsx</a></li><li><a href="module-bajaux_util_CommandButton.html">bajaux/util/CommandButton</a></li><li><a href="module-bajaux_util_CommandButtonGroup.html">bajaux/util/CommandButtonGroup</a></li><li><a href="module-bajaux_util_SaveCommand.html">bajaux/util/SaveCommand</a></li><li><a href="module-bajaux_Validators.html">bajaux/Validators</a></li><li><a href="module-bajaux_Widget.html">bajaux/Widget</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="interfaces.list.html" class="dropdown-toggle" data-toggle="dropdown">Interfaces<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-bajaux_commands_Command-Undoable.html">bajaux/commands/Command~Undoable</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="tutorials.list.html" class="dropdown-toggle" data-toggle="dropdown">Tutorials<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="tutorial-10-mfw-gettingStarted.html">Getting Started - MyFirstWidget</a></li><li><a href="tutorial-20-mfw-modifying.html">Saving Modifications to Station</a></li><li><a href="tutorial-30-mfw-dashboarding.html">Making your Widget Dashboardable</a></li><li><a href="tutorial-40-tipsAndTricks.html">Tips and Tricks</a></li><li><a href="tutorial-50-spandrel.html">Building Composite Widgets With spandrel</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: bajaux/commands/Command</h1>
<section>

<header>
    
        
            
        
    
</header>


<article>
    <div class="container-overview">
    
        

        
            
<hr>
<dt>
    <h4 class="name" id="module:bajaux/commands/Command"><span class="type-signature"></span>new (require("bajaux/commands/Command"))(params [, invokeFunction])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>A Command is essentially an asynchronous function with a nicely formatted<br>
display name attached.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>An Object Literal or the display name for<br>
the Command.</p>
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>displayName</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>The display name format<br>
for the Command. This format will be used in<br>
<code>toDisplayName</code>. This can also be the first argument, in which<br>
case <code>func</code> must be the second.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>func</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>The function this command will execute. The<br>
function can return a promise if it's going to be invoked asynchronously.<br>
As of Niagara 4.11 this can be omitted if creating an undoable function.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>undoable</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_commands_Command-Undoable.html">module:bajaux/commands/Command~Undoable</a></span>
|

<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>As<br>
of Niagara 4.11, any necessary configuration to make this command undoable.<br>
This can be either an <code>undoable</code> directly, or a function that resolves to one.<br>
Please note that an <code>undoable()</code> function itself should not do the actual<br>
work - the <code>redo()</code> function of the returned undoable should do the work.<br>
Note that any asynchronous functions may be declared as synchronous when<br>
passed to the constructor, for simplicity.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>description</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>A description of the<br>
Command. This format will be used in <code>toDescription</code>.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>enabled</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>The enabled state of the Command.<br>
Defaults to <code>true</code>.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>flags</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>The Command flags. Defaults to<br>
<code>Command.flags.ALL</code>.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>icon</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>The Command Icon. This can also be a<br>
String encoding an icon will be created from.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    

        <tr>
            
                <td class="name"><code>invokeFunction</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>the function to invoke, if using the two-argument<br>
constructor</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
        <h5>Example</h5>
        
    <pre class="sunlight-highlight-javascript">new Command(&quot;baja.Format compatible display name&quot;, function () {
  alert(&quot;I&#x27;m a command!&quot;);
});

new Command(&quot;Format compatible display name&quot;, function () {
  return new Promise(function (resolve, reject) {
    setTimeout(function () {
      wobble.foo();
      resolve;
    }, 1000);
  });
});

new Command({
  displayName: &quot;I&#x27;ll be converted to a Format: %lexicon(baja:january)%&quot;,
  description: &quot;I&#x27;ll be converted to a Format too: %lexicon(baja:true)%&quot;,
  func: function () {
    alert(&quot;I&#x27;m a command!&quot;);
  }
});

new Command({
  module: &quot;myModule&quot;, // Create a Command that gets its displayName, description                  
  lex: &quot;myCommand&quot;,   // and icon from a module&#x27;s lexicon.
  func: function () {
    alert(&quot;I&#x27;m a command!&quot;);
  }
});

new Command({
  undoable: () =&gt; {
    return promptUser(&#x27;Are you sure you want to make this change?&#x27;)
      .then((userSaidYes) =&gt; {
        if (!userSaidYes) { return; }

        // redoText/undoText may be strings, getters, or async getters
        // canRedo/canUndo may be booleans, getters, or async getters
        return {
          redo: () =&gt; console.log(&#x27;perform the work of the command&#x27;),
          undo: () =&gt; console.log(&#x27;revert/back out the work of the command&#x27;),
          redoText: () =&gt; &#x27;Text describing what the command will do&#x27;,
          undoText: () =&gt; &#x27;Text describing what undoing the command will do&#x27;,
          canRedo: () =&gt; true, // true if doing the work of the command is allowed
          canUndo: () =&gt; true // true if reverting the work of the command is allowed
        }
      });
});</pre>


    
</dd>

        
    
    </div>

    

    

    

    

    

    
        <h3 class="subsection-title">Members</h3>

        <dl>
            
<hr>
<dt class="name" id=".flags">
    <h4 id=".flags"><span class="type-signature">&lt;static> </span>flags<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Namespace containing numbers for comparing flag bits. C&amp;P'ed directly<br>
from MgrController in workbench module.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".flags.ALL">
    <h4 id=".flags.ALL"><span class="type-signature">&lt;static> </span>flags.ALL<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Match all flags.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".flags.MENU_BAR">
    <h4 id=".flags.MENU_BAR"><span class="type-signature">&lt;static> </span>flags.MENU_BAR<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Makes the command be available in the main menu.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".flags.NONE">
    <h4 id=".flags.NONE"><span class="type-signature">&lt;static> </span>flags.NONE<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Match no flags</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".flags.TOOL_BAR">
    <h4 id=".flags.TOOL_BAR"><span class="type-signature">&lt;static> </span>flags.TOOL_BAR<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Makes the command be available in the main toolbar.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        </dl>
    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id=".isUndoable"><span class="type-signature">&lt;static> </span>isUndoable(undoable)</h4>
    
    
</dt>
<dd>

    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>undoable</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_commands_Command.html">module:bajaux/commands/Command</a></span>
|

<span class="param-type"><a href="module-bajaux_commands_Command-Undoable.html">module:bajaux/commands/Command~Undoable</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.11</li>
		</ul>
	</dd>
	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>true if the given parameter is an undoable Command, or<br>
is an Undoable object</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="defaultNotifyUser"><span class="type-signature"></span>defaultNotifyUser(err [, params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Provides a default way of notifying the user about a Command invocation<br>
failure. Shows a dialog with details about the error.</p>
<p>You might override this at runtime with your own error dialog handler.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>err</code></td>
            

            <td class="type">
            
                
<span class="param-type">Error</span>
|

<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last">
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>messageSummary</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>any additional information you<br>
would like to include in the error dialog</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.12</li>
		</ul>
	</dd>
	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getAccelerator"><span class="type-signature"></span>getAccelerator()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the accelerator for the Command or null if<br>
nothing is defined.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_commands_Command.html#setAccelerator">module:bajaux/commands/Command#setAccelerator</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The accelerator or null if nothing is defined.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Object</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getDescriptionFormat"><span class="type-signature"></span>getDescriptionFormat()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the unformatted description of the command.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getDisplayNameFormat"><span class="type-signature"></span>getDisplayNameFormat()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the format display name of the command.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getFlags"><span class="type-signature"></span>getFlags()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get this command's flags.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getFunction"><span class="type-signature"></span>getFunction()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the raw function associated with this command.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">function</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getIcon"><span class="type-signature"></span>getIcon()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the Command's icon URI</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getId"><span class="type-signature"></span>getId()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return a unique numerical id for the Command.</p>
<p>This is id unique to every Command object created.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="hasFlags"><span class="type-signature"></span>hasFlags(flags)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Check to see if this command's flags match any of the bits of the<br>
input flags.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>flags</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>The flags to check against</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="invoke"><span class="type-signature"></span>invoke()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Invoke the Command. Triggers a <code>bajaux:invokecommand</code> or<br>
<code>bajaux:failcommand</code> event, as appropriate.</p>
<p>Arguments can be passed into <code>invoke()</code> that will be passed into the<br>
function's Command Handler.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>A promise object that will be resolved (or rejected)<br>
once the Command's function handler has finished invoking.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="invokeFromEvent"><span class="type-signature"></span>invokeFromEvent(e)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>If your Command optionally implements this function, then CommandButton<br>
will call it on click instead of simply calling <code>invoke</code>. Use this in case<br>
your Command needs to respond differently based on where on the screen the<br>
user is pointing.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>e</code></td>
            

            <td class="type">
            
                
<span class="param-type">JQuery.Event</span>



            
            </td>

            

            

            <td class="description last"><p>the DOM event triggered by the user's request to<br>
invoke this function</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.11</li>
		</ul>
	</dd>
	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>
|

<span class="param-type">*</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isCommand"><span class="type-signature"></span>isCommand()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Always returns true.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isEnabled"><span class="type-signature"></span>isEnabled()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Gets this command's enabled status.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isLoading"><span class="type-signature"></span>isLoading()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if the Command is still loading.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>true if still loading.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isToggleCommand"><span class="type-signature"></span>isToggleCommand()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Always returns false.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isUndoable"><span class="type-signature"></span>isUndoable()</h4>
    
    
</dt>
<dd>

    

    

    

    
    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.11</li>
		</ul>
	</dd>
	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>true if this command is undoable</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="jq"><span class="type-signature"></span>jq( [jqDom])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>If a jQuery DOM argument is specified, this will set the DOM.<br>
If not specified then no DOM will be set.<br>
This method will always return the jQuery DOM associated with this Command.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>jqDom</code></td>
            

            <td class="type">
            
                
<span class="param-type">JQuery</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>If specified, this will set the jQuery DOM.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>A jQuery DOM object for firing events on.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">JQuery</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="loading"><span class="type-signature"></span>loading()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the loading promise for the Command.</p>
<p>The returned promise will be resolved once the Command<br>
has finished loading.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The promise used for loading a Command.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="merge"><span class="type-signature"></span>merge(cmd)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Attempt to merge this command with another command, and return a new<br>
Command that does both tasks. If the two commands are mutually<br>
incompatible, return a falsy value.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>cmd</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_commands_Command.html">module:bajaux/commands/Command</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_commands_Command.html">module:bajaux/commands/Command</a></span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
        <p class="code-caption">
  Here is an example to show the basic concept. Commands that simply
  add two numbers together can easily be merged together thanks to the
  associative property.
</p>
    
    <pre class="sunlight-highlight-javascript">var AddCommand = function AddCommand(inc) {
  this.$inc = inc;
  Command.call(this, {
    displayName: &#x27;Add &#x27; + inc + &#x27; to the given number&#x27;,
    func: function (num) { return num + inc; }
  });
};
AddCommand.prototype = Object.create(Command.prototype);

AddCommand.prototype.merge = function (cmd) {
  if (cmd instanceof AddCommand) {
    return new AddCommand(this.$inc + cmd.$inc);
  }
};

var addOneCommand = new AddCommand(1),
    addFiveCommand = new AddCommand(5),
    addSixCommand = addOneCommand.merge(addFiveCommand);
addSixCommand.invoke(10)
  .then(function (result) {
    console.log(&#x27;is 16? &#x27;, result === 16);
  });</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="off"><span class="type-signature"></span>off( [event] [, handler])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Unregister a function callback handler for the specified event.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>event</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>The name of the event to unregister.<br>
If name isn't specified, all events for the Command will be unregistered.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>handler</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>The function to unregister. If<br>
not specified, all handlers for the event will be unregistered.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="on"><span class="type-signature"></span>on(event, handler)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Register a function callback handler for the specified event.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>event</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>The event id to register the function for.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>handler</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>The event handler to be called when the event is fired.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setAccelerator"><span class="type-signature"></span>setAccelerator(acc)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set the accelerator information for the Command.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>acc</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>
|

<span class="param-type">String</span>
|

<span class="param-type">Number</span>
|

<span class="param-type">null</span>
|

<span class="param-type">undefined</span>



            
            </td>

            

            

            <td class="description last"><p>The accelerator keyboard information. This can<br>
be a keyCode number, a character (i.e. 'a') or an Object that contains the accelerator information.<br>
If no accelerator should be used the null/undefined should be specified.</p>
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>keyCode</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>The key code of the accelerator. This can be a character<br>
code number or a character (i.e. 'a').</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>ctrl</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p><code>true</code> if the control key needs to be pressed.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>shift</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p><code>true</code> if the shift key needs to be pressed.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>alt</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p><code>true</code> if the alt key needs to be pressed.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>meta</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p><code>true</code> if a meta key needs to be pressed.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-bajaux_commands_Command.html#getAccelerator">module:bajaux/commands/Command#getAccelerator</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setDescriptionFormat"><span class="type-signature"></span>setDescriptionFormat(description)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set the description format of the command. Triggers a<br>
<code>bajaux:changecommand</code> event.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>description</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>the command description - supports baja Format<br>
syntax</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setDisplayNameFormat"><span class="type-signature"></span>setDisplayNameFormat(displayName)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set the display name format of the command. Triggers a<br>
<code>bajaux:changecommand</code> event.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>displayName</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>display name - supports baja Format syntax</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setEnabled"><span class="type-signature"></span>setEnabled(enabled)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Sets this command's enabled status. Triggers a<br>
<code>bajaux:changecommand</code> event.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>enabled</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setFlags"><span class="type-signature"></span>setFlags(flags)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set this command's flags.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>flags</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setFunction"><span class="type-signature"></span>setFunction(func)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set the Command's function handler.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>func</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>The new function handler for the command.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setIcon"><span class="type-signature"></span>setIcon(icon)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Sets the icon for this Command. Triggers a <code>bajaux:changecommand</code> event.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>icon</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>The Command's icon (either a URI or a module:// ORD string)</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="toDescription"><span class="type-signature"></span>toDescription()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Access the Command's description.</p>
<p>In order to access the description, a promise will be returned<br>
that will be resolved once the command has been loaded and<br>
the description has been found.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>Promise to be resolved with the description</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="toDisplayName"><span class="type-signature"></span>toDisplayName()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Access the Command's display name.</p>
<p>In order to access the display name, a promise will be returned<br>
that will be resolved once the command has been loaded and<br>
the display name has been found.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>Promise to be resolved with the display name</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="trigger"><span class="type-signature"></span>trigger(name)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Triggers an event from this Command.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="visit"><span class="type-signature"></span>visit(func)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Visit this Command with the specified function.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>func</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>Will be invoked with this<br>
Command passed in as an argument.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        </dl>
    

    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	bajaux Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:53+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>