<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="wb" qualifiedName="com.tridium.aapup.ui.point.BPupPointManager" name="BPupPointManager" packageName="com.tridium.aapup.ui.point" public="true">
<description>
BPupPointManager uses the BAbstractLearn framework to&#xa; provide a way for the user to create proxy points within&#xa; a BPupDevice.
</description>
<tag name="@author">C<PERSON><PERSON></tag>
<tag name="@creation">7/26/2005 9:46AM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.91</tag>
<extends>
<type class="javax.baja.driver.ui.point.BPointManager"/>
</extends>
<implements>
<type class="com.tridium.aapup.AaPupConst"/>
</implements>
</class>
</bajadoc>
