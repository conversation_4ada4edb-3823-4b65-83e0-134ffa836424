<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="andoverInfinity" runtimeProfile="wb" name="com.tridium.andoverInfinity.ui">
<description/>
<class packageName="com.tridium.andoverInfinity.ui" name="BInfinityBufferLoaderMenuAgent"><description>BInfinityBufferLoaderMenuAgent is an agent to load and save the BVt100&#xa; screen buffer to/from a file on the client machine.</description></class>
<class packageName="com.tridium.andoverInfinity.ui" name="BInfinityBufferLoaderMenuAgent.LoadScreenBufferFromFileCommand"/>
<class packageName="com.tridium.andoverInfinity.ui" name="BInfinityBufferLoaderMenuAgent.SaveScreenBufferToFileCommand"/>
</package>
</bajadoc>
