<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityPolicy" name="BBacnetNetworkSecurityPolicy" packageName="javax.baja.bacnet.datatypes.security" public="true" final="true">
<description>
BBacnetNetworkSecurityPolicy represents the BACnetNetworkSecurityPolicy&#xa; sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="portId" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;portId&lt;/code&gt; property.
</description>
<tag name="@see">#getPortId</tag>
<tag name="@see">#setPortId</tag>
</property>

<property name="securityLevel" flags="">
<type class="javax.baja.bacnet.enums.security.BBacnetSecurityPolicy"/>
<description>
Slot for the &lt;code&gt;securityLevel&lt;/code&gt; property.
</description>
<tag name="@see">#getSecurityLevel</tag>
<tag name="@see">#setSecurityLevel</tag>
</property>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityPolicy() -->
<constructor name="BBacnetNetworkSecurityPolicy" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityPolicy(int, javax.baja.bacnet.enums.security.BBacnetSecurityPolicy) -->
<constructor name="BBacnetNetworkSecurityPolicy" public="true">
<parameter name="portId">
<type class="int"/>
</parameter>
<parameter name="securityLevel">
<type class="javax.baja.bacnet.enums.security.BBacnetSecurityPolicy"/>
</parameter>
<description>
Standard constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityPolicy.getPortId() -->
<method name="getPortId"  public="true">
<description>
Get the &lt;code&gt;portId&lt;/code&gt; property.
</description>
<tag name="@see">#portId</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityPolicy.setPortId(int) -->
<method name="setPortId"  public="true">
<description>
Set the &lt;code&gt;portId&lt;/code&gt; property.
</description>
<tag name="@see">#portId</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityPolicy.getSecurityLevel() -->
<method name="getSecurityLevel"  public="true">
<description>
Get the &lt;code&gt;securityLevel&lt;/code&gt; property.
</description>
<tag name="@see">#securityLevel</tag>
<return>
<type class="javax.baja.bacnet.enums.security.BBacnetSecurityPolicy"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityPolicy.setSecurityLevel(javax.baja.bacnet.enums.security.BBacnetSecurityPolicy) -->
<method name="setSecurityLevel"  public="true">
<description>
Set the &lt;code&gt;securityLevel&lt;/code&gt; property.
</description>
<tag name="@see">#securityLevel</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.security.BBacnetSecurityPolicy"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityPolicy.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityPolicy.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityPolicy.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityPolicy.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityPolicy.portId -->
<field name="portId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;portId&lt;/code&gt; property.
</description>
<tag name="@see">#getPortId</tag>
<tag name="@see">#setPortId</tag>
</field>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityPolicy.securityLevel -->
<field name="securityLevel"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;securityLevel&lt;/code&gt; property.
</description>
<tag name="@see">#getSecurityLevel</tag>
<tag name="@see">#setSecurityLevel</tag>
</field>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityPolicy.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityPolicy.PORT_ID_TAG -->
<field name="PORT_ID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityPolicy.SECURITY_LEVEL_TAG -->
<field name="SECURITY_LEVEL_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
