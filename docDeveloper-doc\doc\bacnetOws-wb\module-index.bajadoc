<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="bacnetOws" runtimeProfile="wb" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>Niagara BACnet OWS Driver</description>
<package name="com.tridium.bacnetOws.ui.device"/>
<class packageName="com.tridium.bacnetOws.ui.device" name="BBacnetOwsDeviceManager"><description>BBacnetOwsDeviceManager is the specialization of BBacnetOwsDeviceManager to&#xa; handle Workstation-specific behavior.</description></class>
</module>
</bajadoc>
