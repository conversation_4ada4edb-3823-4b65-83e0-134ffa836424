<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="wb" qualifiedName="com.tridium.bacnet.ui.point.BBacnetPointManager" name="BBacnetPointManager" packageName="com.tridium.bacnet.ui.point" public="true">
<description>
BBacnetPointManager uses the BAbstractLearn framework to&#xa; provide a way for the user to create proxy points within&#xa; a BBacnetDevice.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">07 Oct 03</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.driver.ui.point.BPointManager"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
</class>
</bajadoc>
