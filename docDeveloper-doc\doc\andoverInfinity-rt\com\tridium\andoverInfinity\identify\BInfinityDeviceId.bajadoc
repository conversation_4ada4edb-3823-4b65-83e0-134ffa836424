<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.identify.BInfinityDeviceId" name="BInfinityDeviceId" packageName="com.tridium.andoverInfinity.identify" public="true">
<description/>
<tag name="@author">cturman</tag>
<extends>
<type class="com.tridium.ddf.identify.BDdfDeviceId"/>
</extends>
<implements>
<type class="com.tridium.ddf.discover.BIDdfDiscoveryLeaf"/>
</implements>
<property name="controllerName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;controllerName&lt;/code&gt; property.&#xa; this is the name of the infinet controller.  It will be&#xa; empty if the point resides on the network controller.&#xa; This will need to be updated whenever the network name&#xa; is edited or changed
</description>
<tag name="@see">#getControllerName</tag>
<tag name="@see">#setControllerName</tag>
</property>

<property name="port" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;port&lt;/code&gt; property.
</description>
<tag name="@see">#getPort</tag>
<tag name="@see">#setPort</tag>
</property>

<property name="model" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;model&lt;/code&gt; property.
</description>
<tag name="@see">#getModel</tag>
<tag name="@see">#setModel</tag>
</property>

<property name="serialNumber" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;serialNumber&lt;/code&gt; property.
</description>
<tag name="@see">#getSerialNumber</tag>
<tag name="@see">#setSerialNumber</tag>
</property>

<property name="id" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#getId</tag>
<tag name="@see">#setId</tag>
</property>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId() -->
<constructor name="BInfinityDeviceId" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.getControllerName() -->
<method name="getControllerName"  public="true">
<description>
Get the &lt;code&gt;controllerName&lt;/code&gt; property.&#xa; this is the name of the infinet controller.  It will be&#xa; empty if the point resides on the network controller.&#xa; This will need to be updated whenever the network name&#xa; is edited or changed
</description>
<tag name="@see">#controllerName</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.setControllerName(java.lang.String) -->
<method name="setControllerName"  public="true">
<description>
Set the &lt;code&gt;controllerName&lt;/code&gt; property.&#xa; this is the name of the infinet controller.  It will be&#xa; empty if the point resides on the network controller.&#xa; This will need to be updated whenever the network name&#xa; is edited or changed
</description>
<tag name="@see">#controllerName</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.getPort() -->
<method name="getPort"  public="true">
<description>
Get the &lt;code&gt;port&lt;/code&gt; property.
</description>
<tag name="@see">#port</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.setPort(java.lang.String) -->
<method name="setPort"  public="true">
<description>
Set the &lt;code&gt;port&lt;/code&gt; property.
</description>
<tag name="@see">#port</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.getModel() -->
<method name="getModel"  public="true">
<description>
Get the &lt;code&gt;model&lt;/code&gt; property.
</description>
<tag name="@see">#model</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.setModel(java.lang.String) -->
<method name="setModel"  public="true">
<description>
Set the &lt;code&gt;model&lt;/code&gt; property.
</description>
<tag name="@see">#model</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.getSerialNumber() -->
<method name="getSerialNumber"  public="true">
<description>
Get the &lt;code&gt;serialNumber&lt;/code&gt; property.
</description>
<tag name="@see">#serialNumber</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.setSerialNumber(java.lang.String) -->
<method name="setSerialNumber"  public="true">
<description>
Set the &lt;code&gt;serialNumber&lt;/code&gt; property.
</description>
<tag name="@see">#serialNumber</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.getId() -->
<method name="getId"  public="true">
<description>
Get the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#id</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.setId(java.lang.String) -->
<method name="setId"  public="true">
<description>
Set the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#id</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.getPingRequestType() -->
<method name="getPingRequestType"  public="true">
<description>
The ping request type is &lt;code&gt;BInfinityDevicePingRequest.TYPE&lt;/code&gt;
</description>
<tag name="@see">com.tridium.devDriver.identify.BIDdfPingParams#getDdfPingRequestType()</tag>
<return>
<type class="javax.baja.sys.Type"/>
<description>
&lt;code&gt;BInfinityDevicePingRequest.TYPE&lt;/code.
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.getDiscoveryName() -->
<method name="getDiscoveryName"  public="true">
<description>
Discovery name for infinity is the controller name
</description>
<tag name="@see">com.tridium.ddf.discover.BIDdfDiscoveryLeaf#getDiscoveryName()</tag>
<return>
<type class="java.lang.String"/>
<description>
the controller name
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.getValidDatabaseTypes() -->
<method name="getValidDatabaseTypes"  public="true">
<description>
Return an array of TypeInfo objects corresponding&#xa; to all valid Niagara Ax types for this discovery object. This is&#xa; important when the end-user clicks &#x27;Add&#x27; from the user interface for&#xa; the manager.
</description>
<tag name="@see">com.tridium.ddf.discover.BIDdfDiscoveryLeaf#getValidDatabaseTypes()</tag>
<return>
<type class="javax.baja.registry.TypeInfo" dimension="1"/>
<description>
for infinity, the only valid type is &lt;code&gt;BInfinetDevice.Type&lt;/code&gt;
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
Override toString() method to put a meaningful description on the property&#xa; sheet view
</description>
<tag name="@see">javax.baja.sys.BInterface#toString(javax.baja.sys.Context)</tag>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
String
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.controllerName -->
<field name="controllerName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;controllerName&lt;/code&gt; property.&#xa; this is the name of the infinet controller.  It will be&#xa; empty if the point resides on the network controller.&#xa; This will need to be updated whenever the network name&#xa; is edited or changed
</description>
<tag name="@see">#getControllerName</tag>
<tag name="@see">#setControllerName</tag>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.port -->
<field name="port"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;port&lt;/code&gt; property.
</description>
<tag name="@see">#getPort</tag>
<tag name="@see">#setPort</tag>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.model -->
<field name="model"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;model&lt;/code&gt; property.
</description>
<tag name="@see">#getModel</tag>
<tag name="@see">#setModel</tag>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.serialNumber -->
<field name="serialNumber"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;serialNumber&lt;/code&gt; property.
</description>
<tag name="@see">#getSerialNumber</tag>
<tag name="@see">#setSerialNumber</tag>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.id -->
<field name="id"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#getId</tag>
<tag name="@see">#setId</tag>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceId.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
