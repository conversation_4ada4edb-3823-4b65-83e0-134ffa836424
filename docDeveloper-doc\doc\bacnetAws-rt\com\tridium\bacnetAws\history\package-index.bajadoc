<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnetAws" runtimeProfile="rt" name="com.tridium.bacnetAws.history">
<description/>
<class packageName="com.tridium.bacnetAws.history" name="BBacnetAwsHistoryDeviceExt"><description>BBacnetOwsHistoryDeviceExt adds support for TrendMultiple.</description></class>
<class packageName="com.tridium.bacnetAws.history" name="BBacnetEventLogImport"><description>BBacnetEventLogImport defines an archive action for transferring&#xa; one event log from a remote Bacnet source to the local&#xa; destination.</description></class>
<class packageName="com.tridium.bacnetAws.history" name="BBacnetEventRecord"><description>&lt;code&gt;BBacnetEventRecord&lt;/code&gt; is a Bacnet event record</description></class>
</package>
</bajadoc>
