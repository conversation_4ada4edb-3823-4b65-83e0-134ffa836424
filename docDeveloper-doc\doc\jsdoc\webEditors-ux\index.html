<!-- Htmldoc has been run -->
<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>webEditors Index</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

<!-- Auto-generated style sheet link --><link rel='StyleSheet' href='module://bajaui/doc/style.css' type='text/css' />
<!-- Auto-generated js link for Activity Monitoring --><script type='text/javascript' src='module://web/rc/util/activityMonitor.js'></script>
<script type='text/javascript'>window.addEventListener('load', activityMonitor.start);</script>
</head>

<body>
<!-- Auto-generated Header NavBar --><p class="navbar">  <a href="/doc/index.html" class="navbar">Index</a> |  <a href="/doc/jsdoc/bajaux-ux/index.html" class="navbar">Prev</a> |  <a href="/doc/ui/uxMedia.html" class="navbar">Next</a></p>


<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">webEditors</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-nmodule_webEditors_rc_fe_baja_BaseEditor.html">nmodule/webEditors/rc/fe/baja/BaseEditor</a></li><li><a href="module-nmodule_webEditors_rc_fe_BaseWidget.html">nmodule/webEditors/rc/fe/BaseWidget</a></li><li><a href="module-nmodule_webEditors_rc_fe_fe.html">nmodule/webEditors/rc/fe/fe</a></li><li><a href="module-nmodule_webEditors_rc_fe_feDialogs.html">nmodule/webEditors/rc/fe/feDialogs</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_commands_MgrCommand.html">nmodule/webEditors/rc/wb/mgr/commands/MgrCommand</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_Manager.html">nmodule/webEditors/rc/wb/mgr/Manager</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html">nmodule/webEditors/rc/wb/mgr/MgrLearn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrStateHandler.html">nmodule/webEditors/rc/wb/mgr/MgrStateHandler</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrTypeInfo.html">nmodule/webEditors/rc/wb/mgr/MgrTypeInfo</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_IconMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinPropMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_NameMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyPathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_TypeMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/MgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrModel.html">nmodule/webEditors/rc/wb/mgr/model/MgrModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">nmodule/webEditors/rc/wb/table/model/Column</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_DisplayNameColumn.html">nmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_IconColumn.html">nmodule/webEditors/rc/wb/table/model/columns/IconColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_JsonObjectPropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_PropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_ToStringColumn.html">nmodule/webEditors/rc/wb/table/model/columns/ToStringColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentSource.html">nmodule/webEditors/rc/wb/table/model/ComponentSource</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentTableModel.html">nmodule/webEditors/rc/wb/table/model/ComponentTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">nmodule/webEditors/rc/wb/table/model/Row</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html">nmodule/webEditors/rc/wb/table/model/TableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_Table.html">nmodule/webEditors/rc/wb/table/Table</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeNodeRow.html">nmodule/webEditors/rc/wb/table/tree/TreeNodeRow</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeTableModel.html">nmodule/webEditors/rc/wb/table/tree/TreeTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_tree_TreeNode.html">nmodule/webEditors/rc/wb/tree/TreeNode</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="tutorials.list.html" class="dropdown-toggle" data-toggle="dropdown">Tutorials<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="tutorial-6-managers.html">Managers</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	
	











	
	





    <section class="readme-section">
        <article><h1>webEditors</h1>
<p>This module contains a number of modules and libraries, built on <code>bajaScript</code><br>
and <code>bajaux</code>, that allow interaction with a Niagara station using HTML5,<br>
JavaScript, and CSS technologies.</p>
<p><code>webEditors</code> contains a large number of different libraries, but the public<br>
interface is centered around one primary use case: enabling you to create your<br>
own HTML5 field editors for use in the HTML5 Property Sheet and other next-gen<br>
Niagara 4 web applications.</p>
<h1>Creating a Field Editor</h1>
<p>Before beginning the process of writing a field editor, please consult the<br>
documentation for<br>
<a href="module://docDeveloper/doc/jsdoc/bajaScript-ux/index.html">BajaScript</a> and<br>
<a href="module://docDeveloper/doc/jsdoc/bajaux-ux/index.html">bajaux</a>.</p>
<p>In order for Baja values to be editable in the Property Sheet, Field Editors<br>
must be implemented that are capable of displaying those values and saving<br>
changes to them. In Niagara AX, these Field Editors were created for the AX<br>
Property Sheet by extending <code>BWbFieldEditor</code> and implementing them in Java. In<br>
Niagara 4, the Property Sheet has been reimagined using HTML5, and the field<br>
editors are implemented using the new <code>bajaux</code> JavaScript framework.</p>
<h2>Writing and Testing Your Code</h2>
<p>In Niagara 4, modules have been split into parts: an RT part for runtime code, a<br>
WB part for Workbench code, etc. HTML5 application code goes into a UX module<br>
part. When beginning development on a new UX module part, we highly recommend<br>
that you use some of the open source tools Tridium has created to get up and<br>
running. Please see the help section on <a href="module://docDeveloper/doc/js/buildingJS.html">Building JavaScript<br>
Applications</a> for info on getting<br>
your development environment set up, and for a preliminary tutorial.</p>
<p>In particular, the section on implementing <code>BIJavaScript</code> and registering as an<br>
agent on your Type will be a requirement.</p>
<h2>Different Form Factors, and Pop-Out Editors</h2>
<p>As part of the <a href="module://docDeveloper/doc/index.html#bajaux">bajaux</a><br>
documentation, you learned about the role of Form Factors in the <code>bajaux</code><br>
framework.</p>
<p>In most cases, as in Workbench, a field editor will be used to edit a Baja value<br>
on the Property Sheet. To enable this, you'll want your <code>BIJavaScript</code> class to<br>
implement <code>BIFormFactorMini</code>, thereby marking it as a &quot;mini&quot; widget that is<br>
well-suited to fit inside a row on the Property Sheet. If this is sufficient for<br>
your use case, then you're finished - the Property Sheet will pick up on the<br>
agent registration and automatically show the correct Mini widget for your Type.</p>
<p>But sometimes you might want to edit a widget in more detail than could<br>
comfortably fit inside a Property Sheet row. An example of this is a <code>Facets</code><br>
instance - the Property Sheet row contains a small amount of information, but a<br>
button allows you to pop up a dialog with more controls, allowing you to add and<br>
remove Facets tags and edit their values.</p>
<p>The editor that appears in the dialog will have a form factor of Compact:<br>
something in between Mini and Max. By also implementing <code>BIFormFactorCompact</code>,<br>
you will cause an &quot;edit&quot; button to appear in the Property Sheet row for your<br>
value. When clicked, this button will pop out the Compact editor in a dialog for<br>
more detailed editing of that value.</p>
<p>(Note that you can do this by creating two completely separate editors, one Mini<br>
and one Compact, both registered as agents on your Type. Or, you can create just<br>
one editor, whose JavaScript implementation behaves differently based on the<br>
Widget's form factor, and whose <code>BIJavaScript</code> class implements both<br>
<code>BIFormFactorMini</code> and <code>BIFormFactorCompact</code>. Either solution will work.)</p>
<h3>On Field Editors and doRead()</h3>
<p>Although <code>bajaux</code> does not place a restriction on what type of value <code>doRead()</code><br>
can resolve, it's recommended that field editors intended for use in the<br>
Property Sheet have <code>doRead()</code> resolve a value of the same Type that was<br>
originally loaded in. This helps to ensure proper behavior of the pop-out Edit<br>
button and validating individual rows.</p>
<h1>Public Modules in webEditors</h1>
<p>The <code>webEditors</code> module contains dozens of JavaScript modules and utilities.<br>
However, <code>webEditors</code> is new for Niagara 4 and under constant improvement, so<br>
only the modules described in this section are considered public. Tridium will<br>
make its best effort to keep these APIs as stable as possible moving forward<br>
into new versions of Niagara 4; breaking changes will be kept to a minimum and<br>
documented with release notes. Consider these modules analogous to <code>javax.baja</code><br>
packages in the Niagara Framework.</p>
<p>You are welcome to explore and make use of the other JavaScript modules in<br>
<code>webEditors</code>, but any not listed here may be removed or drastically changed in<br>
future versions.</p>
<h2>BaseEditor</h2>
<p><a href="module-nmodule_webEditors_rc_fe_baja_BaseEditor.html">BaseEditor</a> is a<br>
subclass of <code>bajaux/Widget</code>. While a vanilla <code>Widget</code> can load in values of any<br>
type, <code>BaseEditor</code>s are more specialized for loading in Baja values.</p>
<p>The set of field editors provided in the <code>webEditors</code> module (like<br>
<code>StringEditor</code>, <code>AbsTimeEditor</code>, etc.) all extend from <code>BaseEditor</code> for<br>
Baja-specific functionality. When implementing a field editor to hold a Baja<br>
value, extending <code>BaseEditor</code> will simplify your implementation and help it<br>
integrate more cleanly with <code>fe</code>.</p>
<h2>fe</h2>
<p><a href="module-nmodule_webEditors_rc_fe_fe.html">fe</a> is a module that allows you to<br>
instantiate widgets for particular Baja values. Essentially, you hand it a<br>
value, and it handles the work of deciding what kind of editor to create for<br>
that value, dynamically retrieving the code for that editor, and building it<br>
into your page.</p>
<p><code>fe</code> is short for &quot;field editors&quot; because it's very common to use it to<br>
instantiate a field editor for a particular Baja value, but it is not limited to<br>
this case.</p>
<p>For example, say I have a <code>Component</code>, and I want the user to be able to edit<br>
its <code>string</code> slot. By using <code>fe</code>, I don't have to worry about going out to find<br>
code for <code>StringEditor</code> and the like - I just pass <code>fe</code> some configuration and<br>
it does the work.</p>
<pre class="prettyprint source lang-javascript"><code>var component = baja.$('baja:Component', { string: 'enter a string' });

// i want to edit this slot, on this Complex, and put the editor in this DOM
// element. i want a Mini form factor for my editor, and set the multiLine
// property to true so i can type line breaks.
fe.buildFor({
  slot: 'string',
  complex: component,
  dom: $('#myStringEditorGoesHere'),
  formFactor: 'mini',
  properties: { multiLine: true }
})
  .then(function (stringEditor) {
    //an editor that can edit baja:String values is now loaded into my page. 
    
    //user types in a new string and clicks Save...
    
    $('#saveButton').on('click', function () {
      stringEditor.save().then(function () {
        //the string has been saved to the component!
        console.log(component.get('string'));
      });
    });
  });
</code></pre>
<h2>feDialogs</h2>
<p><a href="module-nmodule_webEditors_rc_fe_feDialogs.html">feDialogs</a> handles a number<br>
of cases where you might want to show a field editor in a modal dialog, such as<br>
prompting the user for a value or for invoking an Action on a Component.</p>
<pre class="prettyprint source lang-javascript"><code>feDialogs.showFor({
  title: 'Enter a number between 0 and 5',
  value: 0,
  properties: { min: 0, max: 5 }
})
  .then(function (result) {
    if (result === null) {
      console.log('The user clicked Cancel');
    } else {
      console.log('Result: ' + result);
    }
  });
</code></pre></article>
    </section>







		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	webEditors Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:59+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>



<!-- Auto-generated Footer NavBar --><p class="navbar">  <a href="/doc/index.html" class="navbar">Index</a> |  <a href="/doc/jsdoc/bajaux-ux/index.html" class="navbar">Prev</a> |  <a href="/doc/ui/uxMedia.html" class="navbar">Next</a></p>
<!-- Auto-generated copyright note --><p class='copyright'></p>
</body>
</html>
