<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetStringScheduleDescriptor" name="BBacnetStringScheduleDescriptor" packageName="javax.baja.bacnet.export" public="true">
<description>
BBacnetStringScheduleDescriptor exposes a Niagara schedule to Bacnet.
</description>
<tag name="@author"><PERSON> on 18 Aug 03</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.export.BBacnetScheduleDescriptor"/>
</extends>
<!-- javax.baja.bacnet.export.BBacnetStringScheduleDescriptor() -->
<constructor name="BBacnetStringScheduleDescriptor" public="true">
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetStringScheduleDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetStringScheduleDescriptor.doWritePresentValue() -->
<method name="doWritePresentValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Write the present value of the schedule to non-Present_Value&#xa; target properties, and to any external targets.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetStringScheduleDescriptor.readProperty(int, int) -->
<method name="readProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetStringScheduleDescriptor.writeProperty(int, int, byte[], int) -->
<method name="writeProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetStringScheduleDescriptor.doWriteScheduleDefaultValue(com.tridium.bacnet.asn.AsnInputStream, int) -->
<method name="doWriteScheduleDefaultValue"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Write the schedule default value for string type schedule
</description>
<parameter name="asnInputStream">
<type class="com.tridium.bacnet.asn.AsnInputStream"/>
<description/>
</parameter>
<parameter name="applicationTag">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if no error, otherwise error type
</description>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetStringScheduleDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
