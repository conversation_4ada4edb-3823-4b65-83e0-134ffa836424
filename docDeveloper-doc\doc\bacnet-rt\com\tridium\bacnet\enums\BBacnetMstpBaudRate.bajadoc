<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.enums.BBacnetMstpBaudRate" name="BBacnetMstpBaudRate" packageName="com.tridium.bacnet.enums" public="true" final="true">
<description>
BBacnetMstpBaudRate represents the possible baud rate choices&#xa; for a comm port.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">27 Jan 04</tag>
<tag name="@since">Niagara 3.0 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;baud_9600&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>9600</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;baud_19200&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>19200</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;baud_38400&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>38400</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;baud_57600&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>57600</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;baud_76800&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>76800</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;baud_115200&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>115200</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
</class>
</bajadoc>
