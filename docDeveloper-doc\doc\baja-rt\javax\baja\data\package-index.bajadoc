<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="baja" runtimeProfile="rt" name="javax.baja.data">
<description>
&lt;p&gt;Support for core primitive data types.&lt;/p&gt;
</description>
<class packageName="javax.baja.data" name="DataTypes"><description>DataTypes define the types used by the Baja Data API.</description></class>
<class packageName="javax.baja.data" name="DataUtil"><description>DataUtil provides utility methods for the Data API.</description></class>
<class packageName="javax.baja.data" name="BIDataTable" category="interface"><description>A BIDataTable is a &lt;code&gt;<see ref="javax.baja.collection.BITable">BITable</see>&lt;/code&gt; with &lt;code&gt;<see ref="javax.baja.data.BIDataValue">BIDataValue</see>&lt;/code&gt; values for all cell values.</description></class>
<class packageName="javax.baja.data" name="BIDataValue" category="interface"><description>BIDataValue is the interface implemented by BSimples that&#xa; are one of the predefined data types in &lt;code&gt;<see ref="javax.baja.data.DataTypes">DataTypes</see>&lt;/code&gt;.</description></class>
<class packageName="javax.baja.data" name="DataTypeException" category="exception"><description>DataTypeException is thrown when a non-DataType type is&#xa; used where a DataType is expected.</description></class>
<class packageName="javax.baja.data" name="TypeMismatchException" category="exception"><description>TypeMismatchException is thrown when one type is encountered when&#xa; a different type is expected.</description></class>
</package>
</bajadoc>
