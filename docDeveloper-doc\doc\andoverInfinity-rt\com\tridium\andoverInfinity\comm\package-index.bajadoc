<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="andoverInfinity" runtimeProfile="rt" name="com.tridium.andoverInfinity.comm">
<description/>
<class packageName="com.tridium.andoverInfinity.comm" name="BInfinityCommunicator"><description>Communicator for Infinity driver</description></class>
<class packageName="com.tridium.andoverInfinity.comm" name="BInfinityLine"><description>BInfinityLine is used to pass line/format info from the screen buffer to&#xa; the workbench terminal view</description></class>
<class packageName="com.tridium.andoverInfinity.comm" name="BInfinitySerialReceiver"><description>Infinity driver implementation of a BDdfSerialReceiver, uses a &#xa; BVt100ScreenBuffer to store characters read from the input stream.</description></class>
<class packageName="com.tridium.andoverInfinity.comm" name="BInfinityTransmitter"><description>A custom transmitter for Infinity that overrides forceTransmit method</description></class>
<class packageName="com.tridium.andoverInfinity.comm" name="BVt100"><description>Contains a screen buffer to maintain both the content and state&#xa;  of a screen as VT100 commands and control sequences are received.</description></class>
<class packageName="com.tridium.andoverInfinity.comm" name="CursorPosition"><description>Maintains cursor line and column position information for the BVt100Screen</description></class>
<class packageName="com.tridium.andoverInfinity.comm" name="InfinityUtil"/>
<class packageName="com.tridium.andoverInfinity.comm" name="MarkerPosition"><description>Position encapsulates a logical two dimensional character &#xa; position in a document with a zero based line and column&#xa; index.</description></class>
<class packageName="com.tridium.andoverInfinity.comm" name="Vt100Const" category="interface"><description>Constants for use in the Infinity driver</description></class>
</package>
</bajadoc>
