<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="axvelocity" runtimeProfile="rt" name="javax.baja.velocity">
<description/>
<class packageName="javax.baja.velocity" name="BVelocityView"><description>BVelocityView is a servlet view based on an Apache Velocity template.</description></class>
<class packageName="javax.baja.velocity" name="BVelocityWebProfile"><description>A Web Profile for Velocity</description></class>
<class packageName="javax.baja.velocity" name="VelocityContextUtil"><description>VelocityContextUtil contains utility methods for use in velocity templates.</description></class>
<class packageName="javax.baja.velocity" name="VelocityContextUtil.TableData"/>
<class packageName="javax.baja.velocity" name="VelocityContextUtil.TableRow"/>
<class packageName="javax.baja.velocity" name="BIVelocityWebProfile" category="interface"><description>Interface for a Velocity Web Profile.</description></class>
<class packageName="javax.baja.velocity" name="BVelocityView.InvalidProfileException" category="exception"><description>Exception thrown if an incorrect Web Profile is used with this view.</description></class>
</package>
</bajadoc>
