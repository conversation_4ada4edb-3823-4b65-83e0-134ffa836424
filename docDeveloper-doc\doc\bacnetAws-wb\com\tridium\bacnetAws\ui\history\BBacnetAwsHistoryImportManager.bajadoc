<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="wb" qualifiedName="com.tridium.bacnetAws.ui.history.BBacnetAwsHistoryImportManager" name="BBacnetAwsHistoryImportManager" packageName="com.tridium.bacnetAws.ui.history" public="true">
<description>
BBacnetAwsHistoryImportManager augments BBacnetOwsHistoryImportManager to also&#xa; handle EventLogs.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">24 May 2010</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.ui.history.BBacnetHistoryImportManager"/>
</extends>
</class>
</bajadoc>
