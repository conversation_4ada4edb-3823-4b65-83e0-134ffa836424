<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm" name="BOutOfRangeFaultAlgorithm" packageName="javax.baja.alarm.ext.fault" public="true">
<description>
BOutOfRangeFaultAlgorithm implements a standard out-of-range&#xa; alarming algorithm
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">08 Nov 04</tag>
<tag name="@version">$Revision: 10$ $Date: 6/15/10 2:58:33 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.alarm.ext.BFaultAlgorithm"/>
</extends>
<property name="highLimit" flags="">
<type class="double"/>
<description>
Slot for the &lt;code&gt;highLimit&lt;/code&gt; property.&#xa; Value above which the object is evaluated in high-limit alarm.
</description>
<tag name="@see">#getHighLimit</tag>
<tag name="@see">#setHighLimit</tag>
</property>

<property name="lowLimit" flags="">
<type class="double"/>
<description>
Slot for the &lt;code&gt;lowLimit&lt;/code&gt; property.&#xa; Value below which the object is considered in low-limit alarm.
</description>
<tag name="@see">#getLowLimit</tag>
<tag name="@see">#setLowLimit</tag>
</property>

<property name="deadband" flags="">
<type class="double"/>
<description>
Slot for the &lt;code&gt;deadband&lt;/code&gt; property.&#xa; Differential value applied to high and low limits before return-to-normal.&#xa; Deadband is subtracted from highLimit and added to lowLimit.
</description>
<tag name="@see">#getDeadband</tag>
<tag name="@see">#setDeadband</tag>
</property>

<property name="highLimitText" flags="">
<type class="javax.baja.util.BFormat"/>
<description>
Slot for the &lt;code&gt;highLimitText&lt;/code&gt; property.
</description>
<tag name="@see">#getHighLimitText</tag>
<tag name="@see">#setHighLimitText</tag>
</property>

<property name="lowLimitText" flags="">
<type class="javax.baja.util.BFormat"/>
<description>
Slot for the &lt;code&gt;lowLimitText&lt;/code&gt; property.
</description>
<tag name="@see">#getLowLimitText</tag>
<tag name="@see">#setLowLimitText</tag>
</property>

<property name="limitEnable" flags="">
<type class="javax.baja.alarm.ext.BLimitEnable"/>
<description>
Slot for the &lt;code&gt;limitEnable&lt;/code&gt; property.&#xa; Flags that enable low-limit and high-limit alarms, as needed.
</description>
<tag name="@see">#getLimitEnable</tag>
<tag name="@see">#setLimitEnable</tag>
</property>

<property name="inHighState" flags="h">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;inHighState&lt;/code&gt; property.
</description>
<tag name="@see">#getInHighState</tag>
<tag name="@see">#setInHighState</tag>
</property>

<property name="inLowState" flags="h">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;inLowState&lt;/code&gt; property.
</description>
<tag name="@see">#getInLowState</tag>
<tag name="@see">#setInLowState</tag>
</property>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm() -->
<constructor name="BOutOfRangeFaultAlgorithm" public="true">
<description/>
</constructor>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.getHighLimit() -->
<method name="getHighLimit"  public="true">
<description>
Get the &lt;code&gt;highLimit&lt;/code&gt; property.&#xa; Value above which the object is evaluated in high-limit alarm.
</description>
<tag name="@see">#highLimit</tag>
<return>
<type class="double"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.setHighLimit(double) -->
<method name="setHighLimit"  public="true">
<description>
Set the &lt;code&gt;highLimit&lt;/code&gt; property.&#xa; Value above which the object is evaluated in high-limit alarm.
</description>
<tag name="@see">#highLimit</tag>
<parameter name="v">
<type class="double"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.getLowLimit() -->
<method name="getLowLimit"  public="true">
<description>
Get the &lt;code&gt;lowLimit&lt;/code&gt; property.&#xa; Value below which the object is considered in low-limit alarm.
</description>
<tag name="@see">#lowLimit</tag>
<return>
<type class="double"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.setLowLimit(double) -->
<method name="setLowLimit"  public="true">
<description>
Set the &lt;code&gt;lowLimit&lt;/code&gt; property.&#xa; Value below which the object is considered in low-limit alarm.
</description>
<tag name="@see">#lowLimit</tag>
<parameter name="v">
<type class="double"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.getDeadband() -->
<method name="getDeadband"  public="true">
<description>
Get the &lt;code&gt;deadband&lt;/code&gt; property.&#xa; Differential value applied to high and low limits before return-to-normal.&#xa; Deadband is subtracted from highLimit and added to lowLimit.
</description>
<tag name="@see">#deadband</tag>
<return>
<type class="double"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.setDeadband(double) -->
<method name="setDeadband"  public="true">
<description>
Set the &lt;code&gt;deadband&lt;/code&gt; property.&#xa; Differential value applied to high and low limits before return-to-normal.&#xa; Deadband is subtracted from highLimit and added to lowLimit.
</description>
<tag name="@see">#deadband</tag>
<parameter name="v">
<type class="double"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.getHighLimitText() -->
<method name="getHighLimitText"  public="true">
<description>
Get the &lt;code&gt;highLimitText&lt;/code&gt; property.
</description>
<tag name="@see">#highLimitText</tag>
<return>
<type class="javax.baja.util.BFormat"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.setHighLimitText(javax.baja.util.BFormat) -->
<method name="setHighLimitText"  public="true">
<description>
Set the &lt;code&gt;highLimitText&lt;/code&gt; property.
</description>
<tag name="@see">#highLimitText</tag>
<parameter name="v">
<type class="javax.baja.util.BFormat"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.getLowLimitText() -->
<method name="getLowLimitText"  public="true">
<description>
Get the &lt;code&gt;lowLimitText&lt;/code&gt; property.
</description>
<tag name="@see">#lowLimitText</tag>
<return>
<type class="javax.baja.util.BFormat"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.setLowLimitText(javax.baja.util.BFormat) -->
<method name="setLowLimitText"  public="true">
<description>
Set the &lt;code&gt;lowLimitText&lt;/code&gt; property.
</description>
<tag name="@see">#lowLimitText</tag>
<parameter name="v">
<type class="javax.baja.util.BFormat"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.getLimitEnable() -->
<method name="getLimitEnable"  public="true">
<description>
Get the &lt;code&gt;limitEnable&lt;/code&gt; property.&#xa; Flags that enable low-limit and high-limit alarms, as needed.
</description>
<tag name="@see">#limitEnable</tag>
<return>
<type class="javax.baja.alarm.ext.BLimitEnable"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.setLimitEnable(javax.baja.alarm.ext.BLimitEnable) -->
<method name="setLimitEnable"  public="true">
<description>
Set the &lt;code&gt;limitEnable&lt;/code&gt; property.&#xa; Flags that enable low-limit and high-limit alarms, as needed.
</description>
<tag name="@see">#limitEnable</tag>
<parameter name="v">
<type class="javax.baja.alarm.ext.BLimitEnable"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.getInHighState() -->
<method name="getInHighState"  public="true">
<description>
Get the &lt;code&gt;inHighState&lt;/code&gt; property.
</description>
<tag name="@see">#inHighState</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.setInHighState(boolean) -->
<method name="setInHighState"  public="true">
<description>
Set the &lt;code&gt;inHighState&lt;/code&gt; property.
</description>
<tag name="@see">#inHighState</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.getInLowState() -->
<method name="getInLowState"  public="true">
<description>
Get the &lt;code&gt;inLowState&lt;/code&gt; property.
</description>
<tag name="@see">#inLowState</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.setInLowState(boolean) -->
<method name="setInLowState"  public="true">
<description>
Set the &lt;code&gt;inLowState&lt;/code&gt; property.
</description>
<tag name="@see">#inLowState</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.started() -->
<method name="started"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.isGrandparentLegal(javax.baja.sys.BComponent) -->
<method name="isGrandparentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
A BOutOfRangeAlgorithm&#x27;s grandparent must implement&#xa; the NumericPoint interface
</description>
<parameter name="grandparent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.getSlotFacets(javax.baja.sys.Slot) -->
<method name="getSlotFacets"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="slot">
<type class="javax.baja.sys.Slot"/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.checkFault(javax.baja.status.BStatusValue) -->
<method name="checkFault"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="o">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<return>
<type class="javax.baja.alarm.ext.BAlarmState"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.writeAlarmData(javax.baja.status.BStatusValue, java.util.Map) -->
<method name="writeAlarmData"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Write the key-value pairs defining alarm data for the&#xa;  alarm algorithm and state to the given Facets.&#xa; &lt;p&gt;&#xa;  The alarm data for an Out of Range alarm is given by&#xa;  BACnet table 13-3, Standard Object Property Values&#xa;  returned in notifications.
</description>
<parameter name="out">
<type class="javax.baja.status.BStatusValue"/>
<description>
The relevant control point status value
</description>
</parameter>
<parameter name="map">
<parameterizedType class="java.util.Map">
<args>
</args>
</parameterizedType>
<description>
The map.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.highLimit -->
<field name="highLimit"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;highLimit&lt;/code&gt; property.&#xa; Value above which the object is evaluated in high-limit alarm.
</description>
<tag name="@see">#getHighLimit</tag>
<tag name="@see">#setHighLimit</tag>
</field>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.lowLimit -->
<field name="lowLimit"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lowLimit&lt;/code&gt; property.&#xa; Value below which the object is considered in low-limit alarm.
</description>
<tag name="@see">#getLowLimit</tag>
<tag name="@see">#setLowLimit</tag>
</field>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.deadband -->
<field name="deadband"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;deadband&lt;/code&gt; property.&#xa; Differential value applied to high and low limits before return-to-normal.&#xa; Deadband is subtracted from highLimit and added to lowLimit.
</description>
<tag name="@see">#getDeadband</tag>
<tag name="@see">#setDeadband</tag>
</field>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.highLimitText -->
<field name="highLimitText"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;highLimitText&lt;/code&gt; property.
</description>
<tag name="@see">#getHighLimitText</tag>
<tag name="@see">#setHighLimitText</tag>
</field>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.lowLimitText -->
<field name="lowLimitText"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lowLimitText&lt;/code&gt; property.
</description>
<tag name="@see">#getLowLimitText</tag>
<tag name="@see">#setLowLimitText</tag>
</field>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.limitEnable -->
<field name="limitEnable"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;limitEnable&lt;/code&gt; property.&#xa; Flags that enable low-limit and high-limit alarms, as needed.
</description>
<tag name="@see">#getLimitEnable</tag>
<tag name="@see">#setLimitEnable</tag>
</field>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.inHighState -->
<field name="inHighState"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;inHighState&lt;/code&gt; property.
</description>
<tag name="@see">#getInHighState</tag>
<tag name="@see">#setInHighState</tag>
</field>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.inLowState -->
<field name="inLowState"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;inLowState&lt;/code&gt; property.
</description>
<tag name="@see">#getInLowState</tag>
<tag name="@see">#setInLowState</tag>
</field>

<!-- javax.baja.alarm.ext.fault.BOutOfRangeFaultAlgorithm.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
