<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetCommControl" name="BBacnetCommControl" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetCommControl represents the communication control enumeration&#xa; defined in the DeviceCommunicationControl-Request definition.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">05 Nov 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;enable&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;disable&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;disableInitiation&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetCommControl.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetCommControl"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetCommControl.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetCommControl"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetCommControl.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetCommControl.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetCommControl.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
String representation of this BEnum.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetCommControl.ENABLE -->
<field name="ENABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for enable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetCommControl.DISABLE -->
<field name="DISABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for disable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetCommControl.DISABLE_INITIATION -->
<field name="DISABLE_INITIATION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for disableInitiation.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetCommControl.enable -->
<field name="enable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetCommControl"/>
<description>
BBacnetCommControl constant for enable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetCommControl.disable -->
<field name="disable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetCommControl"/>
<description>
BBacnetCommControl constant for disable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetCommControl.disableInitiation -->
<field name="disableInitiation"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetCommControl"/>
<description>
BBacnetCommControl constant for disableInitiation.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetCommControl.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetCommControl"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetCommControl.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
