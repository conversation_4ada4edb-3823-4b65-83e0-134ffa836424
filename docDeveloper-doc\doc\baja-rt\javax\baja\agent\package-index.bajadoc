<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="baja" runtimeProfile="rt" name="javax.baja.agent">
<description>
&lt;p&gt;Contains classes used to interrogate for agents.&lt;/p&gt;
</description>
<class packageName="javax.baja.agent" name="AgentFilter"><description>AgentFilter is used to filter an AgentList.</description></class>
<class packageName="javax.baja.agent" name="BAbstractPxView"><description>BAbstractPxView is a dynamic view which may be added to BComponents&#xa; as a property or by overriding getAgents().</description></class>
<class packageName="javax.baja.agent" name="BDynamicPxView"><description>BDynamicPxView is a BAbstractPxView which dynamically&#xa; generates Presentation XML on demand.</description></class>
<class packageName="javax.baja.agent" name="BPxView"><description>BPxView is a BAbstractPxView which stores &#xa; the view contents in an XML file with a px extension.</description></class>
<class packageName="javax.baja.agent" name="AgentInfo" category="interface"><description>AgentInfo provides summary information for an &#xa; agent within an AgentList.</description></class>
<class packageName="javax.baja.agent" name="AgentList" category="interface"><description>AgentList encapsulates an ordered list of agent AgentInfos.</description></class>
<class packageName="javax.baja.agent" name="BIAgent" category="interface"><description>BIAgent is the interface implemented by agent types.</description></class>
<class packageName="javax.baja.agent" name="NoSuchAgentException" category="exception"><description>NoSuchAgentException is thrown when expecting a specific&#xa; agent, but one is not found.</description></class>
</package>
</bajadoc>
