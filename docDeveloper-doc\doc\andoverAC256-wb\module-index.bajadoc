<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="andoverAC256" runtimeProfile="wb" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>AndoverAC256 Driver</description>
<package name="com.tridium.andoverAC256.ui"/>
<class packageName="com.tridium.andoverAC256.ui" name="BAndoverBackupManager"><description>BAndoverBackupManager.</description></class>
<class packageName="com.tridium.andoverAC256.ui" name="BAndoverConsole"><description>BAndoverConsole - This view provides the user with a dumb terminal into an AC256</description></class>
<class packageName="com.tridium.andoverAC256.ui" name="BAndoverDeviceManager"><description>BAndoverDeviceManager</description></class>
<class packageName="com.tridium.andoverAC256.ui" name="BAndoverPointManager"><description>BAndoverPointManager uses the BAbstractLearn framework to&#xa; provide a way for the user to create proxy points within&#xa; a BAndoverDevice.</description></class>
</module>
</bajadoc>
