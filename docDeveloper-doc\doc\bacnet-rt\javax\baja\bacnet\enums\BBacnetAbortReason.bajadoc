<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetAbortReason" name="BBacnetAbortReason" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetAbortReason represents the Bacnet Abort Reason&#xa; enumeration.&#xa; &lt;p&gt;&#xa; BBacnetAbortReason is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Craig Gemmill</tag>
<tag name="@version">$Revision: 7$ $Date: 12/19/01 4:35:55 PM$</tag>
<tag name="@creation">10 Aug 00</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;other&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bufferOverflow&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;invalidApduInThisState&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;preemptedByHigherPriorityTask&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;segmentationNotSupported&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;securityError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;insufficientSecurity&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;windowSizeOutOfRange&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;applicationExceededReplyTime&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;outOfResources&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tsmTimeout&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;apduTooLong&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetAbortReason.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetAbortReason"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetAbortReason"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.OTHER -->
<field name="OTHER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for other.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.BUFFER_OVERFLOW -->
<field name="BUFFER_OVERFLOW"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bufferOverflow.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.INVALID_APDU_IN_THIS_STATE -->
<field name="INVALID_APDU_IN_THIS_STATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for invalidApduInThisState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.PREEMPTED_BY_HIGHER_PRIORITY_TASK -->
<field name="PREEMPTED_BY_HIGHER_PRIORITY_TASK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for preemptedByHigherPriorityTask.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.SEGMENTATION_NOT_SUPPORTED -->
<field name="SEGMENTATION_NOT_SUPPORTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for segmentationNotSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.SECURITY_ERROR -->
<field name="SECURITY_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for securityError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.INSUFFICIENT_SECURITY -->
<field name="INSUFFICIENT_SECURITY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for insufficientSecurity.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.WINDOW_SIZE_OUT_OF_RANGE -->
<field name="WINDOW_SIZE_OUT_OF_RANGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for windowSizeOutOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.APPLICATION_EXCEEDED_REPLY_TIME -->
<field name="APPLICATION_EXCEEDED_REPLY_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for applicationExceededReplyTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.OUT_OF_RESOURCES -->
<field name="OUT_OF_RESOURCES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for outOfResources.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.TSM_TIMEOUT -->
<field name="TSM_TIMEOUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tsmTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.APDU_TOO_LONG -->
<field name="APDU_TOO_LONG"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for apduTooLong.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.other -->
<field name="other"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetAbortReason"/>
<description>
BBacnetAbortReason constant for other.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.bufferOverflow -->
<field name="bufferOverflow"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetAbortReason"/>
<description>
BBacnetAbortReason constant for bufferOverflow.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.invalidApduInThisState -->
<field name="invalidApduInThisState"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetAbortReason"/>
<description>
BBacnetAbortReason constant for invalidApduInThisState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.preemptedByHigherPriorityTask -->
<field name="preemptedByHigherPriorityTask"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetAbortReason"/>
<description>
BBacnetAbortReason constant for preemptedByHigherPriorityTask.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.segmentationNotSupported -->
<field name="segmentationNotSupported"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetAbortReason"/>
<description>
BBacnetAbortReason constant for segmentationNotSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.securityError -->
<field name="securityError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetAbortReason"/>
<description>
BBacnetAbortReason constant for securityError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.insufficientSecurity -->
<field name="insufficientSecurity"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetAbortReason"/>
<description>
BBacnetAbortReason constant for insufficientSecurity.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.windowSizeOutOfRange -->
<field name="windowSizeOutOfRange"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetAbortReason"/>
<description>
BBacnetAbortReason constant for windowSizeOutOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.applicationExceededReplyTime -->
<field name="applicationExceededReplyTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetAbortReason"/>
<description>
BBacnetAbortReason constant for applicationExceededReplyTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.outOfResources -->
<field name="outOfResources"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetAbortReason"/>
<description>
BBacnetAbortReason constant for outOfResources.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.tsmTimeout -->
<field name="tsmTimeout"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetAbortReason"/>
<description>
BBacnetAbortReason constant for tsmTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.apduTooLong -->
<field name="apduTooLong"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetAbortReason"/>
<description>
BBacnetAbortReason constant for apduTooLong.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetAbortReason"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAbortReason.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
