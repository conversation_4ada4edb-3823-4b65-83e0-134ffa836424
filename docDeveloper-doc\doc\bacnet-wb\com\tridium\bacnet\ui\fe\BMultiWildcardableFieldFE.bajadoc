<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="wb" qualifiedName="com.tridium.bacnet.ui.fe.BMultiWildcardableFieldFE" name="BMultiWildcardableFieldFE" packageName="com.tridium.bacnet.ui.fe" public="true" abstract="true">
<description>
BMultiWildcardableFieldFE allows viewing and editing of&#xa; multiple time fields.  It also allows the ability to&#xa; wildcard these fields.&#xa;&#xa; NOTE:  You must set the fields class in your&#xa;   constructor!!!!!!
</description>
<tag name="@author">Craig <PERSON>mill</tag>
<tag name="@creation">30 Oct 2003</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.workbench.fieldeditor.BWbFieldEditor"/>
</extends>
<action name="increment" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;increment&lt;/code&gt; action.
</description>
<tag name="@see">#increment()</tag>
</action>

<action name="decrement" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;decrement&lt;/code&gt; action.
</description>
<tag name="@see">#decrement()</tag>
</action>

<action name="wildcard" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;wildcard&lt;/code&gt; action.
</description>
<tag name="@see">#wildcard()</tag>
</action>

</class>
</bajadoc>
