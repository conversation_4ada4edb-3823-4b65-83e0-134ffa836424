<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.zip.BZipSpace" name="BZipSpace" packageName="javax.baja.file.zip" public="true">
<description>
BZipSpace is a BFileSpace for a directory tree within BZipFile.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">24 Jan 03</tag>
<tag name="@version">$Revision: 16$ $Date: 11/12/07 12:46:14 PM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.file.BFileSpace"/>
</extends>
<implements>
<type class="javax.baja.file.BIDirectory"/>
</implements>
<!-- javax.baja.file.zip.BZipSpace(java.lang.String) -->
<constructor name="BZipSpace" public="true">
<parameter name="zip">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.file.zip.BZipSpace(javax.baja.file.zip.BZipFile) -->
<constructor name="BZipSpace" public="true">
<parameter name="file">
<type class="javax.baja.file.zip.BZipFile"/>
</parameter>
<description>
Construct for the specified BZipFile.
</description>
</constructor>

<!-- javax.baja.file.zip.BZipSpace(javax.baja.file.zip.BZipFile, java.nio.charset.Charset) -->
<constructor name="BZipSpace" public="true">
<parameter name="file">
<type class="javax.baja.file.zip.BZipFile"/>
<description>
the zip file for this space
</description>
</parameter>
<parameter name="charset">
<type class="java.nio.charset.Charset"/>
<description>
the charset of the zip file, defaults to UTF-8 in the underlying java ZipFile if null
</description>
</parameter>
<description>
Construct for the specified BZipFile and charset.
</description>
<tag name="@since">Niagara 4.10u8 / 4.13u3 / 4.14</tag>
</constructor>

<!-- javax.baja.file.zip.BZipSpace(javax.baja.naming.BOrd, java.util.zip.ZipFile) -->
<constructor name="BZipSpace" public="true">
<parameter name="ordInSession">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<parameter name="zip">
<type class="java.util.zip.ZipFile"/>
</parameter>
<description>
Construct for the specified ZipFile.
</description>
</constructor>

<!-- javax.baja.file.zip.BZipSpace(javax.baja.sys.BModule, java.util.zip.ZipFile) -->
<constructor name="BZipSpace" public="true">
<parameter name="module">
<type class="javax.baja.sys.BModule"/>
</parameter>
<parameter name="zip">
<type class="java.util.zip.ZipFile"/>
</parameter>
<description>
Construct for the specified ZipFile.
</description>
</constructor>

<!-- javax.baja.file.zip.BZipSpace.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.fw(int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object) -->
<method name="fw"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="code">
<type class="int"/>
</parameter>
<parameter name="a">
<type class="java.lang.Object"/>
</parameter>
<parameter name="b">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c">
<type class="java.lang.Object"/>
</parameter>
<parameter name="d">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="java.lang.Object"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.isModuleOrd(javax.baja.naming.BOrd) -->
<method name="isModuleOrd"  protected="true">
<description/>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.getModule() -->
<method name="getModule"  public="true">
<description/>
<return>
<parameterizedType class="java.util.Optional">
<args>
<type class="javax.baja.sys.BModule"/>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.getInputStream(java.util.zip.ZipEntry) -->
<method name="getInputStream"  public="true">
<description/>
<parameter name="zipEntry">
<type class="java.util.zip.ZipEntry"/>
</parameter>
<return>
<type class="java.io.InputStream"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.zip.BZipSpace.getDirectory() -->
<method name="getDirectory"  public="true">
<description>
Get top level directory of files in zip.
</description>
<return>
<type class="javax.baja.file.BDirectory"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.isModule() -->
<method name="isModule"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.makeDir(javax.baja.file.FilePath, javax.baja.sys.Context) -->
<method name="makeDir"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Throw IOException
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.file.BDirectory"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.zip.BZipSpace.makeFile(javax.baja.file.FilePath, javax.baja.sys.Context) -->
<method name="makeFile"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Throw IOException
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.zip.BZipSpace.move(javax.baja.file.FilePath, javax.baja.file.FilePath, javax.baja.sys.Context) -->
<method name="move"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Throw IOException
</description>
<parameter name="from">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="to">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.zip.BZipSpace.delete(javax.baja.file.FilePath, javax.baja.sys.Context) -->
<method name="delete"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Throw IOException
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.zip.BZipSpace.listFiles() -->
<method name="listFiles"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return root files.
</description>
<return>
<type class="javax.baja.file.BIFile" dimension="1"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.findFile(javax.baja.file.FilePath) -->
<method name="findFile"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Map the file path to an instance of BZipFileStore.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.findStore(javax.baja.file.FilePath) -->
<method name="findStore"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Map the file path to an instance of BZipFileStore.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.BIFileStore"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.getChild(javax.baja.file.BIFile, java.lang.String) -->
<method name="getChild"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the child of the specified directory or return null.
</description>
<parameter name="dir">
<type class="javax.baja.file.BIFile"/>
</parameter>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.getChildren(javax.baja.file.BIFile) -->
<method name="getChildren"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the children of the specified directory or&#xa; return an empty array.
</description>
<parameter name="dir">
<type class="javax.baja.file.BIFile"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile" dimension="1"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.appendFilePathToOrd(javax.baja.naming.BOrd, javax.baja.file.FilePath) -->
<method name="appendFilePathToOrd"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Join a file space ord with the given file path.  This relies on&#xa; the fact that the a file space ord should not end with slash, and&#xa; a filepath should.
</description>
<parameter name="baseOrd">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<parameter name="filePath">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.getOrdInSession() -->
<method name="getOrdInSession"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get ord passed to constructor.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.getNavDescription(javax.baja.sys.Context) -->
<method name="getNavDescription"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get short description.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.hasNavChildren() -->
<method name="hasNavChildren"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return true.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.getNavChild(java.lang.String) -->
<method name="getNavChild"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get a root by name.
</description>
<parameter name="navName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.nav.BINavNode"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.getNavChildren() -->
<method name="getNavChildren"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;listFiles()&lt;/code&gt;
</description>
<return>
<type class="javax.baja.nav.BINavNode" dimension="1"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.getHost() -->
<method name="getHost"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.naming.BHost"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.getSession() -->
<method name="getSession"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.naming.BISession"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.mapToDirectory(javax.baja.file.FilePath) -->
<method name="mapToDirectory"  protected="true">
<description>
Walk the path until we get to the parent directory.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.BDirectory"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipSpace.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.file.zip.BZipSpace.log -->
<field name="log"  protected="true" static="true" final="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

</class>
</bajadoc>
