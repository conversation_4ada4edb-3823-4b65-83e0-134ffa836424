<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.rsp.BInfinityReadPointResponse" name="BInfinityReadPointResponse" packageName="com.tridium.andoverInfinity.comm.rsp" public="true">
<description>
Response class to parse a response string into an BStatusValue
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.comm.rsp.BDdfResponse"/>
</extends>
<implements>
<type class="com.tridium.ddf.comm.rsp.BIDdfReadResponse"/>
</implements>
<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityReadPointResponse() -->
<constructor name="BInfinityReadPointResponse" public="true">
<description>
Default constructor
</description>
</constructor>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityReadPointResponse(java.lang.String) -->
<constructor name="BInfinityReadPointResponse" public="true">
<parameter name="txt">
<type class="java.lang.String"/>
</parameter>
<description>
Constructor with String answer.  The answer is actually a line from the network&#x27;s &#xa; screen buffer in command line mode.
</description>
</constructor>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityReadPointResponse.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityReadPointResponse.parseReadValue(com.tridium.ddf.comm.req.IDdfReadable) -->
<method name="parseReadValue"  public="true">
<description>
Parse the answer into a BStatusValue to set as the proxy ext value
</description>
<tag name="@see">com.tridium.devDriver.comm.rsp.BIDdfReadResponse#parseReadValue(com.tridium.devDriver.comm.req.IDevReadable)</tag>
<parameter name="readableSource">
<type class="com.tridium.ddf.comm.req.IDdfReadable"/>
</parameter>
<return>
<type class="javax.baja.status.BStatusValue"/>
<description>
a &lt;code&gt;BStatusValue&lt;/code&gt; appropriate for the base control point type.
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityReadPointResponse.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
