<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.fox.BIFoxAlarmDatabase" name="BIFoxAlarmDatabase" packageName="com.tridium.alarm.fox" public="true" interface="true" abstract="true" category="interface">
<description>
Interface for proxy BAlarmDatabases implementation over FOX
</description>
<tag name="@since">Niagara 4.11</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
</class>
</bajadoc>
