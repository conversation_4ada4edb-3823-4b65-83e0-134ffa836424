<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.Path" name="Path" packageName="javax.baja.naming" public="true" interface="true" abstract="true" category="interface">
<description>
Path is the interface implemented by OrdQueries that contain a path in a hierarchical naming&#xa; system.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">4 Jan 03</tag>
<tag name="@version">$Revision: 3$ $Date: 3/28/05 9:23:01 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<!-- javax.baja.naming.Path.getParentPath() -->
<method name="getParentPath"  public="true" abstract="true">
<description>
Get the parent path or null if there is no parent.
</description>
<return>
<type class="javax.baja.naming.Path"/>
</return>
</method>

<!-- javax.baja.naming.Path.depth() -->
<method name="depth"  public="true" abstract="true">
<description>
Get the number of names in the path.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.naming.Path.nameAt(int) -->
<method name="nameAt"  public="true" abstract="true">
<description>
Get the name at the zero based index between 0 and depth()-1.
</description>
<parameter name="depth">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.Path.getNames() -->
<method name="getNames"  public="true" abstract="true">
<description>
Get a copy of the names array.
</description>
<return>
<type class="java.lang.String" dimension="1"/>
</return>
</method>

<!-- javax.baja.naming.Path.getBackupDepth() -->
<method name="getBackupDepth"  public="true" default="true">
<description>
Get the number of relative backups.  If this path is absolute or directory relative then return&#xa; zero.  The default implementation returns -1 to indicate that backups are not supported.
</description>
<tag name="@since">Niagara 4.3U1</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.naming.Path.makePath(java.lang.String) -->
<method name="makePath"  public="true" default="true">
<description>
Creates a new OrdQuery instance for the given body. Each implementer of this interface should&#xa; override this method and create an instance of itself.
</description>
<tag name="@since">Niagara 4.3U1</tag>
<parameter name="body">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdQuery"/>
</return>
</method>

</class>
</bajadoc>
