<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.ui.portal.BPortalConsoleRecipient" name="BPortalConsoleRecipient" packageName="com.tridium.alarm.ui.portal" public="true">
<description>
BAlarmPortalTool is the component which serves as place &#xa; holder in the tools menu and declares the actual views.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">24 Jan 02</tag>
<tag name="@version">$Revision: 7$ $Date: 3/22/05 1:47:00 PM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="com.tridium.alarm.BConsoleRecipient"/>
</extends>
</class>
</bajadoc>
