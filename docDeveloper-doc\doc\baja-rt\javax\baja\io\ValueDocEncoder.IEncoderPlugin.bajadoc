<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.io.ValueDocEncoder$IEncoderPlugin" name="ValueDocEncoder.IEncoderPlugin" packageName="javax.baja.io" public="true" interface="true" abstract="true" static="true" innerClass="true" category="interface">
<description>
The document format can vary by objects implementing this interface
</description>
<implements>
<type class="java.lang.AutoCloseable"/>
</implements>
<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.encodeDocument(javax.baja.io.ValueDocEncoder, javax.baja.sys.BValue) -->
<method name="encodeDocument"  public="true" abstract="true">
<description/>
<parameter name="encoder">
<type class="javax.baja.io.ValueDocEncoder"/>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.start(java.lang.String) -->
<method name="start"  public="true" abstract="true">
<description/>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.startArray(java.lang.String) -->
<method name="startArray"  public="true" abstract="true">
<description/>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.endArray() -->
<method name="endArray"  public="true" abstract="true">
<description/>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.end() -->
<method name="end"  public="true" abstract="true">
<description/>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.end(java.lang.String) -->
<method name="end"  public="true" abstract="true">
<description/>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.endAttr() -->
<method name="endAttr"  public="true" abstract="true">
<description/>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.key(java.lang.String) -->
<method name="key"  public="true" abstract="true">
<description/>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.attr(java.lang.String, boolean) -->
<method name="attr"  public="true" abstract="true">
<description/>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<parameter name="val">
<type class="boolean"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.attr(java.lang.String, double) -->
<method name="attr"  public="true" abstract="true">
<description/>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<parameter name="val">
<type class="double"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.attr(java.lang.String, java.lang.String) -->
<method name="attr"  public="true" abstract="true">
<description/>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<parameter name="str">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.attrSafe(java.lang.String, java.lang.String) -->
<method name="attrSafe"  public="true" abstract="true">
<description/>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<parameter name="str">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.processEncodingException(javax.baja.sys.BObject, java.lang.Exception) -->
<method name="processEncodingException"  public="true" default="true">
<description>
Encoder will call this method when the encoding of an object fails with an exception
</description>
<tag name="@since">Niagara 4.0</tag>
<parameter name="object">
<type class="javax.baja.sys.BObject"/>
<description>
object whose encoding has failed
</description>
</parameter>
<parameter name="e">
<type class="java.lang.Exception"/>
<description>
exception that was thrown as a result of the encoding failure
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the exception was handled by the plugin and doesn&#x27;t need additional handling&#xa;         by the encoder.   If false, the exception is unhandled and will be logged and added&#xa;         to a list that can be retrieved using &lt;code&gt;<see ref="javax.baja.io.ValueDocEncoder#getUnhandledEncodingExceptions()">ValueDocEncoder#getUnhandledEncodingExceptions()</see>&lt;/code&gt;&#xa;         after encoding completes
</description>
</return>
<throws>
<type class="java.io.IOException"/>
<description>
will be thrown if the plugin determines that the entire encoding operation&#xa;         should fail
</description>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.value(java.lang.String) -->
<method name="value"  public="true" abstract="true">
<description/>
<parameter name="str">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.comment(java.lang.String) -->
<method name="comment"  public="true" abstract="true">
<description/>
<parameter name="text">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.incrementIndent() -->
<method name="incrementIndent"  public="true" abstract="true">
<description/>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.decrementIndent() -->
<method name="decrementIndent"  public="true" abstract="true">
<description/>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.indent() -->
<method name="indent"  public="true" abstract="true">
<description/>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.getIndent() -->
<method name="getIndent"  public="true" abstract="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.newLine() -->
<method name="newLine"  public="true" abstract="true">
<description/>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.encodeType(javax.baja.sys.Type) -->
<method name="encodeType"  public="true" abstract="true">
<description/>
<parameter name="type">
<type class="javax.baja.sys.Type"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.flush() -->
<method name="flush"  public="true" abstract="true">
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.close() -->
<method name="close"  public="true" abstract="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.isZipped() -->
<method name="isZipped"  public="true" abstract="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.setZipped(boolean) -->
<method name="setZipped"  public="true" abstract="true">
<description/>
<parameter name="zipped">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.version() -->
<method name="version"  public="true" default="true">
<description/>
<return>
<type class="javax.baja.util.Version"/>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.IEncoderPlugin.setVersion(javax.baja.util.Version) -->
<method name="setVersion"  public="true" default="true">
<description/>
<parameter name="version">
<type class="javax.baja.util.Version"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

</class>
</bajadoc>
