<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>webEditors Module: nmodule/webEditors/rc/fe/fe</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">webEditors</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-nmodule_webEditors_rc_fe_baja_BaseEditor.html">nmodule/webEditors/rc/fe/baja/BaseEditor</a></li><li><a href="module-nmodule_webEditors_rc_fe_BaseWidget.html">nmodule/webEditors/rc/fe/BaseWidget</a></li><li><a href="module-nmodule_webEditors_rc_fe_fe.html">nmodule/webEditors/rc/fe/fe</a></li><li><a href="module-nmodule_webEditors_rc_fe_feDialogs.html">nmodule/webEditors/rc/fe/feDialogs</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_commands_MgrCommand.html">nmodule/webEditors/rc/wb/mgr/commands/MgrCommand</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_Manager.html">nmodule/webEditors/rc/wb/mgr/Manager</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html">nmodule/webEditors/rc/wb/mgr/MgrLearn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrStateHandler.html">nmodule/webEditors/rc/wb/mgr/MgrStateHandler</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrTypeInfo.html">nmodule/webEditors/rc/wb/mgr/MgrTypeInfo</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_IconMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinPropMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_NameMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyPathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_TypeMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/MgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrModel.html">nmodule/webEditors/rc/wb/mgr/model/MgrModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">nmodule/webEditors/rc/wb/table/model/Column</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_DisplayNameColumn.html">nmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_IconColumn.html">nmodule/webEditors/rc/wb/table/model/columns/IconColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_JsonObjectPropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_PropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_ToStringColumn.html">nmodule/webEditors/rc/wb/table/model/columns/ToStringColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentSource.html">nmodule/webEditors/rc/wb/table/model/ComponentSource</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentTableModel.html">nmodule/webEditors/rc/wb/table/model/ComponentTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">nmodule/webEditors/rc/wb/table/model/Row</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html">nmodule/webEditors/rc/wb/table/model/TableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_Table.html">nmodule/webEditors/rc/wb/table/Table</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeNodeRow.html">nmodule/webEditors/rc/wb/table/tree/TreeNodeRow</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeTableModel.html">nmodule/webEditors/rc/wb/table/tree/TreeTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_tree_TreeNode.html">nmodule/webEditors/rc/wb/tree/TreeNode</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="tutorials.list.html" class="dropdown-toggle" data-toggle="dropdown">Tutorials<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="tutorial-6-managers.html">Managers</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: nmodule/webEditors/rc/fe/fe</h1>
<section>

<header>
    
</header>


<article>
    <div class="container-overview">
    
        
            <div class="description"><p>Functions for registering, looking up, and instantiating editors for<br>
certain Baja types.</p></div>
        

        
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


        
    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id=".buildFor"><span class="type-signature">&lt;static> </span>buildFor(params [, ed])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Instantiates an editor as in <code>makeFor</code>, but with the added steps of<br>
initializing and loading the editor. When the promise resolves, the<br>
editor will be initialized within the DOM, and the passed value will have<br>
been loaded into the editor.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_webEditors_rc_fe_fe.html#~FeParams">module:nmodule/webEditors/rc/fe/fe~FeParams</a></span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last">
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>initializeParams</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>any additional parameters to be<br>
passed to the editor's <code>initialize</code> method</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>loadParams</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>any additional parameters to be<br>
passed to the editor's <code>load</code> method</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    

        <tr>
            
                <td class="name"><code>ed</code></td>
            

            <td class="type">
            
                
<span class="param-type">module:bajaux/Widget</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>optionally,<br>
pass in an editor instance to just initialize and load that, skipping the<br>
<code>makeFor</code> step</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved with the<br>
instance of the editor (fully initialized and loaded), or rejected if<br>
invalid parameters are given (including missing <code>dom</code> parameter).</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise.&lt;module:bajaux/Widget></span>



    </dd>
</dl>


        

    
        <h5>Examples</h5>
        
        <p class="code-caption">Build a raw editor for a String</p>
    
    <pre class="sunlight-highlight-javascript">fe.buildFor({
    value: &#x27;my string&#x27;,
    properties: { multiLine: true },
    dom: $(&#x27;#myStringEditorDiv&#x27;)
  }).then(function (editor) {
    //editor is now fully initialized and loaded
  });</pre>

        <p class="code-caption">Build an editor for a slot on a component</p>
    
    <pre class="sunlight-highlight-javascript">var myComponent = baja.$(&#x27;baja:Component&#x27;, { mySlot: &#x27;hello world&#x27; });

  fe.buildFor({
    complex: myComponent,
    slot: &#x27;mySlot&#x27;,
    properties: { multiLine: true },
    dom: ${&#x27;#myStringSlotEditorDiv&#x27;)
  }).then(function (editor) {
    //editor is now fully initialized and loaded

    $(&#x27;#saveButton&#x27;).click(function () {
      editor.save().then(function () {
        alert(&#x27;your changes are applied to the component&#x27;);
      });
    });
  });</pre>

        <p class="code-caption">Build a StringEditor even though you plan to load a different
  kind of value into it.</p>
    
    <pre class="sunlight-highlight-javascript">fe.buildFor({
    type: StringEditor,
    value: 5,
    dom: $(&#x27;#myStringEditorDiv&#x27;)
  }).then(function (stringEditor) {
    //StringEditor better be able to load the value you specified,
    //or this will reject instead.
  });</pre>

        <p class="code-caption">Example showing the effects of the uxFieldEditor facet
  (BFacets.UX_FIELD_EDITOR). By setting this slot facet to the type spec
  of a `BIJavaScript` implementation, you can force the usage of a
  particular field editor instead of relying on the agent registration.
  </p>
    
    <pre class="sunlight-highlight-javascript">fe.buildFor({
    value: 5,
    dom: $(&#x27;#myStringEditorDiv&#x27;),
    properties: { uxFieldEditor: &#x27;webEditors:StringEditor&#x27; }
  }).then(function (stringEditor) {
    //uxFieldEditor facet enforced usage of StringEditor instead of the
    //default NumericEditor
  });</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".getConstructors"><span class="type-signature">&lt;static> </span>getConstructors(type [, params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Retrieve all available widget constructors for the given type.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>type</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Type</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last">
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>formFactors</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;String></span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved with an array<br>
of constructor functions.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise.&lt;Array.&lt;function()>></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".getDefaultConstructor"><span class="type-signature">&lt;static> </span>getDefaultConstructor(type [, params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Retrieve the <code>Widget</code> constructor function registered for the given Type.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>type</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Type</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last">
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>formFactors</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;(String|Number)></span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>describes the form<br>
factors that the resolved constructor is required to support. These can<br>
be Strings referencing a form factor property on<br>
<code>bajaux/Widget.formfactor</code>, or the value itself. If a constructor matches<br>
<em>any</em> of these form factors it will be returned (union, not intersection).</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>properties</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>pass the widget properties that can help<br>
determine 'a' Widget constructor. For example, 'uxFieldEditor'.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>a promise to be resolved with the constructor<br>
function for the given Type, or with <code>undefined</code> if no constructor is<br>
registered. Note that if an invalid RequireJS module ID was passed to<br>
<code>fe.register()</code>, it will still look up the supertype chain in an attempt<br>
to resolve <em>something</em>.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise.&lt;function()></span>



    </dd>
</dl>


        

    
        <h5>Examples</h5>
        
    <pre class="sunlight-highlight-javascript">function StringEditor() {} //extends Widget
  fe.register(&#x27;baja:String&#x27;, StringEditor, {
    formFactors: [ Widget.formfactor.mini ]
  });

  //resolves StringEditor
  fe.getDefaultConstructor(&#x27;baja:String&#x27;, { formFactors: [ &#x27;mini&#x27; ] });

  //resolves undefined
  fe.getDefaultConstructor(&#x27;baja:String&#x27;, { formFactors: [ &#x27;compact&#x27; ] });</pre>

    <pre class="sunlight-highlight-javascript">// Will return myModule&#x27;s SpecialNumericEditor instead of the default webEditors:NumericEditor
  fe.getDefaultConstructor(&#x27;baja:Double&#x27;, { properties: { uxFieldEditor: &#x27;myModule:SpecialNumericEditor&#x27; } });</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".makeFor"><span class="type-signature">&lt;static> </span>makeFor(params)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Instantiate a new editor for a value of a particular Type.</p>
<p>Note that you will receive a constructed instance of the editor, but<br>
it is uninitialized - calling <code>instantiate()</code> and <code>load()</code> is still your<br>
job. (See <code>buildFor</code>.)</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_webEditors_rc_fe_fe.html#~FeParams">module:nmodule/webEditors/rc/fe/fe~FeParams</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved with an<br>
editor instance, or rejected if invalid parameters are given.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise.&lt;module:bajaux/Widget></span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
        <p class="code-caption">Instantiate an editor for a baja value. Note that the workflow
  below is easily simplified by using fe.buildFor() instead.</p>
    
    <pre class="sunlight-highlight-javascript">var myString = &#x27;my string&#x27;;
  fe.makeFor({
    value: myString
    properties: { multiLine: true }
  }).then(function (editor) {
    return editor.initialize($(&#x27;#myStringEditorDiv&#x27;))
     .then(function () {
       return editor.load(myString);
     });
  });</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".register"><span class="type-signature">&lt;static> </span>register(type, module [, params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Registers a RequireJS module to a baja Type. This takes a RequireJS<br>
module ID string which resolves to a module exporting a constructor for a<br>
Widget subclass.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>type</code></td>
            

            <td class="type">
            
                
<span class="param-type">Type</span>
|

<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>module</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>RequireJS module ID</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last">
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>formFactors</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;String></span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>form factors that this editor<br>
should support</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved after the module<br>
registration is complete. Note that <code>getDefaultConstructor()</code>, <code>makeFor()</code>,<br>
etc. will still work (with some possible extra network calls) before the<br>
promise is fully resolved. Promise will be rejected if RequireJS is unable<br>
to resolve a given module ID.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
        <p class="code-caption">Register StringEditor on baja:String, so that it can be used to
  build "mini" editors for Strings.</p>
    
    <pre class="sunlight-highlight-javascript">fe.register(&#x27;baja:String&#x27;, &#x27;nmodule/webEditors/rc/fe/baja/StringEditor&#x27;, {
    formFactors: [ Widget.formfactor.mini ]
  });</pre>


    
</dd>

        </dl>
    

    
        <h3 class="subsection-title">Type Definitions</h3>

        <dl>
                
<hr>
<dt class="name" id="~FeParams">
    <h4 id="~FeParams">FeParams</h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>This type describes the available parameters to be passed to the various<br>
methods on <code>fe</code>. These values will be used both to look up the type of the<br>
desired editor, and also to construct that editor. In other words, the<br>
data to look up a widget will also be used in the same turn to construct<br>
that widget. See module:bajaux/Widget</p>
    </div>
    

    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">module:bajaux/lifecycle/WidgetManager~BuildParams</span>
|

<span class="param-type">FeSpecificParams</span>



            </li>
        </ul>
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

            </dl>
    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	webEditors Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:59+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>