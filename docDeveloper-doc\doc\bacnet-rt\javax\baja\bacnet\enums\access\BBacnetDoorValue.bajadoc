<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.access.BBacnetDoorValue" name="BBacnetDoorValue" packageName="javax.baja.bacnet.enums.access" public="true" final="true">
<description>
BBacnetDoorValue represents the Bacnet&#xa; Bacnet Door Value enumeration.&#xa; &lt;p&gt;&#xa; BBacnetDoorValue is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Joseph Chandler</tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lock&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unlock&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;pulseUnlock&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;extendedPulseUnlock&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetDoorValue"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetDoorValue"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.LOCK -->
<field name="LOCK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lock.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.UNLOCK -->
<field name="UNLOCK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unlock.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.PULSE_UNLOCK -->
<field name="PULSE_UNLOCK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for pulseUnlock.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.EXTENDED_PULSE_UNLOCK -->
<field name="EXTENDED_PULSE_UNLOCK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for extendedPulseUnlock.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.lock -->
<field name="lock"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetDoorValue"/>
<description>
BBacnetDoorValue constant for lock.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.unlock -->
<field name="unlock"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetDoorValue"/>
<description>
BBacnetDoorValue constant for unlock.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.pulseUnlock -->
<field name="pulseUnlock"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetDoorValue"/>
<description>
BBacnetDoorValue constant for pulseUnlock.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.extendedPulseUnlock -->
<field name="extendedPulseUnlock"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetDoorValue"/>
<description>
BBacnetDoorValue constant for extendedPulseUnlock.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetDoorValue"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetDoorValue.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
