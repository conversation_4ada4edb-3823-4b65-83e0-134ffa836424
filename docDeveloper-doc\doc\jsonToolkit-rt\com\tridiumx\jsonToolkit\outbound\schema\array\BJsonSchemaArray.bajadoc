<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.array.BJsonSchemaArray" name="BJsonSchemaArray" packageName="com.tridiumx.jsonToolkit.outbound.schema.array" public="true">
<description>
A named array container for other &lt;code&gt;<see ref="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaMember">BJsonSchemaMember</see>&lt;/code&gt; children.&#xa; This is for instances when a json array itself does not need to be bound to any station value but just&#xa; needs a named wrapper for other values which are.&#xa; So without any &lt;code&gt;<see ref="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaMember">BJsonSchemaMember</see>&lt;/code&gt; children this will render as name: []
</description>
<tag name="@author">Nick Dodd</tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaMember"/>
</extends>
<implements>
<type class="com.tridiumx.jsonToolkit.outbound.schema.BIJsonArray"/>
</implements>
<!-- com.tridiumx.jsonToolkit.outbound.schema.array.BJsonSchemaArray() -->
<constructor name="BJsonSchemaArray" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.array.BJsonSchemaArray.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.array.BJsonSchemaArray.getJsonName() -->
<method name="getJsonName"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.array.BJsonSchemaArray.populateArrayContent(com.tridium.json.JSONWriter) -->
<method name="populateArrayContent"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="json">
<type class="com.tridium.json.JSONWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.array.BJsonSchemaArray.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.array.BJsonSchemaArray.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
