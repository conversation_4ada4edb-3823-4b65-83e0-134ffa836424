<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.program.BAceAppDownloadJob" name="BAceAppDownloadJob" packageName="com.tridium.ace.program" public="true">
<description>
BAceAppDownloadJob download app file to device list and restart devices
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">5/10/2017</tag>
<extends>
<type class="javax.baja.job.BSimpleJob"/>
</extends>
<action name="getMissingModules" flags="h">
<return>
<type class="javax.baja.sys.BString"/>
</return>
<description>
Slot for the &lt;code&gt;getMissingModules&lt;/code&gt; action.
</description>
<tag name="@see">#getMissingModules()</tag>
</action>

</class>
</bajadoc>
