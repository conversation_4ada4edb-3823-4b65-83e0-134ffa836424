<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="backup" runtimeProfile="rt" name="javax.baja.backup">
<description/>
<class packageName="javax.baja.backup" name="BBackupService"><description>BBackupService is used to define the files included in a&#xa; configuration backup such as config.bog and supporting static&#xa; files such as px, html, png, and jpegs.</description></class>
<class packageName="javax.baja.backup" name="BBackupService.JobCanceler"/>
<class packageName="javax.baja.backup" name="BBackupService.RestoreThread"/>
<class packageName="javax.baja.backup" name="BBackupService.ICanceler" category="interface"/>
</package>
</bajadoc>
