<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnetAws" runtimeProfile="rt" name="com.tridium.bacnetAws.datatypes">
<description/>
<class packageName="com.tridium.bacnetAws.datatypes" name="BBackupConfig"><description>BBackupConfig represents the choices for the&#xa; user in the Backup procedure.</description></class>
<class packageName="com.tridium.bacnetAws.datatypes" name="BCommControlConfig"><description>This class file specifies parameters to constrain a&#xa; Device Communication Control request.</description></class>
<class packageName="com.tridium.bacnetAws.datatypes" name="BCreateObjectParameters"><description>Create Object Parameters transfer the data from &#xa;  the WB to the Station providing the creation job &#xa;  the required information.</description></class>
<class packageName="com.tridium.bacnetAws.datatypes" name="BObjectParameters"/>
<class packageName="com.tridium.bacnetAws.datatypes" name="BReinitializeDeviceConfig"><description>BReinitializeDeviceConfig represents the choices for the&#xa; user in manually issuing a ReinitializeDevice-Request to a device.</description></class>
<class packageName="com.tridium.bacnetAws.datatypes" name="BRestoreConfig"><description>BRestoreConfig represents the choices for the&#xa; user in the Restore procedure.</description></class>
</package>
</bajadoc>
