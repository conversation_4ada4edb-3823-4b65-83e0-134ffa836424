<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetLifeSafetyState" name="BBacnetLifeSafetyState" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetLifeSafetyState represents the Bacnet Life Safety State&#xa; enumeration.&#xa; &lt;p&gt;&#xa; BBacnetLifeSafetyState is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-255 are reserved for use by ASHRAE.&#xa; Values from 256-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Craig Gemmill</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">16 May 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;quiet&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;preAlarm&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;alarm&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;fault&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;faultPreAlarm&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;faultAlarm&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;notReady&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;active&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tamper&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;testAlarm&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;testActive&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;testFault&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;testFaultAlarm&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;holdup&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;duress&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tamperAlarm&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;abnormal&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;emergencyPower&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;delayed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;blocked&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;localAlarm&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;generalAlarm&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;supervisory&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;testSupervisory&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.QUIET -->
<field name="QUIET"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for quiet.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.PRE_ALARM -->
<field name="PRE_ALARM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for preAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.ALARM -->
<field name="ALARM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for alarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.FAULT -->
<field name="FAULT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for fault.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.FAULT_PRE_ALARM -->
<field name="FAULT_PRE_ALARM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for faultPreAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.FAULT_ALARM -->
<field name="FAULT_ALARM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for faultAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.NOT_READY -->
<field name="NOT_READY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for notReady.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.ACTIVE -->
<field name="ACTIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for active.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.TAMPER -->
<field name="TAMPER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tamper.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.TEST_ALARM -->
<field name="TEST_ALARM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for testAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.TEST_ACTIVE -->
<field name="TEST_ACTIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for testActive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.TEST_FAULT -->
<field name="TEST_FAULT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for testFault.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.TEST_FAULT_ALARM -->
<field name="TEST_FAULT_ALARM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for testFaultAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.HOLDUP -->
<field name="HOLDUP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for holdup.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.DURESS -->
<field name="DURESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for duress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.TAMPER_ALARM -->
<field name="TAMPER_ALARM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tamperAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.ABNORMAL -->
<field name="ABNORMAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for abnormal.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.EMERGENCY_POWER -->
<field name="EMERGENCY_POWER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for emergencyPower.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.DELAYED -->
<field name="DELAYED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for delayed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.BLOCKED -->
<field name="BLOCKED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for blocked.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.LOCAL_ALARM -->
<field name="LOCAL_ALARM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for localAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.GENERAL_ALARM -->
<field name="GENERAL_ALARM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for generalAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.SUPERVISORY -->
<field name="SUPERVISORY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for supervisory.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.TEST_SUPERVISORY -->
<field name="TEST_SUPERVISORY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for testSupervisory.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.quiet -->
<field name="quiet"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for quiet.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.preAlarm -->
<field name="preAlarm"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for preAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.alarm -->
<field name="alarm"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for alarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.fault -->
<field name="fault"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for fault.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.faultPreAlarm -->
<field name="faultPreAlarm"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for faultPreAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.faultAlarm -->
<field name="faultAlarm"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for faultAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.notReady -->
<field name="notReady"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for notReady.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.active -->
<field name="active"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for active.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.tamper -->
<field name="tamper"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for tamper.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.testAlarm -->
<field name="testAlarm"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for testAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.testActive -->
<field name="testActive"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for testActive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.testFault -->
<field name="testFault"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for testFault.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.testFaultAlarm -->
<field name="testFaultAlarm"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for testFaultAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.holdup -->
<field name="holdup"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for holdup.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.duress -->
<field name="duress"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for duress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.tamperAlarm -->
<field name="tamperAlarm"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for tamperAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.abnormal -->
<field name="abnormal"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for abnormal.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.emergencyPower -->
<field name="emergencyPower"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for emergencyPower.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.delayed -->
<field name="delayed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for delayed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.blocked -->
<field name="blocked"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for blocked.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.localAlarm -->
<field name="localAlarm"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for localAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.generalAlarm -->
<field name="generalAlarm"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for generalAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.supervisory -->
<field name="supervisory"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for supervisory.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.testSupervisory -->
<field name="testSupervisory"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description>
BBacnetLifeSafetyState constant for testSupervisory.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyState"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyState.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
