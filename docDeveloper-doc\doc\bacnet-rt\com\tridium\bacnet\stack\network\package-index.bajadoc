<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="com.tridium.bacnet.stack.network">
<description/>
<class packageName="com.tridium.bacnet.stack.network" name="BBacnetNetworkLayer"><description>Tridium Network Layer Implementation.</description></class>
<class packageName="com.tridium.bacnet.stack.network" name="BBacnetRouterEntry"><description>BBacnetRouterEntry represents an entry in the&#xa; router table of the Bacnet network layer.</description></class>
<class packageName="com.tridium.bacnet.stack.network" name="BBacnetRouterTable"><description>BBacnetRouterTable stores the table of known routers to&#xa; Bacnet networks.</description></class>
<class packageName="com.tridium.bacnet.stack.network" name="BNetworkPort"><description><PERSON>les sending and receiving messages to and from the&#xa; link layer.</description></class>
<class packageName="com.tridium.bacnet.stack.network" name="BNetworkPriority"><description>BNetworkPriority represents the choice of priority level&#xa; at the Network layer.</description></class>
<class packageName="com.tridium.bacnet.stack.network" name="BRouterStatus"><description>BRouterStatus represents the various health states&#xa; that a router connection can take.</description></class>
</package>
</bajadoc>
