<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BIRemoteAlarmSource" name="BIRemoteAlarmSource" packageName="javax.baja.alarm" public="true" interface="true" abstract="true" category="interface">
<description>
The BIRemoteAlarmSource is a marker interface that is implemented by BObjects &#xa; that handle alarms sent between stations. &#xa; &lt;p&gt;&#xa; ack<PERSON>lar<PERSON> should return immediately after sucessfully sending the alarm &#xa; to the remote station. BAlarmService.routeAlarm() should then be called &#xa; when the ackNotification is received from the remote station.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">19 Jan 04</tag>
<tag name="@version">$Revision: 2$ $Date: 3/30/05 11:35:59 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<implements>
<type class="javax.baja.alarm.BIAlarmSource"/>
</implements>
<!-- javax.baja.alarm.BIRemoteAlarmSource.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
