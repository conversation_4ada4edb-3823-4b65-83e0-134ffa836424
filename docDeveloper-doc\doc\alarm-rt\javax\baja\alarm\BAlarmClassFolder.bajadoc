<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BAlarmClassFolder" name="BAlarmClassFolder" packageName="javax.baja.alarm" public="true">
<description>
BAlarmClassFolder is a Folder for grouping AlarmClasses under the AlarmService.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">10 Jun 08</tag>
<tag name="@version">$Revision: 5$ $Date: 11/3/10 9:49:23 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.util.BFolder"/>
</extends>
<implements>
<type class="javax.baja.alarm.BIAlarmClassFolder"/>
</implements>
<!-- javax.baja.alarm.BAlarmClassFolder() -->
<constructor name="BAlarmClassFolder" public="true">
<description/>
</constructor>

<!-- javax.baja.alarm.BAlarmClassFolder.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClassFolder.checkAdd(java.lang.String, javax.baja.sys.BValue, int, javax.baja.sys.BFacets, javax.baja.sys.Context) -->
<method name="checkAdd"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Disallow duplicate alarm classes
</description>
<parameter name="newName">
<type class="java.lang.String"/>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="flags">
<type class="int"/>
</parameter>
<parameter name="facets">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClassFolder.checkRename(javax.baja.sys.Property, java.lang.String, javax.baja.sys.Context) -->
<method name="checkRename"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Disallow duplicate alarm classes
</description>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="newName">
<type class="java.lang.String"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClassFolder.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="prop">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClassFolder.added(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="added"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="prop">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClassFolder.removed(javax.baja.sys.Property, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="removed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="prop">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="oldValue">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClassFolder.getAlarmService() -->
<method name="getAlarmService"  protected="true">
<description/>
<return>
<type class="javax.baja.alarm.BAlarmService"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClassFolder.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Only the BAlarmService or another BAlarmClassFolder is a legal parent.
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClassFolder.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the icon.
</description>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmClassFolder.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
