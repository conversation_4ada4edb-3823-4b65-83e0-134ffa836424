<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="andoverAC256" runtimeProfile="wb" name="com.tridium.andoverAC256.ui">
<description/>
<class packageName="com.tridium.andoverAC256.ui" name="BAndoverBackupManager"><description>BAndoverBackupManager.</description></class>
<class packageName="com.tridium.andoverAC256.ui" name="BAndoverConsole"><description>BAndoverConsole - This view provides the user with a dumb terminal into an AC256</description></class>
<class packageName="com.tridium.andoverAC256.ui" name="BAndoverDeviceManager"><description>BAndoverDeviceManager</description></class>
<class packageName="com.tridium.andoverAC256.ui" name="BAndoverPointManager"><description>BAndoverPointManager uses the BAbstractLearn framework to&#xa; provide a way for the user to create proxy points within&#xa; a BAndoverDevice.</description></class>
</package>
</bajadoc>
