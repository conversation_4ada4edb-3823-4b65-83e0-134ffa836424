<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.rsp.BInfinityWritePointResponse" name="BInfinityWritePointResponse" packageName="com.tridium.andoverInfinity.comm.rsp" public="true">
<description>
/**&#xa; BInfinityWritePointResponse is used to complete a write point transaction
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.comm.rsp.BDdfResponse"/>
</extends>
<implements>
<type class="com.tridium.ddf.comm.rsp.BIDdfReadResponse"/>
</implements>
<property name="valueWritten" flags="">
<type class="javax.baja.status.BStatusValue"/>
<description>
Slot for the &lt;code&gt;valueWritten&lt;/code&gt; property.&#xa; the value just written
</description>
<tag name="@see">#getValueWritten</tag>
<tag name="@see">#setValueWritten</tag>
</property>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityWritePointResponse() -->
<constructor name="BInfinityWritePointResponse" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityWritePointResponse.getValueWritten() -->
<method name="getValueWritten"  public="true">
<description>
Get the &lt;code&gt;valueWritten&lt;/code&gt; property.&#xa; the value just written
</description>
<tag name="@see">#valueWritten</tag>
<return>
<type class="javax.baja.status.BStatusValue"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityWritePointResponse.setValueWritten(javax.baja.status.BStatusValue) -->
<method name="setValueWritten"  public="true">
<description>
Set the &lt;code&gt;valueWritten&lt;/code&gt; property.&#xa; the value just written
</description>
<tag name="@see">#valueWritten</tag>
<parameter name="v">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityWritePointResponse.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityWritePointResponse.parseReadValue(com.tridium.ddf.comm.req.IDdfReadable) -->
<method name="parseReadValue"  public="true">
<description>
Implementing this method is fundamental to the ddf&#x27;s auto-poll feature for&#xa; driver points. When one or more driver points under a device need polled&#xa; that share the equivalent read parameters, the ddf will instantiate the&#xa; read request type that is identified by the read parameters, assign the&#xa; read paraters to the read request, assign all points that share the read&#xa; parameters to the request, and transmit the read request. Upon receiving&#xa; a successful read response (an instance that implements this interface),&#xa; the ddf driver framework will loop through all of the points under the&#xa; device that shared the same read parameters, cast each point to&#xa; IDdfReadable, and pass each point successively to this method. The ddf&#xa; driver framework will take the return value from this method and pass it&#xa; to the readOk method on the point, thereby updating its value in Niagara.&#xa; When implenting this interface, driver devlopers must implement this&#xa; method and parse a BStatusValue from the response data for the given&#xa; readableSource. If necessary, we suggest that the driver developer can &#xa; check if the readableSource object is an instance of their driver&#x27;s&#xa; proxy extension class. If so, the driver developer can cast the &#xa; readableSource object to their driver&#x27;s proxy extension class and then&#xa; access the point&#x27;s pointId property. The driver developer should design&#xa; the pointId property in such a way that it provides the information&#xa; necessary to parse the particular point&#x27;s value from the read response.&#xa;  &#xa; For Infinity, the value is parsed by the processReceive method of &#xa; the BInfinityWritePointRequest, and the parsed value is set into this&#xa; class using the setValueWritten method.
</description>
<parameter name="readableSource">
<type class="com.tridium.ddf.comm.req.IDdfReadable"/>
</parameter>
<return>
<type class="javax.baja.status.BStatusValue"/>
<description>
the value set using &lt;code&gt;setValueWritten&lt;/code&gt; method
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityWritePointResponse.valueWritten -->
<field name="valueWritten"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;valueWritten&lt;/code&gt; property.&#xa; the value just written
</description>
<tag name="@see">#getValueWritten</tag>
<tag name="@see">#setValueWritten</tag>
</field>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityWritePointResponse.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
