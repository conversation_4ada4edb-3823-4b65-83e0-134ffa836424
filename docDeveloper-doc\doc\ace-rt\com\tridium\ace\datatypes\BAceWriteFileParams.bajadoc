<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.datatypes.BAceWriteFileParams" name="BAceWriteFileParams" packageName="com.tridium.ace.datatypes" public="true">
<description>
BAceWriteFileParams - is container for properties past in download file action
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">5/9/2017</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="fileNameInDevice" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;fileNameInDevice&lt;/code&gt; property.
</description>
<tag name="@see">#getFileNameInDevice</tag>
<tag name="@see">#setFileNameInDevice</tag>
</property>

<property name="fileToWrite" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;fileToWrite&lt;/code&gt; property.
</description>
<tag name="@see">#getFileToWrite</tag>
<tag name="@see">#setFileToWrite</tag>
</property>

</class>
</bajadoc>
