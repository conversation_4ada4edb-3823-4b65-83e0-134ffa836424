<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.server.BBacnetServerLayer" name="BBacnetServerLayer" packageName="com.tridium.bacnet.stack.server" public="true">
<description>
Tridium Server Application Layer Implementation.&#xa; &lt;p&gt;&#xa; BBacnetServerLayer represents the server side of the application&#xa; layer of the Bacnet communications stack.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 4$ $Date: 12/19/01 4:35:30 PM$</tag>
<tag name="@creation">22 Aug 00</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConfirmedServiceChoice"/>
</implements>
<implements>
<type class="javax.baja.bacnet.BacnetUnconfirmedServiceChoice"/>
</implements>
<property name="worker" flags="">
<type class="javax.baja.bacnet.util.BBacnetWorker"/>
<description>
Slot for the &lt;code&gt;worker&lt;/code&gt; property.
</description>
<tag name="@see">#getWorker</tag>
<tag name="@see">#setWorker</tag>
</property>

<property name="eventHandler" flags="">
<type class="com.tridium.bacnet.stack.server.BEventHandler"/>
<description>
Slot for the &lt;code&gt;eventHandler&lt;/code&gt; property.
</description>
<tag name="@see">#getEventHandler</tag>
<tag name="@see">#setEventHandler</tag>
</property>

<property name="reinitializeAllowed" flags="h">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;reinitializeAllowed&lt;/code&gt; property.
</description>
<tag name="@see">#getReinitializeAllowed</tag>
<tag name="@see">#setReinitializeAllowed</tag>
</property>

<property name="timeSynchAllowed" flags="h">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;timeSynchAllowed&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeSynchAllowed</tag>
<tag name="@see">#setTimeSynchAllowed</tag>
</property>

<property name="updateStatusOnCov" flags="h">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;updateStatusOnCov&lt;/code&gt; property.
</description>
<tag name="@see">#getUpdateStatusOnCov</tag>
<tag name="@see">#setUpdateStatusOnCov</tag>
</property>

<property name="issueUnicastIHave" flags="h">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;issueUnicastIHave&lt;/code&gt; property.
</description>
<tag name="@see">#getIssueUnicastIHave</tag>
<tag name="@see">#setIssueUnicastIHave</tag>
</property>

<property name="confirmedWorker" flags="">
<type class="javax.baja.bacnet.util.BBacnetWorker"/>
<description>
Slot for the &lt;code&gt;confirmedWorker&lt;/code&gt; property.
</description>
<tag name="@see">#getConfirmedWorker</tag>
<tag name="@see">#setConfirmedWorker</tag>
</property>

<property name="objectHandler" flags="">
<type class="com.tridium.bacnet.stack.server.object.BObjectHandler"/>
<description>
Slot for the &lt;code&gt;objectHandler&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectHandler</tag>
<tag name="@see">#setObjectHandler</tag>
</property>

<property name="overrideMode" flags="h">
<type class="com.tridium.bacnet.stack.server.BOverrideMode"/>
<description>
Slot for the &lt;code&gt;overrideMode&lt;/code&gt; property.
</description>
<tag name="@see">#getOverrideMode</tag>
<tag name="@see">#setOverrideMode</tag>
</property>

<action name="checkBackupComm" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;checkBackupComm&lt;/code&gt; action.
</description>
<tag name="@see">#checkBackupComm()</tag>
</action>

</class>
</bajadoc>
