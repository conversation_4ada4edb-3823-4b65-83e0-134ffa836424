<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BBacnetTrendLog" name="BBacnetTrendLog" packageName="javax.baja.bacnet.config" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@creation">30 Jan 01</tag>
<tag name="@version">$Revision: 7$ $Date: 12/10/01 9:26:02 AM$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.config.BBacnetCreatableObject"/>
</extends>
<property name="logEnable" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;logEnable&lt;/code&gt; property.
</description>
<tag name="@see">#getLogEnable</tag>
<tag name="@see">#setLogEnable</tag>
</property>

<property name="stopWhenFull" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;stopWhenFull&lt;/code&gt; property.
</description>
<tag name="@see">#getStopWhenFull</tag>
<tag name="@see">#setStopWhenFull</tag>
</property>

<property name="bufferSize" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;bufferSize&lt;/code&gt; property.
</description>
<tag name="@see">#getBufferSize</tag>
<tag name="@see">#setBufferSize</tag>
</property>

<property name="recordCount" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;recordCount&lt;/code&gt; property.
</description>
<tag name="@see">#getRecordCount</tag>
<tag name="@see">#setRecordCount</tag>
</property>

<property name="totalRecordCount" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;totalRecordCount&lt;/code&gt; property.
</description>
<tag name="@see">#getTotalRecordCount</tag>
<tag name="@see">#setTotalRecordCount</tag>
</property>

<property name="notifyType" flags="">
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
<description>
Slot for the &lt;code&gt;notifyType&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getNotifyType</tag>
<tag name="@see">#setNotifyType</tag>
</property>

<property name="notificationClass" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;notificationClass&lt;/code&gt; property.&#xa; references a Notification Class in the same device that specifies the&#xa; handling, reporting, and acknowledgment characteristics for this object.
</description>
<tag name="@see">#getNotificationClass</tag>
<tag name="@see">#setNotificationClass</tag>
</property>

<property name="objectPropertyReference" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
<description>
Slot for the &lt;code&gt;objectPropertyReference&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectPropertyReference</tag>
<tag name="@see">#setObjectPropertyReference</tag>
</property>

<property name="eventState" flags="r">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getEventState</tag>
<tag name="@see">#setEventState</tag>
</property>

<!-- javax.baja.bacnet.config.BBacnetTrendLog() -->
<constructor name="BBacnetTrendLog" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.getLogEnable() -->
<method name="getLogEnable"  public="true">
<description>
Get the &lt;code&gt;logEnable&lt;/code&gt; property.
</description>
<tag name="@see">#logEnable</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.setLogEnable(boolean) -->
<method name="setLogEnable"  public="true">
<description>
Set the &lt;code&gt;logEnable&lt;/code&gt; property.
</description>
<tag name="@see">#logEnable</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.getStopWhenFull() -->
<method name="getStopWhenFull"  public="true">
<description>
Get the &lt;code&gt;stopWhenFull&lt;/code&gt; property.
</description>
<tag name="@see">#stopWhenFull</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.setStopWhenFull(boolean) -->
<method name="setStopWhenFull"  public="true">
<description>
Set the &lt;code&gt;stopWhenFull&lt;/code&gt; property.
</description>
<tag name="@see">#stopWhenFull</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.getBufferSize() -->
<method name="getBufferSize"  public="true">
<description>
Get the &lt;code&gt;bufferSize&lt;/code&gt; property.
</description>
<tag name="@see">#bufferSize</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.setBufferSize(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setBufferSize"  public="true">
<description>
Set the &lt;code&gt;bufferSize&lt;/code&gt; property.
</description>
<tag name="@see">#bufferSize</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.getRecordCount() -->
<method name="getRecordCount"  public="true">
<description>
Get the &lt;code&gt;recordCount&lt;/code&gt; property.
</description>
<tag name="@see">#recordCount</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.setRecordCount(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setRecordCount"  public="true">
<description>
Set the &lt;code&gt;recordCount&lt;/code&gt; property.
</description>
<tag name="@see">#recordCount</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.getTotalRecordCount() -->
<method name="getTotalRecordCount"  public="true">
<description>
Get the &lt;code&gt;totalRecordCount&lt;/code&gt; property.
</description>
<tag name="@see">#totalRecordCount</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.setTotalRecordCount(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setTotalRecordCount"  public="true">
<description>
Set the &lt;code&gt;totalRecordCount&lt;/code&gt; property.
</description>
<tag name="@see">#totalRecordCount</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.getNotifyType() -->
<method name="getNotifyType"  public="true">
<description>
Get the &lt;code&gt;notifyType&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#notifyType</tag>
<return>
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.setNotifyType(javax.baja.bacnet.enums.BBacnetNotifyType) -->
<method name="setNotifyType"  public="true">
<description>
Set the &lt;code&gt;notifyType&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#notifyType</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.getNotificationClass() -->
<method name="getNotificationClass"  public="true">
<description>
Get the &lt;code&gt;notificationClass&lt;/code&gt; property.&#xa; references a Notification Class in the same device that specifies the&#xa; handling, reporting, and acknowledgment characteristics for this object.
</description>
<tag name="@see">#notificationClass</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.setNotificationClass(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setNotificationClass"  public="true">
<description>
Set the &lt;code&gt;notificationClass&lt;/code&gt; property.&#xa; references a Notification Class in the same device that specifies the&#xa; handling, reporting, and acknowledgment characteristics for this object.
</description>
<tag name="@see">#notificationClass</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.getObjectPropertyReference() -->
<method name="getObjectPropertyReference"  public="true">
<description>
Get the &lt;code&gt;objectPropertyReference&lt;/code&gt; property.
</description>
<tag name="@see">#objectPropertyReference</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.setObjectPropertyReference(javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference) -->
<method name="setObjectPropertyReference"  public="true">
<description>
Set the &lt;code&gt;objectPropertyReference&lt;/code&gt; property.
</description>
<tag name="@see">#objectPropertyReference</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.getEventState() -->
<method name="getEventState"  public="true">
<description>
Get the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#eventState</tag>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.setEventState(javax.baja.sys.BEnum) -->
<method name="setEventState"  public="true">
<description>
Set the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#eventState</tag>
<parameter name="v">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.shouldPoll(int) -->
<method name="shouldPoll"  protected="true">
<description>
Should this property ID be polled?&#xa; Override point for objects to filter properties for polling, e.g.,&#xa; Object_List in Device object, or Log_Buffer in Trend Log.
</description>
<parameter name="propertyId">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.getDisplayName(javax.baja.sys.Slot, javax.baja.sys.Context) -->
<method name="getDisplayName"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
This code was added to fix a UI issue raised by BTL.&#xa; The issue is articulated in the Jira entry NCCB-29907.
</description>
<parameter name="slot">
<type class="javax.baja.sys.Slot"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.addObjectInitialValues(javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addObjectInitialValues"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.logEnable -->
<field name="logEnable"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;logEnable&lt;/code&gt; property.
</description>
<tag name="@see">#getLogEnable</tag>
<tag name="@see">#setLogEnable</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.stopWhenFull -->
<field name="stopWhenFull"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;stopWhenFull&lt;/code&gt; property.
</description>
<tag name="@see">#getStopWhenFull</tag>
<tag name="@see">#setStopWhenFull</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.bufferSize -->
<field name="bufferSize"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;bufferSize&lt;/code&gt; property.
</description>
<tag name="@see">#getBufferSize</tag>
<tag name="@see">#setBufferSize</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.recordCount -->
<field name="recordCount"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;recordCount&lt;/code&gt; property.
</description>
<tag name="@see">#getRecordCount</tag>
<tag name="@see">#setRecordCount</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.totalRecordCount -->
<field name="totalRecordCount"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;totalRecordCount&lt;/code&gt; property.
</description>
<tag name="@see">#getTotalRecordCount</tag>
<tag name="@see">#setTotalRecordCount</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.notifyType -->
<field name="notifyType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;notifyType&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getNotifyType</tag>
<tag name="@see">#setNotifyType</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.notificationClass -->
<field name="notificationClass"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;notificationClass&lt;/code&gt; property.&#xa; references a Notification Class in the same device that specifies the&#xa; handling, reporting, and acknowledgment characteristics for this object.
</description>
<tag name="@see">#getNotificationClass</tag>
<tag name="@see">#setNotificationClass</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.objectPropertyReference -->
<field name="objectPropertyReference"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectPropertyReference&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectPropertyReference</tag>
<tag name="@see">#setObjectPropertyReference</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.eventState -->
<field name="eventState"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getEventState</tag>
<tag name="@see">#setEventState</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetTrendLog.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
