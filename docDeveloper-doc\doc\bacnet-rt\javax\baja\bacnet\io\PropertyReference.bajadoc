<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.io.PropertyReference" name="PropertyReference" packageName="javax.baja.bacnet.io" public="true" interface="true" abstract="true" category="interface">
<description>
PropertyReference contains information to reference&#xa; a property to be read.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">14 Mar 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<!-- javax.baja.bacnet.io.PropertyReference.getPropertyId() -->
<method name="getPropertyId"  public="true" abstract="true">
<description>
Get the propertyId.
</description>
<return>
<type class="int"/>
<description>
the propertyID.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.PropertyReference.getPropertyArrayIndex() -->
<method name="getPropertyArrayIndex"  public="true" abstract="true">
<description>
Get the property array index.
</description>
<return>
<type class="int"/>
<description>
the array index.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.PropertyReference.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true" abstract="true">
<description>
Encode the property reference data to Asn.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the Asn encoder.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.PropertyReference.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true" abstract="true">
<description>
Decode the property reference data from Asn.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the Asn decoder.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
<description>
if there is an Asn error.
</description>
</throws>
</method>

</class>
</bajadoc>
