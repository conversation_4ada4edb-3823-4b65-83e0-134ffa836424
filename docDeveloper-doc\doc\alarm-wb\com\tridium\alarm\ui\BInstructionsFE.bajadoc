<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.ui.BInstructionsFE" name="BInstructionsFE" packageName="com.tridium.alarm.ui" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@creation">18 Oct 05</tag>
<tag name="@version">$Revision: 4$ $Date: 5/26/10 4:23:18 PM EDT$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="com.tridium.workbench.fieldeditors.BDialogFE"/>
</extends>
<action name="setEnabledStates" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;setEnabledStates&lt;/code&gt; action.
</description>
<tag name="@see">#setEnabledStates()</tag>
</action>

</class>
</bajadoc>
