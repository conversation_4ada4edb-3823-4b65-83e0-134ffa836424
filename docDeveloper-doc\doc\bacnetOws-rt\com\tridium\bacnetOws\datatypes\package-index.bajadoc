<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnetOws" runtimeProfile="rt" name="com.tridium.bacnetOws.datatypes">
<description/>
<class packageName="com.tridium.bacnetOws.datatypes" name="BGetESummConfig"><description>BGetESummConfig represents the choices for the&#xa; user in manually issuing a GetEnrollmentSummary-Request to a device.</description></class>
<class packageName="com.tridium.bacnetOws.datatypes" name="BGetEventInfoConfig"><description>BGetEventInfoConfig configures the request for event information&#xa; from a BacnetWsDevice.</description></class>
</package>
</bajadoc>
