<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.server.BEventHandler" name="BEventHandler" packageName="com.tridium.bacnet.stack.server" public="true">
<description>
The BEventHandler handles event notification messages,&#xa; and the acknowledgment of those messages, including&#xa; ConfirmedEventNotification, UnconfirmedEventNotification,&#xa; and AcknowledgeAlarm.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 3$ $Date: 11/7/01 10:08:59 AM$</tag>
<tag name="@creation">21 Sep 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="com.tridium.bacnet.stack.server.ServiceHandler"/>
</implements>
<implements>
<type class="javax.baja.alarm.BIAlarmSource"/>
</implements>
<implements>
<type class="javax.baja.bacnet.BacnetConfirmedServiceChoice"/>
</implements>
<implements>
<type class="javax.baja.bacnet.BacnetUnconfirmedServiceChoice"/>
</implements>
<implements>
<type class="javax.baja.bacnet.BacnetAlarmConst"/>
</implements>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<property name="silenceSupported" flags="r">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;silenceSupported&lt;/code&gt; property.
</description>
<tag name="@see">#getSilenceSupported</tag>
<tag name="@see">#setSilenceSupported</tag>
</property>

<property name="eventSummaryAlarmClass" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;eventSummaryAlarmClass&lt;/code&gt; property.
</description>
<tag name="@see">#getEventSummaryAlarmClass</tag>
<tag name="@see">#setEventSummaryAlarmClass</tag>
</property>

<property name="eventSummaryProcessId" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;eventSummaryProcessId&lt;/code&gt; property.
</description>
<tag name="@see">#getEventSummaryProcessId</tag>
<tag name="@see">#setEventSummaryProcessId</tag>
</property>

<property name="toOffnormalBuffer" flags="h">
<type class="com.tridium.bacnet.stack.server.BHashedEventBuffer"/>
<description>
Slot for the &lt;code&gt;toOffnormalBuffer&lt;/code&gt; property.
</description>
<tag name="@see">#getToOffnormalBuffer</tag>
<tag name="@see">#setToOffnormalBuffer</tag>
</property>

<property name="toFaultBuffer" flags="h">
<type class="com.tridium.bacnet.stack.server.BHashedEventBuffer"/>
<description>
Slot for the &lt;code&gt;toFaultBuffer&lt;/code&gt; property.
</description>
<tag name="@see">#getToFaultBuffer</tag>
<tag name="@see">#setToFaultBuffer</tag>
</property>

<property name="toNormalBuffer" flags="h">
<type class="com.tridium.bacnet.stack.server.BHashedEventBuffer"/>
<description>
Slot for the &lt;code&gt;toNormalBuffer&lt;/code&gt; property.
</description>
<tag name="@see">#getToNormalBuffer</tag>
<tag name="@see">#setToNormalBuffer</tag>
</property>

<property name="eventSummaries" flags="h">
<type class="javax.baja.sys.BComponent"/>
<description>
Slot for the &lt;code&gt;eventSummaries&lt;/code&gt; property.
</description>
<tag name="@see">#getEventSummaries</tag>
<tag name="@see">#setEventSummaries</tag>
</property>

<action name="ackAlarm" flags="rh">
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
<description>
Slot for the &lt;code&gt;ackAlarm&lt;/code&gt; action.&#xa; Acknowledge the alarm matching this ack request.
</description>
<tag name="@see">#ackAlarm(BAlarmRecord parameter)</tag>
</action>

<action name="dumpBuffers" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;dumpBuffers&lt;/code&gt; action.
</description>
<tag name="@see">#dumpBuffers()</tag>
</action>

<action name="clearBuffers" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;clearBuffers&lt;/code&gt; action.
</description>
<tag name="@see">#clearBuffers()</tag>
</action>

</class>
</bajadoc>
