<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.array.BJsonSchemaBoundArray" name="BJsonSchemaBoundArray" packageName="com.tridiumx.jsonToolkit.outbound.schema.array" public="true">
<description>
A json array which is bound by a ord to a target station component/slot&#xa; and whose internal values are the values of the slots inside that target.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaBoundSlotsContainer"/>
</extends>
<implements>
<type class="com.tridiumx.jsonToolkit.outbound.schema.BIJsonArray"/>
</implements>
<!-- com.tridiumx.jsonToolkit.outbound.schema.array.BJsonSchemaBoundArray() -->
<constructor name="BJsonSchemaBoundArray" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.array.BJsonSchemaBoundArray.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.array.BJsonSchemaBoundArray.make(javax.baja.naming.BOrd) -->
<method name="make"  public="true" static="true">
<description/>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.array.BJsonSchemaBoundArray"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.array.BJsonSchemaBoundArray.populateArrayContent(com.tridium.json.JSONWriter) -->
<method name="populateArrayContent"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="json">
<type class="com.tridium.json.JSONWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.array.BJsonSchemaBoundArray.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.array.BJsonSchemaBoundArray.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
