<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor" name="BBacnetNiagaraHistoryDescriptor" packageName="javax.baja.bacnet.export" public="true">
<description>
BBacnetNiagaraHistoryDescriptor is the archive component which exposes&#xa; a Niagara history to Bacnet as a trend log.  It only supports &#x27;By Time&#x27;&#xa; requests for the trend log data.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@author"><PERSON> on 4 Nov 03</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.export.BBacnetEventSource"/>
</extends>
<property name="id" flags="s">
<type class="javax.baja.history.BHistoryId"/>
<description>
Slot for the &lt;code&gt;id&lt;/code&gt; property.&#xa; The unique identifier for this history within the&#xa; entire system.
</description>
<tag name="@see">#getId</tag>
<tag name="@see">#setId</tag>
</property>

<property name="historyOrd" flags="hr">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;historyOrd&lt;/code&gt; property.&#xa; The history ord.  Maintained programmatically.
</description>
<tag name="@see">#getHistoryOrd</tag>
<tag name="@see">#setHistoryOrd</tag>
</property>

<property name="objectId" flags="d">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this trend log is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="objectName" flags="d">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;objectName&lt;/code&gt; property.&#xa; the name by which this object is known to the Bacnet world.
</description>
<tag name="@see">#getObjectName</tag>
<tag name="@see">#setObjectName</tag>
</property>

<property name="description" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#getDescription</tag>
<tag name="@see">#setDescription</tag>
</property>

<property name="firstTimestamp" flags="rh">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;firstTimestamp&lt;/code&gt; property.
</description>
<tag name="@see">#getFirstTimestamp</tag>
<tag name="@see">#setFirstTimestamp</tag>
</property>

<property name="firstSeqNum" flags="rh">
<type class="long"/>
<description>
Slot for the &lt;code&gt;firstSeqNum&lt;/code&gt; property.
</description>
<tag name="@see">#getFirstSeqNum</tag>
<tag name="@see">#setFirstSeqNum</tag>
</property>

<property name="lastTimestamp" flags="rh">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;lastTimestamp&lt;/code&gt; property.
</description>
<tag name="@see">#getLastTimestamp</tag>
<tag name="@see">#setLastTimestamp</tag>
</property>

<property name="lastSeqNum" flags="rh">
<type class="long"/>
<description>
Slot for the &lt;code&gt;lastSeqNum&lt;/code&gt; property.
</description>
<tag name="@see">#getLastSeqNum</tag>
<tag name="@see">#setLastSeqNum</tag>
</property>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor() -->
<constructor name="BBacnetNiagaraHistoryDescriptor" public="true">
<description>
Empty default constructor
</description>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getId() -->
<method name="getId"  public="true">
<description>
Get the &lt;code&gt;id&lt;/code&gt; property.&#xa; The unique identifier for this history within the&#xa; entire system.
</description>
<tag name="@see">#id</tag>
<return>
<type class="javax.baja.history.BHistoryId"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.setId(javax.baja.history.BHistoryId) -->
<method name="setId"  public="true">
<description>
Set the &lt;code&gt;id&lt;/code&gt; property.&#xa; The unique identifier for this history within the&#xa; entire system.
</description>
<tag name="@see">#id</tag>
<parameter name="v">
<type class="javax.baja.history.BHistoryId"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getHistoryOrd() -->
<method name="getHistoryOrd"  public="true">
<description>
Get the &lt;code&gt;historyOrd&lt;/code&gt; property.&#xa; The history ord.  Maintained programmatically.
</description>
<tag name="@see">#historyOrd</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.setHistoryOrd(javax.baja.naming.BOrd) -->
<method name="setHistoryOrd"  public="true">
<description>
Set the &lt;code&gt;historyOrd&lt;/code&gt; property.&#xa; The history ord.  Maintained programmatically.
</description>
<tag name="@see">#historyOrd</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getObjectId() -->
<method name="getObjectId"  public="true">
<description>
Get the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this trend log is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#objectId</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.setObjectId(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="setObjectId"  public="true">
<description>
Set the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this trend log is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#objectId</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getObjectName() -->
<method name="getObjectName"  public="true">
<description>
Get the &lt;code&gt;objectName&lt;/code&gt; property.&#xa; the name by which this object is known to the Bacnet world.
</description>
<tag name="@see">#objectName</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.setObjectName(java.lang.String) -->
<method name="setObjectName"  public="true">
<description>
Set the &lt;code&gt;objectName&lt;/code&gt; property.&#xa; the name by which this object is known to the Bacnet world.
</description>
<tag name="@see">#objectName</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getDescription() -->
<method name="getDescription"  public="true">
<description>
Get the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#description</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.setDescription(java.lang.String) -->
<method name="setDescription"  public="true">
<description>
Set the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#description</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getFirstTimestamp() -->
<method name="getFirstTimestamp"  public="true">
<description>
Get the &lt;code&gt;firstTimestamp&lt;/code&gt; property.
</description>
<tag name="@see">#firstTimestamp</tag>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.setFirstTimestamp(javax.baja.sys.BAbsTime) -->
<method name="setFirstTimestamp"  public="true">
<description>
Set the &lt;code&gt;firstTimestamp&lt;/code&gt; property.
</description>
<tag name="@see">#firstTimestamp</tag>
<parameter name="v">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getFirstSeqNum() -->
<method name="getFirstSeqNum"  public="true">
<description>
Get the &lt;code&gt;firstSeqNum&lt;/code&gt; property.
</description>
<tag name="@see">#firstSeqNum</tag>
<return>
<type class="long"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.setFirstSeqNum(long) -->
<method name="setFirstSeqNum"  public="true">
<description>
Set the &lt;code&gt;firstSeqNum&lt;/code&gt; property.
</description>
<tag name="@see">#firstSeqNum</tag>
<parameter name="v">
<type class="long"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getLastTimestamp() -->
<method name="getLastTimestamp"  public="true">
<description>
Get the &lt;code&gt;lastTimestamp&lt;/code&gt; property.
</description>
<tag name="@see">#lastTimestamp</tag>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.setLastTimestamp(javax.baja.sys.BAbsTime) -->
<method name="setLastTimestamp"  public="true">
<description>
Set the &lt;code&gt;lastTimestamp&lt;/code&gt; property.
</description>
<tag name="@see">#lastTimestamp</tag>
<parameter name="v">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getLastSeqNum() -->
<method name="getLastSeqNum"  public="true">
<description>
Get the &lt;code&gt;lastSeqNum&lt;/code&gt; property.
</description>
<tag name="@see">#lastSeqNum</tag>
<return>
<type class="long"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.setLastSeqNum(long) -->
<method name="setLastSeqNum"  public="true">
<description>
Set the &lt;code&gt;lastSeqNum&lt;/code&gt; property.
</description>
<tag name="@see">#lastSeqNum</tag>
<parameter name="v">
<type class="long"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.started() -->
<method name="started"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Register with the Bacnet service when this component is started.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.stopped() -->
<method name="stopped"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Unregister with the Bacnet service when this component is stopped.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Changed.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getSlotFacets(javax.baja.sys.Slot) -->
<method name="getSlotFacets"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get slot facets.
</description>
<parameter name="s">
<type class="javax.baja.sys.Slot"/>
<description/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
<description>
the appropriate slot facets.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getObject() -->
<method name="getObject"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the exported object.
</description>
<return>
<type class="javax.baja.sys.BObject"/>
<description>
the actual exported object by resolving the object ord.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getObjectOrd() -->
<method name="getObjectOrd"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the BOrd to the exported object.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.setObjectOrd(javax.baja.naming.BOrd, javax.baja.sys.Context) -->
<method name="setObjectOrd"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the BOrd to the exported object.
</description>
<parameter name="objectOrd">
<type class="javax.baja.naming.BOrd"/>
<description/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.checkConfiguration() -->
<method name="checkConfiguration"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Check the configuration of this object.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.isValidAlarmExt(javax.baja.alarm.BIAlarmSource) -->
<method name="isValidAlarmExt"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is the given alarm source ext a valid extension for&#xa; exporting BACnet alarm properties?  This determines if the&#xa; given alarm source extension follows the appropriate algorithm&#xa; defined for the intrinsic alarming of a particular object&#xa; type as required by the BACnet specification.&lt;p&gt;&#xa; BACnet BinaryOutput points use a CommandFailure alarm algorithm.
</description>
<parameter name="ext">
<type class="javax.baja.alarm.BIAlarmSource"/>
<description/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if valid, otherwise false.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.updateAlarmInhibit() -->
<method name="updateAlarmInhibit"  protected="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;deprecation&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.isEventInitiationEnabled() -->
<method name="isEventInitiationEnabled"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is this object currently configured to support event initiation?&#xa; This will return false if the exported object does not have an&#xa; appropriate alarm extension configured to allow Bacnet event initiation.
</description>
<return>
<type class="boolean"/>
<description>
true if this object can initiate Bacnet events.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getEventState() -->
<method name="getEventState"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the current Event_State of the object.&#xa; If the exported object also has an alarm extension, this&#xa; returns the current event state as translated from the&#xa; alarm extension&#x27;s alarm state.  Otherwise, it returns null.
</description>
<return>
<type class="javax.baja.sys.BEnum"/>
<description>
the object&#x27;s event state if configured for alarming, or null.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getPoint() -->
<method name="getPoint"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.control.BControlPoint"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getAckedTransitions() -->
<method name="getAckedTransitions"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the current Acknowledged_Transitions property of the object.&#xa; If the exported object also has an alarm extension, this&#xa; returns the current acked transitions as translated from the&#xa; alarm extension&#x27;s alarm transitions.  Otherwise, it returns null.
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
the object&#x27;s acknowledged transitions if configured for alarming, or null.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getEventTimeStamps() -->
<method name="getEventTimeStamps"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the event time stamps.
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetTimeStamp" dimension="1"/>
<description>
the event time stamps, or null if event initiation is not enabled.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getNotifyType() -->
<method name="getNotifyType"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the notify type.
</description>
<return>
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
<description>
the notify type, or null if event initiation is not enabled.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getEventEnable() -->
<method name="getEventEnable"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the event enable bits.
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
the event enable bits, or null if event initiation is not enabled.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getEventPriorities() -->
<method name="getEventPriorities"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the event priorities.
</description>
<return>
<type class="int" dimension="1"/>
<description>
the event priorities, or null if event initiation is not enabled.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getNotificationClass() -->
<method name="getNotificationClass"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the Notification Class object for this event source.
</description>
<return>
<type class="javax.baja.bacnet.export.BBacnetNotificationClassDescriptor"/>
<description>
the &lt;code&gt;BacnetNotificationClassDescriptor&lt;/code&gt; for this object.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getEventType() -->
<method name="getEventType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the BACnetEventType reported by this object.
</description>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
To String.
</description>
<parameter name="c">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getHistory() -->
<method name="getHistory"  public="true" final="true">
<description>
Returns the history or null if not initialized. Creates (and closes)&#xa; a new History Database Connection.
</description>
<return>
<type class="javax.baja.history.BIHistory"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getHistory(javax.baja.history.db.HistoryDatabaseConnection) -->
<method name="getHistory"  public="true" final="true">
<description>
Returns the history or null if not initialized.
</description>
<tag name="@since">Niagara 4.0</tag>
<parameter name="conn">
<type class="javax.baja.history.db.HistoryDatabaseConnection"/>
</parameter>
<return>
<type class="javax.baja.history.BIHistory"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.readProperty(javax.baja.bacnet.io.PropertyReference) -->
<method name="readProperty"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the value of a property.
</description>
<parameter name="ref">
<type class="javax.baja.bacnet.io.PropertyReference"/>
<description>
the PropertyReference containing id and index.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.RejectException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.readPropertyMultiple(javax.baja.bacnet.io.PropertyReference[]) -->
<method name="readPropertyMultiple"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Read the value of multiple Bacnet properties.
</description>
<parameter name="refs">
<type class="javax.baja.bacnet.io.PropertyReference" dimension="1"/>
<description>
the list of property references.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue" dimension="1"/>
<description>
an array of PropertyValues.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.RejectException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.readRange(javax.baja.bacnet.io.RangeReference) -->
<method name="readRange"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Read the specified range of values of a compound property.
</description>
<parameter name="rangeReference">
<type class="javax.baja.bacnet.io.RangeReference"/>
<description>
the range reference describing the requested range.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.RangeData"/>
<description>
a byte array containing the encoded range.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.RejectException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.writeProperty(javax.baja.bacnet.io.PropertyValue) -->
<method name="writeProperty"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the value of a property.
</description>
<parameter name="val">
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
the PropertyValue containing the write information.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.addListElements(javax.baja.bacnet.io.PropertyValue) -->
<method name="addListElements"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Add list elements.
</description>
<parameter name="propertyValue">
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
the PropertyValue containing the propertyId,&#xa;                      propertyArrayIndex, and the encoded list elements.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ChangeListError"/>
<description>
a ChangeListError if unable to add any elements,&#xa; or null if ok.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.removeListElements(javax.baja.bacnet.io.PropertyValue) -->
<method name="removeListElements"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Remove list elements.
</description>
<parameter name="propertyValue">
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
the PropertyValue containing the propertyId,&#xa;                      propertyArrayIndex, and the encoded list elements.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ChangeListError"/>
<description>
a ChangeListError if unable to remove any elements,&#xa; or null if ok.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.readProperty(int, int) -->
<method name="readProperty"  protected="true">
<description>
Get the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.writeProperty(int, int, byte[], int) -->
<method name="writeProperty"  protected="true">
<description>
Set the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.readOptionalProperty(int, int) -->
<method name="readOptionalProperty"  protected="true">
<description>
Read the value of an optional property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.writeOptionalProperty(int, int, byte[], int) -->
<method name="writeOptionalProperty"  protected="true">
<description>
Set the value of an optional property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
<description/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the icon.
</description>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.getPropertyList() -->
<method name="getPropertyList"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.id -->
<field name="id"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;id&lt;/code&gt; property.&#xa; The unique identifier for this history within the&#xa; entire system.
</description>
<tag name="@see">#getId</tag>
<tag name="@see">#setId</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.historyOrd -->
<field name="historyOrd"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;historyOrd&lt;/code&gt; property.&#xa; The history ord.  Maintained programmatically.
</description>
<tag name="@see">#getHistoryOrd</tag>
<tag name="@see">#setHistoryOrd</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this trend log is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.objectName -->
<field name="objectName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectName&lt;/code&gt; property.&#xa; the name by which this object is known to the Bacnet world.
</description>
<tag name="@see">#getObjectName</tag>
<tag name="@see">#setObjectName</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.description -->
<field name="description"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#getDescription</tag>
<tag name="@see">#setDescription</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.firstTimestamp -->
<field name="firstTimestamp"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;firstTimestamp&lt;/code&gt; property.
</description>
<tag name="@see">#getFirstTimestamp</tag>
<tag name="@see">#setFirstTimestamp</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.firstSeqNum -->
<field name="firstSeqNum"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;firstSeqNum&lt;/code&gt; property.
</description>
<tag name="@see">#getFirstSeqNum</tag>
<tag name="@see">#setFirstSeqNum</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.lastTimestamp -->
<field name="lastTimestamp"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lastTimestamp&lt;/code&gt; property.
</description>
<tag name="@see">#getLastTimestamp</tag>
<tag name="@see">#setLastTimestamp</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.lastSeqNum -->
<field name="lastSeqNum"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lastSeqNum&lt;/code&gt; property.
</description>
<tag name="@see">#getLastSeqNum</tag>
<tag name="@see">#setLastSeqNum</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetNiagaraHistoryDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
