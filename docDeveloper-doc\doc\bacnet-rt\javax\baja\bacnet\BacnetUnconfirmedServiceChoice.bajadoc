<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.BacnetUnconfirmedServiceChoice" name="BacnetUnconfirmedServiceChoice" packageName="javax.baja.bacnet" public="true" interface="true" abstract="true" category="interface">
<description>
This interface contains constants which represent the values&#xa; of the BacnetUnconfirmedServiceChoice enumeration.  For more&#xa; information, see Bacnet spec, Clause 21, Unconfirmed Service&#xa; Productions.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 2$ $Date: 12/19/01 4:35:46 PM$</tag>
<tag name="@creation">01 Jul 97</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<!-- javax.baja.bacnet.BacnetUnconfirmedServiceChoice.I_AM -->
<field name="I_AM"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetUnconfirmedServiceChoice.I_HAVE -->
<field name="I_HAVE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetUnconfirmedServiceChoice.UNCONFIRMED_COV_NOTIFICATION -->
<field name="UNCONFIRMED_COV_NOTIFICATION"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetUnconfirmedServiceChoice.UNCONFIRMED_EVENT_NOTIFICATION -->
<field name="UNCONFIRMED_EVENT_NOTIFICATION"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetUnconfirmedServiceChoice.UNCONFIRMED_PRIVATE_TRANSFER -->
<field name="UNCONFIRMED_PRIVATE_TRANSFER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetUnconfirmedServiceChoice.UNCONFIRMED_TEXT_MESSAGE -->
<field name="UNCONFIRMED_TEXT_MESSAGE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetUnconfirmedServiceChoice.TIME_SYNCHRONIZATION -->
<field name="TIME_SYNCHRONIZATION"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetUnconfirmedServiceChoice.WHO_HAS -->
<field name="WHO_HAS"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetUnconfirmedServiceChoice.WHO_IS -->
<field name="WHO_IS"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetUnconfirmedServiceChoice.UTC_TIME_SYNCHRONIZATION -->
<field name="UTC_TIME_SYNCHRONIZATION"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetUnconfirmedServiceChoice.WRITE_GROUP -->
<field name="WRITE_GROUP"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.BacnetUnconfirmedServiceChoice.TAGS -->
<field name="TAGS"  public="true" static="true" final="true">
<type class="java.lang.String" dimension="1"/>
<description/>
</field>

</class>
</bajadoc>
