<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="app" runtimeProfile="rt" qualifiedName="javax.baja.app.BAppContainer" name="BAppContainer" packageName="javax.baja.app" public="true" final="true">
<description>
Niagara Application Container
</description>
<tag name="@author">g<PERSON><PERSON><PERSON></tag>
<tag name="@creation">27 Jul 2011</tag>
<tag name="@version">1</tag>
<tag name="@since">Niagara 3.7</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.sys.BIService"/>
</implements>
<implements>
<type class="javax.baja.app.BIAppFolder"/>
</implements>
<topic name="appsModified" flags="h">
<eventType>
<type class="javax.baja.sys.BValue"/>
</eventType><description>
Slot for the &lt;code&gt;appsModified&lt;/code&gt; topic.&#xa; Fired when the Apps or AppFolders are modified
</description>
<tag name="@see">#fireAppsModified</tag>
</topic>

<!-- javax.baja.app.BAppContainer() -->
<constructor name="BAppContainer" public="true">
<description/>
</constructor>

<!-- javax.baja.app.BAppContainer.fireAppsModified(javax.baja.sys.BValue) -->
<method name="fireAppsModified"  public="true">
<description>
Fire an event for the &lt;code&gt;appsModified&lt;/code&gt; topic.&#xa; Fired when the Apps or AppFolders are modified
</description>
<tag name="@see">#appsModified</tag>
<parameter name="event">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.app.BAppContainer.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.app.BAppContainer.fw(int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object) -->
<method name="fw"  public="true">
<description/>
<parameter name="x">
<type class="int"/>
</parameter>
<parameter name="a">
<type class="java.lang.Object"/>
</parameter>
<parameter name="b">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c">
<type class="java.lang.Object"/>
</parameter>
<parameter name="d">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="java.lang.Object"/>
</return>
</method>

<!-- javax.baja.app.BAppContainer.getServiceTypes() -->
<method name="getServiceTypes"  public="true">
<description/>
<return>
<type class="javax.baja.sys.Type" dimension="1"/>
</return>
</method>

<!-- javax.baja.app.BAppContainer.serviceStarted() -->
<method name="serviceStarted"  public="true">
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.app.BAppContainer.serviceStopped() -->
<method name="serviceStopped"  public="true">
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.app.BAppContainer.getIcon() -->
<method name="getIcon"  public="true">
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.app.BAppContainer.getAppDisplayName(javax.baja.sys.Context) -->
<method name="getAppDisplayName"  public="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.app.BAppContainer.getAppDisplayIcon() -->
<method name="getAppDisplayIcon"  public="true">
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.app.BAppContainer.appsModified -->
<field name="appsModified"  public="true" static="true" final="true">
<type class="javax.baja.sys.Topic"/>
<description>
Slot for the &lt;code&gt;appsModified&lt;/code&gt; topic.&#xa; Fired when the Apps or AppFolders are modified
</description>
<tag name="@see">#fireAppsModified</tag>
</field>

<!-- javax.baja.app.BAppContainer.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
