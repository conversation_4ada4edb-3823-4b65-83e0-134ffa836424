<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetObjectType" name="BBacnetObjectType" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetObjectType represents the BACnetObjectType&#xa; enumeration.&#xa; &lt;p&gt;&#xa; BBacnetObjectType is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-127 are reserved for used by ASHRAE.&#xa; Values from 128-1023 (0x3FF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Craig Gemmill</tag>
<tag name="@version">$Revision: 7$ $Date: 12/19/01 4:35:59 PM$</tag>
<tag name="@creation">21 Jul 00</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;analogInput&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;analogOutput&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;analogValue&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;binaryInput&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;binaryOutput&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;binaryValue&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;calendar&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;command&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;device&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;eventEnrollment&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;file&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;group&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;loop&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;multiStateInput&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;multiStateOutput&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;notificationClass&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;program&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;schedule&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;averaging&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;multiStateValue&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;trendLog&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lifeSafetyPoint&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lifeSafetyZone&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accumulator&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;pulseConverter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;eventLog&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;globalGroup&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;trendLogMultiple&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;loadControl&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;structuredView&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accessDoor&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unassigned31&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accessCredential&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accessPoint&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accessRights&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accessUser&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accessZone&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;credentialDataInput&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;networkSecurity&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bitstringValue&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;characterStringValue&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;datePatternValue&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;dateValue&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;dateTimePatternValue&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;dateTimeValue&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;integerValue&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;largeAnalogValue&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;octetStringValue&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;positiveIntegerValue&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;timePatternValue&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;timeValue&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;notificationForwarder&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;alertEnrollment&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;channel&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lightingOutput&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetObjectType.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
<description>
the integer enumeration value.
</description>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.hasStatusFlags(int) -->
<method name="hasStatusFlags"  public="true" static="true" final="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Utility to determine if this is one of the object types&#xa; with status flags.
</description>
<tag name="@deprecated">Since 3.5 - use &lt;code&gt;hasStatusFlags(int, BBacnetDevice)&lt;/code&gt;</tag>
<parameter name="ordinal">
<type class="int"/>
<description>
object type
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this object type has status flags.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.hasStatusFlags(int, javax.baja.bacnet.BBacnetDevice) -->
<method name="hasStatusFlags"  public="true" static="true" final="true">
<description>
Utility to determine if this is one of the object types&#xa; with status flags.
</description>
<parameter name="ordinal">
<type class="int"/>
<description>
object type
</description>
</parameter>
<parameter name="device">
<type class="javax.baja.bacnet.BBacnetDevice"/>
<description>
the device in which the object resides
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this object type has status flags.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.hasStatusFlags(int, int) -->
<method name="hasStatusFlags"  public="true" static="true" final="true">
<description>
Utility to determine if this is one of the object types&#xa; with status flags.
</description>
<parameter name="ordinal">
<type class="int"/>
<description>
object type
</description>
</parameter>
<parameter name="pr">
<type class="int"/>
<description>
of the device in which the object resides
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this object type has status flags.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.canSupportCov(int) -->
<method name="canSupportCov"  public="true" static="true" final="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Utility to determine if this is one of the object types&#xa; that may support Cov reporting (not Cov-Property).
</description>
<tag name="@deprecated">Since 3.5 - use &lt;code&gt;canSupportCov(int, BBacnetDevice)&lt;/code&gt;</tag>
<parameter name="ordinal">
<type class="int"/>
<description>
object type
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this object type may support Cov reporting.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.canSupportCov(int, javax.baja.bacnet.BBacnetDevice) -->
<method name="canSupportCov"  public="true" static="true" final="true">
<description>
Utility to determine if this is one of the object types&#xa; that may support Cov reporting (not Cov-Property).
</description>
<parameter name="ordinal">
<type class="int"/>
<description>
object type
</description>
</parameter>
<parameter name="device">
<type class="javax.baja.bacnet.BBacnetDevice"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this object type may support Cov reporting.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.canSupportCov(int, int) -->
<method name="canSupportCov"  public="true" static="true" final="true">
<description/>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<parameter name="pr">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.bitmask(int...) -->
<method name="bitmask"  public="true" static="true" isVarargs="true">
<description>
Build a long bit mask for the object ids whose ordinal&#xa; values are specified in the bits arguments.
</description>
<parameter name="bits">
<type class="int" dimension="1"/>
<description>
an array of integers whose bits should be set
</description>
</parameter>
<return>
<type class="long"/>
<description>
the long mask representing the input ordinal values
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.getShortTag(int) -->
<method name="getShortTag"  public="true" static="true">
<description>
Return a short form of the tag
</description>
<parameter name="type">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.getObjectIdFacets(int) -->
<method name="getObjectIdFacets"  public="true" static="true">
<description/>
<parameter name="objectType">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.ANALOG_INPUT -->
<field name="ANALOG_INPUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for analogInput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.ANALOG_OUTPUT -->
<field name="ANALOG_OUTPUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for analogOutput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.ANALOG_VALUE -->
<field name="ANALOG_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for analogValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.BINARY_INPUT -->
<field name="BINARY_INPUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for binaryInput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.BINARY_OUTPUT -->
<field name="BINARY_OUTPUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for binaryOutput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.BINARY_VALUE -->
<field name="BINARY_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for binaryValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.CALENDAR -->
<field name="CALENDAR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for calendar.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.COMMAND -->
<field name="COMMAND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for command.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.DEVICE -->
<field name="DEVICE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for device.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.EVENT_ENROLLMENT -->
<field name="EVENT_ENROLLMENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for eventEnrollment.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.FILE -->
<field name="FILE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for file.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.GROUP -->
<field name="GROUP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for group.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.LOOP -->
<field name="LOOP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for loop.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.MULTI_STATE_INPUT -->
<field name="MULTI_STATE_INPUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for multiStateInput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.MULTI_STATE_OUTPUT -->
<field name="MULTI_STATE_OUTPUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for multiStateOutput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.NOTIFICATION_CLASS -->
<field name="NOTIFICATION_CLASS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for notificationClass.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.PROGRAM -->
<field name="PROGRAM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for program.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.SCHEDULE -->
<field name="SCHEDULE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for schedule.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.AVERAGING -->
<field name="AVERAGING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for averaging.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.MULTI_STATE_VALUE -->
<field name="MULTI_STATE_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for multiStateValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.TREND_LOG -->
<field name="TREND_LOG"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for trendLog.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.LIFE_SAFETY_POINT -->
<field name="LIFE_SAFETY_POINT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lifeSafetyPoint.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.LIFE_SAFETY_ZONE -->
<field name="LIFE_SAFETY_ZONE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lifeSafetyZone.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.ACCUMULATOR -->
<field name="ACCUMULATOR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accumulator.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.PULSE_CONVERTER -->
<field name="PULSE_CONVERTER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for pulseConverter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.EVENT_LOG -->
<field name="EVENT_LOG"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for eventLog.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.GLOBAL_GROUP -->
<field name="GLOBAL_GROUP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for globalGroup.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.TREND_LOG_MULTIPLE -->
<field name="TREND_LOG_MULTIPLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for trendLogMultiple.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.LOAD_CONTROL -->
<field name="LOAD_CONTROL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for loadControl.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.STRUCTURED_VIEW -->
<field name="STRUCTURED_VIEW"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for structuredView.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.ACCESS_DOOR -->
<field name="ACCESS_DOOR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accessDoor.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.UNASSIGNED_31 -->
<field name="UNASSIGNED_31"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unassigned31.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.ACCESS_CREDENTIAL -->
<field name="ACCESS_CREDENTIAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accessCredential.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.ACCESS_POINT -->
<field name="ACCESS_POINT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accessPoint.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.ACCESS_RIGHTS -->
<field name="ACCESS_RIGHTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accessRights.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.ACCESS_USER -->
<field name="ACCESS_USER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accessUser.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.ACCESS_ZONE -->
<field name="ACCESS_ZONE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accessZone.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.CREDENTIAL_DATA_INPUT -->
<field name="CREDENTIAL_DATA_INPUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for credentialDataInput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.NETWORK_SECURITY -->
<field name="NETWORK_SECURITY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for networkSecurity.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.BITSTRING_VALUE -->
<field name="BITSTRING_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bitstringValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.CHARACTER_STRING_VALUE -->
<field name="CHARACTER_STRING_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for characterStringValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.DATE_PATTERN_VALUE -->
<field name="DATE_PATTERN_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for datePatternValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.DATE_VALUE -->
<field name="DATE_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for dateValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.DATE_TIME_PATTERN_VALUE -->
<field name="DATE_TIME_PATTERN_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for dateTimePatternValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.DATE_TIME_VALUE -->
<field name="DATE_TIME_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for dateTimeValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.INTEGER_VALUE -->
<field name="INTEGER_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for integerValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.LARGE_ANALOG_VALUE -->
<field name="LARGE_ANALOG_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for largeAnalogValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.OCTET_STRING_VALUE -->
<field name="OCTET_STRING_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for octetStringValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.POSITIVE_INTEGER_VALUE -->
<field name="POSITIVE_INTEGER_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for positiveIntegerValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.TIME_PATTERN_VALUE -->
<field name="TIME_PATTERN_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for timePatternValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.TIME_VALUE -->
<field name="TIME_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for timeValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.NOTIFICATION_FORWARDER -->
<field name="NOTIFICATION_FORWARDER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for notificationForwarder.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.ALERT_ENROLLMENT -->
<field name="ALERT_ENROLLMENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for alertEnrollment.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.CHANNEL -->
<field name="CHANNEL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for channel.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.LIGHTING_OUTPUT -->
<field name="LIGHTING_OUTPUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lightingOutput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.analogInput -->
<field name="analogInput"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for analogInput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.analogOutput -->
<field name="analogOutput"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for analogOutput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.analogValue -->
<field name="analogValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for analogValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.binaryInput -->
<field name="binaryInput"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for binaryInput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.binaryOutput -->
<field name="binaryOutput"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for binaryOutput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.binaryValue -->
<field name="binaryValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for binaryValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.calendar -->
<field name="calendar"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for calendar.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.command -->
<field name="command"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for command.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.device -->
<field name="device"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for device.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.eventEnrollment -->
<field name="eventEnrollment"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for eventEnrollment.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.file -->
<field name="file"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for file.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.group -->
<field name="group"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for group.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.loop -->
<field name="loop"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for loop.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.multiStateInput -->
<field name="multiStateInput"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for multiStateInput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.multiStateOutput -->
<field name="multiStateOutput"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for multiStateOutput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.notificationClass -->
<field name="notificationClass"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for notificationClass.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.program -->
<field name="program"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for program.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.schedule -->
<field name="schedule"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for schedule.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.averaging -->
<field name="averaging"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for averaging.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.multiStateValue -->
<field name="multiStateValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for multiStateValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.trendLog -->
<field name="trendLog"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for trendLog.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.lifeSafetyPoint -->
<field name="lifeSafetyPoint"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for lifeSafetyPoint.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.lifeSafetyZone -->
<field name="lifeSafetyZone"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for lifeSafetyZone.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.accumulator -->
<field name="accumulator"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for accumulator.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.pulseConverter -->
<field name="pulseConverter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for pulseConverter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.eventLog -->
<field name="eventLog"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for eventLog.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.globalGroup -->
<field name="globalGroup"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for globalGroup.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.trendLogMultiple -->
<field name="trendLogMultiple"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for trendLogMultiple.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.loadControl -->
<field name="loadControl"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for loadControl.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.structuredView -->
<field name="structuredView"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for structuredView.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.accessDoor -->
<field name="accessDoor"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for accessDoor.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.unassigned31 -->
<field name="unassigned31"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for unassigned31.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.accessCredential -->
<field name="accessCredential"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for accessCredential.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.accessPoint -->
<field name="accessPoint"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for accessPoint.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.accessRights -->
<field name="accessRights"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for accessRights.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.accessUser -->
<field name="accessUser"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for accessUser.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.accessZone -->
<field name="accessZone"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for accessZone.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.credentialDataInput -->
<field name="credentialDataInput"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for credentialDataInput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.networkSecurity -->
<field name="networkSecurity"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for networkSecurity.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.bitstringValue -->
<field name="bitstringValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for bitstringValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.characterStringValue -->
<field name="characterStringValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for characterStringValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.datePatternValue -->
<field name="datePatternValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for datePatternValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.dateValue -->
<field name="dateValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for dateValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.dateTimePatternValue -->
<field name="dateTimePatternValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for dateTimePatternValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.dateTimeValue -->
<field name="dateTimeValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for dateTimeValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.integerValue -->
<field name="integerValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for integerValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.largeAnalogValue -->
<field name="largeAnalogValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for largeAnalogValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.octetStringValue -->
<field name="octetStringValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for octetStringValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.positiveIntegerValue -->
<field name="positiveIntegerValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for positiveIntegerValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.timePatternValue -->
<field name="timePatternValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for timePatternValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.timeValue -->
<field name="timeValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for timeValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.notificationForwarder -->
<field name="notificationForwarder"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for notificationForwarder.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.alertEnrollment -->
<field name="alertEnrollment"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for alertEnrollment.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.channel -->
<field name="channel"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for channel.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.lightingOutput -->
<field name="lightingOutput"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description>
BBacnetObjectType constant for lightingOutput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetObjectType"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetObjectType.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
