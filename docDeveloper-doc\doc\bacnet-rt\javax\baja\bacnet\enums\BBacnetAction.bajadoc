<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetAction" name="BBacnetAction" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetAction represents the BACnetAction enumeration.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">26 Jul 2005</tag>
<tag name="@since">Niagara 3.1</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;direct&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;reverse&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetAction.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetAction"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetAction.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetAction"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetAction.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetAction.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetAction.getTag(javax.baja.sys.Context) -->
<method name="getTag"  public="true">
<description>
Get a string tag, using the true/false text in the&#xa; given context, if possible.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetAction.make(boolean) -->
<method name="make"  public="true" static="true" final="true">
<description/>
<parameter name="value">
<type class="boolean"/>
<description>
the boolean value to be represented.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetAction"/>
<description>
a BBacnetAction object with the given code.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetAction.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
String representation of this BEnum.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetAction.DIRECT -->
<field name="DIRECT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for direct.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAction.REVERSE -->
<field name="REVERSE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for reverse.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAction.direct -->
<field name="direct"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetAction"/>
<description>
BBacnetAction constant for direct.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAction.reverse -->
<field name="reverse"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetAction"/>
<description>
BBacnetAction constant for reverse.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAction.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetAction"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetAction.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
