<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.job.BRunnableJob" name="BRunnableJob" packageName="javax.baja.job" public="true">
<description>
BRunnableJob provides a simple job implementations &#xa; for executing runnable tasks in the job framework.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">12 April 2010</tag>
<tag name="@version">$Revision: 1$ $Date: 4/12/10 10:53:18 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.job.BSimpleJob"/>
</extends>
<!-- javax.baja.job.BRunnableJob(java.lang.Runnable) -->
<constructor name="<PERSON>unnableJob" public="true">
<parameter name="runnable">
<type class="java.lang.Runnable"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.job.BRunnableJob() -->
<constructor name="BRunnableJob" public="true">
<description/>
</constructor>

<!-- javax.baja.job.BRunnableJob.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.job.BRunnableJob.run(javax.baja.sys.Context) -->
<method name="run"  public="true" final="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.job.BRunnableJob.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
