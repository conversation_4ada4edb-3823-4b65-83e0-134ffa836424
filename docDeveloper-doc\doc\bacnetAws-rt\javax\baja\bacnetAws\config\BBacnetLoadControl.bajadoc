<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="javax.baja.bacnetAws.config.BBacnetLoadControl" name="BBacnetLoadControl" packageName="javax.baja.bacnetAws.config" public="true">
<description/>
<extends>
<type class="javax.baja.bacnet.BBacnetObject"/>
</extends>
<property name="presentValue" flags="">
<type class="javax.baja.bacnetAws.enums.BBacnetShedState"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</property>

<property name="statusFlags" flags="r">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
Slot for the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#getStatusFlags</tag>
<tag name="@see">#setStatusFlags</tag>
</property>

<property name="eventState" flags="r">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getEventState</tag>
<tag name="@see">#setEventState</tag>
</property>

<property name="requestedShedLevel" flags="">
<type class="javax.baja.bacnetAws.datatypes.BBacnetShedLevel"/>
<description>
Slot for the &lt;code&gt;requestedShedLevel&lt;/code&gt; property.
</description>
<tag name="@see">#getRequestedShedLevel</tag>
<tag name="@see">#setRequestedShedLevel</tag>
</property>

<property name="startTime" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
<description>
Slot for the &lt;code&gt;startTime&lt;/code&gt; property.
</description>
<tag name="@see">#getStartTime</tag>
<tag name="@see">#setStartTime</tag>
</property>

<property name="shedDuration" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;shedDuration&lt;/code&gt; property.
</description>
<tag name="@see">#getShedDuration</tag>
<tag name="@see">#setShedDuration</tag>
</property>

<property name="dutyWindow" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;dutyWindow&lt;/code&gt; property.
</description>
<tag name="@see">#getDutyWindow</tag>
<tag name="@see">#setDutyWindow</tag>
</property>

<property name="enable" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;enable&lt;/code&gt; property.
</description>
<tag name="@see">#getEnable</tag>
<tag name="@see">#setEnable</tag>
</property>

<property name="expectedShedLevel" flags="">
<type class="javax.baja.bacnetAws.datatypes.BBacnetShedLevel"/>
<description>
Slot for the &lt;code&gt;expectedShedLevel&lt;/code&gt; property.
</description>
<tag name="@see">#getExpectedShedLevel</tag>
<tag name="@see">#setExpectedShedLevel</tag>
</property>

<property name="acutalShedLevel" flags="">
<type class="javax.baja.bacnetAws.datatypes.BBacnetShedLevel"/>
<description>
Slot for the &lt;code&gt;acutalShedLevel&lt;/code&gt; property.
</description>
<tag name="@see">#getAcutalShedLevel</tag>
<tag name="@see">#setAcutalShedLevel</tag>
</property>

<property name="shedLevels" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
<description>
Slot for the &lt;code&gt;shedLevels&lt;/code&gt; property.
</description>
<tag name="@see">#getShedLevels</tag>
<tag name="@see">#setShedLevels</tag>
</property>

<property name="shedLevelDescriptions" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
<description>
Slot for the &lt;code&gt;shedLevelDescriptions&lt;/code&gt; property.
</description>
<tag name="@see">#getShedLevelDescriptions</tag>
<tag name="@see">#setShedLevelDescriptions</tag>
</property>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl() -->
<constructor name="BBacnetLoadControl" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.getPresentValue() -->
<method name="getPresentValue"  public="true">
<description>
Get the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#presentValue</tag>
<return>
<type class="javax.baja.bacnetAws.enums.BBacnetShedState"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.setPresentValue(javax.baja.bacnetAws.enums.BBacnetShedState) -->
<method name="setPresentValue"  public="true">
<description>
Set the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#presentValue</tag>
<parameter name="v">
<type class="javax.baja.bacnetAws.enums.BBacnetShedState"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.getStatusFlags() -->
<method name="getStatusFlags"  public="true">
<description>
Get the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#statusFlags</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.setStatusFlags(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="setStatusFlags"  public="true">
<description>
Set the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#statusFlags</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.getEventState() -->
<method name="getEventState"  public="true">
<description>
Get the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#eventState</tag>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.setEventState(javax.baja.sys.BEnum) -->
<method name="setEventState"  public="true">
<description>
Set the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#eventState</tag>
<parameter name="v">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.getRequestedShedLevel() -->
<method name="getRequestedShedLevel"  public="true">
<description>
Get the &lt;code&gt;requestedShedLevel&lt;/code&gt; property.
</description>
<tag name="@see">#requestedShedLevel</tag>
<return>
<type class="javax.baja.bacnetAws.datatypes.BBacnetShedLevel"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.setRequestedShedLevel(javax.baja.bacnetAws.datatypes.BBacnetShedLevel) -->
<method name="setRequestedShedLevel"  public="true">
<description>
Set the &lt;code&gt;requestedShedLevel&lt;/code&gt; property.
</description>
<tag name="@see">#requestedShedLevel</tag>
<parameter name="v">
<type class="javax.baja.bacnetAws.datatypes.BBacnetShedLevel"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.getStartTime() -->
<method name="getStartTime"  public="true">
<description>
Get the &lt;code&gt;startTime&lt;/code&gt; property.
</description>
<tag name="@see">#startTime</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.setStartTime(javax.baja.bacnet.datatypes.BBacnetDateTime) -->
<method name="setStartTime"  public="true">
<description>
Set the &lt;code&gt;startTime&lt;/code&gt; property.
</description>
<tag name="@see">#startTime</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.getShedDuration() -->
<method name="getShedDuration"  public="true">
<description>
Get the &lt;code&gt;shedDuration&lt;/code&gt; property.
</description>
<tag name="@see">#shedDuration</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.setShedDuration(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setShedDuration"  public="true">
<description>
Set the &lt;code&gt;shedDuration&lt;/code&gt; property.
</description>
<tag name="@see">#shedDuration</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.getDutyWindow() -->
<method name="getDutyWindow"  public="true">
<description>
Get the &lt;code&gt;dutyWindow&lt;/code&gt; property.
</description>
<tag name="@see">#dutyWindow</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.setDutyWindow(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setDutyWindow"  public="true">
<description>
Set the &lt;code&gt;dutyWindow&lt;/code&gt; property.
</description>
<tag name="@see">#dutyWindow</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.getEnable() -->
<method name="getEnable"  public="true">
<description>
Get the &lt;code&gt;enable&lt;/code&gt; property.
</description>
<tag name="@see">#enable</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.setEnable(boolean) -->
<method name="setEnable"  public="true">
<description>
Set the &lt;code&gt;enable&lt;/code&gt; property.
</description>
<tag name="@see">#enable</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.getExpectedShedLevel() -->
<method name="getExpectedShedLevel"  public="true">
<description>
Get the &lt;code&gt;expectedShedLevel&lt;/code&gt; property.
</description>
<tag name="@see">#expectedShedLevel</tag>
<return>
<type class="javax.baja.bacnetAws.datatypes.BBacnetShedLevel"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.setExpectedShedLevel(javax.baja.bacnetAws.datatypes.BBacnetShedLevel) -->
<method name="setExpectedShedLevel"  public="true">
<description>
Set the &lt;code&gt;expectedShedLevel&lt;/code&gt; property.
</description>
<tag name="@see">#expectedShedLevel</tag>
<parameter name="v">
<type class="javax.baja.bacnetAws.datatypes.BBacnetShedLevel"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.getAcutalShedLevel() -->
<method name="getAcutalShedLevel"  public="true">
<description>
Get the &lt;code&gt;acutalShedLevel&lt;/code&gt; property.
</description>
<tag name="@see">#acutalShedLevel</tag>
<return>
<type class="javax.baja.bacnetAws.datatypes.BBacnetShedLevel"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.setAcutalShedLevel(javax.baja.bacnetAws.datatypes.BBacnetShedLevel) -->
<method name="setAcutalShedLevel"  public="true">
<description>
Set the &lt;code&gt;acutalShedLevel&lt;/code&gt; property.
</description>
<tag name="@see">#acutalShedLevel</tag>
<parameter name="v">
<type class="javax.baja.bacnetAws.datatypes.BBacnetShedLevel"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.getShedLevels() -->
<method name="getShedLevels"  public="true">
<description>
Get the &lt;code&gt;shedLevels&lt;/code&gt; property.
</description>
<tag name="@see">#shedLevels</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.setShedLevels(javax.baja.bacnet.datatypes.BBacnetArray) -->
<method name="setShedLevels"  public="true">
<description>
Set the &lt;code&gt;shedLevels&lt;/code&gt; property.
</description>
<tag name="@see">#shedLevels</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.getShedLevelDescriptions() -->
<method name="getShedLevelDescriptions"  public="true">
<description>
Get the &lt;code&gt;shedLevelDescriptions&lt;/code&gt; property.
</description>
<tag name="@see">#shedLevelDescriptions</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.setShedLevelDescriptions(javax.baja.bacnet.datatypes.BBacnetArray) -->
<method name="setShedLevelDescriptions"  public="true">
<description>
Set the &lt;code&gt;shedLevelDescriptions&lt;/code&gt; property.
</description>
<tag name="@see">#shedLevelDescriptions</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.presentValue -->
<field name="presentValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.statusFlags -->
<field name="statusFlags"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#getStatusFlags</tag>
<tag name="@see">#setStatusFlags</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.eventState -->
<field name="eventState"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getEventState</tag>
<tag name="@see">#setEventState</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.requestedShedLevel -->
<field name="requestedShedLevel"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;requestedShedLevel&lt;/code&gt; property.
</description>
<tag name="@see">#getRequestedShedLevel</tag>
<tag name="@see">#setRequestedShedLevel</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.startTime -->
<field name="startTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;startTime&lt;/code&gt; property.
</description>
<tag name="@see">#getStartTime</tag>
<tag name="@see">#setStartTime</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.shedDuration -->
<field name="shedDuration"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;shedDuration&lt;/code&gt; property.
</description>
<tag name="@see">#getShedDuration</tag>
<tag name="@see">#setShedDuration</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.dutyWindow -->
<field name="dutyWindow"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;dutyWindow&lt;/code&gt; property.
</description>
<tag name="@see">#getDutyWindow</tag>
<tag name="@see">#setDutyWindow</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.enable -->
<field name="enable"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;enable&lt;/code&gt; property.
</description>
<tag name="@see">#getEnable</tag>
<tag name="@see">#setEnable</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.expectedShedLevel -->
<field name="expectedShedLevel"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;expectedShedLevel&lt;/code&gt; property.
</description>
<tag name="@see">#getExpectedShedLevel</tag>
<tag name="@see">#setExpectedShedLevel</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.acutalShedLevel -->
<field name="acutalShedLevel"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;acutalShedLevel&lt;/code&gt; property.
</description>
<tag name="@see">#getAcutalShedLevel</tag>
<tag name="@see">#setAcutalShedLevel</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.shedLevels -->
<field name="shedLevels"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;shedLevels&lt;/code&gt; property.
</description>
<tag name="@see">#getShedLevels</tag>
<tag name="@see">#setShedLevels</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.shedLevelDescriptions -->
<field name="shedLevelDescriptions"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;shedLevelDescriptions&lt;/code&gt; property.
</description>
<tag name="@see">#getShedLevelDescriptions</tag>
<tag name="@see">#setShedLevelDescriptions</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetLoadControl.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
