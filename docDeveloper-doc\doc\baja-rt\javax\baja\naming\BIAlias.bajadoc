<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.BIAlias" name="BIAlias" packageName="javax.baja.naming" public="true" interface="true" abstract="true" category="interface">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@creation">26 Jan 2012</tag>
<tag name="@version">$Revision: 2$ $Date: 9/17/07 8:50:34 AM EDT$</tag>
<tag name="@since">Niagara 3.7</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<!-- javax.baja.naming.BIAlias.getOrd() -->
<method name="getOrd"  public="true" abstract="true">
<description/>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.naming.BIAlias.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
