<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.collection.TableCursor" name="TableCursor" packageName="javax.baja.collection" public="true" interface="true" abstract="true" category="interface">
<description>
TableCursor is a cursor for iterating through the rows in a table.
</description>
<tag name="@author">&lt;a href=&#x22;mailto:<EMAIL>&#x22;&gt;<PERSON>&lt;/a&gt;</tag>
<tag name="@author">&lt;a href=&#x22;mailto:<EMAIL>&#x22;&gt;<PERSON>&lt;/a&gt;</tag>
<implements>
<parameterizedType class="javax.baja.sys.IterableCursor">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</implements>
<typeParameters>
<typeVariable class="T">
<bounds>
<type class="javax.baja.sys.BIObject"/>
</bounds>
</typeVariable>
</typeParameters>
<!-- javax.baja.collection.TableCursor.getTable() -->
<method name="getTable"  public="true" abstract="true">
<description>
Get the BITable backing this cursor (covariant return).
</description>
<return>
<parameterizedType class="javax.baja.collection.BITable">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.collection.TableCursor.row() -->
<method name="row"  public="true" abstract="true">
<description>
Get the current table row (covariant return).
</description>
<return>
<parameterizedType class="javax.baja.collection.Row">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.collection.TableCursor.cell(javax.baja.collection.Column) -->
<method name="cell"  public="true" default="true">
<description>
Get the cell value for the specified column at the current row.
</description>
<parameter name="column">
<type class="javax.baja.collection.Column"/>
</parameter>
<return>
<type class="javax.baja.sys.BIObject"/>
</return>
</method>

</class>
</bajadoc>
