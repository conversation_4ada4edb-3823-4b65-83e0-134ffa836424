<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BacnetDescriptorUtil" name="BacnetDescriptorUtil" packageName="javax.baja.bacnet.export" public="true" final="true">
<description>
A collection of methods for interacting with BacnetDescriptor instances.
</description>
<tag name="@author"><PERSON><PERSON><PERSON> on 25/10/2018</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- javax.baja.bacnet.export.BacnetDescriptorUtil.isGenericTrendLogExtension(com.tridium.bacnet.history.BIBacnetTrendLogExt) -->
<method name="isGenericTrendLogExtension"  public="true" static="true">
<description/>
<parameter name="ext">
<type class="com.tridium.bacnet.history.BIBacnetTrendLogExt"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BacnetDescriptorUtil.nextObjectIdentifier(int) -->
<method name="nextObjectIdentifier"  public="true" static="true">
<description>
This method calculates the next oid that can be used by a dynamic object for the particular object type.
</description>
<parameter name="objectType">
<type class="int"/>
<description>
The type of object that needs to be created
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
BBacnetObjectIdentifier the next oid to be used for dynamic object creation
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BacnetDescriptorUtil.exportTable() -->
<method name="exportTable"  public="true" static="true">
<description>
Simple utility to ge the exportTable of bacnet network
</description>
<return>
<type class="com.tridium.bacnet.stack.server.BBacnetExportTable"/>
<description>
The exportTable
</description>
</return>
</method>

</class>
</bajadoc>
