<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="ace" runtimeProfile="rt" name="com.tridium.ace.program">
<description/>
<class packageName="com.tridium.ace.program" name="BAceAppDownloadJob"><description>BAceAppDownloadJob download app file to device list and restart devices</description></class>
<class packageName="com.tridium.ace.program" name="BAceReadFileJob"><description>BAceReadFileJob read file from device list</description></class>
<class packageName="com.tridium.ace.program" name="BAceSaveAppJob"><description>BAceSaveAppJob save application with job.</description></class>
<class packageName="com.tridium.ace.program" name="BAceViewApp"><description><PERSON>ceViewA<PERSON> uploads the current contents of running app and creates database version.</description></class>
</package>
</bajadoc>
