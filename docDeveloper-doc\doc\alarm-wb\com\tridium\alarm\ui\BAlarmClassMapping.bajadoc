<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.ui.BAlarmClassMapping" name="BAlarmClassMapping" packageName="com.tridium.alarm.ui" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@creation">26 Apr 04</tag>
<tag name="@version">$Revision: 3$ $Date: 11/15/04 12:56:53 PM EST$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="alarmClassDefinitions" flags="h">
<type class="javax.baja.sys.BComponent"/>
<description>
Slot for the &lt;code&gt;alarmClassDefinitions&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmClassDefinitions</tag>
<tag name="@see">#setAlarmClassDefinitions</tag>
</property>

<property name="alarmClassDefMapping" flags="h">
<type class="javax.baja.sys.BComponent"/>
<description>
Slot for the &lt;code&gt;alarmClassDefMapping&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmClassDefMapping</tag>
<tag name="@see">#setAlarmClassDefMapping</tag>
</property>

</class>
</bajadoc>
