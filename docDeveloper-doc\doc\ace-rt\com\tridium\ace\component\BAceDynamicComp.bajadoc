<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.component.BAceDynamicComp" name="BAceDynamicComp" packageName="com.tridium.ace.component" public="true">
<description>
BAceDynamicComp
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">3/29/2016</tag>
<extends>
<type class="com.tridium.ace.component.BAceComponent"/>
</extends>
<property name="aceType" flags="rs">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;aceType&lt;/code&gt; property.
</description>
<tag name="@see">#getAceType</tag>
<tag name="@see">#setAceType</tag>
</property>

<property name="aceKit" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;aceKit&lt;/code&gt; property.
</description>
<tag name="@see">#getAceKit</tag>
<tag name="@see">#setAceKit</tag>
</property>

</class>
</bajadoc>
