<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.link.ethernet.BBacnetEthernetLinkLayer" name="BBacnetEthernetLinkLayer" packageName="com.tridium.bacnet.stack.link.ethernet" public="true">
<description>
Tridium Bacnet Ethernet Virtual Link Layer Implementation.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">01 Aug 01</tag>
<tag name="@version">$Revision: 4$ $Date: 12/19/01 4:35:19 PM$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.stack.link.BBacnetLinkLayer"/>
</extends>
<implements>
<type class="com.tridium.platBacnet.EthernetConst"/>
</implements>
<implements>
<type class="com.tridium.platBacnet.EthernetListener"/>
</implements>
<property name="adapterTitle" flags="d">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;adapterTitle&lt;/code&gt; property.&#xa; the title of the selected ethernet adapter.  This is a&#xa; name chosen by the manufacturer of the ethernet adapter card.&#xa; The user selects the ethernet adapter to use by this title.
</description>
<tag name="@see">#getAdapterTitle</tag>
<tag name="@see">#setAdapterTitle</tag>
</property>

<property name="adapterDescription" flags="dr">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;adapterDescription&lt;/code&gt; property.&#xa; the manufacturer-supplied description of the selected ethernet adapter.
</description>
<tag name="@see">#getAdapterDescription</tag>
<tag name="@see">#setAdapterDescription</tag>
</property>

<property name="adapterName" flags="dr">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;adapterName&lt;/code&gt; property.&#xa; the name of the selected ethernet adapter.  This is the internal identifier&#xa; by which the adapter is known.  On Win32 platforms, this is a GUID.  In QNX, this&#xa; is a pathname.
</description>
<tag name="@see">#getAdapterName</tag>
<tag name="@see">#setAdapterName</tag>
</property>

</class>
</bajadoc>
