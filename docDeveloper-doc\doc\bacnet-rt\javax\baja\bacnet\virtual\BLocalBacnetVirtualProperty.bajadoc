<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.virtual.BLocalBacnetVirtualProperty" name="BLocalBacnetVirtualProperty" packageName="javax.baja.bacnet.virtual" public="true">
<description/>
<extends>
<type class="javax.baja.bacnet.virtual.BBacnetVirtualProperty"/>
</extends>
<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualProperty() -->
<constructor name="BLocalBacnetVirtualProperty" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualProperty(int, javax.baja.sys.BValue, java.lang.String, boolean) -->
<constructor name="BLocalBacnetVirtualProperty" public="true">
<parameter name="propertyId">
<type class="int"/>
</parameter>
<parameter name="v">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="readFault">
<type class="java.lang.String"/>
</parameter>
<parameter name="useFacets">
<type class="boolean"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualProperty.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualProperty.device() -->
<method name="device"  public="true">
<description>
Local properties are not in a BBacnetDevice.
</description>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualProperty.localDevice() -->
<method name="localDevice"  public="true">
<description>
Convenience method to get the local device object.
</description>
<return>
<type class="javax.baja.bacnet.export.BLocalBacnetDevice"/>
<description>
localBacnetDevice
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualProperty.getDeviceStatus() -->
<method name="getDeviceStatus"  protected="true">
<description>
Get the status of the containing device.
</description>
<return>
<type class="javax.baja.status.BStatus"/>
<description>
the status of the device
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualProperty.pollSubscribe() -->
<method name="pollSubscribe"  protected="true">
<description>
Override to subscribe with the gateway&#x27;s local poll thread&#xa; instead of the BacnetPoll service.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualProperty.pollUnsubscribe() -->
<method name="pollUnsubscribe"  protected="true">
<description>
Override to unsubscribe from the gateway&#x27;s local poll thread&#xa; instead of the BacnetPoll service.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualProperty.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<description>
Only allowed inside BLocalBacnetVirtualObject.
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualProperty.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
