<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="javax.baja.bacnet.config">
<description/>
<class packageName="javax.baja.bacnet.config" name="BBacnetAnalog"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetAnalogInput"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetAnalogOutput"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetAnalogValue"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetAveraging"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetBinary"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetBinaryInput"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetBinaryOutput"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetBinaryValue"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetCalendar"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetConfigDeviceExt"><description>BBacnetConfigDeviceExt represents the configuration representation of a&#xa; Bacnet device.</description></class>
<class packageName="javax.baja.bacnet.config" name="BBacnetConfigFolder"><description>BBacnetConfigFolder is the standard container to use&#xa; under BBacnetConfigDeviceExt to organize BBacnetObjects.</description></class>
<class packageName="javax.baja.bacnet.config" name="BBacnetCreatableObject"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetDeviceObject"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetEventEnrollment"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetFile"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetGroup"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetLoop"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetMultistate"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetMultistateInput"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetMultistateOutput"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetMultistateValue"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetNotificationClass"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetProgram"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetSchedule"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetTrendLog"/>
<class packageName="javax.baja.bacnet.config" name="BBacnetTrendLogMultiple"><description>BBacnetTrendLogMultiple augments BBacnetTrendLog.</description></class>
<class packageName="javax.baja.bacnet.config" name="BIBacnetConfigFolder" category="interface"><description>BIBacnetConfigFolder is the common interface for&#xa; BLocalBacnetDevice and BIBacnetConfigFolder.</description></class>
</package>
</bajadoc>
