<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.io.ValueDocDecoder$IDecoderPlugin" name="ValueDocDecoder.IDecoderPlugin" packageName="javax.baja.io" public="true" interface="true" abstract="true" static="true" innerClass="true" category="interface">
<description>
A value document&#x27;s format can vary (XML or JSON). A decoder plug-in is&#xa; used to support these different formats
</description>
<implements>
<type class="java.lang.AutoCloseable"/>
</implements>
<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.decodeDocument(javax.baja.io.ValueDocDecoder) -->
<method name="decodeDocument"  public="true" abstract="true">
<description/>
<parameter name="decoder">
<type class="javax.baja.io.ValueDocDecoder"/>
</parameter>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.next() -->
<method name="next"  public="true" abstract="true">
<description/>
<return>
<type class="int"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.elem() -->
<method name="elem"  public="true" abstract="true">
<description/>
<return>
<type class="com.tridium.nre.util.IElement"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.skip() -->
<method name="skip"  public="true" abstract="true">
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.skip(int) -->
<method name="skip"  public="true" abstract="true">
<description/>
<parameter name="depth">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.type() -->
<method name="type"  public="true" abstract="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.line() -->
<method name="line"  public="true" abstract="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.column() -->
<method name="column"  public="true" abstract="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.close() -->
<method name="close"  public="true" abstract="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.depth() -->
<method name="depth"  public="true" abstract="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.getEncoding() -->
<method name="getEncoding"  public="true" abstract="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.isZipped() -->
<method name="isZipped"  public="true" abstract="true">
<description/>
<return>
<type class="boolean"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.getVersion() -->
<method name="getVersion"  public="true" default="true">
<description/>
<return>
<type class="javax.baja.util.Version"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.parse() -->
<method name="parse"  public="true" abstract="true">
<description/>
<return>
<type class="com.tridium.nre.util.IElement"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.parse(boolean) -->
<method name="parse"  public="true" abstract="true">
<description/>
<parameter name="close">
<type class="boolean"/>
</parameter>
<return>
<type class="com.tridium.nre.util.IElement"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.parseCurrent() -->
<method name="parseCurrent"  public="true" abstract="true">
<description/>
<return>
<type class="com.tridium.nre.util.IElement"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.parseCurrent(boolean) -->
<method name="parseCurrent"  public="true" abstract="true">
<description/>
<parameter name="close">
<type class="boolean"/>
</parameter>
<return>
<type class="com.tridium.nre.util.IElement"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.getTypeResolver() -->
<method name="getTypeResolver"  public="true" abstract="true">
<description/>
<return>
<type class="javax.baja.io.ValueDocDecoder$ITypeResolver"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.setTypeResolver(javax.baja.io.ValueDocDecoder.ITypeResolver) -->
<method name="setTypeResolver"  public="true" abstract="true">
<description/>
<parameter name="typeResolver">
<type class="javax.baja.io.ValueDocDecoder$ITypeResolver"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.err(java.lang.String, java.lang.Throwable) -->
<method name="err"  public="true" abstract="true">
<description/>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="cause">
<type class="java.lang.Throwable"/>
</parameter>
<return>
<type class="java.lang.RuntimeException"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.err(java.lang.String) -->
<method name="err"  public="true" abstract="true">
<description/>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="java.lang.RuntimeException"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.warningAndSkip(java.lang.String) -->
<method name="warningAndSkip"  public="true" abstract="true">
<description/>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.RuntimeException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.warning(java.lang.String) -->
<method name="warning"  public="true" abstract="true">
<description/>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.getLog() -->
<method name="getLog"  public="true" abstract="true">
<description/>
<return>
<type class="java.util.logging.Logger"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.setLog(java.util.logging.Logger) -->
<method name="setLog"  public="true" abstract="true">
<description/>
<parameter name="log">
<type class="java.util.logging.Logger"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.getWarningCount() -->
<method name="getWarningCount"  public="true" abstract="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.IDecoderPlugin.getPassPhraseValidator() -->
<method name="getPassPhraseValidator"  public="true" default="true">
<description>
If the document contains encrypted data that&#x27;s encoded using a reversible algorithm, and&#xa; the algorithm uses a key created from a pass phrase, return a validator that can&#xa; verify that a given pass phrase matches the one that was used to encode the document.
</description>
<tag name="@since">Niagara 4.0</tag>
<return>
<type class="javax.baja.security.BIPasswordValidator"/>
</return>
</method>

</class>
</bajadoc>
