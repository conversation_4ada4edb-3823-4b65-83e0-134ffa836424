<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaProperty" name="BJsonSchemaProperty" packageName="com.tridiumx.jsonToolkit.outbound.schema.property" public="true" abstract="true">
<description>
Base class for fixed key / value json pair.&#xa;&#xa; Allows us to map the components slot name to be the json key.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaMember"/>
</extends>
<implements>
<parameterizedType class="com.tridiumx.jsonToolkit.outbound.schema.BIJsonProperty">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</implements>
<typeParameters>
<typeVariable class="T">
</typeVariable>
</typeParameters>
<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaProperty() -->
<constructor name="BJsonSchemaProperty" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaProperty.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaProperty.getJsonName() -->
<method name="getJsonName"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaProperty.isChildLegal(javax.baja.sys.BComponent) -->
<method name="isChildLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="child">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaProperty.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
