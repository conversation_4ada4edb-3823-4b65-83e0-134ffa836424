<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="javax.baja.bacnetAws.enums.BAccumulatorStatus" name="BAccumulatorStatus" packageName="javax.baja.bacnetAws.enums" public="true" final="true">
<description>
- Insert description here.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">May 21, 2010</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;normal&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>0</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;starting&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>1</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;recovered&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>2</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;abnormal&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>3</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;failed&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>4</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnetAws.enums.BAccumulatorStatus.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnetAws.enums.BAccumulatorStatus"/>
</return>
</method>

<!-- javax.baja.bacnetAws.enums.BAccumulatorStatus.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnetAws.enums.BAccumulatorStatus"/>
</return>
</method>

<!-- javax.baja.bacnetAws.enums.BAccumulatorStatus.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnetAws.enums.BAccumulatorStatus.NORMAL -->
<field name="NORMAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for normal.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BAccumulatorStatus.STARTING -->
<field name="STARTING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for starting.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BAccumulatorStatus.RECOVERED -->
<field name="RECOVERED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for recovered.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BAccumulatorStatus.ABNORMAL -->
<field name="ABNORMAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for abnormal.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BAccumulatorStatus.FAILED -->
<field name="FAILED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for failed.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BAccumulatorStatus.normal -->
<field name="normal"  public="true" static="true" final="true">
<type class="javax.baja.bacnetAws.enums.BAccumulatorStatus"/>
<description>
BAccumulatorStatus constant for normal.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BAccumulatorStatus.starting -->
<field name="starting"  public="true" static="true" final="true">
<type class="javax.baja.bacnetAws.enums.BAccumulatorStatus"/>
<description>
BAccumulatorStatus constant for starting.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BAccumulatorStatus.recovered -->
<field name="recovered"  public="true" static="true" final="true">
<type class="javax.baja.bacnetAws.enums.BAccumulatorStatus"/>
<description>
BAccumulatorStatus constant for recovered.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BAccumulatorStatus.abnormal -->
<field name="abnormal"  public="true" static="true" final="true">
<type class="javax.baja.bacnetAws.enums.BAccumulatorStatus"/>
<description>
BAccumulatorStatus constant for abnormal.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BAccumulatorStatus.failed -->
<field name="failed"  public="true" static="true" final="true">
<type class="javax.baja.bacnetAws.enums.BAccumulatorStatus"/>
<description>
BAccumulatorStatus constant for failed.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BAccumulatorStatus.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnetAws.enums.BAccumulatorStatus"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.enums.BAccumulatorStatus.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
