<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarmOrion" runtimeProfile="rt" qualifiedName="com.tridium.alarmOrion.transactions.BOrionAlarmTransactionStatistics" name="BOrionAlarmTransactionStatistics" packageName="com.tridium.alarmOrion.transactions" public="true">
<description/>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="queueSize" flags="rts">
<type class="int"/>
<description>
Slot for the &lt;code&gt;queueSize&lt;/code&gt; property.
</description>
<tag name="@see">#getQueueSize</tag>
<tag name="@see">#setQueueSize</tag>
</property>

<property name="processRate" flags="rts">
<type class="double"/>
<description>
Slot for the &lt;code&gt;processRate&lt;/code&gt; property.
</description>
<tag name="@see">#getProcessRate</tag>
<tag name="@see">#setProcessRate</tag>
</property>

<property name="peekProcessRate" flags="rts">
<type class="double"/>
<description>
Slot for the &lt;code&gt;peekProcessRate&lt;/code&gt; property.
</description>
<tag name="@see">#getPeekProcessRate</tag>
<tag name="@see">#setPeekProcessRate</tag>
</property>

<property name="enqueueRate" flags="rts">
<type class="double"/>
<description>
Slot for the &lt;code&gt;enqueueRate&lt;/code&gt; property.
</description>
<tag name="@see">#getEnqueueRate</tag>
<tag name="@see">#setEnqueueRate</tag>
</property>

<property name="peekEnqueueRate" flags="rts">
<type class="double"/>
<description>
Slot for the &lt;code&gt;peekEnqueueRate&lt;/code&gt; property.
</description>
<tag name="@see">#getPeekEnqueueRate</tag>
<tag name="@see">#setPeekEnqueueRate</tag>
</property>

<property name="alarmCount" flags="rts">
<type class="int"/>
<description>
Slot for the &lt;code&gt;alarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmCount</tag>
<tag name="@see">#setAlarmCount</tag>
</property>

<action name="update" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;update&lt;/code&gt; action.
</description>
<tag name="@see">#update()</tag>
</action>

<action name="transactionEnqueued" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;transactionEnqueued&lt;/code&gt; action.
</description>
<tag name="@see">#transactionEnqueued()</tag>
</action>

<action name="transactionProcessed" flags="h">
<parameter name="parameter">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;transactionProcessed&lt;/code&gt; action.
</description>
<tag name="@see">#transactionProcessed(BComponent parameter)</tag>
</action>

<action name="reset" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;reset&lt;/code&gt; action.
</description>
<tag name="@see">#reset()</tag>
</action>

</class>
</bajadoc>
