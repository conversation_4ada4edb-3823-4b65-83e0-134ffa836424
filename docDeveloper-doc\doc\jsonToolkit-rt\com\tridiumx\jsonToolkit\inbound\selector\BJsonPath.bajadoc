<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.selector.BJsonPath" name="BJsonPath" packageName="com.tridiumx.jsonToolkit.inbound.selector" public="true">
<description>
Adds support for JsonPath so customers can more easily extract parts of a schema incoming.&#xa;&#xa; Json Path allow data to be interactively located and extracted from JSON&#xa; structures using a special notation to represent the payload structure.&#xa;&#xa; For the example below, the first item in the values array (1) can be selected using&#xa; a Json Path of `$.data.values.[0]`:&#xa;&#xa; ```&#xa; { &#x22;type&#x22; : &#x22;line&#x22;,&#xa;   &#x22;data&#x22; : {&#xa;     &#x22;labels&#x22; : [&#x22;Sunday&#x22;, &#x22;Monday&#x22;],&#xa;     &#x22;values&#x22; : [ 1, 2 ]&#xa;   }&#xa; }&#xa; ```&#xa;&#xa; Much more explanation of this powerful tool can be found online:&#xa;&#xa;  - [https://goessner.net/articles/JsonPath/](https://goessner.net/articles/JsonPath/)&#xa;  - [http://jsonpath.com/](http://jsonpath.com/)&#xa;  - [https://www.baeldung.com/guide-to-jayway-jsonpath](https://www.baeldung.com/guide-to-jayway-jsonpath)
</description>
<tag name="@link">https://goessner.net/articles/JsonPath/</tag>
<tag name="@link">https://github.com/json-path/JsonPath&#xa;&#xa; Library used to parse incoming json data.</tag>
<tag name="@author">Jason Woollard</tag>
<extends>
<type class="com.tridiumx.jsonToolkit.inbound.selector.BJsonStringSelector"/>
</extends>
<property name="path" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;path&lt;/code&gt; property.&#xa; A JSON Path Expression, for example $.data.values.[0]
</description>
<tag name="@see">#getPath</tag>
<tag name="@see">#setPath</tag>
</property>

<property name="selectFirstArrayElement" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;selectFirstArrayElement&lt;/code&gt; property.
</description>
<tag name="@see">#getSelectFirstArrayElement</tag>
<tag name="@see">#setSelectFirstArrayElement</tag>
</property>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonPath() -->
<constructor name="BJsonPath" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonPath.getPath() -->
<method name="getPath"  public="true">
<description>
Get the &lt;code&gt;path&lt;/code&gt; property.&#xa; A JSON Path Expression, for example $.data.values.[0]
</description>
<tag name="@see">#path</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonPath.setPath(java.lang.String) -->
<method name="setPath"  public="true">
<description>
Set the &lt;code&gt;path&lt;/code&gt; property.&#xa; A JSON Path Expression, for example $.data.values.[0]
</description>
<tag name="@see">#path</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonPath.getSelectFirstArrayElement() -->
<method name="getSelectFirstArrayElement"  public="true">
<description>
Get the &lt;code&gt;selectFirstArrayElement&lt;/code&gt; property.
</description>
<tag name="@see">#selectFirstArrayElement</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonPath.setSelectFirstArrayElement(boolean) -->
<method name="setSelectFirstArrayElement"  public="true">
<description>
Set the &lt;code&gt;selectFirstArrayElement&lt;/code&gt; property.
</description>
<tag name="@see">#selectFirstArrayElement</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonPath.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonPath.routeValue(javax.baja.sys.BString, javax.baja.sys.Context) -->
<method name="routeValue"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="message">
<type class="javax.baja.sys.BString"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.inbound.routing.RoutingFailedException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonPath.setNewGoodOutValue(java.lang.String) -->
<method name="setNewGoodOutValue"  protected="true">
<description/>
<parameter name="newString">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonPath.getRerunTriggers() -->
<method name="getRerunTriggers"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Property" dimension="1"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonPath.path -->
<field name="path"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;path&lt;/code&gt; property.&#xa; A JSON Path Expression, for example $.data.values.[0]
</description>
<tag name="@see">#getPath</tag>
<tag name="@see">#setPath</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonPath.selectFirstArrayElement -->
<field name="selectFirstArrayElement"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;selectFirstArrayElement&lt;/code&gt; property.
</description>
<tag name="@see">#getSelectFirstArrayElement</tag>
<tag name="@see">#setSelectFirstArrayElement</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonPath.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
