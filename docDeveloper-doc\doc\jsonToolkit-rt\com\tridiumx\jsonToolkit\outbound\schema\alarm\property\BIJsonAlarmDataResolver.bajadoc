<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.alarm.property.BIJsonAlarmDataResolver" name="BIJsonAlarmDataResolver" packageName="com.tridiumx.jsonToolkit.outbound.schema.alarm.property" public="true" interface="true" abstract="true" category="interface">
<description>
BIJsonAlarmDataResolver
</description>
<tag name="@author"><PERSON></tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<!-- com.tridiumx.jsonToolkit.outbound.schema.alarm.property.BIJsonAlarmDataResolver.resolve(javax.baja.alarm.BAlarmRecord) -->
<method name="resolve"  public="true" abstract="true">
<description>
Implement a method to extract the AlarmRecord Data to JSON
</description>
<parameter name="r">
<type class="javax.baja.alarm.BAlarmRecord"/>
<description>
The input AlarmRecord
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.alarm.property.BIJsonAlarmDataResolver.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.alarm.property.BIJsonAlarmDataResolver.log -->
<field name="log"  public="true" static="true" final="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

</class>
</bajadoc>
