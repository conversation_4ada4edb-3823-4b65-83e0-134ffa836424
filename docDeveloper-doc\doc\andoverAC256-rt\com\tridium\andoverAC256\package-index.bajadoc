<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="andoverAC256" runtimeProfile="rt" name="com.tridium.andoverAC256">
<description/>
<class packageName="com.tridium.andoverAC256" name="BAndoverBackupFolder"><description>BAndoverBackupFolder is the standard container to use&#xa; under BAndoverBackupTable to organize BAndoverBackupFiles.</description></class>
<class packageName="com.tridium.andoverAC256" name="BAndoverDevice"><description>Device level class for the AC256 or AC8 controller</description></class>
<class packageName="com.tridium.andoverAC256" name="BAndoverDeviceFolder"><description>AndoverAC256 implementation of BDeviceFolder</description></class>
<class packageName="com.tridium.andoverAC256" name="BAndoverNetwork"><description>BAndoverNetwork - represents an AC256 or AC8 Serial Network.</description></class>
<class packageName="com.tridium.andoverAC256" name="BAndoverScript"><description>The BAndoverScript class can be used to send multiple command lines&#xa;  to an andover panel.</description></class>
</package>
</bajadoc>
