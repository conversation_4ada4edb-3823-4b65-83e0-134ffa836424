<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.category.BICategorizable" name="BICategorizable" packageName="javax.baja.category" public="true" interface="true" abstract="true" category="interface">
<description>
BICategorizable is implemented by BObjects which can be assigned to&#xa; one or more categories through the use of a BCategoryMask.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">12 Feb 05</tag>
<tag name="@version">$Revision: 2$ $Date: 3/28/05 9:22:54 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<!-- javax.baja.category.BICategorizable.getAppliedCategoryMask() -->
<method name="getAppliedCategoryMask"  public="true" abstract="true">
<description>
Get the category mask to actually use for this object.  This&#xa; may be different from &lt;code&gt;getCategoryMask()&lt;/code&gt; if using&#xa; category inheritance.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.category.BICategorizable.getCategoryMask() -->
<method name="getCategoryMask"  public="true" abstract="true">
<description>
Get the raw category mask for this object.  If the object &#xa; supports category inheritance, then this method should return &#xa; &lt;code&gt;BCategoryMask.NULL&lt;/code&gt; and return the inherited&#xa; mask for &lt;code&gt;getAppliedCategoryMask()&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.category.BICategorizable.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
