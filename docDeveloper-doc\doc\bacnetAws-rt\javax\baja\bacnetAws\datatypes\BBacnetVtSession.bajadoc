<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="javax.baja.bacnetAws.datatypes.BBacnetVtSession" name="BBacnetVtSession" packageName="javax.baja.bacnetAws.datatypes" public="true" final="true">
<description>
BBacnetVtSession represents the BACnetVtSession data type.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">May 24, 2010</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.6 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="localVtSessionId" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;localVtSessionId&lt;/code&gt; property.
</description>
<tag name="@see">#getLocalVtSessionId</tag>
<tag name="@see">#setLocalVtSessionId</tag>
</property>

<property name="remoteVtSessionId" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;remoteVtSessionId&lt;/code&gt; property.
</description>
<tag name="@see">#getRemoteVtSessionId</tag>
<tag name="@see">#setRemoteVtSessionId</tag>
</property>

<property name="remoteVtAddress" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
Slot for the &lt;code&gt;remoteVtAddress&lt;/code&gt; property.
</description>
<tag name="@see">#getRemoteVtAddress</tag>
<tag name="@see">#setRemoteVtAddress</tag>
</property>

<!-- javax.baja.bacnetAws.datatypes.BBacnetVtSession() -->
<constructor name="BBacnetVtSession" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnetAws.datatypes.BBacnetVtSession.getLocalVtSessionId() -->
<method name="getLocalVtSessionId"  public="true">
<description>
Get the &lt;code&gt;localVtSessionId&lt;/code&gt; property.
</description>
<tag name="@see">#localVtSessionId</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetVtSession.setLocalVtSessionId(int) -->
<method name="setLocalVtSessionId"  public="true">
<description>
Set the &lt;code&gt;localVtSessionId&lt;/code&gt; property.
</description>
<tag name="@see">#localVtSessionId</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetVtSession.getRemoteVtSessionId() -->
<method name="getRemoteVtSessionId"  public="true">
<description>
Get the &lt;code&gt;remoteVtSessionId&lt;/code&gt; property.
</description>
<tag name="@see">#remoteVtSessionId</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetVtSession.setRemoteVtSessionId(int) -->
<method name="setRemoteVtSessionId"  public="true">
<description>
Set the &lt;code&gt;remoteVtSessionId&lt;/code&gt; property.
</description>
<tag name="@see">#remoteVtSessionId</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetVtSession.getRemoteVtAddress() -->
<method name="getRemoteVtAddress"  public="true">
<description>
Get the &lt;code&gt;remoteVtAddress&lt;/code&gt; property.
</description>
<tag name="@see">#remoteVtAddress</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetVtSession.setRemoteVtAddress(javax.baja.bacnet.datatypes.BBacnetAddress) -->
<method name="setRemoteVtAddress"  public="true">
<description>
Set the &lt;code&gt;remoteVtAddress&lt;/code&gt; property.
</description>
<tag name="@see">#remoteVtAddress</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetVtSession.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetVtSession.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetVtSession.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetVtSession.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetVtSession.localVtSessionId -->
<field name="localVtSessionId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;localVtSessionId&lt;/code&gt; property.
</description>
<tag name="@see">#getLocalVtSessionId</tag>
<tag name="@see">#setLocalVtSessionId</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetVtSession.remoteVtSessionId -->
<field name="remoteVtSessionId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;remoteVtSessionId&lt;/code&gt; property.
</description>
<tag name="@see">#getRemoteVtSessionId</tag>
<tag name="@see">#setRemoteVtSessionId</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetVtSession.remoteVtAddress -->
<field name="remoteVtAddress"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;remoteVtAddress&lt;/code&gt; property.
</description>
<tag name="@see">#getRemoteVtAddress</tag>
<tag name="@see">#setRemoteVtAddress</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetVtSession.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
