<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BAlarmSourceInfo" name="BAlarmSourceInfo" packageName="javax.baja.alarm" public="true">
<description>
BAlarmSourceInfo is a data structure used to define common alarm data for use&#xa; with the AlarmSupport class.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">04 Apr 05</tag>
<tag name="@version">$Revision: 10$ $Date: 5/19/11 11:08:39 AM EDT$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="alarmClass" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;alarmClass&lt;/code&gt; property.&#xa; This is the alarm class used for routing this alarm.
</description>
<tag name="@see">#getAlarmClass</tag>
<tag name="@see">#setAlarmClass</tag>
</property>

<property name="sourceName" flags="">
<type class="javax.baja.util.BFormat"/>
<description>
Slot for the &lt;code&gt;sourceName&lt;/code&gt; property.&#xa; Text descriptor for the source name of the alarm. Uses BFormat, but supports only lexicons.
</description>
<tag name="@see">#getSourceName</tag>
<tag name="@see">#setSourceName</tag>
</property>

<property name="toFaultText" flags="">
<type class="javax.baja.util.BFormat"/>
<description>
Slot for the &lt;code&gt;toFaultText&lt;/code&gt; property.&#xa; Text descriptor included in a to-fault alarm for this object. Uses BFormat.
</description>
<tag name="@see">#getToFaultText</tag>
<tag name="@see">#setToFaultText</tag>
</property>

<property name="toOffnormalText" flags="">
<type class="javax.baja.util.BFormat"/>
<description>
Slot for the &lt;code&gt;toOffnormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-offnormal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#getToOffnormalText</tag>
<tag name="@see">#setToOffnormalText</tag>
</property>

<property name="toNormalText" flags="">
<type class="javax.baja.util.BFormat"/>
<description>
Slot for the &lt;code&gt;toNormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-normal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#getToNormalText</tag>
<tag name="@see">#setToNormalText</tag>
</property>

<property name="hyperlinkOrd" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;hyperlinkOrd&lt;/code&gt; property.&#xa; Ord to link to for more information on this alarm.
</description>
<tag name="@see">#getHyperlinkOrd</tag>
<tag name="@see">#setHyperlinkOrd</tag>
</property>

<property name="soundFile" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;soundFile&lt;/code&gt; property.&#xa; Sound to play when the alarm comes into the alarm console.&#xa; Sound must be available on the client.
</description>
<tag name="@see">#getSoundFile</tag>
<tag name="@see">#setSoundFile</tag>
</property>

<property name="alarmIcon" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;alarmIcon&lt;/code&gt; property.&#xa; Icon to display for this alarm in the alarm console&#xa; Icon must be available on the client.
</description>
<tag name="@see">#getAlarmIcon</tag>
<tag name="@see">#setAlarmIcon</tag>
</property>

<property name="alarmInstructions" flags="">
<type class="javax.baja.alarm.BAlarmInstructions"/>
<description>
Slot for the &lt;code&gt;alarmInstructions&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmInstructions</tag>
<tag name="@see">#setAlarmInstructions</tag>
</property>

<property name="metaData" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;metaData&lt;/code&gt; property.&#xa; Additional user defined alarm data.
</description>
<tag name="@see">#getMetaData</tag>
<tag name="@see">#setMetaData</tag>
</property>

<!-- javax.baja.alarm.BAlarmSourceInfo() -->
<constructor name="BAlarmSourceInfo" public="true">
<description/>
</constructor>

<!-- javax.baja.alarm.BAlarmSourceInfo.getAlarmClass() -->
<method name="getAlarmClass"  public="true">
<description>
Get the &lt;code&gt;alarmClass&lt;/code&gt; property.&#xa; This is the alarm class used for routing this alarm.
</description>
<tag name="@see">#alarmClass</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.setAlarmClass(java.lang.String) -->
<method name="setAlarmClass"  public="true">
<description>
Set the &lt;code&gt;alarmClass&lt;/code&gt; property.&#xa; This is the alarm class used for routing this alarm.
</description>
<tag name="@see">#alarmClass</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.getSourceName() -->
<method name="getSourceName"  public="true">
<description>
Get the &lt;code&gt;sourceName&lt;/code&gt; property.&#xa; Text descriptor for the source name of the alarm. Uses BFormat, but supports only lexicons.
</description>
<tag name="@see">#sourceName</tag>
<return>
<type class="javax.baja.util.BFormat"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.setSourceName(javax.baja.util.BFormat) -->
<method name="setSourceName"  public="true">
<description>
Set the &lt;code&gt;sourceName&lt;/code&gt; property.&#xa; Text descriptor for the source name of the alarm. Uses BFormat, but supports only lexicons.
</description>
<tag name="@see">#sourceName</tag>
<parameter name="v">
<type class="javax.baja.util.BFormat"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.getToFaultText() -->
<method name="getToFaultText"  public="true">
<description>
Get the &lt;code&gt;toFaultText&lt;/code&gt; property.&#xa; Text descriptor included in a to-fault alarm for this object. Uses BFormat.
</description>
<tag name="@see">#toFaultText</tag>
<return>
<type class="javax.baja.util.BFormat"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.setToFaultText(javax.baja.util.BFormat) -->
<method name="setToFaultText"  public="true">
<description>
Set the &lt;code&gt;toFaultText&lt;/code&gt; property.&#xa; Text descriptor included in a to-fault alarm for this object. Uses BFormat.
</description>
<tag name="@see">#toFaultText</tag>
<parameter name="v">
<type class="javax.baja.util.BFormat"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.getToOffnormalText() -->
<method name="getToOffnormalText"  public="true">
<description>
Get the &lt;code&gt;toOffnormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-offnormal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#toOffnormalText</tag>
<return>
<type class="javax.baja.util.BFormat"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.setToOffnormalText(javax.baja.util.BFormat) -->
<method name="setToOffnormalText"  public="true">
<description>
Set the &lt;code&gt;toOffnormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-offnormal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#toOffnormalText</tag>
<parameter name="v">
<type class="javax.baja.util.BFormat"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.getToNormalText() -->
<method name="getToNormalText"  public="true">
<description>
Get the &lt;code&gt;toNormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-normal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#toNormalText</tag>
<return>
<type class="javax.baja.util.BFormat"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.setToNormalText(javax.baja.util.BFormat) -->
<method name="setToNormalText"  public="true">
<description>
Set the &lt;code&gt;toNormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-normal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#toNormalText</tag>
<parameter name="v">
<type class="javax.baja.util.BFormat"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.getHyperlinkOrd() -->
<method name="getHyperlinkOrd"  public="true">
<description>
Get the &lt;code&gt;hyperlinkOrd&lt;/code&gt; property.&#xa; Ord to link to for more information on this alarm.
</description>
<tag name="@see">#hyperlinkOrd</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.setHyperlinkOrd(javax.baja.naming.BOrd) -->
<method name="setHyperlinkOrd"  public="true">
<description>
Set the &lt;code&gt;hyperlinkOrd&lt;/code&gt; property.&#xa; Ord to link to for more information on this alarm.
</description>
<tag name="@see">#hyperlinkOrd</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.getSoundFile() -->
<method name="getSoundFile"  public="true">
<description>
Get the &lt;code&gt;soundFile&lt;/code&gt; property.&#xa; Sound to play when the alarm comes into the alarm console.&#xa; Sound must be available on the client.
</description>
<tag name="@see">#soundFile</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.setSoundFile(javax.baja.naming.BOrd) -->
<method name="setSoundFile"  public="true">
<description>
Set the &lt;code&gt;soundFile&lt;/code&gt; property.&#xa; Sound to play when the alarm comes into the alarm console.&#xa; Sound must be available on the client.
</description>
<tag name="@see">#soundFile</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.getAlarmIcon() -->
<method name="getAlarmIcon"  public="true">
<description>
Get the &lt;code&gt;alarmIcon&lt;/code&gt; property.&#xa; Icon to display for this alarm in the alarm console&#xa; Icon must be available on the client.
</description>
<tag name="@see">#alarmIcon</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.setAlarmIcon(javax.baja.naming.BOrd) -->
<method name="setAlarmIcon"  public="true">
<description>
Set the &lt;code&gt;alarmIcon&lt;/code&gt; property.&#xa; Icon to display for this alarm in the alarm console&#xa; Icon must be available on the client.
</description>
<tag name="@see">#alarmIcon</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.getAlarmInstructions() -->
<method name="getAlarmInstructions"  public="true">
<description>
Get the &lt;code&gt;alarmInstructions&lt;/code&gt; property.
</description>
<tag name="@see">#alarmInstructions</tag>
<return>
<type class="javax.baja.alarm.BAlarmInstructions"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.setAlarmInstructions(javax.baja.alarm.BAlarmInstructions) -->
<method name="setAlarmInstructions"  public="true">
<description>
Set the &lt;code&gt;alarmInstructions&lt;/code&gt; property.
</description>
<tag name="@see">#alarmInstructions</tag>
<parameter name="v">
<type class="javax.baja.alarm.BAlarmInstructions"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.getMetaData() -->
<method name="getMetaData"  public="true">
<description>
Get the &lt;code&gt;metaData&lt;/code&gt; property.&#xa; Additional user defined alarm data.
</description>
<tag name="@see">#metaData</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.setMetaData(javax.baja.sys.BFacets) -->
<method name="setMetaData"  public="true">
<description>
Set the &lt;code&gt;metaData&lt;/code&gt; property.&#xa; Additional user defined alarm data.
</description>
<tag name="@see">#metaData</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.makeAlarmData(javax.baja.alarm.BSourceState) -->
<method name="makeAlarmData"  public="true">
<description>
Make BFacets containing the the BAlarmSourceInfo properties for use in&#xa; BAlarmRecord.setAlarmData().&#xa; If a property has not been defined, it is not included in the AlarmData.&#xa; The state is used for determining which Text to use as the BAlarmRecord.MSG_TEXT.
</description>
<parameter name="state">
<type class="javax.baja.alarm.BSourceState"/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmSourceInfo.alarmClass -->
<field name="alarmClass"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmClass&lt;/code&gt; property.&#xa; This is the alarm class used for routing this alarm.
</description>
<tag name="@see">#getAlarmClass</tag>
<tag name="@see">#setAlarmClass</tag>
</field>

<!-- javax.baja.alarm.BAlarmSourceInfo.sourceName -->
<field name="sourceName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;sourceName&lt;/code&gt; property.&#xa; Text descriptor for the source name of the alarm. Uses BFormat, but supports only lexicons.
</description>
<tag name="@see">#getSourceName</tag>
<tag name="@see">#setSourceName</tag>
</field>

<!-- javax.baja.alarm.BAlarmSourceInfo.toFaultText -->
<field name="toFaultText"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;toFaultText&lt;/code&gt; property.&#xa; Text descriptor included in a to-fault alarm for this object. Uses BFormat.
</description>
<tag name="@see">#getToFaultText</tag>
<tag name="@see">#setToFaultText</tag>
</field>

<!-- javax.baja.alarm.BAlarmSourceInfo.toOffnormalText -->
<field name="toOffnormalText"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;toOffnormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-offnormal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#getToOffnormalText</tag>
<tag name="@see">#setToOffnormalText</tag>
</field>

<!-- javax.baja.alarm.BAlarmSourceInfo.toNormalText -->
<field name="toNormalText"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;toNormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-normal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#getToNormalText</tag>
<tag name="@see">#setToNormalText</tag>
</field>

<!-- javax.baja.alarm.BAlarmSourceInfo.hyperlinkOrd -->
<field name="hyperlinkOrd"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;hyperlinkOrd&lt;/code&gt; property.&#xa; Ord to link to for more information on this alarm.
</description>
<tag name="@see">#getHyperlinkOrd</tag>
<tag name="@see">#setHyperlinkOrd</tag>
</field>

<!-- javax.baja.alarm.BAlarmSourceInfo.soundFile -->
<field name="soundFile"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;soundFile&lt;/code&gt; property.&#xa; Sound to play when the alarm comes into the alarm console.&#xa; Sound must be available on the client.
</description>
<tag name="@see">#getSoundFile</tag>
<tag name="@see">#setSoundFile</tag>
</field>

<!-- javax.baja.alarm.BAlarmSourceInfo.alarmIcon -->
<field name="alarmIcon"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmIcon&lt;/code&gt; property.&#xa; Icon to display for this alarm in the alarm console&#xa; Icon must be available on the client.
</description>
<tag name="@see">#getAlarmIcon</tag>
<tag name="@see">#setAlarmIcon</tag>
</field>

<!-- javax.baja.alarm.BAlarmSourceInfo.alarmInstructions -->
<field name="alarmInstructions"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmInstructions&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmInstructions</tag>
<tag name="@see">#setAlarmInstructions</tag>
</field>

<!-- javax.baja.alarm.BAlarmSourceInfo.metaData -->
<field name="metaData"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;metaData&lt;/code&gt; property.&#xa; Additional user defined alarm data.
</description>
<tag name="@see">#getMetaData</tag>
<tag name="@see">#setMetaData</tag>
</field>

<!-- javax.baja.alarm.BAlarmSourceInfo.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
