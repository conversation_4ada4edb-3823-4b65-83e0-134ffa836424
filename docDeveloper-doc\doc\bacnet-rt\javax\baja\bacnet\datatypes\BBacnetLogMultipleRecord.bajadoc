<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord" name="BBacnetLogMultipleRecord" packageName="javax.baja.bacnet.datatypes" public="true">
<description>
BBacnetLogRecord represents the BacnetLogRecord sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">22 Mar 2010</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="timestamp" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
<description>
Slot for the &lt;code&gt;timestamp&lt;/code&gt; property.
</description>
<tag name="@see">#getTimestamp</tag>
<tag name="@see">#setTimestamp</tag>
</property>

<property name="statusFlags" flags="">
<type class="javax.baja.sys.BSimple"/>
<description>
Slot for the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#getStatusFlags</tag>
<tag name="@see">#setStatusFlags</tag>
</property>

<property name="timeChange" flags="">
<type class="javax.baja.sys.BSimple"/>
<description>
Slot for the &lt;code&gt;timeChange&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeChange</tag>
<tag name="@see">#setTimeChange</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord() -->
<constructor name="BBacnetLogMultipleRecord" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord(javax.baja.bacnet.io.AsnInput) -->
<constructor name="BBacnetLogMultipleRecord" public="true">
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
</parameter>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.getTimestamp() -->
<method name="getTimestamp"  public="true">
<description>
Get the &lt;code&gt;timestamp&lt;/code&gt; property.
</description>
<tag name="@see">#timestamp</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.setTimestamp(javax.baja.bacnet.datatypes.BBacnetDateTime) -->
<method name="setTimestamp"  public="true">
<description>
Set the &lt;code&gt;timestamp&lt;/code&gt; property.
</description>
<tag name="@see">#timestamp</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.getStatusFlags() -->
<method name="getStatusFlags"  public="true">
<description>
Get the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#statusFlags</tag>
<return>
<type class="javax.baja.sys.BSimple"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.setStatusFlags(javax.baja.sys.BSimple) -->
<method name="setStatusFlags"  public="true">
<description>
Set the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#statusFlags</tag>
<parameter name="v">
<type class="javax.baja.sys.BSimple"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.getTimeChange() -->
<method name="getTimeChange"  public="true">
<description>
Get the &lt;code&gt;timeChange&lt;/code&gt; property.
</description>
<tag name="@see">#timeChange</tag>
<return>
<type class="javax.baja.sys.BSimple"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.setTimeChange(javax.baja.sys.BSimple) -->
<method name="setTimeChange"  public="true">
<description>
Set the &lt;code&gt;timeChange&lt;/code&gt; property.
</description>
<tag name="@see">#timeChange</tag>
<parameter name="v">
<type class="javax.baja.sys.BSimple"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.getNiagaraRecordType(int) -->
<method name="getNiagaraRecordType"  public="true">
<description/>
<parameter name="n">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.util.BTypeSpec"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.initializeNiagaraRecord(javax.baja.history.BHistoryRecord, long, int) -->
<method name="initializeNiagaraRecord"  public="true">
<description/>
<parameter name="record">
<type class="javax.baja.history.BHistoryRecord"/>
</parameter>
<parameter name="seqNum">
<type class="long"/>
</parameter>
<parameter name="ndx">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.history.BHistoryRecord"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.isLogStatus() -->
<method name="isLogStatus"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.isLogData() -->
<method name="isLogData"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.getLogDataChoice() -->
<method name="getLogDataChoice"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.getTypeSpecs() -->
<method name="getTypeSpecs"  public="true">
<description/>
<return>
<type class="javax.baja.util.BTypeSpec" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.timestamp -->
<field name="timestamp"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;timestamp&lt;/code&gt; property.
</description>
<tag name="@see">#getTimestamp</tag>
<tag name="@see">#setTimestamp</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.statusFlags -->
<field name="statusFlags"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#getStatusFlags</tag>
<tag name="@see">#setStatusFlags</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.timeChange -->
<field name="timeChange"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;timeChange&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeChange</tag>
<tag name="@see">#setTimeChange</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.TIMESTAMP_TAG -->
<field name="TIMESTAMP_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.LOG_DATA_TAG -->
<field name="LOG_DATA_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.LOG_STATUS_TAG -->
<field name="LOG_STATUS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.LOG_DATA_SEQ_TAG -->
<field name="LOG_DATA_SEQ_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.TIME_CHANGE_TAG -->
<field name="TIME_CHANGE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.BOOLEAN_VALUE_TAG -->
<field name="BOOLEAN_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.REAL_VALUE_TAG -->
<field name="REAL_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.ENUM_VALUE_TAG -->
<field name="ENUM_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.UNSIGNED_VALUE_TAG -->
<field name="UNSIGNED_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.SIGNED_VALUE_TAG -->
<field name="SIGNED_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.BITSTRING_VALUE_TAG -->
<field name="BITSTRING_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.NULL_VALUE_TAG -->
<field name="NULL_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.FAILURE_TAG -->
<field name="FAILURE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogMultipleRecord.ANY_VALUE_TAG -->
<field name="ANY_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
