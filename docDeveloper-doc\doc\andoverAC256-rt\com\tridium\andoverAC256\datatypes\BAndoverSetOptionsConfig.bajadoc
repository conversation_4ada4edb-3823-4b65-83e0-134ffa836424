<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.datatypes.BAndoverSetOptionsConfig" name="BAndoverSetOptionsConfig" packageName="com.tridium.andoverAC256.datatypes" public="true">
<description>
AndoverAC256 Set Options Config, used to set options in the&#xa; andover panel using the BAndoverSetOptionsJob.
</description>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@creation">4/26/2005 11:15AM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.82</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="consolePortSpeed" flags="">
<type class="javax.baja.serial.BSerialBaudRate"/>
<description>
Slot for the &lt;code&gt;consolePortSpeed&lt;/code&gt; property.
</description>
<tag name="@see">#getConsolePortSpeed</tag>
<tag name="@see">#setConsolePortSpeed</tag>
</property>

<property name="consoleInactivityAutoLogoff" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;consoleInactivityAutoLogoff&lt;/code&gt; property.
</description>
<tag name="@see">#getConsoleInactivityAutoLogoff</tag>
<tag name="@see">#setConsoleInactivityAutoLogoff</tag>
</property>

<property name="consoleMessageBuffering" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;consoleMessageBuffering&lt;/code&gt; property.
</description>
<tag name="@see">#getConsoleMessageBuffering</tag>
<tag name="@see">#setConsoleMessageBuffering</tag>
</property>

<property name="sPortSpeed" flags="">
<type class="javax.baja.serial.BSerialBaudRate"/>
<description>
Slot for the &lt;code&gt;sPortSpeed&lt;/code&gt; property.
</description>
<tag name="@see">#getSPortSpeed</tag>
<tag name="@see">#setSPortSpeed</tag>
</property>

<property name="sPortInactivityAutoLogoff" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;sPortInactivityAutoLogoff&lt;/code&gt; property.
</description>
<tag name="@see">#getSPortInactivityAutoLogoff</tag>
<tag name="@see">#setSPortInactivityAutoLogoff</tag>
</property>

<property name="sPortMessageBuffering" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;sPortMessageBuffering&lt;/code&gt; property.
</description>
<tag name="@see">#getSPortMessageBuffering</tag>
<tag name="@see">#setSPortMessageBuffering</tag>
</property>

<property name="masterIDNumber" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;masterIDNumber&lt;/code&gt; property.
</description>
<tag name="@see">#getMasterIDNumber</tag>
<tag name="@see">#setMasterIDNumber</tag>
</property>

<property name="xBusSpeed" flags="">
<type class="javax.baja.serial.BSerialBaudRate"/>
<description>
Slot for the &lt;code&gt;xBusSpeed&lt;/code&gt; property.
</description>
<tag name="@see">#getXBusSpeed</tag>
<tag name="@see">#setXBusSpeed</tag>
</property>

<property name="lBusSpeed" flags="">
<type class="javax.baja.serial.BSerialBaudRate"/>
<description>
Slot for the &lt;code&gt;lBusSpeed&lt;/code&gt; property.
</description>
<tag name="@see">#getLBusSpeed</tag>
<tag name="@see">#setLBusSpeed</tag>
</property>

<property name="acnetSpeed" flags="">
<type class="javax.baja.serial.BSerialBaudRate"/>
<description>
Slot for the &lt;code&gt;acnetSpeed&lt;/code&gt; property.
</description>
<tag name="@see">#getAcnetSpeed</tag>
<tag name="@see">#setAcnetSpeed</tag>
</property>

</class>
</bajadoc>
