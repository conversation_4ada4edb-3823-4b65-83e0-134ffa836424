<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.db.file.BFileAlarmDbConfig" name="BFileAlarmDbConfig" packageName="com.tridium.alarm.db.file" public="true">
<description>
BFileAlarmDbConfig is the configuration for the alarm database.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">09 June 2014</tag>
<tag name="@since">Niagara 4.0</tag>
<extends>
<type class="javax.baja.alarm.BAlarmDbConfig"/>
</extends>
<implements>
<type class="javax.baja.sys.BIUnlinkableSlotsContainer"/>
</implements>
<property name="capacity" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;capacity&lt;/code&gt; property.
</description>
<tag name="@see">#getCapacity</tag>
<tag name="@see">#setCapacity</tag>
</property>

</class>
</bajadoc>
