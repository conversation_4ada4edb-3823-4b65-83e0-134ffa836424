<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker" name="BJsonExportMarker" packageName="com.tridiumx.jsonToolkit.exportMarker" public="true">
<description>
This component in added to components which should be found via BQL and exported to a Relative Schema
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionEventFilter"/>
</implements>
<property name="id" flags="rd">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;id&lt;/code&gt; property.&#xa; This value allows an id to be provided from the cloud platform. The expectation is that this value will be unique,&#xa; at least within each station as it may be used by the cloud platform as a primary key.
</description>
<tag name="@see">#getId</tag>
<tag name="@see">#setId</tag>
</property>

<property name="sendHistorySince" flags="rdh">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;sendHistorySince&lt;/code&gt; property.&#xa; Filter should discard history records from before this date
</description>
<tag name="@see">#getSendHistorySince</tag>
<tag name="@see">#setSendHistorySince</tag>
</property>

<property name="sendAlarmSince" flags="rdh">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;sendAlarmSince&lt;/code&gt; property.&#xa; Filter should discard alarm records from before this date
</description>
<tag name="@see">#getSendAlarmSince</tag>
<tag name="@see">#setSendAlarmSince</tag>
</property>

<property name="platformWritable" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;platformWritable&lt;/code&gt; property.&#xa; Used with setpoint/override feature to prevent writes from the upstream platform
</description>
<tag name="@see">#getPlatformWritable</tag>
<tag name="@see">#setPlatformWritable</tag>
</property>

<action name="resetId" flags="co">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;resetId&lt;/code&gt; action.
</description>
<tag name="@see">#resetId()</tag>
</action>

<action name="forceSetId" flags="coh">
<parameter name="parameter">
<type class="javax.baja.sys.BString"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;forceSetId&lt;/code&gt; action.
</description>
<tag name="@see">#forceSetId(BString parameter)</tag>
</action>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker() -->
<constructor name="BJsonExportMarker" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.getId() -->
<method name="getId"  public="true">
<description>
Get the &lt;code&gt;id&lt;/code&gt; property.&#xa; This value allows an id to be provided from the cloud platform. The expectation is that this value will be unique,&#xa; at least within each station as it may be used by the cloud platform as a primary key.
</description>
<tag name="@see">#id</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.setId(java.lang.String) -->
<method name="setId"  public="true">
<description>
Set the &lt;code&gt;id&lt;/code&gt; property.&#xa; This value allows an id to be provided from the cloud platform. The expectation is that this value will be unique,&#xa; at least within each station as it may be used by the cloud platform as a primary key.
</description>
<tag name="@see">#id</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.getSendHistorySince() -->
<method name="getSendHistorySince"  public="true">
<description>
Get the &lt;code&gt;sendHistorySince&lt;/code&gt; property.&#xa; Filter should discard history records from before this date
</description>
<tag name="@see">#sendHistorySince</tag>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.setSendHistorySince(javax.baja.sys.BAbsTime) -->
<method name="setSendHistorySince"  public="true">
<description>
Set the &lt;code&gt;sendHistorySince&lt;/code&gt; property.&#xa; Filter should discard history records from before this date
</description>
<tag name="@see">#sendHistorySince</tag>
<parameter name="v">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.getSendAlarmSince() -->
<method name="getSendAlarmSince"  public="true">
<description>
Get the &lt;code&gt;sendAlarmSince&lt;/code&gt; property.&#xa; Filter should discard alarm records from before this date
</description>
<tag name="@see">#sendAlarmSince</tag>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.setSendAlarmSince(javax.baja.sys.BAbsTime) -->
<method name="setSendAlarmSince"  public="true">
<description>
Set the &lt;code&gt;sendAlarmSince&lt;/code&gt; property.&#xa; Filter should discard alarm records from before this date
</description>
<tag name="@see">#sendAlarmSince</tag>
<parameter name="v">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.getPlatformWritable() -->
<method name="getPlatformWritable"  public="true">
<description>
Get the &lt;code&gt;platformWritable&lt;/code&gt; property.&#xa; Used with setpoint/override feature to prevent writes from the upstream platform
</description>
<tag name="@see">#platformWritable</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.setPlatformWritable(boolean) -->
<method name="setPlatformWritable"  public="true">
<description>
Set the &lt;code&gt;platformWritable&lt;/code&gt; property.&#xa; Used with setpoint/override feature to prevent writes from the upstream platform
</description>
<tag name="@see">#platformWritable</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.resetId() -->
<method name="resetId"  public="true">
<description>
Invoke the &lt;code&gt;resetId&lt;/code&gt; action.
</description>
<tag name="@see">#resetId</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.forceSetId(javax.baja.sys.BString) -->
<method name="forceSetId"  public="true">
<description>
Invoke the &lt;code&gt;forceSetId&lt;/code&gt; action.
</description>
<tag name="@see">#forceSetId</tag>
<parameter name="parameter">
<type class="javax.baja.sys.BString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.make(java.lang.String, boolean) -->
<method name="make"  public="true" static="true">
<description/>
<parameter name="id">
<type class="java.lang.String"/>
</parameter>
<parameter name="writable">
<type class="boolean"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.doResetId(javax.baja.sys.Context) -->
<method name="doResetId"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unused&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.exportMarker.DuplicateExportIdException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.doForceSetId(javax.baja.sys.BString, javax.baja.sys.Context) -->
<method name="doForceSetId"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unused&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<parameter name="id">
<type class="javax.baja.sys.BString"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.exportMarker.DuplicateExportIdException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.checkInvalidId() -->
<method name="checkInvalidId"  public="true">
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.ExportMarkerIdInvalidException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.started() -->
<method name="started"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.exportMarker.DuplicateExportIdException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.stopped() -->
<method name="stopped"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.test(javax.baja.sys.BComponentEvent) -->
<method name="test"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="event">
<type class="javax.baja.sys.BComponentEvent"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.support.FilterResult"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Add export indicator to icon
</description>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.addExportBadge(javax.baja.sys.BIcon) -->
<method name="addExportBadge"  public="true" static="true">
<description>
Add export indicator to icon
</description>
<parameter name="base">
<type class="javax.baja.sys.BIcon"/>
</parameter>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.id -->
<field name="id"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;id&lt;/code&gt; property.&#xa; This value allows an id to be provided from the cloud platform. The expectation is that this value will be unique,&#xa; at least within each station as it may be used by the cloud platform as a primary key.
</description>
<tag name="@see">#getId</tag>
<tag name="@see">#setId</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.sendHistorySince -->
<field name="sendHistorySince"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;sendHistorySince&lt;/code&gt; property.&#xa; Filter should discard history records from before this date
</description>
<tag name="@see">#getSendHistorySince</tag>
<tag name="@see">#setSendHistorySince</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.sendAlarmSince -->
<field name="sendAlarmSince"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;sendAlarmSince&lt;/code&gt; property.&#xa; Filter should discard alarm records from before this date
</description>
<tag name="@see">#getSendAlarmSince</tag>
<tag name="@see">#setSendAlarmSince</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.platformWritable -->
<field name="platformWritable"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;platformWritable&lt;/code&gt; property.&#xa; Used with setpoint/override feature to prevent writes from the upstream platform
</description>
<tag name="@see">#getPlatformWritable</tag>
<tag name="@see">#setPlatformWritable</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.resetId -->
<field name="resetId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;resetId&lt;/code&gt; action.
</description>
<tag name="@see">#resetId()</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.forceSetId -->
<field name="forceSetId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;forceSetId&lt;/code&gt; action.
</description>
<tag name="@see">#forceSetId(BString parameter)</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.log -->
<field name="log"  public="true" static="true" final="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.FILTER_ENABLED_SLOT -->
<field name="FILTER_ENABLED_SLOT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.TOLERANCE_SLOT -->
<field name="TOLERANCE_SLOT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker.LAST_VALUE_SLOT -->
<field name="LAST_VALUE_SLOT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

</class>
</bajadoc>
