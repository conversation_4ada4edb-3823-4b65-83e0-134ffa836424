<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.exportMarker.filter">
<description/>
<class packageName="com.tridiumx.jsonToolkit.exportMarker.filter" name="BAbstractExportMarkerFilter"><description>Contains properties common to all forms of filter accepting Export Marked&#xa; components for consideration.</description></class>
<class packageName="com.tridiumx.jsonToolkit.exportMarker.filter" name="BAlarmExportMarkerFilter"><description>Passes alarms to a JsonRecipient if they have an Export Marker on the source point, which has been &#x22;registered&#x22; in&#xa; the cloud recipient as indicated by the presence of an Id on the Export Marker.</description></class>
<class packageName="com.tridiumx.jsonToolkit.exportMarker.filter" name="BAlarmFilterMode"><description>Allows Alarm export marker filter behaviour selection&#xa;&#xa; Marked With Id: If the Source has an Export Marker present, _with Id set_&#xa; Marked: If the source has an Export Marker present&#xa; Pass All: All Alarms&#xa; Block All: No Alarms</description></class>
<class packageName="com.tridiumx.jsonToolkit.exportMarker.filter" name="BHistoryExportMarkerFilter"><description>Does not support for history imported from another device, requires the use of another schema/query builder</description></class>
<class packageName="com.tridiumx.jsonToolkit.exportMarker.filter" name="ExportMarkerIdInvalidException" category="exception"><description>Indicates an empty export marker id (which can be a valid configuration)</description></class>
<class packageName="com.tridiumx.jsonToolkit.exportMarker.filter" name="ExportMarkerNotFoundException" category="exception"><description>If an object does not have an Export Marker</description></class>
</package>
</bajadoc>
