<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.datatypes.BWhoHasConfig" name="BWhoHasConfig" packageName="com.tridium.bacnet.datatypes" public="true">
<description>
This class file specifies parameters to constrain a&#xa; Who-Has request.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">06 Sep 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.datatypes.BDeviceDiscoveryConfig"/>
</extends>
<property name="use" flags="">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;use&lt;/code&gt; property.
</description>
<tag name="@see">#getUse</tag>
<tag name="@see">#setUse</tag>
</property>

<property name="objectName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;objectName&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectName</tag>
<tag name="@see">#setObjectName</tag>
</property>

<property name="objectId" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

</class>
</bajadoc>
