<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.io.IllegalActionInitiationError" name="IllegalActionInitiationError" packageName="javax.baja.bacnet.io" public="true" category="exception">
<description/>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">16 Aug 2019</tag>
<tag name="@since">Niagara 4.9</tag>
<extends>
<type class="javax.baja.sys.LocalizableRuntimeException"/>
</extends>
<!-- javax.baja.bacnet.io.IllegalActionInitiationError(java.lang.String, java.lang.String, java.lang.Object[]) -->
<constructor name="IllegalActionInitiationError" public="true">
<parameter name="lexiconModule">
<type class="java.lang.String"/>
</parameter>
<parameter name="lexiconKey">
<type class="java.lang.String"/>
</parameter>
<parameter name="lexiconArgs">
<type class="java.lang.Object" dimension="1"/>
</parameter>
<description/>
</constructor>

</class>
</bajadoc>
