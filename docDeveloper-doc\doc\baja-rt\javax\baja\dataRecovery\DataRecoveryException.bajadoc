<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.dataRecovery.DataRecoveryException" name="DataRecoveryException" packageName="javax.baja.dataRecovery" public="true" category="exception">
<description>
Base exception class for data recovery.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">7 July 09</tag>
<tag name="@version">Original</tag>
<tag name="@since">Niagara 3.6</tag>
<extends>
<type class="javax.baja.sys.BajaRuntimeException"/>
</extends>
<!-- javax.baja.dataRecovery.DataRecoveryException(java.lang.String, java.lang.Throwable) -->
<constructor name="DataRecoveryException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="e">
<type class="java.lang.Throwable"/>
</parameter>
<description>
Constructor with specified message and nested exception.
</description>
</constructor>

<!-- javax.baja.dataRecovery.DataRecoveryException(java.lang.Throwable) -->
<constructor name="DataRecoveryException" public="true">
<parameter name="e">
<type class="java.lang.Throwable"/>
</parameter>
<description>
Constructor with specified nested exception.
</description>
</constructor>

<!-- javax.baja.dataRecovery.DataRecoveryException(java.lang.String) -->
<constructor name="DataRecoveryException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<description>
Construct a DataRecoveryException with the given message.
</description>
</constructor>

<!-- javax.baja.dataRecovery.DataRecoveryException() -->
<constructor name="DataRecoveryException" public="true">
<description>
No argument constructor.
</description>
</constructor>

</class>
</bajadoc>
