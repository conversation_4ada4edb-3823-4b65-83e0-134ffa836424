<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetCharacterStringDescriptor" name="BBacnetCharacterStringDescriptor" packageName="javax.baja.bacnet.export" public="true">
<description>
BBacnetCharacterStringDescriptor is the extension that exposes&#xa; Bacnet CharacterString capability.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@since">12/7/2013</tag>
<extends>
<type class="javax.baja.bacnet.export.BBacnetPointDescriptor"/>
</extends>
<implements>
<type class="javax.baja.bacnet.export.BacnetWritableDescriptor"/>
</implements>
<property name="bacnetWritable" flags="rh">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;bacnetWritable&lt;/code&gt; property.
</description>
<tag name="@see">#getBacnetWritable</tag>
<tag name="@see">#setBacnetWritable</tag>
</property>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor() -->
<constructor name="BBacnetCharacterStringDescriptor" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.getBacnetWritable() -->
<method name="getBacnetWritable"  public="true">
<description>
Get the &lt;code&gt;bacnetWritable&lt;/code&gt; property.
</description>
<tag name="@see">#bacnetWritable</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.setBacnetWritable(java.lang.String) -->
<method name="setBacnetWritable"  public="true">
<description>
Set the &lt;code&gt;bacnetWritable&lt;/code&gt; property.
</description>
<tag name="@see">#bacnetWritable</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.isPointTypeLegal(javax.baja.control.BControlPoint) -->
<method name="isPointTypeLegal"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
BBacnetCharacterStringDescriptor may only expose a BStringPoint.
</description>
<parameter name="pt">
<type class="javax.baja.control.BControlPoint"/>
<description>
the exposed point
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the Niagara point type is legal for this point type.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.getEventType() -->
<method name="getEventType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the BACnetEventType reported by this object.
</description>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.isValidAlarmExt(javax.baja.alarm.BIAlarmSource) -->
<method name="isValidAlarmExt"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is the given alarm source ext a valid extension for&#xa; exporting BACnet alarm properties?  This determines if the&#xa; given alarm source extension follows the appropriate algorithm&#xa; defined for the intrinsic alarming of a particular object&#xa; type as required by the BACnet specification.&lt;p&gt;&#xa; All BACnet Analog points use an OutOfRange alarm algorithm.
</description>
<parameter name="ext">
<type class="javax.baja.alarm.BIAlarmSource"/>
<description/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if valid, otherwise false.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.isCommandable() -->
<method name="isCommandable"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.readProperty(int, int) -->
<method name="readProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.readOptionalProperty(int, int) -->
<method name="readOptionalProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="pId">
<type class="int"/>
</parameter>
<parameter name="ndx">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.writeProperty(int, int, byte[], int) -->
<method name="writeProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.writeOptionalProperty(int, int, byte[], int) -->
<method name="writeOptionalProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="pId">
<type class="int"/>
</parameter>
<parameter name="ndx">
<type class="int"/>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
</parameter>
<parameter name="pri">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.addRequiredProps(java.util.Vector) -->
<method name="addRequiredProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Subclass override method to add required properties.&#xa; NOTE: You MUST call super.addRequiredProps(v) first!
</description>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
Vector containing required propertyIds.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.addOptionalProps(java.util.Vector) -->
<method name="addOptionalProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Subclass override method to add optional properties.&#xa; NOTE: You MUST call super.addOptionalProps(v) first!
</description>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
Vector containing optional propertyIds.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.validate() -->
<method name="validate"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Override point for subclasses to validate their exposed point&#x27;s&#xa; current state.  Default implementation does nothing.  Some points may&#xa; set the BACnet status flags to fault if the Niagara value is disallowed&#xa; for the exposed BACnet object type.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.checkCov(javax.baja.status.BStatusValue, javax.baja.status.BStatusValue) -->
<method name="checkCov"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Check to see if the current value requires a COV notification.
</description>
<parameter name="currentValue">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<parameter name="covValue">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the currentValue is different than the cov value, or the status bits have changed
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.knobAdded(javax.baja.sys.Knob, javax.baja.sys.Context) -->
<method name="knobAdded"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="knob">
<type class="javax.baja.sys.Knob"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.knobRemoved(javax.baja.sys.Knob, javax.baja.sys.Context) -->
<method name="knobRemoved"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="knob">
<type class="javax.baja.sys.Knob"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.doMakeWritable(javax.baja.sys.BValue) -->
<method name="doMakeWritable"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="writable">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this point is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.bacnetWritable -->
<field name="bacnetWritable"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;bacnetWritable&lt;/code&gt; property.
</description>
<tag name="@see">#getBacnetWritable</tag>
<tag name="@see">#setBacnetWritable</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetCharacterStringDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
