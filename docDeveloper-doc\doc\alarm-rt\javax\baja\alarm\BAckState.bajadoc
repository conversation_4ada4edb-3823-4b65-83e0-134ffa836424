<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BAckState" name="BAckState" packageName="javax.baja.alarm" public="true" final="true">
<description>
BAckState represents the states of acknowledgement for an alarm.&#xa; &lt;br&gt;&#xa; &lt;ul&gt;&#xa;  &lt;li&gt;unacked: a &#x27;new&#x27; unacknowledged alarm&lt;/li&gt;&#xa;  &lt;li&gt;ackPending: a user has requested that the alrm be acked&lt;/li&gt;&#xa;  &lt;li&gt;acked: the source has recieved the ack request and acknowledged the alarm&lt;/li&gt;&#xa; &lt;/ul&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">14 Sep 04</tag>
<tag name="@version">$Revision: 3$ $Date: 3/30/05 11:35:59 AM EST$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;acked&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unacked&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ackPending&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.alarm.BAckState.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAckState"/>
</return>
</method>

<!-- javax.baja.alarm.BAckState.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAckState"/>
</return>
</method>

<!-- javax.baja.alarm.BAckState.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.BAckState.ACKED -->
<field name="ACKED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for acked.
</description>
</field>

<!-- javax.baja.alarm.BAckState.UNACKED -->
<field name="UNACKED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unacked.
</description>
</field>

<!-- javax.baja.alarm.BAckState.ACK_PENDING -->
<field name="ACK_PENDING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ackPending.
</description>
</field>

<!-- javax.baja.alarm.BAckState.acked -->
<field name="acked"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BAckState"/>
<description>
BAckState constant for acked.
</description>
</field>

<!-- javax.baja.alarm.BAckState.unacked -->
<field name="unacked"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BAckState"/>
<description>
BAckState constant for unacked.
</description>
</field>

<!-- javax.baja.alarm.BAckState.ackPending -->
<field name="ackPending"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BAckState"/>
<description>
BAckState constant for ackPending.
</description>
</field>

<!-- javax.baja.alarm.BAckState.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BAckState"/>
<description/>
</field>

<!-- javax.baja.alarm.BAckState.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
