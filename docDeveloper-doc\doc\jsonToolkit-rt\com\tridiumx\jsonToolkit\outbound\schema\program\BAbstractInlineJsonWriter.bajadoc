<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.program.BAbstractInlineJsonWriter" name="BAbstractInlineJsonWriter" packageName="com.tridiumx.jsonToolkit.outbound.schema.program" public="true" abstract="true">
<description>
Allows the schema to defer control to a Program in the tree of Schema members, meaning the user can&#xa; do as they please to insert dynamic content in the Schema output.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaMember"/>
</extends>
<!-- com.tridiumx.jsonToolkit.outbound.schema.program.BAbstractInlineJsonWriter() -->
<constructor name="BAbstractInlineJsonWriter" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.program.BAbstractInlineJsonWriter.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.program.BAbstractInlineJsonWriter.processChildJsonMembers(com.tridium.json.JSONWriter, boolean) -->
<method name="processChildJsonMembers"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="json">
<type class="com.tridium.json.JSONWriter"/>
</parameter>
<parameter name="jsonKeysValid">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.program.BAbstractInlineJsonWriter.getJsonName() -->
<method name="getJsonName"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.program.BAbstractInlineJsonWriter.getCurrentBase() -->
<method name="getCurrentBase"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unused&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BComplex"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.program.BAbstractInlineJsonWriter.getJsonWriter() -->
<method name="getJsonWriter"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unused&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<return>
<type class="com.tridium.json.JSONWriter"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.program.BAbstractInlineJsonWriter.isJsonKeysValid() -->
<method name="isJsonKeysValid"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unused&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Reflect where in a Json Structure the writer currently is, and if&#xa; keys can be added at this point
</description>
<return>
<type class="boolean"/>
<description>
jsonKeysValid true if we are currently inside an object.
</description>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.program.BAbstractInlineJsonWriter.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Add program indicator to icon
</description>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.program.BAbstractInlineJsonWriter.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
