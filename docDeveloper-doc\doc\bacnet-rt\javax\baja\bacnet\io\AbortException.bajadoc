<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.io.AbortException" name="AbortException" packageName="javax.baja.bacnet.io" public="true" category="exception">
<description>
AbortExceptions are thrown when an error is encountered that&#xa; should result in a transaction being aborted.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 2$ $Date: 11/29/01 1:24:00 PM$</tag>
<tag name="@creation">31 Jul 00</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.BacnetException"/>
</extends>
<!-- javax.baja.bacnet.io.AbortException(int) -->
<constructor name="AbortException" public="true">
<parameter name="abortReason">
<type class="int"/>
<description>
the Bacnet Abort Reason associated with this exception.
</description>
</parameter>
<description>
Constructor
</description>
</constructor>

<!-- javax.baja.bacnet.io.AbortException.getAbortReason() -->
<method name="getAbortReason"  public="true">
<description>
Returns the BBacnetAbortReason
</description>
<return>
<type class="int"/>
<description>
the Bacnet Abort Reason associated with this exception.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.AbortException.toString() -->
<method name="toString"  public="true">
<description>
To String.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

</class>
</bajadoc>
