<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetNodeType" name="BBacnetNodeType" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetNodeType represents the BACnetNodeType enumeration.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">20 Jan 2009</tag>
<tag name="@since">Niagara 3.5</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknown&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;system&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;network&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;device&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;organizational&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;area&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;equipment&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;point&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;collection&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;property&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;functional&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;other&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetNodeType.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.UNKNOWN -->
<field name="UNKNOWN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknown.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.SYSTEM -->
<field name="SYSTEM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for system.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.NETWORK -->
<field name="NETWORK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for network.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.DEVICE -->
<field name="DEVICE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for device.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.ORGANIZATIONAL -->
<field name="ORGANIZATIONAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for organizational.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.AREA -->
<field name="AREA"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for area.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.EQUIPMENT -->
<field name="EQUIPMENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for equipment.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.POINT -->
<field name="POINT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for point.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.COLLECTION -->
<field name="COLLECTION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for collection.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.PROPERTY -->
<field name="PROPERTY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for property.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.FUNCTIONAL -->
<field name="FUNCTIONAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for functional.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.OTHER -->
<field name="OTHER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for other.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.unknown -->
<field name="unknown"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
<description>
BBacnetNodeType constant for unknown.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.system -->
<field name="system"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
<description>
BBacnetNodeType constant for system.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.network -->
<field name="network"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
<description>
BBacnetNodeType constant for network.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.device -->
<field name="device"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
<description>
BBacnetNodeType constant for device.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.organizational -->
<field name="organizational"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
<description>
BBacnetNodeType constant for organizational.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.area -->
<field name="area"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
<description>
BBacnetNodeType constant for area.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.equipment -->
<field name="equipment"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
<description>
BBacnetNodeType constant for equipment.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.point -->
<field name="point"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
<description>
BBacnetNodeType constant for point.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.collection -->
<field name="collection"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
<description>
BBacnetNodeType constant for collection.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.property -->
<field name="property"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
<description>
BBacnetNodeType constant for property.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.functional -->
<field name="functional"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
<description>
BBacnetNodeType constant for functional.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.other -->
<field name="other"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
<description>
BBacnetNodeType constant for other.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNodeType.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
