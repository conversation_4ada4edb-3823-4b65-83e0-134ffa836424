<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.query.QueryFailReasons" name="QueryFailReasons" packageName="com.tridiumx.jsonToolkit.outbound.schema.query" public="true" final="true" category="enum">
<description>
Enumeration of the various ways query execution can fail.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<parameterizedType class="java.lang.Enum">
<args>
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.QueryFailReasons"/>
</args>
</parameterizedType>
</extends>
<!-- com.tridiumx.jsonToolkit.outbound.schema.query.QueryFailReasons.values() -->
<method name="values"  public="true" static="true">
<description/>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.QueryFailReasons" dimension="1"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.QueryFailReasons.valueOf(java.lang.String) -->
<method name="valueOf"  public="true" static="true">
<description/>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.QueryFailReasons"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.QueryFailReasons.getReason() -->
<method name="getReason"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.QueryFailReasons.TIMEOUT -->
<field name="TIMEOUT" enum="true"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.QueryFailReasons"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.QueryFailReasons.OVERLAP -->
<field name="OVERLAP" enum="true"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.QueryFailReasons"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.QueryFailReasons.RUNTIME -->
<field name="RUNTIME" enum="true"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.QueryFailReasons"/>
<description/>
</field>

</class>
</bajadoc>
