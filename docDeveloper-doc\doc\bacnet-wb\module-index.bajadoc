<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="bacnet" runtimeProfile="wb" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>Niagara BACnet Driver</description>
<package name="com.tridium.bacnet.ui.virtual"/>
<package name="com.tridium.bacnet.ui.history"/>
<package name="com.tridium.bacnet.ui.export"/>
<package name="com.tridium.bacnet.ui.fe"/>
<package name="com.tridium.bacnet.ui.schedule"/>
<package name="com.tridium.bacnet.ui.device"/>
<package name="com.tridium.bacnet.ui.ip"/>
<package name="com.tridium.bacnet.ui.file"/>
<package name="com.tridium.bacnet.ui.point"/>
<package name="com.tridium.bacnet.ui.config"/>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetAddressFE"><description>BBacnetAddressFE allows viewing and editing of a BBacnetAddress.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetAnyFE"><description>BBacnetAnyFE allows viewing and editing of a BBacnetAny.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetBinaryPvFE"><description>BBacnetBinaryPvFE allows viewing and editing of a BBacnetBinaryPv&#xa; using a text field.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetBitStringFE"><description>BBacnetBitStringFE allows viewing and editing of a&#xa; BBacnetBitString.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetCalendarEntryFE"><description>BBacnetCalendarEntryFE allows viewing and editing of a BBacnetCalendarEntry.</description></class>
<class packageName="com.tridium.bacnet.ui.config" name="BBacnetConfigManager"><description>BBacnetConfigManager allows the user to create and manage config&#xa; objects within a BACnet device.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetDateFE"><description>BBacnetDateFE allows viewing and editing of a BBacnetDate.</description></class>
<class packageName="com.tridium.bacnet.ui.device" name="BBacnetDeviceManager"><description>BBacnetDeviceManager is the specialization of BDeviceManager to&#xa; provide a way for the user to discover, create, view, and edit Bacnet&#xa; devices in Niagara.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetEventTypeFE"><description>BBacnetEventTypeFE allows the user to manage&#xa; the event parameters of an event enrollment object</description></class>
<class packageName="com.tridium.bacnet.ui.export" name="BBacnetExportManager"><description>BBacnetExportManager.</description></class>
<class packageName="com.tridium.bacnet.ui.export" name="BBacnetFileExportManager"><description>BBacnetFileExportManager.</description></class>
<class packageName="com.tridium.bacnet.ui.file" name="BBacnetHexFileEditor"><description>BBacnetHexFileEditor</description></class>
<class packageName="com.tridium.bacnet.ui.history" name="BBacnetHistoryImportManager"><description>BBacnetHistoryImportManager is the implementation of BHistoryImportManager&#xa; for managing history imports from a remote BACnet device into the&#xa; local station.</description></class>
<class packageName="com.tridium.bacnet.ui.device" name="BBacnetLearnTable"></class>
<class packageName="com.tridium.bacnet.ui.export" name="BBacnetMgrTable"><description>BBacnetMgrTable is a custom implementation of the BMgrTable&#xa; infrastructure made in onder to listen for delete events on the&#xa; table.</description></class>
<class packageName="com.tridium.bacnet.ui.export" name="BBacnetNiagaraLogExportManager"><description>BBacnetNiagaraLogExportManager.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetNotifyTypeFE"><description>BBacnetNotifyTypeFE allows editing of BBacnetNotifyType.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetNullFE"><description>BBacnetNullFE allows viewing of a BBacnetNull&#xa; using a text field.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetObjectIdentifierFE"><description>BBacnetObjectIdentifierFE allows viewing and editing of a BBacnetObjectIdentifier.</description></class>
<class packageName="com.tridium.bacnet.ui.point" name="BBacnetPointManager"><description>BBacnetPointManager uses the BAbstractLearn framework to&#xa; provide a way for the user to create proxy points within&#xa; a BBacnetDevice.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetPriorityValueFE"><description>BBacnetPriorityValueFE allows viewing and editing of a BBacnetPriorityValue.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetRecipientFE"><description>BBacnetRecipientFE allows viewing and editing of a BBacnetRecipient.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetScaleFE"><description>BBacnetScaleFE allows viewing and editing of a BBacnetScale.</description></class>
<class packageName="com.tridium.bacnet.ui.schedule" name="BBacnetScheduleExportManager"><description>BBacnetScheduleExportManager is used for configuring schedules to be imported&#xa; from a remote BACnet device into the Niagara station.</description></class>
<class packageName="com.tridium.bacnet.ui.schedule" name="BBacnetScheduleImportManager"><description>BBacnetScheduleImportManager is used for configuring schedules to be imported&#xa; from a remote BACnet device into the Niagara station.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetTimeFE"><description>BBacnetTimeFE allows viewing and editing of a BBacnetTime.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetTimeStampFE"><description>BBacnetTimeStampFE allows viewing and editing of a BBacnetTimeStamp.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetTimeValueFE"><description>BBacnetTimeValueFE allows viewing and editing of a BBacnetTimeValue.</description></class>
<class packageName="com.tridium.bacnet.ui.history" name="BBacnetTrendMultipleView"><description>BBacnetTrendMultipleView provides a table view of all histories&#xa; associated with a single Bacnet TrendLogMultiple object.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetUnsignedFE"><description>BBacnetUnsignedFE allows viewing and editing of a BBacnetUnsigned&#xa; using a text field.</description></class>
<class packageName="com.tridium.bacnet.ui.virtual" name="BBacnetVirtualPropertyView"></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetWeekNDayFE"><description>BBacnetWeekNDayFE allows user-friendly configuration&#xa; of a BacnetWeekNDay bit string.</description></class>
<class packageName="com.tridium.bacnet.ui.ip" name="BBdtManager"><description>BBdtManager provides access to the BACnet/IP&#xa; Broadcast Distribution Table (BDT) of the local BBMD.</description></class>
<class packageName="com.tridium.bacnet.ui.schedule" name="BChangeTypeDialog"><description>BChangeTypePane</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BDiscoveryNetworksFE"><description>BDiscoveryNetworksFE allows viewing and editing of a&#xa; BDiscoveryNetworks.</description></class>
<class packageName="com.tridium.bacnet.ui.ip" name="BFdtManager"><description>BFdtManager provides access to the BACnet/IP&#xa; Foreign Device Table (FDT) of the local BBMD.</description></class>
<class packageName="com.tridium.bacnet.ui.history" name="BMultiRecord"><description>BMultiRecord is used to build a BMultiRecordTable.</description></class>
<class packageName="com.tridium.bacnet.ui.history" name="BMultiRecordTable"><description>BMultiRecordTable is a table of BacnetTrendRecords from histories&#xa; created by a single BacnetTrendLogMultipleImport.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BMultiWildcardableFieldFE"><description>BMultiWildcardableFieldFE allows viewing and editing of&#xa; multiple time fields.</description></class>
<class packageName="com.tridium.bacnet.ui.export" name="BSvoSubordinateManager"></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BVirtualTuningPolicyNameFE"></class>
<class packageName="com.tridium.bacnet.ui.export" name="BWritableSlotsEditor"><description>BWritableSlotsEditor.</description></class>
</module>
</bajadoc>
