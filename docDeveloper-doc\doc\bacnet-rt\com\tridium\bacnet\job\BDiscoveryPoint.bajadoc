<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.job.BDiscoveryPoint" name="BDiscoveryPoint" packageName="com.tridium.bacnet.job" public="true">
<description>
BDiscoveryPoint.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">03 Jun 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BVector"/>
</extends>
<property name="objectName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;objectName&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectName</tag>
<tag name="@see">#setObjectName</tag>
</property>

<property name="objectId" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="propertyIdentifier" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;propertyIdentifier&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyIdentifier</tag>
<tag name="@see">#setPropertyIdentifier</tag>
</property>

<property name="index" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;index&lt;/code&gt; property.
</description>
<tag name="@see">#getIndex</tag>
<tag name="@see">#setIndex</tag>
</property>

<property name="value" flags="">
<type class="javax.baja.sys.BValue"/>
<description>
Slot for the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#getValue</tag>
<tag name="@see">#setValue</tag>
</property>

<property name="facets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;facets&lt;/code&gt; property.
</description>
<tag name="@see">#getFacets</tag>
<tag name="@see">#setFacets</tag>
</property>

<property name="pointType" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;pointType&lt;/code&gt; property.
</description>
<tag name="@see">#getPointType</tag>
<tag name="@see">#setPointType</tag>
</property>

<property name="description" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#getDescription</tag>
<tag name="@see">#setDescription</tag>
</property>

</class>
</bajadoc>
