<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>js Class: Dialog</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">js</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-dialogs.html">dialogs</a></li><li><a href="module-lex.html">lex</a></li><li><a href="module-log.html">log</a></li><li><a href="module-nmodule_js_rc_csrf_csrfUtil.html">nmodule/js/rc/csrf/csrfUtil</a></li><li><a href="module-nmodule_js_rc_jasmine_promiseUtils.html">nmodule/js/rc/jasmine/promiseUtils</a></li><li><a href="module-nmodule_js_rc_lex_lex.html">nmodule/js/rc/lex/lex</a></li><li><a href="module-nmodule_js_rc_log_Level.html">nmodule/js/rc/log/Level</a></li><li><a href="module-nmodule_js_rc_log_Log.html">nmodule/js/rc/log/Log</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="classes.list.html" class="dropdown-toggle" data-toggle="dropdown">Classes<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-dialogs-Dialog.html">dialogs~Dialog</a></li><li><a href="module-nmodule_js_rc_lex_lex-Lexicon.html">nmodule/js/rc/lex/lex~Lexicon</a></li><li><a href="module-nmodule_js_rc_log_Log.Level.html">nmodule/js/rc/log/Log.Level</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Class: Dialog</h1>
<section>

<header>
    
        <h2>
            <span class="ancestors"><a href="module-dialogs.html">dialogs</a>~</span>
        
        Dialog
        </h2>
        
    
</header>


<article>
    <div class="container-overview">
    
        
<hr>
<dt>
    <h4 class="name" id="Dialog"><span class="type-signature"></span>new Dialog()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>A class for a Dialog box.</p>
<p>An instance of a Dialog can be accessed indirectly by use of<br>
one of the showXxx methods.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
        <h5>Example</h5>
        
        <p class="code-caption">Show a basic simple OK Dialog box</p>
    
    <pre class="sunlight-highlight-javascript">dialogs.showOk(&quot;Here&#x27;s a nice OK dialog box!&quot;);</pre>


    
</dd>

    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id="buttonJq"><span class="type-signature"></span>buttonJq(name)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the button DOM for the given name.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>The name of the button.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>the Button's jQuery DOM object or null if nothing found.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">JQuery</span>
|

<span class="param-type">null</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="cancel"><span class="type-signature"></span>cancel( [handler])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Add a 'cancel' handler to the Dialog or if no handler is specified,<br>
simulate clicking the Dialog's 'cancel' button.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>handler</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>if specified, the handler to be invoked<br>
when the Dialog's 'cancel' button is clicked. The first argument of the<br>
function callback is the Dialog instance.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-dialogs-Dialog.html#on">module:dialogs~Dialog#on</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-dialogs-Dialog.html">module:dialogs~Dialog</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="click"><span class="type-signature"></span>click(name)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Click one of the Dialog's buttons.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>The name of the Dialog button to click.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-dialogs-Dialog.html">module:dialogs~Dialog</a></span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
        <p class="code-caption">
  Show a Dialog with an OK button and click it 2 seconds later
</p>
    
    <pre class="sunlight-highlight-javascript">var dlg = dialogs.showOk(&quot;This is an OK Dialog&quot;)
setTimeout(function () {
  dlg.click(&quot;ok&quot;)
}, 2000);   </pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="close"><span class="type-signature"></span>close( [name] [, fail])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Close the Dialog. This will remove the Dialog box from the screen.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>The name of the button used to close the dialog box.<br>
This parameter is designed to be called from the Dialog JS framework itself.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fail</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>optional failure reason. If truthy, the dialog's promise<br>
will be rejected with this failure reason; otherwise, the promise will be<br>
resolved.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-dialogs-Dialog.html">module:dialogs~Dialog</a></span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
        <p class="code-caption">
  Open a Dialog and close it after 2 seconds
</p>
    
    <pre class="sunlight-highlight-javascript">var dlg = dialogs.showOk(&quot;A notification&quot;);

setTimeout(function () {
  dlg.close();
}, 2000);</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="content"><span class="type-signature"></span>content()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>If this dialog has content, return the jQuery DOM wrapper for it.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The DOM wrapper for the content. This wrapper will<br>
be empty if the dialog is shown with no content.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">JQuery</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="disableButton"><span class="type-signature"></span>disableButton(name)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Disable a button.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>The name of the button.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-dialogs-Dialog.html">module:dialogs~Dialog</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="enableButton"><span class="type-signature"></span>enableButton(name)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Enable a button.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>The name of the button.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-dialogs-Dialog.html">module:dialogs~Dialog</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="header"><span class="type-signature"></span>header()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>If this dialog has a header, return the jQuery DOM wrapper for it.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.12</li>
		</ul>
	</dd>
	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The DOM wrapper for the header. This wrapper will<br>
be empty if the dialog is shown with no header.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">JQuery</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="hide"><span class="type-signature"></span>hide()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Hide the Dialog without closing it. The preferred method<br>
to use is close.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-dialogs-Dialog.html">module:dialogs~Dialog</a></span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
        <p class="code-caption">
  Hide a dialog box after 2 seconds
</p>
    
    <pre class="sunlight-highlight-javascript">dialogs.showYesNo(&quot;Meeting alert! Do you want to be reminded in 10 seconds?&quot;)
       .yes(function (dialog) {
         dialog.hide();
           setTimeout(function () {
           dialog.show();
         }, 10000);
         return false;
       });</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="hideButton"><span class="type-signature"></span>hideButton(name)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Hide a button.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>The name of the button.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-dialogs-Dialog.html">module:dialogs~Dialog</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isClosed"><span class="type-signature"></span>isClosed()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if the Dialog is closed and removed from the DOM.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>Return true if the Dialog has been closed.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isHidden"><span class="type-signature"></span>isHidden()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if the Dialog is hidden.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>Return true if the Dialog has been hidden.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Dialog</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="jq"><span class="type-signature"></span>jq()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the internal jQuery wrapped DOM element for the entire Dialog.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>the Dialog's jQuery DOM object.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">JQuery</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="no"><span class="type-signature"></span>no( [handler])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Add a 'no' handler to the Dialog or if no handler is specified,<br>
simulate clicking the Dialog's 'no' button. The first argument of the<br>
function callback is the Dialog instance.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>handler</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>if specified, the handler to be invoked<br>
when the Dialog's 'no' button is clicked.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-dialogs-Dialog.html#on">module:dialogs~Dialog#on</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-dialogs-Dialog.html">module:dialogs~Dialog</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="ok"><span class="type-signature"></span>ok( [handler])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Add a 'ok' handler to the Dialog or if no handler is specified,<br>
simulate clicking the Dialog's 'ok' button.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>handler</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>if specified, the handler to be invoked<br>
when the Dialog's 'ok' button is clicked. The first argument of the<br>
function callback is the Dialog instance.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-dialogs-Dialog.html#on">module:dialogs~Dialog#on</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-dialogs-Dialog.html">module:dialogs~Dialog</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="on"><span class="type-signature"></span>on(name, handler)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Add a callback handler for a button via its name. This callback<br>
handler will be invoked when the button is clicked.</p>
<p>Any handler function can return a Promise. This<br>
can control when and if the Dialog box closes after the handler<br>
has been invoked. It should be noted that multiple handlers<br>
can be registered on a button.</p>
<ul>
<li>If the handlers return nothing, the Dialog will be closed after<br>
all the Handlers have been invoked.</li>
<li>If one or more handlers return a Promise, the Dialog<br>
will only close after all the Promises have been resolved.</li>
<li>If one of the Promises is rejected, the Dialog will not close.</li>
</ul>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>The name of the button to register the handler on.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>handler</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>The handler of the function to be<br>
invoked when the button is clicked. When invoked, the first argument of the<br>
handler is the Dialog instance.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-dialogs-Dialog.html#ok">module:dialogs~Dialog#ok</a></li>
			
			<li><a href="module-dialogs-Dialog.html#cancel">module:dialogs~Dialog#cancel</a></li>
			
			<li><a href="module-dialogs-Dialog.html#yes">module:dialogs~Dialog#yes</a></li>
			
			<li><a href="module-dialogs-Dialog.html#no">module:dialogs~Dialog#no</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-dialogs-Dialog.html">module:dialogs~Dialog</a></span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
        <p class="code-caption">
  Register a function be to be called when the 'foo' button
  is clicked.
</p>
    
    <pre class="sunlight-highlight-javascript">dialogs.show({
  content: &quot;Show some stuff&quot;,
  buttons: [
    {
      name: &quot;foo&quot;,
      handler: function () {
        alert(&quot;First annoying alert!&quot;);
      }
    }
  ]
}).on(&quot;foo&quot;, function () {
  alert(&quot;This will also be called when foo button is clicked.&quot;);
});</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="promise"><span class="type-signature"></span>promise()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return a promise for the dialog that will be resolved when the dialog closes.<br>
This is useful when wanting to use dialogs in a promise chain when creating a user interface.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The promise to be resolved.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="show"><span class="type-signature"></span>show()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Show the Dialog.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-dialogs-Dialog.html">module:dialogs~Dialog</a></span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
        <p class="code-caption">Create a blank dialog box and then show it.</p>
    
    <pre class="sunlight-highlight-javascript">dialogs.make(&quot;A dialog box with no buttons!&quot;)
         .show();</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="showButton"><span class="type-signature"></span>showButton(name)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Show a button.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>The name of the button.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-dialogs-Dialog.html">module:dialogs~Dialog</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="toBack"><span class="type-signature"></span>toBack()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Move the Dialog to the back.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-dialogs-Dialog.html">module:dialogs~Dialog</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="toFront"><span class="type-signature"></span>toFront()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Move the Dialog to the front.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-dialogs-Dialog.html">module:dialogs~Dialog</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="yes"><span class="type-signature"></span>yes( [handler])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Add a 'yes' handler to the Dialog or if no handler is specified,<br>
simulate clicking the Dialog's 'yes' button.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>handler</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>if specified, the handler to be invoked<br>
when the Dialog's 'yes' button is clicked. The first argument of the<br>
function callback is the Dialog instance.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-dialogs-Dialog.html#on">module:dialogs~Dialog#on</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-dialogs-Dialog.html">module:dialogs~Dialog</a></span>



    </dd>
</dl>


        

    
</dd>

        </dl>
    

    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	js Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:57+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>