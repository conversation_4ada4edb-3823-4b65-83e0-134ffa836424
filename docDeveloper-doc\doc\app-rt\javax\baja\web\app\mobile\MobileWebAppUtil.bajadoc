<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="app" runtimeProfile="rt" qualifiedName="javax.baja.web.app.mobile.MobileWebAppUtil" name="MobileWebAppUtil" packageName="javax.baja.web.app.mobile" public="true" final="true">
<description>
Utility methods for Mobile Web Apps
</description>
<tag name="@author">g<PERSON><PERSON><PERSON></tag>
<tag name="@creation">Apr 3, 2012</tag>
<tag name="@version">1</tag>
<tag name="@since">Niagara 3.7</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- javax.baja.web.app.mobile.MobileWebAppUtil() -->
<constructor name="MobileWebAppUtil" public="true">
<description/>
</constructor>

<!-- javax.baja.web.app.mobile.MobileWebAppUtil.isAppOperational(javax.baja.sys.Type, javax.baja.sys.Context) -->
<method name="isAppOperational"  public="true" static="true">
<description>
Return true if the App is currently operational and can be accessed.
</description>
<parameter name="type">
<type class="javax.baja.sys.Type"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

</class>
</bajadoc>
