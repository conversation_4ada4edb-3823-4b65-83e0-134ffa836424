<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="alarm" runtimeProfile="rt" name="com.tridium.alarm.fox">
<description/>
<class packageName="com.tridium.alarm.fox" name="BAlarmArchiveChannel"><description>BAlarmArchiveChannel is a BAlarmDbChannel that reads and writes data from&#xa; the Alarm Archive Database</description></class>
<class packageName="com.tridium.alarm.fox" name="BAlarmDbChannel"><description>The AlarmDbChannel handles all messages for accessing an alarm database.</description></class>
<class packageName="com.tridium.alarm.fox" name="BFoxAlarmArchive"><description>Fox implementation of the Alarm Archive</description></class>
<class packageName="com.tridium.alarm.fox" name="BFoxAlarmDatabase"><description>BFoxAlarmDatabase provides remote access to an alarm database via the&#xa; fox protocol.</description></class>
<class packageName="com.tridium.alarm.fox" name="BFoxAlarmResolver"><description>BFoxAlarmResolver resolves alarm queries for a fox session.</description></class>
<class packageName="com.tridium.alarm.fox" name="BIFoxAlarmDatabase" category="interface"><description>Interface for proxy BAlarmDatabases implementation over FOX</description></class>
</package>
</bajadoc>
