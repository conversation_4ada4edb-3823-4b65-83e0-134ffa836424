<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>webEditors Module: nmodule/webEditors/rc/wb/table/model/ComponentTableModel</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">webEditors</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-nmodule_webEditors_rc_fe_baja_BaseEditor.html">nmodule/webEditors/rc/fe/baja/BaseEditor</a></li><li><a href="module-nmodule_webEditors_rc_fe_BaseWidget.html">nmodule/webEditors/rc/fe/BaseWidget</a></li><li><a href="module-nmodule_webEditors_rc_fe_fe.html">nmodule/webEditors/rc/fe/fe</a></li><li><a href="module-nmodule_webEditors_rc_fe_feDialogs.html">nmodule/webEditors/rc/fe/feDialogs</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_commands_MgrCommand.html">nmodule/webEditors/rc/wb/mgr/commands/MgrCommand</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_Manager.html">nmodule/webEditors/rc/wb/mgr/Manager</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html">nmodule/webEditors/rc/wb/mgr/MgrLearn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrStateHandler.html">nmodule/webEditors/rc/wb/mgr/MgrStateHandler</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrTypeInfo.html">nmodule/webEditors/rc/wb/mgr/MgrTypeInfo</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_IconMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinPropMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_NameMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyPathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_TypeMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/MgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrModel.html">nmodule/webEditors/rc/wb/mgr/model/MgrModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">nmodule/webEditors/rc/wb/table/model/Column</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_DisplayNameColumn.html">nmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_IconColumn.html">nmodule/webEditors/rc/wb/table/model/columns/IconColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_JsonObjectPropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_PropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_ToStringColumn.html">nmodule/webEditors/rc/wb/table/model/columns/ToStringColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentSource.html">nmodule/webEditors/rc/wb/table/model/ComponentSource</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentTableModel.html">nmodule/webEditors/rc/wb/table/model/ComponentTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">nmodule/webEditors/rc/wb/table/model/Row</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html">nmodule/webEditors/rc/wb/table/model/TableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_Table.html">nmodule/webEditors/rc/wb/table/Table</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeNodeRow.html">nmodule/webEditors/rc/wb/table/tree/TreeNodeRow</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeTableModel.html">nmodule/webEditors/rc/wb/table/tree/TreeTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_tree_TreeNode.html">nmodule/webEditors/rc/wb/tree/TreeNode</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="tutorials.list.html" class="dropdown-toggle" data-toggle="dropdown">Tutorials<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="tutorial-6-managers.html">Managers</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: nmodule/webEditors/rc/wb/table/model/ComponentTableModel</h1>
<section>

<header>
    
        
            
        
    
</header>


<article>
    <div class="container-overview">
    
        

        
            
<hr>
<dt>
    <h4 class="name" id="module:nmodule/webEditors/rc/wb/table/model/ComponentTableModel"><span class="type-signature"></span>new (require("nmodule/webEditors/rc/wb/table/model/ComponentTableModel"))(params)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>API Status: <strong>Development</strong></p>
<p>Table model where each row in the table represents a <code>Component</code>.</p>
<p>A <code>ComponentTableModel</code> is backed by a <code>ComponentSource</code>, which provides<br>
the list of <code>Components</code> to build into table rows.</p>
    </div>
    

    
        <h5>Extends:</h5>
        


    <ul>
        <li><a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html">module:nmodule/webEditors/rc/wb/table/model/TableModel</a></li>
    </ul>


    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>
|

<span class="param-type">baja.Component</span>



            
            </td>

            

            

            <td class="description last"><p>parameters object, or a <code>Component</code><br>
if no parameters required.</p>
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>componentSource</code></td>
            

            <td class="type">
            
                
<span class="param-type">baja.Component</span>
|

<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentSource.html">module:nmodule/webEditors/rc/wb/table/model/ComponentSource</a></span>



            
            </td>

            

            

            <td class="description last"><p>the source of components to build into table rows.<br>
If a <code>Component</code> is given it will just be wrapped in a <code>ComponentSource</code><br>
with no parameters.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>columns</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">module:nmodule/webEditors/rc/wb/table/model/Column</a>></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id="clearRows"><span class="type-signature"></span>clearRows()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Remove all rows from the model.</p>
<p>Will trigger a <code>rowsRemoved</code> <code>tinyevent</code>, with<br>
parameters:</p>
<ul>
<li><code>rowsRemoved</code>: the rows that were removed</li>
<li><code>indices</code>: the original indices of the rows that were removed</li>
</ul>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html#clearRows">module:nmodule/webEditors/rc/wb/table/model/TableModel#clearRows</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getColumn"><span class="type-signature"></span>getColumn(name)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the column in this model matching the given name.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html#getColumn">module:nmodule/webEditors/rc/wb/table/model/TableModel#getColumn</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>the matching<br>
column, or <code>null</code> if not found</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">module:nmodule/webEditors/rc/wb/table/model/Column</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getColumnIndex"><span class="type-signature"></span>getColumnIndex(column)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the index of the given column.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>column</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">module:nmodule/webEditors/rc/wb/table/model/Column</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html#getColumnIndex">module:nmodule/webEditors/rc/wb/table/model/TableModel#getColumnIndex</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>the column's index, or -1 if not found</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">number</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getColumns"><span class="type-signature"></span>getColumns( [flags])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the current set of columns, optionally filtered by flags.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>flags</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>if given, only return columns that have these<br>
flags.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html#getColumns">module:nmodule/webEditors/rc/wb/table/model/TableModel#getColumns</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array.&lt;<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">module:nmodule/webEditors/rc/wb/table/model/Column</a>></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getComponentSource"><span class="type-signature"></span>getComponentSource()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the <code>ComponentSource</code> backing this table model.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentSource.html">module:nmodule/webEditors/rc/wb/table/model/ComponentSource</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getEditableColumns"><span class="type-signature"></span>getEditableColumns()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return all columns with the <code>EDITABLE</code> flag set.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html#getEditableColumns">module:nmodule/webEditors/rc/wb/table/model/TableModel#getEditableColumns</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array.&lt;<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">module:nmodule/webEditors/rc/wb/table/model/Column</a>></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getRow"><span class="type-signature"></span>getRow(i)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the row at the given index.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>i</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.12</li>
		</ul>
	</dd>
	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html#getRow">module:nmodule/webEditors/rc/wb/table/model/TableModel#getRow</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>the row at this index, or<br>
undefined if not present</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a></span>
|

<span class="param-type">undefined</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getRowCount"><span class="type-signature"></span>getRowCount()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the number of rows in the TableModel.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.12</li>
		</ul>
	</dd>
	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html#getRowCount">module:nmodule/webEditors/rc/wb/table/model/TableModel#getRowCount</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getRowIndex"><span class="type-signature"></span>getRowIndex(row)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the index of the given row. If a filter is applied, returns the index of the row among the<br>
currently filtered rows.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>row</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html#getRowIndex">module:nmodule/webEditors/rc/wb/table/model/TableModel#getRowIndex</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>the row's index, or -1 if not found</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">number</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getRows"><span class="type-signature"></span>getRows()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the current set of rows.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html#getRows">module:nmodule/webEditors/rc/wb/table/model/TableModel#getRows</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array.&lt;<a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a>></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getValueAt"><span class="type-signature"></span>getValueAt(x, y)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Ask the column at the given index for the value from the row at the<br>
given index.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>x</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>column index</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>y</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>row index</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html#getValueAt">module:nmodule/webEditors/rc/wb/table/model/TableModel#getValueAt</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved with the value</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="insertColumns"><span class="type-signature"></span>insertColumns(toInsert [, index])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Add new columns to the model. Will trigger a <code>columnsAdded</code> <code>tinyevent</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>toInsert</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">module:nmodule/webEditors/rc/wb/table/model/Column</a>></span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>index</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>index to insert the columns; will append to the<br>
end if omitted</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html#insertColumns">module:nmodule/webEditors/rc/wb/table/model/TableModel#insertColumns</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved if the insert is<br>
successful</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="insertRows"><span class="type-signature"></span>insertRows(toInsert [, index])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Add new rows to the model. If non-<code>Row</code> instances are given, they will be<br>
converted to <code>Row</code>s using <code>makeRow()</code>.</p>
<p>If a row filter has been set to a non-null function the index passed to this<br>
function will be relative to the resulting filtered array returned from getRows().</p>
<p>Will trigger a <code>rowsAdded</code> <code>tinyevent</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>toInsert</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;(<a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a>|*)></span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>index</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>index to insert the rows; will append to the<br>
end if omitted</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html#insertRows">module:nmodule/webEditors/rc/wb/table/model/TableModel#insertRows</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved if the insert is<br>
successful</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="makeRow"><span class="type-signature"></span>makeRow(subject)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Instantiate a new row for the given subject. <code>insertRows</code> will delegate<br>
to this if values are passed in rather than <code>Row</code> instances. Override<br>
as necessary.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>subject</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html#makeRow">module:nmodule/webEditors/rc/wb/table/model/TableModel#makeRow</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="removeColumns"><span class="type-signature"></span>removeColumns(toRemove [, end])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Remove columns from the model. Will trigger a <code>columnsRemoved</code> <code>tinyevent</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>toRemove</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">module:nmodule/webEditors/rc/wb/table/model/Column</a>></span>
|

<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>the columns to remove; or, start index</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>end</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>end index</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html#removeColumns">module:nmodule/webEditors/rc/wb/table/model/TableModel#removeColumns</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved if the remove is<br>
successful</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="removeRows"><span class="type-signature"></span>removeRows(toRemove [, end])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Remove rows from the model. Will trigger a <code>rowsRemoved</code> <code>tinyevent</code>, with<br>
parameters:</p>
<ul>
<li><code>rowsRemoved</code>: the rows that were removed</li>
<li><code>indices</code>: the original indices of the rows that were removed</li>
</ul>
<p>If a row filter has been set to a non-null function any indices passed to this<br>
function will be relative to the resulting filtered array returned from getRows().</p>
<p>Note that <code>rowsRemoved</code> and <code>indices</code> will always be sorted by their<br>
original index in the model's rows, regardless of the order of rows passed<br>
to the <code>removeRows</code> function.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>toRemove</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a>></span>
|

<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>the rows to remove; or, start index</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>end</code></td>
            

            <td class="type">
            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>end index</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html#removeRows">module:nmodule/webEditors/rc/wb/table/model/TableModel#removeRows</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved if the remove is<br>
successful</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setRowFilter"><span class="type-signature"></span>setRowFilter(rowFilterFunction)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Filter the table's rows according to the given filter function. Setting<br>
the <code>rowFilterFunction</code> to <code>null</code> will remove the current filter and<br>
reset the table model to display all rows.</p>
<p>Will trigger a <code>rowsFiltered</code> <code>tinyevent</code>.</p>
<p>Remember that <code>Array#filter</code> is synchronous, so if the filter needs to use<br>
any data that is asynchronously retrieved, the async work must be performed<br>
<em>before</em> the filter so that the filter function can work synchronously.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>rowFilterFunction</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>standard array filter function</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html#setRowFilter">module:nmodule/webEditors/rc/wb/table/model/TableModel#setRowFilter</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    
    <h5>Throws:</h5>
    
            

<dl>
    <dt>
        <div class="param-desc">
        <p>if a non-Function is given</p>
        </div>
    </dt>
    <dt>
        <dl>
            <dt>
                Type
            </dt>
            <dd>
                
<span class="param-type">Error</span>



            </dd>
        </dl>
    </dt>
</dl>


        

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>to be resolved after any necessary post-filtering work (this does <em>not</em> make<br>
the filtering itself asynchronous).</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="sort"><span class="type-signature"></span>sort(sortFunction)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Sort the table's rows according to the given sort function. Emits a<br>
<code>rowsReordered</code> event.</p>
<p>Remember that <code>Array#sort</code> is synchronous, so if the sort needs to use<br>
any data that is asynchronously retrieved, the async work must be performed<br>
<em>before</em> the sort so that the sort function can work synchronously.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>sortFunction</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>standard array sort function to receive<br>
two <code>Row</code> instances</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html#sort">module:nmodule/webEditors/rc/wb/table/model/TableModel#sort</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    
    <h5>Throws:</h5>
    
            

<dl>
    <dt>
        <div class="param-desc">
        <p>if a non-Function is given</p>
        </div>
    </dt>
    <dt>
        <dl>
            <dt>
                Type
            </dt>
            <dd>
                
<span class="param-type">Error</span>



            </dd>
        </dl>
    </dt>
</dl>


        

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>to be resolved after any necessary post-sorting work (this does <em>not</em> make<br>
the sorting itself asynchronous).</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="~exec"><span class="type-signature">&lt;inner> </span>exec(updates)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>This throttles the addition / removal of rows in the table.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>updates</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;Object></span>



            
            </td>

            

            

            <td class="description last"><p>where each object has an <code>add</code> and <code>remove</code> array that<br>
contain the components to add or remove</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>resolves when all rows have been added / removed</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        </dl>
    

    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	webEditors Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:29:00+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>