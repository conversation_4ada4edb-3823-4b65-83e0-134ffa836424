<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetErrorClass" name="BBacnetErrorClass" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetErrorClass represents the error-class portion of the&#xa; BACnet Error sequence.&#xa; &lt;p&gt;&#xa; BBacnetErrorClass is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0xFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Craig Gemmill</tag>
<tag name="@version">$Revision: 7$ $Date: 12/19/01 4:35:57 PM$</tag>
<tag name="@creation">10 Aug 00</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;device&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;object&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;property&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;resources&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;security&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;services&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;vt&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;communication&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetErrorClass.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetErrorClass"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetErrorClass"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.DEVICE -->
<field name="DEVICE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for device.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.OBJECT -->
<field name="OBJECT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for object.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.PROPERTY -->
<field name="PROPERTY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for property.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.RESOURCES -->
<field name="RESOURCES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for resources.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.SECURITY -->
<field name="SECURITY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for security.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.SERVICES -->
<field name="SERVICES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for services.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.VT -->
<field name="VT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for vt.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.COMMUNICATION -->
<field name="COMMUNICATION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for communication.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.device -->
<field name="device"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorClass"/>
<description>
BBacnetErrorClass constant for device.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.object -->
<field name="object"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorClass"/>
<description>
BBacnetErrorClass constant for object.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.property -->
<field name="property"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorClass"/>
<description>
BBacnetErrorClass constant for property.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.resources -->
<field name="resources"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorClass"/>
<description>
BBacnetErrorClass constant for resources.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.security -->
<field name="security"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorClass"/>
<description>
BBacnetErrorClass constant for security.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.services -->
<field name="services"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorClass"/>
<description>
BBacnetErrorClass constant for services.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.vt -->
<field name="vt"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorClass"/>
<description>
BBacnetErrorClass constant for vt.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.communication -->
<field name="communication"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorClass"/>
<description>
BBacnetErrorClass constant for communication.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorClass"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorClass.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
