<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.listeners.DynamicIAmListener" name="DynamicIAmListener" packageName="javax.baja.bacnet.listeners" public="true">
<description>
DynamicIAmListener: As a part of the AMEV AS-B implementations, since we are&#xa; supporting Dynamic Device Binding mainly for Trending, Alarming and&#xa; Scheduling, we do a lot of RP and CoVs to remote device.&#xa; In case the remote device is down, we need a way to wait for an I-Am after&#xa; sending a Who-Is and carry out the immediate necessary steps.
</description>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@creation">03 Jan 19</tag>
<tag name="@since">Niagara 4.7 u1</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="com.tridium.bacnet.stack.IAmListener"/>
</implements>
<!-- javax.baja.bacnet.listeners.DynamicIAmListener.getDynamicIAmListenerInstance() -->
<method name="getDynamicIAmListenerInstance"  public="true" static="true">
<description/>
<return>
<type class="javax.baja.bacnet.listeners.DynamicIAmListener"/>
</return>
</method>

<!-- javax.baja.bacnet.listeners.DynamicIAmListener.receiveIAm(com.tridium.bacnet.services.unconfirmed.IAmRequest, javax.baja.bacnet.datatypes.BBacnetAddress) -->
<method name="receiveIAm"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="request">
<type class="com.tridium.bacnet.services.unconfirmed.IAmRequest"/>
</parameter>
<parameter name="sourceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.listeners.DynamicIAmListener.subscribeHandler(javax.baja.bacnet.listeners.DynamicIAmListener.IAmHandler, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="subscribeHandler"  public="true">
<description/>
<parameter name="handler">
<type class="javax.baja.bacnet.listeners.DynamicIAmListener$IAmHandler"/>
</parameter>
<parameter name="deviceId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.listeners.DynamicIAmListener.handlesTrending(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="handlesTrending"  public="true">
<description/>
<parameter name="deviceId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.listeners.DynamicIAmListener.handlesAlarms(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="handlesAlarms"  public="true">
<description/>
<parameter name="deviceId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.listeners.DynamicIAmListener.handlesScheduling(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="handlesScheduling"  public="true">
<description/>
<parameter name="deviceId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

</class>
</bajadoc>
