<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.datatypes.BAndoverBackupRecord" name="BAndoverBackupRecord" packageName="com.tridium.andoverAC256.datatypes" public="true">
<description>
BAndoverBackupRecord is a dynamic slot added to the BAndoverDevice
</description>
<tag name="@author">Cli<PERSON></tag>
<tag name="@creation">5/4/2005 8:14AM</tag>
<tag name="@version">$Revision$ $Date: 4/13/2005 4:36:34 PM$</tag>
<tag name="@since">AX 3.0.79</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="timestamp" flags="">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;timestamp&lt;/code&gt; property.&#xa; Timestamp of the backup file
</description>
<tag name="@see">#getTimestamp</tag>
<tag name="@see">#setTimestamp</tag>
</property>

<property name="path" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;path&lt;/code&gt; property.&#xa; Path where backup is stored
</description>
<tag name="@see">#getPath</tag>
<tag name="@see">#setPath</tag>
</property>

<property name="ord" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;ord&lt;/code&gt; property.&#xa; Ord of the backup file
</description>
<tag name="@see">#getOrd</tag>
<tag name="@see">#setOrd</tag>
</property>

</class>
</bajadoc>
