<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.object.BJsonSchemaBoundObject" name="BJsonSchemaBoundObject" packageName="com.tridiumx.jsonToolkit.outbound.schema.object" public="true">
<description>
A json object which is bound by an ord to a target station component/slot and whose internal key value pairs&#xa; are the slots inside that target.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaBoundSlotsContainer"/>
</extends>
<implements>
<type class="com.tridiumx.jsonToolkit.outbound.schema.BIJsonObject"/>
</implements>
<property name="jsonSlotNameSource" flags="">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource"/>
<description>
Slot for the &lt;code&gt;jsonSlotNameSource&lt;/code&gt; property.
</description>
<tag name="@see">#getJsonSlotNameSource</tag>
<tag name="@see">#setJsonSlotNameSource</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.object.BJsonSchemaBoundObject() -->
<constructor name="BJsonSchemaBoundObject" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.object.BJsonSchemaBoundObject.getJsonSlotNameSource() -->
<method name="getJsonSlotNameSource"  public="true">
<description>
Get the &lt;code&gt;jsonSlotNameSource&lt;/code&gt; property.
</description>
<tag name="@see">#jsonSlotNameSource</tag>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.object.BJsonSchemaBoundObject.setJsonSlotNameSource(com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource) -->
<method name="setJsonSlotNameSource"  public="true">
<description>
Set the &lt;code&gt;jsonSlotNameSource&lt;/code&gt; property.
</description>
<tag name="@see">#jsonSlotNameSource</tag>
<parameter name="v">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.object.BJsonSchemaBoundObject.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.object.BJsonSchemaBoundObject.make(javax.baja.naming.BOrd) -->
<method name="make"  public="true" static="true">
<description/>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.object.BJsonSchemaBoundObject"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.object.BJsonSchemaBoundObject.started() -->
<method name="started"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.object.BJsonSchemaBoundObject.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.object.BJsonSchemaBoundObject.populateObjectContent(com.tridium.json.JSONWriter) -->
<method name="populateObjectContent"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="json">
<type class="com.tridium.json.JSONWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.object.BJsonSchemaBoundObject.onSchemaEvent(com.tridiumx.jsonToolkit.outbound.schema.support.BSchemaEvent) -->
<method name="onSchemaEvent"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="event">
<type class="com.tridiumx.jsonToolkit.outbound.schema.support.BSchemaEvent"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.object.BJsonSchemaBoundObject.jsonSlotNameSource -->
<field name="jsonSlotNameSource"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;jsonSlotNameSource&lt;/code&gt; property.
</description>
<tag name="@see">#getJsonSlotNameSource</tag>
<tag name="@see">#setJsonSlotNameSource</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.object.BJsonSchemaBoundObject.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
