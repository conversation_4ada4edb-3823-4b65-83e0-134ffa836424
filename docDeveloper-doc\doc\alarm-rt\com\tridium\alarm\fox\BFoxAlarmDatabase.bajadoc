<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.fox.BFoxAlarmDatabase" name="BFoxAlarmDatabase" packageName="com.tridium.alarm.fox" public="true">
<description>
BFoxAlarmDatabase provides remote access to an alarm database via the&#xa; fox protocol.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">23 Sep 2004</tag>
<tag name="@version">$Revision: 9$ $Date: 9/16/08 4:47:17 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.alarm.BAlarmDatabase"/>
</extends>
<implements>
<type class="com.tridium.fox.sys.BIFoxProxySpace"/>
</implements>
<implements>
<type class="javax.baja.bql.Queryable"/>
</implements>
<implements>
<type class="javax.baja.bql.RemoteQueryable"/>
</implements>
<implements>
<type class="javax.baja.nav.NavListener"/>
</implements>
<implements>
<type class="com.tridium.alarm.fox.BIFoxAlarmDatabase"/>
</implements>
</class>
</bajadoc>
