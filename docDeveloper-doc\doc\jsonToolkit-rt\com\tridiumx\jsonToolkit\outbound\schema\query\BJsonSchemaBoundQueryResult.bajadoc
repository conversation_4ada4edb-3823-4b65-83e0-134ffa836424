<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaBoundQueryResult" name="BJsonSchemaBoundQueryResult" packageName="com.tridiumx.jsonToolkit.outbound.schema.query" public="true">
<description>
Binding to the result of a specific query, the resulting columns/values are rendered&#xa; into the json schema according to the chosen json style.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaMember"/>
</extends>
<property name="query" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;query&lt;/code&gt; property.
</description>
<tag name="@see">#getQuery</tag>
<tag name="@see">#setQuery</tag>
</property>

<property name="outputStyle" flags="">
<type class="javax.baja.util.BTypeSpec"/>
<description>
Slot for the &lt;code&gt;outputStyle&lt;/code&gt; property.
</description>
<tag name="@see">#getOutputStyle</tag>
<tag name="@see">#setOutputStyle</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaBoundQueryResult() -->
<constructor name="BJsonSchemaBoundQueryResult" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaBoundQueryResult.getQuery() -->
<method name="getQuery"  public="true">
<description>
Get the &lt;code&gt;query&lt;/code&gt; property.
</description>
<tag name="@see">#query</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaBoundQueryResult.setQuery(java.lang.String) -->
<method name="setQuery"  public="true">
<description>
Set the &lt;code&gt;query&lt;/code&gt; property.
</description>
<tag name="@see">#query</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaBoundQueryResult.getOutputStyle() -->
<method name="getOutputStyle"  public="true">
<description>
Get the &lt;code&gt;outputStyle&lt;/code&gt; property.
</description>
<tag name="@see">#outputStyle</tag>
<return>
<type class="javax.baja.util.BTypeSpec"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaBoundQueryResult.setOutputStyle(javax.baja.util.BTypeSpec) -->
<method name="setOutputStyle"  public="true">
<description>
Set the &lt;code&gt;outputStyle&lt;/code&gt; property.
</description>
<tag name="@see">#outputStyle</tag>
<parameter name="v">
<type class="javax.baja.util.BTypeSpec"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaBoundQueryResult.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaBoundQueryResult.make(java.lang.String, javax.baja.util.BTypeSpec) -->
<method name="make"  public="true" static="true">
<description/>
<parameter name="queryName">
<type class="java.lang.String"/>
</parameter>
<parameter name="writerType">
<type class="javax.baja.util.BTypeSpec"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaBoundQueryResult"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaBoundQueryResult.getJsonName() -->
<method name="getJsonName"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaBoundQueryResult.process(com.tridium.json.JSONWriter, boolean) -->
<method name="process"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="jsonWriter">
<type class="com.tridium.json.JSONWriter"/>
</parameter>
<parameter name="jsonKeysValid">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaBoundQueryResult.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaBoundQueryResult.query -->
<field name="query"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;query&lt;/code&gt; property.
</description>
<tag name="@see">#getQuery</tag>
<tag name="@see">#setQuery</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaBoundQueryResult.outputStyle -->
<field name="outputStyle"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;outputStyle&lt;/code&gt; property.
</description>
<tag name="@see">#getOutputStyle</tag>
<tag name="@see">#setOutputStyle</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaBoundQueryResult.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
