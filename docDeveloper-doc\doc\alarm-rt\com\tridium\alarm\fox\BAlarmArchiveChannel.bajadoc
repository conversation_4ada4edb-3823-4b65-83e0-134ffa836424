<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.fox.BAlarmArchiveChannel" name="BAlarmArchiveChannel" packageName="com.tridium.alarm.fox" public="true">
<description>
BAlarmArchiveChannel is a BAlarmDbChannel that reads and writes data from&#xa; the Alarm Archive Database
</description>
<tag name="@since">Niagara 4.11</tag>
<extends>
<type class="com.tridium.alarm.fox.BAlarmDbChannel"/>
</extends>
</class>
</bajadoc>
