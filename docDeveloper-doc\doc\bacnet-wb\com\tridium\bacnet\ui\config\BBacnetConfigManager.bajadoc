<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="wb" qualifiedName="com.tridium.bacnet.ui.config.BBacnetConfigManager" name="BBacnetConfigManager" packageName="com.tridium.bacnet.ui.config" public="true">
<description>
BBacnetConfigManager allows the user to create and manage config&#xa; objects within a BACnet device.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">01 Dec 2004</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.workbench.mgr.folder.BFolderManager"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
</class>
</bajadoc>
