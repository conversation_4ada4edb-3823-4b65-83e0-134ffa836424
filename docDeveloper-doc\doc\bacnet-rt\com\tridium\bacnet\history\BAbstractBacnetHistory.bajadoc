<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.history.BAbstractBacnetHistory" name="BAbstractBacnetHistory" packageName="com.tridium.bacnet.history" public="true" abstract="true">
<description>
BAbstractBacnetHistory defines an archive action for transferring&#xa; one trend log from a remote Bacnet source to the local&#xa; destination.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 1$ $Date: 9/02/03 2:05:15 PM$</tag>
<tag name="@creation">02 Sep 03</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.driver.history.BHistoryImport"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<property name="objectId" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; The object ID in the remote device.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="localHistoryName" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;localHistoryName&lt;/code&gt; property.&#xa; The unique name of the history within the&#xa; station where imported records will be appended.
</description>
<tag name="@see">#getLocalHistoryName</tag>
<tag name="@see">#setLocalHistoryName</tag>
</property>

<property name="localHistoryNameFormat" flags="">
<type class="javax.baja.util.BFormat"/>
<description>
Slot for the &lt;code&gt;localHistoryNameFormat&lt;/code&gt; property.&#xa; The format for generating the unique name of the history&#xa; within the station where imported records will be appended.
</description>
<tag name="@see">#getLocalHistoryNameFormat</tag>
<tag name="@see">#setLocalHistoryNameFormat</tag>
</property>

<property name="referenceTime" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
<description>
Slot for the &lt;code&gt;referenceTime&lt;/code&gt; property.&#xa; Defines the starting reference time to use&#xa; for reading the trend log records.
</description>
<tag name="@see">#getReferenceTime</tag>
<tag name="@see">#setReferenceTime</tag>
</property>

<property name="maxRecordsPerRequest" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;maxRecordsPerRequest&lt;/code&gt; property.&#xa; Defines the maximum number of records that can be&#xa; requested at a time.
</description>
<tag name="@see">#getMaxRecordsPerRequest</tag>
<tag name="@see">#setMaxRecordsPerRequest</tag>
</property>

<property name="alwaysRequestByReferenceTime" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;alwaysRequestByReferenceTime&lt;/code&gt; property.&#xa; When true, the records retrieved will always be requested BY_TIME.&#xa; When false, records will be retrieved using the BY_SEQUENCE_NUMBER method,&#xa; if possible.
</description>
<tag name="@see">#getAlwaysRequestByReferenceTime</tag>
<tag name="@see">#setAlwaysRequestByReferenceTime</tag>
</property>

<property name="lastSequenceNumberProcessed" flags="">
<type class="long"/>
<description>
Slot for the &lt;code&gt;lastSequenceNumberProcessed&lt;/code&gt; property.&#xa; represents the last sequence number of the read records.
</description>
<tag name="@see">#getLastSequenceNumberProcessed</tag>
<tag name="@see">#setLastSequenceNumberProcessed</tag>
</property>

<property name="maxStartingEvents" flags="h">
<type class="int"/>
<description>
Slot for the &lt;code&gt;maxStartingEvents&lt;/code&gt; property.&#xa; the maximum number of &#x22;startup&#x22; events before a &#x22;real&#x22;&#xa; record containing data would exist
</description>
<tag name="@see">#getMaxStartingEvents</tag>
<tag name="@see">#setMaxStartingEvents</tag>
</property>

<property name="discoveryHistoryType" flags="hr">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;discoveryHistoryType&lt;/code&gt; property.&#xa; history type from discovery
</description>
<tag name="@see">#getDiscoveryHistoryType</tag>
<tag name="@see">#setDiscoveryHistoryType</tag>
</property>

<action name="clearRecordsInDevice" flags="c">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;clearRecordsInDevice&lt;/code&gt; action.&#xa; This action causes the trend log records on the actual Bacnet&#xa; device will be cleared (by writing a zero to the recordCount&#xa; property on the device).
</description>
<tag name="@see">#clearRecordsInDevice()</tag>
</action>

</class>
</bajadoc>
