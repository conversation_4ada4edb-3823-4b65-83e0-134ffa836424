<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetDestination" name="BBacnetDestination" packageName="javax.baja.bacnet.datatypes" public="true">
<description>
BBacnetDestination represents the Bacnet Destination&#xa; sequence, used in the Recipient_List of Notification&#xa; Class objects.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">31 Jul 01</tag>
<tag name="@version">$Revision: 5$ $Date: 12/10/01 9:26:07 AM$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.alarm.BAlarmRecipient"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<implements>
<type class="javax.baja.bacnet.BacnetAlarmConst"/>
</implements>
<property name="recipient" flags="s">
<type class="javax.baja.bacnet.datatypes.BBacnetRecipient"/>
<description>
Slot for the &lt;code&gt;recipient&lt;/code&gt; property.
</description>
<tag name="@see">#getRecipient</tag>
<tag name="@see">#setRecipient</tag>
</property>

<property name="processIdentifier" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;processIdentifier&lt;/code&gt; property.
</description>
<tag name="@see">#getProcessIdentifier</tag>
<tag name="@see">#setProcessIdentifier</tag>
</property>

<property name="issueConfirmedNotifications" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;issueConfirmedNotifications&lt;/code&gt; property.
</description>
<tag name="@see">#getIssueConfirmedNotifications</tag>
<tag name="@see">#setIssueConfirmedNotifications</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination() -->
<constructor name="BBacnetDestination" public="true">
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.getRecipient() -->
<method name="getRecipient"  public="true">
<description>
Get the &lt;code&gt;recipient&lt;/code&gt; property.
</description>
<tag name="@see">#recipient</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetRecipient"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.setRecipient(javax.baja.bacnet.datatypes.BBacnetRecipient) -->
<method name="setRecipient"  public="true">
<description>
Set the &lt;code&gt;recipient&lt;/code&gt; property.
</description>
<tag name="@see">#recipient</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetRecipient"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.getProcessIdentifier() -->
<method name="getProcessIdentifier"  public="true">
<description>
Get the &lt;code&gt;processIdentifier&lt;/code&gt; property.
</description>
<tag name="@see">#processIdentifier</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.setProcessIdentifier(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setProcessIdentifier"  public="true">
<description>
Set the &lt;code&gt;processIdentifier&lt;/code&gt; property.
</description>
<tag name="@see">#processIdentifier</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.getIssueConfirmedNotifications() -->
<method name="getIssueConfirmedNotifications"  public="true">
<description>
Get the &lt;code&gt;issueConfirmedNotifications&lt;/code&gt; property.
</description>
<tag name="@see">#issueConfirmedNotifications</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.setIssueConfirmedNotifications(boolean) -->
<method name="setIssueConfirmedNotifications"  public="true">
<description>
Set the &lt;code&gt;issueConfirmedNotifications&lt;/code&gt; property.
</description>
<tag name="@see">#issueConfirmedNotifications</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.handleAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="handleAlarm"  public="true" final="true">
<description>
Handle an alarm.
</description>
<parameter name="alarmRecord">
<type class="javax.baja.alarm.BAlarmRecord"/>
<description>
the alarm.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.sendNotification(com.tridium.bacnet.stack.client.AsyncEventNotificationRequest) -->
<method name="sendNotification"  protected="true">
<description/>
<parameter name="request">
<type class="com.tridium.bacnet.stack.client.AsyncEventNotificationRequest"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.recipientEquals(javax.baja.bacnet.datatypes.BBacnetRecipient) -->
<method name="recipientEquals"  public="true" final="true">
<description>
Is the specified recipient equal to this destination&#x27;s recipient?
</description>
<parameter name="recip">
<type class="javax.baja.bacnet.datatypes.BBacnetRecipient"/>
<description/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the recipient is equal.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.destinationEquals(javax.baja.bacnet.datatypes.BBacnetDestination) -->
<method name="destinationEquals"  public="true" final="true">
<description>
Compare if all of this object&#x27;s properties are&#xa; equal to the specified object.
</description>
<parameter name="dest">
<type class="javax.baja.bacnet.datatypes.BBacnetDestination"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.destinationEquals(javax.baja.bacnet.datatypes.BBacnetDestination, boolean) -->
<method name="destinationEquals"  public="true" final="true">
<description/>
<parameter name="dest">
<type class="javax.baja.bacnet.datatypes.BBacnetDestination"/>
</parameter>
<parameter name="compareMillis">
<type class="boolean"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.timeRangesEquivalent(javax.baja.util.BTimeRange, javax.baja.util.BTimeRange, boolean) -->
<method name="timeRangesEquivalent"  public="true" final="true">
<description/>
<parameter name="tr1">
<type class="javax.baja.util.BTimeRange"/>
</parameter>
<parameter name="tr2">
<type class="javax.baja.util.BTimeRange"/>
</parameter>
<parameter name="compareMillis">
<type class="boolean"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.started() -->
<method name="started"  public="true" final="true">
<description>
Started.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true" final="true">
<description>
Bubble changes up to the parent component.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.atSteadyState() -->
<method name="atSteadyState"  public="true" final="true">
<description>
On startup, discover the address of the recipient if it is a device-type&#xa; recipient, and not already mapped in our network.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.subscribed() -->
<method name="subscribed"  public="true" final="true">
<description>
Callback when the component enters the subscribed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.unsubscribed() -->
<method name="unsubscribed"  public="true" final="true">
<description>
Callback when the component leaves the subscribed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true" final="true">
<description>
BBacnetDestinations can only be placed directly in the Alarm Service.&#xa; This aids in looking them up for handling acknowledgments.
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.getAppliedCategoryMask() -->
<method name="getAppliedCategoryMask"  public="true" final="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.getCategoryMask() -->
<method name="getCategoryMask"  public="true" final="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.getPermissions(javax.baja.sys.Context) -->
<method name="getPermissions"  public="true" final="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.security.BPermissions"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.accept(javax.baja.alarm.BAlarmRecord) -->
<method name="accept"  public="true">
<description>
Check to see if the alarm falls within the time and day&#xa; ranges and the transitions.&#xa; This overrides the superclass behavior to comply with the&#xa; specific requirements of the BACnet standard.
</description>
<parameter name="rec">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true" final="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true" final="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.timeRange -->
<field name="timeRange"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;timeRange&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeRange</tag>
<tag name="@see">#setTimeRange</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.transitions -->
<field name="transitions"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;transitions&lt;/code&gt; property.
</description>
<tag name="@see">#getTransitions</tag>
<tag name="@see">#setTransitions</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.routeAcks -->
<field name="routeAcks"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;routeAcks&lt;/code&gt; property.
</description>
<tag name="@see">#getRouteAcks</tag>
<tag name="@see">#setRouteAcks</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.recipient -->
<field name="recipient"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;recipient&lt;/code&gt; property.
</description>
<tag name="@see">#getRecipient</tag>
<tag name="@see">#setRecipient</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.processIdentifier -->
<field name="processIdentifier"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;processIdentifier&lt;/code&gt; property.
</description>
<tag name="@see">#getProcessIdentifier</tag>
<tag name="@see">#setProcessIdentifier</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.issueConfirmedNotifications -->
<field name="issueConfirmedNotifications"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;issueConfirmedNotifications&lt;/code&gt; property.
</description>
<tag name="@see">#getIssueConfirmedNotifications</tag>
<tag name="@see">#setIssueConfirmedNotifications</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.LOCAL_PROCESS_ID -->
<field name="LOCAL_PROCESS_ID"  public="true" static="true" final="true">
<type class="long"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDestination.MAX_ENCODED_SIZE -->
<field name="MAX_ENCODED_SIZE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
