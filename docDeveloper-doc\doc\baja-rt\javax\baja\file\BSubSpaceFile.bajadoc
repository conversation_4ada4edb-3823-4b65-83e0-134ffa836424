<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BSubSpaceFile" name="BSubSpaceFile" packageName="javax.baja.file" public="true" abstract="true">
<description>
BSubSpaceFile is a data file that contains a sub-space with&#xa; its own object hierarchy and navigation scheme.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">24 Jan 03</tag>
<tag name="@version">$Revision: 8$ $Date: 6/15/10 9:51:14 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.file.BDataFile"/>
</extends>
<!-- javax.baja.file.BSubSpaceFile(javax.baja.file.BIFileStore) -->
<constructor name="BSubSpaceFile" public="true">
<parameter name="store">
<type class="javax.baja.file.BIFileStore"/>
</parameter>
<description>
Construct a file with the specified store.
</description>
</constructor>

<!-- javax.baja.file.BSubSpaceFile() -->
<constructor name="BSubSpaceFile" public="true">
<description>
Construct (must call setStore()).
</description>
</constructor>

<!-- javax.baja.file.BSubSpaceFile.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.file.BSubSpaceFile.isOpen() -->
<method name="isOpen"  public="true">
<description>
Return true if there is an open space for this file.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BSubSpaceFile.getSubSpace() -->
<method name="getSubSpace"  public="true">
<description>
Get the space which is contained within this file.
</description>
<return>
<type class="javax.baja.space.BSpace"/>
</return>
</method>

<!-- javax.baja.file.BSubSpaceFile.listOpen() -->
<method name="listOpen"  public="true" static="true">
<description>
Get the list of open files.
</description>
<return>
<type class="javax.baja.file.BSubSpaceFile" dimension="1"/>
</return>
</method>

<!-- javax.baja.file.BSubSpaceFile.open() -->
<method name="open"  public="true">
<description>
Open the sub space for this file.
</description>
<return>
<type class="javax.baja.space.BSpace"/>
</return>
</method>

<!-- javax.baja.file.BSubSpaceFile.save() -->
<method name="save"  public="true">
<description>
Save this file.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.file.BSubSpaceFile.reload() -->
<method name="reload"  public="true">
<description>
Reload is basically a close and then open.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.file.BSubSpaceFile.close() -->
<method name="close"  public="true">
<description>
Close the specified file.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.file.BSubSpaceFile.isModified() -->
<method name="isModified"  public="true" abstract="true">
<description>
Return if the associated space is modified.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BSubSpaceFile.doOpen() -->
<method name="doOpen"  protected="true" abstract="true">
<description>
Open the associated space.
</description>
<return>
<type class="javax.baja.space.BSpace"/>
</return>
</method>

<!-- javax.baja.file.BSubSpaceFile.doSave() -->
<method name="doSave"  protected="true" abstract="true">
<description>
Save the associated space.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.file.BSubSpaceFile.doClose() -->
<method name="doClose"  protected="true" abstract="true">
<description>
Close the associated space.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.file.BSubSpaceFile.hasNavChildren() -->
<method name="hasNavChildren"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return true.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BSubSpaceFile.getNavChild(java.lang.String) -->
<method name="getNavChild"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;open().getNavChild(name)&lt;/code&gt;.
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.nav.BINavNode"/>
</return>
</method>

<!-- javax.baja.file.BSubSpaceFile.getNavChildren() -->
<method name="getNavChildren"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;open().getNavChildren()&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.nav.BINavNode" dimension="1"/>
</return>
</method>

<!-- javax.baja.file.BSubSpaceFile.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.file.BSubSpaceFile.log -->
<field name="log"  public="true" static="true" final="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

</class>
</bajadoc>
