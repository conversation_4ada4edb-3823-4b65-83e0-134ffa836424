<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.datatypes.BExeParam" name="BExeParam" packageName="com.tridium.ace.datatypes" public="true" final="true">
<description>
BExeParam is implementation of &lt;code&gt;BIAceInteger&lt;/code&gt; for ACE component&#xa; exeParam property.   This property encodes the scan level and execution order&#xa; in an 8 bit field lloooooo  - l level bits &amp; o order bits
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">7/15/2019</tag>
<extends>
<type class="javax.baja.sys.BSimple"/>
</extends>
<implements>
<type class="com.tridium.ace.datatypes.BIAceInteger"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
</class>
</bajadoc>
