<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter" name="BHistoryExportMarkerFilter" packageName="com.tridiumx.jsonToolkit.exportMarker.filter" public="true">
<description>
Does not support for history imported from another device, requires the use of another schema/query builder
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter"/>
</extends>
<property name="currentQuery" flags="rd">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;currentQuery&lt;/code&gt; property.&#xa; This is linked to a query selected by the Schema to generate Json output for each history
</description>
<tag name="@see">#getCurrentQuery</tag>
<tag name="@see">#setCurrentQuery</tag>
</property>

<property name="columns" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;columns&lt;/code&gt; property.
</description>
<tag name="@see">#getColumns</tag>
<tag name="@see">#setColumns</tag>
</property>

<property name="updateSendSinceTime" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;updateSendSinceTime&lt;/code&gt; property.
</description>
<tag name="@see">#getUpdateSendSinceTime</tag>
<tag name="@see">#setUpdateSendSinceTime</tag>
</property>

<property name="historyExportFilter" flags="">
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchema"/>
<description>
Slot for the &lt;code&gt;historyExportFilter&lt;/code&gt; property.&#xa; Has own schema to monitor progress metrics
</description>
<tag name="@see">#getHistoryExportFilter</tag>
<tag name="@see">#setHistoryExportFilter</tag>
</property>

<action name="sendSinceLastExport" flags="sa">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;sendSinceLastExport&lt;/code&gt; action.&#xa; Timestamp is inclusive of records at that timestamp
</description>
<tag name="@see">#sendSinceLastExport()</tag>
</action>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter() -->
<constructor name="BHistoryExportMarkerFilter" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.getCurrentQuery() -->
<method name="getCurrentQuery"  public="true">
<description>
Get the &lt;code&gt;currentQuery&lt;/code&gt; property.&#xa; This is linked to a query selected by the Schema to generate Json output for each history
</description>
<tag name="@see">#currentQuery</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.setCurrentQuery(javax.baja.naming.BOrd) -->
<method name="setCurrentQuery"  public="true">
<description>
Set the &lt;code&gt;currentQuery&lt;/code&gt; property.&#xa; This is linked to a query selected by the Schema to generate Json output for each history
</description>
<tag name="@see">#currentQuery</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.getColumns() -->
<method name="getColumns"  public="true">
<description>
Get the &lt;code&gt;columns&lt;/code&gt; property.
</description>
<tag name="@see">#columns</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.setColumns(java.lang.String) -->
<method name="setColumns"  public="true">
<description>
Set the &lt;code&gt;columns&lt;/code&gt; property.
</description>
<tag name="@see">#columns</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.getUpdateSendSinceTime() -->
<method name="getUpdateSendSinceTime"  public="true">
<description>
Get the &lt;code&gt;updateSendSinceTime&lt;/code&gt; property.
</description>
<tag name="@see">#updateSendSinceTime</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.setUpdateSendSinceTime(boolean) -->
<method name="setUpdateSendSinceTime"  public="true">
<description>
Set the &lt;code&gt;updateSendSinceTime&lt;/code&gt; property.
</description>
<tag name="@see">#updateSendSinceTime</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.getHistoryExportFilter() -->
<method name="getHistoryExportFilter"  public="true">
<description>
Get the &lt;code&gt;historyExportFilter&lt;/code&gt; property.&#xa; Has own schema to monitor progress metrics
</description>
<tag name="@see">#historyExportFilter</tag>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchema"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.setHistoryExportFilter(com.tridiumx.jsonToolkit.outbound.schema.BJsonSchema) -->
<method name="setHistoryExportFilter"  public="true">
<description>
Set the &lt;code&gt;historyExportFilter&lt;/code&gt; property.&#xa; Has own schema to monitor progress metrics
</description>
<tag name="@see">#historyExportFilter</tag>
<parameter name="v">
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchema"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.sendSinceLastExport() -->
<method name="sendSinceLastExport"  public="true">
<description>
Invoke the &lt;code&gt;sendSinceLastExport&lt;/code&gt; action.&#xa; Timestamp is inclusive of records at that timestamp
</description>
<tag name="@see">#sendSinceLastExport</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.doSendSince(javax.baja.sys.BAbsTime) -->
<method name="doSendSince"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unused&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<parameter name="time">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.doSendSinceLastExport() -->
<method name="doSendSinceLastExport"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unused&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.currentQuery -->
<field name="currentQuery"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;currentQuery&lt;/code&gt; property.&#xa; This is linked to a query selected by the Schema to generate Json output for each history
</description>
<tag name="@see">#getCurrentQuery</tag>
<tag name="@see">#setCurrentQuery</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.columns -->
<field name="columns"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;columns&lt;/code&gt; property.
</description>
<tag name="@see">#getColumns</tag>
<tag name="@see">#setColumns</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.updateSendSinceTime -->
<field name="updateSendSinceTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;updateSendSinceTime&lt;/code&gt; property.
</description>
<tag name="@see">#getUpdateSendSinceTime</tag>
<tag name="@see">#setUpdateSendSinceTime</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.historyExportFilter -->
<field name="historyExportFilter"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;historyExportFilter&lt;/code&gt; property.&#xa; Has own schema to monitor progress metrics
</description>
<tag name="@see">#getHistoryExportFilter</tag>
<tag name="@see">#setHistoryExportFilter</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.sendSinceLastExport -->
<field name="sendSinceLastExport"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;sendSinceLastExport&lt;/code&gt; action.&#xa; Timestamp is inclusive of records at that timestamp
</description>
<tag name="@see">#sendSinceLastExport()</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BHistoryExportMarkerFilter.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
