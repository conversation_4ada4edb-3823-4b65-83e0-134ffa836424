<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.UnknownSchemeException" name="UnknownSchemeException" packageName="javax.baja.naming" public="true" category="exception">
<description>
UnknownSchemeException is thrown when an unregistered&#xa; scheme is encountered in parsing a BOrd.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Nov 02</tag>
<tag name="@version">$Revision: 1$ $Date: 12/12/02 10:20:28 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BajaRuntimeException"/>
</extends>
<!-- javax.baja.naming.UnknownSchemeException(java.lang.String, java.lang.Throwable) -->
<constructor name="UnknownSchemeException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="cause">
<type class="java.lang.Throwable"/>
</parameter>
<description>
Constructor with specified message and cause.
</description>
</constructor>

<!-- javax.baja.naming.UnknownSchemeException(java.lang.String) -->
<constructor name="UnknownSchemeException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<description>
Constructor with specified message.
</description>
</constructor>

<!-- javax.baja.naming.UnknownSchemeException(java.lang.Throwable) -->
<constructor name="UnknownSchemeException" public="true">
<parameter name="cause">
<type class="java.lang.Throwable"/>
</parameter>
<description>
Constructor with specified cause.
</description>
</constructor>

</class>
</bajadoc>
