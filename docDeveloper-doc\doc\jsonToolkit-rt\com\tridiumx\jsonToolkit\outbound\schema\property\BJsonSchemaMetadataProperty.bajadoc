<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty" name="BJsonSchemaMetadataProperty" packageName="com.tridiumx.jsonToolkit.outbound.schema.property" public="true">
<description>
A JSON property which uses a selected metadata field as the source&#xa; for the json value (instead of a slot value as per other bindings).&#xa;&#xa; &lt;p&gt;For example with a binding to /folderX/myPoint/out&lt;br&gt;&#xa; with a metadata field selection of name, the property will render as &#x22;name&#x22; : &#x22;out&#x22;&lt;br&gt;&#xa; with a metadata field selection of parentsName, the property will render as &#x22;name&#x22; : &#x22;myPoint&#x22;&lt;br&gt;&#xa; with a metadata field selection of nearestFolderName, the property will render as &#x22;name&#x22; : &#x22;folderX&#x22;&lt;/p&gt;
</description>
<tag name="@see">BMetadataField</tag>
<tag name="@author">Nick Dodd</tag>
<tag name="@since">Niagara 4.10</tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaBoundMember"/>
</extends>
<implements>
<parameterizedType class="com.tridiumx.jsonToolkit.outbound.schema.BIJsonProperty">
<args>
<type class="javax.baja.sys.BString"/>
</args>
</parameterizedType>
</implements>
<property name="field" flags="">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
<description>
Slot for the &lt;code&gt;field&lt;/code&gt; property.
</description>
<tag name="@see">#getField</tag>
<tag name="@see">#setField</tag>
</property>

<property name="useParentBinding" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;useParentBinding&lt;/code&gt; property.
</description>
<tag name="@see">#getUseParentBinding</tag>
<tag name="@see">#setUseParentBinding</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty() -->
<constructor name="BJsonSchemaMetadataProperty" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty.getField() -->
<method name="getField"  public="true">
<description>
Get the &lt;code&gt;field&lt;/code&gt; property.
</description>
<tag name="@see">#field</tag>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty.setField(com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField) -->
<method name="setField"  public="true">
<description>
Set the &lt;code&gt;field&lt;/code&gt; property.
</description>
<tag name="@see">#field</tag>
<parameter name="v">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty.getUseParentBinding() -->
<method name="getUseParentBinding"  public="true">
<description>
Get the &lt;code&gt;useParentBinding&lt;/code&gt; property.
</description>
<tag name="@see">#useParentBinding</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty.setUseParentBinding(boolean) -->
<method name="setUseParentBinding"  public="true">
<description>
Set the &lt;code&gt;useParentBinding&lt;/code&gt; property.
</description>
<tag name="@see">#useParentBinding</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty.make(com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField, javax.baja.naming.BOrd) -->
<method name="make"  public="true" static="true">
<description>
Make a new metadata property for a given ord and field type
</description>
<parameter name="metadataField">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
<description>
the metadata field to use as the source for the property value
</description>
</parameter>
<parameter name="bindingOrd">
<type class="javax.baja.naming.BOrd"/>
<description>
the binding to use for this property
</description>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty"/>
<description>
a new metadata property for a given ord and field type
</description>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty.make(com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField) -->
<method name="make"  public="true" static="true">
<description>
Make a new metadata property for a given field type, that uses it&#x27;s parents binding.
</description>
<parameter name="metadataField">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
<description>
the metadata field to use as the source for the property value
</description>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty"/>
<description>
a new metadata property for a given ord and field type
</description>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty.getJsonValue() -->
<method name="getJsonValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BString"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty.getPropertiesToIncludeInJson(javax.baja.sys.BComplex) -->
<method name="getPropertiesToIncludeInJson"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="resolvedTarget">
<type class="javax.baja.sys.BComplex"/>
</parameter>
<return>
<parameterizedType class="java.util.List">
<args>
<type class="java.lang.String"/>
</args>
</parameterizedType>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty.getBinding() -->
<method name="getBinding"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty.isBindOrdSet() -->
<method name="isBindOrdSet"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty.field -->
<field name="field"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;field&lt;/code&gt; property.
</description>
<tag name="@see">#getField</tag>
<tag name="@see">#setField</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty.useParentBinding -->
<field name="useParentBinding"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;useParentBinding&lt;/code&gt; property.
</description>
<tag name="@see">#getUseParentBinding</tag>
<tag name="@see">#setUseParentBinding</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty.jsonName -->
<field name="jsonName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;jsonName&lt;/code&gt; property.
</description>
<tag name="@see">#getJsonName</tag>
<tag name="@see">#setJsonName</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty.jsonNameSource -->
<field name="jsonNameSource"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;jsonNameSource&lt;/code&gt; property.
</description>
<tag name="@see">#getJsonNameSource</tag>
<tag name="@see">#setJsonNameSource</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaMetadataProperty.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
