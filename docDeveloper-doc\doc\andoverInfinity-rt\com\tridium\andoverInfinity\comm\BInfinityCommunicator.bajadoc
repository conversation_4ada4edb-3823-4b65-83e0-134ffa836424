<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.BInfinityCommunicator" name="BInfinityCommunicator" packageName="com.tridium.andoverInfinity.comm" public="true">
<description>
Communicator for Infinity driver
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">NiagaraAX 3.2</tag>
<extends>
<type class="com.tridium.ddfSerial.comm.singleTransaction.BDdfSerialSitCommunicator"/>
</extends>
<!-- com.tridium.andoverInfinity.comm.BInfinityCommunicator() -->
<constructor name="BInfinityCommunicator" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.comm.BInfinityCommunicator.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityCommunicator.grantAccess(com.tridium.ddf.comm.req.BIDdfRequest) -->
<method name="grantAccess"  protected="true">
<description>
This method is called for each request that is dequeued.&#xa; &#xa; Overridden in Infinity to provide exclusive access to the &#xa; communicator when doing terminal mode sessions and backups/&#xa; restores of the controllers.&#xa; &#xa; If returning true then the request will be processed&#xa; immediately (transmitted and scheduled for response checking). If&#xa; the developer returns false then the request will be placed back&#xa; in the back of the communicator queue.
</description>
<parameter name="ddfRequest">
<type class="com.tridium.ddf.comm.req.BIDdfRequest"/>
<description>
this is a request that was just pulled off of the&#xa; communicator queue. However, this request has not yet been processed.&#xa; This request will be processed immediately if this method returns&#xa; true. This request will be processed later if this method returns&#xa; false.
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true, unless the backup,restore, or terminal mode is active, and&#xa; the request is a Reload, ReloadLine, or Keystroke command.
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityCommunicator.isSteadyStateWorker() -->
<method name="isSteadyStateWorker"  protected="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityCommunicator.receiver -->
<field name="receiver"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;receiver&lt;/code&gt; property.
</description>
<tag name="@see">#getReceiver</tag>
<tag name="@see">#setReceiver</tag>
</field>

<!-- com.tridium.andoverInfinity.comm.BInfinityCommunicator.transmitter -->
<field name="transmitter"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;transmitter&lt;/code&gt; property.
</description>
<tag name="@see">#getTransmitter</tag>
<tag name="@see">#setTransmitter</tag>
</field>

<!-- com.tridium.andoverInfinity.comm.BInfinityCommunicator.pollScheduler -->
<field name="pollScheduler"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;pollScheduler&lt;/code&gt; property.&#xa; Use the default poll scheduler
</description>
<tag name="@see">#getPollScheduler</tag>
<tag name="@see">#setPollScheduler</tag>
</field>

<!-- com.tridium.andoverInfinity.comm.BInfinityCommunicator.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
