<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetPriorityValue" name="BBacnetPriorityValue" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BBacnetPriorityValue represents the BacnetPriorityValue&#xa; choice.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">28 May 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="value" flags="h">
<type class="javax.baja.sys.BSimple"/>
<description>
Slot for the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#getValue</tag>
<tag name="@see">#setValue</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue() -->
<constructor name="BBacnetPriorityValue" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue(javax.baja.bacnet.datatypes.BBacnetNull) -->
<constructor name="BBacnetPriorityValue" public="true">
<parameter name="bacnetNull">
<type class="javax.baja.bacnet.datatypes.BBacnetNull"/>
</parameter>
<description>
Null constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue(javax.baja.sys.BFloat) -->
<constructor name="BBacnetPriorityValue" public="true">
<parameter name="real">
<type class="javax.baja.sys.BFloat"/>
</parameter>
<description>
Real constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue(javax.baja.sys.BDouble) -->
<constructor name="BBacnetPriorityValue" public="true">
<parameter name="dble">
<type class="javax.baja.sys.BDouble"/>
</parameter>
<description>
Double constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue(javax.baja.bacnet.enums.BBacnetBinaryPv) -->
<constructor name="BBacnetPriorityValue" public="true">
<parameter name="binary">
<type class="javax.baja.bacnet.enums.BBacnetBinaryPv"/>
</parameter>
<description>
Binary constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue(javax.baja.sys.BInteger) -->
<constructor name="BBacnetPriorityValue" public="true">
<parameter name="integer">
<type class="javax.baja.sys.BInteger"/>
</parameter>
<description>
public BBacnetPriorityValue(BBacnetUnsigned unsigned)&#xa;  {&#xa;    choice = UNSIGNED_TAG;&#xa;    setValue(unsigned);&#xa;  }&#xa; Integer constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue(javax.baja.sys.BString) -->
<constructor name="BBacnetPriorityValue" public="true">
<parameter name="str">
<type class="javax.baja.sys.BString"/>
</parameter>
<description>
String constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue(javax.baja.sys.BSimple) -->
<constructor name="BBacnetPriorityValue" public="true">
<parameter name="constructedValue">
<type class="javax.baja.sys.BSimple"/>
</parameter>
<description>
public BBacnetPriorityValue(BBacnetOctetString octetString)&#xa;  {&#xa;    choice = OCTET_STRING_TAG;&#xa;    setValue(octetString);&#xa;  }&#xa;  public BBacnetPriorityValue(BBacnetBitString bitString)&#xa;  {&#xa;    choice = BIT_STRING_TAG;&#xa;    setValue(bitString);&#xa;  }&#xa;  public BBacnetPriorityValue(BBacnetDate date)&#xa;  {&#xa;    choice = DATE_TAG;&#xa;    setValue(date);&#xa;  }&#xa;  public BBacnetPriorityValue(BBacnetTime time)&#xa;  {&#xa;    choice = TIME_TAG;&#xa;    setValue(time);&#xa;  }&#xa;  public BBacnetPriorityValue(BAbsTime datetime)&#xa;  {&#xa;    choice = DATE_TIME_TAG;&#xa;    setValue(datetime);&#xa;  }&#xa; Constructed value constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.getValue() -->
<method name="getValue"  public="true">
<description>
Get the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#value</tag>
<return>
<type class="javax.baja.sys.BSimple"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.setValue(javax.baja.sys.BSimple) -->
<method name="setValue"  public="true">
<description>
Set the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#value</tag>
<parameter name="v">
<type class="javax.baja.sys.BSimple"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.choice() -->
<method name="choice"  public="true">
<description>
Get the choice of Bacnet data types currently being used&#xa; for this priority value.&#xa; This forces an update of the type from the value property.
</description>
<return>
<type class="int"/>
<description>
the choice: null, real, binary, integer, or constructedValue.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.isNull() -->
<method name="isNull"  public="true">
<description>
Some types of BObjects are used to indicate&#xa; a null value.  This method allows those types to&#xa; declare their null status by overriding this common&#xa; method.  The default is to return false.
</description>
<return>
<type class="boolean"/>
<description>
true if the current choice is NULL.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.getPriorityValue() -->
<method name="getPriorityValue"  public="true">
<description>
Get the priority value as a BValue.
</description>
<return>
<type class="javax.baja.sys.BValue"/>
<description>
the value property, as a BValue.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.setPriorityValue(javax.baja.sys.BValue) -->
<method name="setPriorityValue"  public="true">
<description>
Set the priority value.
</description>
<parameter name="v">
<type class="javax.baja.sys.BValue"/>
<description>
the priority value.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.setPriorityValue(javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="setPriorityValue"  public="true">
<description>
Set the priority value.
</description>
<parameter name="v">
<type class="javax.baja.sys.BValue"/>
<description>
the priority value.
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
the context for the set.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.choiceName() -->
<method name="choiceName"  public="true">
<description>
Get the choice name for this BBacnetPriorityValue&#x27;s choice.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.choiceName(int) -->
<method name="choiceName"  public="true" static="true">
<description>
Get the choice name for the given choice.
</description>
<parameter name="choice">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.choice(java.lang.String) -->
<method name="choice"  public="true" static="true">
<description>
Get the choice for the given choice name.
</description>
<parameter name="choiceName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.value -->
<field name="value"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#getValue</tag>
<tag name="@see">#setValue</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.INVALID_CHOICE -->
<field name="INVALID_CHOICE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.NULL_CHOICE -->
<field name="NULL_CHOICE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.REAL_CHOICE -->
<field name="REAL_CHOICE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.DOUBLE_CHOICE -->
<field name="DOUBLE_CHOICE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.BINARY_CHOICE -->
<field name="BINARY_CHOICE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.UNSIGNED_CHOICE -->
<field name="UNSIGNED_CHOICE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.INTEGER_CHOICE -->
<field name="INTEGER_CHOICE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.STRING_CHOICE -->
<field name="STRING_CHOICE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.OCTET_STRING_CHOICE -->
<field name="OCTET_STRING_CHOICE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.BIT_STRING_CHOICE -->
<field name="BIT_STRING_CHOICE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.DATE_CHOICE -->
<field name="DATE_CHOICE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.TIME_CHOICE -->
<field name="TIME_CHOICE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.DATE_TIME_CHOICE -->
<field name="DATE_TIME_CHOICE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.CONSTRUCTED_VALUE_CHOICE -->
<field name="CONSTRUCTED_VALUE_CHOICE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPriorityValue.CONSTRUCTED_VALUE_TAG -->
<field name="CONSTRUCTED_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
