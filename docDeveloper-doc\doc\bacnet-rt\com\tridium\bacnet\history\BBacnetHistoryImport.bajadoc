<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.history.BBacnetHistoryImport" name="BBacnetHistoryImport" packageName="com.tridium.bacnet.history" public="true">
<description>
BBacnetHistoryImport defines an archive action for transferring&#xa; one trend log from a remote Bacnet source to the local&#xa; destination.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 1$ $Date: 9/02/03 2:05:15 PM$</tag>
<tag name="@creation">02 Sep 03</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.history.BAbstractBacnetHistory"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
</class>
</bajadoc>
