<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.BBacnetNetwork" name="BBacnetNetwork" packageName="javax.baja.bacnet" public="true">
<description>
BBacnetNetwork is the base container for Bacnet communications.&#xa; &lt;p&gt;&#xa; It contains the BACnet communications stack, a local device which&#xa; displays information about our BACnet device representation, and&#xa; manages the worker queues for requests and writes.&#xa; &lt;p&gt;&#xa; All BACnet devices must be contained under this network container.&#xa; There may be at most one instance of BBacnetNetwork per station.&#xa; &lt;p&gt;&#xa; The order of callbacks during the startup sequence is as follows:&#xa; &lt;pre&gt;&#xa; BacnetNetwork.started()&#xa; BacnetStack.started()&#xa; BacnetClientLayer.started()&#xa; BacnetClientLayer.descendantsStarted()&#xa; BacnetServerLayer.started()&#xa; BacnetServerLayer.descendantsStarted()&#xa; BacnetTransportLayer.started()&#xa; BacnetTransportLayer.descendantsStarted()&#xa; BacnetNetworkLayer.started()&#xa; NetworkPort{ipPort}.started()&#xa; BacnetIpLinkLayer.started()&#xa; BacnetIpLinkLayer.descendantsStarted()&#xa; NetworkPort{ipPort}.descendantsStarted()&#xa; [NetworkPort{Xxx}.started()]&#xa; [BacnetXxxLinkLayer.started()]&#xa; [BacnetXxxLinkLayer.descendantsStarted()]&#xa; [NetworkPort.descendantsStarted()]&#xa; BacnetNetworkLayer.descendantsStarted()&#xa; BacnetStack.descendantsStarted()&#xa; [BacnetDevice1.started()]&#xa; [BacnetPointDeviceExt1.started()]&#xa; [BacnetPointDeviceExt1.descendantsStarted()]&#xa; [... virtual alarms schedules trendLogs config ...]&#xa; [BacnetDevice1.descendantsStarted()]&#xa; [BacnetDevice2.started()]&#xa; [BacnetPointDeviceExt2.started()]&#xa; [BacnetPointDeviceExt2.descendantsStarted()]&#xa; [... virtual alarms schedules trendLogs config ...]&#xa; [BacnetDevice2.descendantsStarted()]&#xa; BacnetNetwork.descendantsStarted()&#xa; &lt;/pre&gt;
</description>
<tag name="@author">Craig Gemmill on 12 Jan 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.driver.loadable.BLoadableNetwork"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<implements>
<type class="javax.baja.sys.BIService"/>
</implements>
<property name="historyPolicies" flags="">
<type class="javax.baja.driver.history.BHistoryNetworkExt"/>
<description>
Slot for the &lt;code&gt;historyPolicies&lt;/code&gt; property.&#xa; The rules used to determine the configuration of histories&#xa; that are pushed in to this device.
</description>
<tag name="@see">#getHistoryPolicies</tag>
<tag name="@see">#setHistoryPolicies</tag>
</property>

<property name="worker" flags="h">
<type class="javax.baja.bacnet.util.BBacnetWorker"/>
<description>
Slot for the &lt;code&gt;worker&lt;/code&gt; property.&#xa; the worker for managing asynchronous tasks like device&#xa; and object discovery, pings, etc.
</description>
<tag name="@see">#getWorker</tag>
<tag name="@see">#setWorker</tag>
</property>

<property name="writeWorker" flags="h">
<type class="javax.baja.bacnet.util.BBacnetWorker"/>
<description>
Slot for the &lt;code&gt;writeWorker&lt;/code&gt; property.&#xa; the worker for managing writes.
</description>
<tag name="@see">#getWriteWorker</tag>
<tag name="@see">#setWriteWorker</tag>
</property>

<property name="bacnetComm" flags="">
<type class="javax.baja.bacnet.io.BBacnetComm"/>
<description>
Slot for the &lt;code&gt;bacnetComm&lt;/code&gt; property.&#xa; the Bacnet comm stack.
</description>
<tag name="@see">#getBacnetComm</tag>
<tag name="@see">#setBacnetComm</tag>
</property>

<property name="localDevice" flags="">
<type class="javax.baja.bacnet.export.BLocalBacnetDevice"/>
<description>
Slot for the &lt;code&gt;localDevice&lt;/code&gt; property.&#xa; the representation of this Niagara station as a Bacnet device.
</description>
<tag name="@see">#getLocalDevice</tag>
<tag name="@see">#setLocalDevice</tag>
</property>

<property name="tuningPolicies" flags="">
<type class="javax.baja.driver.point.BTuningPolicyMap"/>
<description>
Slot for the &lt;code&gt;tuningPolicies&lt;/code&gt; property.&#xa; the map of tuning policies governing reads and writes.
</description>
<tag name="@see">#getTuningPolicies</tag>
<tag name="@see">#setTuningPolicies</tag>
</property>

<property name="covWorker" flags="h">
<type class="com.tridium.bacnet.stack.server.cov.BBacnetCovWorker"/>
<description>
Slot for the &lt;code&gt;covWorker&lt;/code&gt; property.&#xa; the worker for sending COV notifications
</description>
<tag name="@see">#getCovWorker</tag>
<tag name="@see">#setCovWorker</tag>
</property>

<property name="asyncPing" flags="h">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;asyncPing&lt;/code&gt; property.&#xa; dispatch ping requests to the network worker
</description>
<tag name="@see">#getAsyncPing</tag>
<tag name="@see">#setAsyncPing</tag>
</property>

<action name="submitDeviceManagerJob" flags="h">
<parameter name="parameter">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;submitDeviceManagerJob&lt;/code&gt; action.
</description>
<tag name="@see">#submitDeviceManagerJob(BValue parameter)</tag>
</action>

<action name="lookupDeviceById" flags="h">
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
</return>
<description>
Slot for the &lt;code&gt;lookupDeviceById&lt;/code&gt; action.
</description>
<tag name="@see">#lookupDeviceById(BBacnetObjectIdentifier parameter)</tag>
</action>

<action name="lookupDeviceByAddress" flags="h">
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
</return>
<description>
Slot for the &lt;code&gt;lookupDeviceByAddress&lt;/code&gt; action.
</description>
<tag name="@see">#lookupDeviceByAddress(BBacnetAddress parameter)</tag>
</action>

<action name="lookupDeviceOrdById" flags="h">
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;lookupDeviceOrdById&lt;/code&gt; action.
</description>
<tag name="@see">#lookupDeviceOrdById(BBacnetObjectIdentifier parameter)</tag>
</action>

<action name="lookupDeviceOrdByAddress" flags="h">
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;lookupDeviceOrdByAddress&lt;/code&gt; action.
</description>
<tag name="@see">#lookupDeviceOrdByAddress(BBacnetAddress parameter)</tag>
</action>

<!-- javax.baja.bacnet.BBacnetNetwork() -->
<constructor name="BBacnetNetwork" public="true">
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.BBacnetNetwork.getHistoryPolicies() -->
<method name="getHistoryPolicies"  public="true">
<description>
Get the &lt;code&gt;historyPolicies&lt;/code&gt; property.&#xa; The rules used to determine the configuration of histories&#xa; that are pushed in to this device.
</description>
<tag name="@see">#historyPolicies</tag>
<return>
<type class="javax.baja.driver.history.BHistoryNetworkExt"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.setHistoryPolicies(javax.baja.driver.history.BHistoryNetworkExt) -->
<method name="setHistoryPolicies"  public="true">
<description>
Set the &lt;code&gt;historyPolicies&lt;/code&gt; property.&#xa; The rules used to determine the configuration of histories&#xa; that are pushed in to this device.
</description>
<tag name="@see">#historyPolicies</tag>
<parameter name="v">
<type class="javax.baja.driver.history.BHistoryNetworkExt"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.getWorker() -->
<method name="getWorker"  public="true">
<description>
Get the &lt;code&gt;worker&lt;/code&gt; property.&#xa; the worker for managing asynchronous tasks like device&#xa; and object discovery, pings, etc.
</description>
<tag name="@see">#worker</tag>
<return>
<type class="javax.baja.bacnet.util.BBacnetWorker"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.setWorker(javax.baja.bacnet.util.BBacnetWorker) -->
<method name="setWorker"  public="true">
<description>
Set the &lt;code&gt;worker&lt;/code&gt; property.&#xa; the worker for managing asynchronous tasks like device&#xa; and object discovery, pings, etc.
</description>
<tag name="@see">#worker</tag>
<parameter name="v">
<type class="javax.baja.bacnet.util.BBacnetWorker"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.getWriteWorker() -->
<method name="getWriteWorker"  public="true">
<description>
Get the &lt;code&gt;writeWorker&lt;/code&gt; property.&#xa; the worker for managing writes.
</description>
<tag name="@see">#writeWorker</tag>
<return>
<type class="javax.baja.bacnet.util.BBacnetWorker"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.setWriteWorker(javax.baja.bacnet.util.BBacnetWorker) -->
<method name="setWriteWorker"  public="true">
<description>
Set the &lt;code&gt;writeWorker&lt;/code&gt; property.&#xa; the worker for managing writes.
</description>
<tag name="@see">#writeWorker</tag>
<parameter name="v">
<type class="javax.baja.bacnet.util.BBacnetWorker"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.getBacnetComm() -->
<method name="getBacnetComm"  public="true">
<description>
Get the &lt;code&gt;bacnetComm&lt;/code&gt; property.&#xa; the Bacnet comm stack.
</description>
<tag name="@see">#bacnetComm</tag>
<return>
<type class="javax.baja.bacnet.io.BBacnetComm"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.setBacnetComm(javax.baja.bacnet.io.BBacnetComm) -->
<method name="setBacnetComm"  public="true">
<description>
Set the &lt;code&gt;bacnetComm&lt;/code&gt; property.&#xa; the Bacnet comm stack.
</description>
<tag name="@see">#bacnetComm</tag>
<parameter name="v">
<type class="javax.baja.bacnet.io.BBacnetComm"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.getLocalDevice() -->
<method name="getLocalDevice"  public="true">
<description>
Get the &lt;code&gt;localDevice&lt;/code&gt; property.&#xa; the representation of this Niagara station as a Bacnet device.
</description>
<tag name="@see">#localDevice</tag>
<return>
<type class="javax.baja.bacnet.export.BLocalBacnetDevice"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.setLocalDevice(javax.baja.bacnet.export.BLocalBacnetDevice) -->
<method name="setLocalDevice"  public="true">
<description>
Set the &lt;code&gt;localDevice&lt;/code&gt; property.&#xa; the representation of this Niagara station as a Bacnet device.
</description>
<tag name="@see">#localDevice</tag>
<parameter name="v">
<type class="javax.baja.bacnet.export.BLocalBacnetDevice"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.getTuningPolicies() -->
<method name="getTuningPolicies"  public="true">
<description>
Get the &lt;code&gt;tuningPolicies&lt;/code&gt; property.&#xa; the map of tuning policies governing reads and writes.
</description>
<tag name="@see">#tuningPolicies</tag>
<return>
<type class="javax.baja.driver.point.BTuningPolicyMap"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.setTuningPolicies(javax.baja.driver.point.BTuningPolicyMap) -->
<method name="setTuningPolicies"  public="true">
<description>
Set the &lt;code&gt;tuningPolicies&lt;/code&gt; property.&#xa; the map of tuning policies governing reads and writes.
</description>
<tag name="@see">#tuningPolicies</tag>
<parameter name="v">
<type class="javax.baja.driver.point.BTuningPolicyMap"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.getCovWorker() -->
<method name="getCovWorker"  public="true">
<description>
Get the &lt;code&gt;covWorker&lt;/code&gt; property.&#xa; the worker for sending COV notifications
</description>
<tag name="@see">#covWorker</tag>
<return>
<type class="com.tridium.bacnet.stack.server.cov.BBacnetCovWorker"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.setCovWorker(com.tridium.bacnet.stack.server.cov.BBacnetCovWorker) -->
<method name="setCovWorker"  public="true">
<description>
Set the &lt;code&gt;covWorker&lt;/code&gt; property.&#xa; the worker for sending COV notifications
</description>
<tag name="@see">#covWorker</tag>
<parameter name="v">
<type class="com.tridium.bacnet.stack.server.cov.BBacnetCovWorker"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.getAsyncPing() -->
<method name="getAsyncPing"  public="true">
<description>
Get the &lt;code&gt;asyncPing&lt;/code&gt; property.&#xa; dispatch ping requests to the network worker
</description>
<tag name="@see">#asyncPing</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.setAsyncPing(boolean) -->
<method name="setAsyncPing"  public="true">
<description>
Set the &lt;code&gt;asyncPing&lt;/code&gt; property.&#xa; dispatch ping requests to the network worker
</description>
<tag name="@see">#asyncPing</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.submitDeviceManagerJob(javax.baja.sys.BValue) -->
<method name="submitDeviceManagerJob"  public="true">
<description>
Invoke the &lt;code&gt;submitDeviceManagerJob&lt;/code&gt; action.
</description>
<tag name="@see">#submitDeviceManagerJob</tag>
<parameter name="parameter">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.lookupDeviceById(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="lookupDeviceById"  public="true">
<description>
Invoke the &lt;code&gt;lookupDeviceById&lt;/code&gt; action.
</description>
<tag name="@see">#lookupDeviceById</tag>
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.lookupDeviceByAddress(javax.baja.bacnet.datatypes.BBacnetAddress) -->
<method name="lookupDeviceByAddress"  public="true">
<description>
Invoke the &lt;code&gt;lookupDeviceByAddress&lt;/code&gt; action.
</description>
<tag name="@see">#lookupDeviceByAddress</tag>
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.lookupDeviceOrdById(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="lookupDeviceOrdById"  public="true">
<description>
Invoke the &lt;code&gt;lookupDeviceOrdById&lt;/code&gt; action.
</description>
<tag name="@see">#lookupDeviceOrdById</tag>
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.lookupDeviceOrdByAddress(javax.baja.bacnet.datatypes.BBacnetAddress) -->
<method name="lookupDeviceOrdByAddress"  public="true">
<description>
Invoke the &lt;code&gt;lookupDeviceOrdByAddress&lt;/code&gt; action.
</description>
<tag name="@see">#lookupDeviceOrdByAddress</tag>
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.getServiceTypes() -->
<method name="getServiceTypes"  public="true">
<description>
Return the service types.
</description>
<return>
<type class="javax.baja.sys.Type" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.serviceStarted() -->
<method name="serviceStarted"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Service started.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.serviceStopped() -->
<method name="serviceStopped"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Service stopped.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.isChildLegal(javax.baja.sys.BComponent) -->
<method name="isChildLegal"  public="true">
<description>
Only one BLocalBacnetDevice is allowed.
</description>
<parameter name="child">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true unless the child is a second instance of BLocalBacnetDevice.
</description>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.started() -->
<method name="started"  public="true">
<description>
Network started.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<description/>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.descendantsStarted() -->
<method name="descendantsStarted"  public="true">
<description>
When all of the layers have been started,&#xa; announce ourselves on the Bacnet internetwork.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.descendantsStopped() -->
<method name="descendantsStopped"  public="true">
<description>
Wait until all BACnet points and devices have had a chance to complete&#xa; their traffic before shutting down the comm stack.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.getDeviceType() -->
<method name="getDeviceType"  public="true">
<description>
The base device type for BACnet devices is BBacnetDevice.
</description>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.getDeviceFolderType() -->
<method name="getDeviceFolderType"  public="true">
<description>
Get the Type for DeviceFolders for this network.
</description>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.getNavChildren() -->
<method name="getNavChildren"  public="true">
<description>
Filter out frozen slots which tend to be support objects&#xa; and not useful to display in the navigation tree.
</description>
<return>
<type class="javax.baja.nav.BINavNode" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.getLicenseFeature() -->
<method name="getLicenseFeature"  public="true" final="true">
<description>
If this driver is to be licensed using the standard licensing&#xa; mechanism then override this method to return the Feature or&#xa; return null for no license checks.  Convention is that the&#xa; vendor and feature name matches the declaring module.
</description>
<return>
<type class="javax.baja.license.Feature"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.isAws() -->
<method name="isAws"  public="true">
<description>
Override point for BacnetAwsNetwork, used to check&#xa; before invoking AWS-specific behavior.&#xa; Is this a BacnetAwsNetwork?
</description>
<return>
<type class="boolean"/>
<description>
false
</description>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.postAsync(java.lang.Runnable) -->
<method name="postAsync"  public="true">
<description>
Post an async action to the thread pool worker.
</description>
<parameter name="runnable">
<type class="java.lang.Runnable"/>
</parameter>
<return>
<type class="javax.baja.util.IFuture"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.postWrite(java.lang.Runnable) -->
<method name="postWrite"  public="true">
<description>
Post an async action to the thread pool worker.
</description>
<parameter name="runnable">
<type class="java.lang.Runnable"/>
</parameter>
<return>
<type class="javax.baja.util.IFuture"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.doSubmitDeviceManagerJob(javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="doSubmitDeviceManagerJob"  public="true">
<description>
Submit a device manager job.
</description>
<parameter name="arg">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.doLookupDeviceById(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="doLookupDeviceById"  public="true">
<description>
Look up a device by its objectId.
</description>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
<description>
the &lt;code&gt;BBacnetDevice&lt;/code&gt; registered with this objectId.
</description>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.doLookupDeviceByAddress(javax.baja.bacnet.datatypes.BBacnetAddress) -->
<method name="doLookupDeviceByAddress"  public="true">
<description>
Look up a device by its address.
</description>
<parameter name="address">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description/>
</parameter>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
<description>
the &lt;code&gt;BBacnetDevice&lt;/code&gt; registered with this address.
</description>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.doLookupDeviceOrdById(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="doLookupDeviceOrdById"  public="true">
<description>
Look up the ord for a device by its objectId.
</description>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
<description>
a &lt;code&gt;BOrd&lt;/code&gt; to the &lt;code&gt;BBacnetDevice&lt;/code&gt; registered with this objectId.
</description>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.doLookupDeviceOrdByAddress(javax.baja.bacnet.datatypes.BBacnetAddress) -->
<method name="doLookupDeviceOrdByAddress"  public="true">
<description>
Look up the ord for a device by its address.
</description>
<parameter name="address">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
<description>
a &lt;code&gt;BOrd&lt;/code&gt; for the &lt;code&gt;BBacnetDevice&lt;/code&gt; registered with this address.
</description>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.registerDevice(javax.baja.bacnet.BBacnetDevice) -->
<method name="registerDevice"  public="true" synchronized="true">
<description/>
<parameter name="device">
<type class="javax.baja.bacnet.BBacnetDevice"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.unregisterDevice(javax.baja.bacnet.BBacnetDevice) -->
<method name="unregisterDevice"  public="true" synchronized="true">
<description/>
<parameter name="device">
<type class="javax.baja.bacnet.BBacnetDevice"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.updateDevice(javax.baja.bacnet.BBacnetDevice) -->
<method name="updateDevice"  public="true" synchronized="true">
<description/>
<parameter name="device">
<type class="javax.baja.bacnet.BBacnetDevice"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.lookupDevice(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="lookupDevice"  public="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Look up a device by its objectId.
</description>
<tag name="@see">#doLookupDeviceById(BBacnetObjectIdentifier)</tag>
<tag name="@deprecated">as of 3.5.  Use the &lt;code&gt;lookupDeviceByObjectId&lt;/code&gt; action&#xa; or its implementation, &lt;code&gt;doLookupDeviceByObjectId(objectId)&lt;/code&gt;.</tag>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
<description>
the &lt;code&gt;BBacnetDevice&lt;/code&gt; registered with this objectId.
</description>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.lookupDevice(javax.baja.bacnet.datatypes.BBacnetAddress) -->
<method name="lookupDevice"  public="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Look up a device by its address.
</description>
<tag name="@see">#doLookupDeviceByAddress(BBacnetAddress)</tag>
<tag name="@deprecated">as of 3.5.  Use the &lt;code&gt;lookupDeviceByAddress&lt;/code&gt; action&#xa; or its implementation, &lt;code&gt;doLookupDeviceByAddress(address)&lt;/code&gt;.</tag>
<parameter name="address">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description/>
</parameter>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
<description>
the &lt;code&gt;BBacnetDevice&lt;/code&gt; registered with this address.
</description>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.updateDeviceInfo(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, javax.baja.bacnet.datatypes.BBacnetAddress, int, javax.baja.bacnet.enums.BBacnetSegmentation, int) -->
<method name="updateDeviceInfo"  public="true">
<description>
Update the device information.&#xa; The objectId is used to look up the &lt;code&gt;BBacnetDevice&lt;/code&gt;, and&#xa; its parameters are then updated with the new values.&#xa; &lt;p&gt;&#xa; This is generally done as the result of receiving an I-Am message&#xa; from the device.
</description>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<parameter name="address">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description/>
</parameter>
<parameter name="maxAPDULengthAccepted">
<type class="int"/>
<description/>
</parameter>
<parameter name="segmentationSupported">
<type class="javax.baja.bacnet.enums.BBacnetSegmentation"/>
<description/>
</parameter>
<parameter name="vendorId">
<type class="int"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.getDeviceList() -->
<method name="getDeviceList"  public="true">
<description>
Get an array containing all the BBacnetDevice&#xa; children of this network.
</description>
<return>
<type class="javax.baja.bacnet.BBacnetDevice" dimension="1"/>
<description>
an array of &lt;code&gt;BBacnetDevice&lt;/code&gt;s.
</description>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.getObjectId() -->
<method name="getObjectId"  public="true">
<description>
Get our object identifier.
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.bacnet() -->
<method name="bacnet"  public="true" static="true">
<description>
Get the Bacnet Service.
</description>
<return>
<type class="javax.baja.bacnet.BBacnetNetwork"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.localDevice() -->
<method name="localDevice"  public="true" static="true">
<description>
Get the local Bacnet device.
</description>
<return>
<type class="javax.baja.bacnet.export.BLocalBacnetDevice"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.isNetworkReady() -->
<method name="isNetworkReady"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.getPollService(javax.baja.bacnet.util.BIBacnetPollable) -->
<method name="getPollService"  public="true">
<description/>
<parameter name="pollable">
<type class="javax.baja.bacnet.util.BIBacnetPollable"/>
</parameter>
<return>
<type class="javax.baja.driver.util.BAbstractPollService"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.tuningChanged(javax.baja.bacnet.point.BBacnetTuningPolicy, javax.baja.sys.Context) -->
<method name="tuningChanged"  public="true">
<description/>
<parameter name="policy">
<type class="javax.baja.bacnet.point.BBacnetTuningPolicy"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.uploadOnStart() -->
<method name="uploadOnStart"  public="true">
<description/>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.setAndGetWriteOnFacetChange() -->
<method name="setAndGetWriteOnFacetChange"  public="true">
<description/>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.setAndGetShouldSupportFaults() -->
<method name="setAndGetShouldSupportFaults"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.setAndGetPrivateTransferResultBlockFlag() -->
<method name="setAndGetPrivateTransferResultBlockFlag"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.getAgents(javax.baja.sys.Context) -->
<method name="getAgents"  public="true">
<description>
Get the agent list.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.agent.AgentList"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetNetwork.historyPolicies -->
<field name="historyPolicies"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;historyPolicies&lt;/code&gt; property.&#xa; The rules used to determine the configuration of histories&#xa; that are pushed in to this device.
</description>
<tag name="@see">#getHistoryPolicies</tag>
<tag name="@see">#setHistoryPolicies</tag>
</field>

<!-- javax.baja.bacnet.BBacnetNetwork.worker -->
<field name="worker"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;worker&lt;/code&gt; property.&#xa; the worker for managing asynchronous tasks like device&#xa; and object discovery, pings, etc.
</description>
<tag name="@see">#getWorker</tag>
<tag name="@see">#setWorker</tag>
</field>

<!-- javax.baja.bacnet.BBacnetNetwork.writeWorker -->
<field name="writeWorker"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;writeWorker&lt;/code&gt; property.&#xa; the worker for managing writes.
</description>
<tag name="@see">#getWriteWorker</tag>
<tag name="@see">#setWriteWorker</tag>
</field>

<!-- javax.baja.bacnet.BBacnetNetwork.bacnetComm -->
<field name="bacnetComm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;bacnetComm&lt;/code&gt; property.&#xa; the Bacnet comm stack.
</description>
<tag name="@see">#getBacnetComm</tag>
<tag name="@see">#setBacnetComm</tag>
</field>

<!-- javax.baja.bacnet.BBacnetNetwork.localDevice -->
<field name="localDevice"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;localDevice&lt;/code&gt; property.&#xa; the representation of this Niagara station as a Bacnet device.
</description>
<tag name="@see">#getLocalDevice</tag>
<tag name="@see">#setLocalDevice</tag>
</field>

<!-- javax.baja.bacnet.BBacnetNetwork.tuningPolicies -->
<field name="tuningPolicies"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;tuningPolicies&lt;/code&gt; property.&#xa; the map of tuning policies governing reads and writes.
</description>
<tag name="@see">#getTuningPolicies</tag>
<tag name="@see">#setTuningPolicies</tag>
</field>

<!-- javax.baja.bacnet.BBacnetNetwork.covWorker -->
<field name="covWorker"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;covWorker&lt;/code&gt; property.&#xa; the worker for sending COV notifications
</description>
<tag name="@see">#getCovWorker</tag>
<tag name="@see">#setCovWorker</tag>
</field>

<!-- javax.baja.bacnet.BBacnetNetwork.asyncPing -->
<field name="asyncPing"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;asyncPing&lt;/code&gt; property.&#xa; dispatch ping requests to the network worker
</description>
<tag name="@see">#getAsyncPing</tag>
<tag name="@see">#setAsyncPing</tag>
</field>

<!-- javax.baja.bacnet.BBacnetNetwork.submitDeviceManagerJob -->
<field name="submitDeviceManagerJob"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;submitDeviceManagerJob&lt;/code&gt; action.
</description>
<tag name="@see">#submitDeviceManagerJob(BValue parameter)</tag>
</field>

<!-- javax.baja.bacnet.BBacnetNetwork.lookupDeviceById -->
<field name="lookupDeviceById"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;lookupDeviceById&lt;/code&gt; action.
</description>
<tag name="@see">#lookupDeviceById(BBacnetObjectIdentifier parameter)</tag>
</field>

<!-- javax.baja.bacnet.BBacnetNetwork.lookupDeviceByAddress -->
<field name="lookupDeviceByAddress"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;lookupDeviceByAddress&lt;/code&gt; action.
</description>
<tag name="@see">#lookupDeviceByAddress(BBacnetAddress parameter)</tag>
</field>

<!-- javax.baja.bacnet.BBacnetNetwork.lookupDeviceOrdById -->
<field name="lookupDeviceOrdById"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;lookupDeviceOrdById&lt;/code&gt; action.
</description>
<tag name="@see">#lookupDeviceOrdById(BBacnetObjectIdentifier parameter)</tag>
</field>

<!-- javax.baja.bacnet.BBacnetNetwork.lookupDeviceOrdByAddress -->
<field name="lookupDeviceOrdByAddress"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;lookupDeviceOrdByAddress&lt;/code&gt; action.
</description>
<tag name="@see">#lookupDeviceOrdByAddress(BBacnetAddress parameter)</tag>
</field>

<!-- javax.baja.bacnet.BBacnetNetwork.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.BBacnetNetwork.UPLOAD_ON_START -->
<field name="UPLOAD_ON_START"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BBacnetNetwork.WRITE_ON_FACET_CHANGE -->
<field name="WRITE_ON_FACET_CHANGE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BBacnetNetwork.SHOULD_SUPPORT_FAULTS_MULTI_STATE -->
<field name="SHOULD_SUPPORT_FAULTS_MULTI_STATE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BBacnetNetwork.PRIVATE_TRANSFER_RESULT_BLOCK -->
<field name="PRIVATE_TRANSFER_RESULT_BLOCK"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BBacnetNetwork.log -->
<field name="log"  protected="true" static="true" final="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

</class>
</bajadoc>
