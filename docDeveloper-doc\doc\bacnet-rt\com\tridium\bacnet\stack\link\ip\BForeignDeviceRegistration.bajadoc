<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.link.ip.BForeignDeviceRegistration" name="BForeignDeviceRegistration" packageName="com.tridium.bacnet.stack.link.ip" public="true">
<description>
The BForeignDeviceRegistration class exposes the ability to&#xa; more finely control foreign device registrations with BBMD devices.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="enabled" flags="d">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;enabled&lt;/code&gt; property.&#xa; Should this registration be used?
</description>
<tag name="@see">#getEnabled</tag>
<tag name="@see">#setEnabled</tag>
</property>

<property name="bbmdAddress" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;bbmdAddress&lt;/code&gt; property.&#xa; The IP address of the BBMD with which the local device will&#xa; register as a Foreign Device, if there is no BBMD on our subnet.&#xa; The udpPort is used to form the full address.
</description>
<tag name="@see">#getBbmdAddress</tag>
<tag name="@see">#setBbmdAddress</tag>
</property>

<property name="registrationLifetime" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;registrationLifetime&lt;/code&gt; property.&#xa; The duration of the registration with the BBMD.&#xa; The device will re-register periodically before this time expires.
</description>
<tag name="@see">#getRegistrationLifetime</tag>
<tag name="@see">#setRegistrationLifetime</tag>
</property>

<action name="registerWithBBMD" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;registerWithBBMD&lt;/code&gt; action.&#xa; Register as a foreign device to the specified BBMD.
</description>
<tag name="@see">#registerWithBBMD()</tag>
</action>

<action name="unregisterWithBBMD" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;unregisterWithBBMD&lt;/code&gt; action.&#xa; Unregister as a foreign device from the specified BBMD.
</description>
<tag name="@see">#unregisterWithBBMD()</tag>
</action>

</class>
</bajadoc>
