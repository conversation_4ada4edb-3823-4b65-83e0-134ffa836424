<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="alarm" runtimeProfile="wb" name="com.tridium.alarm.ui.portal">
<description/>
<class packageName="com.tridium.alarm.ui.portal" name="BAlarmPortal"><description>BAlarmPortal is the main display view of the&#xa; alarm portal tool.</description></class>
<class packageName="com.tridium.alarm.ui.portal" name="BAlarmPortalOptions"><description>BAlarmPortalOptions wraps all configuration&#xa; for an alarm portal.</description></class>
<class packageName="com.tridium.alarm.ui.portal" name="BAlarmPortalProfile"/>
<class packageName="com.tridium.alarm.ui.portal" name="BAlarmPortalTool"><description>BAlarmPortalTool is the component which serves as place&#xa; holder in the tools menu and declares the actual views.</description></class>
<class packageName="com.tridium.alarm.ui.portal" name="BConsoleRecord"/>
<class packageName="com.tridium.alarm.ui.portal" name="BConsoleRecordList"/>
<class packageName="com.tridium.alarm.ui.portal" name="BPortalAlarmConsole"/>
<class packageName="com.tridium.alarm.ui.portal" name="BPortalConsoleRecipient"><description>BAlarmPortalTool is the component which serves as place &#xa; holder in the tools menu and declares the actual views.</description></class>
</package>
</bajadoc>
