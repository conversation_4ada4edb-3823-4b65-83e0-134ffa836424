<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.datatypes.BPupPeerListRecord" name="BPupPeerListRecord" packageName="com.tridium.aapup.datatypes" public="true">
<description>
BPupPeerListRecord is a dynamic slot added to the BPupNetwork&#x27;s peer list&#xa; whenever a new device is detected on the network, whether by&#xa; discovery or passively by listening while other devices have the&#xa; token, or by virtue of being configured as a BPupDevice in the&#xa; station configuration.
</description>
<tag name="@author">Clif <PERSON></tag>
<tag name="@creation">7/15/2005 9:54AM</tag>
<tag name="@version">$Revision$ $Date: 7/15/2005 9:54AM</tag>
<tag name="@since">AX 3.0.90</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="unitNumber" flags="tr">
<type class="int"/>
<description>
Slot for the &lt;code&gt;unitNumber&lt;/code&gt; property.&#xa; The address of the device
</description>
<tag name="@see">#getUnitNumber</tag>
<tag name="@see">#setUnitNumber</tag>
</property>

<property name="peerType" flags="tr">
<type class="com.tridium.aapup.enums.BPupPeerTypeEnum"/>
<description>
Slot for the &lt;code&gt;peerType&lt;/code&gt; property.&#xa; The type of peer, if known
</description>
<tag name="@see">#getPeerType</tag>
<tag name="@see">#setPeerType</tag>
</property>

<property name="lastTokenTime" flags="tr">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;lastTokenTime&lt;/code&gt; property.&#xa; the last time we heard anything from this guy
</description>
<tag name="@see">#getLastTokenTime</tag>
<tag name="@see">#setLastTokenTime</tag>
</property>

</class>
</bajadoc>
