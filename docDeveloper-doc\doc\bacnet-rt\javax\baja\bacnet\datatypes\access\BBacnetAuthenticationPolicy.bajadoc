<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicy" name="BBacnetAuthenticationPolicy" packageName="javax.baja.bacnet.datatypes.access" public="true" final="true">
<description>
BBacnetAuthenticationPolicy represents the BBacnetAuthenticationPolicy&#xa; sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="orderEnforced" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;orderEnforced&lt;/code&gt; property.
</description>
<tag name="@see">#getOrderEnforced</tag>
<tag name="@see">#setOrderEnforced</tag>
</property>

<property name="timeout" flags="">
<type class="long"/>
<description>
Slot for the &lt;code&gt;timeout&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeout</tag>
<tag name="@see">#setTimeout</tag>
</property>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicy() -->
<constructor name="BBacnetAuthenticationPolicy" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicy(java.util.List&lt;javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicyEntry&gt;, boolean, int) -->
<constructor name="BBacnetAuthenticationPolicy" public="true">
<parameter name="policyEntries">
<parameterizedType class="java.util.List">
<args>
<type class="javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicyEntry"/>
</args>
</parameterizedType>
</parameter>
<parameter name="orderEnforced">
<type class="boolean"/>
</parameter>
<parameter name="timeout">
<type class="int"/>
</parameter>
<description>
Standard constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicy.getOrderEnforced() -->
<method name="getOrderEnforced"  public="true">
<description>
Get the &lt;code&gt;orderEnforced&lt;/code&gt; property.
</description>
<tag name="@see">#orderEnforced</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicy.setOrderEnforced(boolean) -->
<method name="setOrderEnforced"  public="true">
<description>
Set the &lt;code&gt;orderEnforced&lt;/code&gt; property.
</description>
<tag name="@see">#orderEnforced</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicy.getTimeout() -->
<method name="getTimeout"  public="true">
<description>
Get the &lt;code&gt;timeout&lt;/code&gt; property.
</description>
<tag name="@see">#timeout</tag>
<return>
<type class="long"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicy.setTimeout(long) -->
<method name="setTimeout"  public="true">
<description>
Set the &lt;code&gt;timeout&lt;/code&gt; property.
</description>
<tag name="@see">#timeout</tag>
<parameter name="v">
<type class="long"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicy.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicy.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicy.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicy.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicy.orderEnforced -->
<field name="orderEnforced"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;orderEnforced&lt;/code&gt; property.
</description>
<tag name="@see">#getOrderEnforced</tag>
<tag name="@see">#setOrderEnforced</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicy.timeout -->
<field name="timeout"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;timeout&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeout</tag>
<tag name="@see">#setTimeout</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicy.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicy.POLICY_TAG -->
<field name="POLICY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicy.ORDER_ENFORCED_TAG -->
<field name="ORDER_ENFORCED_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicy.TIMEOUT_TAG -->
<field name="TIMEOUT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationPolicy.MAX_ENTRIES -->
<field name="MAX_ENTRIES"  public="true" static="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
