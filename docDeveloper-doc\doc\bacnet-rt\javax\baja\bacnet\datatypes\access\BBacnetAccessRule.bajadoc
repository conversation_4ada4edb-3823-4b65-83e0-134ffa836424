<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.access.BBacnetAccessRule" name="BBacnetAccessRule" packageName="javax.baja.bacnet.datatypes.access" public="true" final="true">
<description>
BBacnetAccessRule represents the BACnetAccessRule sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="timeRangeSpecifier" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;timeRangeSpecifier&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeRangeSpecifier</tag>
<tag name="@see">#setTimeRangeSpecifier</tag>
</property>

<property name="timeRange" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
<description>
Slot for the &lt;code&gt;timeRange&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeRange</tag>
<tag name="@see">#setTimeRange</tag>
</property>

<property name="locationSpecifier" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;locationSpecifier&lt;/code&gt; property.
</description>
<tag name="@see">#getLocationSpecifier</tag>
<tag name="@see">#setLocationSpecifier</tag>
</property>

<property name="location" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference"/>
<description>
Slot for the &lt;code&gt;location&lt;/code&gt; property.
</description>
<tag name="@see">#getLocation</tag>
<tag name="@see">#setLocation</tag>
</property>

<property name="enable" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;enable&lt;/code&gt; property.
</description>
<tag name="@see">#getEnable</tag>
<tag name="@see">#setEnable</tag>
</property>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule() -->
<constructor name="BBacnetAccessRule" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule(int, javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference, int, javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference, boolean) -->
<constructor name="BBacnetAccessRule" public="true">
<parameter name="timeRangeSpecifier">
<type class="int"/>
</parameter>
<parameter name="timeRange">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
</parameter>
<parameter name="locationSpecifier">
<type class="int"/>
</parameter>
<parameter name="location">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference"/>
</parameter>
<parameter name="enable">
<type class="boolean"/>
</parameter>
<description>
Standard constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.getTimeRangeSpecifier() -->
<method name="getTimeRangeSpecifier"  public="true">
<description>
Get the &lt;code&gt;timeRangeSpecifier&lt;/code&gt; property.
</description>
<tag name="@see">#timeRangeSpecifier</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.setTimeRangeSpecifier(int) -->
<method name="setTimeRangeSpecifier"  public="true">
<description>
Set the &lt;code&gt;timeRangeSpecifier&lt;/code&gt; property.
</description>
<tag name="@see">#timeRangeSpecifier</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.getTimeRange() -->
<method name="getTimeRange"  public="true">
<description>
Get the &lt;code&gt;timeRange&lt;/code&gt; property.
</description>
<tag name="@see">#timeRange</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.setTimeRange(javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference) -->
<method name="setTimeRange"  public="true">
<description>
Set the &lt;code&gt;timeRange&lt;/code&gt; property.
</description>
<tag name="@see">#timeRange</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.getLocationSpecifier() -->
<method name="getLocationSpecifier"  public="true">
<description>
Get the &lt;code&gt;locationSpecifier&lt;/code&gt; property.
</description>
<tag name="@see">#locationSpecifier</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.setLocationSpecifier(int) -->
<method name="setLocationSpecifier"  public="true">
<description>
Set the &lt;code&gt;locationSpecifier&lt;/code&gt; property.
</description>
<tag name="@see">#locationSpecifier</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.getLocation() -->
<method name="getLocation"  public="true">
<description>
Get the &lt;code&gt;location&lt;/code&gt; property.
</description>
<tag name="@see">#location</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.setLocation(javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference) -->
<method name="setLocation"  public="true">
<description>
Set the &lt;code&gt;location&lt;/code&gt; property.
</description>
<tag name="@see">#location</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.getEnable() -->
<method name="getEnable"  public="true">
<description>
Get the &lt;code&gt;enable&lt;/code&gt; property.
</description>
<tag name="@see">#enable</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.setEnable(boolean) -->
<method name="setEnable"  public="true">
<description>
Set the &lt;code&gt;enable&lt;/code&gt; property.
</description>
<tag name="@see">#enable</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.timeRangeSpecifier -->
<field name="timeRangeSpecifier"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;timeRangeSpecifier&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeRangeSpecifier</tag>
<tag name="@see">#setTimeRangeSpecifier</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.timeRange -->
<field name="timeRange"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;timeRange&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeRange</tag>
<tag name="@see">#setTimeRange</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.locationSpecifier -->
<field name="locationSpecifier"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;locationSpecifier&lt;/code&gt; property.
</description>
<tag name="@see">#getLocationSpecifier</tag>
<tag name="@see">#setLocationSpecifier</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.location -->
<field name="location"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;location&lt;/code&gt; property.
</description>
<tag name="@see">#getLocation</tag>
<tag name="@see">#setLocation</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.enable -->
<field name="enable"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;enable&lt;/code&gt; property.
</description>
<tag name="@see">#getEnable</tag>
<tag name="@see">#setEnable</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.SPECIFIED -->
<field name="SPECIFIED"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.TIME_RANGE_SPECIFIER_TAG -->
<field name="TIME_RANGE_SPECIFIER_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.TIME_RANGE_TAG -->
<field name="TIME_RANGE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.LOCATION_SPECIFIER_TAG -->
<field name="LOCATION_SPECIFIER_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.LOCATION_TAG -->
<field name="LOCATION_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.ENABLE_TAG -->
<field name="ENABLE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessRule.MAX_ENCODED_SIZE -->
<field name="MAX_ENCODED_SIZE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
