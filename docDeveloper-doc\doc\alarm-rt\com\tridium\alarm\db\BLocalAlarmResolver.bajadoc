<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.db.BLocalAlarmResolver" name="BLocalAlarmResolver" packageName="com.tridium.alarm.db" public="true" final="true">
<description>
Resolves alarm queries locally.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">23 Sep 2004</tag>
<tag name="@version">$Revision: 4$ $Date: 1/4/11 12:57:49 PM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BSingleton"/>
</extends>
<implements>
<type class="com.tridium.alarm.db.BIAlarmResolver"/>
</implements>
<implements>
<type class="javax.baja.agent.BIAgent"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraSingleton"/>
</annotation>
</class>
</bajadoc>
