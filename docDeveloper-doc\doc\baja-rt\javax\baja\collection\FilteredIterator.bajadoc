<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.collection.FilteredIterator" name="FilteredIterator" packageName="javax.baja.collection" public="true">
<description>
A FilteredIterator wraps an existing Iterator and filters the results&#xa; using a predicate.  The resulting iterator will include only those&#xa; elements which pass the predicate test (i.e. test(T) returns true).
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">2/17/14</tag>
<tag name="@since">Niagara 4.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<parameterizedType class="java.util.Iterator">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</implements>
<typeParameters>
<typeVariable class="T">
</typeVariable>
</typeParameters>
<!-- javax.baja.collection.FilteredIterator(java.util.function.Predicate&lt;T&gt;, java.util.Iterator&lt;T&gt;) -->
<constructor name="FilteredIterator" public="true">
<parameter name="filter">
<parameterizedType class="java.util.function.Predicate">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
<description>
The predicate for testing the elements of the inner Iterator.&#xa;               The result includes all elements for which test(T) returns true.
</description>
</parameter>
<parameter name="inner">
<parameterizedType class="java.util.Iterator">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
<description>
An Iterator to filter.
</description>
</parameter>
<description>
Create a new FilteredIterator that filters the specified sub-iterator&#xa; using the specified predicate filter.
</description>
</constructor>

<!-- javax.baja.collection.FilteredIterator.hasNext() -->
<method name="hasNext"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.FilteredIterator.next() -->
<method name="next"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<typeVariable class="T"/>
</return>
</method>

</class>
</bajadoc>
