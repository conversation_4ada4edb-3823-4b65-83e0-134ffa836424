<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="com.tridium.bacnetAws.BBacnetAwsDevice" name="BBacnetAwsDevice" packageName="com.tridium.bacnetAws" public="true">
<description>
BBacnetDevice represents the Baja shadow object for a Bacnet device under a BacnetAwsNetwork.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">07 June 2010</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.5</tag>
<extends>
<type class="javax.baja.bacnet.BBacnetDevice"/>
</extends>
<action name="createObject" flags="h">
<parameter name="parameter">
<type class="com.tridium.bacnetAws.datatypes.BCreateObjectParameters"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;createObject&lt;/code&gt; action.&#xa; Create one or more objects on device.
</description>
<tag name="@see">#createObject(BCreateObjectParameters parameter)</tag>
</action>

<action name="deleteObject" flags="h">
<parameter name="parameter">
<type class="com.tridium.bacnetAws.datatypes.BObjectParameters"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;deleteObject&lt;/code&gt; action.&#xa; Create one or more objects on device.
</description>
<tag name="@see">#deleteObject(BObjectParameters parameter)</tag>
</action>

<topic name="objectCreated" flags="">
<eventType>
<type class="javax.baja.sys.BString"/>
</eventType><description>
Slot for the &lt;code&gt;objectCreated&lt;/code&gt; topic.&#xa; fired when the object successfully created on device
</description>
<tag name="@see">#fireObjectCreated</tag>
</topic>

<topic name="objectDeleted" flags="">
<eventType>
<type class="javax.baja.sys.BString"/>
</eventType><description>
Slot for the &lt;code&gt;objectDeleted&lt;/code&gt; topic.&#xa; fired when the object successfully created on device
</description>
<tag name="@see">#fireObjectDeleted</tag>
</topic>

</class>
</bajadoc>
