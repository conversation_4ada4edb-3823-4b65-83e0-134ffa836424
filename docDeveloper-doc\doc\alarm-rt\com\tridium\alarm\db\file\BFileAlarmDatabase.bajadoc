<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.db.file.BFileAlarmDatabase" name="BFileAlarmDatabase" packageName="com.tridium.alarm.db.file" public="true">
<description>
BFileAlarmDatabase is an alarm database implementation that stores&#xa; alarms in a file on the file system.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">05 Oct 2004</tag>
<tag name="@version">$Revision: 34$ $Date: 8/3/10 1:04:04 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.alarm.BAlarmDatabase"/>
</extends>
<implements>
<type class="javax.baja.dataRecovery.BIDataRecoverySource"/>
</implements>
</class>
</bajadoc>
