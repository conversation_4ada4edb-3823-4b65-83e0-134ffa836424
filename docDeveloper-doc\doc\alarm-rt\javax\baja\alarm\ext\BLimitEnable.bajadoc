<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.ext.BLimitEnable" name="BLimitEnable" packageName="javax.baja.alarm.ext" public="true">
<description>
BLimitEnable
</description>
<tag name="@author">Dan <PERSON></tag>
<tag name="@creation">9 Nov 00</tag>
<tag name="@version">$Revision: 5$ $Date: 10/23/03 8:36:16 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="lowLimitEnable" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;lowLimitEnable&lt;/code&gt; property.
</description>
<tag name="@see">#getLowLimitEnable</tag>
<tag name="@see">#setLowLimitEnable</tag>
</property>

<property name="highLimitEnable" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;highLimitEnable&lt;/code&gt; property.
</description>
<tag name="@see">#getHighLimitEnable</tag>
<tag name="@see">#setHighLimitEnable</tag>
</property>

<!-- javax.baja.alarm.ext.BLimitEnable() -->
<constructor name="BLimitEnable" public="true">
<description/>
</constructor>

<!-- javax.baja.alarm.ext.BLimitEnable.getLowLimitEnable() -->
<method name="getLowLimitEnable"  public="true">
<description>
Get the &lt;code&gt;lowLimitEnable&lt;/code&gt; property.
</description>
<tag name="@see">#lowLimitEnable</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BLimitEnable.setLowLimitEnable(boolean) -->
<method name="setLowLimitEnable"  public="true">
<description>
Set the &lt;code&gt;lowLimitEnable&lt;/code&gt; property.
</description>
<tag name="@see">#lowLimitEnable</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BLimitEnable.getHighLimitEnable() -->
<method name="getHighLimitEnable"  public="true">
<description>
Get the &lt;code&gt;highLimitEnable&lt;/code&gt; property.
</description>
<tag name="@see">#highLimitEnable</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BLimitEnable.setHighLimitEnable(boolean) -->
<method name="setHighLimitEnable"  public="true">
<description>
Set the &lt;code&gt;highLimitEnable&lt;/code&gt; property.
</description>
<tag name="@see">#highLimitEnable</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BLimitEnable.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BLimitEnable.isHighLimitEnabled() -->
<method name="isHighLimitEnabled"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BLimitEnable.isLowLimitEnabled() -->
<method name="isLowLimitEnabled"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BLimitEnable.lowLimitEnable -->
<field name="lowLimitEnable"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lowLimitEnable&lt;/code&gt; property.
</description>
<tag name="@see">#getLowLimitEnable</tag>
<tag name="@see">#setLowLimitEnable</tag>
</field>

<!-- javax.baja.alarm.ext.BLimitEnable.highLimitEnable -->
<field name="highLimitEnable"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;highLimitEnable&lt;/code&gt; property.
</description>
<tag name="@see">#getHighLimitEnable</tag>
<tag name="@see">#setHighLimitEnable</tag>
</field>

<!-- javax.baja.alarm.ext.BLimitEnable.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
