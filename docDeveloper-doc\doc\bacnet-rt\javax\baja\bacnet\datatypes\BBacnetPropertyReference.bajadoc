<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetPropertyReference" name="BBacnetPropertyReference" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
This class represents the BacnetPropertyReference sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">31 May 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.io.PropertyReference"/>
</implements>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="propertyId" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyId</tag>
<tag name="@see">#setPropertyId</tag>
</property>

<property name="propertyArrayIndex" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyArrayIndex</tag>
<tag name="@see">#setPropertyArrayIndex</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyReference() -->
<constructor name="BBacnetPropertyReference" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyReference(int) -->
<constructor name="BBacnetPropertyReference" public="true">
<parameter name="propertyId">
<type class="int"/>
<description>
the property-identifier to be referenced.
</description>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyReference(int, int) -->
<constructor name="BBacnetPropertyReference" public="true">
<parameter name="propertyId">
<type class="int"/>
<description>
the property-identifier to be referenced.
</description>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
<description>
the array index.
</description>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyReference.getPropertyId() -->
<method name="getPropertyId"  public="true">
<description>
Get the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#propertyId</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyReference.setPropertyId(int) -->
<method name="setPropertyId"  public="true">
<description>
Set the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#propertyId</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyReference.getPropertyArrayIndex() -->
<method name="getPropertyArrayIndex"  public="true">
<description>
Get the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#propertyArrayIndex</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyReference.setPropertyArrayIndex(int) -->
<method name="setPropertyArrayIndex"  public="true">
<description>
Set the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#propertyArrayIndex</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyReference.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyReference.isPropertyArrayIndexUsed() -->
<method name="isPropertyArrayIndexUsed"  public="true">
<description/>
<return>
<type class="boolean"/>
<description>
true if the property array index is used.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyReference.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyReference.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyReference.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
a descriptive string.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyReference.toDebugString() -->
<method name="toDebugString"  public="true">
<description>
Debug string.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyReference.propertyId -->
<field name="propertyId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyId</tag>
<tag name="@see">#setPropertyId</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyReference.propertyArrayIndex -->
<field name="propertyArrayIndex"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyArrayIndex</tag>
<tag name="@see">#setPropertyArrayIndex</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyReference.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyReference.PROPERTY_ID_TAG -->
<field name="PROPERTY_ID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
NBacnetPropertyReference Asn Context Tags&#xa; See Bacnet Clause 21.
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPropertyReference.PROPERTY_ARRAY_INDEX_TAG -->
<field name="PROPERTY_ARRAY_INDEX_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
