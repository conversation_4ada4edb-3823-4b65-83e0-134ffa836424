<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.BAndoverNetwork" name="BAndoverNetwork" packageName="com.tridium.andoverAC256" public="true">
<description>
BAndoverNetwork - represents an AC256 or AC8 Serial Network.
</description>
<tag name="@author"><PERSON><PERSON><PERSON> on 28 Sep 04</tag>
<tag name="@since">Niagara 3.0 andoverAC256 1.0</tag>
<extends>
<type class="com.tridium.basicdriver.serial.BSerialNetwork"/>
</extends>
<property name="unsolicitedReceiveHandler" flags="">
<type class="com.tridium.andoverAC256.comm.BAndoverUnsolicitedReceive"/>
<description>
Slot for the &lt;code&gt;unsolicitedReceiveHandler&lt;/code&gt; property.&#xa; Queues and process unsolicited messages received from the field-bus
</description>
<tag name="@see">#getUnsolicitedReceiveHandler</tag>
<tag name="@see">#setUnsolicitedReceiveHandler</tag>
</property>

<property name="interCharacterDelay" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;interCharacterDelay&lt;/code&gt; property.&#xa; issue 20408: Add intercharacter delay property&#xa; delay between transmitted characters during for all except reload operations
</description>
<tag name="@see">#getInterCharacterDelay</tag>
<tag name="@see">#setInterCharacterDelay</tag>
</property>

<property name="dom0ReloadInterCharacterDelay" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;dom0ReloadInterCharacterDelay&lt;/code&gt; property.&#xa; issue 20451: Separate Intercharacter delays required for reloads&#xa; delay between transmitted characters while reloading the main panel
</description>
<tag name="@see">#getDom0ReloadInterCharacterDelay</tag>
<tag name="@see">#setDom0ReloadInterCharacterDelay</tag>
</property>

<property name="lcuReloadInterCharacterDelay" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;lcuReloadInterCharacterDelay&lt;/code&gt; property.&#xa; issue 20451: Separate Intercharacter delays required for reloads&#xa; delay between transmitted characters while reloading LCU panels
</description>
<tag name="@see">#getLcuReloadInterCharacterDelay</tag>
<tag name="@see">#setLcuReloadInterCharacterDelay</tag>
</property>

<property name="reloadInterLineDelay" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;reloadInterLineDelay&lt;/code&gt; property.&#xa; Puts  a pause at the end of sending a reload line to a controller
</description>
<tag name="@see">#getReloadInterLineDelay</tag>
<tag name="@see">#setReloadInterLineDelay</tag>
</property>

<property name="lowLevelTrace" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;lowLevelTrace&lt;/code&gt; property.&#xa; If trace is turned on, and this is true than rx/tx bytes will be&#xa; logged to the ac256 log
</description>
<tag name="@see">#getLowLevelTrace</tag>
<tag name="@see">#setLowLevelTrace</tag>
</property>

<action name="submitConsoleJob" flags="hc">
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;submitConsoleJob&lt;/code&gt; action.&#xa; start a console session with special console view
</description>
<tag name="@see">#submitConsoleJob()</tag>
</action>

<topic name="terminalUpdated" flags="">
<eventType>
<type class="javax.baja.sys.BValue"/>
</eventType><description>
Slot for the &lt;code&gt;terminalUpdated&lt;/code&gt; topic.
</description>
<tag name="@see">#fireTerminalUpdated</tag>
</topic>

</class>
</bajadoc>
