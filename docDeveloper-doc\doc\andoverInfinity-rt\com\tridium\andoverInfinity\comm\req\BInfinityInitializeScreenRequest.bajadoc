<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.req.BInfinityInitializeScreenRequest" name="BInfinityInitializeScreenRequest" packageName="com.tridium.andoverInfinity.comm.req" public="true">
<description>
BInfinityInitializeScreenRequest sends a &#x22;\n&#x22; + CTL-Z to force&#xa; the infinity panel to resend all lines.  If no response, then a&#xa; logon sequence is initiated.
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.comm.req.BDdfRequest"/>
</extends>
<implements>
<type class="com.tridium.ddf.comm.req.BIDdfCustomRequest"/>
</implements>
<implements>
<type class="com.tridium.andoverInfinity.comm.Vt100Const"/>
</implements>
<implements>
<type class="com.tridium.andoverInfinity.comm.req.RequiresNetworkAccess"/>
</implements>
<!-- com.tridium.andoverInfinity.comm.req.BInfinityInitializeScreenRequest() -->
<constructor name="BInfinityInitializeScreenRequest" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityInitializeScreenRequest(com.tridium.andoverInfinity.BInfinityNetwork) -->
<constructor name="BInfinityInitializeScreenRequest" public="true">
<parameter name="network">
<type class="com.tridium.andoverInfinity.BInfinityNetwork"/>
</parameter>
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityInitializeScreenRequest.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityInitializeScreenRequest.setNetwork(com.tridium.andoverInfinity.BInfinityNetwork) -->
<method name="setNetwork"  public="true">
<description>
Implementation of RequiresNetworkAccess interface
</description>
<tag name="@see">com.tridium.andoverInfinity.comm.req.RequiresNetworkAccess#setNetwork(com.tridium.andoverInfinity.BInfinityNetwork)</tag>
<parameter name="network">
<type class="com.tridium.andoverInfinity.BInfinityNetwork"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityInitializeScreenRequest.processReceive(com.tridium.ddf.comm.IDdfDataFrame) -->
<method name="processReceive"  public="true">
<description/>
<parameter name="iDevDataFrame">
<type class="com.tridium.ddf.comm.IDdfDataFrame"/>
</parameter>
<return>
<type class="com.tridium.ddf.comm.rsp.BIDdfResponse"/>
</return>
<throws>
<type class="com.tridium.ddf.comm.rsp.DdfResponseException"/>
</throws>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityInitializeScreenRequest.toByteArray() -->
<method name="toByteArray"  public="true">
<description>
This is the first message sent on station startup.  Start by sending a&#xa; &#x22;\r&#x22; to get out of a dialog box that may be open, followed  by a CTL-Z&#xa; to cause the infinity panel to send back its entire screen buffer.
</description>
<tag name="@see">com.tridium.devDriver.comm.req.BIDdfRequest#toByteArray()</tag>
<return>
<type class="byte" dimension="1"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityInitializeScreenRequest.processErrorResponse(com.tridium.ddf.comm.rsp.DdfResponseException) -->
<method name="processErrorResponse"  public="true">
<description/>
<parameter name="errorRsp">
<type class="com.tridium.ddf.comm.rsp.DdfResponseException"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityInitializeScreenRequest.processLateResponse(com.tridium.ddf.comm.rsp.BIDdfResponse) -->
<method name="processLateResponse"  public="true">
<description/>
<parameter name="devRsp">
<type class="com.tridium.ddf.comm.rsp.BIDdfResponse"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityInitializeScreenRequest.processResponse(com.tridium.ddf.comm.rsp.BIDdfResponse) -->
<method name="processResponse"  public="true">
<description>
Implemented BIDdfCustomResponse so that we could set a flag on the &#xa; network to indicate we were able to initialize the screen.
</description>
<tag name="@see">com.tridium.devDriver.comm.req.BIDdfCustomRequest#processResponse(com.tridium.devDriver.comm.rsp.BIDevResponse)</tag>
<parameter name="devRsp">
<type class="com.tridium.ddf.comm.rsp.BIDdfResponse"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityInitializeScreenRequest.processTimeout() -->
<method name="processTimeout"  public="true">
<description>
If we were un-successful at initializing the screen, then perhaps we &#xa; need to log in.  Send a BInfinityLogonSequenceRequest.
</description>
<tag name="@see">com.tridium.devDriver.comm.req.BIDdfCustomRequest#processTimeout()</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityInitializeScreenRequest.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
