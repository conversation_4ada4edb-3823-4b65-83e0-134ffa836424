<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum" name="BInfinitySendDisableEnum" packageName="com.tridium.andoverInfinity.enums" public="true" final="true">
<description>
BInfinitySendDisableEnum
</description>
<tag name="@author">C<PERSON><PERSON></tag>
<tag name="@creation">9/30/2004 10:56AM</tag>
<tag name="@version">$Revision$ $Date:9/30/2004 10:56AM$</tag>
<tag name="@since">Niagara 3.0 andoverAC256 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;never&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>0</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;onEveryWrite&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>1</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;onlyOnFirstWrite&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>2</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum.NEVER -->
<field name="NEVER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for never.
</description>
</field>

<!-- com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum.ON_EVERY_WRITE -->
<field name="ON_EVERY_WRITE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for onEveryWrite.
</description>
</field>

<!-- com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum.ONLY_ON_FIRST_WRITE -->
<field name="ONLY_ON_FIRST_WRITE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for onlyOnFirstWrite.
</description>
</field>

<!-- com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum.never -->
<field name="never"  public="true" static="true" final="true">
<type class="com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum"/>
<description>
BInfinitySendDisableEnum constant for never.
</description>
</field>

<!-- com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum.onEveryWrite -->
<field name="onEveryWrite"  public="true" static="true" final="true">
<type class="com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum"/>
<description>
BInfinitySendDisableEnum constant for onEveryWrite.
</description>
</field>

<!-- com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum.onlyOnFirstWrite -->
<field name="onlyOnFirstWrite"  public="true" static="true" final="true">
<type class="com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum"/>
<description>
BInfinitySendDisableEnum constant for onlyOnFirstWrite.
</description>
</field>

<!-- com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
