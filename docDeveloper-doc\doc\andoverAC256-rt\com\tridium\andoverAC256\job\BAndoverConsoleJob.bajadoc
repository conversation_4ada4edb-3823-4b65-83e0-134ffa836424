<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.job.BAndoverConsoleJob" name="BAndoverConsoleJob" packageName="com.tridium.andoverAC256.job" public="true">
<description>
BAndoverConsoleJob sends commands from an&#xa; AndoverConole to the com port and routes&#xa; responses back to the AndoverConsole.&#xa; &lt;p&gt;
</description>
<tag name="@author">Cli<PERSON></tag>
<tag name="@creation">6/7/2005 3:32PM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.84</tag>
<extends>
<type class="javax.baja.job.BJob"/>
</extends>
<implements>
<type class="com.tridium.andoverAC256.messages.AndoverMessageConst"/>
</implements>
<implements>
<type class="java.lang.Runnable"/>
</implements>
<action name="setCommand" flags="">
<parameter name="parameter">
<type class="javax.baja.sys.BString"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;setCommand&lt;/code&gt; action.
</description>
<tag name="@see">#setCommand(BString parameter)</tag>
</action>

<topic name="responseFragment" flags="">
<eventType>
<type class="javax.baja.sys.BValue"/>
</eventType><description>
Slot for the &lt;code&gt;responseFragment&lt;/code&gt; topic.
</description>
<tag name="@see">#fireResponseFragment</tag>
</topic>

</class>
</bajadoc>
