<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.collection.PropertyMap$PropertySet" name="PropertyMap.PropertySet" packageName="javax.baja.collection" public="true" static="true" innerClass="true">
<description/>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<parameterizedType class="java.util.Set">
<args>
<type class="javax.baja.sys.Property"/>
</args>
</parameterizedType>
</implements>
<!-- javax.baja.collection.PropertyMap.PropertySet(javax.baja.sys.BComplex) -->
<constructor name="PropertySet" public="true">
<parameter name="obj">
<type class="javax.baja.sys.BComplex"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.collection.PropertyMap.PropertySet.add(javax.baja.sys.Property) -->
<method name="add"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.PropertySet.addAll(java.util.Collection&lt;? extends javax.baja.sys.Property&gt;) -->
<method name="addAll"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="c">
<parameterizedType class="java.util.Collection">
<args>
<wildcardType class="?">
<bounds kind="extends">
<type class="javax.baja.sys.Property"/>
</bounds>
</wildcardType>
</args>
</parameterizedType>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.PropertySet.clear() -->
<method name="clear"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.PropertySet.remove(java.lang.Object) -->
<method name="remove"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="o">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.PropertySet.removeAll(java.util.Collection&lt;?&gt;) -->
<method name="removeAll"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="c">
<parameterizedType class="java.util.Collection">
<args>
<wildcardType class="?">
</wildcardType>
</args>
</parameterizedType>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.PropertySet.retainAll(java.util.Collection&lt;?&gt;) -->
<method name="retainAll"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="c">
<parameterizedType class="java.util.Collection">
<args>
<wildcardType class="?">
</wildcardType>
</args>
</parameterizedType>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.PropertySet.contains(java.lang.Object) -->
<method name="contains"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="o">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.PropertySet.containsAll(java.util.Collection&lt;?&gt;) -->
<method name="containsAll"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="c">
<parameterizedType class="java.util.Collection">
<args>
<wildcardType class="?">
</wildcardType>
</args>
</parameterizedType>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.PropertySet.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description/>
<parameter name="o">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.PropertySet.hashCode() -->
<method name="hashCode"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.PropertySet.isEmpty() -->
<method name="isEmpty"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.PropertySet.size() -->
<method name="size"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.PropertySet.toArray() -->
<method name="toArray"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.Object" dimension="1"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.PropertySet.&lt;T&gt;toArray(T[]) -->
<method name="toArray"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<typeParameters>
<typeVariable class="T">
</typeVariable>
</typeParameters>
<description/>
<parameter name="a">
<type class="java.lang.T" dimension="1"/>
</parameter>
<return>
<type class="java.lang.T" dimension="1"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.PropertySet.iterator() -->
<method name="iterator"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<parameterizedType class="java.util.Iterator">
<args>
<type class="javax.baja.sys.Property"/>
</args>
</parameterizedType>
</return>
</method>

</class>
</bajadoc>
