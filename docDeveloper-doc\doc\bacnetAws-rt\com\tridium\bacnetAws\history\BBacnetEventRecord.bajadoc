<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="com.tridium.bacnetAws.history.BBacnetEventRecord" name="BBacnetEventRecord" packageName="com.tridium.bacnetAws.history" public="true">
<description>
&lt;code&gt;BBacnetEventRecord&lt;/code&gt; is a Bacnet event record
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">26 May 2010</tag>
<tag name="@version">$Revision: 1$ $Date: 8/11/2003 1:54:12 PM$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.history.BBacnetTrendRecord"/>
</extends>
<property name="processId" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;processId&lt;/code&gt; property.
</description>
<tag name="@see">#getProcessId</tag>
<tag name="@see">#setProcessId</tag>
</property>

<property name="initiatingDevice" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;initiatingDevice&lt;/code&gt; property.
</description>
<tag name="@see">#getInitiatingDevice</tag>
<tag name="@see">#setInitiatingDevice</tag>
</property>

<property name="eventObject" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;eventObject&lt;/code&gt; property.
</description>
<tag name="@see">#getEventObject</tag>
<tag name="@see">#setEventObject</tag>
</property>

<property name="timeStamp" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;timeStamp&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeStamp</tag>
<tag name="@see">#setTimeStamp</tag>
</property>

<property name="notificationClass" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;notificationClass&lt;/code&gt; property.
</description>
<tag name="@see">#getNotificationClass</tag>
<tag name="@see">#setNotificationClass</tag>
</property>

<property name="priority" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;priority&lt;/code&gt; property.
</description>
<tag name="@see">#getPriority</tag>
<tag name="@see">#setPriority</tag>
</property>

<property name="eventType" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;eventType&lt;/code&gt; property.
</description>
<tag name="@see">#getEventType</tag>
<tag name="@see">#setEventType</tag>
</property>

<property name="messageText" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;messageText&lt;/code&gt; property.
</description>
<tag name="@see">#getMessageText</tag>
<tag name="@see">#setMessageText</tag>
</property>

<property name="notifyType" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;notifyType&lt;/code&gt; property.
</description>
<tag name="@see">#getNotifyType</tag>
<tag name="@see">#setNotifyType</tag>
</property>

<property name="ackRequired" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;ackRequired&lt;/code&gt; property.
</description>
<tag name="@see">#getAckRequired</tag>
<tag name="@see">#setAckRequired</tag>
</property>

<property name="fromState" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;fromState&lt;/code&gt; property.
</description>
<tag name="@see">#getFromState</tag>
<tag name="@see">#setFromState</tag>
</property>

<property name="toState" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;toState&lt;/code&gt; property.
</description>
<tag name="@see">#getToState</tag>
<tag name="@see">#setToState</tag>
</property>

<property name="eventValues" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;eventValues&lt;/code&gt; property.
</description>
<tag name="@see">#getEventValues</tag>
<tag name="@see">#setEventValues</tag>
</property>

</class>
</bajadoc>
