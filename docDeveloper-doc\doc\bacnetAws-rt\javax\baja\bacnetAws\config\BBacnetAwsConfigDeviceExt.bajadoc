<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="javax.baja.bacnetAws.config.BBacnetAwsConfigDeviceExt" name="BBacnetAwsConfigDeviceExt" packageName="javax.baja.bacnetAws.config" public="true">
<description>
BBacnetConfigDeviceExt represents the configuration representation of a&#xa; Bacnet Aws device.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">07 June 2010</tag>
<tag name="@creation">Jun 7, 2010</tag>
<extends>
<type class="javax.baja.bacnet.config.BBacnetConfigDeviceExt"/>
</extends>
<!-- javax.baja.bacnetAws.config.BBacnetAwsConfigDeviceExt() -->
<constructor name="BBacnetAwsConfigDeviceExt" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnetAws.config.BBacnetAwsConfigDeviceExt.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAwsConfigDeviceExt.getAgents(javax.baja.sys.Context) -->
<method name="getAgents"  public="true">
<description>
Get the agent list.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.agent.AgentList"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAwsConfigDeviceExt.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
