<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.datatypes.BWriteFileConfig" name="BWriteFileConfig" packageName="com.tridium.bacnet.datatypes" public="true">
<description>
This class file specifies parameters to constrain a&#xa; read file request.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">13 Aug 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<property name="remoteStart" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;remoteStart&lt;/code&gt; property.&#xa; This is the starting byte position in the remote file for&#xa; stream-access files, and the starting record number in the&#xa; remote file for record-access files.
</description>
<tag name="@see">#getRemoteStart</tag>
<tag name="@see">#setRemoteStart</tag>
</property>

<property name="localStart" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;localStart&lt;/code&gt; property.&#xa; This is the starting byte position in the local file for&#xa; stream-access files, and the starting record number in the&#xa; local file for record-access files.
</description>
<tag name="@see">#getLocalStart</tag>
<tag name="@see">#setLocalStart</tag>
</property>

</class>
</bajadoc>
