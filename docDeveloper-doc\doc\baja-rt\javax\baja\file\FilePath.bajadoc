<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.FilePath" name="FilePath" packageName="javax.baja.file" public="true">
<description>
FilePath is a specialization of OrdScheme for file queries.  &#xa; The file body BNF is:&#xa; &lt;pre&gt;&lt;code&gt;&amp;#xa;   file      := path [fragment]&amp;#xa;   path      := absolute | relative&amp;#xa;   fragment  := &amp;#x22;#&amp;#x22; name&amp;#xa;&amp;#xa;   absolute     := authorityAbs | localAbs | homeAbs&amp;#xa;   authorityAbs := &amp;#x22;//&amp;#x22; authority &amp;#x22;/&amp;#x22; [names]&amp;#xa;   localAbs     := &amp;#x22;/&amp;#x22; [names]&amp;#xa;   sysHomeAbs   := &amp;#x22;!&amp;#x22; [names]&amp;#xa;   userHomeAbs  := &amp;#x22;~&amp;#x22; [names]&amp;#xa;   stationHomeAbs  := &amp;#x22;^&amp;#x22; [names]&amp;#xa;   protectedStationHomeAbs  := &amp;#x22;^^&amp;#x22; [names]&amp;#xa;&amp;#xa;   relative  := backup | dirRel&amp;#xa;   rel       := dirRel&amp;#xa;   backup    := ( &amp;#x22;../&amp;#x22; )* path&amp;#xa;&amp;#xa;   names     := names [ &amp;#x22;/&amp;#x22; path ]&amp;#xa;   name      := nameChar (nameChar)*&amp;#xa;   nameChar  := (a-z) | (A-Z) | (0-9) | specials&amp;#xa;   specials  := space | . | : | - | _ | $ | + | ( | ) | &amp;amp; | ` | &amp;#x27; | @ | [ | ]&amp;#xa; &amp;#xa; &lt;/code&gt;&lt;/pre&gt;
</description>
<tag name="@author">Brian Frank</tag>
<tag name="@creation">3 Jan 03</tag>
<tag name="@version">$Revision: 29$ $Date: 10/19/06 3:21:46 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="javax.baja.naming.OrdQuery"/>
</implements>
<implements>
<type class="javax.baja.naming.Path"/>
</implements>
<implements>
<parameterizedType class="java.lang.Comparable">
<args>
<type class="javax.baja.file.FilePath"/>
</args>
</parameterizedType>
</implements>
<!-- javax.baja.file.FilePath(java.lang.String, java.lang.String) -->
<constructor name="FilePath" public="true">
<parameter name="scheme">
<type class="java.lang.String"/>
</parameter>
<parameter name="body">
<type class="java.lang.String"/>
</parameter>
<throws>
<type class="javax.baja.naming.SyntaxException"/>
<description>
if the body isn&#x27;t a valid file path.
</description>
</throws>
<description>
Construct an FilePath with the specified scheme and body.
</description>
</constructor>

<!-- javax.baja.file.FilePath(java.lang.String) -->
<constructor name="FilePath" public="true">
<parameter name="body">
<type class="java.lang.String"/>
</parameter>
<throws>
<type class="javax.baja.naming.SyntaxException"/>
</throws>
<description>
Convenience with &#x22;file&#x22; scheme.
</description>
</constructor>

<!-- javax.baja.file.FilePath.newInstance(java.lang.String) -->
<method name="newInstance"  protected="true">
<description/>
<parameter name="body">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.file.FilePath"/>
</return>
</method>

<!-- javax.baja.file.FilePath.makePath(java.lang.String) -->
<method name="makePath"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<tag name="@since">Niagara 4.3U1</tag>
<parameter name="body">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdQuery"/>
</return>
</method>

<!-- javax.baja.file.FilePath.isAbsolute() -->
<method name="isAbsolute"  public="true">
<description>
Return true if this file path is authority absolute, &#xa; local absolute, or home absolute.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.FilePath.isRelative() -->
<method name="isRelative"  public="true">
<description>
Return inverse of isAbsolute.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.FilePath.getAbsoluteMode() -->
<method name="getAbsoluteMode"  public="true">
<description>
Return one of the absolute mode constants or &#xa; RELATIVE if this file path is not absolute.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.file.FilePath.isAuthorityAbsolute() -->
<method name="isAuthorityAbsolute"  public="true">
<description>
Convenience for &lt;code&gt;getAbsoluteMode() == AUTHORITY_ABSOLUTE&lt;/code&gt;.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.FilePath.isLocalAbsolute() -->
<method name="isLocalAbsolute"  public="true">
<description>
Convenience for &lt;code&gt;getAbsoluteMode() == LOCAL_ABSOLUTE&lt;/code&gt;.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.FilePath.isSysHomeAbsolute() -->
<method name="isSysHomeAbsolute"  public="true">
<description>
Convenience for &lt;code&gt;getAbsoluteMode() == SYS_HOME_ABSOLUTE&lt;/code&gt;.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.FilePath.isUserHomeAbsolute() -->
<method name="isUserHomeAbsolute"  public="true">
<description>
Convenience for &lt;code&gt;getAbsoluteMode() == USER_HOME_ABSOLUTE&lt;/code&gt;.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.FilePath.isStationHomeAbsolute() -->
<method name="isStationHomeAbsolute"  public="true">
<description>
Convenience for &lt;code&gt;getAbsoluteMode() == STATION_HOME_ABSOLUTE&lt;/code&gt;.&#xa; Only applicable for station VMs.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.FilePath.isProtectedStationHomeAbsolute() -->
<method name="isProtectedStationHomeAbsolute"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.FilePath.getAbsoluteBase() -->
<method name="getAbsoluteBase"  public="true">
<description>
If this path is:&#xa; &lt;ul&gt;&#xa; &lt;li&gt;authority absolute: return &lt;code&gt;&#x22;//&#x22; + authority &#x22;/&#x22;&lt;/code&gt;&lt;/li&gt;&#xa; &lt;li&gt;local absolute: return &lt;code&gt;&#x22;/&#x22;&lt;/code&gt;&lt;/li&gt;&#xa; &lt;li&gt;sys home absolute: return &lt;code&gt;&#x22;!&#x22;&lt;/code&gt;&lt;/li&gt;&#xa; &lt;li&gt;station home absolute: return &lt;code&gt;&#x22;^&#x22;&lt;/code&gt;&lt;/li&gt;&#xa; &lt;li&gt;relative: return &lt;code&gt;&#x22;&#x22;&lt;/code&gt;&lt;/li&gt;&#xa; &lt;/ul&gt;
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.FilePath.getAuthority() -->
<method name="getAuthority"  public="true">
<description>
Return the authority name if this a AUTHORITY_ABSOLUTE &#xa; path, otherwise return null.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.FilePath.getFragment() -->
<method name="getFragment"  public="true">
<description>
Get the fragment name or return null if not found.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.FilePath.getName() -->
<method name="getName"  public="true">
<description>
Get the simple name of the file.  This is the last &#xa; name in the path.  Or if the path length is zero&#xa; return return &lt;code&gt;getAbsoluteBase()&lt;/code&gt;.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.FilePath.getBackupDepth() -->
<method name="getBackupDepth"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the number of leading &#x22;../&#x22; indicating a &#xa; relative backup.  If this path is absolute or&#xa; directory relative then return 0.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.file.FilePath.depth() -->
<method name="depth"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the number of names in the path after the&#xa; absolute or relative backup prefix.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.file.FilePath.nameAt(int) -->
<method name="nameAt"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the name at the zero based index between 0 and depth()-1.
</description>
<parameter name="depth">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.FilePath.getNames() -->
<method name="getNames"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get a copy of the names array.
</description>
<return>
<type class="java.lang.String" dimension="1"/>
</return>
</method>

<!-- javax.baja.file.FilePath.getParentPath() -->
<method name="getParentPath"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Convenience for &lt;code&gt;getParent()&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.naming.Path"/>
</return>
</method>

<!-- javax.baja.file.FilePath.getParent() -->
<method name="getParent"  public="true">
<description>
Return a FilePath for the parent path or null if &#xa; there is no parent.
</description>
<return>
<type class="javax.baja.file.FilePath"/>
</return>
</method>

<!-- javax.baja.file.FilePath.isValidName(java.lang.String) -->
<method name="isValidName"  public="true" static="true">
<description>
Does the specified string contain a file name.
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.FilePath.verifyValidName(java.lang.String) -->
<method name="verifyValidName"  public="true" static="true">
<description>
If the specified name is invalid then throw an IllegalNameException.
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.file.FilePath.getScheme() -->
<method name="getScheme"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return the scheme field.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.FilePath.getBody() -->
<method name="getBody"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return the body field.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.FilePath.isHost() -->
<method name="isHost"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return false.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.FilePath.isSession() -->
<method name="isSession"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return false.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.FilePath.normalize(javax.baja.naming.OrdQueryList, int) -->
<method name="normalize"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
If the query at index+1 is also a FilePath, then perform &#xa; a merge using the &lt;code&gt;merge()&lt;/code&gt; method.
</description>
<parameter name="list">
<type class="javax.baja.naming.OrdQueryList"/>
</parameter>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.file.FilePath.merge(java.lang.String) -->
<method name="merge"  public="true">
<description>
Convenience for &lt;code&gt;merge(new FilePath(getScheme(), a))&lt;/code&gt;.
</description>
<parameter name="a">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.file.FilePath"/>
</return>
</method>

<!-- javax.baja.file.FilePath.merge(javax.baja.file.FilePath) -->
<method name="merge"  public="true">
<description>
Merge this path with the specified path.  If the specified&#xa; path is absolute, then return it since it trumps this one.&#xa; If the specified path is relative then create a new merged &#xa; path taking in account any backup.  If the specified path&#xa; has a fragment, then the merged path will contain the&#xa; fragment too.
</description>
<parameter name="a">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.FilePath"/>
</return>
</method>

<!-- javax.baja.file.FilePath.toString() -->
<method name="toString"  public="true">
<description>
Return &lt;code&gt;scheme + &#x22;:&#x22; + body&lt;/code&gt;.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.FilePath.hashCode() -->
<method name="hashCode"  public="true">
<description>
Returns a hash code value for the file path string. Should not&#xa; be used to uniquely identify the path&#x27;s target file.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.file.FilePath.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description>
Provide a textual comparison of the file path returning true&#xa; if the provided FilePath matches the current object. The returned&#xa; value does not indicate whether the two paths resolve to the &#xa; same file.
</description>
<parameter name="other">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.FilePath.compareTo(javax.baja.file.FilePath) -->
<method name="compareTo"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Provide a textual comparison of the file path.  Compares this &#xa; object with the specified object for order. Returns a negative &#xa; integer, zero, or a positive integer as this object is less than, &#xa; equal to, or greater than the specified object.
</description>
<parameter name="other">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.file.FilePath.RELATIVE -->
<field name="RELATIVE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.file.FilePath.AUTHORITY_ABSOLUTE -->
<field name="AUTHORITY_ABSOLUTE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.file.FilePath.LOCAL_ABSOLUTE -->
<field name="LOCAL_ABSOLUTE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.file.FilePath.SYS_HOME_ABSOLUTE -->
<field name="SYS_HOME_ABSOLUTE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.file.FilePath.STATION_HOME_ABSOLUTE -->
<field name="STATION_HOME_ABSOLUTE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.file.FilePath.USER_HOME_ABSOLUTE -->
<field name="USER_HOME_ABSOLUTE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.file.FilePath.PROTECTED_STATION_HOME_ABSOLUTE -->
<field name="PROTECTED_STATION_HOME_ABSOLUTE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
