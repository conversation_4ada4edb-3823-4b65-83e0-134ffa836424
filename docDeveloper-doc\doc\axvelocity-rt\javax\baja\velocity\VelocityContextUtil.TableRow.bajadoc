<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="axvelocity" runtimeProfile="rt" qualifiedName="javax.baja.velocity.VelocityContextUtil$TableRow" name="VelocityContextUtil.TableRow" packageName="javax.baja.velocity" public="true" final="true" innerClass="true">
<description/>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- javax.baja.velocity.VelocityContextUtil.TableRow.get(java.lang.String) -->
<method name="get"  public="true">
<description/>
<parameter name="colName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="java.lang.Object"/>
</return>
</method>

<!-- javax.baja.velocity.VelocityContextUtil.TableRow.getDisplay(java.lang.String) -->
<method name="getDisplay"  public="true">
<description/>
<parameter name="colName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="java.lang.Object"/>
</return>
</method>

</class>
</bajadoc>
