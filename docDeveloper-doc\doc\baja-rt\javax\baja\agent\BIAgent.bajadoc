<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.agent.BIAgent" name="BIAgent" packageName="javax.baja.agent" public="true" interface="true" abstract="true" category="interface">
<description>
BIAgent is the interface implemented by agent types.  An&#xa; agent is a special BObject type that provides services for&#xa; other BObject types.  Agents are registered on their target&#xa; types via the module manifest and queried via the Registry&#xa; interface.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">23 Dec 02</tag>
<tag name="@version">$Revision: 1$ $Date: 12/23/02 8:14:11 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<!-- javax.baja.agent.BIAgent.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
