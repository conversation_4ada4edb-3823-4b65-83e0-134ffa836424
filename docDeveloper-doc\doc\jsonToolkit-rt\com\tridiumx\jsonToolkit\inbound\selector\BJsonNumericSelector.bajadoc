<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.selector.BJsonNumericSelector" name="BJsonNumericSelector" packageName="com.tridiumx.jsonToolkit.inbound.selector" public="true" abstract="true">
<description>
Numeric Selectors have a double out slot
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.inbound.selector.BJsonSelector"/>
</extends>
<property name="out" flags="rst">
<type class="double"/>
<description>
Slot for the &lt;code&gt;out&lt;/code&gt; property.
</description>
<tag name="@see">#getOut</tag>
<tag name="@see">#setOut</tag>
</property>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonNumericSelector() -->
<constructor name="BJsonNumericSelector" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonNumericSelector.getOut() -->
<method name="getOut"  public="true">
<description>
Get the &lt;code&gt;out&lt;/code&gt; property.
</description>
<tag name="@see">#out</tag>
<return>
<type class="double"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonNumericSelector.setOut(double) -->
<method name="setOut"  public="true">
<description>
Set the &lt;code&gt;out&lt;/code&gt; property.
</description>
<tag name="@see">#out</tag>
<parameter name="v">
<type class="double"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonNumericSelector.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonNumericSelector.getOutProperty() -->
<method name="getOutProperty"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Property"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonNumericSelector.out -->
<field name="out"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;out&lt;/code&gt; property.
</description>
<tag name="@see">#getOut</tag>
<tag name="@see">#setOut</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonNumericSelector.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
