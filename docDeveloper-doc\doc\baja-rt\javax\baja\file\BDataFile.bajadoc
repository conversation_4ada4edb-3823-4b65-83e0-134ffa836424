<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BDataFile" name="BDataFile" packageName="javax.baja.file" public="true">
<description>
BDataFile is a BIFile used when no specified file &#xa; type is known.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">24 Jan 03</tag>
<tag name="@version">$Revision: 7$ $Date: 3/28/05 9:22:55 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.file.BAbstractFile"/>
</extends>
<implements>
<type class="javax.baja.file.BIDataFile"/>
</implements>
<!-- javax.baja.file.BDataFile(javax.baja.file.BIFileStore) -->
<constructor name="BDataFile" public="true">
<parameter name="store">
<type class="javax.baja.file.BIFileStore"/>
</parameter>
<description>
Construct a file with the specified store.
</description>
</constructor>

<!-- javax.baja.file.BDataFile() -->
<constructor name="BDataFile" public="true">
<description>
Construct (must call setStore()).
</description>
</constructor>

<!-- javax.baja.file.BDataFile.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.file.BDataFile.getNavDescription(javax.baja.sys.Context) -->
<method name="getNavDescription"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get a short description.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.BDataFile.hasNavChildren() -->
<method name="hasNavChildren"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return false.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BDataFile.getNavChild(java.lang.String) -->
<method name="getNavChild"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return null
</description>
<parameter name="navName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.nav.BINavNode"/>
</return>
</method>

<!-- javax.baja.file.BDataFile.getNavChildren() -->
<method name="getNavChildren"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get empty array.
</description>
<return>
<type class="javax.baja.nav.BINavNode" dimension="1"/>
</return>
</method>

<!-- javax.baja.file.BDataFile.getAgents(javax.baja.sys.Context) -->
<method name="getAgents"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.agent.AgentList"/>
</return>
</method>

<!-- javax.baja.file.BDataFile.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.file.BDataFile.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
