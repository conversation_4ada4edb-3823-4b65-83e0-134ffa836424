<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.point.BCvOverrideAction" name="BCvOverrideAction" packageName="com.tridium.aapup.point" public="true">
<description>
BCvOverrideAction is used to adjust the override value&#xa; of CVs for inputs.
</description>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@creation">9/9/2005 10:19AM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.93</tag>
<extends>
<type class="javax.baja.sys.BAction"/>
</extends>
<property name="parameterTypeSpec" flags="hr">
<type class="javax.baja.util.BTypeSpec"/>
<description>
Slot for the &lt;code&gt;parameterTypeSpec&lt;/code&gt; property.
</description>
<tag name="@see">#getParameterTypeSpec</tag>
<tag name="@see">#setParameterTypeSpec</tag>
</property>

</class>
</bajadoc>
