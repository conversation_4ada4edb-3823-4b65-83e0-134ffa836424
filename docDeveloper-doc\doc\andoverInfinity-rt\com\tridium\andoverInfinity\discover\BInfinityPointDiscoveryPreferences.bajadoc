<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryPreferences" name="BInfinityPointDiscoveryPreferences" packageName="com.tridium.andoverInfinity.discover" public="true">
<description>
BInfinityPointDiscoveryPreferences
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 22, 2007</tag>
<tag name="@version">$Revision$ $May 22, 2007 9:21:03 AM$</tag>
<tag name="@since"/>
<extends>
<type class="com.tridium.ddf.discover.auto.BDdfAutoDiscoveryPreferences"/>
</extends>
<property name="pointDiscoveryPageTimeout" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;pointDiscoveryPageTimeout&lt;/code&gt; property.&#xa; This is the amount of time to wait for the network controller to return 1 page worth&#xa; of points after performing a page-down
</description>
<tag name="@see">#getPointDiscoveryPageTimeout</tag>
<tag name="@see">#setPointDiscoveryPageTimeout</tag>
</property>

<!-- com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryPreferences() -->
<constructor name="BInfinityPointDiscoveryPreferences" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryPreferences.getPointDiscoveryPageTimeout() -->
<method name="getPointDiscoveryPageTimeout"  public="true">
<description>
Get the &lt;code&gt;pointDiscoveryPageTimeout&lt;/code&gt; property.&#xa; This is the amount of time to wait for the network controller to return 1 page worth&#xa; of points after performing a page-down
</description>
<tag name="@see">#pointDiscoveryPageTimeout</tag>
<return>
<type class="javax.baja.sys.BRelTime"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryPreferences.setPointDiscoveryPageTimeout(javax.baja.sys.BRelTime) -->
<method name="setPointDiscoveryPageTimeout"  public="true">
<description>
Set the &lt;code&gt;pointDiscoveryPageTimeout&lt;/code&gt; property.&#xa; This is the amount of time to wait for the network controller to return 1 page worth&#xa; of points after performing a page-down
</description>
<tag name="@see">#pointDiscoveryPageTimeout</tag>
<parameter name="v">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryPreferences.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryPreferences.timeout -->
<field name="timeout"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;timeout&lt;/code&gt; property.&#xa; This is the amount of time to wait per field-bus request before timing out
</description>
<tag name="@see">#getTimeout</tag>
<tag name="@see">#setTimeout</tag>
</field>

<!-- com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryPreferences.pointDiscoveryPageTimeout -->
<field name="pointDiscoveryPageTimeout"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;pointDiscoveryPageTimeout&lt;/code&gt; property.&#xa; This is the amount of time to wait for the network controller to return 1 page worth&#xa; of points after performing a page-down
</description>
<tag name="@see">#getPointDiscoveryPageTimeout</tag>
<tag name="@see">#setPointDiscoveryPageTimeout</tag>
</field>

<!-- com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryPreferences.retryCount -->
<field name="retryCount"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;retryCount&lt;/code&gt; property.&#xa; This is the number of discovery field-message retransmissions&#xa; per request.
</description>
<tag name="@see">#getRetryCount</tag>
<tag name="@see">#setRetryCount</tag>
</field>

<!-- com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryPreferences.min -->
<field name="min"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;min&lt;/code&gt; property.&#xa; This is the id of the lowest device in your driver to attempt to&#xa; learn by default
</description>
<tag name="@see">#getMin</tag>
<tag name="@see">#setMin</tag>
</field>

<!-- com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryPreferences.max -->
<field name="max"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;max&lt;/code&gt; property.&#xa; This is the id of the highest device in your driver to attempt to&#xa; learn by default
</description>
<tag name="@see">#getMax</tag>
<tag name="@see">#setMax</tag>
</field>

<!-- com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryPreferences.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
