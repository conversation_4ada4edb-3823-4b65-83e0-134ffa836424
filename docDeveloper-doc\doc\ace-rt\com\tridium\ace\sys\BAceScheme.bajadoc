<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.sys.BAceScheme" name="BAceScheme" packageName="com.tridium.ace.sys" public="true">
<description>
BAceScheme is the portal from AceAppFile to AceSpace.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">17 Feb 17</tag>
<tag name="@since">Niagara 4.4</tag>
<extends>
<type class="javax.baja.naming.BOrdScheme"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraSingleton"/>
</annotation>
</class>
</bajadoc>
