<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.firewall.BIpProtocolEnum" name="BIpProtocolEnum" packageName="javax.baja.firewall" public="true" final="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@creation">11/20/2014</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tcp&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;udp&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tcpAndUdp&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.firewall.BIpProtocolEnum.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.firewall.BIpProtocolEnum"/>
</return>
</method>

<!-- javax.baja.firewall.BIpProtocolEnum.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.firewall.BIpProtocolEnum"/>
</return>
</method>

<!-- javax.baja.firewall.BIpProtocolEnum.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.firewall.BIpProtocolEnum.make(javax.baja.firewall.BIpProtocolEnum) -->
<method name="make"  public="true" static="true">
<description/>
<parameter name="protocol">
<type class="javax.baja.firewall.BIpProtocolEnum"/>
</parameter>
<return>
<type class="com.tridium.nre.firewall.IpProtocol"/>
</return>
</method>

<!-- javax.baja.firewall.BIpProtocolEnum.TCP -->
<field name="TCP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tcp.
</description>
</field>

<!-- javax.baja.firewall.BIpProtocolEnum.UDP -->
<field name="UDP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for udp.
</description>
</field>

<!-- javax.baja.firewall.BIpProtocolEnum.TCP_AND_UDP -->
<field name="TCP_AND_UDP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tcpAndUdp.
</description>
</field>

<!-- javax.baja.firewall.BIpProtocolEnum.tcp -->
<field name="tcp"  public="true" static="true" final="true">
<type class="javax.baja.firewall.BIpProtocolEnum"/>
<description>
BIpProtocolEnum constant for tcp.
</description>
</field>

<!-- javax.baja.firewall.BIpProtocolEnum.udp -->
<field name="udp"  public="true" static="true" final="true">
<type class="javax.baja.firewall.BIpProtocolEnum"/>
<description>
BIpProtocolEnum constant for udp.
</description>
</field>

<!-- javax.baja.firewall.BIpProtocolEnum.tcpAndUdp -->
<field name="tcpAndUdp"  public="true" static="true" final="true">
<type class="javax.baja.firewall.BIpProtocolEnum"/>
<description>
BIpProtocolEnum constant for tcpAndUdp.
</description>
</field>

<!-- javax.baja.firewall.BIpProtocolEnum.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.firewall.BIpProtocolEnum"/>
<description/>
</field>

<!-- javax.baja.firewall.BIpProtocolEnum.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
