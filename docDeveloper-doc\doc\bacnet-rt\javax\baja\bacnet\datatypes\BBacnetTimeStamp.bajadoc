<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetTimeStamp" name="BBacnetTimeStamp" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
This class represents the Bacnet Timestamp Choice.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 4$ $Date: 11/28/01 6:13:55 AM$</tag>
<tag name="@creation">26 Feb 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="choice" flags="h">
<type class="int"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</property>

<property name="time" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
<description>
Slot for the &lt;code&gt;time&lt;/code&gt; property.
</description>
<tag name="@see">#getTime</tag>
<tag name="@see">#setTime</tag>
</property>

<property name="sequenceNumber" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;sequenceNumber&lt;/code&gt; property.
</description>
<tag name="@see">#getSequenceNumber</tag>
<tag name="@see">#setSequenceNumber</tag>
</property>

<property name="dateTime" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
<description>
Slot for the &lt;code&gt;dateTime&lt;/code&gt; property.
</description>
<tag name="@see">#getDateTime</tag>
<tag name="@see">#setDateTime</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp() -->
<constructor name="BBacnetTimeStamp" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp(javax.baja.bacnet.datatypes.BBacnetTime) -->
<constructor name="BBacnetTimeStamp" public="true">
<parameter name="time">
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
<description/>
</parameter>
<description>
Time constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<constructor name="BBacnetTimeStamp" public="true">
<parameter name="sequenceNumber">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description/>
</parameter>
<description>
Sequence Number constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp(javax.baja.bacnet.datatypes.BBacnetDateTime) -->
<constructor name="BBacnetTimeStamp" public="true">
<parameter name="dateTime">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
<description/>
</parameter>
<description>
DateTime constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp(javax.baja.sys.BAbsTime) -->
<constructor name="BBacnetTimeStamp" public="true">
<parameter name="babsTime">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<description>
Create a BBacnetTimeStamp from a BAbsTime.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.getChoice() -->
<method name="getChoice"  public="true">
<description>
Get the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.setChoice(int) -->
<method name="setChoice"  public="true">
<description>
Set the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.getTime() -->
<method name="getTime"  public="true">
<description>
Get the &lt;code&gt;time&lt;/code&gt; property.
</description>
<tag name="@see">#time</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.setTime(javax.baja.bacnet.datatypes.BBacnetTime) -->
<method name="setTime"  public="true">
<description>
Set the &lt;code&gt;time&lt;/code&gt; property.
</description>
<tag name="@see">#time</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.getSequenceNumber() -->
<method name="getSequenceNumber"  public="true">
<description>
Get the &lt;code&gt;sequenceNumber&lt;/code&gt; property.
</description>
<tag name="@see">#sequenceNumber</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.setSequenceNumber(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setSequenceNumber"  public="true">
<description>
Set the &lt;code&gt;sequenceNumber&lt;/code&gt; property.
</description>
<tag name="@see">#sequenceNumber</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.getDateTime() -->
<method name="getDateTime"  public="true">
<description>
Get the &lt;code&gt;dateTime&lt;/code&gt; property.
</description>
<tag name="@see">#dateTime</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.setDateTime(javax.baja.bacnet.datatypes.BBacnetDateTime) -->
<method name="setDateTime"  public="true">
<description>
Set the &lt;code&gt;dateTime&lt;/code&gt; property.
</description>
<tag name="@see">#dateTime</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.getTimeStamp() -->
<method name="getTimeStamp"  public="true">
<description/>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.setTimeStamp(javax.baja.sys.BValue) -->
<method name="setTimeStamp"  public="true">
<description/>
<parameter name="ts">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.setTimeStamp(javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="setTimeStamp"  public="true">
<description/>
<parameter name="ts">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.toDebugString() -->
<method name="toDebugString"  public="true">
<description>
To String.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.fromText(java.lang.String) -->
<method name="fromText"  public="true" static="true">
<description>
Set the values from a &lt;code&gt;String&lt;/code&gt;.
</description>
<parameter name="text">
<type class="java.lang.String"/>
<description>
the &lt;code&gt;String&lt;/code&gt;.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetTimeStamp"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.toBAbsTime() -->
<method name="toBAbsTime"  public="true">
<description>
Return a BAbsTime equivalent to this BBacnetTimeStamp.&#xa; Note that some conversions need to be applied to handle the differences&#xa; between Bacnet date and time and Niagara date and time.
</description>
<return>
<type class="javax.baja.sys.BAbsTime"/>
<description>
a BAbsTime matching the timestamp value.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.fromBAbsTime(javax.baja.sys.BAbsTime) -->
<method name="fromBAbsTime"  public="true">
<description>
Set this BBacnetTimeStamp from a Niagara BAbsTime.&#xa; Note that some conversions need to be applied to handle the differences&#xa; between Bacnet date and time and Niagara date and time.
</description>
<parameter name="babsTime">
<type class="javax.baja.sys.BAbsTime"/>
<description>
the Niagara time.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.fromBAbsTime(javax.baja.sys.BAbsTime, int) -->
<method name="fromBAbsTime"  public="true">
<description>
Set thie BBacnetTimeStamp from a Niagara BAbsTime.&#xa; Note that some conversions need to be applied to handle the differences&#xa; between Bacnet date and time and Niagara date and time.
</description>
<parameter name="babsTime">
<type class="javax.baja.sys.BAbsTime"/>
<description>
the Niagara time.
</description>
</parameter>
<parameter name="timestampType">
<type class="int"/>
<description>
the desired timestamp type - cannot be SEQUENCE_NUMBER_TAG.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.encodeTimeStamp(javax.baja.sys.BAbsTime, javax.baja.bacnet.io.AsnOutput) -->
<method name="encodeTimeStamp"  public="true" static="true">
<description>
Write a BAbsTime to the output stream as a BBacnetTimeStamp,&#xa; using the DATE_TIME_TAG.
</description>
<parameter name="t">
<type class="javax.baja.sys.BAbsTime"/>
<description>
BAbsTime to be written
</description>
</parameter>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
AsnOutputStream to which the timestamp is written.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.encode(java.io.DataOutput) -->
<method name="encode"  public="true">
<description>
Serialize
</description>
<parameter name="out">
<type class="java.io.DataOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.decode(java.io.DataInput) -->
<method name="decode"  public="true">
<description>
Unserialized
</description>
<parameter name="in">
<type class="java.io.DataInput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.choice -->
<field name="choice"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.time -->
<field name="time"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;time&lt;/code&gt; property.
</description>
<tag name="@see">#getTime</tag>
<tag name="@see">#setTime</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.sequenceNumber -->
<field name="sequenceNumber"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;sequenceNumber&lt;/code&gt; property.
</description>
<tag name="@see">#getSequenceNumber</tag>
<tag name="@see">#setSequenceNumber</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.dateTime -->
<field name="dateTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;dateTime&lt;/code&gt; property.
</description>
<tag name="@see">#getDateTime</tag>
<tag name="@see">#setDateTime</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.TIME_TAG -->
<field name="TIME_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
BBacnetTimeStamp Asn Context Tags&#xa; See Bacnet Clause 21
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.SEQUENCE_NUMBER_TAG -->
<field name="SEQUENCE_NUMBER_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.DATE_TIME_TAG -->
<field name="DATE_TIME_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeStamp.MAX_SEQUENCE_NUMBER -->
<field name="MAX_SEQUENCE_NUMBER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
