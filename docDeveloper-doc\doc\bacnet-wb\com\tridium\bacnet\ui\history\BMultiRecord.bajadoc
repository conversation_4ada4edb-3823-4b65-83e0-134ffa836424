<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="wb" qualifiedName="com.tridium.bacnet.ui.history.BMultiRecord" name="BMultiRecord" packageName="com.tridium.bacnet.ui.history" public="true">
<description>
BMultiRecord is used to build a BMultiRecordTable. It contains all the &#xa; records for a particular time stamp.  It will not be added to the database.&#xa; It is only used when building a BBacnetTrendMultiView.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">Apr 22, 2010</tag>
<extends>
<type class="javax.baja.sys.BObject"/>
</extends>
</class>
</bajadoc>
