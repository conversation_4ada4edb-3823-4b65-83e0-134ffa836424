<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="andoverAC256" runtimeProfile="rt" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>AndoverAC256 Driver</description>
<package name="com.tridium.andoverAC256.datatypes"/>
<package name="com.tridium.andoverAC256.enums"/>
<package name="com.tridium.andoverAC256.job"/>
<package name="com.tridium.andoverAC256"/>
<package name="com.tridium.andoverAC256.comm"/>
<package name="com.tridium.andoverAC256.point"/>
<class packageName="com.tridium.andoverAC256.enums" name="BAndoverAcnetSpeedEnum"><description>BAndoverAcnetSpeedEnum represents the possible baud rate choices&#xa; for the Andover AC256 C-Port.</description></class>
<class packageName="com.tridium.andoverAC256.datatypes" name="BAndoverBackupConfig"><description>BAndoverBackupConfig holds data for the BAndoverBackupJob</description></class>
<class packageName="com.tridium.andoverAC256.job" name="BAndoverBackupControllerJob"><description>Andover AC256 Point Discovery.</description></class>
<class packageName="com.tridium.andoverAC256" name="BAndoverBackupFolder"><description>BAndoverBackupFolder is the standard container to use&#xa; under BAndoverBackupTable to organize BAndoverBackupFiles.</description></class>
<class packageName="com.tridium.andoverAC256.datatypes" name="BAndoverBackupRecord"><description>BAndoverBackupRecord is a dynamic slot added to the BAndoverDevice</description></class>
<class packageName="com.tridium.andoverAC256.point" name="BAndoverBooleanProxyExt"><description>BAndoverBooleanProxyExt represents a single binary value&#xa; from an Andover AC256 system.</description></class>
<class packageName="com.tridium.andoverAC256.job" name="BAndoverConsoleJob"><description>BAndoverConsoleJob sends commands from an&#xa; AndoverConole to the com port and routes&#xa; responses back to the AndoverConsole.</description></class>
<class packageName="com.tridium.andoverAC256.enums" name="BAndoverConsolePortSpeedEnum"><description>BAndoverConsolePortSpeedEnum represents the possible baud rate choices&#xa; for the Andover AC256 C-Port.</description></class>
<class packageName="com.tridium.andoverAC256" name="BAndoverDevice"><description>Device level class for the AC256 or AC8 controller</description></class>
<class packageName="com.tridium.andoverAC256" name="BAndoverDeviceFolder"><description>AndoverAC256 implementation of BDeviceFolder</description></class>
<class packageName="com.tridium.andoverAC256.enums" name="BAndoverDeviceTypeEnum"><description>Arbitrary enumeration of device type, necessary because&#xa; the AC256 and AC8 controllers differ(slightly) in logon&#xa; sequence, and differ in the info contained in the PRINT&#xa; STATUS response, and differ in sub-panels supported (impacts&#xa; the learn process)</description></class>
<class packageName="com.tridium.andoverAC256.job" name="BAndoverDiscoverPointsJob"><description>Andover AC256 Point Discovery.</description></class>
<class packageName="com.tridium.andoverAC256.job" name="BAndoverDiscoveryPoint"><description>Andover AC256 Discovery Point.</description></class>
<class packageName="com.tridium.andoverAC256.point" name="BAndoverEnumProxyExt"><description>BAndoverTriStateProxyExt represents a single tri-state value&#xa; from an Andover AC256 system.</description></class>
<class packageName="com.tridium.andoverAC256.datatypes" name="BAndoverIouPointDiscoveryConfig"><description>AndoverAC256 Device Discovery Config, used to govern what&#xa; tables or sub-panel are learned during the learn process</description></class>
<class packageName="com.tridium.andoverAC256.enums" name="BAndoverLBusSpeedEnum"><description>BAndoverLBusSpeedEnum represents the possible baud rate choices&#xa; for the Andover AC256 C-Port.</description></class>
<class packageName="com.tridium.andoverAC256.datatypes" name="BAndoverLcuPointDiscoveryConfig"><description>AndoverAC256 Device Discovery Config, used to govern what&#xa; tables or sub-panel are learned during the learn process</description></class>
<class packageName="com.tridium.andoverAC256" name="BAndoverNetwork"><description>BAndoverNetwork - represents an AC256 or AC8 Serial Network.</description></class>
<class packageName="com.tridium.andoverAC256.point" name="BAndoverNumericProxyExt"><description>BAndoverNumericProxyExt represents a single binary value&#xa; from an Andover AC256 system.</description></class>
<class packageName="com.tridium.andoverAC256.point" name="BAndoverPointDeviceExt"><description>AndoverAC256 implementation of BPointDeviceExt</description></class>
<class packageName="com.tridium.andoverAC256.datatypes" name="BAndoverPointDiscoveryConfig"><description>AndoverAC256 Device Discovery Config, used to govern what&#xa; tables or sub-panel are learned during the learn process</description></class>
<class packageName="com.tridium.andoverAC256.point" name="BAndoverPointFolder"><description>AndoverAC256 implementation of BPointFolder</description></class>
<class packageName="com.tridium.andoverAC256.enums" name="BAndoverPromptEnum"><description>Arbitrary enumeration of context states possible on the AC256&#xa; or AC8 controllers.</description></class>
<class packageName="com.tridium.andoverAC256.point" name="BAndoverProxyExt"><description>AndoverAC256 implementation of BProxyExt</description></class>
<class packageName="com.tridium.andoverAC256.enums" name="BAndoverReadEnabledStateEnum"><description>BAndoverAcnetSpeedEnum represents the possible baud rate choices&#xa; for the Andover AC256 C-Port.</description></class>
<class packageName="com.tridium.andoverAC256.datatypes" name="BAndoverRestoreConfig"><description>BAndoverRestoreConfig holds data for the BAndoverBackupJob</description></class>
<class packageName="com.tridium.andoverAC256.job" name="BAndoverRestoreControllerJob"><description>Andover AC256 Restore a controller&#x27;s program.</description></class>
<class packageName="com.tridium.andoverAC256.job" name="BAndoverRunScriptJob"><description>Run a script from an BAndoverScript object</description></class>
<class packageName="com.tridium.andoverAC256" name="BAndoverScript"><description>The BAndoverScript class can be used to send multiple command lines&#xa;  to an andover panel.</description></class>
<class packageName="com.tridium.andoverAC256.enums" name="BAndoverSendDisableEnum"><description>BAndoverAcnetSpeedEnum represents the possible baud rate choices&#xa; for the Andover AC256 C-Port.</description></class>
<class packageName="com.tridium.andoverAC256.datatypes" name="BAndoverSetOptionsConfig"><description>AndoverAC256 Set Options Config, used to set options in the&#xa; andover panel using the BAndoverSetOptionsJob.</description></class>
<class packageName="com.tridium.andoverAC256.job" name="BAndoverSetOptionsJob"><description>Andover AC256 Set Options.</description></class>
<class packageName="com.tridium.andoverAC256.point" name="BAndoverStringProxyExt"><description>BAndoverBooleanProxyExt represents a single string value&#xa; from an Andover AC256 system.</description></class>
<class packageName="com.tridium.andoverAC256.comm" name="BAndoverUnsolicitedReceive"><description>This class customizes unsolicited receive handling for the&#xa; Andover driver.</description></class>
<class packageName="com.tridium.andoverAC256.enums" name="BAndoverXBusSpeedEnum"><description>BAndoverXBusSpeedEnum represents the possible baud rate choices&#xa; for the Andover AC256 C-Port.</description></class>
<class packageName="com.tridium.andoverAC256.datatypes" name="BPrintStatusReadings"><description>BPrintStatusReadings - Component that encapsulates all relevant&#xa; data from the Andover PRINT STATUS response</description></class>
<class packageName="com.tridium.andoverAC256.datatypes" name="BProgramReloadSettings"><description>BProgramReloadSettings: Component that encapsulates all relevant&#xa; data used to re-set the number of IOUs and LCUs in the Andover&#xa; configuration.</description></class>
</module>
</bajadoc>
