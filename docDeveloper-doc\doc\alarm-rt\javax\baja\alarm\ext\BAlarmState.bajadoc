<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.ext.BAlarmState" name="BAlarmState" packageName="javax.baja.alarm.ext" public="true" final="true">
<description>
BAlarmState is an BEnum that represents valid Baja alarm states
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">9 Nov 00</tag>
<tag name="@version">$Revision: 10$ $Date: 3/30/05 11:21:46 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;normal&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;fault&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;offnormal&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;highLimit&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lowLimit&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.alarm.ext.BAlarmState.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.alarm.ext.BAlarmState"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmState.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.alarm.ext.BAlarmState"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmState.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmState.isInAlarm() -->
<method name="isInAlarm"  public="true">
<description>
Convenience method.  Returns true if and only&#xa;  if this instance is not equal to BAlarmState.normal
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmState.isOffnormal() -->
<method name="isOffnormal"  public="true">
<description>
Convenience method.  Returns true if and only&#xa;  if this instance is not equal to&#xa;  BAlarmState.normal or BAlarmState.fault
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmState.NORMAL -->
<field name="NORMAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for normal.
</description>
</field>

<!-- javax.baja.alarm.ext.BAlarmState.FAULT -->
<field name="FAULT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for fault.
</description>
</field>

<!-- javax.baja.alarm.ext.BAlarmState.OFFNORMAL -->
<field name="OFFNORMAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for offnormal.
</description>
</field>

<!-- javax.baja.alarm.ext.BAlarmState.HIGH_LIMIT -->
<field name="HIGH_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for highLimit.
</description>
</field>

<!-- javax.baja.alarm.ext.BAlarmState.LOW_LIMIT -->
<field name="LOW_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lowLimit.
</description>
</field>

<!-- javax.baja.alarm.ext.BAlarmState.normal -->
<field name="normal"  public="true" static="true" final="true">
<type class="javax.baja.alarm.ext.BAlarmState"/>
<description>
BAlarmState constant for normal.
</description>
</field>

<!-- javax.baja.alarm.ext.BAlarmState.fault -->
<field name="fault"  public="true" static="true" final="true">
<type class="javax.baja.alarm.ext.BAlarmState"/>
<description>
BAlarmState constant for fault.
</description>
</field>

<!-- javax.baja.alarm.ext.BAlarmState.offnormal -->
<field name="offnormal"  public="true" static="true" final="true">
<type class="javax.baja.alarm.ext.BAlarmState"/>
<description>
BAlarmState constant for offnormal.
</description>
</field>

<!-- javax.baja.alarm.ext.BAlarmState.highLimit -->
<field name="highLimit"  public="true" static="true" final="true">
<type class="javax.baja.alarm.ext.BAlarmState"/>
<description>
BAlarmState constant for highLimit.
</description>
</field>

<!-- javax.baja.alarm.ext.BAlarmState.lowLimit -->
<field name="lowLimit"  public="true" static="true" final="true">
<type class="javax.baja.alarm.ext.BAlarmState"/>
<description>
BAlarmState constant for lowLimit.
</description>
</field>

<!-- javax.baja.alarm.ext.BAlarmState.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.alarm.ext.BAlarmState"/>
<description/>
</field>

<!-- javax.baja.alarm.ext.BAlarmState.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
