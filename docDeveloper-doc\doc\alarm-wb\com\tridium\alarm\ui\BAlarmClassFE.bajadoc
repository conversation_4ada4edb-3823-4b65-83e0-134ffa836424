<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.ui.BAlarmClassFE" name="BAlarmClassFE" packageName="com.tridium.alarm.ui" public="true">
<description>
Plugin for BString when used as an alarm class identifier.&#xa; AlarmClasses need to have a unique name for routing. This allows us to nest&#xa; AlarmClasses in AlarmClassFolder and display them in this Field Editor.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">01 Aug 03</tag>
<tag name="@version">$Revision: 11$ $Date: 10/29/08 8:45:53 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="com.tridium.workbench.fieldeditors.BComponentNamePickerFE"/>
</extends>
</class>
</bajadoc>
