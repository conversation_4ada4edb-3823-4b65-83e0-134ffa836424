<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="app" runtimeProfile="rt" qualifiedName="javax.baja.app.BIAppComponent" name="BIAppComponent" packageName="javax.baja.app" public="true" interface="true" abstract="true" category="interface">
<description>
Interface for all App related Components
</description>
<tag name="@author">g<PERSON><PERSON><PERSON></tag>
<tag name="@creation">11 Aug 2011</tag>
<tag name="@version">1</tag>
<tag name="@since">Niagara 3.7</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<!-- javax.baja.app.BIAppComponent.getAppDisplayName(javax.baja.sys.Context) -->
<method name="getAppDisplayName"  public="true" abstract="true">
<description>
Return the App Display Name.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.app.BIAppComponent.getAppDisplayIcon() -->
<method name="getAppDisplayIcon"  public="true" abstract="true">
<description>
Return the App Display Icon.
</description>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.app.BIAppComponent.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
