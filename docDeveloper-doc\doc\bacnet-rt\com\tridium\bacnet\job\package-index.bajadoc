<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="com.tridium.bacnet.job">
<description/>
<class packageName="com.tridium.bacnet.job" name="BBacnetDiscoverConfigJob"><description>BBacnetDiscoverConfigJob is the task that manages discovery of BACnet objects&#xa; for the Config Manager.</description></class>
<class packageName="com.tridium.bacnet.job" name="BBacnetDiscoverDevicesJob"><description>BBacnetDiscoverDevicesJob queries the connected network for&#xa; undiscovered devices.</description></class>
<class packageName="com.tridium.bacnet.job" name="BBacnetDiscoverJob"><description>BBacnetDiscoverJob is the task that manages&#xa; discovery in a remote BACnet device.</description></class>
<class packageName="com.tridium.bacnet.job" name="BBacnetDiscoverPointsJob"><description>BBacnetDiscoverPointsJob represents a request from Niagara to&#xa; read the Object_List property of a remote device and create BBacnetObjects&#xa; for any objects that are not currently mapped in the Niagara database.</description></class>
<class packageName="com.tridium.bacnet.job" name="BBacnetDiscoverSchedulesJob"><description>BBacnetDiscoverSchedulesJob is the task that manages calendar and schedule&#xa; discovery in a remote BACnet device.</description></class>
<class packageName="com.tridium.bacnet.job" name="BBacnetDiscoverTrendLogsJob"><description>BBacnetDiscoverTrendLogsJob is the task that manages history&#xa; discovery in a remote BACnet device.</description></class>
<class packageName="com.tridium.bacnet.job" name="BBacnetScheduleTypeChangeJob"><description>BBacnetScheduleTypeChangeJob makes changes to a schedule object needed to&#xa; change the type of objects scheduled or cleanup inconsistent properties</description></class>
<class packageName="com.tridium.bacnet.job" name="BChangeDeviceIdJob"/>
<class packageName="com.tridium.bacnet.job" name="BDeviceManagerJob"><description>BDeviceManagerJob is the base class for jobs submitted by the&#xa; device manager GUI.</description></class>
<class packageName="com.tridium.bacnet.job" name="BDiscoveryConfig"><description>BDiscoveryConfig.</description></class>
<class packageName="com.tridium.bacnet.job" name="BDiscoveryDevice"><description>BDiscoveryDevice.</description></class>
<class packageName="com.tridium.bacnet.job" name="BDiscoveryLog"><description>BDiscoveryLog.</description></class>
<class packageName="com.tridium.bacnet.job" name="BDiscoveryPoint"><description>BDiscoveryPoint.</description></class>
<class packageName="com.tridium.bacnet.job" name="BDiscoveryPointTable"><description>BDiscoveryPointTable.</description></class>
<class packageName="com.tridium.bacnet.job" name="BDiscoverySchedule"><description>BDiscoverySchedule.</description></class>
<class packageName="com.tridium.bacnet.job" name="BTimeSynchJob"><description>BTimeSynchJob sends a one-shot time synch message to the network.</description></class>
<class packageName="com.tridium.bacnet.job" name="BWhoHasJob"><description>BWhoHasJob queries the connected network for devices that&#xa; contain objects with the specified objectId or objectName.</description></class>
</package>
</bajadoc>
