<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.MarkerPosition" name="MarkerPosition" packageName="com.tridium.andoverInfinity.comm" public="true" final="true">
<description>
Position encapsulates a logical two dimensional character &#xa; position in a document with a zero based line and column&#xa; index.  Because BTextWidget allows the caret to be freely &#xa; moved around the pane where there might not actually be text &#xa; yet, a Position doesn&#x27;t necessary always reference a real &#xa; position in the document text.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">12 Dec 00</tag>
<tag name="@version">$Revision: 4$ $Date: 3/28/2005 11:32:35 AM$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<parameterizedType class="java.lang.Comparable">
<args>
<type class="com.tridium.andoverInfinity.comm.MarkerPosition"/>
</args>
</parameterizedType>
</implements>
<!-- com.tridium.andoverInfinity.comm.MarkerPosition(int, int) -->
<constructor name="MarkerPosition" public="true">
<parameter name="line">
<type class="int"/>
<description>
zero indexed line number
</description>
</parameter>
<parameter name="column">
<type class="int"/>
<description>
zero indexed column number
</description>
</parameter>
<description>
Constructor with line and column.
</description>
</constructor>

<!-- com.tridium.andoverInfinity.comm.MarkerPosition.compareTo(com.tridium.andoverInfinity.comm.MarkerPosition) -->
<method name="compareTo"  public="true" final="true">
<description>
Return a negative integer, zero, or a positive integer &#xa; as this Position is less than, equal to, or greater than &#xa; the specified Position.
</description>
<parameter name="obj">
<type class="com.tridium.andoverInfinity.comm.MarkerPosition"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.MarkerPosition.equals(java.lang.Object) -->
<method name="equals"  public="true" final="true">
<description>
Equality.
</description>
<parameter name="obj">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.MarkerPosition.toString() -->
<method name="toString"  public="true" final="true">
<description>
To string.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.MarkerPosition.hashCode() -->
<method name="hashCode"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.MarkerPosition.line -->
<field name="line"  public="true" final="true">
<type class="int"/>
<description>
Zero indexed line number.
</description>
</field>

<!-- com.tridium.andoverInfinity.comm.MarkerPosition.column -->
<field name="column"  public="true" final="true">
<type class="int"/>
<description>
Zero indexed column number.
</description>
</field>

</class>
</bajadoc>
