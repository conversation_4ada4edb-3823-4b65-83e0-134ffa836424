<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.sys.BIAceSpace" name="BIAceSpace" packageName="com.tridium.ace.sys" public="true" interface="true" abstract="true" category="interface">
<description>
BIAceSpace
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">13 Feb 17</tag>
<tag name="@since">Niagara 4.4</tag>
<implements>
<type class="javax.baja.space.BISpace"/>
</implements>
</class>
</bajadoc>
