<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.io.RangeReference" name="RangeReference" packageName="javax.baja.bacnet.io" public="true" interface="true" abstract="true" category="interface">
<description>
RangeReference contains information to request&#xa; a range of values in a compound property.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">13 Sep 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<implements>
<type class="javax.baja.bacnet.io.PropertyReference"/>
</implements>
<!-- javax.baja.bacnet.io.RangeReference.getRangeType() -->
<method name="getRangeType"  public="true" abstract="true">
<description>
Get the rangeType.
</description>
<return>
<type class="int"/>
<description>
the rangeType.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.RangeReference.getReferenceIndex() -->
<method name="getReferenceIndex"  public="true" abstract="true">
<description>
Get the reference index.&#xa; &lt;p&gt;&#xa; Interpretation depends on the value of rangeType.
</description>
<return>
<type class="long"/>
<description>
the reference index.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.RangeReference.getReferenceTime() -->
<method name="getReferenceTime"  public="true" abstract="true">
<description>
Get the reference time.&#xa; &lt;p&gt;&#xa; Only meaningful if rangeType is BY_TIME.&#xa; If rangeType is BY_POSITION or BY_SEQUENCE_NUMBER, this&#xa; will be null.
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
<description>
the reference index.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.RangeReference.getCount() -->
<method name="getCount"  public="true" abstract="true">
<description>
Get the number of items requested.
</description>
<return>
<type class="int"/>
<description>
the count of requested items.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.RangeReference.BY_POSITION -->
<field name="BY_POSITION"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.io.RangeReference.BY_TIME_DEPRECATED -->
<field name="BY_TIME_DEPRECATED"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.io.RangeReference.TIME_RANGE_DEPRECATED -->
<field name="TIME_RANGE_DEPRECATED"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.io.RangeReference.BY_SEQUENCE_NUMBER -->
<field name="BY_SEQUENCE_NUMBER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.io.RangeReference.BY_TIME -->
<field name="BY_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
