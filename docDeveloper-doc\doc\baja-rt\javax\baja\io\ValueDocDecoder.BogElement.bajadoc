<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.io.ValueDocDecoder$BogElement" name="ValueDocDecoder.BogElement" packageName="javax.baja.io" public="true" static="true" final="true" innerClass="true">
<description>
A BOG document element
</description>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="com.tridium.nre.util.IElement"/>
</implements>
<!-- javax.baja.io.ValueDocDecoder.BogElement.make(javax.baja.xml.XElem) -->
<method name="make"  public="true" static="true">
<description/>
<parameter name="elem">
<type class="javax.baja.xml.XElem"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocDecoder$BogElement"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogElement.name() -->
<method name="name"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogElement.get(java.lang.String) -->
<method name="get"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="attrName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogElement.get(java.lang.String, java.lang.String) -->
<method name="get"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="attrName">
<type class="java.lang.String"/>
</parameter>
<parameter name="def">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogElement.geti(java.lang.String) -->
<method name="geti"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="attrName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogElement.geti(java.lang.String, int) -->
<method name="geti"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="attrName">
<type class="java.lang.String"/>
</parameter>
<parameter name="def">
<type class="int"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogElement.getd(java.lang.String) -->
<method name="getd"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="attrName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="double"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogElement.getd(java.lang.String, double) -->
<method name="getd"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="attrName">
<type class="java.lang.String"/>
</parameter>
<parameter name="def">
<type class="double"/>
</parameter>
<return>
<type class="double"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogElement.getf(java.lang.String) -->
<method name="getf"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="attrName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="float"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogElement.getf(java.lang.String, float) -->
<method name="getf"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="attrName">
<type class="java.lang.String"/>
</parameter>
<parameter name="def">
<type class="float"/>
</parameter>
<return>
<type class="float"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogElement.getl(java.lang.String) -->
<method name="getl"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="attrName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="long"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogElement.getl(java.lang.String, long) -->
<method name="getl"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="attrName">
<type class="java.lang.String"/>
</parameter>
<parameter name="def">
<type class="long"/>
</parameter>
<return>
<type class="long"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogElement.attrSize() -->
<method name="attrSize"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogElement.attrName(int) -->
<method name="attrName"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogElement.attrValue(int) -->
<method name="attrValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogElement.copy() -->
<method name="copy"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="com.tridium.nre.util.IElement"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogElement.toString() -->
<method name="toString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogElement.getXmlElement() -->
<method name="getXmlElement"  public="true">
<description/>
<return>
<type class="javax.baja.xml.XElem"/>
</return>
</method>

</class>
</bajadoc>
