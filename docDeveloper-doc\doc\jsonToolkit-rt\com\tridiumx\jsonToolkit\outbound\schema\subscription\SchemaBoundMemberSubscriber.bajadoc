<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.subscription.SchemaBoundMemberSubscriber" name="SchemaBoundMemberSubscriber" packageName="com.tridiumx.jsonToolkit.outbound.schema.subscription" public="true">
<description>
A subscriber for a specific bound member. Checks each subscription event against a&#xa; list of filters and if passes (as deemed to result in changed json)&#xa; then requests a new schema output generation.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.sys.Subscriber"/>
</extends>
<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.SchemaBoundMemberSubscriber(com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaBoundMember, com.tridiumx.jsonToolkit.outbound.schema.subscription.Subscription, com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionEventHandler) -->
<constructor name="SchemaBoundMemberSubscriber" public="true">
<parameter name="schemaBoundMember">
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaBoundMember"/>
</parameter>
<parameter name="subscription">
<type class="com.tridiumx.jsonToolkit.outbound.schema.subscription.Subscription"/>
</parameter>
<parameter name="handler">
<type class="com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionEventHandler"/>
</parameter>
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.SchemaBoundMemberSubscriber.subscribeTo(javax.baja.sys.BComponent) -->
<method name="subscribeTo"  public="true">
<description/>
<parameter name="target">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.SchemaBoundMemberSubscriber.event(javax.baja.sys.BComponentEvent) -->
<method name="event"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="event">
<type class="javax.baja.sys.BComponentEvent"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.SchemaBoundMemberSubscriber.subscribed(javax.baja.sys.BComponent, javax.baja.sys.Context) -->
<method name="subscribed"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="component">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.SchemaBoundMemberSubscriber.unsubscribed(javax.baja.sys.BComponent, javax.baja.sys.Context) -->
<method name="unsubscribed"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="component">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
