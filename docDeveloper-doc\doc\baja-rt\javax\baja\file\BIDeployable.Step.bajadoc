<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BIDeployable$Step" name="BIDeployable.Step" packageName="javax.baja.file" public="true" static="true" innerClass="true">
<description/>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- javax.baja.file.BIDeployable.Step(javax.baja.sys.BObject, java.lang.String, javax.baja.naming.BOrd) -->
<constructor name="Step" public="true">
<parameter name="value">
<type class="javax.baja.sys.BObject"/>
</parameter>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="destination">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.file.BIDeployable.Step(javax.baja.space.Mark, javax.baja.naming.BOrd) -->
<constructor name="Step" public="true">
<parameter name="mark">
<type class="javax.baja.space.Mark"/>
</parameter>
<parameter name="destination">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.file.BIDeployable.Step.mark -->
<field name="mark"  public="true">
<type class="javax.baja.space.Mark"/>
<description/>
</field>

<!-- javax.baja.file.BIDeployable.Step.destination -->
<field name="destination"  public="true">
<type class="javax.baja.naming.BOrd"/>
<description/>
</field>

</class>
</bajadoc>
