<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.history.BBacnetTrendRecord" name="BBacnetTrendRecord" packageName="com.tridium.bacnet.history" public="true" abstract="true">
<description>
BBacnetTrendRecord is a Bacnet history record that includes special&#xa; semantics for histories that track a single data point&#xa; like the histories generated by a history extension&#xa; on a control point.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 1$ $Date: 8/11/2003 1:54:12 PM$</tag>
<tag name="@creation">11 Aug 2003</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.history.BTrendRecord"/>
</extends>
<implements>
<type class="javax.baja.status.BIStatus"/>
</implements>
<property name="sequenceNumber" flags="">
<type class="long"/>
<description>
Slot for the &lt;code&gt;sequenceNumber&lt;/code&gt; property.&#xa; The sequence number associated with the Bacnet&#xa; trend log record.
</description>
<tag name="@see">#getSequenceNumber</tag>
<tag name="@see">#setSequenceNumber</tag>
</property>

<property name="logEvent" flags="">
<type class="com.tridium.bacnet.datatypes.BTrendEvent"/>
<description>
Slot for the &lt;code&gt;logEvent&lt;/code&gt; property.&#xa; Specifies any events associated with this&#xa; Bacnet trend log record (i.e. log status changes,&#xa; failures, time changes)
</description>
<tag name="@see">#getLogEvent</tag>
<tag name="@see">#setLogEvent</tag>
</property>

</class>
</bajadoc>
