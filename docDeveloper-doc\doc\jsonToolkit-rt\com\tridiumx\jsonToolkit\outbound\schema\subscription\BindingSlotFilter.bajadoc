<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.subscription.BindingSlotFilter" name="BindingSlotFilter" packageName="com.tridiumx.jsonToolkit.outbound.schema.subscription" public="true">
<description>
A subscription filter which only matches if the event slot is the first slot in a component when walking up the&#xa; target ord of the bound member.&#xa;&#xa; e.g&#xa;&#xa; if bound to slot:/testing/component  then the event slot needs to be component&#xa; if bound to slot:/testing/component/struct  then the event slot needs to be struct&#xa; if bound to slot:/testing/component/struct/simple  then the event slot needs to be struct&#xa;&#xa; NOTE - matching is on slot name, not slot equality because in a relative json schema, the ord&#xa; might be &#x27;slot:out&#x27;. With slot.equals() this will not work as the slot is redefined under&#xa; NumericPoint/BooleanPoint etc
</description>
<tag name="@author">Nicholas Dodd</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionEventFilter"/>
</implements>
<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.BindingSlotFilter.test(javax.baja.sys.BComponentEvent) -->
<method name="test"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="event">
<type class="javax.baja.sys.BComponentEvent"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.support.FilterResult"/>
</return>
</method>

</class>
</bajadoc>
