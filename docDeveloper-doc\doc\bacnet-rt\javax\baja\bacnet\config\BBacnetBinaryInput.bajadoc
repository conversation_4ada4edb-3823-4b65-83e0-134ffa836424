<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BBacnetBinaryInput" name="BBacnetBinaryInput" packageName="javax.baja.bacnet.config" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 8$ $Date: 12/10/01 9:26:04 AM$</tag>
<tag name="@creation">14 Feb 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.config.BBacnetBinary"/>
</extends>
<property name="polarity" flags="">
<type class="javax.baja.bacnet.enums.BBacnetPolarity"/>
<description>
Slot for the &lt;code&gt;polarity&lt;/code&gt; property.&#xa; polarity describes the relationship between the physical state&#xa; of the point and the logical state represented by the presentValue&#xa; property.
</description>
<tag name="@see">#getPolarity</tag>
<tag name="@see">#setPolarity</tag>
</property>

<!-- javax.baja.bacnet.config.BBacnetBinaryInput() -->
<constructor name="BBacnetBinaryInput" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetBinaryInput.getPolarity() -->
<method name="getPolarity"  public="true">
<description>
Get the &lt;code&gt;polarity&lt;/code&gt; property.&#xa; polarity describes the relationship between the physical state&#xa; of the point and the logical state represented by the presentValue&#xa; property.
</description>
<tag name="@see">#polarity</tag>
<return>
<type class="javax.baja.bacnet.enums.BBacnetPolarity"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetBinaryInput.setPolarity(javax.baja.bacnet.enums.BBacnetPolarity) -->
<method name="setPolarity"  public="true">
<description>
Set the &lt;code&gt;polarity&lt;/code&gt; property.&#xa; polarity describes the relationship between the physical state&#xa; of the point and the logical state represented by the presentValue&#xa; property.
</description>
<tag name="@see">#polarity</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.BBacnetPolarity"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetBinaryInput.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetBinaryInput.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetBinaryInput.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetBinaryInput.polarity -->
<field name="polarity"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;polarity&lt;/code&gt; property.&#xa; polarity describes the relationship between the physical state&#xa; of the point and the logical state represented by the presentValue&#xa; property.
</description>
<tag name="@see">#getPolarity</tag>
<tag name="@see">#setPolarity</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetBinaryInput.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
