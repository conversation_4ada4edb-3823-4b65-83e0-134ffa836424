<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor" name="BBacnetAuthenticationFactor" packageName="javax.baja.bacnet.datatypes.access" public="true" final="true">
<description>
BBacnetAssignedAccessRights represents the BACnetAssignedAccessRights&#xa; sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="formatType" flags="">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
Slot for the &lt;code&gt;formatType&lt;/code&gt; property.
</description>
<tag name="@see">#getFormatType</tag>
<tag name="@see">#setFormatType</tag>
</property>

<property name="formatClass" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;formatClass&lt;/code&gt; property.
</description>
<tag name="@see">#getFormatClass</tag>
<tag name="@see">#setFormatClass</tag>
</property>

<property name="value" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetOctetString"/>
<description>
Slot for the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#getValue</tag>
<tag name="@see">#setValue</tag>
</property>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor() -->
<constructor name="BBacnetAuthenticationFactor" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor(javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType, int, javax.baja.bacnet.datatypes.BBacnetOctetString) -->
<constructor name="BBacnetAuthenticationFactor" public="true">
<parameter name="formatType">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
</parameter>
<parameter name="formatClass">
<type class="int"/>
</parameter>
<parameter name="value">
<type class="javax.baja.bacnet.datatypes.BBacnetOctetString"/>
</parameter>
<description>
Standard constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor.getFormatType() -->
<method name="getFormatType"  public="true">
<description>
Get the &lt;code&gt;formatType&lt;/code&gt; property.
</description>
<tag name="@see">#formatType</tag>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor.setFormatType(javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType) -->
<method name="setFormatType"  public="true">
<description>
Set the &lt;code&gt;formatType&lt;/code&gt; property.
</description>
<tag name="@see">#formatType</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor.getFormatClass() -->
<method name="getFormatClass"  public="true">
<description>
Get the &lt;code&gt;formatClass&lt;/code&gt; property.
</description>
<tag name="@see">#formatClass</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor.setFormatClass(int) -->
<method name="setFormatClass"  public="true">
<description>
Set the &lt;code&gt;formatClass&lt;/code&gt; property.
</description>
<tag name="@see">#formatClass</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor.getValue() -->
<method name="getValue"  public="true">
<description>
Get the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#value</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetOctetString"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor.setValue(javax.baja.bacnet.datatypes.BBacnetOctetString) -->
<method name="setValue"  public="true">
<description>
Set the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#value</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetOctetString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor.formatType -->
<field name="formatType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;formatType&lt;/code&gt; property.
</description>
<tag name="@see">#getFormatType</tag>
<tag name="@see">#setFormatType</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor.formatClass -->
<field name="formatClass"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;formatClass&lt;/code&gt; property.
</description>
<tag name="@see">#getFormatClass</tag>
<tag name="@see">#setFormatClass</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor.value -->
<field name="value"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#getValue</tag>
<tag name="@see">#setValue</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor.FORMAT_TYPE_TAG -->
<field name="FORMAT_TYPE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor.FORMAT_CLASS_TAG -->
<field name="FORMAT_CLASS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor.VALUE_TAG -->
<field name="VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
