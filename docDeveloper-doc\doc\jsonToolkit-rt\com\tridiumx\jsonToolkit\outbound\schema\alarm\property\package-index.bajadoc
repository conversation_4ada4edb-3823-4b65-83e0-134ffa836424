<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.outbound.schema.alarm.property">
<description/>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.alarm.property" name="BBFormatString"><description>&lt;p&gt;&#xa; BBFormatString Holds a format such as %alarmData.sourceName% which will be extracted from an&#xa; AlarmRecord passed in by the parent Alarm Recipient.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.alarm.property" name="BJsonSchemaAlarmRecordProperty"><description>The &lt;code&gt;<see ref="com.tridiumx.jsonToolkit.outbound.schema.alarm.BJsonAlarmRecipient">com.tridiumx.jsonToolkit.outbound.schema.alarm.BJsonAlarmRecipient</see>&lt;/code&gt; schema supports alarm-related properties.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.alarm.property" name="BIJsonAlarmDataResolver" category="interface"><description>BIJsonAlarmDataResolver</description></class>
</package>
</bajadoc>
