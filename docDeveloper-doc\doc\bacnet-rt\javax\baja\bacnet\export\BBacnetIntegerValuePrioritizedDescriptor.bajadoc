<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetIntegerValuePrioritizedDescriptor" name="BBacnetIntegerValuePrioritizedDescriptor" packageName="javax.baja.bacnet.export" public="true">
<description>
BBacnetLargeAnalogValueDescriptor exposes a ControlPoint as a writable (non-commandable) Bacnet&#xa; Analog Value Object.
</description>
<tag name="@author"><PERSON> on 19 Feb 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.export.BBacnetAnalogWritableDescriptor"/>
</extends>
<!-- javax.baja.bacnet.export.BBacnetIntegerValuePrioritizedDescriptor() -->
<constructor name="BBacnetIntegerValuePrioritizedDescriptor" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetIntegerValuePrioritizedDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValuePrioritizedDescriptor.asnType() -->
<method name="asnType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValuePrioritizedDescriptor.convertToAsn(double) -->
<method name="convertToAsn"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
This is a loss-y conversion
</description>
<parameter name="value">
<type class="double"/>
<description>
double value to convert to asn.1
</description>
</parameter>
<return>
<type class="byte" dimension="1"/>
<description>
truncated and asn.1 encoded integer
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValuePrioritizedDescriptor.convertFromAsn(byte[]) -->
<method name="convertFromAsn"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Read a bacnet integer and convert into a double&#xa; ~precise up to +/- 2^53
</description>
<parameter name="value">
<type class="byte" dimension="1"/>
<description>
asn.1 byte array containing a number
</description>
</parameter>
<return>
<type class="double"/>
<description>
converted double value
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
<description/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValuePrioritizedDescriptor.addRequiredProps(java.util.Vector) -->
<method name="addRequiredProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValuePrioritizedDescriptor.addOptionalProps(java.util.Vector) -->
<method name="addOptionalProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValuePrioritizedDescriptor.appendToAsn(com.tridium.bacnet.asn.AsnOutputStream, double) -->
<method name="appendToAsn"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="out">
<type class="com.tridium.bacnet.asn.AsnOutputStream"/>
<description>
asn.1 byte stream to append the numeric value
</description>
</parameter>
<parameter name="value">
<type class="double"/>
<description>
asn.1 byte array containing a number
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValuePrioritizedDescriptor.readFromAsn(com.tridium.bacnet.asn.AsnInputStream) -->
<method name="readFromAsn"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="in">
<type class="com.tridium.bacnet.asn.AsnInputStream"/>
<description>
asn.1 byte stream read a numeric value from
</description>
</parameter>
<return>
<type class="double"/>
<description>
bacnet integer truncated to java double
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
<description>
if an unexpected ASN_TYPE is encountered
</description>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValuePrioritizedDescriptor.getSlotFacets(javax.baja.sys.Slot) -->
<method name="getSlotFacets"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get slot facets.
</description>
<parameter name="s">
<type class="javax.baja.sys.Slot"/>
<description>
the
</description>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
<description>
the appropriate slot facets.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValuePrioritizedDescriptor.readProperty(int, int) -->
<method name="readProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="pId">
<type class="int"/>
</parameter>
<parameter name="ndx">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValuePrioritizedDescriptor.getDeadBandValue(byte[]) -->
<method name="getDeadBandValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the deadband value as Unsigned integer
</description>
<parameter name="value">
<type class="byte" dimension="1"/>
<description/>
</parameter>
<return>
<type class="double"/>
<description>
deadband value
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValuePrioritizedDescriptor.getDeadBandBytes(double) -->
<method name="getDeadBandBytes"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the deadband value as Asn Byte array
</description>
<parameter name="value">
<type class="double"/>
<description/>
</parameter>
<return>
<type class="byte" dimension="1"/>
<description>
deadband bytes
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValuePrioritizedDescriptor.getCovIncrement(byte[]) -->
<method name="getCovIncrement"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
get the COV increment as Unsigned value
</description>
<parameter name="value">
<type class="byte" dimension="1"/>
<description/>
</parameter>
<return>
<type class="double"/>
<description>
cov increment
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
<description/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValuePrioritizedDescriptor.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this point is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetIntegerValuePrioritizedDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
