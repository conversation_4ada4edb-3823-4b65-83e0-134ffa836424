<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="aapup" runtimeProfile="wb" name="com.tridium.aapup.ui.device">
<description/>
<class packageName="com.tridium.aapup.ui.device" name="BPupDeviceManager"><description>BPupDeviceManager uses the BAbstractLearn framework to&#xa; provide a way for the user to create PUP Devices.</description></class>
</package>
</bajadoc>
