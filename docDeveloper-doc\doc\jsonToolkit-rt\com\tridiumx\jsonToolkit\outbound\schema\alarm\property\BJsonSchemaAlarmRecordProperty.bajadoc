<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.alarm.property.BJsonSchemaAlarmRecordProperty" name="BJsonSchemaAlarmRecordProperty" packageName="com.tridiumx.jsonToolkit.outbound.schema.alarm.property" public="true">
<description>
The &lt;code&gt;<see ref="com.tridiumx.jsonToolkit.outbound.schema.alarm.BJsonAlarmRecipient">com.tridiumx.jsonToolkit.outbound.schema.alarm.BJsonAlarmRecipient</see>&lt;/code&gt; schema supports alarm-related properties.&#xa; Adding each of these to the schema allows inclusion of the selected alarm property in the output.&#xa;&#xa; For example, the sourceState, uuid, alarmClass etc.&#xa; Note that some record properties such as alarm high/low limit are returned as strings.
</description>
<tag name="@author">Jason Woollard</tag>
<tag name="@since">Niagara 4.9</tag>
<extends>
<parameterizedType class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaProperty">
<args>
<type class="java.lang.Object"/>
</args>
</parameterizedType>
</extends>
<implements>
<type class="com.tridiumx.jsonToolkit.outbound.schema.alarm.property.BIJsonAlarmDataResolver"/>
</implements>
<property name="alarmProperty" flags="">
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
Slot for the &lt;code&gt;alarmProperty&lt;/code&gt; property.&#xa; Lighter alternative to getPropertiesToIncludeInJson / slotsToInclude:
</description>
<tag name="@see">#getAlarmProperty</tag>
<tag name="@see">#setAlarmProperty</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.alarm.property.BJsonSchemaAlarmRecordProperty() -->
<constructor name="BJsonSchemaAlarmRecordProperty" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.alarm.property.BJsonSchemaAlarmRecordProperty.getAlarmProperty() -->
<method name="getAlarmProperty"  public="true">
<description>
Get the &lt;code&gt;alarmProperty&lt;/code&gt; property.&#xa; Lighter alternative to getPropertiesToIncludeInJson / slotsToInclude:
</description>
<tag name="@see">#alarmProperty</tag>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.alarm.property.BJsonSchemaAlarmRecordProperty.setAlarmProperty(javax.baja.sys.BDynamicEnum) -->
<method name="setAlarmProperty"  public="true">
<description>
Set the &lt;code&gt;alarmProperty&lt;/code&gt; property.&#xa; Lighter alternative to getPropertiesToIncludeInJson / slotsToInclude:
</description>
<tag name="@see">#alarmProperty</tag>
<parameter name="v">
<type class="javax.baja.sys.BDynamicEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.alarm.property.BJsonSchemaAlarmRecordProperty.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.alarm.property.BJsonSchemaAlarmRecordProperty.getJsonValue() -->
<method name="getJsonValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.Object"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.alarm.property.BJsonSchemaAlarmRecordProperty.resolve(javax.baja.alarm.BAlarmRecord) -->
<method name="resolve"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Populate resolvedValue field with value from the given Alarm Record
</description>
<parameter name="r">
<type class="javax.baja.alarm.BAlarmRecord"/>
<description>
The input AlarmRecord
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.alarm.property.BJsonSchemaAlarmRecordProperty.getAlarmPropOf(java.lang.String) -->
<method name="getAlarmPropOf"  public="true" static="true">
<description>
Testing convenience returning a copy of the enum, currently set to &#x22;tag&#x22;
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.alarm.property.BJsonSchemaAlarmRecordProperty.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Testing/Builder convenience returning a configured Alarm Prop, currently set to &#x22;tag&#x22;
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.alarm.property.BJsonSchemaAlarmRecordProperty"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.alarm.property.BJsonSchemaAlarmRecordProperty.alarmProperty -->
<field name="alarmProperty"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmProperty&lt;/code&gt; property.&#xa; Lighter alternative to getPropertiesToIncludeInJson / slotsToInclude:
</description>
<tag name="@see">#getAlarmProperty</tag>
<tag name="@see">#setAlarmProperty</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.alarm.property.BJsonSchemaAlarmRecordProperty.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
