<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BBacnetTrendLogMultiple" name="BBacnetTrendLogMultiple" packageName="javax.baja.bacnet.config" public="true">
<description>
BBacnetTrendLogMultiple augments BBacnetTrendLog.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">09 Sep 2009</tag>
<tag name="@since">Niagara 3.5</tag>
<extends>
<type class="javax.baja.bacnet.config.BBacnetTrendLog"/>
</extends>
<property name="logDeviceObjectProperty" flags="r">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
<description>
Slot for the &lt;code&gt;logDeviceObjectProperty&lt;/code&gt; property.
</description>
<tag name="@see">#getLogDeviceObjectProperty</tag>
<tag name="@see">#setLogDeviceObjectProperty</tag>
</property>

<!-- javax.baja.bacnet.config.BBacnetTrendLogMultiple() -->
<constructor name="BBacnetTrendLogMultiple" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetTrendLogMultiple.getLogDeviceObjectProperty() -->
<method name="getLogDeviceObjectProperty"  public="true">
<description>
Get the &lt;code&gt;logDeviceObjectProperty&lt;/code&gt; property.
</description>
<tag name="@see">#logDeviceObjectProperty</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLogMultiple.setLogDeviceObjectProperty(javax.baja.bacnet.datatypes.BBacnetArray) -->
<method name="setLogDeviceObjectProperty"  public="true">
<description>
Set the &lt;code&gt;logDeviceObjectProperty&lt;/code&gt; property.
</description>
<tag name="@see">#logDeviceObjectProperty</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLogMultiple.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetTrendLogMultiple.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetTrendLogMultiple.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetTrendLogMultiple.logDeviceObjectProperty -->
<field name="logDeviceObjectProperty"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;logDeviceObjectProperty&lt;/code&gt; property.
</description>
<tag name="@see">#getLogDeviceObjectProperty</tag>
<tag name="@see">#setLogDeviceObjectProperty</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetTrendLogMultiple.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
