<!-- Htmldoc has been run -->
<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>js Module: dialogs</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

<!-- Auto-generated style sheet link --><link rel='StyleSheet' href='module://bajaui/doc/style.css' type='text/css' />
<!-- Auto-generated js link for Activity Monitoring --><script type='text/javascript' src='module://web/rc/util/activityMonitor.js'></script>
<script type='text/javascript'>window.addEventListener('load', activityMonitor.start);</script>
</head>

<body>
<!-- Auto-generated Header NavBar --><p class="navbar">  <a href="/doc/index.html" class="navbar">Index</a> |  <a href="/doc/jsdoc/js-ux/module-log.html" class="navbar">Prev</a> |  <a href="/doc/dashboard.html" class="navbar">Next</a></p>


<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">js</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-dialogs.html">dialogs</a></li><li><a href="module-lex.html">lex</a></li><li><a href="module-log.html">log</a></li><li><a href="module-nmodule_js_rc_csrf_csrfUtil.html">nmodule/js/rc/csrf/csrfUtil</a></li><li><a href="module-nmodule_js_rc_jasmine_promiseUtils.html">nmodule/js/rc/jasmine/promiseUtils</a></li><li><a href="module-nmodule_js_rc_lex_lex.html">nmodule/js/rc/lex/lex</a></li><li><a href="module-nmodule_js_rc_log_Level.html">nmodule/js/rc/log/Level</a></li><li><a href="module-nmodule_js_rc_log_Log.html">nmodule/js/rc/log/Log</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="classes.list.html" class="dropdown-toggle" data-toggle="dropdown">Classes<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-dialogs-Dialog.html">dialogs~Dialog</a></li><li><a href="module-nmodule_js_rc_lex_lex-Lexicon.html">nmodule/js/rc/lex/lex~Lexicon</a></li><li><a href="module-nmodule_js_rc_log_Log.Level.html">nmodule/js/rc/log/Log.Level</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: dialogs</h1>
<section>

<header>
    
</header>


<article>
    <div class="container-overview">
    
        
            <div class="description"><p>Create stunning, modal Dialog boxes in JavaScript.</p>
<p>This is a UI library used to create dynamic, modal Dialog boxes in your<br>
browser.</p></div>
        

        
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


        
            <h3>Examples</h3>
            
        <p class="code-caption">
  A simple modal OK dialog box.
</p>
    
    <pre class="sunlight-highlight-javascript">dialogs.showOk(&quot;Some Dialog Box Content&quot;)
       .ok(function () {
         console.log(&quot;The OK button has been clicked&quot;);
       });</pre>

        <p class="code-caption">
  A Dialog box with a title and some HTML content.
</p>
    
    <pre class="sunlight-highlight-javascript">dialogs.showOk({
         title: &quot;The Dialog Box&#x27;s Title!&quot;,
         content: &quot;&amp;lt;p&amp;gt;Some HTML Content&amp;lt;/p&amp;gt;&quot;
       })
       .ok(function () {
         console.log(&quot;The OK button has been clicked&quot;);
       });

 </pre>

        <p class="code-caption">
   A simple Yes, No, Cancel dialog box.
 </p>
    
    <pre class="sunlight-highlight-javascript">dialogs.showYesNoCancel(&quot;Would you like some tea with that?&quot;)
        .yes(function () {
          console.log(&quot;The user clicked Yes&quot;);
        })
        .no(function() {
          console.log(&quot;The user clicked No&quot;);
        })
        .cancel(function () {
          console.log(&quot;The user clicked Cancel&quot;);
        });</pre>

        <p class="code-caption">
  Show a loading dialog box and have it close after the AJAX call has finished.
</p>
    
    <pre class="sunlight-highlight-javascript">dialogs.showLoading(0, $.ajax(uri, options));</pre>

        <p class="code-caption">
  Use promises to show a loading dialog box and then pop up another dialog.
</p>
    
    <pre class="sunlight-highlight-javascript">var dlg = dialogs.showLoading();
// After 2 seconds, close the loading box.
setTimeout(function () {
  dlg.close();
}, 2000);
dlg.promise().then(([ dlg, buttonClicked ]) =&gt; {
  // Prints &#x27;ok&#x27;
  console.log(buttonClicked);
     
  dialogs.showOk(&quot;The foobar has finished loading!&quot;);
});</pre>

        <p class="code-caption">
  Show a dialog. Have the content dynamically created by
  passing in a function for the content.
</p>
    
    <pre class="sunlight-highlight-javascript">dialogs.show(function(dlg, jq) {
  jq.html(&quot;&amp;lt;div&amp;gt;I love Niagara 4!&amp;lt;/div&amp;gt;&quot;);
});</pre>

        <p class="code-caption">
  Show a dialog. Have the content dynamically created
  by passing in a function for the content. The dialog
  will only show when the return promise has been resolved.
</p>
    
    <pre class="sunlight-highlight-javascript">dialogs.show(function(dlg, jq) {
  return Promise.resolve($.ajax(&quot;/myajax&quot;)
    .then(function (response) {
      jq.html(&quot;The answer is...&quot; + JSON.parse(response).answer);
    });
});</pre>

        <p class="code-caption">
  A Dialog BOX with background privacy setting.
</p>
    
    <pre class="sunlight-highlight-javascript">dialogs.showOk({
         title: &quot;The Dialog Box&#x27;s Title!&quot;,
         content: &quot;&amp;lt;p&amp;gt;Some HTML Content&amp;lt;/p&amp;gt;&quot;,
         private: true //ensures background contents are screened when the dialog is showing
       })
       .ok(function () {
         console.log(&quot;The OK button has been clicked&quot;);
       });</pre>


        
    
    </div>

    

    
        <h3 class="subsection-title">Requires</h3>

        <ul>
            <li>module:jquery</li>
        
            <li>module:lex!js</li>
        
            <li>module:css!nmodule/js/rc/dialogs/dialogs</li>
        </ul>
    

    
        <h3 class="subsection-title">Classes</h3>

        <dl>
            <dt><a href="module-dialogs-Dialog.html">Dialog</a></dt>
            <dd></dd>
        </dl>
    

    

    

    

    

    
        <h3 class="subsection-title">Type Definitions</h3>

        <dl>
                
<hr>
<dt class="name" id="~Button">
    <h4 id="~Button">Button</h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>An Object that defines a Button.</p>
    </div>
    

    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">Object</span>



            </li>
        </ul>
    

    
<dl class="details">
    

    <h5 class="subsection-title">Properties:</h5>

    <dl>

<table class="props table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>A button's unique name so it can be identified.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>displayName</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>The button's display name. This name will used<br>
on the button. If not specified, the button's name will be used instead.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>handler</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>A function that will be invoked when the button is<br>
clicked. Returning false will keep the dialog from closing after this handler<br>
has been invoked.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>disable</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>if set to true, the button will be disabled until <code>enableButton</code><br>
is called.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>hide</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>if set to true, the button will be hidden until <code>showButton</code> is<br>
called.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>esc</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>If true, this handler will be invoked when the user<br>
hits the escape key.</p></td>
        </tr>

    
    </tbody>
</table>
</dl>

    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-dialogs-Dialog.html#on">module:dialogs~Dialog#on</a></li>
			</ul>
	</dd>
	

	
</dl>


    
</dd>

            </dl>
    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	js Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:57+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>



<!-- Auto-generated Footer NavBar --><p class="navbar">  <a href="/doc/index.html" class="navbar">Index</a> |  <a href="/doc/jsdoc/js-ux/module-log.html" class="navbar">Prev</a> |  <a href="/doc/dashboard.html" class="navbar">Next</a></p>
<!-- Auto-generated copyright note --><p class='copyright'></p>
</body>
</html>
