<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.subscription.ChildSlotRenamedFilter" name="ChildSlotRenamedFilter" packageName="com.tridiumx.jsonToolkit.outbound.schema.subscription" public="true">
<description>
Subscription filter for property rename events. We only care about this for bound objects which uses child slot&#xa; names so important to keep up to date. All other bound types can ignore these events.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionEventFilter"/>
</implements>
<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.ChildSlotRenamedFilter.test(javax.baja.sys.BComponentEvent) -->
<method name="test"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="event">
<type class="javax.baja.sys.BComponentEvent"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.support.FilterResult"/>
</return>
</method>

</class>
</bajadoc>
