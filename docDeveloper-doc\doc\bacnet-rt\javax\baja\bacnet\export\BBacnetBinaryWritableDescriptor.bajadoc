<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor" name="BBacnetBinaryWritableDescriptor" packageName="javax.baja.bacnet.export" public="true" abstract="true">
<description>
BBacnetBinaryWritableDescriptor exposes a ControlPoint as a&#xa; commandable binary point.  It is the superclass for Binary Output&#xa; and commandable Binary Value points.
</description>
<tag name="@author"><PERSON> on 19 Feb 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.export.BBacnetBinaryPointDescriptor"/>
</extends>
<implements>
<type class="javax.baja.bacnet.export.BacnetWritableDescriptor"/>
</implements>
<property name="bacnetWritable" flags="rh">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;bacnetWritable&lt;/code&gt; property.
</description>
<tag name="@see">#getBacnetWritable</tag>
<tag name="@see">#setBacnetWritable</tag>
</property>

<!-- javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor() -->
<constructor name="BBacnetBinaryWritableDescriptor" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor.getBacnetWritable() -->
<method name="getBacnetWritable"  public="true">
<description>
Get the &lt;code&gt;bacnetWritable&lt;/code&gt; property.
</description>
<tag name="@see">#bacnetWritable</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor.setBacnetWritable(java.lang.String) -->
<method name="setBacnetWritable"  public="true">
<description>
Set the &lt;code&gt;bacnetWritable&lt;/code&gt; property.
</description>
<tag name="@see">#bacnetWritable</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor.isPointTypeLegal(javax.baja.control.BControlPoint) -->
<method name="isPointTypeLegal"  protected="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
BBacnetBinaryWritableDescriptor may only expose BBooleanWritable.
</description>
<parameter name="pt">
<type class="javax.baja.control.BControlPoint"/>
<description>
the exposed point
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the Niagara point type is legal for this point type.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor.isCommandable() -->
<method name="isCommandable"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is this export descriptor representing a BACnet object&#xa; with a Commandable Present_Value property (per the Clause 19&#xa; prioritization procedure)?&lt;p&gt;&#xa; Writable descriptors must override this to return true.
</description>
<return>
<type class="boolean"/>
<description>
true if commandable, otherwise false
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor.commandabilityRequired() -->
<method name="commandabilityRequired"  protected="true">
<description>
The priority array and relinquish default properties&#xa; are required for binary outputs but optional for binary values.
</description>
<return>
<type class="boolean"/>
<description>
true if the priority array and relinquish default properties&#xa; should be setup as required properties
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor.readProperty(int, int) -->
<method name="readProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor.writeProperty(int, int, byte[], int) -->
<method name="writeProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor.addRequiredProps(java.util.Vector) -->
<method name="addRequiredProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Subclass override method to add required properties.&#xa; NOTE: You MUST call super.addRequiredProps(v) first!
</description>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
Vector containing required propertyIds.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor.addOptionalProps(java.util.Vector) -->
<method name="addOptionalProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Subclass override method to add optional properties.&#xa; NOTE: You MUST call super.addOptionalProps(v) first!
</description>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
Vector containing optional propertyIds.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor.readOptionalProperty(int, int) -->
<method name="readOptionalProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Read the value of an optional property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor.writeOptionalProperty(int, int, byte[], int) -->
<method name="writeOptionalProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the value of an optional property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor.knobAdded(javax.baja.sys.Knob, javax.baja.sys.Context) -->
<method name="knobAdded"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="knob">
<type class="javax.baja.sys.Knob"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor.knobRemoved(javax.baja.sys.Knob, javax.baja.sys.Context) -->
<method name="knobRemoved"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="knob">
<type class="javax.baja.sys.Knob"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor.doMakeWritable(javax.baja.sys.BValue) -->
<method name="doMakeWritable"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="writable">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor.bacnetWritable -->
<field name="bacnetWritable"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;bacnetWritable&lt;/code&gt; property.
</description>
<tag name="@see">#getBacnetWritable</tag>
<tag name="@see">#setBacnetWritable</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetBinaryWritableDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
