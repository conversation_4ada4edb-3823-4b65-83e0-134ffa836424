<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.sys.BAceApp" name="BAceApp" packageName="com.tridium.ace.sys" public="true">
<description>
BAceApp
</description>
<tag name="@author"><PERSON> on 4/12/2016</tag>
<extends>
<type class="com.tridium.ace.component.BAceComponent"/>
</extends>
<property name="objectId" flags="drs">
<type class="int"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="appName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;appName&lt;/code&gt; property.
</description>
<tag name="@see">#getAppName</tag>
<tag name="@see">#setAppName</tag>
</property>

<property name="appVersion" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;appVersion&lt;/code&gt; property.
</description>
<tag name="@see">#getAppVersion</tag>
<tag name="@see">#setAppVersion</tag>
</property>

<property name="frameworkVersion" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;frameworkVersion&lt;/code&gt; property.
</description>
<tag name="@see">#getFrameworkVersion</tag>
<tag name="@see">#setFrameworkVersion</tag>
</property>

<property name="scanPeriod" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;scanPeriod&lt;/code&gt; property.
</description>
<tag name="@see">#getScanPeriod</tag>
<tag name="@see">#setScanPeriod</tag>
</property>

<property name="minSleepTime" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;minSleepTime&lt;/code&gt; property.
</description>
<tag name="@see">#getMinSleepTime</tag>
<tag name="@see">#setMinSleepTime</tag>
</property>

<property name="overruns" flags="r">
<type class="long"/>
<description>
Slot for the &lt;code&gt;overruns&lt;/code&gt; property.
</description>
<tag name="@see">#getOverruns</tag>
<tag name="@see">#setOverruns</tag>
</property>

<property name="timeToSteadyState" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;timeToSteadyState&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeToSteadyState</tag>
<tag name="@see">#setTimeToSteadyState</tag>
</property>

<property name="logLevel" flags="">
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
Slot for the &lt;code&gt;logLevel&lt;/code&gt; property.
</description>
<tag name="@see">#getLogLevel</tag>
<tag name="@see">#setLogLevel</tag>
</property>

<property name="scansLevel2" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;scansLevel2&lt;/code&gt; property.
</description>
<tag name="@see">#getScansLevel2</tag>
<tag name="@see">#setScansLevel2</tag>
</property>

<property name="scansLevel3" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;scansLevel3&lt;/code&gt; property.
</description>
<tag name="@see">#getScansLevel3</tag>
<tag name="@see">#setScansLevel3</tag>
</property>

<property name="scansLevel4" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;scansLevel4&lt;/code&gt; property.
</description>
<tag name="@see">#getScansLevel4</tag>
<tag name="@see">#setScansLevel4</tag>
</property>

<property name="scanDiagEnable" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;scanDiagEnable&lt;/code&gt; property.
</description>
<tag name="@see">#getScanDiagEnable</tag>
<tag name="@see">#setScanDiagEnable</tag>
</property>

<property name="scanTime1" flags="r">
<type class="float"/>
<description>
Slot for the &lt;code&gt;scanTime1&lt;/code&gt; property.
</description>
<tag name="@see">#getScanTime1</tag>
<tag name="@see">#setScanTime1</tag>
</property>

<property name="scanTime2" flags="r">
<type class="float"/>
<description>
Slot for the &lt;code&gt;scanTime2&lt;/code&gt; property.
</description>
<tag name="@see">#getScanTime2</tag>
<tag name="@see">#setScanTime2</tag>
</property>

<property name="scanTime3" flags="r">
<type class="float"/>
<description>
Slot for the &lt;code&gt;scanTime3&lt;/code&gt; property.
</description>
<tag name="@see">#getScanTime3</tag>
<tag name="@see">#setScanTime3</tag>
</property>

<property name="scanTime4" flags="r">
<type class="float"/>
<description>
Slot for the &lt;code&gt;scanTime4&lt;/code&gt; property.
</description>
<tag name="@see">#getScanTime4</tag>
<tag name="@see">#setScanTime4</tag>
</property>

<property name="maxScanTime1" flags="r">
<type class="float"/>
<description>
Slot for the &lt;code&gt;maxScanTime1&lt;/code&gt; property.
</description>
<tag name="@see">#getMaxScanTime1</tag>
<tag name="@see">#setMaxScanTime1</tag>
</property>

<property name="maxScanTime2" flags="r">
<type class="float"/>
<description>
Slot for the &lt;code&gt;maxScanTime2&lt;/code&gt; property.
</description>
<tag name="@see">#getMaxScanTime2</tag>
<tag name="@see">#setMaxScanTime2</tag>
</property>

<property name="maxScanTime3" flags="r">
<type class="float"/>
<description>
Slot for the &lt;code&gt;maxScanTime3&lt;/code&gt; property.
</description>
<tag name="@see">#getMaxScanTime3</tag>
<tag name="@see">#setMaxScanTime3</tag>
</property>

<property name="maxScanTime4" flags="r">
<type class="float"/>
<description>
Slot for the &lt;code&gt;maxScanTime4&lt;/code&gt; property.
</description>
<tag name="@see">#getMaxScanTime4</tag>
<tag name="@see">#setMaxScanTime4</tag>
</property>

<property name="minScanTime1" flags="r">
<type class="float"/>
<description>
Slot for the &lt;code&gt;minScanTime1&lt;/code&gt; property.
</description>
<tag name="@see">#getMinScanTime1</tag>
<tag name="@see">#setMinScanTime1</tag>
</property>

<property name="minScanTime2" flags="r">
<type class="float"/>
<description>
Slot for the &lt;code&gt;minScanTime2&lt;/code&gt; property.
</description>
<tag name="@see">#getMinScanTime2</tag>
<tag name="@see">#setMinScanTime2</tag>
</property>

<property name="minScanTime3" flags="r">
<type class="float"/>
<description>
Slot for the &lt;code&gt;minScanTime3&lt;/code&gt; property.
</description>
<tag name="@see">#getMinScanTime3</tag>
<tag name="@see">#setMinScanTime3</tag>
</property>

<property name="minScanTime4" flags="r">
<type class="float"/>
<description>
Slot for the &lt;code&gt;minScanTime4&lt;/code&gt; property.
</description>
<tag name="@see">#getMinScanTime4</tag>
<tag name="@see">#setMinScanTime4</tag>
</property>

<property name="utcOffset" flags="r">
<type class="int"/>
<description>
Slot for the &lt;code&gt;utcOffset&lt;/code&gt; property.
</description>
<tag name="@see">#getUtcOffset</tag>
<tag name="@see">#setUtcOffset</tag>
</property>

<property name="componentCount" flags="r">
<type class="int"/>
<description>
Slot for the &lt;code&gt;componentCount&lt;/code&gt; property.
</description>
<tag name="@see">#getComponentCount</tag>
<tag name="@see">#setComponentCount</tag>
</property>

<property name="autoForceOrder" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;autoForceOrder&lt;/code&gt; property.
</description>
<tag name="@see">#getAutoForceOrder</tag>
<tag name="@see">#setAutoForceOrder</tag>
</property>

<action name="forceExeOrder" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;forceExeOrder&lt;/code&gt; action.
</description>
<tag name="@see">#forceExeOrder()</tag>
</action>

</class>
</bajadoc>
