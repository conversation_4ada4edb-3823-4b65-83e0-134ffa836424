<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="aapup" runtimeProfile="rt" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>American AutoMatrix PUP Driver</description>
<package name="com.tridium.aapup"/>
<package name="com.tridium.aapup.datatypes"/>
<package name="com.tridium.aapup.enums"/>
<package name="com.tridium.aapup.job"/>
<package name="com.tridium.aapup.comm"/>
<package name="com.tridium.aapup.point"/>
<class packageName="com.tridium.aapup.point" name="BCvOverrideAction"><description>BCvOverrideAction is used to adjust the override value&#xa; of CVs for inputs.</description></class>
<class packageName="com.tridium.aapup.point" name="BCvResetOverrideAction"><description>BCvResetOverrideAction is used to adjust an attribute in a&#xa; PUP controller that has a &#x22;time&#x22; type.</description></class>
<class packageName="com.tridium.aapup.datatypes" name="BDownloadRegionParams"></class>
<class packageName="com.tridium.aapup.enums" name="BPingMessageSelectionEnum"><description>The BPingMessageSelectionEnum class provides enumeration message types that can&#xa; be used for ping.</description></class>
<class packageName="com.tridium.aapup.enums" name="BPupAlarmStatusEnum"></class>
<class packageName="com.tridium.aapup.point" name="BPupBooleanProxyExt"><description>BPupBooleanProxyExt is the proxy extension for bringing&#xa; PUP point information into Niagara Boolean Points.</description></class>
<class packageName="com.tridium.aapup.enums" name="BPupCommSpeedEnum"></class>
<class packageName="com.tridium.aapup" name="BPupDevice"><description>Models a PUP device</description></class>
<class packageName="com.tridium.aapup.datatypes" name="BPupDeviceDiscoveryConfig"><description>Pup Device Discovery Config, used to narrow a range of&#xa; device address to search for devices</description></class>
<class packageName="com.tridium.aapup" name="BPupDeviceFolder"><description>AaPup implementation of BDeviceFolder&#xa; Is a container for instances of BPupDevice</description></class>
<class packageName="com.tridium.aapup.job" name="BPupDiscoverDevicesJob"><description>PUP Device Discovery.</description></class>
<class packageName="com.tridium.aapup.job" name="BPupDiscoverPointsJob"><description>PUP Point Discovery.</description></class>
<class packageName="com.tridium.aapup.job" name="BPupDiscoveryDevice"><description>PUP Discovery Device.</description></class>
<class packageName="com.tridium.aapup.job" name="BPupDiscoveryPoint"><description>PUP Discovery Point models a point as it is learned but before it&#xa; is added to the database</description></class>
<class packageName="com.tridium.aapup.job" name="BPupDownloadRegionJob"><description>PUP Download SPL&#xa; This job class handles the implementation of download&#xa; of SPL programs to a given region in a controller</description></class>
<class packageName="com.tridium.aapup.point" name="BPupEnumProxyExt"><description>BPupEnumProxyExt is the proxy extension for bringing&#xa; PUP point information into Niagara Enum Points.</description></class>
<class packageName="com.tridium.aapup.job" name="BPupLearnRegionsJob"><description>PUP Region Discovery.</description></class>
<class packageName="com.tridium.aapup" name="BPupNetwork"><description>BAaPupNetwork - represents an PUP Serial Network.</description></class>
<class packageName="com.tridium.aapup.point" name="BPupNumericProxyExt"><description>BPupNumericProxyExt is the proxy extension for bringing&#xa; PUP point information into Niagara Numeric Points.</description></class>
<class packageName="com.tridium.aapup.enums" name="BPupOccupancyEnum"></class>
<class packageName="com.tridium.aapup" name="BPupPeerListFolder"><description>BPupPeerListFolder is the standard container to use&#xa; under BPupNetwork to organize BPupDeviceRecords.</description></class>
<class packageName="com.tridium.aapup.datatypes" name="BPupPeerListRecord"><description>BPupPeerListRecord is a dynamic slot added to the BPupNetwork&#x27;s peer list&#xa; whenever a new device is detected on the network, whether by&#xa; discovery or passively by listening while other devices have the&#xa; token, or by virtue of being configured as a BPupDevice in the&#xa; station configuration.</description></class>
<class packageName="com.tridium.aapup.enums" name="BPupPeerTypeEnum"><description>The BPupPeerTypeEnum class provides enumeration of American&#xa; Automatrix Public Unitary Protocol (PUP) Peer Types, for selection&#xa; of how Niagara should interact with the PUP network.</description></class>
<class packageName="com.tridium.aapup.point" name="BPupPointDeviceExt"><description>aapup implementation of BPointDeviceExt</description></class>
<class packageName="com.tridium.aapup.datatypes" name="BPupPointDiscoveryConfig"><description>Pup Point Discovery Config, used to govern what&#xa; channels and attribs learned during the learn process</description></class>
<class packageName="com.tridium.aapup.point" name="BPupPointFolder"><description>aapup implementation of BPointFolder</description></class>
<class packageName="com.tridium.aapup.enums" name="BPupPrewriteAttributeEnum"></class>
<class packageName="com.tridium.aapup.enums" name="BPupProgramStatusEnum"></class>
<class packageName="com.tridium.aapup.point" name="BPupProxyExt"><description>BPupProxyExt is the base proxy extension for bringing&#xa; point information from all PUP device types into Niagara.</description></class>
<class packageName="com.tridium.aapup.datatypes" name="BPupRegionRecord"><description>BPupRegionRecord is a dynamic slot added to the BPupDevice by the learn regions job</description></class>
<class packageName="com.tridium.aapup" name="BPupRegionsFolder"><description>BPupRegionsFolder is the standard container to use&#xa; under BPupDevice to organize BPupRegionRecords.</description></class>
<class packageName="com.tridium.aapup.enums" name="BPupScalingTypeEnum"></class>
<class packageName="com.tridium.aapup.point" name="BPupStringProxyExt"><description>BPupStringProxyExt is the proxy extension for bringing&#xa; PUP point information into Niagara String Points.</description></class>
<class packageName="com.tridium.aapup.datatypes" name="BPupTokenPassConfig"><description>BPupTokenPassConfig is a folder for storing configuration properties&#xa; for token passing scheme.</description></class>
<class packageName="com.tridium.aapup.comm" name="BPupUnsolicitedReceive"><description>This class customizes unsolicited receive handling for the&#xa; aapup driver.</description></class>
<class packageName="com.tridium.aapup.job" name="BPupUploadRegionJob"><description>PUP Upload Region Job&#xa; This job class handles the implementation of upload&#xa; of SPL programs from a given region in a controller to a file</description></class>
<class packageName="com.tridium.aapup.enums" name="BPupWeekdayEnum"></class>
<class packageName="com.tridium.aapup.point" name="BSetDateAction"><description>BSetDateAction is used to adjust an attribute in a&#xa; PUP controller that has a &#x22;date&#x22; type (E4 &amp; E3)</description></class>
<class packageName="com.tridium.aapup.point" name="BSetTimeAction"><description>BSetTimeAction is used to adjust an attribute in a&#xa; PUP controller that has a &#x22;time&#x22; type.</description></class>
<class packageName="com.tridium.aapup.datatypes" name="BUploadRegionParams"></class>
</module>
</bajadoc>
