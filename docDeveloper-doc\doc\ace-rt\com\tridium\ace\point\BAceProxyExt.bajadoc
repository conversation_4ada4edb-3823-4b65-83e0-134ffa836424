<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.point.BAceProxyExt" name="BAceProxyExt" packageName="com.tridium.ace.point" public="true">
<description>
BAceAbstractProxyExt
</description>
<tag name="@author"><PERSON> on 02-Sep-16</tag>
<extends>
<type class="javax.baja.driver.point.BProxyExt"/>
</extends>
<implements>
<type class="javax.baja.util.ICoalesceable"/>
</implements>
<property name="compName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;compName&lt;/code&gt; property.
</description>
<tag name="@see">#getCompName</tag>
<tag name="@see">#setCompName</tag>
</property>

<property name="propName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;propName&lt;/code&gt; property.
</description>
<tag name="@see">#getPropName</tag>
<tag name="@see">#setPropName</tag>
</property>

<property name="compId" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;compId&lt;/code&gt; property.
</description>
<tag name="@see">#getCompId</tag>
<tag name="@see">#setCompId</tag>
</property>

<property name="propId" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;propId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropId</tag>
<tag name="@see">#setPropId</tag>
</property>

<property name="dataType" flags="">
<type class="com.tridium.ace.enums.BAcePrimitiveTypeEnum"/>
<description>
Slot for the &lt;code&gt;dataType&lt;/code&gt; property.
</description>
<tag name="@see">#getDataType</tag>
<tag name="@see">#setDataType</tag>
</property>

<property name="slotType" flags="">
<type class="com.tridium.ace.enums.BAceSlotTypeEnum"/>
<description>
Slot for the &lt;code&gt;slotType&lt;/code&gt; property.
</description>
<tag name="@see">#getSlotType</tag>
<tag name="@see">#setSlotType</tag>
</property>

</class>
</bajadoc>
