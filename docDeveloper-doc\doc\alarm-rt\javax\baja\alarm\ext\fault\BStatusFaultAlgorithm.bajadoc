<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.ext.fault.BStatusFaultAlgorithm" name="BStatusFaultAlgorithm" packageName="javax.baja.alarm.ext.fault" public="true">
<description>
BStatusAlgorithm allows alarming based on the ControlsPoint&#x27;s status value.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Dec 04</tag>
<tag name="@version">$Revision: 7$ $Date: 7/13/11 10:59:23 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.alarm.ext.fault.BTwoStateFaultAlgorithm"/>
</extends>
<property name="faultValues" flags="">
<type class="javax.baja.status.BStatus"/>
<description>
Slot for the &lt;code&gt;faultValues&lt;/code&gt; property.&#xa; Set of allowed fault statuses.  &#x22;Disabled&#x22; and &#x22;Stale&#x22; are excluded because&#xa; BAlarmSourceExt does not evaluate alarm algorithms if the control point&#x27;s status is one of&#xa; these status values.
</description>
<tag name="@see">#getFaultValues</tag>
<tag name="@see">#setFaultValues</tag>
</property>

<!-- javax.baja.alarm.ext.fault.BStatusFaultAlgorithm() -->
<constructor name="BStatusFaultAlgorithm" public="true">
<description/>
</constructor>

<!-- javax.baja.alarm.ext.fault.BStatusFaultAlgorithm.getFaultValues() -->
<method name="getFaultValues"  public="true">
<description>
Get the &lt;code&gt;faultValues&lt;/code&gt; property.&#xa; Set of allowed fault statuses.  &#x22;Disabled&#x22; and &#x22;Stale&#x22; are excluded because&#xa; BAlarmSourceExt does not evaluate alarm algorithms if the control point&#x27;s status is one of&#xa; these status values.
</description>
<tag name="@see">#faultValues</tag>
<return>
<type class="javax.baja.status.BStatus"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BStatusFaultAlgorithm.setFaultValues(javax.baja.status.BStatus) -->
<method name="setFaultValues"  public="true">
<description>
Set the &lt;code&gt;faultValues&lt;/code&gt; property.&#xa; Set of allowed fault statuses.  &#x22;Disabled&#x22; and &#x22;Stale&#x22; are excluded because&#xa; BAlarmSourceExt does not evaluate alarm algorithms if the control point&#x27;s status is one of&#xa; these status values.
</description>
<tag name="@see">#faultValues</tag>
<parameter name="v">
<type class="javax.baja.status.BStatus"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BStatusFaultAlgorithm.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BStatusFaultAlgorithm.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BStatusFaultAlgorithm.writeAlarmData(javax.baja.status.BStatusValue, java.util.Map) -->
<method name="writeAlarmData"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<parameter name="out">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<parameter name="map">
<parameterizedType class="java.util.Map">
<args>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BStatusFaultAlgorithm.isNormal(javax.baja.status.BStatusValue) -->
<method name="isNormal"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="out">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.fault.BStatusFaultAlgorithm.faultValues -->
<field name="faultValues"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;faultValues&lt;/code&gt; property.&#xa; Set of allowed fault statuses.  &#x22;Disabled&#x22; and &#x22;Stale&#x22; are excluded because&#xa; BAlarmSourceExt does not evaluate alarm algorithms if the control point&#x27;s status is one of&#xa; these status values.
</description>
<tag name="@see">#getFaultValues</tag>
<tag name="@see">#setFaultValues</tag>
</field>

<!-- javax.baja.alarm.ext.fault.BStatusFaultAlgorithm.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
