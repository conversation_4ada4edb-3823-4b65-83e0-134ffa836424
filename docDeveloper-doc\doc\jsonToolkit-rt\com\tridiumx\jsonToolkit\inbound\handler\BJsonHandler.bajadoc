<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.handler.BJsonHandler" name="BJsonHandler" packageName="com.tridiumx.jsonToolkit.inbound.handler" public="true" abstract="true">
<description>
Handlers perform a specific task with the data routed/selected via the other inbound components.&#xa; The result topic may then be fired to report a resultant value or status message.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.inbound.BJsonInbound"/>
</extends>
<topic name="result" flags="s">
<eventType>
<type class="javax.baja.sys.BString"/>
</eventType><description>
Slot for the &lt;code&gt;result&lt;/code&gt; topic.
</description>
<tag name="@see">#fireResult</tag>
</topic>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonHandler() -->
<constructor name="BJsonHandler" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonHandler.fireResult(javax.baja.sys.BString) -->
<method name="fireResult"  public="true">
<description>
Fire an event for the &lt;code&gt;result&lt;/code&gt; topic.
</description>
<tag name="@see">#result</tag>
<parameter name="event">
<type class="javax.baja.sys.BString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonHandler.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonHandler.result(java.lang.String, java.lang.Exception) -->
<method name="result"  protected="true">
<description>
Report the handlers result
</description>
<parameter name="result">
<type class="java.lang.String"/>
</parameter>
<parameter name="e">
<type class="java.lang.Exception"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonHandler.implOutputsToClear() -->
<method name="implOutputsToClear"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<parameterizedType class="java.util.List">
<args>
<type class="javax.baja.sys.Property"/>
</args>
</parameterizedType>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonHandler.result -->
<field name="result"  public="true" static="true" final="true">
<type class="javax.baja.sys.Topic"/>
<description>
Slot for the &lt;code&gt;result&lt;/code&gt; topic.
</description>
<tag name="@see">#fireResult</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BJsonHandler.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
