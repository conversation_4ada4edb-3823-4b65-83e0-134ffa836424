<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference" name="BBacnetDeviceObjectReference" packageName="javax.baja.bacnet.datatypes" public="true">
<description>
This class represents the BBacnetDeviceObjectReference sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">23 July 2008</tag>
<tag name="@since">Niagara 3.4</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="deviceId" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;deviceId&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceId</tag>
<tag name="@see">#setDeviceId</tag>
</property>

<property name="objectId" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference() -->
<constructor name="BBacnetDeviceObjectReference" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<constructor name="BBacnetDeviceObjectReference" public="true">
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<constructor name="BBacnetDeviceObjectReference" public="true">
<parameter name="deviceId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference.getDeviceId() -->
<method name="getDeviceId"  public="true">
<description>
Get the &lt;code&gt;deviceId&lt;/code&gt; property.
</description>
<tag name="@see">#deviceId</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference.setDeviceId(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="setDeviceId"  public="true">
<description>
Set the &lt;code&gt;deviceId&lt;/code&gt; property.
</description>
<tag name="@see">#deviceId</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference.getObjectId() -->
<method name="getObjectId"  public="true">
<description>
Get the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#objectId</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference.setObjectId(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="setObjectId"  public="true">
<description>
Set the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#objectId</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference.isDeviceIdUsed() -->
<method name="isDeviceIdUsed"  public="true" final="true">
<description/>
<return>
<type class="boolean"/>
<description>
true if the deviceId is used.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true" final="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true" final="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
a descriptive string.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference.toDebugString() -->
<method name="toDebugString"  public="true" final="true">
<description>
Debug string.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference.deviceId -->
<field name="deviceId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;deviceId&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceId</tag>
<tag name="@see">#setDeviceId</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference.MAX_ENCODED_SIZE -->
<field name="MAX_ENCODED_SIZE"  public="true" static="true" final="true">
<type class="int"/>
<description>
public static final BBacnetDeviceObjectPropertyReference fromString(String s)&#xa; {&#xa; try&#xa; {&#xa; return (BBacnetDeviceObjectPropertyReference)REF.decodeFromString(s);&#xa; }&#xa; catch (IOException e)&#xa; {&#xa; return null;&#xa; }&#xa; }
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference.DEVICE_ID_TAG -->
<field name="DEVICE_ID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
BBacnetDeviceObjectReference Asn Context Tags&#xa; See Bacnet Clause 21.
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference.OBJECT_ID_TAG -->
<field name="OBJECT_ID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
