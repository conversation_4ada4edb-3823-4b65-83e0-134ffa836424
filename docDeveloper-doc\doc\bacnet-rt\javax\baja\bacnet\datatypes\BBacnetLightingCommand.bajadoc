<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetLightingCommand" name="BBacnetLightingCommand" packageName="javax.baja.bacnet.datatypes" public="true">
<description>
Recipient for an alarm to be exported to Bacnet.&#xa; &lt;p&gt;&#xa; BBacnetLightingCommand represents the Bacnet LightingCommand&#xa; choice.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="operation" flags="">
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
<description>
Slot for the &lt;code&gt;operation&lt;/code&gt; property.
</description>
<tag name="@see">#getOperation</tag>
<tag name="@see">#setOperation</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand() -->
<constructor name="BBacnetLightingCommand" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand(javax.baja.bacnet.enums.lighting.BBacnetLightingOperation) -->
<constructor name="BBacnetLightingCommand" public="true">
<parameter name="operation">
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand(javax.baja.bacnet.enums.lighting.BBacnetLightingOperation, java.lang.Float, java.lang.Float, java.lang.Float, java.lang.Long, java.lang.Integer) -->
<constructor name="BBacnetLightingCommand" public="true">
<parameter name="operation">
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
</parameter>
<parameter name="targetLevel">
<type class="java.lang.Float"/>
</parameter>
<parameter name="rampRate">
<type class="java.lang.Float"/>
</parameter>
<parameter name="stepIncrement">
<type class="java.lang.Float"/>
</parameter>
<parameter name="fadeTime">
<type class="java.lang.Long"/>
</parameter>
<parameter name="priority">
<type class="java.lang.Integer"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.getOperation() -->
<method name="getOperation"  public="true">
<description>
Get the &lt;code&gt;operation&lt;/code&gt; property.
</description>
<tag name="@see">#operation</tag>
<return>
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.setOperation(javax.baja.bacnet.enums.lighting.BBacnetLightingOperation) -->
<method name="setOperation"  public="true">
<description>
Set the &lt;code&gt;operation&lt;/code&gt; property.
</description>
<tag name="@see">#operation</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.getTargetLevel() -->
<method name="getTargetLevel"  public="true">
<description/>
<return>
<type class="java.lang.Float"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.setTargetLevel(java.lang.Float) -->
<method name="setTargetLevel"  public="true">
<description/>
<parameter name="targetLevel">
<type class="java.lang.Float"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.getRampRate() -->
<method name="getRampRate"  public="true">
<description/>
<return>
<type class="java.lang.Float"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.setRampRate(java.lang.Float) -->
<method name="setRampRate"  public="true">
<description/>
<parameter name="rampRate">
<type class="java.lang.Float"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.getStepIncrement() -->
<method name="getStepIncrement"  public="true">
<description/>
<return>
<type class="java.lang.Float"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.setStepIncrement(java.lang.Float) -->
<method name="setStepIncrement"  public="true">
<description/>
<parameter name="stepIncrement">
<type class="java.lang.Float"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.getFadeTime() -->
<method name="getFadeTime"  public="true">
<description/>
<return>
<type class="java.lang.Long"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.setFadeTime(java.lang.Long) -->
<method name="setFadeTime"  public="true">
<description/>
<parameter name="fadeTime">
<type class="java.lang.Long"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.getPriority() -->
<method name="getPriority"  public="true">
<description/>
<return>
<type class="java.lang.Integer"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.setPriority(java.lang.Integer) -->
<method name="setPriority"  public="true">
<description/>
<parameter name="priority">
<type class="java.lang.Integer"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.getFloat(java.lang.String) -->
<method name="getFloat"  public="true">
<description/>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="java.lang.Float"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.operation -->
<field name="operation"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;operation&lt;/code&gt; property.
</description>
<tag name="@see">#getOperation</tag>
<tag name="@see">#setOperation</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.OPERATION_TAG -->
<field name="OPERATION_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.TARGET_LEVEL_TAG -->
<field name="TARGET_LEVEL_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.RAMP_RATE_TAG -->
<field name="RAMP_RATE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.STEP_INCREMENT_TAG -->
<field name="STEP_INCREMENT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.FADE_TIME_TAG -->
<field name="FADE_TIME_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLightingCommand.PRIORITY_TAG -->
<field name="PRIORITY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
