<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.inbound.routing.ext">
<description/>
<class packageName="com.tridiumx.jsonToolkit.inbound.routing.ext" name="BRouterExt"><description>Called by &lt;code&gt;<see ref="com.tridiumx.jsonToolkit.inbound.BJsonInbound">BJsonInbound</see>&lt;/code&gt; to allow  extension of fixed exportMarker functionality,&#xa; subclasses are passed the incoming message to react to as they choose.</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.routing.ext" name="RouterExtException" category="exception"><description>Exception type to be thrown by registration exportMarker utils ...</description></class>
</package>
</bajadoc>
