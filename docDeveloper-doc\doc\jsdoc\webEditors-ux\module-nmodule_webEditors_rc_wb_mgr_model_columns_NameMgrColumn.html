<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>webEditors Module: nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">webEditors</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-nmodule_webEditors_rc_fe_baja_BaseEditor.html">nmodule/webEditors/rc/fe/baja/BaseEditor</a></li><li><a href="module-nmodule_webEditors_rc_fe_BaseWidget.html">nmodule/webEditors/rc/fe/BaseWidget</a></li><li><a href="module-nmodule_webEditors_rc_fe_fe.html">nmodule/webEditors/rc/fe/fe</a></li><li><a href="module-nmodule_webEditors_rc_fe_feDialogs.html">nmodule/webEditors/rc/fe/feDialogs</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_commands_MgrCommand.html">nmodule/webEditors/rc/wb/mgr/commands/MgrCommand</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_Manager.html">nmodule/webEditors/rc/wb/mgr/Manager</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html">nmodule/webEditors/rc/wb/mgr/MgrLearn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrStateHandler.html">nmodule/webEditors/rc/wb/mgr/MgrStateHandler</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrTypeInfo.html">nmodule/webEditors/rc/wb/mgr/MgrTypeInfo</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_IconMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinPropMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_NameMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyPathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_TypeMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/MgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrModel.html">nmodule/webEditors/rc/wb/mgr/model/MgrModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">nmodule/webEditors/rc/wb/table/model/Column</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_DisplayNameColumn.html">nmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_IconColumn.html">nmodule/webEditors/rc/wb/table/model/columns/IconColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_JsonObjectPropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_PropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_ToStringColumn.html">nmodule/webEditors/rc/wb/table/model/columns/ToStringColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentSource.html">nmodule/webEditors/rc/wb/table/model/ComponentSource</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentTableModel.html">nmodule/webEditors/rc/wb/table/model/ComponentTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">nmodule/webEditors/rc/wb/table/model/Row</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html">nmodule/webEditors/rc/wb/table/model/TableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_Table.html">nmodule/webEditors/rc/wb/table/Table</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeNodeRow.html">nmodule/webEditors/rc/wb/table/tree/TreeNodeRow</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeTableModel.html">nmodule/webEditors/rc/wb/table/tree/TreeTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_tree_TreeNode.html">nmodule/webEditors/rc/wb/tree/TreeNode</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="tutorials.list.html" class="dropdown-toggle" data-toggle="dropdown">Tutorials<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="tutorial-6-managers.html">Managers</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn</h1>
<section>

<header>
    
        
            
        
    
</header>


<article>
    <div class="container-overview">
    
        

        
            
<hr>
<dt>
    <h4 class="name" id="module:nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn"><span class="type-signature"></span>new (require("nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn"))(params)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>API Status: <strong>Development</strong></p>
<p><code>MgrColumn</code> subclass that allows components in a <code>MgrModel</code> to be renamed.</p>
    </div>
    

    
        <h5>Extends:</h5>
        


    <ul>
        <li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html">module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn</a></li>
    </ul>


    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id="buildCell"><span class="type-signature"></span>buildCell(row, dom)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Creates the cell's contents by calling getDisplayName on the row's proposed value<br>
or the current value if there is no proposal.<br>
If the row's subject does not have a displayName function, buildCell defers to the superclass.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>row</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dom</code></td>
            

            <td class="type">
            
                
<span class="param-type">JQuery</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html#buildCell">module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#buildCell</a>
    </li></ul></dd>
    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="coalesceRows"><span class="type-signature"></span>coalesceRows(rows)</h4>
    
    
</dt>
<dd>

    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>rows</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a>></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html#coalesceRows">module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#coalesceRows</a>
    </li></ul></dd>
    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    
    <h5>Throws:</h5>
    
            

<dl>
    <dt>
        <div class="param-desc">
        <p>if more than one row is selected</p>
        </div>
    </dt>
    <dt>
        <dl>
            <dt>
                Type
            </dt>
            <dd>
                
<span class="param-type">Error</span>



            </dd>
        </dl>
    </dt>
</dl>


        

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>the component name when a single row is selected</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">string</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="commit"><span class="type-signature"></span>commit(value, row [, params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Renames the row's <code>Component</code> subject.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>value</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>the new name</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>row</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a></span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last">
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>batch</code></td>
            

            <td class="type">
            
                
<span class="param-type">baja.comm.Batch</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html#commit">module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#commit</a>
    </li></ul></dd>
    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved when the component has<br>
been renamed.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="destroyCell"><span class="type-signature"></span>destroyCell(row, dom)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Called when the table is destroying the DOM element built for a cell in this column. This<br>
gives a <code>Column</code> implementation the chance to clean up any resources that might have been<br>
created during the earlier call to <code>#buildCell</code>, perhaps destroying a widget in the cell,<br>
for example. As with <code>#buildCell</code>, if this completes synchronously and doesn't return a<br>
Promise, the caller must wrap this in a call to <code>Promise.resolve()</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>row</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dom</code></td>
            

            <td class="type">
            
                
<span class="param-type">JQuery</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#destroyCell">module:nmodule/webEditors/rc/wb/table/model/Column#destroyCell</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>
|

<span class="param-type">*</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getColumnIcon"><span class="type-signature"></span>getColumnIcon()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return <code>object.png</code>.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html#getColumnIcon">module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getColumnIcon</a>
    </li></ul></dd>
    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getConfigFor"><span class="type-signature"></span>getConfigFor(rows)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>After coalescing the selected rows into a single value, calculate a<br>
config object to be given to <code>fe.makeFor</code> that will determine how the<br>
editor will be built to edit that value.</p>
<p>This function will typically not be called directly but serves as an<br>
override point. By default, it will simply get the coalesced value from<br>
those rows and have <code>fe.makeFor</code> build the default editor for that value.<br>
Note that this means if the coalesced value is a non-Baja value, like an<br>
array, this function <em>must</em> be overridden.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>rows</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a>></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html#getConfigFor">module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getConfigFor</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>configuration object to be given to <code>fe.makeFor</code></p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Object</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getFlags"><span class="type-signature"></span>getFlags()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the flags set on this column.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#getFlags">module:nmodule/webEditors/rc/wb/table/model/Column#getFlags</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getName"><span class="type-signature"></span>getName()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the column name or <code>null</code> if none was given.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#getName">module:nmodule/webEditors/rc/wb/table/model/Column#getName</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getProposedValueFor"><span class="type-signature"></span>getProposedValueFor(row)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the currently proposed value for the given row. If no value proposed<br>
yet, will return the actual column value (<code>getValueFor</code>).</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>row</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html#getProposedValueFor">module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getProposedValueFor</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">*</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getValueFor"><span class="type-signature"></span>getValueFor(row)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Returns the row's <code>Component</code> subject's slot name.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>row</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html#getValueFor">module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getValueFor</a>
    </li></ul></dd>
    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="hasFlags"><span class="type-signature"></span>hasFlags(flags)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if the column has <em>all</em> of the given flags.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>flags</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>flags to check for</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#hasFlags">module:nmodule/webEditors/rc/wb/table/model/Column#hasFlags</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isEditable"><span class="type-signature"></span>isEditable()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if the column is editable.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#isEditable">module:nmodule/webEditors/rc/wb/table/model/Column#isEditable</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isEditorSuitable"><span class="type-signature"></span>isEditorSuitable(editor, rows)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>We can't set multiple components to the same name. So if editing more than<br>
one row, we need the editor to be a DisplayOnlyEditor.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>editor</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_webEditors_rc_fe_baja_BaseEditor.html">module:nmodule/webEditors/rc/fe/baja/BaseEditor</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rows</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a>></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html#isEditorSuitable">module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#isEditorSuitable</a>
    </li></ul></dd>
    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>true when:<br>
there's more than one row and it's a DisplayOnlyEditor,<br>
it's a frozen slot and it's a DisplayOnlyEditor, or<br>
it's only one row and it's editable</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isExportable"><span class="type-signature"></span>isExportable()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if the column should show up in export operations, e.g. to CSV.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.8</li>
		</ul>
	</dd>
	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#isExportable">module:nmodule/webEditors/rc/wb/table/model/Column#isExportable</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isHidable"><span class="type-signature"></span>isHidable()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if the column should available in the table's show/hide context menu.<br>
Defaults to true.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#isHidable">module:nmodule/webEditors/rc/wb/table/model/Column#isHidable</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isReadonly"><span class="type-signature"></span>isReadonly()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if the column is readonly.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#isReadonly">module:nmodule/webEditors/rc/wb/table/model/Column#isReadonly</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isSortable"><span class="type-signature"></span>isSortable()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Returns a boolean indicating whether the column should not be sortable via the table headings.<br>
Defaults to true.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#isSortable">module:nmodule/webEditors/rc/wb/table/model/Column#isSortable</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isUnseen"><span class="type-signature"></span>isUnseen()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if the column is unseen.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#isUnseen">module:nmodule/webEditors/rc/wb/table/model/Column#isUnseen</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="mgrValidate"><span class="type-signature"></span>mgrValidate(model, data [, params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Allows this column to validate proposed changes.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>model</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrModel.html">module:nmodule/webEditors/rc/wb/mgr/model/MgrModel</a></span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>the model to which we're about to apply changes.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>data</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>an array of proposed changes to this column, one per<br>
row in the <code>MgrModel</code>. If a value in this array is null, no change has<br>
been proposed for that row.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last">
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>editor</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_webEditors_rc_fe_baja_BaseEditor.html">module:nmodule/webEditors/rc/fe/baja/BaseEditor</a></span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>the editor from which the proposed values were read. Note that the editor<br>
may have been used to edit other rows, so the editor's current value may<br>
not match the proposed new values.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html#mgrValidate">module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#mgrValidate</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise that resolves by default</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
        <p class="code-caption">Validating this column may require that I examine the changes I'm
about to make to other columns as well.</p>
    
    <pre class="sunlight-highlight-javascript">MyMgrColumn.prototype.mgrValidate = function (model, data, params) {
  var that = this,
      rows = model.getRows(),
      otherColumn = model.getColumn(&#x27;otherColumn&#x27;);

  //search through all MgrModel rows, and check to see that my proposed
  //change is compatible with the proposed change to another column.
  //say, i&#x27;m a &quot;password&quot; column, and the other column is a &quot;password
  //scheme&quot; column - i need to make sure that the proposed password is
  //considered valid by the proposed password scheme.

  for (var i = 0; i &lt; rows.length; i++) {
    var row = rows[i],
        myValue = data[i],
        otherValue = otherColumn.getProposedValueFor(row);

    if (myValue === null) {
      //no changes proposed for this row, so nothing to validate.
    }

    if (!isCompatible(myValue, otherValue)) {
      return Promise.reject(new Error(&#x27;incompatible values&#x27;));
    }
  }
};</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="propose"><span class="type-signature"></span>propose(value, row)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Should read the value and &quot;tentatively&quot; apply it to the<br>
selected row. In most cases this will be setting some temporary data<br>
for display-only purposes.</p>
<p>By default, will set some temporary data on the row using the column's<br>
name as a key.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>value</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>row</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html#propose">module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#propose</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setEditable"><span class="type-signature"></span>setEditable(editable)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set or unset the column's <code>EDITABLE</code> flag. Emits a <code>flagsChanged</code> event.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>editable</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#setEditable">module:nmodule/webEditors/rc/wb/table/model/Column#setEditable</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setExportable"><span class="type-signature"></span>setExportable(exportable)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set or unset whether the column should show up in export operations.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>exportable</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.8</li>
		</ul>
	</dd>
	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#setExportable">module:nmodule/webEditors/rc/wb/table/model/Column#setExportable</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setFlags"><span class="type-signature"></span>setFlags(flags)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set the column's flags.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>flags</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#setFlags">module:nmodule/webEditors/rc/wb/table/model/Column#setFlags</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    
    <h5>Throws:</h5>
    
            

<dl>
    <dt>
        <div class="param-desc">
        <p>if a non-Number given</p>
        </div>
    </dt>
    <dt>
        <dl>
            <dt>
                Type
            </dt>
            <dd>
                
<span class="param-type">Error</span>



            </dd>
        </dl>
    </dt>
</dl>


        

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setHidable"><span class="type-signature"></span>setHidable(hidable)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set or unset whether the column should be allowed to be hidden or shown by the table's<br>
show/hide context menu.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>hidable</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#setHidable">module:nmodule/webEditors/rc/wb/table/model/Column#setHidable</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setReadonly"><span class="type-signature"></span>setReadonly(readonly)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set or unset the column's <code>READONLY</code> flag. Emits a <code>flagsChanged</code> event.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>readonly</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#setReadonly">module:nmodule/webEditors/rc/wb/table/model/Column#setReadonly</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setSortable"><span class="type-signature"></span>setSortable(sortable)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set or unset whether the column should be allowed to be sorted by the table heading.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>sortable</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#setSortable">module:nmodule/webEditors/rc/wb/table/model/Column#setSortable</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setUnseen"><span class="type-signature"></span>setUnseen(unseen)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set or unset the column's <code>UNSEEN</code> flag. Emits a <code>flagsChanged</code> event.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>unseen</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#setUnseen">module:nmodule/webEditors/rc/wb/table/model/Column#setUnseen</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="toDisplayName"><span class="type-signature"></span>toDisplayName()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Returns <code>name</code> from webEditors lexicon.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html#toDisplayName">module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#toDisplayName</a>
    </li></ul></dd>
    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
</dd>

        </dl>
    

    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	webEditors Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:29:00+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>