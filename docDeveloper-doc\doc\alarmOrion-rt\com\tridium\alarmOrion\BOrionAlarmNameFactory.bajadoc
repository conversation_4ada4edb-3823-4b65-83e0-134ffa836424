<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarmOrion" runtimeProfile="rt" qualifiedName="com.tridium.alarmOrion.BOrionAlarmNameFactory" name="BOrionAlarmNameFactory" packageName="com.tridium.alarmOrion" public="true">
<description>
BOrionAlarmNameFactory provides custom naming for tables&#xa; in the alarmOrionApp.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">09 Jun 11</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="com.tridium.orion.BNameFactory"/>
</extends>
<implements>
<type class="javax.baja.agent.BIAgent"/>
</implements>
</class>
</bajadoc>
