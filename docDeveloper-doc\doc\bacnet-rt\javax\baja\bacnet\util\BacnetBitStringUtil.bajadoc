<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.util.BacnetBitStringUtil" name="BacnetBitStringUtil" packageName="javax.baja.bacnet.util" public="true">
<description>
BacnetBitStringUtil.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">25 Mar 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- javax.baja.bacnet.util.BacnetBitStringUtil() -->
<constructor name="BacnetBitStringUtil" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getBDaysOfWeekBits(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="getBDaysOfWeekBits"  public="true" static="true">
<description>
BacnetDaysOfWeek to BDaysOfWeekBits&#xa; This is handled specially because extra stuff needs to be done.&#xa; Niagara uses an integer to represent the days of the week bits,&#xa; with Sunday in bit position 0, up to Saturday in position 6 (position&#xa; 7 is unused).  Bacnet uses Monday in position 7, down to Sunday in&#xa; position 1, with position 0 unused.
</description>
<parameter name="bs">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="javax.baja.util.BDaysOfWeekBits"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getBDaysOfWeekBits(boolean[]) -->
<method name="getBDaysOfWeekBits"  public="true" static="true">
<description>
BacnetDaysOfWeek to BDaysOfWeekBits&#xa; This is handled specially because extra stuff needs to be done.&#xa; Niagara uses an integer to represent the days of the week bits,&#xa; with Sunday in bit position 0, up to Saturday in position 6 (position&#xa; 7 is unused).  Bacnet uses Monday in position 7, down to Sunday in&#xa; position 1, with position 0 unused.
</description>
<parameter name="bsbits">
<type class="boolean" dimension="1"/>
</parameter>
<return>
<type class="javax.baja.util.BDaysOfWeekBits"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getBacnetDaysOfWeek(javax.baja.util.BDaysOfWeekBits) -->
<method name="getBacnetDaysOfWeek"  public="true" static="true">
<description>
BDaysOfWeekBits to BacnetDaysOfWeek&#xa; This is handled specially because extra stuff needs to be done.&#xa; Niagara uses an integer to represent the days of the week bits,&#xa; with Sunday in bit position 0, up to Saturday in position 6 (position&#xa; 7 is unused).  Bacnet uses Monday in position 7, down to Sunday in&#xa; position 1, with position 0 unused.
</description>
<parameter name="dow">
<type class="javax.baja.util.BDaysOfWeekBits"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getBAlarmTransitionBits(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="getBAlarmTransitionBits"  public="true" static="true">
<description>
BacnetEventTransitionBits to BAlarmTransitionBits.
</description>
<parameter name="bs">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getBAlarmTransitionBits(boolean[]) -->
<method name="getBAlarmTransitionBits"  public="true" static="true">
<description>
BacnetEventTransitionBits to BAlarmTransitionBits.
</description>
<parameter name="bits">
<type class="boolean" dimension="1"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getBacnetEventTransitionBits(javax.baja.alarm.BAlarmTransitionBits) -->
<method name="getBacnetEventTransitionBits"  public="true" static="true">
<description>
BAlarmTransitionBits to BacnetEventTransitionBits.
</description>
<parameter name="at">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getBLimitEnable(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="getBLimitEnable"  public="true" static="true">
<description>
BacnetLimitEnable to BLimitEnable.
</description>
<parameter name="bs">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="javax.baja.alarm.ext.BLimitEnable"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getBLimitEnable(boolean[]) -->
<method name="getBLimitEnable"  public="true" static="true">
<description>
BacnetLimitEnable to BLimitEnable.
</description>
<parameter name="bits">
<type class="boolean" dimension="1"/>
</parameter>
<return>
<type class="javax.baja.alarm.ext.BLimitEnable"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getBacnetLimitEnable(javax.baja.alarm.ext.BLimitEnable) -->
<method name="getBacnetLimitEnable"  public="true" static="true">
<description>
BLimitEnable to BacnetLimitEnable.
</description>
<parameter name="le">
<type class="javax.baja.alarm.ext.BLimitEnable"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getBStatus(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="getBStatus"  public="true" static="true">
<description>
BacnetStatusFlags to BStatus.
</description>
<parameter name="bs">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="javax.baja.status.BStatus"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getBStatus(boolean[]) -->
<method name="getBStatus"  public="true" static="true">
<description>
BacnetStatusFlags to BStatus.
</description>
<parameter name="bits">
<type class="boolean" dimension="1"/>
</parameter>
<return>
<type class="javax.baja.status.BStatus"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getBacnetStatusFlags(javax.baja.status.BStatus) -->
<method name="getBacnetStatusFlags"  public="true" static="true">
<description>
BStatus to BacnetStatusFlags.
</description>
<parameter name="status">
<type class="javax.baja.status.BStatus"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getBacnetStatusFlags(java.lang.String) -->
<method name="getBacnetStatusFlags"  public="true" static="true">
<description>
String representation of BStatus to BacnetStatusFlags.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.decode(java.lang.String, java.lang.String) -->
<method name="decode"  public="true" static="true">
<description/>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<parameter name="bitStringName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getBitStringTags(int, int) -->
<method name="getBitStringTags"  public="true" static="true">
<description/>
<parameter name="objectType">
<type class="int"/>
</parameter>
<parameter name="propertyId">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.sys.Context"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getBitStringLength(java.lang.String) -->
<method name="getBitStringLength"  public="true" static="true">
<description/>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getTags(javax.baja.sys.Context) -->
<method name="getTags"  public="true" static="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getBitIndex(java.lang.String, java.lang.String) -->
<method name="getBitIndex"  public="true" static="true">
<description/>
<parameter name="mapName">
<type class="java.lang.String"/>
</parameter>
<parameter name="bitName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getBitStringMap(java.lang.String) -->
<method name="getBitStringMap"  public="true" static="true">
<description/>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="javax.baja.data.BIDataValue"/>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.getBitStringIndexMap(java.lang.String) -->
<method name="getBitStringIndexMap"  public="true" static="true">
<description/>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="java.lang.Integer"/>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.DEFAULT_STATUS -->
<field name="DEFAULT_STATUS"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_GENERIC_BITS -->
<field name="BACNET_GENERIC_BITS"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Bit string names.
</description>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_DAYS_OF_WEEK -->
<field name="BACNET_DAYS_OF_WEEK"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_EVENT_TRANSITION_BITS -->
<field name="BACNET_EVENT_TRANSITION_BITS"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_LIMIT_ENABLE -->
<field name="BACNET_LIMIT_ENABLE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_LOG_STATUS -->
<field name="BACNET_LOG_STATUS"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_OBJECT_TYPES_SUPPORTED -->
<field name="BACNET_OBJECT_TYPES_SUPPORTED"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_RESULT_FLAGS -->
<field name="BACNET_RESULT_FLAGS"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_SERVICES_SUPPORTED -->
<field name="BACNET_SERVICES_SUPPORTED"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_STATUS_FLAGS -->
<field name="BACNET_STATUS_FLAGS"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_GENERIC_BITS_MAP -->
<field name="BACNET_GENERIC_BITS_MAP"  public="true" static="true" final="true">
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="javax.baja.data.BIDataValue"/>
</args>
</parameterizedType>
<description>
Hash maps for known bit strings.
</description>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_DAYS_OF_WEEK_MAP -->
<field name="BACNET_DAYS_OF_WEEK_MAP"  public="true" static="true" final="true">
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="javax.baja.data.BIDataValue"/>
</args>
</parameterizedType>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_EVENT_TRANSITION_BITS_MAP -->
<field name="BACNET_EVENT_TRANSITION_BITS_MAP"  public="true" static="true" final="true">
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="javax.baja.data.BIDataValue"/>
</args>
</parameterizedType>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_LIMIT_ENABLE_MAP -->
<field name="BACNET_LIMIT_ENABLE_MAP"  public="true" static="true" final="true">
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="javax.baja.data.BIDataValue"/>
</args>
</parameterizedType>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_LOG_STATUS_MAP -->
<field name="BACNET_LOG_STATUS_MAP"  public="true" static="true" final="true">
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="javax.baja.data.BIDataValue"/>
</args>
</parameterizedType>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_OBJECT_TYPES_SUPPORTED_MAP -->
<field name="BACNET_OBJECT_TYPES_SUPPORTED_MAP"  public="true" static="true" final="true">
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="javax.baja.data.BIDataValue"/>
</args>
</parameterizedType>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_RESULT_FLAGS_MAP -->
<field name="BACNET_RESULT_FLAGS_MAP"  public="true" static="true" final="true">
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="javax.baja.data.BIDataValue"/>
</args>
</parameterizedType>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_SERVICES_SUPPORTED_MAP -->
<field name="BACNET_SERVICES_SUPPORTED_MAP"  public="true" static="true" final="true">
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="javax.baja.data.BIDataValue"/>
</args>
</parameterizedType>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_STATUS_FLAGS_MAP -->
<field name="BACNET_STATUS_FLAGS_MAP"  public="true" static="true" final="true">
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="javax.baja.data.BIDataValue"/>
</args>
</parameterizedType>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_GENERIC_BITS_INDEX_MAP -->
<field name="BACNET_GENERIC_BITS_INDEX_MAP"  public="true" static="true" final="true">
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="java.lang.Integer"/>
</args>
</parameterizedType>
<description>
Hash maps for known bit strings.
</description>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_DAYS_OF_WEEK_INDEX_MAP -->
<field name="BACNET_DAYS_OF_WEEK_INDEX_MAP"  public="true" static="true" final="true">
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="java.lang.Integer"/>
</args>
</parameterizedType>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_EVENT_TRANSITION_BITS_INDEX_MAP -->
<field name="BACNET_EVENT_TRANSITION_BITS_INDEX_MAP"  public="true" static="true" final="true">
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="java.lang.Integer"/>
</args>
</parameterizedType>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_LIMIT_ENABLE_INDEX_MAP -->
<field name="BACNET_LIMIT_ENABLE_INDEX_MAP"  public="true" static="true" final="true">
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="java.lang.Integer"/>
</args>
</parameterizedType>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_LOG_STATUS_INDEX_MAP -->
<field name="BACNET_LOG_STATUS_INDEX_MAP"  public="true" static="true" final="true">
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="java.lang.Integer"/>
</args>
</parameterizedType>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_OBJECT_TYPES_SUPPORTED_INDEX_MAP -->
<field name="BACNET_OBJECT_TYPES_SUPPORTED_INDEX_MAP"  public="true" static="true" final="true">
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="java.lang.Integer"/>
</args>
</parameterizedType>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_RESULT_FLAGS_INDEX_MAP -->
<field name="BACNET_RESULT_FLAGS_INDEX_MAP"  public="true" static="true" final="true">
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="java.lang.Integer"/>
</args>
</parameterizedType>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_SERVICES_SUPPORTED_INDEX_MAP -->
<field name="BACNET_SERVICES_SUPPORTED_INDEX_MAP"  public="true" static="true" final="true">
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="java.lang.Integer"/>
</args>
</parameterizedType>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_STATUS_FLAGS_INDEX_MAP -->
<field name="BACNET_STATUS_FLAGS_INDEX_MAP"  public="true" static="true" final="true">
<parameterizedType class="java.util.HashMap">
<args>
<type class="java.lang.String"/>
<type class="java.lang.Integer"/>
</args>
</parameterizedType>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_DAYS_OF_WEEK_FACETS -->
<field name="BACNET_DAYS_OF_WEEK_FACETS"  public="true" static="true" final="true">
<type class="javax.baja.sys.BFacets"/>
<description>
Facets for slots.
</description>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_EVENT_TRANSITION_BITS_FACETS -->
<field name="BACNET_EVENT_TRANSITION_BITS_FACETS"  public="true" static="true" final="true">
<type class="javax.baja.sys.BFacets"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_LIMIT_ENABLE_FACETS -->
<field name="BACNET_LIMIT_ENABLE_FACETS"  public="true" static="true" final="true">
<type class="javax.baja.sys.BFacets"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_LOG_STATUS_FACETS -->
<field name="BACNET_LOG_STATUS_FACETS"  public="true" static="true" final="true">
<type class="javax.baja.sys.BFacets"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_OBJECT_TYPES_SUPPORTED_FACETS -->
<field name="BACNET_OBJECT_TYPES_SUPPORTED_FACETS"  public="true" static="true" final="true">
<type class="javax.baja.sys.BFacets"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_RESULT_FLAGS_FACETS -->
<field name="BACNET_RESULT_FLAGS_FACETS"  public="true" static="true" final="true">
<type class="javax.baja.sys.BFacets"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_SERVICES_SUPPORTED_FACETS -->
<field name="BACNET_SERVICES_SUPPORTED_FACETS"  public="true" static="true" final="true">
<type class="javax.baja.sys.BFacets"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BACNET_STATUS_FLAGS_FACETS -->
<field name="BACNET_STATUS_FLAGS_FACETS"  public="true" static="true" final="true">
<type class="javax.baja.sys.BFacets"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BacnetDaysOfWeek -->
<field name="BacnetDaysOfWeek"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description>
Context instances.
</description>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BacnetEventTransitionBits -->
<field name="BacnetEventTransitionBits"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BacnetLimitEnable -->
<field name="BacnetLimitEnable"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BacnetLogStatus -->
<field name="BacnetLogStatus"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BacnetObjectTypesSupported -->
<field name="BacnetObjectTypesSupported"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BacnetResultFlags -->
<field name="BacnetResultFlags"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BacnetServicesSupported -->
<field name="BacnetServicesSupported"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BacnetBitStringUtil.BacnetStatusFlags -->
<field name="BacnetStatusFlags"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description/>
</field>

</class>
</bajadoc>
