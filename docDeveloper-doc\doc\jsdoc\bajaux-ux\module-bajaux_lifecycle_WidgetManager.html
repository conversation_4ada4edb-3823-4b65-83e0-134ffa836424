<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>bajaux Module: bajaux/lifecycle/WidgetManager</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">bajaux</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-bajaux_commands_Command.html">bajaux/commands/Command</a></li><li><a href="module-bajaux_commands_CommandGroup.html">bajaux/commands/CommandGroup</a></li><li><a href="module-bajaux_commands_ToggleCommand.html">bajaux/commands/ToggleCommand</a></li><li><a href="module-bajaux_commands_ToggleCommandGroup.html">bajaux/commands/ToggleCommandGroup</a></li><li><a href="module-bajaux_container_wb_Clipboard.html">bajaux/container/wb/Clipboard</a></li><li><a href="module-bajaux_container_wb_StringList.html">bajaux/container/wb/StringList</a></li><li><a href="module-bajaux_dragdrop_dragDropUtils.html">bajaux/dragdrop/dragDropUtils</a></li><li><a href="module-bajaux_dragdrop_Envelope.html">bajaux/dragdrop/Envelope</a></li><li><a href="module-bajaux_dragdrop_NavNodeEnvelope.html">bajaux/dragdrop/NavNodeEnvelope</a></li><li><a href="module-bajaux_dragdrop_StringEnvelope.html">bajaux/dragdrop/StringEnvelope</a></li><li><a href="module-bajaux_events.html">bajaux/events</a></li><li><a href="module-bajaux_icon_iconUtils.html">bajaux/icon/iconUtils</a></li><li><a href="module-bajaux_lifecycle_WidgetManager.html">bajaux/lifecycle/WidgetManager</a></li><li><a href="module-bajaux_mixin_batchLoadMixin.html">bajaux/mixin/batchLoadMixin</a></li><li><a href="module-bajaux_mixin_batchSaveMixin.html">bajaux/mixin/batchSaveMixin</a></li><li><a href="module-bajaux_mixin_responsiveMixIn.html">bajaux/mixin/responsiveMixIn</a></li><li><a href="module-bajaux_mixin_subscriberMixIn.html">bajaux/mixin/subscriberMixIn</a></li><li><a href="module-bajaux_Properties.html">bajaux/Properties</a></li><li><a href="module-bajaux_registry_Registry.html">bajaux/registry/Registry</a></li><li><a href="module-bajaux_registry_RegistryEntry.html">bajaux/registry/RegistryEntry</a></li><li><a href="module-bajaux_spandrel.html">bajaux/spandrel</a></li><li><a href="module-bajaux_spandrel_jsx.html">bajaux/spandrel/jsx</a></li><li><a href="module-bajaux_util_CommandButton.html">bajaux/util/CommandButton</a></li><li><a href="module-bajaux_util_CommandButtonGroup.html">bajaux/util/CommandButtonGroup</a></li><li><a href="module-bajaux_util_SaveCommand.html">bajaux/util/SaveCommand</a></li><li><a href="module-bajaux_Validators.html">bajaux/Validators</a></li><li><a href="module-bajaux_Widget.html">bajaux/Widget</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="interfaces.list.html" class="dropdown-toggle" data-toggle="dropdown">Interfaces<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-bajaux_commands_Command-Undoable.html">bajaux/commands/Command~Undoable</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="tutorials.list.html" class="dropdown-toggle" data-toggle="dropdown">Tutorials<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="tutorial-10-mfw-gettingStarted.html">Getting Started - MyFirstWidget</a></li><li><a href="tutorial-20-mfw-modifying.html">Saving Modifications to Station</a></li><li><a href="tutorial-30-mfw-dashboarding.html">Making your Widget Dashboardable</a></li><li><a href="tutorial-40-tipsAndTricks.html">Tips and Tricks</a></li><li><a href="tutorial-50-spandrel.html">Building Composite Widgets With spandrel</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: bajaux/lifecycle/WidgetManager</h1>
<section>

<header>
    
        
            
                <div class="class-description"><p>WidgetManager's job is to manage the lifecycle of widgets, from the initial<br>
&quot;what kind of widget do I need?&quot; question through to the destruction of<br>
the unneeded widget.</p></div>
            
        
    
</header>


<article>
    <div class="container-overview">
    
        

        
            
<hr>
<dt>
    <h4 class="name" id="module:bajaux/lifecycle/WidgetManager"><span class="type-signature"></span>new (require("bajaux/lifecycle/WidgetManager"))(params)</h4>
    
    
</dt>
<dd>

    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>



            
            </td>

            

            

            <td class="description last">
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>registry</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_registry_Registry.html">module:bajaux/registry/Registry</a></span>



            
            </td>

            

            

            <td class="description last"><p>the registry<br>
responsible for looking up Widget types</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.10</li>
		</ul>
	</dd>
	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id="buildContext"><span class="type-signature"></span>buildContext(params)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>This method functions as the &quot;starting point&quot; for a Widget build. It<br>
receives the parameters as given by the user, and calculates a build<br>
context to be used during the rest of the initialize/load/destroy<br>
lifecycle.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_lifecycle_WidgetManager.html#~BuildParams">module:bajaux/lifecycle/WidgetManager~BuildParams</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise.&lt;<a href="module-bajaux_lifecycle_WidgetManager.html#~BuildContext">module:bajaux/lifecycle/WidgetManager~BuildContext</a>></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="buildFor"><span class="type-signature"></span>buildFor(params [, widget])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Instantiates, initializes, and loads a value into a new Widget as defined<br>
by the input parameters.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_lifecycle_WidgetManager.html#~BuildParams">module:bajaux/lifecycle/WidgetManager~BuildParams</a></span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>widget</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_Widget.html">module:bajaux/Widget</a></span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>if present, skip the instantiation<br>
and just initialize/load the given widget instance.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>resolves to the widget after it<br>
has been initialized and loaded.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise.&lt;<a href="module-bajaux_Widget.html">module:bajaux/Widget</a>></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="deriveConfiguredConstructor"><span class="type-signature"></span>deriveConfiguredConstructor(params)</h4>
    
    
</dt>
<dd>

    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_lifecycle_WidgetManager.html#~BuildParams">module:bajaux/lifecycle/WidgetManager~BuildParams</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>the constructor, as configured via<br>
the <code>type</code> parameter, if present; otherwise undefined. Override to<br>
define other methods of examining params to derive a directly-configured<br>
constructor.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">function</span>
|

<span class="param-type">Promise.&lt;function()></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="destroy"><span class="type-signature"></span>destroy(widget)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Destroy the widget.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>widget</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_Widget.html">module:bajaux/Widget</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="error"><span class="type-signature"></span>error(err)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>This method is called when an error is encountered with a Widget.</p>
<p>If there is an installed error hook on this manager, it will be invoked.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>err</code></td>
            

            <td class="type">
            
                
<span class="param-type">Error</span>



            
            </td>

            

            

            <td class="description last"><p>the error from the Widget</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>If an error hook is installed, this will<br>
resolve once the error hook is finished. If no error hook is installed,<br>
this will reject with the provided error.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="initialize"><span class="type-signature"></span>initialize(widget, buildContext)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Initialize the widget into the DOM element as specified in the build<br>
context.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>widget</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_Widget.html">module:bajaux/Widget</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>buildContext</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_lifecycle_WidgetManager.html#~BuildContext">module:bajaux/lifecycle/WidgetManager~BuildContext</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="installHooks"><span class="type-signature"></span>installHooks(hooks)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Install hooks to be invoked at various stages of a widget lifecycle.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>hooks</code></td>
            

            <td class="type">
            
                
<span class="param-type">module:bajaux/lifecycle/WidgetManager~BuildHooks</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="instantiate"><span class="type-signature"></span>instantiate(buildContext)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Create a new Widget instance from the build context. If no widget<br>
constructor could be determined, default to a<br>
ToStringWidget.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>buildContext</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_lifecycle_WidgetManager.html#~BuildContext">module:bajaux/lifecycle/WidgetManager~BuildContext</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-bajaux_Widget.html">module:bajaux/Widget</a></span>
|

<span class="param-type">Promise.&lt;<a href="module-bajaux_Widget.html">module:bajaux/Widget</a>></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="load"><span class="type-signature"></span>load(widget, buildContext)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Load the value from the build context into the widget.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>widget</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_Widget.html">module:bajaux/Widget</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>buildContext</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_lifecycle_WidgetManager.html#~BuildContext">module:bajaux/lifecycle/WidgetManager~BuildContext</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="makeFor"><span class="type-signature"></span>makeFor(params)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Resolves a new Widget instance as defined by the input parameters, but<br>
does not initialize or load it anywhere.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_lifecycle_WidgetManager.html#~BuildParams">module:bajaux/lifecycle/WidgetManager~BuildParams</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise.&lt;<a href="module-bajaux_Widget.html">module:bajaux/Widget</a>></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="resolveConstructor"><span class="type-signature"></span>resolveConstructor(params)</h4>
    
    
</dt>
<dd>

    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_lifecycle_WidgetManager.html#~BuildParams">module:bajaux/lifecycle/WidgetManager~BuildParams</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>resolves the constructor to be<br>
used to instantiate the widget, either as configured via params or as<br>
looked up from the registry.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise.&lt;(function()|undefined)></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="resolveFromRegistry"><span class="type-signature"></span>resolveFromRegistry(params)</h4>
    
    
</dt>
<dd>

    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-bajaux_lifecycle_WidgetManager.html#~BuildParams">module:bajaux/lifecycle/WidgetManager~BuildParams</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>the constructor resolved from the registry.<br>
By default, do a simple lookup by <code>params.value</code>; override to define how<br>
registry lookups are performed.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise.&lt;function()></span>



    </dd>
</dl>


        

    
</dd>

        </dl>
    

    
        <h3 class="subsection-title">Type Definitions</h3>

        <dl>
                
<hr>
<dt class="name" id="~BuildContext">
    <h4 id="~BuildContext">BuildContext</h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Object describing the configuration needed to construct, initialize, and<br>
load a Widget in a DOM element.</p>
    </div>
    

    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">Object</span>



            </li>
        </ul>
    

    
<dl class="details">
    

    <h5 class="subsection-title">Properties:</h5>

    <dl>

<table class="props table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>widgetConstructor</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>Widget constructor to instantiate.<br>
If no constructor could be found it is up to the WidgetManager to decide<br>
whether to instantiate a default Widget type or to reject.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>constructorParams</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>



            
            </td>

            
                <td class="attributes">
                

                
                </td>
            

            

            <td class="description last"><p>params object to pass to the Widget<br>
constructor</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>initializeParams</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>params object to pass to the<br>
initialize() method</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>layoutParams</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>params object to pass to the layout()<br>
method</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>loadParams</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>params object to pass to the load() method</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dom</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>
|

<span class="param-type">HTMLElement</span>
|

<span class="param-type">JQuery</span>
|

<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                

                
                </td>
            

            

            <td class="description last"><p>DOM element in which to build a<br>
Widget. Will be translated by the <code>WidgetManager</code> to an appropriate type<br>
for the Widget.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>value</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>the value to load into the Widget. <code>null</code> is an<br>
acceptable loadable value. If <code>undefined</code>, no loading should be performed.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>data</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>



            
            </td>

            
                <td class="attributes">
                

                
                </td>
            

            

            <td class="description last"><p>any additional data passed by the caller into the<br>
<code>WidgetManager</code> as the <code>data</code> property. This will be passed through the<br>
build lifecycle untouched. Most useful when using lifecycle hooks to add<br>
functionality to the <code>WidgetManager</code> instance.</p></td>
        </tr>

    
    </tbody>
</table>
</dl>

    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

            
                
<hr>
<dt class="name" id="~BuildHooks%7D">
    <h4 id="~BuildHooks%7D">BuildHooks}</h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Object describing hooks to be invoked at various points in a widget's<br>
lifecycle. Each hook will be invoked with <code>widget</code> and <code>buildContext</code><br>
arguments.</p>
    </div>
    

    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">Object</span>



            </li>
        </ul>
    

    
<dl class="details">
    

    <h5 class="subsection-title">Properties:</h5>

    <dl>

<table class="props table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>instantiated</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>called immediately after a widget is constructed</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>preInitialize</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>called before <code>initialize()</code> is called</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>postInitialize</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>called after <code>initialize()</code> completes</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>preLoad</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>called before <code>load()</code> is called</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>postLoad</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>called after <code>load()</code> completes</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>error</code></td>
            

            <td class="type">
            
                
<span class="param-type">module:bajaux/lifecycle/WidgetManager~error</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>called when<br>
a widget encounters an error</p></td>
        </tr>

    
    </tbody>
</table>
</dl>

    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

            
                
<hr>
<dt class="name" id="~BuildParams">
    <h4 id="~BuildParams">BuildParams</h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Object describing the parameters that can be passed to <code>WidgetManager</code> to<br>
define a build context. Subclasses of <code>WidgetManager</code> may support<br>
additional parameters.</p>
    </div>
    

    
        <h5>Type:</h5>
        <ul>
            <li>
                
<span class="param-type">Object</span>



            </li>
        </ul>
    

    
<dl class="details">
    

    <h5 class="subsection-title">Properties:</h5>

    <dl>

<table class="props table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>type</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>a <code>bajaux/Widget</code> subclass constructor<br>
function - if given, an instance of that Widget will <em>always</em> be<br>
instantiated instead of dynamically looked up from the <code>value</code>. You can<br>
also use a RequireJS module ID that resolves to a <code>Widget</code> subclass<br>
constructor.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>value</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>the value to be loaded into the new widget, if<br>
applicable.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dom</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>
|

<span class="param-type">HTMLElement</span>
|

<span class="param-type">JQuery</span>
|

<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>the DOM element in which the<br>
new widget should be initialized, if applicable.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>properties</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>the bajaux Properties the new widget<br>
should have.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>enabled</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>set to <code>false</code> to cause the new widget to be<br>
disabled. Not used for lookups.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>readonly</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>set to <code>true</code> to cause the new widget to be<br>
readonly. Not used for lookups.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>formFactors</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Array.&lt;String></span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>the possible form factors<br>
the new widget should have. The created widget could match any of these<br>
form factors depending on what is registered in the database. If no widget<br>
is found that supports any of these form factors, then no widget will be<br>
created (even if one is present that supports a different form factor). If<br>
no form factor is given, then the widget created could be of <em>any</em> form<br>
factor.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>formFactor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>same as a <code>formFactors</code> array of length 1.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>hooks</code></td>
            

            <td class="type">
            
                
<span class="param-type">module:bajaux/lifecycle/WidgetManager~BuildHooks</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"><p>any<br>
hooks you wish to run at various stages in this widget's lifecycle. They<br>
will run immediately after any installed hooks.</p></td>
        </tr>

    
    </tbody>
</table>
</dl>

    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

            </dl>
    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	bajaux Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:53+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>