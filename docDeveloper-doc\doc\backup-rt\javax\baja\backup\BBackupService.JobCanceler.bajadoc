<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="backup" runtimeProfile="rt" qualifiedName="javax.baja.backup.BBackupService$JobCanceler" name="BBackupService.JobCanceler" packageName="javax.baja.backup" public="true" static="true" innerClass="true">
<description/>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="javax.baja.backup.BBackupService$ICanceler"/>
</implements>
<!-- javax.baja.backup.BBackupService.JobCanceler(javax.baja.job.BJob) -->
<constructor name="JobCanceler" public="true">
<parameter name="job">
<type class="javax.baja.job.BJob"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.backup.BBackupService.JobCanceler.isCanceled() -->
<method name="isCanceled"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="boolean"/>
</return>
</method>

</class>
</bajadoc>
