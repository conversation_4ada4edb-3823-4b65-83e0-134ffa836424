<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.fox.BAlarmDbChannel" name="BAlarmDbChannel" packageName="com.tridium.alarm.fox" public="true">
<description>
The AlarmDbChannel handles all messages for accessing an alarm database.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">27 Sep 2004</tag>
<tag name="@version">$Revision: 11$ $Date: 9/8/08 3:03:44 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="com.tridium.fox.sys.BFoxChannel"/>
</extends>
</class>
</bajadoc>
