<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.device.LatencyRecorder" name="LatencyRecorder" packageName="javax.baja.bacnet.device" public="true" interface="true" abstract="true" category="interface">
<description>
The LatencyRecorder interface can&#xa; be implemented to receive latency events&#xa; for interactions with BBacnetDevices.
</description>
<tag name="@author"><PERSON></tag>
<!-- javax.baja.bacnet.device.LatencyRecorder.recordLatency(long) -->
<method name="recordLatency"  public="true" abstract="true">
<description>
Called by components that send messages to a BBacnetDevice.&#xa; Primarily ping and poll messages.
</description>
<parameter name="ms">
<type class="long"/>
<description>
of latency before a response was received from a device.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.device.LatencyRecorder.isRecordingLatency() -->
<method name="isRecordingLatency"  public="true" abstract="true">
<description>
Are there any active LatencyRecorders listening for latency&#xa; events from this device?
</description>
<return>
<type class="boolean"/>
<description>
true if latency events should be generated&#xa; false if no recorders are listening
</description>
</return>
</method>

</class>
</bajadoc>
