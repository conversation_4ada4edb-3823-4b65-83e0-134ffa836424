<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.ack.BAlarmAcknowledger" name="BAlarmAcknowledger" packageName="com.tridium.alarm.ack" public="true" abstract="true">
<description>
Abstract class for a component that acknowledges alarms. Typically, a class that extends&#xa; this component will connect to a given service and receive messages. The given message is decoded&#xa; by the subclass and if it contains valid alarm look up information, the alarm will be looked up&#xa; and acknowledged.&#xa;&#xa; Any subclasses should adhere to the following...&#xa;&#xa; - Post any message processing to the Worker&#xa; - Update the necessary message counter Properties
</description>
<tag name="@author">g<PERSON><PERSON><PERSON></tag>
<tag name="@creation">16 Oct 2008</tag>
<tag name="@version">1</tag>
<tag name="@since">Niagara 3.5</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.status.BIStatus"/>
</implements>
<property name="status" flags="rtA">
<type class="javax.baja.status.BStatus"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.&#xa; The Status of the Alarm Acknowledger
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</property>

<property name="enabled" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;enabled&lt;/code&gt; property.&#xa; Determines whether alarms will be acknowledged
</description>
<tag name="@see">#getEnabled</tag>
<tag name="@see">#setEnabled</tag>
</property>

<property name="ackAlarmsFromSameSource" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;ackAlarmsFromSameSource&lt;/code&gt; property.&#xa; Acknowledge all alarms from the same source
</description>
<tag name="@see">#getAckAlarmsFromSameSource</tag>
<tag name="@see">#setAckAlarmsFromSameSource</tag>
</property>

<property name="lastAlarmAcked" flags="rtdA">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;lastAlarmAcked&lt;/code&gt; property.&#xa; The UUID of the last alarm acked
</description>
<tag name="@see">#getLastAlarmAcked</tag>
<tag name="@see">#setLastAlarmAcked</tag>
</property>

<property name="lastAlarmAckedTime" flags="rtdA">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;lastAlarmAckedTime&lt;/code&gt; property.&#xa; The time of the last alarm acked
</description>
<tag name="@see">#getLastAlarmAckedTime</tag>
<tag name="@see">#setLastAlarmAckedTime</tag>
</property>

<property name="lastAlarmAckedFailureTime" flags="rtdA">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;lastAlarmAckedFailureTime&lt;/code&gt; property.&#xa; The time of the last alarm ack failure
</description>
<tag name="@see">#getLastAlarmAckedFailureTime</tag>
<tag name="@see">#setLastAlarmAckedFailureTime</tag>
</property>

<property name="lastAlarmAckedFailureCause" flags="rtdA">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;lastAlarmAckedFailureCause&lt;/code&gt; property.&#xa; The cause of the last alarm ack failure
</description>
<tag name="@see">#getLastAlarmAckedFailureCause</tag>
<tag name="@see">#setLastAlarmAckedFailureCause</tag>
</property>

<property name="totalAlarmsAckedToday" flags="rtdA">
<type class="int"/>
<description>
Slot for the &lt;code&gt;totalAlarmsAckedToday&lt;/code&gt; property.&#xa; The total number of alarms acked today (reset at midnight)
</description>
<tag name="@see">#getTotalAlarmsAckedToday</tag>
<tag name="@see">#setTotalAlarmsAckedToday</tag>
</property>

<property name="totalAlarmAckedFailures" flags="rtdA">
<type class="int"/>
<description>
Slot for the &lt;code&gt;totalAlarmAckedFailures&lt;/code&gt; property.&#xa; The total number messages received that failed in an alarm ack (reset at midnight)
</description>
<tag name="@see">#getTotalAlarmAckedFailures</tag>
<tag name="@see">#setTotalAlarmAckedFailures</tag>
</property>

<property name="totalMessagesReceivedToday" flags="rtdA">
<type class="int"/>
<description>
Slot for the &lt;code&gt;totalMessagesReceivedToday&lt;/code&gt; property.&#xa; The total number of messages received today (reset at midnight)
</description>
<tag name="@see">#getTotalMessagesReceivedToday</tag>
<tag name="@see">#setTotalMessagesReceivedToday</tag>
</property>

<action name="resetTotals" flags="ha">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;resetTotals&lt;/code&gt; action.&#xa; Reset totals
</description>
<tag name="@see">#resetTotals()</tag>
</action>

<topic name="alarmAcked" flags="">
<eventType>
<type class="javax.baja.alarm.BAlarmRecord"/>
</eventType><description>
Slot for the &lt;code&gt;alarmAcked&lt;/code&gt; topic.&#xa; Fired when an alarm has been acknowledged
</description>
<tag name="@see">#fireAlarmAcked</tag>
</topic>

</class>
</bajadoc>
