<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetPolarity" name="BBacnetPolarity" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetPolarity represents the Bacnet Polarity&#xa; enumeration.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 7$ $Date: 12/19/01 4:35:59 PM$</tag>
<tag name="@creation">19 Jun 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;normal&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;reverse&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetPolarity.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetPolarity"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetPolarity.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetPolarity"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetPolarity.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetPolarity.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetPolarity.make(boolean) -->
<method name="make"  public="true" static="true" final="true">
<description/>
<parameter name="value">
<type class="boolean"/>
<description>
the boolean value to be represented.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetPolarity"/>
<description>
a BBacnetPolarity object with the given code.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetPolarity.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
String representation of this BEnum.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetPolarity.NORMAL -->
<field name="NORMAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for normal.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPolarity.REVERSE -->
<field name="REVERSE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for reverse.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPolarity.normal -->
<field name="normal"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPolarity"/>
<description>
BBacnetPolarity constant for normal.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPolarity.reverse -->
<field name="reverse"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPolarity"/>
<description>
BBacnetPolarity constant for reverse.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPolarity.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPolarity"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPolarity.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
