<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="alarm" runtimeProfile="rt" name="javax.baja.alarm">
<description>
&lt;p&gt;&#xa;This package provides the core functionality for the lifecycle management of&#xa;alarms within the Baja framework.&#xa;&lt;/p&gt;&#xa;&#xa;&lt;p&gt;&#xa;Every alarm has an &lt;i&gt;alarm class&lt;/i&gt; which defines how and where the alarm is&#xa;routed.  Examples of alarm classes might include:&#xa;  &lt;ul&gt;&#xa;    &lt;li&gt;HVAC alarms&lt;/li&gt;&#xa;    &lt;li&gt;Fire alarms&lt;/li&gt;&#xa;    &lt;li&gt;Security alarms&lt;/li&gt;&#xa;  &lt;/ul&gt;&#xa;Alarm classes are represented by &lt;code&gt;BAlarmClass&lt;/code&gt;.  Alarm classes route alarms to&#xa;one or more recipients.&#xa;&lt;/p&gt;&#xa;&#xa;&lt;p&gt;&#xa;Every alarm class has one or more alarm recipients.  Recipients represent a component that&#xa;is able to receive an alarm and report it to some physical source.  Examples of alarm &#xa;recipients might include:&#xa;  &lt;ul&gt;&#xa;    &lt;li&gt;Printers&lt;/li&gt;&#xa;    &lt;li&gt;Email&lt;/li&gt;&#xa;    &lt;li&gt;Instant Messaging&lt;/li&gt;&#xa;  &lt;/ul&gt;    &#xa;Recipients are modelled by &lt;code&gt;BAlarmRecipient&lt;/code&gt;.&#xa;&lt;/p&gt;&#xa;&#xa;&lt;h3&gt;Alarm Lifecylce&lt;/h3&gt;&#xa;&lt;p&gt;&#xa;In a nutshell, alarms are generated by components implementing the &lt;code&gt;&#xa;AlarmSource&lt;/code&gt; interface.  Those alarms (represented by class &lt;code&gt;&#xa;BAlarmRecord&lt;/code&gt;) are then routed to the singleton alarm service, represented&#xa;by &lt;code&gt;BAlarmService&lt;/code&gt;.  The alarm service manages archival and routing of alarms.&#xa;Alarms are then routed to one or more recipients based on their alarm class.&#xa;&lt;/p&gt;&#xa;&#xa;&lt;p&gt;&#xa;Alarms may be acknowledged by external sources.  Those sources make an acknowledgment request&#xa;to an alarm recipient.  The recipient then routes the alarm back to the alarm service which in&#xa;turn routes the ack request back to the original source of the alarm.  The source may will then &#xa;issue an acknowedgment notification, indicating that the alarm is acknowledged.  The ack notification&#xa;gets routed back to the alarm service, then to the alarm class, and finally back to the recipient.&#xa;&lt;/p&gt;
</description>
<class packageName="javax.baja.alarm" name="AlarmDbConnection"><description>AlarmDbConnection provides access to an Alarm Database.</description></class>
<class packageName="javax.baja.alarm" name="AlarmSupport"><description>AlarmSupport is a support class to enabled easy alarm generation and alarm&#xa; handling for BIAlarmSources.</description></class>
<class packageName="javax.baja.alarm" name="BAckState"><description>BAckState represents the states of acknowledgement for an alarm.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmArchive"><description>An Alarm Archive is a archive of cleared alarms.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmClass"><description>A BAlarmClass object is used to group alarms that have&#xa; the same routing/handling characteristics.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmClassFolder"><description>BAlarmClassFolder is a Folder for grouping AlarmClasses under the AlarmService.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmDatabase"><description>BAlarmDatabase stores both a history of alarms and a&#xa; list of unacked alarm persistently.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmDbConfig"><description>BAlarmDbConfig is the configuration for the alarm database.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmInstructions"><description>Ordered list of User Instructions for how to handle an alarm.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmPriorities"><description>BAlarmPriorities contains the priority mapping for each &#xa; Baja alarm transition type.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmRecipient"><description>BAlarmRecipient is the super-class of all alarm recipients&#xa; in the Baja framework.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmRecord"><description>Representation of a time stamped alarm record.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmSchema"><description>BAlarmSchema is the set of name-type pairs that describe a single&#xa; record in a alarm.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmScheme"><description>The alarm scheme provides access to the alarm database.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmService"><description>The BAlarmService uses BAlarmClasses to route all alarm&#xa; messages between AlarmSources and BAlarmRecipients.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmSource"><description>An AlarmSource represents a source for in the Niagara alarm database.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmSource.SourceComparator"/>
<class packageName="javax.baja.alarm" name="BAlarmSourceInfo"><description>BAlarmSourceInfo is a data structure used to define common alarm data for use&#xa; with the AlarmSupport class.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmTransitionBits"><description>The BAlarmTransitionBits object contains a bit for each&#xa; alarm state transition type defined within Baja:&#xa;    toOffnormal&#xa;    toFault&#xa;    toNormal</description></class>
<class packageName="javax.baja.alarm" name="BArchiveAlarmProvider"><description>BArchiveAlarmProvider is added to the BAlarmService and provides mechanism to&#xa; archive cleared alarms.</description></class>
<class packageName="javax.baja.alarm" name="BRecoverableRecipient"><description>Recipient for sending remote alarms.</description></class>
<class packageName="javax.baja.alarm" name="BSourceState"><description>BSourceState represents the state of an alarm source.</description></class>
<class packageName="javax.baja.alarm" name="AlarmSpaceConnection" category="interface"><description>AlarmSpaceConnection provides access to a BIAlarmSpace.</description></class>
<class packageName="javax.baja.alarm" name="BIAlarmClassFolder" category="interface"><description>BIAlarmClassFolder is a marker interface for Objects that are folders of &#xa; BAlarmClasses.</description></class>
<class packageName="javax.baja.alarm" name="BIAlarmSource" category="interface"><description>The BIAlarmSource interface must be implemented by all&#xa; BObjects capable of generating alarms</description></class>
<class packageName="javax.baja.alarm" name="BIAlarmSpace" category="interface"><description>Common interface for all AlarmSpace implementations.</description></class>
<class packageName="javax.baja.alarm" name="BIRemoteAlarmRecipient" category="interface"><description>RemoteAlarmRecipient iis a marker interface for AlarmRecipients that are remote&#xa; to the station that generates the alarm.</description></class>
<class packageName="javax.baja.alarm" name="BIRemoteAlarmSource" category="interface"><description>The BIRemoteAlarmSource is a marker interface that is implemented by BObjects &#xa; that handle alarms sent between stations.</description></class>
<class packageName="javax.baja.alarm" name="AlarmException" category="exception"><description>Base exception class for alarm.</description></class>
</package>
</bajadoc>
