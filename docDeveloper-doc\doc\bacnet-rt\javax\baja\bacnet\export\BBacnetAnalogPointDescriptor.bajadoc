<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetAnalogPointDescriptor" name="BBacnetAnalogPointDescriptor" packageName="javax.baja.bacnet.export" public="true" abstract="true">
<description>
BBacnetAnalogPointDescriptor is the superclass for analog-type&#xa; point extensions exposing NumericPoints to Bacnet.
</description>
<tag name="@author"><PERSON> on 19 Feb 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.export.BBacnetPointDescriptor"/>
</extends>
<property name="covIncrement" flags="">
<type class="double"/>
<description>
Slot for the &lt;code&gt;covIncrement&lt;/code&gt; property.
</description>
<tag name="@see">#getCovIncrement</tag>
<tag name="@see">#setCovIncrement</tag>
</property>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor() -->
<constructor name="BBacnetAnalogPointDescriptor" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.getCovIncrement() -->
<method name="getCovIncrement"  public="true">
<description>
Get the &lt;code&gt;covIncrement&lt;/code&gt; property.
</description>
<tag name="@see">#covIncrement</tag>
<return>
<type class="double"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.setCovIncrement(double) -->
<method name="setCovIncrement"  public="true">
<description>
Set the &lt;code&gt;covIncrement&lt;/code&gt; property.
</description>
<tag name="@see">#covIncrement</tag>
<parameter name="v">
<type class="double"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.isPointTypeLegal(javax.baja.control.BControlPoint) -->
<method name="isPointTypeLegal"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
BBacnetAnalogPointDescriptor may only expose BNumericPoint.
</description>
<parameter name="pt">
<type class="javax.baja.control.BControlPoint"/>
<description>
the exposed point
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the Niagara point type is legal for this point type.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.getEventType() -->
<method name="getEventType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the BACnetEventType reported by this object.
</description>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.isValidAlarmExt(javax.baja.alarm.BIAlarmSource) -->
<method name="isValidAlarmExt"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is the given alarm source ext a valid extension for&#xa; exporting BACnet alarm properties?  This determines if the&#xa; given alarm source extension follows the appropriate algorithm&#xa; defined for the intrinsic alarming of a particular object&#xa; type as required by the BACnet specification.&lt;p&gt;&#xa; All BACnet Analog points use an OutOfRange alarm algorithm.
</description>
<parameter name="ext">
<type class="javax.baja.alarm.BIAlarmSource"/>
<description/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if valid, otherwise false.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.asnType() -->
<method name="asnType"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.getDeadBandValue(byte[]) -->
<method name="getDeadBandValue"  public="true">
<description>
Get the deadband value as Unsigned integer
</description>
<parameter name="value">
<type class="byte" dimension="1"/>
<description/>
</parameter>
<return>
<type class="double"/>
<description>
deadBand value
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.getDeadBandBytes(double) -->
<method name="getDeadBandBytes"  public="true">
<description>
Get the deadband value as Asn Byte array
</description>
<parameter name="value">
<type class="double"/>
<description/>
</parameter>
<return>
<type class="byte" dimension="1"/>
<description>
deadband bytes
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.convertToAsn(double) -->
<method name="convertToAsn"  public="true">
<description>
Override hook for LAV
</description>
<parameter name="value">
<type class="double"/>
<description>
double value to convert to asn.1
</description>
</parameter>
<return>
<type class="byte" dimension="1"/>
<description>
byte[] containing the required asn.1 formatted numeric value
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.convertFromAsn(byte[]) -->
<method name="convertFromAsn"  public="true">
<description>
Override hook for LAV
</description>
<parameter name="value">
<type class="byte" dimension="1"/>
<description>
asn.1 byte array containing a number
</description>
</parameter>
<return>
<type class="double"/>
<description>
the number decoded from the byte[]
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
<description>
if the array does not contain a properly
</description>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.appendToAsn(com.tridium.bacnet.asn.AsnOutputStream, double) -->
<method name="appendToAsn"  public="true">
<description>
Override hook for LAV
</description>
<parameter name="out">
<type class="com.tridium.bacnet.asn.AsnOutputStream"/>
<description>
asn.1 byte stream to append the numeric value
</description>
</parameter>
<parameter name="value">
<type class="double"/>
<description>
asn.1 byte array containing a number
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.readFromAsn(com.tridium.bacnet.asn.AsnInputStream) -->
<method name="readFromAsn"  public="true">
<description>
Override hook for LAV
</description>
<parameter name="in">
<type class="com.tridium.bacnet.asn.AsnInputStream"/>
</parameter>
<return>
<type class="double"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
<description>
if an unexpected ASN_TYPE is encountered
</description>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.readProperty(int, int) -->
<method name="readProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.writeProperty(int, int, byte[], int) -->
<method name="writeProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.getCovIncrement(byte[]) -->
<method name="getCovIncrement"  protected="true">
<description>
get the COV increment as real value
</description>
<parameter name="value">
<type class="byte" dimension="1"/>
<description/>
</parameter>
<return>
<type class="double"/>
<description>
cov increment
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
<description/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.addRequiredProps(java.util.Vector) -->
<method name="addRequiredProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Subclass override method to add required properties.&#xa; NOTE: You MUST call super.addRequiredProps(v) first!
</description>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
Vector containing required propertyIds.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.addOptionalProps(java.util.Vector) -->
<method name="addOptionalProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Subclass override method to add optional properties.&#xa; NOTE: You MUST call super.addOptionalProps(v) first!
</description>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
Vector containing optional propertyIds.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.readOptionalProperty(int, int) -->
<method name="readOptionalProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Read the value of an optional property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.writeOptionalProperty(int, int, byte[], int) -->
<method name="writeOptionalProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the value of an optional property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.validate() -->
<method name="validate"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Override point for subclasses to validate their exposed point&#x27;s&#xa; current state.  Default implementation does nothing.  Some points may&#xa; set the BACnet status flags to fault if the Niagara value is disallowed&#xa; for the exposed BACnet object type.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.covIncrement -->
<field name="covIncrement"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;covIncrement&lt;/code&gt; property.
</description>
<tag name="@see">#getCovIncrement</tag>
<tag name="@see">#setCovIncrement</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetAnalogPointDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
