<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.ui.BAlarmConsoleUIChannel" name="BAlarmConsoleUIChannel" packageName="com.tridium.alarm.ui" public="true">
<description>
BAlarmConsoleUIChannel is used by BAlarmConsole to handle messages between&#xa; the BConsoleRecipient and BAlarmConsole.
</description>
<tag name="@see">com.tridium.alarm.BAlarmConsoleChannel</tag>
<tag name="@author"><PERSON></tag>
<tag name="@creation">19 Jun 02</tag>
<tag name="@version">$Revision: 19$ $Date: 7/12/11 12:18:31 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="com.tridium.alarm.BAlarmConsoleChannel"/>
</extends>
</class>
</bajadoc>
