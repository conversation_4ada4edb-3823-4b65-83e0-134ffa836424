<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.BPupPeerListFolder" name="BPupPeerListFolder" packageName="com.tridium.aapup" public="true">
<description>
BPupPeerListFolder is the standard container to use&#xa; under BPupNetwork to organize BPupDeviceRecords.
</description>
<tag name="@author">Cli<PERSON></tag>
<tag name="@creation">7/15/2005 10:10AM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.0.90</tag>
<extends>
<type class="javax.baja.util.BFolder"/>
</extends>
<topic name="tableUpdated" flags="">
<eventType>
<type class="javax.baja.sys.BValue"/>
</eventType><description>
Slot for the &lt;code&gt;tableUpdated&lt;/code&gt; topic.
</description>
<tag name="@see">#fireTableUpdated</tag>
</topic>

</class>
</bajadoc>
