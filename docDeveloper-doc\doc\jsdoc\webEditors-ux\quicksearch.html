<html>
<head>
</head>
<body style="background: transparent;">
    <script src="scripts/docstrap.lib.js"></script>
    <script src="scripts/lunr.min.js"></script>
    <script src="scripts/fulltext-search.js"></script>

    <script type="text/x-docstrap-searchdb">
    {"modules.list.html":{"id":"modules.list.html","title":"Modules","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Modules Classes module:nmodule/webEditors/rc/fe/baja/BaseEditor module:nmodule/webEditors/rc/fe/BaseWidget module:nmodule/webEditors/rc/wb/mgr/Manager module:nmodule/webEditors/rc/wb/mgr/MgrStateHandler module:nmodule/webEditors/rc/wb/mgr/MgrTypeInfo module:nmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumn module:nmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumn module:nmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumn module:nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn module:nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn module:nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn module:nmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumn module:nmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumn module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn module:nmodule/webEditors/rc/wb/mgr/model/MgrModel module:nmodule/webEditors/rc/wb/table/model/Column module:nmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumn module:nmodule/webEditors/rc/wb/table/model/columns/IconColumn module:nmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumn module:nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn module:nmodule/webEditors/rc/wb/table/model/columns/ToStringColumn module:nmodule/webEditors/rc/wb/table/model/ComponentSource module:nmodule/webEditors/rc/wb/table/model/ComponentTableModel module:nmodule/webEditors/rc/wb/table/model/Row module:nmodule/webEditors/rc/wb/table/model/TableModel module:nmodule/webEditors/rc/wb/table/Table module:nmodule/webEditors/rc/wb/table/tree/TreeNodeRow module:nmodule/webEditors/rc/wb/table/tree/TreeTableModel module:nmodule/webEditors/rc/wb/tree/TreeNode Mixins module:nmodule/webEditors/rc/wb/mgr/commands/MgrCommand module:nmodule/webEditors/rc/wb/mgr/MgrLearn × Search results Close "},"tutorials.list.html":{"id":"tutorials.list.html","title":"Tutorials","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Tutorials Classes module:nmodule/webEditors/rc/fe/baja/BaseEditor module:nmodule/webEditors/rc/fe/BaseWidget module:nmodule/webEditors/rc/wb/mgr/Manager module:nmodule/webEditors/rc/wb/mgr/MgrStateHandler module:nmodule/webEditors/rc/wb/mgr/MgrTypeInfo module:nmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumn module:nmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumn module:nmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumn module:nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn module:nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn module:nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn module:nmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumn module:nmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumn module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn module:nmodule/webEditors/rc/wb/mgr/model/MgrModel module:nmodule/webEditors/rc/wb/table/model/Column module:nmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumn module:nmodule/webEditors/rc/wb/table/model/columns/IconColumn module:nmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumn module:nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn module:nmodule/webEditors/rc/wb/table/model/columns/ToStringColumn module:nmodule/webEditors/rc/wb/table/model/ComponentSource module:nmodule/webEditors/rc/wb/table/model/ComponentTableModel module:nmodule/webEditors/rc/wb/table/model/Row module:nmodule/webEditors/rc/wb/table/model/TableModel module:nmodule/webEditors/rc/wb/table/Table module:nmodule/webEditors/rc/wb/table/tree/TreeNodeRow module:nmodule/webEditors/rc/wb/table/tree/TreeTableModel module:nmodule/webEditors/rc/wb/tree/TreeNode Mixins module:nmodule/webEditors/rc/wb/mgr/commands/MgrCommand module:nmodule/webEditors/rc/wb/mgr/MgrLearn × Search results Close "},"index.html":{"id":"index.html","title":"Index","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers webEditors This module contains a number of modules and libraries, built on bajaScript and bajaux, that allow interaction with a Niagara station using HTML5, JavaScript, and CSS technologies. webEditors contains a large number of different libraries, but the public interface is centered around one primary use case: enabling you to create your own HTML5 field editors for use in the HTML5 Property Sheet and other next-gen Niagara 4 web applications. Creating a Field Editor Before beginning the process of writing a field editor, please consult the documentation for BajaScript and bajaux. In order for Baja values to be editable in the Property Sheet, Field Editors must be implemented that are capable of displaying those values and saving changes to them. In Niagara AX, these Field Editors were created for the AX Property Sheet by extending BWbFieldEditor and implementing them in Java. In Niagara 4, the Property Sheet has been reimagined using HTML5, and the field editors are implemented using the new bajaux JavaScript framework. Writing and Testing Your Code In Niagara 4, modules have been split into parts: an RT part for runtime code, a WB part for Workbench code, etc. HTML5 application code goes into a UX module part. When beginning development on a new UX module part, we highly recommend that you use some of the open source tools Tridium has created to get up and running. Please see the help section on Building JavaScript Applications for info on getting your development environment set up, and for a preliminary tutorial. In particular, the section on implementing BIJavaScript and registering as an agent on your Type will be a requirement. Different Form Factors, and Pop-Out Editors As part of the bajaux documentation, you learned about the role of Form Factors in the bajaux framework. In most cases, as in Workbench, a field editor will be used to edit a Baja value on the Property Sheet. To enable this, you'll want your BIJavaScript class to implement BIFormFactorMini, thereby marking it as a \"mini\" widget that is well-suited to fit inside a row on the Property Sheet. If this is sufficient for your use case, then you're finished - the Property Sheet will pick up on the agent registration and automatically show the correct Mini widget for your Type. But sometimes you might want to edit a widget in more detail than could comfortably fit inside a Property Sheet row. An example of this is a Facets instance - the Property Sheet row contains a small amount of information, but a button allows you to pop up a dialog with more controls, allowing you to add and remove Facets tags and edit their values. The editor that appears in the dialog will have a form factor of Compact: something in between Mini and Max. By also implementing BIFormFactorCompact, you will cause an \"edit\" button to appear in the Property Sheet row for your value. When clicked, this button will pop out the Compact editor in a dialog for more detailed editing of that value. (Note that you can do this by creating two completely separate editors, one Mini and one Compact, both registered as agents on your Type. Or, you can create just one editor, whose JavaScript implementation behaves differently based on the Widget's form factor, and whose BIJavaScript class implements both BIFormFactorMini and BIFormFactorCompact. Either solution will work.) On Field Editors and doRead() Although bajaux does not place a restriction on what type of value doRead() can resolve, it's recommended that field editors intended for use in the Property Sheet have doRead() resolve a value of the same Type that was originally loaded in. This helps to ensure proper behavior of the pop-out Edit button and validating individual rows. Public Modules in webEditors The webEditors module contains dozens of JavaScript modules and utilities. However, webEditors is new for Niagara 4 and under constant improvement, so only the modules described in this section are considered public. Tridium will make its best effort to keep these APIs as stable as possible moving forward into new versions of Niagara 4; breaking changes will be kept to a minimum and documented with release notes. Consider these modules analogous to javax.baja packages in the Niagara Framework. You are welcome to explore and make use of the other JavaScript modules in webEditors, but any not listed here may be removed or drastically changed in future versions. BaseEditor BaseEditor is a subclass of bajaux/Widget. While a vanilla Widget can load in values of any type, BaseEditors are more specialized for loading in Baja values. The set of field editors provided in the webEditors module (like StringEditor, AbsTimeEditor, etc.) all extend from BaseEditor for Baja-specific functionality. When implementing a field editor to hold a Baja value, extending BaseEditor will simplify your implementation and help it integrate more cleanly with fe. fe fe is a module that allows you to instantiate widgets for particular Baja values. Essentially, you hand it a value, and it handles the work of deciding what kind of editor to create for that value, dynamically retrieving the code for that editor, and building it into your page. fe is short for \"field editors\" because it's very common to use it to instantiate a field editor for a particular Baja value, but it is not limited to this case. For example, say I have a Component, and I want the user to be able to edit its string slot. By using fe, I don't have to worry about going out to find code for StringEditor and the like - I just pass fe some configuration and it does the work. var component = baja.$('baja:Component', { string: 'enter a string' }); // i want to edit this slot, on this Complex, and put the editor in this DOM // element. i want a Mini form factor for my editor, and set the multiLine // property to true so i can type line breaks. fe.buildFor({ slot: 'string', complex: component, dom: $('#myStringEditorGoesHere'), formFactor: 'mini', properties: { multiLine: true } }) .then(function (stringEditor) { //an editor that can edit baja:String values is now loaded into my page. //user types in a new string and clicks Save... $('#saveButton').on('click', function () { stringEditor.save().then(function () { //the string has been saved to the component! console.log(component.get('string')); }); }); }); feDialogs feDialogs handles a number of cases where you might want to show a field editor in a modal dialog, such as prompting the user for a value or for invoking an Action on a Component. feDialogs.showFor({ title: 'Enter a number between 0 and 5', value: 0, properties: { min: 0, max: 5 } }) .then(function (result) { if (result === null) { console.log('The user clicked Cancel'); } else { console.log('Result: ' + result); } }); × Search results Close "},"module-nmodule_webEditors_rc_fe_baja_BaseEditor.html":{"id":"module-nmodule_webEditors_rc_fe_baja_BaseEditor.html","title":"Module: nmodule/webEditors/rc/fe/baja/BaseEditor","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/fe/baja/BaseEditor new (require(\"nmodule/webEditors/rc/fe/baja/BaseEditor\"))( [params]) Base class for all webEditors editors for Baja values. This editor incorporates all the Widget sugar from BaseWidget and adds more Baja-specific features on top. Most Niagara field editors should extend from this class. Extends: module:nmodule/webEditors/rc/fe/BaseWidget Parameters: Name Type Argument Description params Object &lt;optional&gt; Same parameters as BaseWidget Properties Name Type Argument Description facets baja.Facets | Object &lt;optional&gt; (Deprecated - use properties) facets to use to customize this editor instance. These facets will typically come from the slot facets of a Baja value being loaded into this BaseEditor. They will be converted into transient bajaux Properties and will override any other specified Properties with the same name. This can be either a Facets instance or an object literal. Methods &lt;static&gt; typeToClass(type) Convert a BajaScript Type to a corresponding CSS class. Parameters: Name Type Description type String | Type spec Returns: Type String Example expect(BaseEditor.typeToClass('baja:String')).toBe('type-baja-String'); destroy() Removes all classes added during a call to #load. Emits a destroyed tinyevent. Overrides: module:nmodule/webEditors/rc/fe/BaseWidget#destroy Returns: call to module:bajaux/Widget#destroy Type Promise getChildEditors( [params]) Same as getChildWidgets, but is limited to instances of BaseEditor. Parameters: Name Type Argument Description params Object &lt;optional&gt; Deprecated: use `getChildWidgets` instead. initialize(dom) Every BaseWidget will add the editor class to the element and emit an initialized tinyevent when initialized. Parameters: Name Type Description dom JQuery Inherited From: module:nmodule/webEditors/rc/fe/BaseWidget#initialize Returns: call to module:bajaux/Widget#initialize Type Promise load(value [, params]) Every BaseEditor will apply a number of CSS classes to a DOM element when a value is loaded into it: editor If the loaded value is a Baja value, a number of CSS classes corresponding to the value's Type and all superTypes. Classes will be determined using typeToClass(). It will also emit a loaded tinyevent. Parameters: Name Type Argument Description value baja.Value | * params Object &lt;optional&gt; Returns: call to module:bajaux/Widget#load Type Promise setFacets(facets) Convert the given Facets into hidden, transient bajaux Properties and apply them to this editor. In most cases you'll want to use properties().setValue() directly, but this method is useful when applying Complex slot facets. Parameters: Name Type Description facets baja.Facets | Object (a baja.Facets instance or an object literal to be converted to baja.Facets) shouldValidate( [flag]) This provides an extra hook for an editor to declare itself as needing to be validated before saving or not. The default behavior is to return true if this editor is modified, or if a shouldValidate bajaux Property is present and truthy. If neither of these conditions is true, it will check all known child editors, and return true if it has a child editor that should validate. If flag is given, then the check against the shouldValidate Property will return true only if the value bitwise matches the parameter. See BaseWidget.SHOULD_VALIDATE_ON_SAVE, etc. Parameters: Name Type Argument Description flag Number &lt;optional&gt; Inherited From: module:nmodule/webEditors/rc/fe/BaseWidget#shouldValidate Returns: Type Boolean × Search results Close "},"module-nmodule_webEditors_rc_fe_BaseWidget.html":{"id":"module-nmodule_webEditors_rc_fe_BaseWidget.html","title":"Module: nmodule/webEditors/rc/fe/BaseWidget","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/fe/BaseWidget new (require(\"nmodule/webEditors/rc/fe/BaseWidget\"))( [params]) Base class for all webEditors widgets. This widget includes lots of sugar for the Widget constructor, utilities for managing nested collections of Widgets, etc. Note that this widget has no idea what BajaScript is; for more Baja-specific editor functionality, reach for BaseEditor. Extends: module:bajaux/Widget Parameters: Name Type Argument Description params Object &lt;optional&gt; Properties Name Type Argument Default Description properties module:bajaux/Properties | Object &lt;optional&gt; properties to add to this editor's underlying bajaux/Properties instance. This can be either a Properties instance or an object literal. enabled Boolean &lt;optional&gt; false to disable this editor readonly Boolean &lt;optional&gt; true to readonly this editor formFactor String &lt;optional&gt; form factor this editor should use (c.f. Widget.formfactor) keyName String &lt;optional&gt; the key name that bajaux should use to look up lexicon entries for this editor moduleName String &lt;optional&gt; 'webEditors' the module name that bajaux should use to look up lexicon entries for this editor data Object &lt;optional&gt; optional additional configuration data that may be used on a per-widget basis. This will often be used in conjunction with fe. Mixes In: tinyevents Members &lt;static&gt; SHOULD_VALIDATE :string String Property name used by shouldValidate(). Type: string &lt;static&gt; VALIDATE_ON_READ :number The shouldValidate Property should match this value if this editor should always validate on read. Type: number &lt;static&gt; VALIDATE_ON_SAVE :number The shouldValidate Property should match this value if this editor should always validate on save. Type: number &lt;static&gt; VALUE_READY_EVENT :string VALUE_READY_EVENT is a completely optional event. You may choose to trigger this event when the user has taken some action to indicate that the given value is the desired one, without actually saving the editor. Why would you want to trigger this event? Currently the best reason would be if your editor is shown in a dialog, and it has some kind of selection mechanism (dropdown, radio buttons, list etc.). This would allow the user to simply select a value and allow the dialog to close, without actually making the user click OK to save. For instance, feDialogs listens for VALUE_READY_EVENT when showing an editor in a dialog. When the event is emitted, the dialog will automatically be saved and closed. To illustrate, an OrdChooser in a dialog will show a station nav tree. When the user double-clicks a file, VALUE_READY_EVENT will be emitted. Therefore all the user must do is double-click the file to select it. Without the event, the user would have to click to select the file, then manually go down and click OK to commit the changes. You may also respond to this event when fired directly on the editor itself. Why would you do this? Certain framework modules may invert this pattern, that is, the framework may notify the editor itself that the user has chosen a value he or she is satisfied with, without saving the editor. An example of this is, again, feDialogs: when the user clicks OK, the current value will be read and used elsewhere, although the editor may not itself be saved. The two uses refer to the same use case (the user likes this value even without wishing to commit it to the station yet); they just go in two different directions: one is the editor notifying the framework, the other is the framework notifying the editor. Be careful not to re-trigger one in a handler for the other, or you may get an infinite loop! Type: string See: module:webEditors/rc/fe/feDialogs Examples dom.on('dblclick', '.listItem', function (e) { var value = $(this).text(); dom.trigger(BaseWidget.VALUE_READY_EVENT, [ value ]); }); function MyEditor() { ... var that = this; that.on(VALUE_READY_EVENT, function (newValue) { console.log('The user has chosen to commit this value: ' + newValue); //...even though it may not be written to the station yet. return that.saveToMostRecentlyUsedValues(newValue); }); } Methods destroy() Removes the editor class and emits a destroyed tinyevent. Returns: call to module:bajaux/Widget#destroy Type Promise initialize(dom) Every BaseWidget will add the editor class to the element and emit an initialized tinyevent when initialized. Parameters: Name Type Description dom JQuery Returns: call to module:bajaux/Widget#initialize Type Promise shouldValidate( [flag]) This provides an extra hook for an editor to declare itself as needing to be validated before saving or not. The default behavior is to return true if this editor is modified, or if a shouldValidate bajaux Property is present and truthy. If neither of these conditions is true, it will check all known child editors, and return true if it has a child editor that should validate. If flag is given, then the check against the shouldValidate Property will return true only if the value bitwise matches the parameter. See BaseWidget.SHOULD_VALIDATE_ON_SAVE, etc. Parameters: Name Type Argument Description flag Number &lt;optional&gt; Returns: Type Boolean × Search results Close "},"module-nmodule_webEditors_rc_fe_fe.html":{"id":"module-nmodule_webEditors_rc_fe_fe.html","title":"Module: nmodule/webEditors/rc/fe/fe","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/fe/fe Functions for registering, looking up, and instantiating editors for certain Baja types. Methods &lt;static&gt; buildFor(params [, ed]) Instantiates an editor as in makeFor, but with the added steps of initializing and loading the editor. When the promise resolves, the editor will be initialized within the DOM, and the passed value will have been loaded into the editor. Parameters: Name Type Argument Description params module:nmodule/webEditors/rc/fe/fe~FeParams Properties Name Type Argument Description initializeParams Object &lt;optional&gt; any additional parameters to be passed to the editor's initialize method loadParams Object &lt;optional&gt; any additional parameters to be passed to the editor's load method ed module:bajaux/Widget &lt;optional&gt; optionally, pass in an editor instance to just initialize and load that, skipping the makeFor step Returns: promise to be resolved with the instance of the editor (fully initialized and loaded), or rejected if invalid parameters are given (including missing dom parameter). Type Promise.&lt;module:bajaux/Widget&gt; Examples Build a raw editor for a String fe.buildFor({ value: 'my string', properties: { multiLine: true }, dom: $('#myStringEditorDiv') }).then(function (editor) { //editor is now fully initialized and loaded }); Build an editor for a slot on a component var myComponent = baja.$('baja:Component', { mySlot: 'hello world' }); fe.buildFor({ complex: myComponent, slot: 'mySlot', properties: { multiLine: true }, dom: ${'#myStringSlotEditorDiv') }).then(function (editor) { //editor is now fully initialized and loaded $('#saveButton').click(function () { editor.save().then(function () { alert('your changes are applied to the component'); }); }); }); Build a StringEditor even though you plan to load a different kind of value into it. fe.buildFor({ type: StringEditor, value: 5, dom: $('#myStringEditorDiv') }).then(function (stringEditor) { //StringEditor better be able to load the value you specified, //or this will reject instead. }); Example showing the effects of the uxFieldEditor facet (BFacets.UX_FIELD_EDITOR). By setting this slot facet to the type spec of a `BIJavaScript` implementation, you can force the usage of a particular field editor instead of relying on the agent registration. fe.buildFor({ value: 5, dom: $('#myStringEditorDiv'), properties: { uxFieldEditor: 'webEditors:StringEditor' } }).then(function (stringEditor) { //uxFieldEditor facet enforced usage of StringEditor instead of the //default NumericEditor }); &lt;static&gt; getConstructors(type [, params]) Retrieve all available widget constructors for the given type. Parameters: Name Type Argument Description type String | Type params Object &lt;optional&gt; Properties Name Type Argument Description formFactors Array.&lt;String&gt; &lt;optional&gt; Returns: promise to be resolved with an array of constructor functions. Type Promise.&lt;Array.&lt;function()&gt;&gt; &lt;static&gt; getDefaultConstructor(type [, params]) Retrieve the Widget constructor function registered for the given Type. Parameters: Name Type Argument Description type String | Type params Object &lt;optional&gt; Properties Name Type Argument Description formFactors Array.&lt;(String|Number)&gt; &lt;optional&gt; describes the form factors that the resolved constructor is required to support. These can be Strings referencing a form factor property on bajaux/Widget.formfactor, or the value itself. If a constructor matches any of these form factors it will be returned (union, not intersection). properties Object &lt;optional&gt; pass the widget properties that can help determine 'a' Widget constructor. For example, 'uxFieldEditor'. Returns: a promise to be resolved with the constructor function for the given Type, or with undefined if no constructor is registered. Note that if an invalid RequireJS module ID was passed to fe.register(), it will still look up the supertype chain in an attempt to resolve something. Type Promise.&lt;function()&gt; Examples function StringEditor() {} //extends Widget fe.register('baja:String', StringEditor, { formFactors: [ Widget.formfactor.mini ] }); //resolves StringEditor fe.getDefaultConstructor('baja:String', { formFactors: [ 'mini' ] }); //resolves undefined fe.getDefaultConstructor('baja:String', { formFactors: [ 'compact' ] }); // Will return myModule's SpecialNumericEditor instead of the default webEditors:NumericEditor fe.getDefaultConstructor('baja:Double', { properties: { uxFieldEditor: 'myModule:SpecialNumericEditor' } }); &lt;static&gt; makeFor(params) Instantiate a new editor for a value of a particular Type. Note that you will receive a constructed instance of the editor, but it is uninitialized - calling instantiate() and load() is still your job. (See buildFor.) Parameters: Name Type Description params module:nmodule/webEditors/rc/fe/fe~FeParams Returns: promise to be resolved with an editor instance, or rejected if invalid parameters are given. Type Promise.&lt;module:bajaux/Widget&gt; Example Instantiate an editor for a baja value. Note that the workflow below is easily simplified by using fe.buildFor() instead. var myString = 'my string'; fe.makeFor({ value: myString properties: { multiLine: true } }).then(function (editor) { return editor.initialize($('#myStringEditorDiv')) .then(function () { return editor.load(myString); }); }); &lt;static&gt; register(type, module [, params]) Registers a RequireJS module to a baja Type. This takes a RequireJS module ID string which resolves to a module exporting a constructor for a Widget subclass. Parameters: Name Type Argument Description type Type | String module String RequireJS module ID params Object &lt;optional&gt; Properties Name Type Argument Description formFactors Array.&lt;String&gt; &lt;optional&gt; form factors that this editor should support Returns: promise to be resolved after the module registration is complete. Note that getDefaultConstructor(), makeFor(), etc. will still work (with some possible extra network calls) before the promise is fully resolved. Promise will be rejected if RequireJS is unable to resolve a given module ID. Type Promise Example Register StringEditor on baja:String, so that it can be used to build \"mini\" editors for Strings. fe.register('baja:String', 'nmodule/webEditors/rc/fe/baja/StringEditor', { formFactors: [ Widget.formfactor.mini ] }); Type Definitions FeParams This type describes the available parameters to be passed to the various methods on fe. These values will be used both to look up the type of the desired editor, and also to construct that editor. In other words, the data to look up a widget will also be used in the same turn to construct that widget. See module:bajaux/Widget Type: module:bajaux/lifecycle/WidgetManager~BuildParams | FeSpecificParams × Search results Close "},"module-nmodule_webEditors_rc_fe_feDialogs.html":{"id":"module-nmodule_webEditors_rc_fe_feDialogs.html","title":"Module: nmodule/webEditors/rc/fe/feDialogs","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/fe/feDialogs Functions for showing field editors in modal dialogs. Useful for prompting the user to enter values, edit individual slots, and fire actions. Methods &lt;static&gt; action(params) Invoke an action on a mounted component. If the action requires a parameter, a field editor dialog will be shown to retrieve that argument from the user. Parameters: Name Type Description params Object Properties Name Type Argument Description component baja.Component the component on which to invoke the action. Must be mounted. slot String | baja.Slot the action slot to invoke. Must be a valid Action slot. actionArgument baja.Value &lt;optional&gt; Starting in Niagara 4.10, this action argument can be used instead of showing a dialog to obtain the argument. Returns: promise to be resolved with the action return value if the action was successfully invoked, resolved with null if the user clicked Cancel, or rejected if the parameters were invalid or the action could not be invoked. Type Promise &lt;static&gt; error(err [, params]) Show details about an error. Parameters: Name Type Argument Description err Error | * params Object &lt;optional&gt; Properties Name Type Argument Description title String &lt;optional&gt; command module:bajaux/commands/Command &lt;optional&gt; An optional Command to help display information on which Command failed messageSummary String &lt;optional&gt; An optional messageSummary to prepend to the Error. Returns: Type Promise &lt;static&gt; selfClosing(params) Show an editor in a dialog, similar to showFor, but with the added expectation that the editor represents a one-time interaction, like a button click, after which the dialog can be immediately closed. In other words, the \"click ok to close\" functionality is embedded in the editor itself. Only a Cancel button will be shown in the dialog itself. In order for the dialog to close, the shown editor must trigger a feDialogs.VALUE_READY_EVENT, optionally with a read value. When this event is triggered, the dialog will be closed and the promise resolved with the value passed to the event trigger. Parameters: Name Type Description params Object params to be passed to fe.buildFor Properties Name Type Argument Default Description title String &lt;optional&gt; title for the dialog delay Number &lt;optional&gt; 200 delay in ms to wait before showing a loading spinner. The spinner will disappear when the field editor has finished initializing and loading. progressCallback function &lt;optional&gt; pass a progress callback to receive notifications as the editor being shown goes through the stages of its life cycle (created, initialized, loaded). Returns: promise to be resolved when the editor has triggered its own value event. It will be resolved with any value passed to the event trigger, or with null if Cancel was clicked. Type Promise Example Trigger a VALUE_READY_EVENT to cause the dialog to be closed. // ... MyEditor.prototype.doInitialize = function (dom) { dom.on('click', 'button', function () { dom.trigger(feDialogs.VALUE_READY_EVENT, [ 'my value' ]); }); }; //... feDialogs.selfClosing({ type: MyEditor }} .then(function (value) { if (value === 'my value') { //success! } }); &lt;static&gt; showFor(params) Shows a field editor in a dialog. When the user clicks OK, the editor will be saved, committing any changes. The value that the user entered will be read from the editor and used to resolve the promise. Parameters: Name Type Description params Object params to be passed to fe.buildFor(). Properties Name Type Argument Default Description dom jQuery &lt;optional&gt; if your widget type should be instantiated into a specific kind of DOM element, it can be passed in as a parameter. Note that the given element will be appended to the dialog element itself, so do not pass in an element that is already parented. If omitted, a div will be created and used. title String &lt;optional&gt; title for the dialog delay Number &lt;optional&gt; 200 delay in ms to wait before showing a loading spinner. The spinner will disappear when the field editor has finished initializing and loading. save boolean &lt;optional&gt; set to false to specify that the dialog should not be saved on clicking OK - only the current value will be read from the editor and used to resolve the promise. onSaveError function &lt;optional&gt; when this function is set and save is set to true, the error will be handed off to this method. Otherwise, when save is true feDialogs will show whatever error caused by the save in a different dialog. This may optionally return a promise. progressCallback function &lt;optional&gt; pass a progress callback to receive notifications as the editor being shown goes through the stages of its life cycle (created, initialized, loaded), as well as whenever the editor is validated (invalid, valid). buttons Array.&lt;(module:dialogs~Button|string)&gt; &lt;optional&gt; as of Niagara 4.12, custom buttons can be specified. See examples for details. on Object.&lt;string, function()&gt; &lt;optional&gt; as of Niagara 4.12, custom handlers for bajaux events (and only bajaux events) can be specified. This is an object literal where the keys are bajaux event names and the values are event handler functions. See examples for details. Returns: promise to be resolved when the user has entered a value into the field editor and clicked OK, or rejected if the field could not be read. The promise will be resolved with the value that the user entered (or null if Cancel was clicked). Type Promise Examples feDialogs.showFor({ value: 'enter a string here (max 50 chars)', properties: { max: 50 }, progressCallback: function (msg, arg) { switch(msg) { case 'created': return console.log('editor created', arg); case 'initialized': return console.log('editor initialized', arg.jq()); case 'loaded': return console.log('editor loaded', arg.value()); case 'invalid': return console.log('validation error', arg); case 'valid': return console.log('value is valid', arg); } } }) .then(function (str) { if (str === null) { console.log('you clicked cancel'); } else { console.log('you entered: ' + str); } }); Specify custom button handlers. If the user clicks one of these custom buttons, the showFor promise will be resolved with the value resolved by its handler. feDialogs.showFor({ value: 'enter a string', buttons: [ { name: 'uppercase', displayName: 'Uppercase It', handler: (dialog, event, editor) { // the arguments to the button handler are: the Dialog instance, the click event, // and the editor being shown in the dialog. // call `dialog.keepOpen` if you are not ready for the dialog to close. the dialog will // stay open and the promise will not be resolved yet. dialog.keepOpen(); return editor.read().then((string) =&gt; editor.load(string.toUpperCase()); } }, { name: 'lowercase', displayName: 'Lowercase It', handler: (dialog, event, editor) { dialog.keepOpen(); return editor.read().then((string) =&gt; editor.load(string.toLowerCase())); } }, { // default 'ok' behavior is to read the value and resolve the promise. you don't have to // specify a handler to do this. name: 'ok' } ] }); The strings 'ok', 'cancel', 'yes', and 'no' are special - you can include them in the buttons parameter to get their default behavior. // only show the OK button, and resolve the promise with the entered value. 'yes' works the same. feDialogs.showFor({ value: 'enter a string', buttons: [ 'ok' ] }); // only show the Cancel button, and resolve the promise with null. 'no' works the same. feDialogs.showFor({ value: 'your changes will not be used', buttons: [ 'cancel' ] }); The buttons parameter can be an object literal, where the values are button definitions or handler functions. feDialogs.showFor({ value: 'Value to Edit', buttons: { ok: () =&gt; 'my custom ok result', // the value can be just a handler function. the default display name will be used. cancel: { displayName: \"Never Mind\" // omit the handler, and default handler for \"cancel\" will resolve null. }, yes: {}, // just an empty object will use the default display name and default handler. no: { handler: () =&gt; 'user clicked \"no\"' // include a handler to override the default handler. }, delete: { // for anything other than 'ok', 'cancel', 'yes', or 'no', you'll need to provide a // display name - or else just the button name will be used. displayName: 'Delete Everything', handler: () =&gt; deleteEverything() }, retry: shouldShowRetryButton() &amp;&amp; { // falsy values will cause the button _not_ to be shown. displayName: 'Try Again', handler: () =&gt; letUserTryAgain() } } }); Use the 'on' parameter to respond to any bajaux events that are triggered by the editor. const { MODIFY_EVENT } = events; feDialogs.showFor({ value: 'edit me', properties: { max: 10 }, on: { [MODIFY_EVENT]: (dialog, event, editor) { return editor.validate() .catch(() =&gt; alert('no more than 10 characters pls')); } } }); × Search results Close "},"module-nmodule_webEditors_rc_wb_mgr_commands_MgrCommand.html":{"id":"module-nmodule_webEditors_rc_wb_mgr_commands_MgrCommand.html","title":"Mixin: module:nmodule/webEditors/rc/wb/mgr/commands/MgrCommand","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Mixin: module:nmodule/webEditors/rc/wb/mgr/commands/MgrCommand module:nmodule/webEditors/rc/wb/mgr/commands/MgrCommand API Status: Development Optional mixin type used to extend a bajaux Command with extra functionality used by a Manager view. Commands don't need to apply this mixin to be functional in a bajaux Manager, this can just provide additional behavior to any Command that requires it. Methods isShownInActionBar() Returns a Boolean indicating whether this Command should be shown in the 'action bar' at the bottom of the manager view. Defaults to true. Returns: Type Boolean setShowInActionBar(show) Set whether this Command should be shown in the 'action bar' at the bottom of the manager view. The default behavior is to show all commands in the manager's command group at the bottom of the view. If called, it will typically be with a false argument show the command in the tool bar, but not in the main view. Parameters: Name Type Description show Boolean true if this instance should be shown in the manager's action bar. × Search results Close "},"module-nmodule_webEditors_rc_wb_mgr_Manager.html":{"id":"module-nmodule_webEditors_rc_wb_mgr_Manager.html","title":"Module: nmodule/webEditors/rc/wb/mgr/Manager","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/mgr/Manager &lt;abstract&gt; new (require(\"nmodule/webEditors/rc/wb/mgr/Manager\"))(params) API Status: Development View for managing groups of components, monitoring their current state and adding/removing components to the group. The concrete manager type must provide the moduleName and keyName parameters if it requires state to be saved between hyperlinks and page reloads, as these values will be used when generating the key used to index the cached state data. Due to the incubating status of the manager framework, it is not recommended that you extend Manager directly. Instead, extend DeviceMgr or PointMgr: these will provide more robust functionality for most use cases. Extends: module:nmodule/webEditors/rc/fe/baja/BaseEditor Parameters: Name Type Description params Object Properties Name Type Description moduleName String The module name, used for accessing values from the lexicon and also used to generate the key for saving state information for a manager type. keyName String The key name, used for accessing values from the lexicon and also used to generate the key for saving state information for a manager type. See: module:nmodule/driver/rc/wb/mgr/DeviceMgr module:nmodule/driver/rc/wb/mgr/PointMgr Methods buildMainTableCell(column, row, dom) Override point, allowing a Manager to customize the building of a cell within its main table. This will default to delegating the cell building to the column. Parameters: Name Type Description column module:nmodule/webEditors/rc/wb/table/model/Column row module:nmodule/webEditors/rc/wb/table/model/Row dom jQuery Returns: Type Promise | * destroy() Overrides the base destroy method to give the manager a chance to save its state before the content (such as the child table widgets) is destroyed. Overrides: module:nmodule/webEditors/rc/fe/baja/BaseEditor#destroy destroyMainTableCell(column, row, dom) Override point, allowing a Manager to customize the destruction of a cell that it created in #buildMainTableCell. The default behavior is to delegate to the column. Parameters: Name Type Description column module:nmodule/webEditors/rc/wb/table/model/Column row module:nmodule/webEditors/rc/wb/table/model/Row dom jQuery Returns: Type Promise | * doDestroy() Destroy child editors, the main table, and its model. Returns: Type Promise doInitialize(dom [, params]) Set up elements for the main table and command group. Parameters: Name Type Argument Description dom JQuery params Object &lt;optional&gt; the initialization parameters Returns: Type Promise doLoad() Initializes and loads the main table with the MgrModel. If overriding, be sure to call the super method. Returns: Type Promise finishMainTableRow(row, dom) Override point, allowing a Manager to customize the dom for a Row after the cells have been built, but before it is inserted into the main table. This allows sub-classes to perform any CSS customizations they may require at an individual row level. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom jQuery Returns: Type Promise getChildEditors( [params]) Same as getChildWidgets, but is limited to instances of BaseEditor. Parameters: Name Type Argument Description params Object &lt;optional&gt; Inherited From: module:nmodule/webEditors/rc/fe/baja/BaseEditor#getChildEditors Deprecated: use `getChildWidgets` instead. getMainTable() Get the main Table widget. Since: Niagara 4.6 Returns: Type module:nmodule/webEditors/rc/wb/table/Table getModel() Get the MgrModel backing this manager. This will return undefined until load() is called. It can safely be called from inside doLoad(). Since: Niagara 4.6 Returns: Type module:nmodule/webEditors/rc/wb/mgr/model/MgrModel getOrdBase() If the loaded MgrModel is backed by a mounted Component, then use that Component to resolve ORDs. Overrides: module:nmodule/webEditors/rc/fe/baja/BaseEditor#getOrdBase Returns: Type Promise.&lt;(baja.Component|undefined)&gt; getSubject(dom) Parameters: Name Type Description dom JQuery Returns: the selected subject of the table being clicked Type Array | null initialize(dom) Every BaseWidget will add the editor class to the element and emit an initialized tinyevent when initialized. Parameters: Name Type Description dom JQuery Inherited From: module:nmodule/webEditors/rc/fe/BaseWidget#initialize Returns: call to module:bajaux/Widget#initialize Type Promise load(value [, params]) Every BaseEditor will apply a number of CSS classes to a DOM element when a value is loaded into it: editor If the loaded value is a Baja value, a number of CSS classes corresponding to the value's Type and all superTypes. Classes will be determined using typeToClass(). It will also emit a loaded tinyevent. Parameters: Name Type Argument Description value baja.Value | * params Object &lt;optional&gt; Inherited From: module:nmodule/webEditors/rc/fe/baja/BaseEditor#load Overrides: module:nmodule/webEditors/rc/fe/baja/BaseEditor#load Returns: call to module:bajaux/Widget#load Type Promise &lt;abstract&gt; makeModel(value) Abstract method to create the MgrModel for the main database table. The method should return a Promise that will resolve to a MgrModel instance for the ComponentSource provided in the parameter. This is used to convert the value being loaded into the widget (e.g. a network) into the model used for the table widget. Parameters: Name Type Description value baja.Component the value being loaded into the Widget. Returns: the model for the main table. Type Promise.&lt;module:nmodule/webEditors/rc/wb/mgr/model/MgrModel&gt; makeStateHandler() Make a state handler instance for saving and restoring the Manager's state. Returns: Type Promise.&lt;module:nmodule/webEditors/rc/wb/mgr/MgrStateHandler&gt; restoreState() Invoke the handler created by makeStateHandler() to restore the Manager's current state when the Manager is loaded. Returns: Type Promise saveState() Invoke the handler created by makeStateHandler() to save the Manager's current state when the Manager is destroyed. setFacets(facets) Convert the given Facets into hidden, transient bajaux Properties and apply them to this editor. In most cases you'll want to use properties().setValue() directly, but this method is useful when applying Complex slot facets. Parameters: Name Type Description facets baja.Facets | Object (a baja.Facets instance or an object literal to be converted to baja.Facets) Inherited From: module:nmodule/webEditors/rc/fe/baja/BaseEditor#setFacets shouldValidate( [flag]) This provides an extra hook for an editor to declare itself as needing to be validated before saving or not. The default behavior is to return true if this editor is modified, or if a shouldValidate bajaux Property is present and truthy. If neither of these conditions is true, it will check all known child editors, and return true if it has a child editor that should validate. If flag is given, then the check against the shouldValidate Property will return true only if the value bitwise matches the parameter. See BaseWidget.SHOULD_VALIDATE_ON_SAVE, etc. Parameters: Name Type Argument Description flag Number &lt;optional&gt; Inherited From: module:nmodule/webEditors/rc/fe/BaseWidget#shouldValidate Returns: Type Boolean × Search results Close "},"module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html":{"id":"module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html","title":"Mixin: module:nmodule/webEditors/rc/wb/mgr/MgrLearn","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Mixin: module:nmodule/webEditors/rc/wb/mgr/MgrLearn module:nmodule/webEditors/rc/wb/mgr/MgrLearn API Status: Development A mixin to provide learn support to a bajaux manager view. To support discovery, in addition to applying this mixin, the target manager object must provide several functions that this mixin will use to accomplish the discovery and the creation of new components from the discovered items. The concrete manager must provide a makeLearnModel() method. This should return a Promise that will resolve to a TreeTableModel. This will be used as the data model for the discovery table. On completion of the discovery job, the manager should use the result of the job to insert items into the discovery model. The concrete manager must also provide an implementation of a doDiscover() function that will create a job (typically by invoking an action that will submit a job and return the ord), and then set the job on the manager via the setJob() function. This function will accept the job instance or the ord for a job, specified either as a baja.Ord or a string. Once the job is complete, a 'jobcomplete' tinyevent will be emitted on the manager. The concrete manager will also typically have a handler for that event, which will get the discovered items from the job by some means, and then update the discovery table. This will normally involve inserting nodes into the learn model. The manager may store arbitrary data on those nodes, which it may retrieve later via the node's value() function. The manager must also implement a getTypesForDiscoverySubject() function. This will be called when dragging an item from the discovery table to the database table or invoking the 'add' command. The function may be called several times, each time its argument will be a TreeNode representing the item to be added into to the database table. The implementation of this function is expected to return a single MgrTypeInfo instance or any array of them. These will be used to create a new component instance of the required type for the discovered node. Also to support the addition of new components, the manager should implement a function called getProposedValuesFromDiscovery(). This will be passed the tree node that was dragged from the discovery table to the database table. The function should obtain any information the manager had set on the node at discovery time and use it to create an object containing the initial values for the new component. The names of properties on the object returned by the function will be compared against the column names in the main database model. For the columns that have matching names, the values of those properties will be used to set the initial proposed values on the new row(s) when the dialog for editing the new instances is displayed. Example Add the MgrLearn mixin to a Manager subclass to add learn functionality. require([...'nmodule/webEditors/rc/wb/mgr/MgrLearn'], function (...MgrLearn) { function MyManager() { Manager.apply(this, arguments); MgrLearn(this); } MyManager.prototype = Object.create(Manager.prototype); //implement abstract functions MyManager.prototype.doDiscover = function () { ... }); Extends module:nmodule/webEditors/rc/wb/mgr/Manager Methods &lt;abstract&gt; doDiscover() Abstract method used to initiate the discovery process. What this implementation does is a matter for the concrete manager, but the typical pattern will be to invoke an Action that will submit a job, and then set that job or its Ord on the manager via the #setJob() function. Returns: Optionally return a Promise Type Promise | * getExisting(discovery) Search for the existing component that matches the given node from the discovery table. To match a component, the concrete manager subclass must contain a function named isExisting() which will be passed the discovery object and a component. The function will be used as a predicate and should return true if the given component represents the same item as the discovery table item, false otherwise. If the manager does not provide such a function, all discovery nodes will be considered as not matching any existing components. Parameters: Name Type Description discovery * a discovered object Returns: the existing component that was found to match the given discovery node, or undefined if no such match was found. Type Promise.&lt;(baja.Component|undefined)&gt; getJob() Get the discovery job currently set against the manager. Returns: Type baja.Component getLearnModel() Get the learn model. The model will have been created via a call to makeLearnModel(); a function that the concrete manager must provide. This will return the TreeTableModel resolved from the Promise. Returns: Type module:nmodule/webEditors/rc/wb/table/tree/TreeTableModel &lt;abstract&gt; getProposedValuesFromDiscovery(discovery, subject) Abstract method to get the initial values for a discovered node when it is being added to the station as a new component. This method should return an Object instance, with the values to be used by the new instances. The returned object may have a property called 'name', which will be used to set the slot name of the new component. It may also have a child object named 'values'. Each property of this object with a name that matches the name of a Column in the main table model will have that property's value used as the initial value when the component editor is displayed. Parameters: Name Type Description discovery * an object obtained from a node in discovery table. subject * the subject of the Row whose values are to be proposed. See: module:nmodule/webEditors/rc/wb/table/tree/TreeNode Returns: an object literal with the name and initial values to be used for the new component. Type Object | Promise.&lt;Object&gt; Example Return the initial values for the component name, and the 'version' and 'address' columns MyDeviceMgr.prototype.getProposedValuesFromDiscovery = function (discovery) { return { name: discovery.deviceName, values: { address: discovery.address, version: discovery.firmwareVersionMajor + '.' + discovery.firmwareVersionMinor } }; }; &lt;abstract&gt; getTypesForDiscoverySubject(discovery) Abstract method to get the Component type(s) that could be created for the given discovery node when adding it to the station as a component. If returning an array, the first element of the array should be the type that represents the best mapping for the discovery item. Parameters: Name Type Description discovery * a discovery object Returns: an array of TypeSpecs that could be constructed from this node, or a Promise resolving to one Type Promise.&lt;Array.&lt;string&gt;&gt; isExisting(discovery, component) Parameters: Name Type Description discovery * the discovery item component baja.Component component already existing in local database Returns: true if the local component already represents the discovery item Type boolean | Promise.&lt;boolean&gt; makeDiscoveryCommands() Creates and returns an array of discovery related commands. These are the LearnModeCommand (show/hide the learn pane), DiscoverCommand, CancelDiscoverCommand, AddCommand, and MatchCommand. Returns: a new array containing the discovery related commands. Type Array.&lt;module:bajaux/commands/Command&gt; &lt;abstract&gt; makeLearnModel() Abstract method used to obtain the model for the learn tree table. This should return a TreeTableModel, or a Promise that resolves to one. Returns: a tree table model that will be used by the manager's discovery table. Type Promise.&lt;module:nmodule/webEditors/rc/wb/table/tree/TreeTableModel&gt; newInstanceFromDiscoverySubject(discovery, typeInfos) Creates a new component instance from the types the manager specified for a particular node in the discovery table. If the manager returned more than one type, this default implementation will return a new instance based on the first type info. Parameters: Name Type Description discovery * an instance of a discovery object (e.g. an ndriver:NDiscoveryLeaf), dragged from the discovery table and dropped onto the database table or selected when the 'Add' command was invoked. typeInfos Array.&lt;module:nmodule/webEditors/rc/wb/mgr/MgrTypeInfo&gt; an array of MgrTypeInfos, created from the type or types returned by the manager's getTypesForDiscoverySubject() implementation. Returns: a Promise of new component instance for the discovered item based on the provided type information. Type Promise setJob(params) Attach a job to this manager, typically as part of a driver discovery process. The act of attaching a job will subscribe to it, and cause a 'jobcomplete' event to be emitted once the job is complete. A manager will typically update the learn model at that point. Parameters: Name Type Description params Object an Object literal containing the parameters for this function. Properties Name Type Argument Description jobOrOrd string | baja.Ord | baja.Component &lt;optional&gt; either a BJob instance, an Ord referencing a job, or a String containing the ord for a job. depth Number &lt;optional&gt; optional parameter that will be used when subscribing to the job once it has completed; this allows the job plus its final set of children to be subscribed. A depth of 1 will subscribe to the job, 2 will subscribe the job and its children, and so on. Subscription will default to a depth of 1 if this parameter is not specified. Returns: Type Promise × Search results Close "},"module-nmodule_webEditors_rc_wb_mgr_MgrStateHandler.html":{"id":"module-nmodule_webEditors_rc_wb_mgr_MgrStateHandler.html","title":"Module: nmodule/webEditors/rc/wb/mgr/MgrStateHandler","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/mgr/MgrStateHandler API Status: Development MgrStateHandler provides the ability to save some state for a Manager view, allowing, at the most basic level, a Manager to preserve the state of its hidden/visible columns and the visibility of the learn table when moving between views. Managers may also optionally use it to remember other state, such as the items that were found during the last discovery action. Note that although it provides similar functionality to the Java MgrState type, it differs in that this type is not used to preserve the state on its own instance. This type provides the functionality to serialize and deserialize the data to JSON; the object instance itself does not store any state and is not preserved between hyperlinks or page reloads. A Manager may provide its own functions that can be used to save and restore custom data for a particular Manager type. See the description of the save() function for information. new (require(\"nmodule/webEditors/rc/wb/mgr/MgrStateHandler\"))() Constructor not to be called directly. Call .make() instead. Methods &lt;static&gt; make(params) Takes a key string that will be used to index the state information in the storage. This key is usually derived from the Manager widget's moduleName and keyName parameters. Note that MgrStateHandler relies upon SyncedSessionStorage which can take up to 1000ms to initialize, so this may take that long to resolve. Parameters: Name Type Description params Object | String the parameters object or a string containing the key parameter. Properties Name Type Description key String the key name used to index the saved state information. Usually derived from the Manager's moduleName and keyName parameters. Returns: Type Promise.&lt;module:nmodule/webEditors/rc/wb/mgr/MgrStateHandler&gt; deserializeFromStorage() Retrieve the state from storage and return the state object. This will be called early in the manager's load process in order for it to be able to access relevant state before the model is created. The object returned from this method will be passed back to the restore function later in the load process. Returns: the stored state deserialized from JSON. Type Object doRestore(mgr, obj) Use the deserialized object to restore the state of the manager. This will restore the basic state, then invoke the custom functions on the manager itself, if they are defined. Parameters: Name Type Description mgr module:nmodule/webEditors/rc/wb/mgr/Manager the Manager instance being restored. obj Object the object containing the state to be restored Returns: Type Promise doSave(mgr, state) Save the Manager's state to the given object, prior to serialization. This will save the basic state supported for all Manager views, and then try to see if the Manager provides its own functions for saving custom data. Parameters: Name Type Description mgr module:nmodule/webEditors/rc/wb/mgr/Manager the Manager instance being saved. state Object an object instance that will contain the state to be serialized. postRestore(mgr, obj) Test whether the Manager has a postRestore function, and invoke it, if found. This allows for additional actions such as clean up or other calls to be handled after restoring the state. Parameters: Name Type Description mgr module:nmodule/webEditors/rc/wb/mgr/Manager the Manager instance being restored. obj Object Since: Niagara 4.12 Returns: The Promise returned by the Manager's function if the manager does not provide the function. Type Promise restore(mgr, state) Restore the state of a Manager. This function will retrieve the stored state information from session storage using the Manager's key. It takes a deserialized state object returned from an earlier call to deserializeFromStorage. The properties of that object will then be used to restore the prior state. The default restore implementation will restore the visibility of the table columns and the discovery tables. If the Manager provides restoreStateForKey and/or restoreStateForOrd functions to correspond to the save functions, these will be called with the deserialized versions of the objects the save functions returned. Parameters: Name Type Description mgr module:nmodule/webEditors/rc/wb/mgr/Manager the Manager instance being restored. state Object a deserialized state object with properties containing the state to be restored. Returns: A promise resolved when the state restoration has completed. Type Promise Example Add a function on the Manager to restore the items found in the last discovery. MyDeviceMgr.prototype.restoreStateForOrd = function (state) { if (state.discoveries) { this.discoveredItems = state.discoveries; } return this.reloadLearnModel(); // Returns a Promise that will reload the table }; restoreForKey(mgr, obj) Test whether the Manager has a restoreStateForKey function, and invoke it, if found. Parameters: Name Type Description mgr module:nmodule/webEditors/rc/wb/mgr/Manager the Manager instance being restored. obj Object the object containing the state to be restored Returns: The Promise returned by the Manager's function, or undefined if the manager does not provide the function. Type Promise restoreForOrd(mgr, obj) Test whether the Manager has a restoreStateForOrd function, and invoke it, if found. Parameters: Name Type Description mgr module:nmodule/webEditors/rc/wb/mgr/Manager the Manager instance being restored. obj Object Returns: The Promise returned by the Manager's function, or undefined if the manager does not provide the function. Type Promise save(mgr) Save the state of the Manager to session storage. This will perform three steps: first the manager's state is saved as properties upon a state object, secondly the object is serialized to JSON, and finally the JSON string is placed in session storage. The default save implementation will save the common basic state of the manager; that is the visibility of the table columns and the visibility of the discovery table. The manager can also provide functions to save state, which will be called by this type, if defined. The manager may provide a saveStateForKey and or a saveStateForOrd function. The saveStateForOrd function will be used to store information against a particular ord loaded in the manager, typically to save discovery data. Only the last ord that was loaded for a particular manager type will have its ord data saved. Any previous ord data for the same manager type will not be reloaded and thus will be erased when the data is saved again. The saveStateForKey function can be used to save generic data for the type of Manager. Both of these functions should return an Object containing the state to save. The returned value will be added to the data to be serialized. The Manager should also provide corresponding restoreStateForKeyand/orrestoreStateForOrd` functions that will receive a deserialized version of the object. Parameters: Name Type Description mgr module:nmodule/webEditors/rc/wb/mgr/Manager the Manager instance requiring its state to be saved. Returns: Type Promise Example Add a function on the Manager to save the items found in the last discovery. MyDeviceMgr.prototype.saveStateForOrd = function () { return { discoveries: this.discoveredItems // These objects will be serialized as JSON }; }; saveForKey(mgr, state) Test whether the Manager has a saveStateForKey function, and invoke it, if found. Parameters: Name Type Description mgr module:nmodule/webEditors/rc/wb/mgr/Manager the Manager instance being saved. state Object an object instance that will contain the state to be serialized. saveForOrd(mgr, state) Test whether the Manager has a saveStateForOrd function, and invoke it, if found. Parameters: Name Type Description mgr module:nmodule/webEditors/rc/wb/mgr/Manager the Manager instance being saved. state Object an object instance that will contain the state to be serialized. × Search results Close "},"module-nmodule_webEditors_rc_wb_mgr_MgrTypeInfo.html":{"id":"module-nmodule_webEditors_rc_wb_mgr_MgrTypeInfo.html","title":"Module: nmodule/webEditors/rc/wb/mgr/MgrTypeInfo","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/mgr/MgrTypeInfo new (require(\"nmodule/webEditors/rc/wb/mgr/MgrTypeInfo\"))() API Status: Development MgrTypeInfo wraps information about what type to create in the station database when doing a new or add operation. This information may come from an exact type, a registry query for concrete types given a base type, or from a component instance used as a prototype. In addition to the basic type operations, this provides extra functionality specific to the manager views, such as the ability to compare and match types. This constructor should be not called directly. Client code should use the static .make() function. Methods &lt;static&gt; BY_DISPLAY_NAME() Helper function to be passed to an array sorting function to ensure MgrTypeInfo instances are ordered according to the display name. Example Sort the array of MgrTypeInfos obtained from the make() function. MgrTypeInfo.make({ from: 'baja:ControlPoint', concreteTypes: true }) .then(function (mgrInfos) { mgrInfos.sort(MgrTypeInfo.BY_DISPLAY_NAME); }); &lt;static&gt; make(params) Static .make() function, returning a Promise that will resolve to a single MgrTypeInfo or array of MgrTypeInfos, depending on the arguments passed to the function. This is the normal way to obtain a MgrTypeInfo instance; the type's constructor should not be used by client code directly. Parameters: Name Type Description params Object an Object containing the function's arguments Properties Name Type Argument Description from String | baja.Type | Array The type spec used to create the type information. This may be a single type spec string, or a BajaScript Type instance, or may be an array of spec strings or Types. concreteTypes Boolean &lt;optional&gt; true if the from parameter should be used as a base type to create an Array of MgrTypeInfos for its concrete types. If this parameter is specified as true, the from parameter must contain a single base type. batch baja.comm.Batch &lt;optional&gt; An optional batch object, which if provided can be used to batch network calls. Returns: Either a single MgrTypeInfo instance, or an array of MgrTypeInfos, depending on the value passed in the 'from' parameter and the value of the 'concreteTypes' parameter. If the 'from' parameter specifies a single type and 'concreteTypes' is either false or undefined, the returned value will be a single MgrTypeInfo, otherwise the returned value will be an array of MgrTypeInfos. Type Promise Examples Make an array of MgrTypeInfos from an array of type spec strings. MgrTypeInfo.make({ from: [ 'baja:AbsTime', 'baja:Date', 'baja.RelTime' ] }) .then(function (mgrInfos) { // ... do something with the array of MgrTypeInfo }); Get the concrete MgrTypeInfos for the abstract ControlPoint type. MgrTypeInfo.make({ from: 'baja:ControlPoint', concreteTypes: true }) .then(function (mgrInfos) { // ... do something with the array of MgrTypeInfo }); Get the MgrTypeInfos for the agents on a type baja.registry.getAgents(\"type:moduleName:TypeName\") .then(function (agentInfos) { return MgrTypeInfo.make({ from: agentInfos }); }) .then(function (mgrInfos) { // ... do something with the array of MgrTypeInfo }); &lt;static&gt; markDuplicates(infos) Static function to compare an array of MgrTypeInfos for duplicate type names. This is used to alter the display name for any non-prototype created MgrTypeInfos, where there may be two or more modules exposing types with the same display names. Parameters: Name Type Description infos Array.&lt;module:nmodule/webEditors/rc/wb/mgr/MgrTypeInfo&gt; equals(other) Equality comparison for two MgrTypeInfo instances based on a comparison of the display name. This will return false if the other object is not a MgrTypeInfo instance. Parameters: Name Type Description other Object the object to be compared against Returns: Type Boolean getDisplayName() Get the display name of the type to create. Returns: Type String getIcon() Get the icon of the type to create. Returns: Type baja.Icon getType() Get the BajaScript Type to be created by this MgrTypeInfo instance. Returns: Type baja.Type isMatchable(db) Return true if this type may be used to perform a learn match against the specified database component. Parameters: Name Type Description db baja.Component the database component Returns: Type Boolean newInstance() Returns a Promise that will create a new instance of a component from the type information. Returns: Type Promise.&lt;baja.Component&gt; toSlotName() Get the type as a slot name. The default implementation returns the display name stripped of spaces and escaped. Returns: Type String toString() Returns the display name for the type represented by this instance. Returns: Type String × Search results Close "},"module-nmodule_webEditors_rc_wb_mgr_model_columns_IconMgrColumn.html":{"id":"module-nmodule_webEditors_rc_wb_mgr_model_columns_IconMgrColumn.html","title":"Module: nmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumn","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumn new (require(\"nmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumn\"))() API Status: Development Subclass of the IconColumn type, for displaying an icon in a manager table. Extends: module:nmodule/webEditors/rc/wb/table/model/columns/IconColumn Methods buildCell(row, dom) Build the dom for the cell. This overrides the buildCell() function provided by MgrColumn to directly call the base class, which will build a field editor for the icon. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Overrides: module:nmodule/webEditors/rc/wb/table/model/columns/IconColumn#buildCell Returns: A promise resolved with the img element added to the dom. Type Promise destroyCell(row, dom) Destroy the IconEditor that was created for this cell. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Overrides: module:nmodule/webEditors/rc/wb/table/model/columns/IconColumn#destroyCell Returns: Type Promise getColumnIcon() Returns a URI for an icon representing this column. Returns null by default; override as needed in subclasses. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getColumnIcon Returns: a URI for an icon to be shown for this column. Type String getFlags() Get the flags set on this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getFlags Returns: Type Number getName() Get the column name or null if none was given. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getName Returns: Type String getValueFor(row) Get the icon for the row. If a type column has a new selected type that has not been committed yet, this will return the icon for the new type, assuming the row has an icon defined. If the row has no icon, a new type's icon will not be considered. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Overrides: module:nmodule/webEditors/rc/wb/table/model/columns/IconColumn#getValueFor Returns: Type baja.Icon hasFlags(flags) Return true if the column has all of the given flags. Parameters: Name Type Description flags Number flags to check for Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#hasFlags Returns: Type Boolean isEditable() Return true if the column is editable. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isEditable Returns: Type Boolean isExportable() Return true if the column should show up in export operations, e.g. to CSV. Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isExportable Returns: Type Boolean isHidable() Return true if the column should available in the table's show/hide context menu. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isHidable Returns: Type Boolean isReadonly() Return true if the column is readonly. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isReadonly Returns: Type Boolean isSortable() Returns a boolean indicating whether the column should not be sortable via the table headings. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isSortable Returns: Type Boolean isUnseen() Return true if the column is unseen. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isUnseen Returns: Type Boolean setEditable(editable) Set or unset the column's EDITABLE flag. Emits a flagsChanged event. Parameters: Name Type Description editable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setEditable setExportable(exportable) Set or unset whether the column should show up in export operations. Parameters: Name Type Description exportable boolean Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setExportable setFlags(flags) Set the column's flags. Parameters: Name Type Description flags Number Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setFlags Throws: if a non-Number given Type Error setHidable(hidable) Set or unset whether the column should be allowed to be hidden or shown by the table's show/hide context menu. Parameters: Name Type Description hidable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setHidable setReadonly(readonly) Set or unset the column's READONLY flag. Emits a flagsChanged event. Parameters: Name Type Description readonly boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setReadonly setSortable(sortable) Set or unset whether the column should be allowed to be sorted by the table heading. Parameters: Name Type Description sortable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setSortable setUnseen(unseen) Set or unset the column's UNSEEN flag. Emits a flagsChanged event. Parameters: Name Type Description unseen boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setUnseen toDisplayName() Resolves a display name for this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#toDisplayName Returns: promise to be resolved when the element's display name has been fully built. It's also acceptable for overrides of this function to complete synchronously without returning a promise, so be sure to wrap calls to this function in Promise.resolve() when appropriate. Type Promise | * × Search results Close "},"module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinMgrColumn.html":{"id":"module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinMgrColumn.html","title":"Module: nmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumn","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumn new (require(\"nmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumn\"))(type, params) API Status: Development Manager column for working with a baja:IMixIn. Extends: module:nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn Parameters: Name Type Description type Type the type of the BIMixin subclass this column is intended to edit params Object Methods buildCell(row, dom) Shows a display string of the desired property value of the row's loaded Complex. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn#buildCell Returns: resolves when the cell has been built Type Promise commit(value, row [, params]) Sets/adds the property value on the row's Component subject. Parameters: Name Type Argument Description value baja.Value row module:nmodule/webEditors/rc/wb/table/model/Row params Object &lt;optional&gt; Properties Name Type Argument Description batch baja.comm.Batch &lt;optional&gt; Inherited From: module:nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn#commit Overrides: module:nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn#commit Returns: promise to be resolved when the value has been set on the backing Component Type Promise destroyCell(row, dom) Called when the table is destroying the DOM element built for a cell in this column. This gives a Column implementation the chance to clean up any resources that might have been created during the earlier call to #buildCell, perhaps destroying a widget in the cell, for example. As with #buildCell, if this completes synchronously and doesn't return a Promise, the caller must wrap this in a call to Promise.resolve(). Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#destroyCell Returns: Type Promise | * getColumnIcon() Overrides: module:nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn#getColumnIcon Returns: the icon of the mixin's type Type string getConfigFor(rows) Load the default editor for the coalesced instance of the mixin. Parameters: Name Type Description rows Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; Overrides: module:nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn#getConfigFor Returns: config object for fe.makeFor Type Object getFlags() Get the flags set on this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getFlags Returns: Type Number getName() Get the column name or null if none was given. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getName Returns: Type String getValueFor(row) Get the value of the Property specified in the constructor from the row's loaded Complex. If the Complex does not have that Property: If a getDefaultValue param was passed into this column's constructor, the row's Complex will be passed into the function and the result will be returned. If a type param was passed into this column's constructor, the value of the property from the default instance of the given type will be returned. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Inherited From: module:nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn#getValueFor Throws: if the row does not actually have a Complex loaded, or does not have the specified Property (and type is unknown) Type Error Returns: the Property value read from the Complex Type baja.Value hasFlags(flags) Return true if the column has all of the given flags. Parameters: Name Type Description flags Number flags to check for Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#hasFlags Returns: Type Boolean isEditable() Return true if the column is editable. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isEditable Returns: Type Boolean isExportable() Return true if the column should show up in export operations, e.g. to CSV. Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isExportable Returns: Type Boolean isHidable() Return true if the column should available in the table's show/hide context menu. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isHidable Returns: Type Boolean isReadonly() Return true if the column is readonly. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isReadonly Returns: Type Boolean isSortable() Returns a boolean indicating whether the column should not be sortable via the table headings. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isSortable Returns: Type Boolean isUnseen() Return true if the column is unseen. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isUnseen Returns: Type Boolean mixin(Ctor) Applies MgrColumn functionality to an arbitrary Column subclass. Parameters: Name Type Description Ctor function Inherited From: module:nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn#mixin Mixes In: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn.mixin setEditable(editable) Set or unset the column's EDITABLE flag. Emits a flagsChanged event. Parameters: Name Type Description editable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setEditable setExportable(exportable) Set or unset whether the column should show up in export operations. Parameters: Name Type Description exportable boolean Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setExportable setFlags(flags) Set the column's flags. Parameters: Name Type Description flags Number Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setFlags Throws: if a non-Number given Type Error setHidable(hidable) Set or unset whether the column should be allowed to be hidden or shown by the table's show/hide context menu. Parameters: Name Type Description hidable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setHidable setReadonly(readonly) Set or unset the column's READONLY flag. Emits a flagsChanged event. Parameters: Name Type Description readonly boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setReadonly setSortable(sortable) Set or unset whether the column should be allowed to be sorted by the table heading. Parameters: Name Type Description sortable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setSortable setUnseen(unseen) Set or unset the column's UNSEEN flag. Emits a flagsChanged event. Parameters: Name Type Description unseen boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setUnseen toDisplayName() Resolves the display name of the property slot. Inherited From: module:nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn#toDisplayName Returns: promise to be resolved with the display name Type Promise × Search results Close "},"module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinPropMgrColumn.html":{"id":"module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinPropMgrColumn.html","title":"Module: nmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumn","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumn API Status: Development MgrColumn that allows you to edit a property on a baja:IMixIn installed on a component. The property may also be several levels deep relative to the row's subject component's mixin. new (require(\"nmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumn\"))(name, mixinType, path [, params]) Extends: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn Parameters: Name Type Argument Description name string column name (if omitted, one will be generated). This was added in Niagara 4.13, but may be omitted (you may still use mixinType as the first parameter). mixinType Type the BIMixIn Type whose property you wish to edit path String | baja.Slot the property or property path (as a '/'-delimited string) you wish to edit. params Object &lt;optional&gt; Properties Name Type Argument Description displayName String &lt;optional&gt; the display name for the column; if omitted, will use the slot display name from the Type Throws: if Type or prop are missing Type Error Methods buildCell(row, dom) Creates the cell's contents by calling toString on the row's proposed value or the current value if there is no proposal. HTML will be safely escaped. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#buildCell Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#buildCell Returns: Type Promise coalesceRows(rows) Given the set of rows to be edited, coalesce their values into one single value to load into an editor. By default, this will simply read the proposed value from the first row. This is appropriate for a use case where one value will be entered and written back to all edited components. If editing one value for all the given rows is not a use case supported by your column (a good example is a Name column, because no two components can share the same name), throw an error. Parameters: Name Type Description rows Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; Inherited From: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#coalesceRows Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#coalesceRows Throws: if rows array not given, or values from rows are not all of the same type Type Error Returns: value coalesced from the given rows Type * commit(value, row [, params]) Sets the value to the mixin instance on the row's component. If the component does not have an instance of the mixin, a new one will be created. This method may be safely overridden while maintaining the assurance that the mixin instance exists. Parameters: Name Type Argument Description value baja.Value row module:nmodule/webEditors/rc/wb/table/model/Row params Object &lt;optional&gt; Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#commit Returns: Type Promise destroyCell(row, dom) Called when the table is destroying the DOM element built for a cell in this column. This gives a Column implementation the chance to clean up any resources that might have been created during the earlier call to #buildCell, perhaps destroying a widget in the cell, for example. As with #buildCell, if this completes synchronously and doesn't return a Promise, the caller must wrap this in a call to Promise.resolve(). Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#destroyCell Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#destroyCell Returns: Type Promise | * getColumnIcon() Returns a URI for an icon representing this column. Returns null by default; override as needed in subclasses. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getColumnIcon Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getColumnIcon Returns: a URI for an icon to be shown for this column. Type String getComplexFromPath(row) Get the complex this property is on. Utilize the existing mixin instance or create a new instance of the mixIn if it does not exist yet. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Returns: Type baja.Complex getConfigFor(rows) After coalescing the selected rows into a single value, calculate a config object to be given to fe.makeFor that will determine how the editor will be built to edit that value. This function will typically not be called directly but serves as an override point. By default, it will simply get the coalesced value from those rows and have fe.makeFor build the default editor for that value. Note that this means if the coalesced value is a non-Baja value, like an array, this function must be overridden. Parameters: Name Type Description rows Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; Inherited From: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getConfigFor Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getConfigFor Returns: configuration object to be given to fe.makeFor Type Object getFlags() Get the flags set on this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getFlags Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getFlags Returns: Type Number getMixinContainer(row) Get the container where the mixin is located or will be located once it is added. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Since: Niagara 4.13 Returns: Type baja.Complex getMixinInstance(row) Get the existing mixin instance that already belongs to the component loaded into the row. If called from commit(), the instance is guaranteed to have been added if it did not already exist. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Returns: Type baja.Value | null getName() Get the column name or null if none was given. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getName Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getName Returns: Type String getProposedValueFor(row) Get the currently proposed value for the given row. If no value proposed yet, will return the actual column value (getValueFor). Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Inherited From: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getProposedValueFor Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getProposedValueFor Returns: Type * getValueFor(row) Get the property from the mixin instance on this row's component. If the component does not have an instance of that mixin (e.g. it is an unmounted component created browser-side), the default value from an instance of that mixin will be returned. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getValueFor Returns: Type baja.Value hasFlags(flags) Return true if the column has all of the given flags. Parameters: Name Type Description flags Number flags to check for Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#hasFlags Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#hasFlags Returns: Type Boolean isEditable() Return true if the column is editable. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isEditable Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#isEditable Returns: Type Boolean isEditorSuitable(editor, rows) If an editor has already been built, it may be possible to reuse it, simply loading in a new coalesced value rather than destroying and rebuilding the existing editor. This function should return true if the editor is suitable to be reused for the given rows. By default, will always return true. Parameters: Name Type Description editor module:nmodule/webEditors/rc/fe/baja/BaseEditor rows Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; Inherited From: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#isEditorSuitable Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#isEditorSuitable Returns: Type Boolean isExportable() Return true if the column should show up in export operations, e.g. to CSV. Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isExportable Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#isExportable Returns: Type Boolean isHidable() Return true if the column should available in the table's show/hide context menu. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isHidable Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#isHidable Returns: Type Boolean isReadonly() Return true if the column is readonly. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isReadonly Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#isReadonly Returns: Type Boolean isSortable() Returns a boolean indicating whether the column should not be sortable via the table headings. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isSortable Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#isSortable Returns: Type Boolean isUnseen() Return true if the column is unseen. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isUnseen Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#isUnseen Returns: Type Boolean mgrValidate(model, data [, params]) Allows this column to validate proposed changes. Parameters: Name Type Argument Description model module:nmodule/webEditors/rc/wb/mgr/model/MgrModel the model to which we're about to apply changes. data Array an array of proposed changes to this column, one per row in the MgrModel. If a value in this array is null, no change has been proposed for that row. params Object &lt;optional&gt; Properties Name Type Argument Description editor module:nmodule/webEditors/rc/fe/baja/BaseEditor &lt;optional&gt; the editor from which the proposed values were read. Note that the editor may have been used to edit other rows, so the editor's current value may not match the proposed new values. Inherited From: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#mgrValidate Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#mgrValidate Returns: promise that resolves by default Type Promise Example Validating this column may require that I examine the changes I'm about to make to other columns as well. MyMgrColumn.prototype.mgrValidate = function (model, data, params) { var that = this, rows = model.getRows(), otherColumn = model.getColumn('otherColumn'); //search through all MgrModel rows, and check to see that my proposed //change is compatible with the proposed change to another column. //say, i'm a \"password\" column, and the other column is a \"password //scheme\" column - i need to make sure that the proposed password is //considered valid by the proposed password scheme. for (var i = 0; i &lt; rows.length; i++) { var row = rows[i], myValue = data[i], otherValue = otherColumn.getProposedValueFor(row); if (myValue === null) { //no changes proposed for this row, so nothing to validate. } if (!isCompatible(myValue, otherValue)) { return Promise.reject(new Error('incompatible values')); } } }; propose(value, row) Should read the value and \"tentatively\" apply it to the selected row. In most cases this will be setting some temporary data for display-only purposes. By default, will set some temporary data on the row using the column's name as a key. Parameters: Name Type Description value * row module:nmodule/webEditors/rc/wb/table/model/Row Inherited From: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#propose Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#propose Returns: Type Promise setEditable(editable) Set or unset the column's EDITABLE flag. Emits a flagsChanged event. Parameters: Name Type Description editable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setEditable Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#setEditable setExportable(exportable) Set or unset whether the column should show up in export operations. Parameters: Name Type Description exportable boolean Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setExportable Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#setExportable setFlags(flags) Set the column's flags. Parameters: Name Type Description flags Number Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setFlags Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#setFlags Throws: if a non-Number given Type Error setHidable(hidable) Set or unset whether the column should be allowed to be hidden or shown by the table's show/hide context menu. Parameters: Name Type Description hidable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setHidable Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#setHidable setReadonly(readonly) Set or unset the column's READONLY flag. Emits a flagsChanged event. Parameters: Name Type Description readonly boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setReadonly Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#setReadonly setSortable(sortable) Set or unset whether the column should be allowed to be sorted by the table heading. Parameters: Name Type Description sortable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setSortable Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#setSortable setUnseen(unseen) Set or unset the column's UNSEEN flag. Emits a flagsChanged event. Parameters: Name Type Description unseen boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setUnseen Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#setUnseen toDisplayName() Resolves a display name for this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#toDisplayName Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#toDisplayName Returns: promise to be resolved when the element's display name has been fully built. It's also acceptable for overrides of this function to complete synchronously without returning a promise, so be sure to wrap calls to this function in Promise.resolve() when appropriate. Type Promise | * × Search results Close "},"module-nmodule_webEditors_rc_wb_mgr_model_columns_NameMgrColumn.html":{"id":"module-nmodule_webEditors_rc_wb_mgr_model_columns_NameMgrColumn.html","title":"Module: nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn new (require(\"nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn\"))(params) API Status: Development MgrColumn subclass that allows components in a MgrModel to be renamed. Extends: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn Parameters: Name Type Description params Object Methods buildCell(row, dom) Creates the cell's contents by calling getDisplayName on the row's proposed value or the current value if there is no proposal. If the row's subject does not have a displayName function, buildCell defers to the superclass. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#buildCell Returns: Type Promise coalesceRows(rows) Parameters: Name Type Description rows Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#coalesceRows Throws: if more than one row is selected Type Error Returns: the component name when a single row is selected Type string commit(value, row [, params]) Renames the row's Component subject. Parameters: Name Type Argument Description value String the new name row module:nmodule/webEditors/rc/wb/table/model/Row params Object &lt;optional&gt; Properties Name Type Argument Description batch baja.comm.Batch &lt;optional&gt; Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#commit Returns: promise to be resolved when the component has been renamed. Type Promise destroyCell(row, dom) Called when the table is destroying the DOM element built for a cell in this column. This gives a Column implementation the chance to clean up any resources that might have been created during the earlier call to #buildCell, perhaps destroying a widget in the cell, for example. As with #buildCell, if this completes synchronously and doesn't return a Promise, the caller must wrap this in a call to Promise.resolve(). Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#destroyCell Returns: Type Promise | * getColumnIcon() Return object.png. Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getColumnIcon Returns: Type String getConfigFor(rows) After coalescing the selected rows into a single value, calculate a config object to be given to fe.makeFor that will determine how the editor will be built to edit that value. This function will typically not be called directly but serves as an override point. By default, it will simply get the coalesced value from those rows and have fe.makeFor build the default editor for that value. Note that this means if the coalesced value is a non-Baja value, like an array, this function must be overridden. Parameters: Name Type Description rows Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; Inherited From: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getConfigFor Returns: configuration object to be given to fe.makeFor Type Object getFlags() Get the flags set on this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getFlags Returns: Type Number getName() Get the column name or null if none was given. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getName Returns: Type String getProposedValueFor(row) Get the currently proposed value for the given row. If no value proposed yet, will return the actual column value (getValueFor). Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Inherited From: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getProposedValueFor Returns: Type * getValueFor(row) Returns the row's Component subject's slot name. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getValueFor Returns: Type String hasFlags(flags) Return true if the column has all of the given flags. Parameters: Name Type Description flags Number flags to check for Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#hasFlags Returns: Type Boolean isEditable() Return true if the column is editable. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isEditable Returns: Type Boolean isEditorSuitable(editor, rows) We can't set multiple components to the same name. So if editing more than one row, we need the editor to be a DisplayOnlyEditor. Parameters: Name Type Description editor module:nmodule/webEditors/rc/fe/baja/BaseEditor rows Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#isEditorSuitable Returns: true when: there's more than one row and it's a DisplayOnlyEditor, it's a frozen slot and it's a DisplayOnlyEditor, or it's only one row and it's editable Type boolean isExportable() Return true if the column should show up in export operations, e.g. to CSV. Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isExportable Returns: Type Boolean isHidable() Return true if the column should available in the table's show/hide context menu. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isHidable Returns: Type Boolean isReadonly() Return true if the column is readonly. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isReadonly Returns: Type Boolean isSortable() Returns a boolean indicating whether the column should not be sortable via the table headings. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isSortable Returns: Type Boolean isUnseen() Return true if the column is unseen. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isUnseen Returns: Type Boolean mgrValidate(model, data [, params]) Allows this column to validate proposed changes. Parameters: Name Type Argument Description model module:nmodule/webEditors/rc/wb/mgr/model/MgrModel the model to which we're about to apply changes. data Array an array of proposed changes to this column, one per row in the MgrModel. If a value in this array is null, no change has been proposed for that row. params Object &lt;optional&gt; Properties Name Type Argument Description editor module:nmodule/webEditors/rc/fe/baja/BaseEditor &lt;optional&gt; the editor from which the proposed values were read. Note that the editor may have been used to edit other rows, so the editor's current value may not match the proposed new values. Inherited From: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#mgrValidate Returns: promise that resolves by default Type Promise Example Validating this column may require that I examine the changes I'm about to make to other columns as well. MyMgrColumn.prototype.mgrValidate = function (model, data, params) { var that = this, rows = model.getRows(), otherColumn = model.getColumn('otherColumn'); //search through all MgrModel rows, and check to see that my proposed //change is compatible with the proposed change to another column. //say, i'm a \"password\" column, and the other column is a \"password //scheme\" column - i need to make sure that the proposed password is //considered valid by the proposed password scheme. for (var i = 0; i &lt; rows.length; i++) { var row = rows[i], myValue = data[i], otherValue = otherColumn.getProposedValueFor(row); if (myValue === null) { //no changes proposed for this row, so nothing to validate. } if (!isCompatible(myValue, otherValue)) { return Promise.reject(new Error('incompatible values')); } } }; propose(value, row) Should read the value and \"tentatively\" apply it to the selected row. In most cases this will be setting some temporary data for display-only purposes. By default, will set some temporary data on the row using the column's name as a key. Parameters: Name Type Description value * row module:nmodule/webEditors/rc/wb/table/model/Row Inherited From: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#propose Returns: Type Promise setEditable(editable) Set or unset the column's EDITABLE flag. Emits a flagsChanged event. Parameters: Name Type Description editable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setEditable setExportable(exportable) Set or unset whether the column should show up in export operations. Parameters: Name Type Description exportable boolean Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setExportable setFlags(flags) Set the column's flags. Parameters: Name Type Description flags Number Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setFlags Throws: if a non-Number given Type Error setHidable(hidable) Set or unset whether the column should be allowed to be hidden or shown by the table's show/hide context menu. Parameters: Name Type Description hidable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setHidable setReadonly(readonly) Set or unset the column's READONLY flag. Emits a flagsChanged event. Parameters: Name Type Description readonly boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setReadonly setSortable(sortable) Set or unset whether the column should be allowed to be sorted by the table heading. Parameters: Name Type Description sortable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setSortable setUnseen(unseen) Set or unset the column's UNSEEN flag. Emits a flagsChanged event. Parameters: Name Type Description unseen boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setUnseen toDisplayName() Returns name from webEditors lexicon. Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#toDisplayName Returns: Type String × Search results Close "},"module-nmodule_webEditors_rc_wb_mgr_model_columns_PathMgrColumn.html":{"id":"module-nmodule_webEditors_rc_wb_mgr_model_columns_PathMgrColumn.html","title":"Module: nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn new (require(\"nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn\"))() API Status: Development Manager column used to display the slot path of a BComponent within the station. This type is equivalent to the MgrColumn.Path class in the bajaui abstract manager framework. Extends: module:nmodule/webEditors/rc/wb/table/model/Column Methods buildCell(row, dom) Builds out the DOM element, typically a td, that represents the intersection of this column with a particular row. Often this will simply be a simple toString() or similar, but could also do more sophisticated things like build widgets. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#buildCell Returns: promise to be resolved when the element's contents have been fully built. It's also acceptable for overrides of this function to complete synchronously without returning a promise, so be sure to wrap calls to this function in Promise.resolve() when appropriate. Type Promise | * destroyCell(row, dom) Called when the table is destroying the DOM element built for a cell in this column. This gives a Column implementation the chance to clean up any resources that might have been created during the earlier call to #buildCell, perhaps destroying a widget in the cell, for example. As with #buildCell, if this completes synchronously and doesn't return a Promise, the caller must wrap this in a call to Promise.resolve(). Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#destroyCell Returns: Type Promise | * getColumnIcon() Returns a URI for an icon representing this column. Returns null by default; override as needed in subclasses. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getColumnIcon Returns: a URI for an icon to be shown for this column. Type String getFlags() Get the flags set on this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getFlags Returns: Type Number getName() Get the column name or null if none was given. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getName Returns: Type String getValueFor(row) Gets the value of the row as an unescaped slot path string. The row's subject must be a Component, otherwise an Error will be thrown. In the case of an unmounted component, an empty string will be returned. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row A row representing a Component in a manager view. Overrides: module:nmodule/webEditors/rc/wb/table/model/Column#getValueFor Returns: Type String hasFlags(flags) Return true if the column has all of the given flags. Parameters: Name Type Description flags Number flags to check for Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#hasFlags Returns: Type Boolean isEditable() Return true if the column is editable. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isEditable Returns: Type Boolean isExportable() Return true if the column should show up in export operations, e.g. to CSV. Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isExportable Returns: Type Boolean isHidable() Return true if the column should available in the table's show/hide context menu. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isHidable Returns: Type Boolean isReadonly() Return true if the column is readonly. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isReadonly Returns: Type Boolean isSortable() Returns a boolean indicating whether the column should not be sortable via the table headings. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isSortable Returns: Type Boolean isUnseen() Return true if the column is unseen. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isUnseen Returns: Type Boolean setEditable(editable) Set or unset the column's EDITABLE flag. Emits a flagsChanged event. Parameters: Name Type Description editable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setEditable setExportable(exportable) Set or unset whether the column should show up in export operations. Parameters: Name Type Description exportable boolean Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setExportable setFlags(flags) Set the column's flags. Parameters: Name Type Description flags Number Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setFlags Throws: if a non-Number given Type Error setHidable(hidable) Set or unset whether the column should be allowed to be hidden or shown by the table's show/hide context menu. Parameters: Name Type Description hidable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setHidable setReadonly(readonly) Set or unset the column's READONLY flag. Emits a flagsChanged event. Parameters: Name Type Description readonly boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setReadonly setSortable(sortable) Set or unset whether the column should be allowed to be sorted by the table heading. Parameters: Name Type Description sortable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setSortable setUnseen(unseen) Set or unset the column's UNSEEN flag. Emits a flagsChanged event. Parameters: Name Type Description unseen boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setUnseen toDisplayName() Resolves a display name for this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#toDisplayName Returns: promise to be resolved when the element's display name has been fully built. It's also acceptable for overrides of this function to complete synchronously without returning a promise, so be sure to wrap calls to this function in Promise.resolve() when appropriate. Type Promise | * × Search results Close "},"module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyMgrColumn.html":{"id":"module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyMgrColumn.html","title":"Module: nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn new (require(\"nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn\"))() API Status: Development MgrColumn subclass that allows individual property values to be edited on a component. The column name will correspond directly to the slot name to modify. Extends: module:nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn Mixes In: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn Methods buildCell(row, dom) Shows a display string of the desired property value of the row's loaded Complex. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn#buildCell Returns: resolves when the cell has been built Type Promise commit(value, row [, params]) Sets/adds the property value on the row's Component subject. Parameters: Name Type Argument Description value baja.Value row module:nmodule/webEditors/rc/wb/table/model/Row params Object &lt;optional&gt; Properties Name Type Argument Description batch baja.comm.Batch &lt;optional&gt; Returns: promise to be resolved when the value has been set on the backing Component Type Promise destroyCell(row, dom) Called when the table is destroying the DOM element built for a cell in this column. This gives a Column implementation the chance to clean up any resources that might have been created during the earlier call to #buildCell, perhaps destroying a widget in the cell, for example. As with #buildCell, if this completes synchronously and doesn't return a Promise, the caller must wrap this in a call to Promise.resolve(). Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#destroyCell Returns: Type Promise | * getColumnIcon() If a type param was given, then return an icon for the given slot on that type. Otherwise, return null. Inherited From: module:nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn#getColumnIcon Returns: Type String getConfigFor(rows) If editing only one row, then the facets used to configure the field editor will be taken from the component instance in that one row. If editing multiple rows, then the facets will be taken from the appropriate frozen slot on the column's configured Type, if present. Parameters: Name Type Description rows Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; Returns: config object for fe.makeFor, possibly including properties derived from slot facets Type Object getFlags() Get the flags set on this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getFlags Returns: Type Number getName() Get the column name or null if none was given. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getName Returns: Type String getValueFor(row) Get the value of the Property specified in the constructor from the row's loaded Complex. If the Complex does not have that Property: If a getDefaultValue param was passed into this column's constructor, the row's Complex will be passed into the function and the result will be returned. If a type param was passed into this column's constructor, the value of the property from the default instance of the given type will be returned. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Inherited From: module:nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn#getValueFor Throws: if the row does not actually have a Complex loaded, or does not have the specified Property (and type is unknown) Type Error Returns: the Property value read from the Complex Type baja.Value hasFlags(flags) Return true if the column has all of the given flags. Parameters: Name Type Description flags Number flags to check for Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#hasFlags Returns: Type Boolean isEditable() Return true if the column is editable. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isEditable Returns: Type Boolean isExportable() Return true if the column should show up in export operations, e.g. to CSV. Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isExportable Returns: Type Boolean isHidable() Return true if the column should available in the table's show/hide context menu. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isHidable Returns: Type Boolean isReadonly() Return true if the column is readonly. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isReadonly Returns: Type Boolean isSortable() Returns a boolean indicating whether the column should not be sortable via the table headings. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isSortable Returns: Type Boolean isUnseen() Return true if the column is unseen. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isUnseen Returns: Type Boolean mixin(Ctor) Applies MgrColumn functionality to an arbitrary Column subclass. Parameters: Name Type Description Ctor function Mixes In: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn.mixin setEditable(editable) Set or unset the column's EDITABLE flag. Emits a flagsChanged event. Parameters: Name Type Description editable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setEditable setExportable(exportable) Set or unset whether the column should show up in export operations. Parameters: Name Type Description exportable boolean Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setExportable setFlags(flags) Set the column's flags. Parameters: Name Type Description flags Number Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setFlags Throws: if a non-Number given Type Error setHidable(hidable) Set or unset whether the column should be allowed to be hidden or shown by the table's show/hide context menu. Parameters: Name Type Description hidable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setHidable setReadonly(readonly) Set or unset the column's READONLY flag. Emits a flagsChanged event. Parameters: Name Type Description readonly boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setReadonly setSortable(sortable) Set or unset whether the column should be allowed to be sorted by the table heading. Parameters: Name Type Description sortable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setSortable setUnseen(unseen) Set or unset the column's UNSEEN flag. Emits a flagsChanged event. Parameters: Name Type Description unseen boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setUnseen toDisplayName() Resolves the display name of the property slot. Inherited From: module:nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn#toDisplayName Returns: promise to be resolved with the display name Type Promise × Search results Close "},"module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyPathMgrColumn.html":{"id":"module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyPathMgrColumn.html","title":"Module: nmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumn","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumn new (require(\"nmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumn\"))(name, path, params) API Status: Development Column for a property several levels deep relative to the row's subject component. The constructor is passed a '/' delimited string specifying a property path. Extends: module:nmodule/webEditors/rc/wb/table/model/Column Parameters: Name Type Description name String Optional A name for this column (if not provided, will be calculated) path String A slot path, specified as a '/' delimited string params Object Properties Name Type Description type Type The type declaring the target slot, used to obtain the slot facets. Mixes In: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn Methods &lt;static&gt; getPropValueFromPath(complex, path) Take a subject component (e.g. a control point) and an array of slot names representing a slot path (e.g leading to a property on its point ext), and resolve the descendants of the subject using the property names. Parameters: Name Type Description complex baja.Complex path Array.&lt;(baja.Slot|string)&gt; Since: Niagara 4.13 Returns: Type baja.Value buildCell(row, dom) Builds out the DOM element, typically a td, that represents the intersection of this column with a particular row. Often this will simply be a simple toString() or similar, but could also do more sophisticated things like build widgets. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#buildCell Returns: promise to be resolved when the element's contents have been fully built. It's also acceptable for overrides of this function to complete synchronously without returning a promise, so be sure to wrap calls to this function in Promise.resolve() when appropriate. Type Promise | * commit(value, row [, params]) Sets/adds the property on the slot resolved from the path. This requires the previous slots in the path to exist. Parameters: Name Type Argument Description value baja.Value row module:nmodule/webEditors/rc/wb/table/model/Row params Object &lt;optional&gt; Properties Name Type Argument Description batch baja.comm.Batch &lt;optional&gt; Returns: promise to be resolved when the value has been set on the slot resolved from the path. Type Promise destroyCell(row, dom) Called when the table is destroying the DOM element built for a cell in this column. This gives a Column implementation the chance to clean up any resources that might have been created during the earlier call to #buildCell, perhaps destroying a widget in the cell, for example. As with #buildCell, if this completes synchronously and doesn't return a Promise, the caller must wrap this in a call to Promise.resolve(). Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#destroyCell Returns: Type Promise | * getColumnIcon() Get the icon URI for the type of slot in the last component of the path. Returns null if the URI could not be obtained because the slot's declaring type was not specified in the path. Overrides: module:nmodule/webEditors/rc/wb/table/model/Column#getColumnIcon Returns: The URI for the icon for the target slot. Type String getComplexFromPath(row) Get the complex this property is on. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Since: Niagara 4.8 Returns: Type baja.Complex getConfigFor(rows) If editing only one row, then the facets used to configure the field editor will be taken from the component instance containing the final property in the property path. If editing multiple rows, the facets will be derived from the specified Type and the frozen slot at the end of the property path. If these are not present, no facets will be used. Parameters: Name Type Description rows Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; Returns: config object for fe.makeFor, which might include facets if the path's last element had a type declared. Type Object getFlags() Get the flags set on this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getFlags Returns: Type Number getName() Get the column name or null if none was given. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getName Returns: Type String getPropertyName() Get the property name for this column. Since: Niagara 4.8 Returns: String getValueFor(row) Get the value of this column for the given row. This will follow the slot path to obtain the value of the slot descended from the row's subject. Parameters: Name Type Description row Overrides: module:nmodule/webEditors/rc/wb/table/model/Column#getValueFor Returns: Type * hasFlags(flags) Return true if the column has all of the given flags. Parameters: Name Type Description flags Number flags to check for Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#hasFlags Returns: Type Boolean isEditable() Return true if the column is editable. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isEditable Returns: Type Boolean isExportable() Return true if the column should show up in export operations, e.g. to CSV. Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isExportable Returns: Type Boolean isHidable() Return true if the column should available in the table's show/hide context menu. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isHidable Returns: Type Boolean isReadonly() Return true if the column is readonly. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isReadonly Returns: Type Boolean isSortable() Returns a boolean indicating whether the column should not be sortable via the table headings. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isSortable Returns: Type Boolean isUnseen() Return true if the column is unseen. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isUnseen Returns: Type Boolean mixin(Ctor) Applies MgrColumn functionality to an arbitrary Column subclass. Parameters: Name Type Description Ctor function Mixes In: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn.mixin setEditable(editable) Set or unset the column's EDITABLE flag. Emits a flagsChanged event. Parameters: Name Type Description editable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setEditable setExportable(exportable) Set or unset whether the column should show up in export operations. Parameters: Name Type Description exportable boolean Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setExportable setFlags(flags) Set the column's flags. Parameters: Name Type Description flags Number Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setFlags Throws: if a non-Number given Type Error setHidable(hidable) Set or unset whether the column should be allowed to be hidden or shown by the table's show/hide context menu. Parameters: Name Type Description hidable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setHidable setReadonly(readonly) Set or unset the column's READONLY flag. Emits a flagsChanged event. Parameters: Name Type Description readonly boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setReadonly setSortable(sortable) Set or unset whether the column should be allowed to be sorted by the table heading. Parameters: Name Type Description sortable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setSortable setUnseen(unseen) Set or unset the column's UNSEEN flag. Emits a flagsChanged event. Parameters: Name Type Description unseen boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setUnseen toDisplayName() Resolves the display name of the property slot at the end of the path. Overrides: module:nmodule/webEditors/rc/wb/table/model/Column#toDisplayName Returns: promise to be resolved with the display name for the final slot. Type Promise × Search results Close "},"module-nmodule_webEditors_rc_wb_mgr_model_columns_TypeMgrColumn.html":{"id":"module-nmodule_webEditors_rc_wb_mgr_model_columns_TypeMgrColumn.html","title":"Module: nmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumn","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumn new (require(\"nmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumn\"))() API Status: Development Manager column used to display and edit the component type of a row. Extends: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn Members &lt;static&gt; DISCOVERY_DATA_KEY :String The TypeMgrColumn key for the discovery object in Row.data() Type: String &lt;static&gt; SELECTED_TYPE_KEY :String The TypeMgrColumn key for the selected TypeInfo in Row.data() Type: String Methods buildCell(row, dom) Builds the cell contents using the type's display name. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#buildCell coalesceRows(rows) For the given array of rows, this will produce a single value from from the intersection of the available types. The types will have been set as a data value on the row by either the NewCommand or AddCommand. This will create a dynamic enum with each tag representing one of the intersected types. Parameters: Name Type Description rows Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#coalesceRows Throws: if not every row has a type to choose from (i.e. the rows are being edited, not added) Type Error Returns: Type baja.DynamicEnum | string &lt;abstract&gt; commit(value, row [, params]) Should read the value and \"officially\" apply it back to the selected rows. If params.batch is received, then params.progressCallback must be called with MgrColumn.COMMIT_READY when this function is done using that batch (even if no network calls are added to it). Note: sometimes you may want to abort the entire process of saving changes to the Manager, for instance, if one of your columns requires the user to confirm a dialog before committing. Returning a Promise that rejects will show an error dialog to the user, which may not be what you want if you've already shown a dialog. Another option is to return a Promise that never resolves or rejects, which will drop the user back at the edit screen without committing any changes (all commit calls must resolve for any changes to post to the station). A more explicit API for this may be provided in the future. Parameters: Name Type Argument Description value * the proposed value to commit to the row row module:nmodule/webEditors/rc/wb/table/model/Row params Object &lt;optional&gt; Properties Name Type Argument Description editor module:nmodule/webEditors/rc/fe/baja/BaseEditor &lt;optional&gt; the editor from which the value was read. If the column is not editable, this parameter will be undefined, as no editor will have been created for the value. This situation may occur when a value obtained via discovery is set on row for a non-editable column. batch baja.comm.Batch &lt;optional&gt; a batch to use to commit changes up to the station progressCallback function &lt;optional&gt; call this with MgrColumn.COMMIT_READY when this function is done adding network calls to the batch. Inherited From: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#commit Returns: Type Promise destroyCell(row, dom) Called when the table is destroying the DOM element built for a cell in this column. This gives a Column implementation the chance to clean up any resources that might have been created during the earlier call to #buildCell, perhaps destroying a widget in the cell, for example. As with #buildCell, if this completes synchronously and doesn't return a Promise, the caller must wrap this in a call to Promise.resolve(). Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#destroyCell Returns: Type Promise | * getColumnIcon() Return the icon to be used for the column in the batch component editor. Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getColumnIcon Returns: Type baja.Icon getConfigFor(rows) Create the config object for the editor based on the coalesced value. Parameters: Name Type Description rows Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getConfigFor Throws: if not every row has a type to choose from (i.e. the rows are being edited, not added) Type Error Returns: Type Object getFlags() Get the flags set on this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getFlags Returns: Type Number getName() Get the column name or null if none was given. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getName Returns: Type String getProposedValueFor(row) Get the currently proposed value for the given row. If no value proposed yet, will return the actual column value (getValueFor). Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Inherited From: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getProposedValueFor Returns: Type * getValueFor(row) Returns the baja.Type of the Row's subject as the value. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#getValueFor Returns: Type baja.Type hasFlags(flags) Return true if the column has all of the given flags. Parameters: Name Type Description flags Number flags to check for Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#hasFlags Returns: Type Boolean isEditable() Return true if the column is editable. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isEditable Returns: Type Boolean isEditorSuitable(editor, rows) If an editor has already been built, it may be possible to reuse it, simply loading in a new coalesced value rather than destroying and rebuilding the existing editor. This function should return true if the editor is suitable to be reused for the given rows. By default, will always return true. Parameters: Name Type Description editor module:nmodule/webEditors/rc/fe/baja/BaseEditor rows Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; Inherited From: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#isEditorSuitable Returns: Type Boolean isExportable() Return true if the column should show up in export operations, e.g. to CSV. Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isExportable Returns: Type Boolean isHidable() Return true if the column should available in the table's show/hide context menu. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isHidable Returns: Type Boolean isReadonly() Return true if the column is readonly. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isReadonly Returns: Type Boolean isSortable() Returns a boolean indicating whether the column should not be sortable via the table headings. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isSortable Returns: Type Boolean isUnseen() Return true if the column is unseen. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isUnseen Returns: Type Boolean mgrValidate(model, data [, params]) Allows this column to validate proposed changes. Parameters: Name Type Argument Description model module:nmodule/webEditors/rc/wb/mgr/model/MgrModel the model to which we're about to apply changes. data Array an array of proposed changes to this column, one per row in the MgrModel. If a value in this array is null, no change has been proposed for that row. params Object &lt;optional&gt; Properties Name Type Argument Description editor module:nmodule/webEditors/rc/fe/baja/BaseEditor &lt;optional&gt; the editor from which the proposed values were read. Note that the editor may have been used to edit other rows, so the editor's current value may not match the proposed new values. Inherited From: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#mgrValidate Returns: promise that resolves by default Type Promise Example Validating this column may require that I examine the changes I'm about to make to other columns as well. MyMgrColumn.prototype.mgrValidate = function (model, data, params) { var that = this, rows = model.getRows(), otherColumn = model.getColumn('otherColumn'); //search through all MgrModel rows, and check to see that my proposed //change is compatible with the proposed change to another column. //say, i'm a \"password\" column, and the other column is a \"password //scheme\" column - i need to make sure that the proposed password is //considered valid by the proposed password scheme. for (var i = 0; i &lt; rows.length; i++) { var row = rows[i], myValue = data[i], otherValue = otherColumn.getProposedValueFor(row); if (myValue === null) { //no changes proposed for this row, so nothing to validate. } if (!isCompatible(myValue, otherValue)) { return Promise.reject(new Error('incompatible values')); } } }; newInstance(mgrModel, row) Return a new instance for the selected type. Parameters: Name Type Description mgrModel module:nmodule/webEditors/rc/wb/mgr/model/MgrModel row module:nmodule/webEditors/rc/wb/table/model/Row Returns: Type Promise.&lt;baja.Value&gt; propose(value, row) Set the proposed value for the Row. This will normally be the DynamicEnum created by the coalesceRows() function. Parameters: Name Type Description value * row module:nmodule/webEditors/rc/wb/table/model/Row Overrides: module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn#propose Returns: Type Promise setEditable(editable) Set or unset the column's EDITABLE flag. Emits a flagsChanged event. Parameters: Name Type Description editable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setEditable setExportable(exportable) Set or unset whether the column should show up in export operations. Parameters: Name Type Description exportable boolean Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setExportable setFlags(flags) Set the column's flags. Parameters: Name Type Description flags Number Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setFlags Throws: if a non-Number given Type Error setHidable(hidable) Set or unset whether the column should be allowed to be hidden or shown by the table's show/hide context menu. Parameters: Name Type Description hidable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setHidable setReadonly(readonly) Set or unset the column's READONLY flag. Emits a flagsChanged event. Parameters: Name Type Description readonly boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setReadonly setSortable(sortable) Set or unset whether the column should be allowed to be sorted by the table heading. Parameters: Name Type Description sortable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setSortable setUnseen(unseen) Set or unset the column's UNSEEN flag. Emits a flagsChanged event. Parameters: Name Type Description unseen boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setUnseen toDisplayName() Resolves a display name for this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#toDisplayName Returns: promise to be resolved when the element's display name has been fully built. It's also acceptable for overrides of this function to complete synchronously without returning a promise, so be sure to wrap calls to this function in Promise.resolve() when appropriate. Type Promise | * × Search results Close "},"module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html":{"id":"module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html","title":"Module: nmodule/webEditors/rc/wb/mgr/model/MgrColumn","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/mgr/model/MgrColumn new (require(\"nmodule/webEditors/rc/wb/mgr/model/MgrColumn\"))() API Status: Development Column for use in a Manager workflow. It functions both as a vanilla TableModel column, and as a defined field for batch editing rows in a manager view. A MgrColumn provides for the following workflow: Identify a number of components to edit at once. Coalesce corresponding values from those components into a single value. Load the coalesced value into a single editor for editing. Commit the entered value back to the edited components. Extends: module:nmodule/webEditors/rc/wb/table/model/Column Methods &lt;static&gt; mixin(Ctor) Applies MgrColumn functionality to an arbitrary Column subclass. Parameters: Name Type Description Ctor function buildCell(row, dom) Creates the cell's contents by calling toString on the row's proposed value or the current value if there is no proposal. HTML will be safely escaped. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Overrides: module:nmodule/webEditors/rc/wb/table/model/Column#buildCell Returns: Type Promise coalesceRows(rows) Given the set of rows to be edited, coalesce their values into one single value to load into an editor. By default, this will simply read the proposed value from the first row. This is appropriate for a use case where one value will be entered and written back to all edited components. If editing one value for all the given rows is not a use case supported by your column (a good example is a Name column, because no two components can share the same name), throw an error. Parameters: Name Type Description rows Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; Throws: if rows array not given, or values from rows are not all of the same type Type Error Returns: value coalesced from the given rows Type * &lt;abstract&gt; commit(value, row [, params]) Should read the value and \"officially\" apply it back to the selected rows. If params.batch is received, then params.progressCallback must be called with MgrColumn.COMMIT_READY when this function is done using that batch (even if no network calls are added to it). Note: sometimes you may want to abort the entire process of saving changes to the Manager, for instance, if one of your columns requires the user to confirm a dialog before committing. Returning a Promise that rejects will show an error dialog to the user, which may not be what you want if you've already shown a dialog. Another option is to return a Promise that never resolves or rejects, which will drop the user back at the edit screen without committing any changes (all commit calls must resolve for any changes to post to the station). A more explicit API for this may be provided in the future. Parameters: Name Type Argument Description value * the proposed value to commit to the row row module:nmodule/webEditors/rc/wb/table/model/Row params Object &lt;optional&gt; Properties Name Type Argument Description editor module:nmodule/webEditors/rc/fe/baja/BaseEditor &lt;optional&gt; the editor from which the value was read. If the column is not editable, this parameter will be undefined, as no editor will have been created for the value. This situation may occur when a value obtained via discovery is set on row for a non-editable column. batch baja.comm.Batch &lt;optional&gt; a batch to use to commit changes up to the station progressCallback function &lt;optional&gt; call this with MgrColumn.COMMIT_READY when this function is done adding network calls to the batch. Returns: Type Promise destroyCell(row, dom) Called when the table is destroying the DOM element built for a cell in this column. This gives a Column implementation the chance to clean up any resources that might have been created during the earlier call to #buildCell, perhaps destroying a widget in the cell, for example. As with #buildCell, if this completes synchronously and doesn't return a Promise, the caller must wrap this in a call to Promise.resolve(). Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#destroyCell Returns: Type Promise | * getColumnIcon() Returns a URI for an icon representing this column. Returns null by default; override as needed in subclasses. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getColumnIcon Returns: a URI for an icon to be shown for this column. Type String getConfigFor(rows) After coalescing the selected rows into a single value, calculate a config object to be given to fe.makeFor that will determine how the editor will be built to edit that value. This function will typically not be called directly but serves as an override point. By default, it will simply get the coalesced value from those rows and have fe.makeFor build the default editor for that value. Note that this means if the coalesced value is a non-Baja value, like an array, this function must be overridden. Parameters: Name Type Description rows Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; Returns: configuration object to be given to fe.makeFor Type Object getFlags() Get the flags set on this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getFlags Returns: Type Number getName() Get the column name or null if none was given. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getName Returns: Type String getProposedValueFor(row) Get the currently proposed value for the given row. If no value proposed yet, will return the actual column value (getValueFor). Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Returns: Type * &lt;abstract&gt; getValueFor(row) Override point; each column should define its own method of retrieving a value from the given table row. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getValueFor Throws: if not implemented Type Error Returns: the row value. Note that this is synchronous; if the row's subject is not ready to provide a value, it should not be loaded into the table. Type * hasFlags(flags) Return true if the column has all of the given flags. Parameters: Name Type Description flags Number flags to check for Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#hasFlags Returns: Type Boolean isEditable() Return true if the column is editable. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isEditable Returns: Type Boolean isEditorSuitable(editor, rows) If an editor has already been built, it may be possible to reuse it, simply loading in a new coalesced value rather than destroying and rebuilding the existing editor. This function should return true if the editor is suitable to be reused for the given rows. By default, will always return true. Parameters: Name Type Description editor module:nmodule/webEditors/rc/fe/baja/BaseEditor rows Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; Returns: Type Boolean isExportable() Return true if the column should show up in export operations, e.g. to CSV. Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isExportable Returns: Type Boolean isHidable() Return true if the column should available in the table's show/hide context menu. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isHidable Returns: Type Boolean isReadonly() Return true if the column is readonly. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isReadonly Returns: Type Boolean isSortable() Returns a boolean indicating whether the column should not be sortable via the table headings. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isSortable Returns: Type Boolean isUnseen() Return true if the column is unseen. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isUnseen Returns: Type Boolean mgrValidate(model, data [, params]) Allows this column to validate proposed changes. Parameters: Name Type Argument Description model module:nmodule/webEditors/rc/wb/mgr/model/MgrModel the model to which we're about to apply changes. data Array an array of proposed changes to this column, one per row in the MgrModel. If a value in this array is null, no change has been proposed for that row. params Object &lt;optional&gt; Properties Name Type Argument Description editor module:nmodule/webEditors/rc/fe/baja/BaseEditor &lt;optional&gt; the editor from which the proposed values were read. Note that the editor may have been used to edit other rows, so the editor's current value may not match the proposed new values. Returns: promise that resolves by default Type Promise Example Validating this column may require that I examine the changes I'm about to make to other columns as well. MyMgrColumn.prototype.mgrValidate = function (model, data, params) { var that = this, rows = model.getRows(), otherColumn = model.getColumn('otherColumn'); //search through all MgrModel rows, and check to see that my proposed //change is compatible with the proposed change to another column. //say, i'm a \"password\" column, and the other column is a \"password //scheme\" column - i need to make sure that the proposed password is //considered valid by the proposed password scheme. for (var i = 0; i &lt; rows.length; i++) { var row = rows[i], myValue = data[i], otherValue = otherColumn.getProposedValueFor(row); if (myValue === null) { //no changes proposed for this row, so nothing to validate. } if (!isCompatible(myValue, otherValue)) { return Promise.reject(new Error('incompatible values')); } } }; propose(value, row) Should read the value and \"tentatively\" apply it to the selected row. In most cases this will be setting some temporary data for display-only purposes. By default, will set some temporary data on the row using the column's name as a key. Parameters: Name Type Description value * row module:nmodule/webEditors/rc/wb/table/model/Row Returns: Type Promise setEditable(editable) Set or unset the column's EDITABLE flag. Emits a flagsChanged event. Parameters: Name Type Description editable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setEditable setExportable(exportable) Set or unset whether the column should show up in export operations. Parameters: Name Type Description exportable boolean Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setExportable setFlags(flags) Set the column's flags. Parameters: Name Type Description flags Number Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setFlags Throws: if a non-Number given Type Error setHidable(hidable) Set or unset whether the column should be allowed to be hidden or shown by the table's show/hide context menu. Parameters: Name Type Description hidable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setHidable setReadonly(readonly) Set or unset the column's READONLY flag. Emits a flagsChanged event. Parameters: Name Type Description readonly boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setReadonly setSortable(sortable) Set or unset whether the column should be allowed to be sorted by the table heading. Parameters: Name Type Description sortable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setSortable setUnseen(unseen) Set or unset the column's UNSEEN flag. Emits a flagsChanged event. Parameters: Name Type Description unseen boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setUnseen toDisplayName() Resolves a display name for this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#toDisplayName Returns: promise to be resolved when the element's display name has been fully built. It's also acceptable for overrides of this function to complete synchronously without returning a promise, so be sure to wrap calls to this function in Promise.resolve() when appropriate. Type Promise | * × Search results Close "},"module-nmodule_webEditors_rc_wb_mgr_model_MgrModel.html":{"id":"module-nmodule_webEditors_rc_wb_mgr_model_MgrModel.html","title":"Module: nmodule/webEditors/rc/wb/mgr/model/MgrModel","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/mgr/model/MgrModel new (require(\"nmodule/webEditors/rc/wb/mgr/model/MgrModel\"))(params) API Status: Development A layer that adds edit/add/remove functionality on top of a ComponentTableModel. Due to the incubating status of the manager framework, it is not recommended that you extend MgrModel directly. Instead, extend DeviceMgrModel or PointMgrModel: these will provide more robust functionality for most use cases. Extends: module:nmodule/webEditors/rc/wb/table/model/ComponentTableModel Parameters: Name Type Description params Object Properties Name Type Argument Description newTypes Array.&lt;module:nmodule/webEditors/rc/wb/mgr/MgrTypeInfo&gt; &lt;optional&gt; array of MgrTypeInfo instances representing types that will be offered when creating new instances for this model. See: module:nmodule/driver/rc/wb/mgr/DeviceMgrModel module:nmodule/driver/rc/wb/mgr/PointMgrModel Methods &lt;static&gt; getColumns( [flags]) Get the columns contained in this MgrModel. Parameters: Name Type Argument Description flags Number &lt;optional&gt; if given, only return columns that have these flags. Returns: Type Array.&lt;module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn&gt; addInstances(instances [, names]) Add the instances to this model, to be called when an add dialog or other add operation is committed. The default implementation is to add the given instances to the underlying ComponentSource container, which will commit these changes up to the station. Like Workbench, parented instances will be unparented and re-added to the container component. (Workbench does this via a Mark; here it will be done using BajaScript remove/add calls.) Parameters: Name Type Argument Description instances Array.&lt;baja.Component&gt; the instances to add names Array.&lt;String&gt; &lt;optional&gt; the desired slot names for the instances. If omitted, default names derived from the instance Types will be used. Returns: as of Niagara 4.13, this will resolve with an array of the components that were successfully added. Or: rejected if the add fails. Type Promise.&lt;Array.&lt;baja.Component&gt;&gt; clearRows() Remove all rows from the model. Will trigger a rowsRemoved tinyevent, with parameters: rowsRemoved: the rows that were removed indices: the original indices of the rows that were removed Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#clearRows Returns: Type Promise destroy() Clean up the MgrModel when the parent Manager is being destroyed. This is an opportunity to unhook any remaining event handlers on the model or its data source. Returns: Promise.&lt;*&gt; getColumn(name) Get the column in this model matching the given name. Parameters: Name Type Description name String Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getColumn Returns: the matching column, or null if not found Type module:nmodule/webEditors/rc/wb/table/model/Column getColumnIndex(column) Get the index of the given column. Parameters: Name Type Description column module:nmodule/webEditors/rc/wb/table/model/Column Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getColumnIndex Returns: the column's index, or -1 if not found Type number getColumns( [flags]) Get the current set of columns, optionally filtered by flags. Parameters: Name Type Argument Description flags Number &lt;optional&gt; if given, only return columns that have these flags. Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getColumns Returns: Type Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; getComponentSource() Get the ComponentSource backing this table model. Inherited From: module:nmodule/webEditors/rc/wb/table/model/ComponentTableModel#getComponentSource Returns: Type module:nmodule/webEditors/rc/wb/table/model/ComponentSource getEditableColumns() Return all columns with the EDITABLE flag set. Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getEditableColumns Returns: Type Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; getNewTypes() Get the MgrTypeInfo instances representing the types that are acceptable to add to this model. Returns: array of MgrTypeInfos to add Type Array.&lt;module:nmodule/webEditors/rc/wb/mgr/MgrTypeInfo&gt; getRow(i) Get the row at the given index. Parameters: Name Type Description i number Since: Niagara 4.12 Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getRow Returns: the row at this index, or undefined if not present Type module:nmodule/webEditors/rc/wb/table/model/Row | undefined getRowCount() Get the number of rows in the TableModel. Since: Niagara 4.12 Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getRowCount Returns: Type Number getRowIndex(row) Get the index of the given row. If a filter is applied, returns the index of the row among the currently filtered rows. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getRowIndex Returns: the row's index, or -1 if not found Type number getRows() Get the current set of rows. Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getRows Returns: Type Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; getValueAt(x, y) Ask the column at the given index for the value from the row at the given index. Parameters: Name Type Description x Number column index y Number row index Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getValueAt Returns: promise to be resolved with the value Type Promise insertColumns(toInsert [, index]) Add new columns to the model. Will trigger a columnsAdded tinyevent. Parameters: Name Type Argument Description toInsert Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; index Number &lt;optional&gt; index to insert the columns; will append to the end if omitted Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#insertColumns Returns: promise to be resolved if the insert is successful Type Promise insertRows(toInsert [, index]) Add new rows to the model. If non-Row instances are given, they will be converted to Rows using makeRow(). If a row filter has been set to a non-null function the index passed to this function will be relative to the resulting filtered array returned from getRows(). Will trigger a rowsAdded tinyevent. Parameters: Name Type Argument Description toInsert Array.&lt;(module:nmodule/webEditors/rc/wb/table/model/Row|*)&gt; index Number &lt;optional&gt; index to insert the rows; will append to the end if omitted Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#insertRows Returns: promise to be resolved if the insert is successful Type Promise makeRow(subject) Instantiate a new row for the given subject. insertRows will delegate to this if values are passed in rather than Row instances. Override as necessary. Parameters: Name Type Description subject * Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#makeRow Returns: Type module:nmodule/webEditors/rc/wb/table/model/Row newInstance(typeInfo) Override point to customize how new instances of the selected MgrTypeInfo are instantiated. The default implementation is to simply delegate to the type info's #newInstance() function. Parameters: Name Type Description typeInfo module:nmodule/webEditors/rc/wb/mgr/MgrTypeInfo Returns: Type baja.Value | Promise removeColumns(toRemove [, end]) Remove columns from the model. Will trigger a columnsRemoved tinyevent. Parameters: Name Type Argument Description toRemove Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; | Number the columns to remove; or, start index end Number &lt;optional&gt; end index Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#removeColumns Returns: promise to be resolved if the remove is successful Type Promise removeRows(toRemove [, end]) Remove rows from the model. Will trigger a rowsRemoved tinyevent, with parameters: rowsRemoved: the rows that were removed indices: the original indices of the rows that were removed If a row filter has been set to a non-null function any indices passed to this function will be relative to the resulting filtered array returned from getRows(). Note that rowsRemoved and indices will always be sorted by their original index in the model's rows, regardless of the order of rows passed to the removeRows function. Parameters: Name Type Argument Description toRemove Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; | Number the rows to remove; or, start index end &lt;optional&gt; end index Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#removeRows Returns: promise to be resolved if the remove is successful Type Promise setRowFilter(rowFilterFunction) Filter the table's rows according to the given filter function. Setting the rowFilterFunction to null will remove the current filter and reset the table model to display all rows. Will trigger a rowsFiltered tinyevent. Remember that Array#filter is synchronous, so if the filter needs to use any data that is asynchronously retrieved, the async work must be performed before the filter so that the filter function can work synchronously. Parameters: Name Type Description rowFilterFunction function standard array filter function Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#setRowFilter Throws: if a non-Function is given Type Error Returns: to be resolved after any necessary post-filtering work (this does not make the filtering itself asynchronous). Type Promise sort(sortFunction) Sort the table's rows according to the given sort function. Emits a rowsReordered event. Remember that Array#sort is synchronous, so if the sort needs to use any data that is asynchronously retrieved, the async work must be performed before the sort so that the sort function can work synchronously. Parameters: Name Type Description sortFunction function standard array sort function to receive two Row instances Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#sort Throws: if a non-Function is given Type Error Returns: to be resolved after any necessary post-sorting work (this does not make the sorting itself asynchronous). Type Promise × Search results Close "},"module-nmodule_webEditors_rc_wb_table_model_Column.html":{"id":"module-nmodule_webEditors_rc_wb_table_model_Column.html","title":"Module: nmodule/webEditors/rc/wb/table/model/Column","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/table/model/Column new (require(\"nmodule/webEditors/rc/wb/table/model/Column\"))( [name] [, params]) API Status: Development Column for use in a TableModel. As of Niagara 4.8, arbitrary data can be stored on a Column instance. To enable tables and table exporters to use a context object when formatting table cells, add a context value. Parameters: Name Type Argument Description name String &lt;optional&gt; column name params Object &lt;optional&gt; Properties Name Type Argument Description displayName String &lt;optional&gt; column display name; name will be used if omitted. This should not contain HTML as any HTML will be escaped. flags Number &lt;optional&gt; column flags Mixes In: tinyevents module:nmodule/webEditors/rc/mixin/DataMixin See: module:nmodule/webEditors/rc/wb/table/model/TableModel Example Append context data for formatting use in tables and table exporters. const myBooleanColumn = new Column('myBoolean'); myBooleanColumn.data('context', { trueText: 'Yes!', falseText: 'No!' }); Members &lt;static&gt; flags :Object Available configuration flags for a Column. Type: Object Methods buildCell(row, dom) Builds out the DOM element, typically a td, that represents the intersection of this column with a particular row. Often this will simply be a simple toString() or similar, but could also do more sophisticated things like build widgets. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Returns: promise to be resolved when the element's contents have been fully built. It's also acceptable for overrides of this function to complete synchronously without returning a promise, so be sure to wrap calls to this function in Promise.resolve() when appropriate. Type Promise | * destroyCell(row, dom) Called when the table is destroying the DOM element built for a cell in this column. This gives a Column implementation the chance to clean up any resources that might have been created during the earlier call to #buildCell, perhaps destroying a widget in the cell, for example. As with #buildCell, if this completes synchronously and doesn't return a Promise, the caller must wrap this in a call to Promise.resolve(). Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Returns: Type Promise | * getColumnIcon() Returns a URI for an icon representing this column. Returns null by default; override as needed in subclasses. Returns: a URI for an icon to be shown for this column. Type String getFlags() Get the flags set on this column. Returns: Type Number getName() Get the column name or null if none was given. Returns: Type String &lt;abstract&gt; getValueFor(row) Override point; each column should define its own method of retrieving a value from the given table row. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Throws: if not implemented Type Error Returns: the row value. Note that this is synchronous; if the row's subject is not ready to provide a value, it should not be loaded into the table. Type * hasFlags(flags) Return true if the column has all of the given flags. Parameters: Name Type Description flags Number flags to check for Returns: Type Boolean isEditable() Return true if the column is editable. Returns: Type Boolean isExportable() Return true if the column should show up in export operations, e.g. to CSV. Since: Niagara 4.8 Returns: Type Boolean isHidable() Return true if the column should available in the table's show/hide context menu. Defaults to true. Returns: Type Boolean isReadonly() Return true if the column is readonly. Returns: Type Boolean isSortable() Returns a boolean indicating whether the column should not be sortable via the table headings. Defaults to true. Returns: Type Boolean isUnseen() Return true if the column is unseen. Returns: Type Boolean setEditable(editable) Set or unset the column's EDITABLE flag. Emits a flagsChanged event. Parameters: Name Type Description editable boolean setExportable(exportable) Set or unset whether the column should show up in export operations. Parameters: Name Type Description exportable boolean Since: Niagara 4.8 setFlags(flags) Set the column's flags. Parameters: Name Type Description flags Number Throws: if a non-Number given Type Error setHidable(hidable) Set or unset whether the column should be allowed to be hidden or shown by the table's show/hide context menu. Parameters: Name Type Description hidable boolean setReadonly(readonly) Set or unset the column's READONLY flag. Emits a flagsChanged event. Parameters: Name Type Description readonly boolean setSortable(sortable) Set or unset whether the column should be allowed to be sorted by the table heading. Parameters: Name Type Description sortable boolean setUnseen(unseen) Set or unset the column's UNSEEN flag. Emits a flagsChanged event. Parameters: Name Type Description unseen boolean toDisplayName() Resolves a display name for this column. Returns: promise to be resolved when the element's display name has been fully built. It's also acceptable for overrides of this function to complete synchronously without returning a promise, so be sure to wrap calls to this function in Promise.resolve() when appropriate. Type Promise | * × Search results Close "},"module-nmodule_webEditors_rc_wb_table_model_columns_DisplayNameColumn.html":{"id":"module-nmodule_webEditors_rc_wb_table_model_columns_DisplayNameColumn.html","title":"Module: nmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumn","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumn new (require(\"nmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumn\"))() API Status: Development Column for showing a component's display name. Extends: module:nmodule/webEditors/rc/wb/table/model/Column Methods buildCell(row, dom) Builds out the DOM element, typically a td, that represents the intersection of this column with a particular row. Often this will simply be a simple toString() or similar, but could also do more sophisticated things like build widgets. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#buildCell Returns: promise to be resolved when the element's contents have been fully built. It's also acceptable for overrides of this function to complete synchronously without returning a promise, so be sure to wrap calls to this function in Promise.resolve() when appropriate. Type Promise | * destroyCell(row, dom) Called when the table is destroying the DOM element built for a cell in this column. This gives a Column implementation the chance to clean up any resources that might have been created during the earlier call to #buildCell, perhaps destroying a widget in the cell, for example. As with #buildCell, if this completes synchronously and doesn't return a Promise, the caller must wrap this in a call to Promise.resolve(). Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#destroyCell Returns: Type Promise | * getColumnIcon() Returns a URI for an icon representing this column. Returns null by default; override as needed in subclasses. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getColumnIcon Returns: a URI for an icon to be shown for this column. Type String getFlags() Get the flags set on this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getFlags Returns: Type Number getName() Return name from the webEditors lexicon. Overrides: module:nmodule/webEditors/rc/wb/table/model/Column#getName Returns: Type String getValueFor(row) Return the component's display name. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Overrides: module:nmodule/webEditors/rc/wb/table/model/Column#getValueFor Throws: if the row does not actually have a Component loaded Type Error Returns: the component's display name Type String hasFlags(flags) Return true if the column has all of the given flags. Parameters: Name Type Description flags Number flags to check for Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#hasFlags Returns: Type Boolean isEditable() Return true if the column is editable. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isEditable Returns: Type Boolean isExportable() Return true if the column should show up in export operations, e.g. to CSV. Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isExportable Returns: Type Boolean isHidable() Return true if the column should available in the table's show/hide context menu. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isHidable Returns: Type Boolean isReadonly() Return true if the column is readonly. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isReadonly Returns: Type Boolean isSortable() Returns a boolean indicating whether the column should not be sortable via the table headings. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isSortable Returns: Type Boolean isUnseen() Return true if the column is unseen. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isUnseen Returns: Type Boolean setEditable(editable) Set or unset the column's EDITABLE flag. Emits a flagsChanged event. Parameters: Name Type Description editable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setEditable setExportable(exportable) Set or unset whether the column should show up in export operations. Parameters: Name Type Description exportable boolean Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setExportable setFlags(flags) Set the column's flags. Parameters: Name Type Description flags Number Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setFlags Throws: if a non-Number given Type Error setHidable(hidable) Set or unset whether the column should be allowed to be hidden or shown by the table's show/hide context menu. Parameters: Name Type Description hidable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setHidable setReadonly(readonly) Set or unset the column's READONLY flag. Emits a flagsChanged event. Parameters: Name Type Description readonly boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setReadonly setSortable(sortable) Set or unset whether the column should be allowed to be sorted by the table heading. Parameters: Name Type Description sortable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setSortable setUnseen(unseen) Set or unset the column's UNSEEN flag. Emits a flagsChanged event. Parameters: Name Type Description unseen boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setUnseen toDisplayName() Resolves a display name for this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#toDisplayName Returns: promise to be resolved when the element's display name has been fully built. It's also acceptable for overrides of this function to complete synchronously without returning a promise, so be sure to wrap calls to this function in Promise.resolve() when appropriate. Type Promise | * × Search results Close "},"module-nmodule_webEditors_rc_wb_table_model_columns_IconColumn.html":{"id":"module-nmodule_webEditors_rc_wb_table_model_columns_IconColumn.html","title":"Module: nmodule/webEditors/rc/wb/table/model/columns/IconColumn","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/table/model/columns/IconColumn new (require(\"nmodule/webEditors/rc/wb/table/model/columns/IconColumn\"))() API Status: Development Column type used to show an icon in a specific column the table. This column type will not, by default, be available in the show/hide menu, and will not have the sorting functionality available. Users of this type can change this behavior by calling the #setSortable and #setHidable functions after construction. Extends: module:nmodule/webEditors/rc/wb/table/model/Column Methods buildCell(row, dom) Build the dom for the cell. This will build an IconEditor for each icon returned by the getValueFor function. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Overrides: module:nmodule/webEditors/rc/wb/table/model/Column#buildCell Returns: A promise resolved with the img element added to the dom. Type Promise destroyCell(row, dom) Destroy the IconEditor that was created for this cell. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Overrides: module:nmodule/webEditors/rc/wb/table/model/Column#destroyCell Returns: Type Promise getColumnIcon() Returns a URI for an icon representing this column. Returns null by default; override as needed in subclasses. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getColumnIcon Returns: a URI for an icon to be shown for this column. Type String getFlags() Get the flags set on this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getFlags Returns: Type Number getName() Get the column name or null if none was given. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getName Returns: Type String getValueFor(row) Gets the icon URI for the given row. By default, this will delegate to the Row's getIcon() function. This function can be overridden to allow the source of the icon to be more flexible. This method may return a single icon, or an array of icons. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row A table row Overrides: module:nmodule/webEditors/rc/wb/table/model/Column#getValueFor Returns: the icon(s) for the row, or null if the row does not specify any. Type baja.Icon | Array.&lt;baja.Icon&gt; hasFlags(flags) Return true if the column has all of the given flags. Parameters: Name Type Description flags Number flags to check for Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#hasFlags Returns: Type Boolean isEditable() Return true if the column is editable. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isEditable Returns: Type Boolean isExportable() Return true if the column should show up in export operations, e.g. to CSV. Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isExportable Returns: Type Boolean isHidable() Return true if the column should available in the table's show/hide context menu. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isHidable Returns: Type Boolean isReadonly() Return true if the column is readonly. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isReadonly Returns: Type Boolean isSortable() Returns a boolean indicating whether the column should not be sortable via the table headings. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isSortable Returns: Type Boolean isUnseen() Return true if the column is unseen. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isUnseen Returns: Type Boolean setEditable(editable) Set or unset the column's EDITABLE flag. Emits a flagsChanged event. Parameters: Name Type Description editable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setEditable setExportable(exportable) Set or unset whether the column should show up in export operations. Parameters: Name Type Description exportable boolean Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setExportable setFlags(flags) Set the column's flags. Parameters: Name Type Description flags Number Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setFlags Throws: if a non-Number given Type Error setHidable(hidable) Set or unset whether the column should be allowed to be hidden or shown by the table's show/hide context menu. Parameters: Name Type Description hidable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setHidable setReadonly(readonly) Set or unset the column's READONLY flag. Emits a flagsChanged event. Parameters: Name Type Description readonly boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setReadonly setSortable(sortable) Set or unset whether the column should be allowed to be sorted by the table heading. Parameters: Name Type Description sortable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setSortable setUnseen(unseen) Set or unset the column's UNSEEN flag. Emits a flagsChanged event. Parameters: Name Type Description unseen boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setUnseen toDisplayName() Resolves a display name for this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#toDisplayName Returns: promise to be resolved when the element's display name has been fully built. It's also acceptable for overrides of this function to complete synchronously without returning a promise, so be sure to wrap calls to this function in Promise.resolve() when appropriate. Type Promise | * × Search results Close "},"module-nmodule_webEditors_rc_wb_table_model_columns_JsonObjectPropertyColumn.html":{"id":"module-nmodule_webEditors_rc_wb_table_model_columns_JsonObjectPropertyColumn.html","title":"Module: nmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumn","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumn new (require(\"nmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumn\"))( [name] [, params]) API Status: Development Column for showing the value of a JavaScript Object's property Extends: module:nmodule/webEditors/rc/wb/table/model/Column Parameters: Name Type Argument Description name String &lt;optional&gt; column name and name of the property retrieved from the JavaScript object. params Object &lt;optional&gt; Properties Name Type Argument Description displayName String &lt;optional&gt; column display name; name will be used if omitted. Methods buildCell(row, dom) Builds out the DOM element, typically a td, that represents the intersection of this column with a particular row. Often this will simply be a simple toString() or similar, but could also do more sophisticated things like build widgets. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#buildCell Returns: promise to be resolved when the element's contents have been fully built. It's also acceptable for overrides of this function to complete synchronously without returning a promise, so be sure to wrap calls to this function in Promise.resolve() when appropriate. Type Promise | * destroyCell(row, dom) Called when the table is destroying the DOM element built for a cell in this column. This gives a Column implementation the chance to clean up any resources that might have been created during the earlier call to #buildCell, perhaps destroying a widget in the cell, for example. As with #buildCell, if this completes synchronously and doesn't return a Promise, the caller must wrap this in a call to Promise.resolve(). Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#destroyCell Returns: Type Promise | * getColumnIcon() Returns a URI for an icon representing this column. Returns null by default; override as needed in subclasses. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getColumnIcon Returns: a URI for an icon to be shown for this column. Type String getFlags() Get the flags set on this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getFlags Returns: Type Number getName() Get the column name or null if none was given. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getName Returns: Type String getValueFor(row) Get the value of the JSON element specified in the constructor from the row's loaded component. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Overrides: module:nmodule/webEditors/rc/wb/table/model/Column#getValueFor Returns: Type * Example // Set up the model with a 'text' column. let columns = [new JsonObjectPropertyColumn('text')]; let model = new TableModel({columns: columns}); model.insertRows([new Row({ text: 'This is a test.' })]); // Use the model expect(column.getValueFor(row)).toBe('This is a test.'); hasFlags(flags) Return true if the column has all of the given flags. Parameters: Name Type Description flags Number flags to check for Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#hasFlags Returns: Type Boolean isEditable() Return true if the column is editable. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isEditable Returns: Type Boolean isExportable() Return true if the column should show up in export operations, e.g. to CSV. Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isExportable Returns: Type Boolean isHidable() Return true if the column should available in the table's show/hide context menu. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isHidable Returns: Type Boolean isReadonly() Return true if the column is readonly. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isReadonly Returns: Type Boolean isSortable() Returns a boolean indicating whether the column should not be sortable via the table headings. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isSortable Returns: Type Boolean isUnseen() Return true if the column is unseen. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isUnseen Returns: Type Boolean setEditable(editable) Set or unset the column's EDITABLE flag. Emits a flagsChanged event. Parameters: Name Type Description editable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setEditable setExportable(exportable) Set or unset whether the column should show up in export operations. Parameters: Name Type Description exportable boolean Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setExportable setFlags(flags) Set the column's flags. Parameters: Name Type Description flags Number Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setFlags Throws: if a non-Number given Type Error setHidable(hidable) Set or unset whether the column should be allowed to be hidden or shown by the table's show/hide context menu. Parameters: Name Type Description hidable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setHidable setReadonly(readonly) Set or unset the column's READONLY flag. Emits a flagsChanged event. Parameters: Name Type Description readonly boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setReadonly setSortable(sortable) Set or unset whether the column should be allowed to be sorted by the table heading. Parameters: Name Type Description sortable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setSortable setUnseen(unseen) Set or unset the column's UNSEEN flag. Emits a flagsChanged event. Parameters: Name Type Description unseen boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setUnseen toDisplayName() Resolves a display name for this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#toDisplayName Returns: promise to be resolved when the element's display name has been fully built. It's also acceptable for overrides of this function to complete synchronously without returning a promise, so be sure to wrap calls to this function in Promise.resolve() when appropriate. Type Promise | * × Search results Close "},"module-nmodule_webEditors_rc_wb_table_model_columns_PropertyColumn.html":{"id":"module-nmodule_webEditors_rc_wb_table_model_columns_PropertyColumn.html","title":"Module: nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn new (require(\"nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn\"))(prop [, params]) API Status: Development Column for showing the value of a Complex's Property slot. Extends: module:nmodule/webEditors/rc/wb/table/model/Column Parameters: Name Type Argument Description prop String | baja.Slot the property slot to read from a Complex params Object &lt;optional&gt; Properties Name Type Argument Description type Type &lt;optional&gt; if reading a frozen slot, pass in the Type instance to be able to resolve the display name/facets for that frozen slot getDefaultValue function &lt;optional&gt; a function to provide a custom default value for getValueFor to return if the row's Complex does not have the column prop. The function will be passed the row's Complex. Methods buildCell(row, dom) Shows a display string of the desired property value of the row's loaded Complex. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Overrides: module:nmodule/webEditors/rc/wb/table/model/Column#buildCell Returns: resolves when the cell has been built Type Promise destroyCell(row, dom) Called when the table is destroying the DOM element built for a cell in this column. This gives a Column implementation the chance to clean up any resources that might have been created during the earlier call to #buildCell, perhaps destroying a widget in the cell, for example. As with #buildCell, if this completes synchronously and doesn't return a Promise, the caller must wrap this in a call to Promise.resolve(). Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#destroyCell Returns: Type Promise | * getColumnIcon() If a type param was given, then return an icon for the given slot on that type. Otherwise, return null. Overrides: module:nmodule/webEditors/rc/wb/table/model/Column#getColumnIcon Returns: Type String getFlags() Get the flags set on this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getFlags Returns: Type Number getName() Get the column name or null if none was given. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getName Returns: Type String getValueFor(row) Get the value of the Property specified in the constructor from the row's loaded Complex. If the Complex does not have that Property: If a getDefaultValue param was passed into this column's constructor, the row's Complex will be passed into the function and the result will be returned. If a type param was passed into this column's constructor, the value of the property from the default instance of the given type will be returned. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Overrides: module:nmodule/webEditors/rc/wb/table/model/Column#getValueFor Throws: if the row does not actually have a Complex loaded, or does not have the specified Property (and type is unknown) Type Error Returns: the Property value read from the Complex Type baja.Value hasFlags(flags) Return true if the column has all of the given flags. Parameters: Name Type Description flags Number flags to check for Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#hasFlags Returns: Type Boolean isEditable() Return true if the column is editable. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isEditable Returns: Type Boolean isExportable() Return true if the column should show up in export operations, e.g. to CSV. Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isExportable Returns: Type Boolean isHidable() Return true if the column should available in the table's show/hide context menu. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isHidable Returns: Type Boolean isReadonly() Return true if the column is readonly. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isReadonly Returns: Type Boolean isSortable() Returns a boolean indicating whether the column should not be sortable via the table headings. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isSortable Returns: Type Boolean isUnseen() Return true if the column is unseen. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isUnseen Returns: Type Boolean setEditable(editable) Set or unset the column's EDITABLE flag. Emits a flagsChanged event. Parameters: Name Type Description editable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setEditable setExportable(exportable) Set or unset whether the column should show up in export operations. Parameters: Name Type Description exportable boolean Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setExportable setFlags(flags) Set the column's flags. Parameters: Name Type Description flags Number Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setFlags Throws: if a non-Number given Type Error setHidable(hidable) Set or unset whether the column should be allowed to be hidden or shown by the table's show/hide context menu. Parameters: Name Type Description hidable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setHidable setReadonly(readonly) Set or unset the column's READONLY flag. Emits a flagsChanged event. Parameters: Name Type Description readonly boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setReadonly setSortable(sortable) Set or unset whether the column should be allowed to be sorted by the table heading. Parameters: Name Type Description sortable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setSortable setUnseen(unseen) Set or unset the column's UNSEEN flag. Emits a flagsChanged event. Parameters: Name Type Description unseen boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setUnseen toDisplayName() Resolves the display name of the property slot. Overrides: module:nmodule/webEditors/rc/wb/table/model/Column#toDisplayName Returns: promise to be resolved with the display name Type Promise × Search results Close "},"module-nmodule_webEditors_rc_wb_table_model_columns_ToStringColumn.html":{"id":"module-nmodule_webEditors_rc_wb_table_model_columns_ToStringColumn.html","title":"Module: nmodule/webEditors/rc/wb/table/model/columns/ToStringColumn","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/table/model/columns/ToStringColumn new (require(\"nmodule/webEditors/rc/wb/table/model/columns/ToStringColumn\"))() API Status: Development Column for showing a row's subject string representation. Extends: module:nmodule/webEditors/rc/wb/table/model/Column Methods buildCell(row, dom) Builds out the DOM element, typically a td, that represents the intersection of this column with a particular row. Often this will simply be a simple toString() or similar, but could also do more sophisticated things like build widgets. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#buildCell Returns: promise to be resolved when the element's contents have been fully built. It's also acceptable for overrides of this function to complete synchronously without returning a promise, so be sure to wrap calls to this function in Promise.resolve() when appropriate. Type Promise | * destroyCell(row, dom) Called when the table is destroying the DOM element built for a cell in this column. This gives a Column implementation the chance to clean up any resources that might have been created during the earlier call to #buildCell, perhaps destroying a widget in the cell, for example. As with #buildCell, if this completes synchronously and doesn't return a Promise, the caller must wrap this in a call to Promise.resolve(). Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row dom JQuery Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#destroyCell Returns: Type Promise | * getColumnIcon() Returns a URI for an icon representing this column. Returns null by default; override as needed in subclasses. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getColumnIcon Returns: a URI for an icon to be shown for this column. Type String getFlags() Get the flags set on this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getFlags Returns: Type Number getName() Get the column name or null if none was given. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#getName Returns: Type String getValueFor(row) Return the row's subject string representation. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Overrides: module:nmodule/webEditors/rc/wb/table/model/Column#getValueFor Returns: Type String hasFlags(flags) Return true if the column has all of the given flags. Parameters: Name Type Description flags Number flags to check for Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#hasFlags Returns: Type Boolean isEditable() Return true if the column is editable. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isEditable Returns: Type Boolean isExportable() Return true if the column should show up in export operations, e.g. to CSV. Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isExportable Returns: Type Boolean isHidable() Return true if the column should available in the table's show/hide context menu. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isHidable Returns: Type Boolean isReadonly() Return true if the column is readonly. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isReadonly Returns: Type Boolean isSortable() Returns a boolean indicating whether the column should not be sortable via the table headings. Defaults to true. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isSortable Returns: Type Boolean isUnseen() Return true if the column is unseen. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#isUnseen Returns: Type Boolean setEditable(editable) Set or unset the column's EDITABLE flag. Emits a flagsChanged event. Parameters: Name Type Description editable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setEditable setExportable(exportable) Set or unset whether the column should show up in export operations. Parameters: Name Type Description exportable boolean Since: Niagara 4.8 Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setExportable setFlags(flags) Set the column's flags. Parameters: Name Type Description flags Number Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setFlags Throws: if a non-Number given Type Error setHidable(hidable) Set or unset whether the column should be allowed to be hidden or shown by the table's show/hide context menu. Parameters: Name Type Description hidable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setHidable setReadonly(readonly) Set or unset the column's READONLY flag. Emits a flagsChanged event. Parameters: Name Type Description readonly boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setReadonly setSortable(sortable) Set or unset whether the column should be allowed to be sorted by the table heading. Parameters: Name Type Description sortable boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setSortable setUnseen(unseen) Set or unset the column's UNSEEN flag. Emits a flagsChanged event. Parameters: Name Type Description unseen boolean Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#setUnseen toDisplayName() Resolves a display name for this column. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Column#toDisplayName Returns: promise to be resolved when the element's display name has been fully built. It's also acceptable for overrides of this function to complete synchronously without returning a promise, so be sure to wrap calls to this function in Promise.resolve() when appropriate. Type Promise | * × Search results Close "},"module-nmodule_webEditors_rc_wb_table_model_ComponentSource.html":{"id":"module-nmodule_webEditors_rc_wb_table_model_ComponentSource.html","title":"Module: nmodule/webEditors/rc/wb/table/model/ComponentSource","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/table/model/ComponentSource new (require(\"nmodule/webEditors/rc/wb/table/model/ComponentSource\"))(obj) API Status: Development For use with a ComponentTableModel, acts as a source of components to load into table rows. This should be overridden to provide components from different kinds of sources; e.g. search results. Each subclass must obey the following contract: getComponents() returns an array of all available components added and removed events are emitted when the list of components changes (to notify the ComponentTableModel to add or remove rows). These should be fired with arrays of all components that were just added or removed. changed events are emitted when one of the components in the list has been changed (to notify the ComponentTableModel that row contents have changed). These should be fired once per component that changed, with two arguments: the component that changed, and the property on that component that changed. Parameters: Name Type Description obj Object | baja.Component params object, or the container itself Properties Name Type Description container Object the Component container Mixes In: tinyevents See: module:nmodule/webEditors/rc/wb/table/model/ComponentTableModel Methods &lt;abstract&gt; addComponents(comps [, names]) Add new components to this component source. Must be implemented by subclasses and emit added event as appropriate. Parameters: Name Type Argument Description comps Array.&lt;baja.Component&gt; names Array.&lt;String&gt; &lt;optional&gt; desired names for the components; may not always be respected by subclasses Throws: if not implemented Type Error Returns: as of Niagara 4.13, will be resolved with the actual component instances after they are added Type Promise.&lt;Array.&lt;baja.Component&gt;&gt; destroy() Clean up event handlers associated with this component source. &lt;abstract&gt; getComponents() Gets an array of all matching child Components of the container. Must be implemented by subclasses. Throws: if not implemented Type Error Returns: Type Array.&lt;baja.Component&gt; getContainer() The container object passed to the constructor. Returns: Type Object &lt;abstract&gt; removeComponents(comps) Remove these components from this component source. Must be implemented by subclasses and emit removed event as appropriate. Parameters: Name Type Description comps Array.&lt;baja.Component&gt; Throws: if not implemented Type Error Returns: Type Promise × Search results Close "},"module-nmodule_webEditors_rc_wb_table_model_ComponentTableModel.html":{"id":"module-nmodule_webEditors_rc_wb_table_model_ComponentTableModel.html","title":"Module: nmodule/webEditors/rc/wb/table/model/ComponentTableModel","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/table/model/ComponentTableModel new (require(\"nmodule/webEditors/rc/wb/table/model/ComponentTableModel\"))(params) API Status: Development Table model where each row in the table represents a Component. A ComponentTableModel is backed by a ComponentSource, which provides the list of Components to build into table rows. Extends: module:nmodule/webEditors/rc/wb/table/model/TableModel Parameters: Name Type Description params Object | baja.Component parameters object, or a Component if no parameters required. Properties Name Type Description componentSource baja.Component | module:nmodule/webEditors/rc/wb/table/model/ComponentSource the source of components to build into table rows. If a Component is given it will just be wrapped in a ComponentSource with no parameters. columns Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; Methods clearRows() Remove all rows from the model. Will trigger a rowsRemoved tinyevent, with parameters: rowsRemoved: the rows that were removed indices: the original indices of the rows that were removed Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#clearRows Returns: Type Promise getColumn(name) Get the column in this model matching the given name. Parameters: Name Type Description name String Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getColumn Returns: the matching column, or null if not found Type module:nmodule/webEditors/rc/wb/table/model/Column getColumnIndex(column) Get the index of the given column. Parameters: Name Type Description column module:nmodule/webEditors/rc/wb/table/model/Column Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getColumnIndex Returns: the column's index, or -1 if not found Type number getColumns( [flags]) Get the current set of columns, optionally filtered by flags. Parameters: Name Type Argument Description flags Number &lt;optional&gt; if given, only return columns that have these flags. Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getColumns Returns: Type Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; getComponentSource() Get the ComponentSource backing this table model. Returns: Type module:nmodule/webEditors/rc/wb/table/model/ComponentSource getEditableColumns() Return all columns with the EDITABLE flag set. Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getEditableColumns Returns: Type Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; getRow(i) Get the row at the given index. Parameters: Name Type Description i number Since: Niagara 4.12 Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getRow Returns: the row at this index, or undefined if not present Type module:nmodule/webEditors/rc/wb/table/model/Row | undefined getRowCount() Get the number of rows in the TableModel. Since: Niagara 4.12 Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getRowCount Returns: Type Number getRowIndex(row) Get the index of the given row. If a filter is applied, returns the index of the row among the currently filtered rows. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getRowIndex Returns: the row's index, or -1 if not found Type number getRows() Get the current set of rows. Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getRows Returns: Type Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; getValueAt(x, y) Ask the column at the given index for the value from the row at the given index. Parameters: Name Type Description x Number column index y Number row index Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getValueAt Returns: promise to be resolved with the value Type Promise insertColumns(toInsert [, index]) Add new columns to the model. Will trigger a columnsAdded tinyevent. Parameters: Name Type Argument Description toInsert Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; index Number &lt;optional&gt; index to insert the columns; will append to the end if omitted Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#insertColumns Returns: promise to be resolved if the insert is successful Type Promise insertRows(toInsert [, index]) Add new rows to the model. If non-Row instances are given, they will be converted to Rows using makeRow(). If a row filter has been set to a non-null function the index passed to this function will be relative to the resulting filtered array returned from getRows(). Will trigger a rowsAdded tinyevent. Parameters: Name Type Argument Description toInsert Array.&lt;(module:nmodule/webEditors/rc/wb/table/model/Row|*)&gt; index Number &lt;optional&gt; index to insert the rows; will append to the end if omitted Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#insertRows Returns: promise to be resolved if the insert is successful Type Promise makeRow(subject) Instantiate a new row for the given subject. insertRows will delegate to this if values are passed in rather than Row instances. Override as necessary. Parameters: Name Type Description subject * Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#makeRow Returns: Type module:nmodule/webEditors/rc/wb/table/model/Row removeColumns(toRemove [, end]) Remove columns from the model. Will trigger a columnsRemoved tinyevent. Parameters: Name Type Argument Description toRemove Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; | Number the columns to remove; or, start index end Number &lt;optional&gt; end index Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#removeColumns Returns: promise to be resolved if the remove is successful Type Promise removeRows(toRemove [, end]) Remove rows from the model. Will trigger a rowsRemoved tinyevent, with parameters: rowsRemoved: the rows that were removed indices: the original indices of the rows that were removed If a row filter has been set to a non-null function any indices passed to this function will be relative to the resulting filtered array returned from getRows(). Note that rowsRemoved and indices will always be sorted by their original index in the model's rows, regardless of the order of rows passed to the removeRows function. Parameters: Name Type Argument Description toRemove Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; | Number the rows to remove; or, start index end &lt;optional&gt; end index Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#removeRows Returns: promise to be resolved if the remove is successful Type Promise setRowFilter(rowFilterFunction) Filter the table's rows according to the given filter function. Setting the rowFilterFunction to null will remove the current filter and reset the table model to display all rows. Will trigger a rowsFiltered tinyevent. Remember that Array#filter is synchronous, so if the filter needs to use any data that is asynchronously retrieved, the async work must be performed before the filter so that the filter function can work synchronously. Parameters: Name Type Description rowFilterFunction function standard array filter function Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#setRowFilter Throws: if a non-Function is given Type Error Returns: to be resolved after any necessary post-filtering work (this does not make the filtering itself asynchronous). Type Promise sort(sortFunction) Sort the table's rows according to the given sort function. Emits a rowsReordered event. Remember that Array#sort is synchronous, so if the sort needs to use any data that is asynchronously retrieved, the async work must be performed before the sort so that the sort function can work synchronously. Parameters: Name Type Description sortFunction function standard array sort function to receive two Row instances Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#sort Throws: if a non-Function is given Type Error Returns: to be resolved after any necessary post-sorting work (this does not make the sorting itself asynchronous). Type Promise &lt;inner&gt; exec(updates) This throttles the addition / removal of rows in the table. Parameters: Name Type Description updates Array.&lt;Object&gt; where each object has an add and remove array that contain the components to add or remove Returns: resolves when all rows have been added / removed Type Promise × Search results Close "},"module-nmodule_webEditors_rc_wb_table_model_Row.html":{"id":"module-nmodule_webEditors_rc_wb_table_model_Row.html","title":"Module: nmodule/webEditors/rc/wb/table/model/Row","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/table/model/Row new (require(\"nmodule/webEditors/rc/wb/table/model/Row\"))(subject [, icon]) API Status: Development Row for use in a TableModel. Parameters: Name Type Argument Description subject * the value represented by this row icon * &lt;optional&gt; Mixes In: module:nmodule/webEditors/rc/mixin/DataMixin See: module:nmodule/webEditors/rc/wb/table/model/TableModel Methods getIcon() Get this row's icon. Returns: the icon, or null if none given Type * getSubject() Get the value represented by this row. Returns: Type * × Search results Close "},"module-nmodule_webEditors_rc_wb_table_model_TableModel.html":{"id":"module-nmodule_webEditors_rc_wb_table_model_TableModel.html","title":"Module: nmodule/webEditors/rc/wb/table/model/TableModel","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/table/model/TableModel new (require(\"nmodule/webEditors/rc/wb/table/model/TableModel\"))( [params]) API Status: Development Table Model, for use in backing a Table widget or similar. Parameters: Name Type Argument Description params Object &lt;optional&gt; Properties Name Type Argument Description columns Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; &lt;optional&gt; rows Array &lt;optional&gt; if given, the values will be converted to the model's initial rows by passing them through makeRow(). Mixes In: tinyevents module:nmodule/webEditors/rc/mixin/DataMixin Methods clearRows() Remove all rows from the model. Will trigger a rowsRemoved tinyevent, with parameters: rowsRemoved: the rows that were removed indices: the original indices of the rows that were removed Returns: Type Promise getColumn(name) Get the column in this model matching the given name. Parameters: Name Type Description name String Returns: the matching column, or null if not found Type module:nmodule/webEditors/rc/wb/table/model/Column getColumnIndex(column) Get the index of the given column. Parameters: Name Type Description column module:nmodule/webEditors/rc/wb/table/model/Column Returns: the column's index, or -1 if not found Type number getColumns( [flags]) Get the current set of columns, optionally filtered by flags. Parameters: Name Type Argument Description flags Number &lt;optional&gt; if given, only return columns that have these flags. Returns: Type Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; getEditableColumns() Return all columns with the EDITABLE flag set. Returns: Type Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; getRow(i) Get the row at the given index. Parameters: Name Type Description i number Since: Niagara 4.12 Returns: the row at this index, or undefined if not present Type module:nmodule/webEditors/rc/wb/table/model/Row | undefined getRowCount() Get the number of rows in the TableModel. Since: Niagara 4.12 Returns: Type Number getRowIndex(row) Get the index of the given row. If a filter is applied, returns the index of the row among the currently filtered rows. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Returns: the row's index, or -1 if not found Type number getRows() Get the current set of rows. Returns: Type Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; getValueAt(x, y) Ask the column at the given index for the value from the row at the given index. Parameters: Name Type Description x Number column index y Number row index Returns: promise to be resolved with the value Type Promise insertColumns(toInsert [, index]) Add new columns to the model. Will trigger a columnsAdded tinyevent. Parameters: Name Type Argument Description toInsert Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; index Number &lt;optional&gt; index to insert the columns; will append to the end if omitted Returns: promise to be resolved if the insert is successful Type Promise insertRows(toInsert [, index]) Add new rows to the model. If non-Row instances are given, they will be converted to Rows using makeRow(). If a row filter has been set to a non-null function the index passed to this function will be relative to the resulting filtered array returned from getRows(). Will trigger a rowsAdded tinyevent. Parameters: Name Type Argument Description toInsert Array.&lt;(module:nmodule/webEditors/rc/wb/table/model/Row|*)&gt; index Number &lt;optional&gt; index to insert the rows; will append to the end if omitted Returns: promise to be resolved if the insert is successful Type Promise makeRow(subject) Instantiate a new row for the given subject. insertRows will delegate to this if values are passed in rather than Row instances. Override as necessary. Parameters: Name Type Description subject * Returns: Type module:nmodule/webEditors/rc/wb/table/model/Row removeColumns(toRemove [, end]) Remove columns from the model. Will trigger a columnsRemoved tinyevent. Parameters: Name Type Argument Description toRemove Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; | Number the columns to remove; or, start index end Number &lt;optional&gt; end index Returns: promise to be resolved if the remove is successful Type Promise removeRows(toRemove [, end]) Remove rows from the model. Will trigger a rowsRemoved tinyevent, with parameters: rowsRemoved: the rows that were removed indices: the original indices of the rows that were removed If a row filter has been set to a non-null function any indices passed to this function will be relative to the resulting filtered array returned from getRows(). Note that rowsRemoved and indices will always be sorted by their original index in the model's rows, regardless of the order of rows passed to the removeRows function. Parameters: Name Type Argument Description toRemove Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; | Number the rows to remove; or, start index end &lt;optional&gt; end index Returns: promise to be resolved if the remove is successful Type Promise setRowFilter(rowFilterFunction) Filter the table's rows according to the given filter function. Setting the rowFilterFunction to null will remove the current filter and reset the table model to display all rows. Will trigger a rowsFiltered tinyevent. Remember that Array#filter is synchronous, so if the filter needs to use any data that is asynchronously retrieved, the async work must be performed before the filter so that the filter function can work synchronously. Parameters: Name Type Description rowFilterFunction function standard array filter function Throws: if a non-Function is given Type Error Returns: to be resolved after any necessary post-filtering work (this does not make the filtering itself asynchronous). Type Promise sort(sortFunction) Sort the table's rows according to the given sort function. Emits a rowsReordered event. Remember that Array#sort is synchronous, so if the sort needs to use any data that is asynchronously retrieved, the async work must be performed before the sort so that the sort function can work synchronously. Parameters: Name Type Description sortFunction function standard array sort function to receive two Row instances Throws: if a non-Function is given Type Error Returns: to be resolved after any necessary post-sorting work (this does not make the sorting itself asynchronous). Type Promise × Search results Close "},"module-nmodule_webEditors_rc_wb_table_Table.html":{"id":"module-nmodule_webEditors_rc_wb_table_Table.html","title":"Module: nmodule/webEditors/rc/wb/table/Table","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/table/Table new (require(\"nmodule/webEditors/rc/wb/table/Table\"))(params) API Status: Development Table widget. It supports the following bajaux Properties: fixedHeaders: (boolean) set to true to allow scrolling the table body up and down while the headers remain fixed. This will only make sense when the table widget is instantiated in a block-level element, like a div, whose dimensions are constrained. hideUnseenColumns: (boolean) set to false to cause columns with the UNSEEN flag to always be shown. Defaults to true (unseen columns are hidden by default). density: (string) supports \"small\", \"medium\" and \"large\" font-sizes to specify the density of the table Extends: module:nmodule/webEditors/rc/fe/BaseWidget Parameters: Name Type Description params Object Properties Name Type Argument Description selection module:nmodule/webEditors/rc/util/ListSelection &lt;optional&gt; the ListSelection to manage which rows are currently selected - if not given, a new one will be constructed. Implements: module:nmodule/export/rc/TransformOperationProvider Members &lt;static&gt; CELL_ACTIVATED_EVENT :string Will be triggered when a row is \"activated,\" or selected by the user, such as by double-clicking on it. The handler will receive the Table that triggered the event, and the Row and Column that were activated. Type: string Since: Niagara 4.12 Example dom.on(Table.CELL_ACTIVATED_EVENT, (event, table, activatedRow, activatedColumn) =&gt; { const activatedValue = activatedColumn.getValueFor(activatedRow); // value for the cell const activatedSubject = activatedRow.getSubject(); // value for the row }); &lt;static&gt; ROW_SELECTION_CHANGED_EVENT :string Will be triggered when rows are selected or deselected. The handler will receive the Table that triggered the event. Calculating which rows are selected is cheap but not free, so to protect performance in the case that the actual selected rows are not used, they will not be passed to the handler. To act on the newly selected rows, call table.getSelectedRows(). Type: string Since: Niagara 4.12 Example dom.on(Table.ROW_SELECTION_CHANGED_EVENT, (event, table) =&gt; { const newSelectedRows = table.getSelectedRows(); }); Methods $applyDensity() Applies density property to the table. $handleColumnEvent(tableModel, columns, eventName) Parameters: Name Type Description tableModel module:nmodule/webEditors/rc/wb/table/model/TableModel columns Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; eventName string Returns: Type Promise $initializePagination(model) Arm event handlers on the loaded PaginationModel. Parameters: Name Type Description model module:nmodule/webEditors/rc/wb/table/pagination/PaginationModel Returns: Type Promise $rebuildThead(tableModel) Parameters: Name Type Description tableModel module:nmodule/webEditors/rc/wb/table/model/TableModel Returns: Type Promise destroy() Removes the editor class and emits a destroyed tinyevent. Inherited From: module:nmodule/webEditors/rc/fe/BaseWidget#destroy Returns: call to module:bajaux/Widget#destroy Type Promise doChanged() Detect density property change and apply it to the table See: {module:nmodule/webEditors/rc/wb/table/Table} for valid densities doDestroy() Remove TableWidget class and event handlers from the loaded table model. doInitialize(dom, params) Initialize the HTML table, creating thead, tbody, and tfoot elements. Parameters: Name Type Description dom JQuery params Object optional initialization parameters doLoad(model) Load in a TableModel, immediately rendering all columns and rows. Event handlers will be registered to listen for updates to the table model. Parameters: Name Type Description model module:nmodule/webEditors/rc/wb/table/model/TableModel Throws: if no TableModel provided Type Error Returns: Type Promise getModel() Get the currently loaded TableModel. Since: Niagara 4.6 Returns: Type module:nmodule/webEditors/rc/wb/table/model/TableModel getSelectedRows() Get all rows which are currently selected by the user. Since: Niagara 4.6 Returns: Type Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; getSubject(elem) When showing a context menu, will decide which values in the TableModel are the targets of the right-click operation. If the row being right-clicked is not already selected, then the subject of the corresponding Row will be used to show the context menu. If the row being right-clicked is already selected, then the subjects of all selected Rows will be used. Parameters: Name Type Description elem JQuery Returns: array containing the subjects of the rows being right-clicked. Can return an empty array if no rows are present. Type Array.&lt;*&gt; getTransformOperations() Returns: Type Promise.&lt;Array.&lt;module:nmodule/export/rc/TransformOperation&gt;&gt; initialize(dom) Every BaseWidget will add the editor class to the element and emit an initialized tinyevent when initialized. Parameters: Name Type Description dom JQuery Inherited From: module:nmodule/webEditors/rc/fe/BaseWidget#initialize Returns: call to module:bajaux/Widget#initialize Type Promise shouldValidate( [flag]) This provides an extra hook for an editor to declare itself as needing to be validated before saving or not. The default behavior is to return true if this editor is modified, or if a shouldValidate bajaux Property is present and truthy. If neither of these conditions is true, it will check all known child editors, and return true if it has a child editor that should validate. If flag is given, then the check against the shouldValidate Property will return true only if the value bitwise matches the parameter. See BaseWidget.SHOULD_VALIDATE_ON_SAVE, etc. Parameters: Name Type Argument Description flag Number &lt;optional&gt; Inherited From: module:nmodule/webEditors/rc/fe/BaseWidget#shouldValidate Returns: Type Boolean sort(column, desc) Sort table rows given a column and asc/desc flag. Override this if you want to override the sort ordering. Parameters: Name Type Description column module:nmodule/webEditors/rc/wb/table/model/Column desc boolean Since: Niagara 4.8 Returns: Type Promise | * × Search results Close "},"module-nmodule_webEditors_rc_wb_table_tree_TreeNodeRow.html":{"id":"module-nmodule_webEditors_rc_wb_table_tree_TreeNodeRow.html","title":"Module: nmodule/webEditors/rc/wb/table/tree/TreeNodeRow","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/table/tree/TreeNodeRow new (require(\"nmodule/webEditors/rc/wb/table/tree/TreeNodeRow\"))(node) API Status: Development Row backed by a TreeNode. The value and icon of the TreeNode will be used as the subject and icon of the row itself. This type of row will be used in a TreeTableModel. This allows the use of regular Column types in the model (using the actual value of the node as the row's subject) while still being able to use the expand/collapse functionality of a tree (by accessing #getTreeNode()). Extends: module:nmodule/webEditors/rc/wb/table/model/Row Parameters: Name Type Description node module:nmodule/webEditors/rc/wb/tree/TreeNode Since: Niagara 4.6 See: module:nmodule/webEditors/rc/wb/table/tree/TreeTableModel Methods getIcon() Overrides: module:nmodule/webEditors/rc/wb/table/model/Row#getIcon Returns: the icon of the backing TreeNode Type Array.&lt;String&gt; getSubject() Get the value represented by this row. Inherited From: module:nmodule/webEditors/rc/wb/table/model/Row#getSubject Returns: Type * getTreeNode() Returns: the node backing this row Type module:nmodule/webEditors/rc/wb/tree/TreeNode × Search results Close "},"module-nmodule_webEditors_rc_wb_table_tree_TreeTableModel.html":{"id":"module-nmodule_webEditors_rc_wb_table_tree_TreeTableModel.html","title":"Module: nmodule/webEditors/rc/wb/table/tree/TreeTableModel","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/table/tree/TreeTableModel new (require(\"nmodule/webEditors/rc/wb/table/tree/TreeTableModel\"))(params) API Status: Development A TableModel backed by a TreeNode. Each Row in the table must also be backed by a TreeNode. You should not typically call this constructor directly - use the .make() method instead. Extends: module:nmodule/webEditors/rc/wb/table/model/TableModel Parameters: Name Type Description params Object Properties Name Type Argument Description node module:nmodule/webEditors/rc/wb/tree/TreeNode &lt;optional&gt; the root TreeNode backing this model Example //when deciding which columns to use in the model, remember that rows //will return the actual values via getSubject(), so ordinary //Columns/MgrColumns can be used. custom column types can call //row.getTreeNode() if necessary. TreeTableModel.make({ columns: [ new PropertyMgrColumn('prop1'), new PropertyMgrColumn('prop2') ] }); //when inserting, values must be TreeNodes. treeTableModel.insertRows(components.map(function (comp) { var node = new TreeNode('component:' + comp.getName()); node.value = function () { return comp; }; return node; }); Methods &lt;static&gt; make(params) Create a new TreeTableModel instance. Parameters: Name Type Description params Object Properties Name Type Description node module:nmodule/webEditors/rc/wb/tree/TreeNode the root node backing this model Returns: promise to be resolved with the new TreeTableModel instance, containing one row per child node of the root node passed in Type Promise.&lt;module:nmodule/webEditors/rc/wb/table/tree/TreeTableModel&gt; clearRows() Remove all rows from the model. Will trigger a rowsRemoved tinyevent, with parameters: rowsRemoved: the rows that were removed indices: the original indices of the rows that were removed Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#clearRows Returns: Type Promise collapse(row) Get child nodes of the given Row's TreeNode and remove their corresponding Rows. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/tree/TreeNodeRow Returns: promise to be resolved with an array of the rows that were removed Type Promise.&lt;Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt;&gt; expand(row) Get child nodes of the given Row's TreeNode and insert new Rows for each one. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/tree/TreeNodeRow Returns: promise to be resolved with an array of the inserted rows Type Promise.&lt;Array.&lt;module:nmodule/webEditors/rc/wb/table/tree/TreeNodeRow&gt;&gt; getColumn(name) Get the column in this model matching the given name. Parameters: Name Type Description name String Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getColumn Returns: the matching column, or null if not found Type module:nmodule/webEditors/rc/wb/table/model/Column getColumnIndex(column) Get the index of the given column. Parameters: Name Type Description column module:nmodule/webEditors/rc/wb/table/model/Column Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getColumnIndex Returns: the column's index, or -1 if not found Type number getColumns( [flags]) Get the current set of columns, optionally filtered by flags. Parameters: Name Type Argument Description flags Number &lt;optional&gt; if given, only return columns that have these flags. Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getColumns Returns: Type Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; getDepth(row) Get the depth of this Row's TreeNode from the TreeTableModel's root node. A direct child of the root will have a depth of 0, etc. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Returns: the depth of the Row, or null if the depth could not be determined (e.g. the Row is not actually contained in this model). Type number | null getEditableColumns() Return all columns with the EDITABLE flag set. Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getEditableColumns Returns: Type Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; getRootNode() Get the root node backing this TreeTableModel. Returns: Type module:nmodule/webEditors/rc/wb/tree/TreeNode getRow(i) Get the row at the given index. Parameters: Name Type Description i number Since: Niagara 4.12 Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getRow Returns: the row at this index, or undefined if not present Type module:nmodule/webEditors/rc/wb/table/model/Row | undefined getRowCount() Get the number of rows in the TableModel. Since: Niagara 4.12 Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getRowCount Returns: Type Number getRowIndex(row) Get the index of the given row. If a filter is applied, returns the index of the row among the currently filtered rows. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getRowIndex Returns: the row's index, or -1 if not found Type number getRows() Get the current set of rows. Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getRows Returns: Type Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; getValueAt(x, y) Ask the column at the given index for the value from the row at the given index. Parameters: Name Type Description x Number column index y Number row index Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#getValueAt Returns: promise to be resolved with the value Type Promise insertColumns(toInsert [, index]) Add new columns to the model. Will trigger a columnsAdded tinyevent. Parameters: Name Type Argument Description toInsert Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; index Number &lt;optional&gt; index to insert the columns; will append to the end if omitted Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#insertColumns Returns: promise to be resolved if the insert is successful Type Promise insertRows(toInsert [, index]) Add new rows to the model. If non-Row instances are given, they will be converted to Rows using makeRow(). If a row filter has been set to a non-null function the index passed to this function will be relative to the resulting filtered array returned from getRows(). Will trigger a rowsAdded tinyevent. Parameters: Name Type Argument Description toInsert Array.&lt;(module:nmodule/webEditors/rc/wb/table/model/Row|*)&gt; index Number &lt;optional&gt; index to insert the rows; will append to the end if omitted Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#insertRows Returns: promise to be resolved if the insert is successful Type Promise isExpandable(row) Return true if the Row's TreeNode might have child nodes. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/tree/TreeNodeRow Returns: Type boolean isExpanded(row) Return true if the given Row is marked as expanded. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Returns: Type boolean makeRow(subject) Create a new Row instance with a TreeNode as the subject. Parameters: Name Type Description subject module:nmodule/webEditors/rc/wb/tree/TreeNode Overrides: module:nmodule/webEditors/rc/wb/table/model/TableModel#makeRow Returns: Type module:nmodule/webEditors/rc/wb/table/tree/TreeNodeRow removeColumns(toRemove [, end]) Remove columns from the model. Will trigger a columnsRemoved tinyevent. Parameters: Name Type Argument Description toRemove Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Column&gt; | Number the columns to remove; or, start index end Number &lt;optional&gt; end index Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#removeColumns Returns: promise to be resolved if the remove is successful Type Promise removeRows(toRemove [, end]) Remove rows from the model. Will trigger a rowsRemoved tinyevent, with parameters: rowsRemoved: the rows that were removed indices: the original indices of the rows that were removed If a row filter has been set to a non-null function any indices passed to this function will be relative to the resulting filtered array returned from getRows(). Note that rowsRemoved and indices will always be sorted by their original index in the model's rows, regardless of the order of rows passed to the removeRows function. Parameters: Name Type Argument Description toRemove Array.&lt;module:nmodule/webEditors/rc/wb/table/model/Row&gt; | Number the rows to remove; or, start index end &lt;optional&gt; end index Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#removeRows Returns: promise to be resolved if the remove is successful Type Promise setRowFilter(rowFilterFunction) Filter the table's rows according to the given filter function. Setting the rowFilterFunction to null will remove the current filter and reset the table model to display all rows. Will trigger a rowsFiltered tinyevent. Remember that Array#filter is synchronous, so if the filter needs to use any data that is asynchronously retrieved, the async work must be performed before the filter so that the filter function can work synchronously. Parameters: Name Type Description rowFilterFunction function standard array filter function Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#setRowFilter Throws: if a non-Function is given Type Error Returns: to be resolved after any necessary post-filtering work (this does not make the filtering itself asynchronous). Type Promise sort(sortFunction) Sort the table's rows according to the given sort function. Emits a rowsReordered event. Remember that Array#sort is synchronous, so if the sort needs to use any data that is asynchronously retrieved, the async work must be performed before the sort so that the sort function can work synchronously. Parameters: Name Type Description sortFunction function standard array sort function to receive two Row instances Inherited From: module:nmodule/webEditors/rc/wb/table/model/TableModel#sort Throws: if a non-Function is given Type Error Returns: to be resolved after any necessary post-sorting work (this does not make the sorting itself asynchronous). Type Promise toggle(row) Collapse the row if it is expanded, and vice versa. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row Returns: Type Promise × Search results Close "},"module-nmodule_webEditors_rc_wb_tree_TreeNode.html":{"id":"module-nmodule_webEditors_rc_wb_tree_TreeNode.html","title":"Module: nmodule/webEditors/rc/wb/tree/TreeNode","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Module: nmodule/webEditors/rc/wb/tree/TreeNode new (require(\"nmodule/webEditors/rc/wb/tree/TreeNode\"))(name, display [, kids]) API Status: Development Represents a single node in a tree. One node has a number of different properties, as well as a reference to a backing value this node represents. This backing value could be a nav node on a station, a file or folder on the file system, etc. It also maintains a list of children. Note that this list of children will be lazily, asynchronously requested the first time it is loaded. After that, the list of children must be kept up to date using the parent node's mutators (added/removed/etc.). Please note that any child nodes added to a parent node effectively become the parent node's \"property\" and are subject to alteration by the parent. If the parent node is activated, the child nodes will likewise be activated, and same for destroying. Parameters: Name Type Argument Description name String the node name display String the node display kids Array.&lt;module:nmodule/webEditors/rc/wb/tree/TreeNode&gt; &lt;optional&gt; an array of child nodes Mixes In: tinyevents Methods &lt;static&gt; BY_NODE_NAME() Pass this to #reorder to sort all tree nodes by name. &lt;abstract&gt; $loadKids( [params]) Performs a one-time, asynchronous load of child nodes. On a vanilla TreeNode, this does nothing but resolve the array of child nodes passed into the constructor. In subclasses, this should be overridden to perform any network calls or other asynchronous behavior to load child nodes. This method is intended to be overridden by subclasses, but not called directly. It will automatically be used the first time getKids() is called. After getKids() is called for the first time, any updates or changes to the list of nodes should only be done through the add(), remove(), and other mutator methods. Do not set the parent of the child nodes created by this method - they will automatically be parented when getKids() is called. Important contractual note: in some cases, the async operation to load kids can be batched together if loading a number of nodes at once. If $loadKids receives a Batch object, it is obligated to ensure that any progressCallback param passed in will be called with a commitReady progress event to notify the caller that the batch is ready to be committed. Parameters: Name Type Argument Description params Object &lt;optional&gt; Properties Name Type Argument Description batch baja.comm.Batch &lt;optional&gt; optional Batch that may be used when loading multiple tree nodes. See method description for contract. progressCallback function &lt;optional&gt; optional function that will receive progress notifications during the load process. Returns: promise to be resolved when all child nodes have been loaded. It should be resolved with an array of TreeNode instances. Type Promise activate() Activates the node and all of its child nodes. This method works very similarly to Widget#initialize() in that it delegates the implementation of the destruction of each individual node to doActivate(). Note that child nodes will not be activated if they are not yet loaded. Returns: promise to be resolved when this node and all child nodes (if loaded) have been activated Type Promise add(kid) Adds a child node to the end of this parent's list of child nodes. The child will automatically be parented when it is set. If this node has been activated, the child node will likewise be activated when it is added. Parameters: Name Type Description kid module:nmodule/webEditors/rc/wb/tree/TreeNode Returns: promise to be resolved when the child node is added, or rejected if the child node is already parented, if the list of children is not yet loaded (getKids() not yet called), or an existing child with a duplicate name is found Type Promise destroy() Destroys the node and all of its child nodes. This method works very similarly to Widget#destroy() in that it delegates the implementation of the destruction of each individual node to doDestroy(). Note that child nodes will not be destroyed if they are not yet loaded. Returns: promise to be resolved when this node and all child nodes (if loaded) have been destroyed Type Promise doActivate() Implementation of activate(). This method should acquire any resources the node needs to function properly - registering event handlers, subscribing components, etc. Ensure that all resources acquired are properly released in doDestroy(). By default, does nothing. Returns: promise to be resolved when activation is complete - or return undefined if no asynchronous work needs to be done Type Promise doDestroy() Implementation of destroy(). This method should release any resources acquired by the node during doActivate(). By default, does nothing. Returns: promise to be resolved when destruction is complete - or return undefined if no asynchronous work needs to be done Type Promise doDrop(values) A tree node that returned true from isDropTarget can then take an array of values to perform the drop action. By default, this function does nothing. Parameters: Name Type Description values Array the values being dropped onto this node Returns: promise to be resolved when the drop operation completes, or rejected if the given array does not hold valid data to perform a drop operation. Type Promise equals(value) Test to see if this node is equivalent to some value. By default, a node is equivalent only to itself. Parameters: Name Type Description value * Returns: Type boolean getDescendent(names) Retrieves a child by traversing the tree using the names provided. Each name will traverse a level deeper into the tree. Parameters: Name Type Description names Array.&lt;string&gt; Returns: promise to be resolved with the descendent node, or undefined if not found Type Promise getFullPath() The full path of names leading to this node, beginning from the parent node. Since names must be unique among siblings, each node in a tree will therefore have a unique full path. Returns: an array of node names, with the name of the root node first and this node last Type Array.&lt;String&gt; getIcon() Return a list of URIs to image files that represent a display icon for this node. Typically, this will only return zero or one URI, but may return several if the node's icon should be layered or have a \"badge\" applied. By default, this just returns an empty array. Returns: an array of URIs to image files Type Array.&lt;String&gt; getKid(name) Retrieves a child node by name. If child nodes are not yet loaded, they will be upon calling this method. Parameters: Name Type Description name String Returns: promise to be resolved with the child node with the given name, or undefined if not found Type Promise getKids( [params]) Resolves all child nodes of this node. If they have already been loaded, they will be resolved immediately, otherwise they will be asynchronously loaded in a one-time operation. (The children will not be loaded if the node was destroyed first.) After getKids() is called for the first time, any updates or changes to the list of nodes should only be done through the add(), remove(), and other mutator methods. Parameters: Name Type Argument Description params Object &lt;optional&gt; params object to be passed to $loadKids. This should be provided if you are calling getKids without being sure you have called $loadKids first. Returns: promise to be resolved with an array of child TreeNodes Type Promise getName() The name of this node. If this node has siblings, note that names must be unique among all sibling nodes. Returns: Type String getParent() The parent node. If the node is unparented, will return null. Returns: Type module:nmodule/webEditors/rc/wb/tree/TreeNode isDraggable() Return true if this tree node is eligible to begin a drag operation. Returns: false by default Type boolean isDropTarget() A tree node has the option of accepting data from a drag and drop operation. If a node is to accept drag and drop, this function should be overridden to examine the currently loaded value (if appropriate) and determine if it can accept a drop operation. It is up to the NavTree that holds this node to return false from the event handler, apply any CSS styles, etc. Naturally, any node that implements this function should also implement doDrop to perform the requested drop operation. Returns: false by default Type boolean isSelectable() Override this method to return false to prevent this node from being selected in the tree. Returns: true by default. Type Boolean &lt;abstract&gt; mayHaveKids() Return false if you know for a fact that this node has no child nodes. Why is this different from the bajaui implementation which declares a getChildCount() method? Remember that retrieving child nodes is asynchronous, so it's not always possible to count them synchronously. This function will mainly serve as a hint to UI widgets whether to show an expander for this node, with the understanding that getKids() may still resolve zero nodes, even if this function returned true. Returns: Type boolean remove(kid) Removes a child node from this parent's list of child nodes. Note that child's destroy() will be called when it is removed. Parameters: Name Type Description kid module:nmodule/webEditors/rc/wb/tree/TreeNode | String the node to remove, or the name of the node to remove Returns: promise to be resolved with the removed/destroyed child, or rejected if the given node or node name is not found in the existing list of children, or if the list of children is not yet loaded (getKids() not yet called) Type Promise rename(name, newName) Renames one child node. Parameters: Name Type Description name String the name of the existing child node to rename newName String the new name of the child node Returns: promise to be resolved when the child is renamed, or rejected if the child was not found, if the node already has a sibling by the new name, or if the list of children is not yet loaded (getKids() not yet called) Type Promise reorder(newKids) Sets the order of this node's children. The input array must contain the exact same set of children as this node has, but in any order. Parameters: Name Type Description newKids Array.&lt;module:nmodule/webEditors/rc/wb/tree/TreeNode&gt; | Array.&lt;String&gt; | function the children of this node, in the desired new order. This can be an array of the actual nodes rearranged, or an array of node names. It can also be a sort function that takes two tree nodes; the existing nodes will be reordered according to this function. Returns: promise to be resolved when the child nodes are reordered, or rejected if the input array contains a different number of nodes than this node has children, if it contains a node that does not exist as a child node, or if the list of children is not yet loaded (getKids() not yet called) Type Promise toDisplay() The display name of this node, to be shown in user interfaces. May make asynchronous calls to format the display name. Returns: promise to be resolved with the display name Type Promise toHyperlinkUri() When activated, many tree nodes will instigate a page change. Override this function to specify the hyperlink target. Returns: promise to be resolved with the hyperlink target. Bu default, resolves undefined. Type Promise toString() Returns a string representation of this node. By default, just returns the name. Returns: Type String value() Returns the backing value represented by this node. By default, this will return undefined, since a vanilla TreeNode is really just a name/display pair. Subclasses of TreeNode intended to represent real-life values should override this method to return the appropriate value. Returns: Type * × Search results Close "},"tutorial-6-managers.html":{"id":"tutorial-6-managers.html","title":"Tutorial: Managers","body":" webEditors Modules nmodule/webEditors/rc/fe/baja/BaseEditornmodule/webEditors/rc/fe/BaseWidgetnmodule/webEditors/rc/fe/fenmodule/webEditors/rc/fe/feDialogsnmodule/webEditors/rc/wb/mgr/commands/MgrCommandnmodule/webEditors/rc/wb/mgr/Managernmodule/webEditors/rc/wb/mgr/MgrLearnnmodule/webEditors/rc/wb/mgr/MgrStateHandlernmodule/webEditors/rc/wb/mgr/MgrTypeInfonmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumnnmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrColumnnmodule/webEditors/rc/wb/mgr/model/MgrModelnmodule/webEditors/rc/wb/table/model/Columnnmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumnnmodule/webEditors/rc/wb/table/model/columns/IconColumnnmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumnnmodule/webEditors/rc/wb/table/model/columns/PropertyColumnnmodule/webEditors/rc/wb/table/model/columns/ToStringColumnnmodule/webEditors/rc/wb/table/model/ComponentSourcenmodule/webEditors/rc/wb/table/model/ComponentTableModelnmodule/webEditors/rc/wb/table/model/Rownmodule/webEditors/rc/wb/table/model/TableModelnmodule/webEditors/rc/wb/table/Tablenmodule/webEditors/rc/wb/table/tree/TreeNodeRownmodule/webEditors/rc/wb/table/tree/TreeTableModelnmodule/webEditors/rc/wb/tree/TreeNode Tutorials Managers Managers Bajaux Manager Framework Contents Introduction The Manager Type MgrModel Component Sources MgrColumn Rows Commands Manager State MgrTypeInfo Discovery Subscribers Point Manager Device Manager Glossary Introduction For a number of years, Niagara's bajaui manager framework has provided drivers and other component containers with a common, consistent user interface framework that allows a user to add, edit and delete components in a station. It also provides a user with a familiar way to discover new items and add them to the station using a simple drag and drop process between two tables. The bajaux manager framework exists to provide a similar, consistent batch editing framework using HTML5 technologies, allowing the same style of configuration to be performed by a user in a browser environment, but without the need for a Java runtime. The manager framework is based around the concept of a view containing one or more tables. Niagara drivers are consumers of the manager APIs, with views provided to allow a user to configure devices and points within a station. As an example of the manager views in use, a user may use a manager to discover the points in a remote device via a protocol implemented by the driver. Once complete, the view's discovery table will display the points found during the discovery. The user can then pick points to add to the station, with the manager containing code for creating and configuring the proxy extensions for the points. The manager can configure the component from the discovered item's properties, but also provides editing capabilities to allow a user to adjust the properties, as required. As with the bajaui version, the bajaux manager framework is implemented around table widgets and their corresponding models. A bajaux Manager will, at a minimum, create a model for a main table, providing a number of columns that describe the values that should be shown and which of those values should be available for editing. In addition to this basic functionality, the manager may optionally provide the support for discovery of new items, which will require the creation of a second model for the discovery table. As with the Java version, these tables are arranged with the discovery table on top and the main table at the bottom. A user may choose an item from the upper discovery table, and then by dragging and dropping onto the lower database table, or by using the 'Add' command, can edit the properties of a newly created Component before it is added to the station. Warning: This document describes an API that should currently be considered experimental (_development_ level stability). The current feature set does not have complete parity with the Java manager framework and the JavaScript API may be subject to changes in future releases as more functionality is added. Any third-party code written against this API may require changes to function correctly with future releases of Niagara. The Manager Type The base of the UX manager framework is a JavaScript type called Manager. This is a bajaux Widget type that will create a child Table widget for the main table and load the model into it. In addition to this, it also creates a CommandButtonGroup widget for the manager's commands, which will be arranged horizontally along the bottom edge of the view. It also provides some functionality to save manager state temporarily and restore it again. The functionality provided by the base Manager type is relatively small; extra functionality is provided by derived classes and by the use of mixed-in JavaScript modules. The Manager type can be accessed by requiring it from the webEditors-ux module: define(['nmodule/webEditors/rc/wb/mgr/Manager'], function (Manager) { The constructor of the Manager type requires some parameters to be passed via object properties. The required parameters are as follows: moduleName - a string with the name of the module that contains the new manager type. keyName - a string to identify the manager, typically the name of the type. These values should be specified when the constructor of the derived class calls the super class constructor. var MyManager = function MyManager (params) { Manager.call(this, { moduleName: 'myModule', keyName: 'MyManager' // Typically the name of the type }); }; The key and module names are used for the purposes of deciding a lexicon to load strings from, when necessary, and are also used to define a key for storing manager state, thus requiring them to be unique for each concrete manager type. As with any other bajaux Widget acting as a view on a component, the manager type must have a corresponding Java type implementing the BIJavaScript interface (and in a manager's case should implement BIFormFactorMax, too), and the Java type should be registered as an agent on the relevant component type via the module-include.xml file. MgrModel Each concrete manager type must define a model for its main table. In bajaux, the MgrModel type provides the base class for main table models. Derived from TableModel, it adds some extra functionality for creating new component instances and adding them to the station. The MgrModel type's constructor requires: One or more Columns. A Component or ComponentSource used to obtain the rows. An array of MgrTypeInfo instances representing the types of any new objects that the manager may create. For any Manager, the makeModel method must be implemented. It should resolve to an instance of MgrModel, or a subclass of it. ///// MyManager.js: /** * @param {baja.Component} component the component being loaded into the Manager * @returns {Promise.&lt;MgrModel&gt;} */ MyManager.prototype.makeModel = function (component) { return MyMgrModel.make(component); }; ///// MyMgrModel.js: var TYPES_MY_MANAGER_CAN_CREATE = [ 'control:BooleanWritable', 'control:NumericWritable' ]; //it is permitted, but not required, to subclass MgrModel. var MyMgrModel = function MyMgrModel () { MgrModel.apply(this, arguments); }; MyMgrModel.prototype = Object.create(MgrModel.prototype); MyMgrModel.prototype.constructor = MyMgrModel; /** @returns {Promise.&lt;MgrModel&gt;} */ MyMgrModel.make = function (component) { return MgrTypeInfo.make(TYPES_MY_MANAGER_CAN_CREATE) .then(function (newTypes) { return new MyMgrModel({ columns: makeColumns(), // An array of columns for the model componentSource: component, // The component being loaded into the manager or a component source newTypes: newTypes // The types that the manager may create new instances of }); }); }; In the above example, the makeColumns function would instantiate one or more MgrColumn types and return them in an array. The MgrModel you create can be accessed as soon as doLoad() is called using the getModel method. If overriding doLoad(), be sure to call the super method as Manager#doLoad() provides important functionality. MyManager.prototype.doLoad = function (component) { var model = this.getModel(); model.getRows().forEach(function (row) { /* ... */ }); // be sure to call super. return Manager.prototype.doLoad.apply(this, arguments); }; Component Sources A manager model needs a way to obtain the initial set of Rows it should contain. In bajaux, when viewing components of a station, this is provided by an instance of a ComponentSource. The most common type of source used for manager models will be a ContainerComponentSource, which uses the child property values of a parent container as the subjects for the model's rows. If a Component is passed to the model constructor, rather than a ComponentSource, then a ContainerComponentSource will be created automatically as the default. In addition to returning the rows for the model, the source also has the responsibility for adding or removing items from the container. One important feature of the ContainerComponentSource to note is the filter functionality. The source's default behavior is to return all visible children of the parent container (by checking each slot's flags). This may be appropriate in many cases, but in others it may be necessary to have finer control over which children are used for the table rows. The ContainerComponentSource provides for this by taking an optional filter parameter in its constructor. This filter may take one of two forms: an array of type specs to identify types that should be allowed for the table's rows, or a predicate function, called for each Slot on the parent container and receiving the slot as a parameter, which should return true for the children that should be included in the model. Taking the example MgrModel defined above, it could be modified to filter out components via the array method: var TYPES_MY_MANAGER_SHOULD_DISPLAY = [ 'control:ControlPoint', 'driver:PointFolder' ]; MyMgrModel.make = function (component) { return MgrTypeInfo.make(TYPES_MY_MANAGER_CAN_CREATE) .then(function (newTypes) { return new MyMgrModel({ columns: makeColumns(), componentSource: new ContainerComponentSource({ container: component, filter: TYPES_MY_MANAGER_SHOULD_DISPLAY }), newTypes: newTypes }); }); }; It could also filter its rows by passing a function as the filter parameter: function filterComponentsByTypeAndVisibility(prop) { var visible = !(prop.getFlags() &amp; baja.Flags.HIDDEN), type = prop.getType(); return visible &amp;&amp; (type.is('control:ControlPoint') || type.is('driver:PointFolder')); } //... componentSource: new ContainerComponentSource({ container: component, filter: filterComponentsByTypeAndVisibility }) //... MgrColumn A manager's table model must define one or more columns to define exactly what should be displayed for each row's subject and, if the column supports editing the value, how a modified value should be saved for a subject. All columns are derived from a base Column type. This is a generic table column type and is usable outside of manager views. For manager specific functionality, the MgrColumn type is used. The MgrColumn type can be used in one of two ways: As the direct base class for a new type of manager column. As a mixin to augment a more generic Column type with the functionality required to be used in a manager model. To use it as a direct base class, set up the prototype and apply the constructor in the usual way: // Create a new manager column, directly inheriting from MgrColumn var MyMgrColumn = function MyMgrColumn () { MgrColumn.apply(this, arguments); }; MyMgrColumn.prototype = Object.create(MgrColumn.prototype); MyMgrColumn.prototype.constructor = MyMgrColumn; Alternatively, to apply it to another generic Column type that may have uses in other, non-manager tables, use the static mixin function: // Create a new manager column type, derived from another non-manager column, mixing in MgrColumn var MyOtherMgrColumn = function MyOtherMgrColumn () { FooColumn.apply(this, arguments); }; MyOtherMgrColumn.prototype = Object.create(FooColumn.prototype); MyOtherMgrColumn.prototype.constructor = MyOtherMgrColumn; MgrColumn.mixin(MyOtherMgrColumn); The Column base class has a name parameter in the constructor. The column names are used when setting a component's initial values from a discovered item. This will be described in the discovery section. The constructor may also be provided with a separate displayName parameter, to provide a localized user visible name for the column. If this parameter is not specified, the name will be used as the display name. When creating a column, a manager may also wish to set the flags via the constructor. There are three flags that can be specified: Column.flags.EDITABLE - Use this to indicate that the component editor should show the value for the column's value. Column.flags.UNSEEN - Use this to indicate that the column should not be visible by default. The user can choose to show it if they wish. Column.flags.READONLY - Use this to indicate that the column's value should be shown in the component editor, but should be readonly. These flags can be bitwise-combined as required for the column. getValueFor is an abstract method on the Column type that should return the appropriate value for a given row. All new manager columns must implement this method. /** * Return this column's value for the given row. */ MyMgrColumn.prototype.getValueFor = function (row) { var componentInRow = row.getSubject(); return getSomeValueFrom(componentInRow); }; Another important method on the Column type is buildCell. This is called when the table is creating its DOM content. The first parameter is the row, the second is the jQuery object for the &lt;td&gt; element. /** * Build the dom content for the given row. */ MyMgrColumn.prototype.buildCell = function (row, dom) { var value = this.getValueFor(row), text = getDisplayText(value); return Promise.resolve(dom.text(text)); }; As well as displaying a value, a new manager column may also want to provide support for editing a value. There are several steps involved in editing a column's value: configuring the field editor, validating a user's change and committing a change back to the row's subject. The getConfigFor override point allows the column to set a configuration object for the field editor before it is built. The default implementation will coalesce multiple rows into a single value to be provided to the editor as the value. If a manager requires specialized behavior, it may override this method and provide the required properties that will be passed to the field editor via the fe.makeFor() method. See the fe documentation for further details of editor configuration. Data validation is a task an editable manager column will almost certainly want to perform. The mgrValidate method can be overridden to have an opportunity to inspect the proposed changes for the model's rows and possibly reject them. The validation method will be passed the model and an array of proposed changes for the column. Each item in the array will either contain the proposed change to the row at the same index, or null if there is no change for that particular row. The method should inspect the values in the array and return a rejected Promise if any values do not pass the validation criteria. /** * Validate the proposed changes to the rows. */ MyMgrColumn.prototype.mgrValidate = function (model, data, params) { for (var i = 0; i &lt; data.length; i++) { if (!isValid(data[i])) { return Promise.reject(new Error('invalid value')); } } }; After the edits for a column have been validated, they must be committed back to the source. The commit method should take the given value and write it to the subject of the Row, returning a Promise that will resolve when the write is complete. The framework can support the use of batches when rows are being committed, which will enable several changes to be sent to the station in a single network call. /** * Commit the changes back to the station. */ MyMgrColumn.prototype.commit = function (value, row, params) { var comp = row.getSubject(), batch = params &amp;&amp; params.batch, progressCallback = params &amp;&amp; params.progressCallback, promise = setValueOnComponent(comp, value, batch); if (progressCallback) { progressCallback(MgrColumn.COMMIT_READY); } return promise; }; The webEditors module provides several pre-defined columns that may be useful for managers: NameMgrColumn: Used to display the name of a row's subject component. IconMgrColumn: Used to display the icon of a row's subject component. PathMgrColumn: Used to display the slot path of a row's subject component. PropertyMgrColumn: Used to create a cell's content from a direct property of a row's subject component. PropertyPathMgrColumn: Used to create a cell's content from a descendant property of a row's subject component, for example a property on a proxy extension for a control point subject. See the API documentation for those types for further details on their implementation and usage. Rows Rows in the database table are represented by a Row type. A Row has a subject, which can be a JavaScript object of arbitrary type (it will normally be a reference to the Component represented by the row), an optional icon, and optional metadata. The Row is passed as a parameter to many of the Column's methods, such as when building the DOM content for a cell in the table. In such a case, the column will call the row's getSubject method to access the component, whereupon it will use the subject's properties to generate the table cell content. New instances of a Row are created by the model's makeRow function. Unlike columns, it will not normally be necessary to subclass the Row type. Rows allow keyed data to be temporarily stored against an instance. This could allow a manager to store a value it may want use later against a row, without having to subclass the Row type or add direct properties to the row object. /** * Create a new row for the model. */ MyMgrModel.prototype.makeRow = function (subject) { var row = new Row(subject, subject.getNavIcon()); row.data('my-meta-data', 'foo'); // Set some data to be used later return row; }; Commands Manager views use the bajaux Command and CommandGroup types to provide the commands for the buttons at the bottom of the view and on the toolbar. These are accessible via the command API provided the base Widget type. On top of base command functionality, the manager framework provides an optional mixin called MgrCommand, which can be used to extend the base Command type with extra features. The MgrCommand mixin provides a function named setShowInActionBar, which can be used to indicate a command should be available in the toolbar, but not in the action bar at the bottom of the view. This would normally be used for commands that are not frequently used. The 'discovery mode' command, which is used to toggle the visibility of the discovery table, is an example of the use of this mixin. // Just show the command on the toolbar, not in the action bar at the bottom of the view. myCommand.setShowInActionBar(false); Manager State The Manager type provides the ability to save state data temporarily, so that certain aspects of the manager's state can be restored when hyperlinking back to a previously visited Manager view. The user's web browser will store the state in session storage, which will preserve the state for the duration of the session; after a browser or Workbench restart, the state will have been discarded. By default a small amount of information is saved by the Manager base class. The manager will remember which columns are currently shown or hidden, and will store whether the discovery table is currently visible, if the manager has discovery support mixed in. The Manager class allows for a couple of override points that give a derived class the opportunity to save its own custom state, should it wish to. A typical example might be a driver saving discovery data, meaning that returning to the manager view for a particular network does not require the user to perform a re-discovery (which might perhaps be time consuming, depending on the nature of the system the driver is communicating with). The storage provided by the Manager class is intended for simple, transient state for the user interface. The storage mechanism should not be considered secure and must not be used to store sensitive information such as passwords, private keys or authorization tokens. The first way a manager can add support for saving data is to add a function named saveStateForOrd to the Manager's prototype. This is intended to be used in the situation where the state is only appropriate for a particular Component instance - the Component's ORD will be keyed against the data. This might be used in a case where device specific data is to be cached, for example the discovery data for a device, which has no relevance for other devices of the same type. This function should return an object with properties that the manager wants to be stored: /** * Return the state that should be saved, keyed against the current Manager view's ord base. */ MyManager.prototype.saveStateForOrd = function () { return { discoveryConfig: { discoverInputs: true, discoverOutputs: false } }; }; Another option is to add a function to the prototype called saveStateForKey. This allows data to be cached against a particular type of manager view, and can be restored for any instance of that manager. This uses the moduleName and keyName parameters passed to the constructor. Again, this should return an object containing the properties to be stored: /** * Return that state that should be saved for any instances of this Manager type. */ MyManager.prototype.saveStateForKey = function () { return { discoveryTimeout: 10000 }; } A Manager that implements either of the above functions will also want to provide corresponding functions to restore that state when the view is reloaded. If it provides a saveStateForOrd function, then a Manager should also provide a restoreStateForOrd function, too. This function's argument will be a deserialized object containing the state that had previously been saved. The function may optionally return a Promise if the restoration of the state requires some asynchronous work to be performed. /** * Restore the Manager's state from the deserialized state object. */ MyManager.prototype.restoreStateForOrd = function (state) { var that = this; return that.doSomethingAsynchronous(state) .then(function () { that.restoreMyState(state); }); }; Likewise, a saveStateForKey function should have a corresponding restoreStateForKey function, which again will received a deserialized state object as the argument when it is called. This too may also optionally return a Promise if the restore is asynchronous. /** * Restore the Manager's state from the deserialized state object. */ MyManager.prototype.restoreStateForKey = function (state) { // Restore the state, possibly returning a Promise... }; The manager will call these restore functions during the Widget's load() process. It will be called at a point after the main table has been loaded with the model. As part of the restore process there is an optional function that can be defined, postRestore. This function can be used to do any post restore processing that might be necessary. This will receive the full state object as the argument when it is called, and it may also optionally return a Promise if its process is asynchronous. /** * Does any post processing that might be necessary in a restore */ MyManager.prototype.postRestore = function (state) { // Does any post restore processing that might be needed, possibly // returning a Promise... }; MgrTypeInfo The bajaux manager views make use of a type named MgrTypeInfo for representing the information about new type instances that can be created by the manager. This is used to represent types that can be created by the 'New' command and also types that may be created from a particular discovery item. This is similar to the Java type of the same name used with bajaui manager views. The MgrTypeInfo class provides a static make() method that can be used to create instances in one of several ways: From a type spec string or baja.Type (which can be either a single instance or an array) From a type spec string or baja.Type to be used as a base type, which will return MgrTypeInfo instances for the concrete subclasses of that type. From a Component instance to be used as a 'prototype' for the MgrTypeInfo. Note that this is not a prototype in the JavaScript Object prototype sense, but is used as a way to create a new instance by cloning an existing Component via its newCopy() method. The MgrTypeInfo.make() method returns a Promise that will resolve to a single MgrTypeInfo or array of MgrTypeInfos, depending on the input parameters. The most basic use is to provide a type or array of types in the from parameter: MgrTypeInfo.make({ from: [ 'control:BooleanWritable', 'control:NumericWritable' ] }) .then(function (mgrInfos) { // Do something with the MgrTypeInfos }); To create an array of MgrTypeInfos that represent all the concrete types of a specified base type, pass an additional boolean concreteTypes parameter to the make method: MgrTypeInfo.make({ from: 'driver:Device', concreteTypes: true }) .then(function (mgrInfos) { // Do something with the MgrTypeInfos }); The BajaScript registry can be used in to create an array of MgrTypeInfos for the agents registered on a particular type. The make() method will accept the result returned by the registry's getAgents() function. baja.registry.getAgents(\"type:myModule:MyType\") .then(function (agentInfos) { return MgrTypeInfo.make({ from: agentInfos }); }) .then(function (mgrInfos) { // Do something with the MgrTypeInfos }); When providing an array of Types or type specs to the make() function, the resulting array of MgrTypeInfos will be in the same order as the corresponding types in the 'from' array. A static helper function is provided that can be used to sort an array of MgrTypeInfos alphabetically according to their display names. This function can be passed directly to the sort function of the JavaScript Array type. typeInfos.sort(MgrTypeInfo.BY_DISPLAY_NAME); Discovery A manager that wishes to support dynamic discovery of items can do so by requiring the MgrLearn mixin: define([... 'nmodule/webEditors/rc/wb/mgr/MgrLearn'], function ( ..., addLearnSupport) { and can then apply it to the manager instance in its constructor: addLearnSupport(this); A typical pattern for a Manager's discovery process will be something like this: The user clicks the 'Discover' button, which calls the doDiscover() method on the manager. The doDiscover method invokes an Action on the station, perhaps first displaying a dialog to obtain some configuration parameters, if required. This action will start a discovery job and return its ORD. The ORD of the discovery job is then passed to the setJob() method on the Manager. The Manager will then wait for the event to signal that discovery is complete. Once the job is complete, the Manager will obtain the discovered items (typically by reading dynamic slots from the job) and use those to create TreeNodes for the discovery table. The discovery table is then loaded with the new tree table nodes. When applying this mixin, a number of methods are required to be implemented by the concrete manager. These are: makeLearnModel() doDiscover() getTypesForDiscoverySubject() getProposedValuesFromDiscovery() Each of these will described separately below. makeLearnModel() The makeLearnModel method will be called to create a TreeTableModel for the discovery table. It should return a Promise that will resolve to a TreeTableModel. The use of a tree table allows a multilevel hierarchy to be represented in the discovery table; to show a set of objects at the first level of the tree, and the properties of those objects (name, value, description, etc) at the second level, for instance. As with the main table model, this requires defining a set of Columns. TreeTableModel class defines a static make() method for creating an instance, which is returned via a Promise: /** * Return a Promise that will resolve to the model for the table. */ MyManager.prototype.makeLearnModel = function () { return MyLearnModel.make(); }; /** * A static factory method for the learn model. * @returns {Promise} */ MyLearnModel.make = function () { return TreeTableModel.make({ columns: createColumns() // return an array of Columns }); }; The learn model may use whatever columns are appropriate. The use of PropertyColumns to read values from Components or Structs added to the job as dynamic slots will likely be common pattern. doDiscover() The doDiscover function is called in response to the user clicking the 'Discover' button and its implementation should contain the functionality required to start an asynchronous discovery via some means. As described earlier, the most typical pattern will be for the function to invoke an Action slot on a Component which will submit the appropriate discovery job on the station side, and then return the job's ORD as the return value of the Action. This ORD will then be set on the manager via the setJob() method, which will load the job component into the job bar at the top of the view, thus giving a progress bar indicator for the discovery, and will cause the manager to subscribe to the job, in order to be informed of its progress. /** * Invoke an Action on the station that will submit a discovery job, then * set the returned ORD on the manager */ MyManager.prototype.doDiscover = function () { var that = this, model = that.value(), pointExt = model.getComponentSource().getContainer(); return that.showDiscoveryConfigurationDialog() .then(function (config) { // invoke an action that will submit a job and return the ORD return pointExt.discoverPoints(config); }) .then(function (ord) { ord = baja.Ord.make({ base: baja.Ord.make('station:'), child: ord.relativizeToSession() }); return that.setJob(ord); }); }; Once the job has completed, in either success, cancellation or failure, the code added by the mixin will emit a jobcomplete event, which the concrete manager can attach a handler function for: var MyManager = function MyManager (params) { var that = this; Manager.call(that, { moduleName: 'myModule', keyName: 'MyManager' }); // Add an event handler for the 'jobcomplete' event to know when discovery has // finished. that.on('jobcomplete', function (job) { that.updateLearnTableModelFromJob(job).catch(baja.error); }); }; /** * Called asynchronously after the job submitted by doDiscover() has * finished. This should get the items found in the discovery and * update the TreeNodes in the learn table. * @returns {Promise} */ MyManager.prototype.updateLearnTableModelFromJob = function (job) { var that = this; return job.loadSlots() .then(function () { var discoveries = job.getSlots() .is('myModule:MyDiscoveryPoint') .toValueArray(); that.updateLearnTableModel(discoveries); }); }; /** * Function to update the model for the learn table with the discovered * items obtained from the job. */ MyManager.prototype.updateLearnTableModel = function (discoveries) { var model = this.getLearnModel(), root = model.getRootNode(); // Update the model with the discoveries. Create TreeNodes with a value // returning the discovered item. }; getTypesForDiscoverySubject() In short: what new things can I create from this discovered object? getTypesForDiscoverySubject is used when the user is creating a new component in the station from something that has been discovered and displayed in the discovery table. The function will take the value of the discovered object that the user wishes to add, and should return a single MgrTypeInfo or an array of MgrTypeInfos, if the discovery item may have several possible types in the station. A typical example of multiple types would be the discovery of control point items. When a user drags a point with a boolean output value, the manager might return BooleanWritable, BooleanPoint, StringWritable and StringPoint as potential types. If returning more than one type, the most appropriate type should be the first item in the array. /** * Return the type(s) suitable for the given discovery item. Some managers may * need to inspect the discovery value to return a suitable type or several * types. * @param {*} discoveredObject * @returns {Promise.&lt;MgrTypeInfo[]&gt;} */ MyManager.prototype.getTypesForDiscoverySubject = function (discoveredObject) { if (discoveredObject.isBoolean()) { return MgrTypeInfo.make({ from: [ 'control:BooleanWritable', 'control:BooleanPoint' ] }); } else { // handle other data types.... } }; getProposedValuesFromDiscovery() In short: when adding a new point from a discovered object, how should that point be initially configured? getProposedValuesFromDiscovery is used to take values found during the discovery process (point labels or engineering units, for example) and use them to set the initial values for a new component. These values will be displayed in the batch editor dialog, allowing the user to further adjust them before the component is actually added to the station. The method implementation should return an object with a string property containing the proposed name (the name property) and an object property containing any proposed values (the values property). Each property of the values object should have a name that matches the name of a column in the main table model and its value should be the proposed value for that column. It is not necessary to propose a value for every editable column, as any properties on the created component that do not have proposals will simply use the default slot value. /** * Return a proposed name for the new Component, and proposed initial values * for the 'id', 'enabled' and 'facets' columns. * @param {baja.Value} discoveredObject */ MyManager.prototype.getProposedValuesFromDiscovery = function (discoveredObject) { return { name: discoveredObject.getPointLabel(), values: { id: discoveredObject.getPointId(), enabled: true, facets: makeProposedFacets(discoveredObject.getEngineeringUnits()) } }; }; isExisting() In short: have I discovered this thing already? Implementations of the four methods described above are mandatory for discovery support. The manager may also optionally provide a method on its prototype called isExisting() to check whether a given item found during discovery corresponds to a component already existing within the station. This is used to adjust the row's icon, to give the user a visual indication that the given item is already represented in the station's database. When invoked, the first parameter passed to the function will be the value of a node in the discovery table, the second parameter will be a component in the station. If the component corresponds to the discovery item, the function should return true. /** * A discovered object is considered existing if its ID corresponds to the ID * of a proxy component already in the station. * @param {baja.Value} discoveredObject * @param {baja.Component} component */ MyManager.prototype.isExisting = function (discoveredObject, component) { return discoveredObject.getId() === component.getProxyExt().getPointId(); }; The mixin also adds several methods to the Manager that can be used by a concrete manager class. setJob() getJob() makeDiscoveryCommands() The setJob method is used to set a discovery job against the manager. It can be called with either a job Component or its ORD as the parameter. This will attach the job to the progress bar and cause the manager to listen for events on the job. See the doDiscover code above for an example of using this method. The getJob method will return the job passed to setJob. The makeDiscoveryCommands method is a helper that will create and return five new commands in an array. These commands can then be added to the Manager's command group. The returned array of commands will contain: LearnModeCommand - used to toggle the visibility of the discovery pane. DiscoverCommand - used to start a new discovery. CancelDiscoverCommand - used to cancel a currently running discovery. AddCommand - used to add a new component from a discovered item. MatchCommand - used to update an existing component from a discovered item. Subscribers The bajaux module provides a subscriber mixin that can be used in conjunction with a manager view. It can be required as follows: define(['bajaux/mixin/subscriberMixIn'], function (subscribable) { and applied in the constructor: subscribable(this); This will subscribe to the view's component at the time it is loaded, and will unsubscribe at the time the view is destroyed. This mixin uses a regular BajaScript Subscriber by default. In some cases, subscription to the root container and one or more levels of components under the root may be necessary. The webEditors-ux module provides a depth subscriber that can be used for this purpose. It too needs to be required: define(['bajaux/mixin/subscriberMixIn', 'nmodule/webEditors/rc/fe/baja/util/DepthSubscriber'], function ( subscribable, DepthSubscriber) { Then to use it, create an instance and add it to the manager as a property named $subscriber, before the subscriber mixin is applied: this.$subscriber = new DepthSubscriber(2); subscribable(this); Point Manager The driver-ux module part contains a type named PointMgr that can be used as the base class for point manager views. It also provides a corresponding base class for a point manager model. The model sets up an appropriate default filter that will pick out points and point folders for inclusion. Its default implementation has the ability to create a new ControlPoint type, configured with a proxy extension type specified in the constructor. A new point manager can be created by extending the base class: define(['nmodule/driver/rc/wb/mgr/PointMgr'], function (PointMgr) { /** * Constructor. This specifies a point folder type and a depth to use for a DepthSubscriber */ var MyPointManager = function MyPointManager () { PointMgr.call(this, { moduleName: 'myModule', keyName: 'MyPointManager', folderType: 'myModule:MyPointFolder', subscriptionDepth: 3 }); }; MyPointManager.prototype = Object.create(PointMgr.prototype); MyPointManager.prototype.constructor = MyPointManager; For developer convenience, PointMgr provides folder support and a depth subscriber that can be configured just by passing a the folderType and subscriptionDepth properties in the constructor, as in the example above. The concrete point manager should override the makeModel method to return a PointMgrModel or subclass. The point manager model has a static getDefaultNewTypes method; this can be called to get an array of MgrTypeInfo types for the four control point data types (boolean, numeric, enum, string) in both the 'Point' and 'Writable' versions. This can be used to obtain the new types for the model. require([...'nmodule/driver/rc/wb/mgr/PointMgrModel'], function (...PointMgrModel) { //... MyPointManager.prototype.makeModel = function (component) { return PointMgrModel.getDefaultNewTypes() .then(function (newTypes) { return new PointMgrModel({ columns: makeColumns(), component: component, newTypes: newTypes, folderType: 'myModule:MyPointFolder', proxyExtType: 'myModule:MyProxyExt' }); }); }; }); As with all manager models, a concrete point manager model must provide the columns in the call to the base class constructor. The PointMgrModel constructor takes an additional optional parameter named proxyExtType. If this parameter is specified, then the default implementation of the newInstance method will create an instance of that proxy extension type and set it on any new instances of ControlPoint derived types it creates. If the proxyExtType parameter is not specified in the constructor (perhaps the driver supports several proxy extension types), then the concrete model should probably override the newInstance method, and provide an implementation that will configure the appropriate proxy extension on the new point component. Something that a concrete point manager might wish to override is the makeCommands() method. The default implementation will at minimum return an array containing the 'New' and 'Edit' commands. If a folder type was provided in the call to the base class constructor then a 'New Folder' command will be added too. If the manager supports discovery, then the discovery related commands will be added ('Add', 'Match', 'Toggle Learn Mode', 'Discover', 'Cancel'). This may be sufficient for some managers, but others may wish to override the method to add new commands or remove them. Device Manager The driver module also provides a base class for device managers and their models too. The device manager constructor accepts similar parameters to the point manager: the module and key strings, the subscription depth for the subscriber and the optional folder type: define(['nmodule/driver/rc/wb/mgr/DeviceMgr'], function (DeviceMgr) { /** * Constructor. */ var MyDeviceManager = function MyDeviceManager () { DeviceMgr.call(this, { moduleName: 'myModule', keyName: 'MyDeviceManager', folderType: 'myModule:MyDeviceFolder', subscriptionDepth: 1 }); }; MyDeviceManager.prototype = Object.create(DeviceMgr.prototype); MyDeviceManager.prototype.constructor = MyDeviceManager; As with the point manager, the concrete device manager should provide a makeModel method that returns a subclass of DeviceMgrModel. /** * Return a Promise that will resolve to the device model */ MyDeviceManager.prototype.makeModel = function (component) { return this.getNewTypes() .then(function (newTypes) { return new MyDeviceManagerModel({ component: component, newTypes: newTypes }); }); }; Like PointMgr, the base DeviceMgr class provides a makeCommands method (returning the same default command set as the point manager), which can be overridden to suit the concrete manager's needs. Glossary Term Description Action Bar The set of Command buttons arranged horizontally along the bottom edge of the Manager Depth Subscriber A Subscriber type, used to subscribe and component and its descendants down to a certain tree depth Discovery The act of running an automated process to find potential subjects to be added to the database, for example querying a remote device to find all the data points it contains Job Bar A widget displayed along the top edge of the manager, used to display the progress of the discovery job, and allow the user to cancel it Learn Synonymous with 'Discovery' MgrTypeInfo A class used by manager views to represent a type that the manager is capable of creating × Search results Close "}}
    </script>

    <script type="text/javascript">
        $(document).ready(function() {
            Searcher.init();
        });

        $(window).on("message", function(msg) {
            var msgData = msg.originalEvent.data;

            if (msgData.msgid != "docstrap.quicksearch.start") {
                return;
            }

            var results = Searcher.search(msgData.searchTerms);

            window.parent.postMessage({"results": results, "msgid": "docstrap.quicksearch.done"}, "*");
        });
    </script>
</body>
</html>
