<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="ace" runtimeProfile="rt" name="com.tridium.ace.enums">
<description/>
<class packageName="com.tridium.ace.enums" name="BAceCompQueryTypeEnum"><description>BAceCompQueryTypeEnum</description></class>
<class packageName="com.tridium.ace.enums" name="BAceCovRegisterTypeEnum"><description>BAceMessageType</description></class>
<class packageName="com.tridium.ace.enums" name="BAceFileTransferDirectionEnum"><description>BAceFileTransferDirectionEnum</description></class>
<class packageName="com.tridium.ace.enums" name="BAceLogLevelEnum"><description>BAceLogLevelEnum</description></class>
<class packageName="com.tridium.ace.enums" name="BAceMessageTypeEnum"><description>BAceMessageType</description></class>
<class packageName="com.tridium.ace.enums" name="BAcePrimitiveTypeEnum"><description>AcePrimitiveType</description></class>
<class packageName="com.tridium.ace.enums" name="BAceReqActionTypeEnum"><description>BAceReqActionTypeEnum</description></class>
<class packageName="com.tridium.ace.enums" name="BAceSlotTypeEnum"><description>BAceSlotTypeEnum</description></class>
</package>
</bajadoc>
