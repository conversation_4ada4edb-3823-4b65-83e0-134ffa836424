<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.ui.portal.BAlarmPortalOptions" name="BAlarmPortalOptions" packageName="com.tridium.alarm.ui.portal" public="true">
<description>
BAlarmPortalOptions wraps all configuration&#xa; for an alarm portal.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">05 Feb 02</tag>
<tag name="@version">$Revision: 17$ $Date: 9/15/08 4:29:29 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.ui.options.BUserOptions"/>
</extends>
<property name="consoleList" flags="h">
<type class="com.tridium.alarm.ui.portal.BConsoleRecordList"/>
<description>
Slot for the &lt;code&gt;consoleList&lt;/code&gt; property.
</description>
<tag name="@see">#getConsoleList</tag>
<tag name="@see">#setConsoleList</tag>
</property>

<property name="trayIconEnabled" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;trayIconEnabled&lt;/code&gt; property.
</description>
<tag name="@see">#getTrayIconEnabled</tag>
<tag name="@see">#setTrayIconEnabled</tag>
</property>

<property name="alarmPopupEnabled" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;alarmPopupEnabled&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmPopupEnabled</tag>
<tag name="@see">#setAlarmPopupEnabled</tag>
</property>

<property name="alarmPopupAlwaysOnTop" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;alarmPopupAlwaysOnTop&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmPopupAlwaysOnTop</tag>
<tag name="@see">#setAlarmPopupAlwaysOnTop</tag>
</property>

<property name="alarmPopupUncloseable" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;alarmPopupUncloseable&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmPopupUncloseable</tag>
<tag name="@see">#setAlarmPopupUncloseable</tag>
</property>

<property name="popupPosition" flags="h">
<type class="javax.baja.gx.BPoint"/>
<description>
Slot for the &lt;code&gt;popupPosition&lt;/code&gt; property.
</description>
<tag name="@see">#getPopupPosition</tag>
<tag name="@see">#setPopupPosition</tag>
</property>

<property name="kioskMode" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;kioskMode&lt;/code&gt; property.
</description>
<tag name="@see">#getKioskMode</tag>
<tag name="@see">#setKioskMode</tag>
</property>

<property name="reconnectInterval" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;reconnectInterval&lt;/code&gt; property.&#xa; How often to scan list of Alarm Consoles for ones needing to reconnect
</description>
<tag name="@see">#getReconnectInterval</tag>
<tag name="@see">#setReconnectInterval</tag>
</property>

<property name="defaultTimeRange" flags="">
<type class="com.tridium.bql.util.BDynamicTimeRange"/>
<description>
Slot for the &lt;code&gt;defaultTimeRange&lt;/code&gt; property.
</description>
<tag name="@see">#getDefaultTimeRange</tag>
<tag name="@see">#setDefaultTimeRange</tag>
</property>

</class>
</bajadoc>
