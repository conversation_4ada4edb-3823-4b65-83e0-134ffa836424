<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.selector.BJsonSelector" name="BJsonSelector" packageName="com.tridiumx.jsonToolkit.inbound.selector" public="true" abstract="true">
<description>
Base class for all json inbound classes which take a payload, apply some selection criteria&#xa; and set the result in an out slot.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.inbound.BJsonInbound"/>
</extends>
<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonSelector() -->
<constructor name="BJsonSelector" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonSelector.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonSelector.implOutputsToClear() -->
<method name="implOutputsToClear"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<parameterizedType class="java.util.List">
<args>
<type class="javax.baja.sys.Property"/>
</args>
</parameterizedType>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonSelector.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
