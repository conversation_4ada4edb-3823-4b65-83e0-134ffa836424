<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.ui.portal.BConsoleRecord" name="BConsoleRecord" packageName="com.tridium.alarm.ui.portal" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@creation">07 May 03</tag>
<tag name="@version">$Revision: 10$ $Date: 10/21/10 4:57:24 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="hostOrd" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;hostOrd&lt;/code&gt; property.
</description>
<tag name="@see">#getHostOrd</tag>
<tag name="@see">#setHostOrd</tag>
</property>

<property name="port" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;port&lt;/code&gt; property.
</description>
<tag name="@see">#getPort</tag>
<tag name="@see">#setPort</tag>
</property>

<property name="useFoxs" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;useFoxs&lt;/code&gt; property.
</description>
<tag name="@see">#getUseFoxs</tag>
<tag name="@see">#setUseFoxs</tag>
</property>

<property name="consoleOrd" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;consoleOrd&lt;/code&gt; property.
</description>
<tag name="@see">#getConsoleOrd</tag>
<tag name="@see">#setConsoleOrd</tag>
</property>

</class>
</bajadoc>
