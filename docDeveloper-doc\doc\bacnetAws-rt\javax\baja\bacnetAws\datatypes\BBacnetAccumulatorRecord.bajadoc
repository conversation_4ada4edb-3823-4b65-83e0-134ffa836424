<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord" name="BBacnetAccumulatorRecord" packageName="javax.baja.bacnetAws.datatypes" public="true">
<description>
BBacnetAccumulatorRecord represents the BACnetAccumulatorRecord sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">01 Dec 2008</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.5</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="timestamp" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
<description>
Slot for the &lt;code&gt;timestamp&lt;/code&gt; property.
</description>
<tag name="@see">#getTimestamp</tag>
<tag name="@see">#setTimestamp</tag>
</property>

<property name="presentValue" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</property>

<property name="accumulateValue" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;accumulateValue&lt;/code&gt; property.
</description>
<tag name="@see">#getAccumulateValue</tag>
<tag name="@see">#setAccumulateValue</tag>
</property>

<property name="accumulatorStatus" flags="">
<type class="javax.baja.bacnetAws.enums.BAccumulatorStatus"/>
<description>
Slot for the &lt;code&gt;accumulatorStatus&lt;/code&gt; property.
</description>
<tag name="@see">#getAccumulatorStatus</tag>
<tag name="@see">#setAccumulatorStatus</tag>
</property>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord() -->
<constructor name="BBacnetAccumulatorRecord" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.getTimestamp() -->
<method name="getTimestamp"  public="true">
<description>
Get the &lt;code&gt;timestamp&lt;/code&gt; property.
</description>
<tag name="@see">#timestamp</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.setTimestamp(javax.baja.bacnet.datatypes.BBacnetDateTime) -->
<method name="setTimestamp"  public="true">
<description>
Set the &lt;code&gt;timestamp&lt;/code&gt; property.
</description>
<tag name="@see">#timestamp</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.getPresentValue() -->
<method name="getPresentValue"  public="true">
<description>
Get the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#presentValue</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.setPresentValue(int) -->
<method name="setPresentValue"  public="true">
<description>
Set the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#presentValue</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.getAccumulateValue() -->
<method name="getAccumulateValue"  public="true">
<description>
Get the &lt;code&gt;accumulateValue&lt;/code&gt; property.
</description>
<tag name="@see">#accumulateValue</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.setAccumulateValue(int) -->
<method name="setAccumulateValue"  public="true">
<description>
Set the &lt;code&gt;accumulateValue&lt;/code&gt; property.
</description>
<tag name="@see">#accumulateValue</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.getAccumulatorStatus() -->
<method name="getAccumulatorStatus"  public="true">
<description>
Get the &lt;code&gt;accumulatorStatus&lt;/code&gt; property.
</description>
<tag name="@see">#accumulatorStatus</tag>
<return>
<type class="javax.baja.bacnetAws.enums.BAccumulatorStatus"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.setAccumulatorStatus(javax.baja.bacnetAws.enums.BAccumulatorStatus) -->
<method name="setAccumulatorStatus"  public="true">
<description>
Set the &lt;code&gt;accumulatorStatus&lt;/code&gt; property.
</description>
<tag name="@see">#accumulatorStatus</tag>
<parameter name="v">
<type class="javax.baja.bacnetAws.enums.BAccumulatorStatus"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.timestamp -->
<field name="timestamp"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;timestamp&lt;/code&gt; property.
</description>
<tag name="@see">#getTimestamp</tag>
<tag name="@see">#setTimestamp</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.presentValue -->
<field name="presentValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.accumulateValue -->
<field name="accumulateValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;accumulateValue&lt;/code&gt; property.
</description>
<tag name="@see">#getAccumulateValue</tag>
<tag name="@see">#setAccumulateValue</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.accumulatorStatus -->
<field name="accumulatorStatus"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;accumulatorStatus&lt;/code&gt; property.
</description>
<tag name="@see">#getAccumulatorStatus</tag>
<tag name="@see">#setAccumulatorStatus</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.TIMESTAMP_TAG -->
<field name="TIMESTAMP_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.PRESENT_VALUE_TAG -->
<field name="PRESENT_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.ACCUMULATED_VALUE_TAG -->
<field name="ACCUMULATED_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord.ACCUMULATED_STATUS_TAG -->
<field name="ACCUMULATED_STATUS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
