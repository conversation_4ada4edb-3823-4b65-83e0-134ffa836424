<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBooleanProperty" name="BJsonSchemaBooleanProperty" packageName="com.tridiumx.jsonToolkit.outbound.schema.property" public="true">
<description>
A fixed boolean key / value json pair. The name and value are governed by user input.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<parameterizedType class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaProperty">
<args>
<type class="java.lang.Boolean"/>
</args>
</parameterizedType>
</extends>
<property name="booleanValue" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;booleanValue&lt;/code&gt; property.
</description>
<tag name="@see">#getBooleanValue</tag>
<tag name="@see">#setBooleanValue</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBooleanProperty() -->
<constructor name="BJsonSchemaBooleanProperty" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBooleanProperty.getBooleanValue() -->
<method name="getBooleanValue"  public="true">
<description>
Get the &lt;code&gt;booleanValue&lt;/code&gt; property.
</description>
<tag name="@see">#booleanValue</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBooleanProperty.setBooleanValue(boolean) -->
<method name="setBooleanValue"  public="true">
<description>
Set the &lt;code&gt;booleanValue&lt;/code&gt; property.
</description>
<tag name="@see">#booleanValue</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBooleanProperty.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBooleanProperty.make(boolean) -->
<method name="make"  public="true" static="true">
<description/>
<parameter name="value">
<type class="boolean"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBooleanProperty"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBooleanProperty.getJsonValue() -->
<method name="getJsonValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.Boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBooleanProperty.booleanValue -->
<field name="booleanValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;booleanValue&lt;/code&gt; property.
</description>
<tag name="@see">#getBooleanValue</tag>
<tag name="@see">#setBooleanValue</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBooleanProperty.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
