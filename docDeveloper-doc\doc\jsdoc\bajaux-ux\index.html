<!-- Htmldoc has been run -->
<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>bajaux Index</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

<!-- Auto-generated style sheet link --><link rel='StyleSheet' href='module://bajaui/doc/style.css' type='text/css' />
<!-- Auto-generated js link for Activity Monitoring --><script type='text/javascript' src='module://web/rc/util/activityMonitor.js'></script>
<script type='text/javascript'>window.addEventListener('load', activityMonitor.start);</script>
</head>

<body>
<!-- Auto-generated Header NavBar --><p class="navbar">  <a href="/doc/index.html" class="navbar">Index</a> |  <a href="/doc/jsdoc/bajaScript-ux/index.html" class="navbar">Prev</a> |  <a href="/doc/jsdoc/webEditors-ux/index.html" class="navbar">Next</a></p>


<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">bajaux</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-bajaux_commands_Command.html">bajaux/commands/Command</a></li><li><a href="module-bajaux_commands_CommandGroup.html">bajaux/commands/CommandGroup</a></li><li><a href="module-bajaux_commands_ToggleCommand.html">bajaux/commands/ToggleCommand</a></li><li><a href="module-bajaux_commands_ToggleCommandGroup.html">bajaux/commands/ToggleCommandGroup</a></li><li><a href="module-bajaux_container_wb_Clipboard.html">bajaux/container/wb/Clipboard</a></li><li><a href="module-bajaux_container_wb_StringList.html">bajaux/container/wb/StringList</a></li><li><a href="module-bajaux_dragdrop_dragDropUtils.html">bajaux/dragdrop/dragDropUtils</a></li><li><a href="module-bajaux_dragdrop_Envelope.html">bajaux/dragdrop/Envelope</a></li><li><a href="module-bajaux_dragdrop_NavNodeEnvelope.html">bajaux/dragdrop/NavNodeEnvelope</a></li><li><a href="module-bajaux_dragdrop_StringEnvelope.html">bajaux/dragdrop/StringEnvelope</a></li><li><a href="module-bajaux_events.html">bajaux/events</a></li><li><a href="module-bajaux_icon_iconUtils.html">bajaux/icon/iconUtils</a></li><li><a href="module-bajaux_lifecycle_WidgetManager.html">bajaux/lifecycle/WidgetManager</a></li><li><a href="module-bajaux_mixin_batchLoadMixin.html">bajaux/mixin/batchLoadMixin</a></li><li><a href="module-bajaux_mixin_batchSaveMixin.html">bajaux/mixin/batchSaveMixin</a></li><li><a href="module-bajaux_mixin_responsiveMixIn.html">bajaux/mixin/responsiveMixIn</a></li><li><a href="module-bajaux_mixin_subscriberMixIn.html">bajaux/mixin/subscriberMixIn</a></li><li><a href="module-bajaux_Properties.html">bajaux/Properties</a></li><li><a href="module-bajaux_registry_Registry.html">bajaux/registry/Registry</a></li><li><a href="module-bajaux_registry_RegistryEntry.html">bajaux/registry/RegistryEntry</a></li><li><a href="module-bajaux_spandrel.html">bajaux/spandrel</a></li><li><a href="module-bajaux_spandrel_jsx.html">bajaux/spandrel/jsx</a></li><li><a href="module-bajaux_util_CommandButton.html">bajaux/util/CommandButton</a></li><li><a href="module-bajaux_util_CommandButtonGroup.html">bajaux/util/CommandButtonGroup</a></li><li><a href="module-bajaux_util_SaveCommand.html">bajaux/util/SaveCommand</a></li><li><a href="module-bajaux_Validators.html">bajaux/Validators</a></li><li><a href="module-bajaux_Widget.html">bajaux/Widget</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="interfaces.list.html" class="dropdown-toggle" data-toggle="dropdown">Interfaces<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-bajaux_commands_Command-Undoable.html">bajaux/commands/Command~Undoable</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="tutorials.list.html" class="dropdown-toggle" data-toggle="dropdown">Tutorials<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="tutorial-10-mfw-gettingStarted.html">Getting Started - MyFirstWidget</a></li><li><a href="tutorial-20-mfw-modifying.html">Saving Modifications to Station</a></li><li><a href="tutorial-30-mfw-dashboarding.html">Making your Widget Dashboardable</a></li><li><a href="tutorial-40-tipsAndTricks.html">Tips and Tricks</a></li><li><a href="tutorial-50-spandrel.html">Building Composite Widgets With spandrel</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	
	











	
	





    <section class="readme-section">
        <article><p>The <code>bajaux</code> framework enables developers to create widgets for Niagara using HTML5 and JavaScript.</p>
<h2>Why bajaux?</h2>
<p><code>bajaui</code> is the existing, Java-based, Niagara UI framework for use in Workbench. It has been in use since the very first release of Niagara AX. It is still in widespread use and will be supported for some time. But it has two primary shortcomings that <code>bajaux</code> solves.</p>
<h3>With <code>bajaux</code>, Workbench is no longer a requirement.</h3>
<p>If you write a view using <code>bajaui</code>, then the only way your customer can access it is through Workbench or a standalone Java application. In many cases, we want them to be able to just open their laptop or phone and start using the software right away, in their web browser. Because <code>bajaux</code> is based on HTML5 and JavaScript, it works natively in the web browser - no Java required.</p>
<h3>Write your view once, run it anywhere.</h3>
<p>Previously, if you wanted your UI to work natively in both Workbench and the browser, you had to write it at least twice: once using <code>bajaui</code> for use in Workbench, and once using <code>hx</code> for use in the browser. (If you wanted to target the now-deprecated Niagara Mobile framework as well, make that three times!)</p>
<p>Now, <code>bajaux</code> works natively in the web browser. But it also works natively in Workbench! Workbench supports <code>bajaux</code> views through the use of a web browser embedded directly into Workbench. You can write one <code>bajaux</code> view and have it work in Workbench, Px, and the browser. This reduces development effort and ensures that your users get a consistent user experience.</p>
<p>Not only will your widget display in Workbench, it will also integrate into the given environment. For instance, commands defined for any widgets will render in Workbench's Java toolbar. Also, users can drag and drop from the nav tree onto a <code>bajaux</code> widget, both in Workbench and the browser!</p>
<h2>Widget</h2>
<p><a href="module-bajaux_Widget.html"><code>Widget</code></a> is the core API of <code>bajaux</code>. Adhering to the Widget APIs will ensure your UI works consistently in all supported environments.</p>
<p>A Widget has a well-defined lifecycle. It is created, performs its needed functionality, and then is destroyed. Because <code>bajaux</code> strives to be as unopinionated as possible, you have the freedom to decide exactly what your Widget will do at each point in its lifecycle.</p>
<p>Understanding these points is key to understanding how Widgets work. Let's take a look at the points in a Widget lifecycle in some more detail.</p>
<h3>Construct</h3>
<p>A Widget is a JavaScript object like any other. You create an instance of it using the <code>new</code> keyword, and define your own behaviors by subclassing it. Here's a very simple example.</p>
<pre class="prettyprint source lang-javascript"><code>class MyWidget extends Widget {
  constructor(params) {
    super({ params, defaults: { properties: { foo: 'bar' } } });
  }
}

const myWidget = new MyWidget({ properties: { baz: 'buzz' } });
</code></pre>
<p>Don't worry, we'll examine the actual API in more detail a bit later. The key takeaways from this code snippet are:</p>
<ul>
<li>A Widget can have default values for its attributes when it is constructed. (Consider how Niagara Components can have default values for their frozen Slots.)</li>
<li>You can set actual values for the Widget's attributes by passing them to the constructor function.</li>
</ul>
<h3>Initialize</h3>
<p>Each Widget instance is bound to a single DOM element and remains bound to that DOM element for its entire lifecycle. The moment at which a Widget is bound to a DOM element is called <em>initialization</em>.</p>
<pre class="prettyprint source lang-javascript"><code>class MyWidget extends Widget {
  doInitialize(dom) {
    dom.text('Hello world!');
  }
}

const myWidget = new MyWidget();
const dom = $('#myWidgetGoesHere');

myWidget.initialize(dom)
  .then(() => {
    console.log('initialized!');
    console.log(myWidget.jq().text()); // Hello world!
    console.log(Widget.in(dom)); // MyWidget
  });
</code></pre>
<p>Let's see what we can learn from this code snippet.</p>
<p>First, see how the <code>dom</code> parameter is a jQuery object. <code>bajaux</code> uses jQuery for all its DOM interactions. (However, there is still a live DOM HTMLElement within that jQuery object, so you are free to use any method of interacting with an HTMLElement you choose.)</p>
<p>Next, note how my Widget subclass overrode the <code>doInitialize()</code> method, and then to bind my Widget instance to a DOM element, I called the <code>initialize()</code> method. This is a <strong>key pattern</strong> that you will see in every Widget. If the method starts with <code>do</code>, you <em>override</em> that method to implement your own behavior, but you do not call it! If the method does not start with <code>do</code>, then that is a method you <em>call</em>.</p>
<p>Implement <code>doInitialize()</code>, call <code>initialize()</code>. You'll see this pattern repeated throughout the framework.</p>
<p>Initialization is a one-time operation. Once you've initialized a Widget into a DOM element, that Widget will remain bound to that DOM element until it is destroyed. A DOM element can only have one Widget initialized into it at a time, and a Widget can only be initialized into one DOM element at a time. It's a one-to-one relationship.</p>
<p>(If you are familiar with other UI frameworks such as React, you may have seen other UI components create their own DOM element in which to live. <code>bajaux</code> Widgets do not create their own DOM element. They are given an existing DOM element, and then they can build out the contents (child nodes) of that DOM element. This is a <strong>key difference</strong> that is important for understanding <code>bajaux</code>.)</p>
<p>Because it's a one-time operation, you can implement <code>doInitialize</code> to set up any invariant (unchanging) conditions for your Widget. A couple of things you might do:</p>
<ul>
<li>If your Widget is to edit a string, you could create an <code>&lt;input type=&quot;text&quot;&gt;</code> to hold that String.</li>
<li>If your Widget needs to respond to user events, you could arm event handlers such as an <code>onClick</code> on your DOM element.</li>
</ul>
<p>Once initialization is complete, you can get the Widget that is bound to any particular DOM element using the <code>Widget.in()</code> function. The widget's <code>.jq()</code> method is its inverse: it returns the DOM element (as jQuery) that the Widget is bound to.</p>
<p>Finally, note that <code>initialize()</code> returns a Promise. Most <code>bajaux</code> operations are asynchronous, since they may need to make network calls to retrieve information, such as Lexicon text, from the station. <code>doInitialize()</code>, like all methods that start with <code>do</code>, can optionally return a Promise to indicate when its work is complete, or (like our example) it may be implemented asynchronously.</p>
<h3>Load</h3>
<p>Most UI components are implemented to allow the user to view or interact with a piece of data. A few examples are:</p>
<ul>
<li>Display a string for the user to read.</li>
<li>Provide a text editor for the user to edit a string.</li>
<li>Provide child editors for the user to edit different properties of an object.</li>
</ul>
<p>In all cases, <code>doLoad()</code> is the override point to allow a piece of data to be loaded into a Widget. Let's look at an example in which we load in a string and simply display it to the user.</p>
<pre class="prettyprint source lang-javascript"><code>class MyWidget extends Widget {
  doInitialize(dom) {
    dom.html('&lt;span>Your string is: &lt;/span>&lt;span class=&quot;myString&quot;/>');
  }

  doLoad(string) {
    this.jq().children('.myString').text(string);
  }
}

const myWidget = new MyWidget();
const dom = $('#myWidgetGoesHere');
myWidget.initialize(dom)
  .then(() => myWidget.load('hello world!'))
  .then(() => {
    console.log(dom.text()); // Your string is: hello world!
    return myWidget.load('hello again!');
  })
  .then(() => {
    console.log(dom.text()); // Your string is: hello again!
    console.log(myWidget.value());  // hello again!
  });
</code></pre>
<p>We use the one-time operation, <code>doInitialize()</code>, to set up the widget to receive a value to be loaded later.</p>
<p>Again, <code>load()</code> is called, not implemented; <code>doLoad()</code> is implemented, not called. <code>doLoad()</code> receives the value being loaded in and updates the DOM to reflect that loaded value. <code>load()</code> can be called as many times as needed, over the lifecycle of the Widget, to reflect different values.</p>
<p>Take a look at the <code>.value()</code> method. This returns the Widget's currently loaded value; that is, the last value passed to the Widget's <code>load()</code> method.</p>
<p>Also, note the use of <code>.text()</code> instead of <code>.html()</code>. <code>bajaux</code> itself does not perform any XSS sanitization. It is up to you to ensure that any user-provided values are not placed directly into the DOM without being sanitized, as this can open up a <a href="https://owasp.org/www-community/attacks/xss/">reflected XSS attack</a>. Using <code>.text()</code> is one good way to do this; there are many others.</p>
<p>Although this widget example is display-only, many widgets that load a value are implemented to allow the user to change that value, and read out a new one.</p>
<h3>Read</h3>
<p><code>doRead()</code> answers the question: &quot;what value has the user entered?&quot;</p>
<p>As the user interacts with your widget, they will change its current value. They may type in new strings, check or uncheck checkboxes, move sliders, and so on. It's your Widget's responsibility to look at the current state of the DOM, and implement <code>doRead()</code> to resolve an actual value that represents <em>what the user has entered.</em> This is different from <code>.value()</code>, which returns the last value loaded in.</p>
<p>If you are creating a field editor, then <code>doRead()</code> should typically resolve a value of a type that is compatible with what was loaded in. If your widget accepts a <code>baja:String</code> to its <code>load()</code> method, it would typically resolve a <code>baja:String</code> from its <code>read()</code> method. This is because field editors for Simples often pass the result of <code>read()</code> right back to <code>load()</code>, so reading out a value of a different type from what was loaded can result in some unexpected behavior. Fullscreen views have more latitude.</p>
<pre class="prettyprint source lang-javascript"><code>class MyWidget extends Widget {
  doInitialize(dom) {
    dom.html('&lt;input type=&quot;text&quot;>');
    dom.on('change', 'input', () => this.setModified(true));
  }

  doLoad(string) {
    this.$getTextInput().val(string);
  }

  doRead() {
    return this.$getTextInput().val();
  }

  $getTextInput() { // see note about the $ prefix below
    return this.jq().children('input');
  }
}

const myWidget = new MyWidget();
const dom = $('#myWidgetGoesHere');

dom.on(events.MODIFY_EVENT, (e, ed) => {
  ed.read()
    .then((userTypedString) => {
      let msg = 'User typed: ' + userTypedString;
      if (userTypedString !== ed.value()) {
        msg += ' (different)';
      }
      console.log(msg);
    })
    .catch((err) => console.error(err));
});

myWidget.initialize(dom)
  .then(() => myWidget.load('initial value'));
</code></pre>
<p>Here is a straightforward example: this widget creates a text input for the user to type in, and <code>doRead()</code> reads out the value the user has typed.</p>
<p>(Take a quick look at the method <code>$getTextInput()</code>. The <code>$</code> prefix, by convention, indicates a private method. If you are inspecting an object in the console, and see a method prefixed with <code>$</code>, consider that a private method and don't call it externally. You can mark your own private methods with the <code>@private</code> JSDoc tag.)</p>
<p>This example also introduces a couple new Widget behaviors: <em>modification</em> and <em>events</em>.</p>
<h3>Modification</h3>
<p>Take a look at <code>doInitialize()</code>. <code>doInitialize()</code> is a good place to arm DOM event handlers because it only runs once. Here, we arm a handler to listen for a <code>change</code> event from the <code>input</code> tag, which will be fired every time the user types a character. When we get a <code>change</code> event, we mark the widget as modified by calling <code>setModified(true)</code>.</p>
<p>Marking a widget as modified is very important. One reason why is that when a widget is marked modified, then if you try to navigate away without saving your changes, the profile can show the &quot;do you want to save changes?&quot; dialog to ensure that the user changes are not lost. This functionality only works if the widget correctly marks itself as modified, so <code>isModified()</code> returns true, when user changes are made.</p>
<p>Another reason is that calling <code>setModified(true)</code> will trigger a corresponding <em>event</em>.</p>
<h3>Events</h3>
<p>At different moments in a widget's lifecycle, it will emit different <em>events</em>. These events are enumerated in the <a href="module-bajaux_events.html">bajaux/events module</a>. These are triggered like any other jQuery event, and you can arm event handlers to respond when a widget further down in the DOM triggers one of these events.</p>
<p>In the preceding example, we listen for <code>MODIFY_EVENT</code> so we know when the widget is modified. The event handler for a <code>bajaux</code> event always receives the Event object itself, followed by the Widget that triggered it. Our event handler then calls <code>.read()</code> to read out the currently entered value and log it to the console. Also note how it can be compared against the result of <code>.value()</code> to see if the user has actually entered a different string.</p>
<p>Also in this example, note how we do not return the Promise created by the call to <code>.read()</code>. 99% of the time, when performing an asynchronous operation, you should <em>return</em> the Promise so that the caller of the function can know when the work is completed and handle any errors. This is one notable exception to that rule: with jQuery event handlers, the caller of the function is jQuery itself, and jQuery <em>does not know how to handle a returned Promise</em>. Specifically for jQuery event handlers, we do not return the Promise, and we catch and log the error ourselves.</p>
<p>There will be a bit more detail on this in the upcoming section on asynchronous programming.</p>
<h3>Validate</h3>
<p>Your widget may need constraints on what values are acceptable for the user to enter. For instance, say we want the user to enter a percentage between 0% and 100%. For this, we can use a validator function. To add validators, simply call <code>validators().add(validatorFunction)</code>.</p>
<pre class="prettyprint source lang-javascript"><code>class PercentInput extends Widget {
  constructor() {
    super(...arguments);
    this.validators().add((percent) => {
      if (percent &lt; 0 || percent > 100) {
        throw new Error('Must be valid percentage [0-100]');
      }
    });
  }

  doInitialize(dom) {
    dom.html('&lt;input type=&quot;text&quot;>&lt;span>%&lt;/span>');
    dom.on('change', 'input', () => this.setModified(true));
  }

  doLoad(number) {
    this.jq().children('input').val(String(number));
  }

  doRead() {
    const text = this.jq().children('input').val();
    const number = parseFloat(text);
    if (isNaN(num)) {
      throw new Error('Not a valid number: ' + text);
    }
    return number;
  }
}

const myWidget = new PercentInput();
const dom = $('#myWidgetGoesHere');

dom.on(events.MODIFY_EVENT, (e, ed) => {
  ed.validate()
    .then((percent) => console.log('valid percent: ' + percent))
    .catch((err) => console.error(err));
});

myWidget.initialize(dom)
  .then(() => myWidget.load(0));
</code></pre>
<p>In this example, we create an input for the user to type a number into, with the constraint that it be a number between 0 and 100. If the value is not actually a number, then we throw an error from <code>doRead()</code> indicating that we couldn't read a number at all (note we could also return a rejected Promise).</p>
<p>For validation, the validator function we add in the constructor will receive the value resolved from <code>doRead()</code> (a number) and check that it is between 0 and 100. If not, it throws an error (again, could also be a rejected Promise). We trigger the validation by calling <code>validate()</code>: this will <code>read()</code> the value out first, and then pass it to the validator function. This answers the question: does the user have a valid value entered?</p>
<p>You may be wondering: why are we checking the value in <em>both</em> <code>doRead()</code> and the validator? Couldn't we do both checks in <code>doRead()</code>? The answer is yes, we could, but it depends on your own use case. If <code>doRead()</code> rejects, it's like the widget is saying, &quot;my current state is so messed up, I have no idea what the user was even trying to enter.&quot; If <code>doRead()</code> rejects, so will <code>read()</code>. But if <code>doRead()</code> resolves but <code>validate()</code> fails, it's like saying, &quot;I know what the user has entered, but it's not a correct value for my use case.&quot; In our example, this makes sense: if the user has an actual number entered, we can know that, and subsequently validate its numeric value. If we wanted to, we could do this:</p>
<pre class="prettyprint source lang-javascript"><code>ed.validate()
  .catch(() => {
    return ed.read()
      .then((invalidValue) => {
        console.log('user entered ' + invalidValue + ' but it failed validation.');
      });
  });
</code></pre>
<p>This would not be possible if <code>read()</code> itself rejected.</p>
<h3>Save</h3>
<p>When the user-entered changes are satisfactory, call <code>.save()</code> to save those changes. The behavior will be defined, again, by implementing <code>doSave()</code>.</p>
<p>You can typically think of this as <em>mutating the current <code>.value()</code></em>. Whatever value I last passed to <code>.load()</code> will be <em>mutated</em> when I call <code>.save()</code>. Your implementation may vary, but this is the most common pattern to follow.</p>
<p>It follows that if you just load in an immutable simple value like a String, calling <code>.save()</code> does not make sense because there is no way to mutate that String. But if your widget loads a mutable value like an object or a Component, <code>.save()</code> can commit changes to that value.</p>
<pre class="prettyprint source lang-javascript"><code>class UserNamer extends Widget {
  doInitialize(dom) {
    dom.html('&lt;input type=&quot;text&quot;>');
    dom.on('change', 'input', () => this.setModified(true));
  }

  doLoad(user) {
    this.getTextInput().val(user.name);
  }

  doRead() {
    return this.getTextInput().val();
  }

  doSave(name) {
    this.value().name = name;
  }

  getTextInput() {
    return this.jq().find('input');
  }
}

const myWidget = new UserNamer();
const dom = $('#myWidgetGoesHere');
const user = { name: 'Alice' };

myWidget.initialize(dom)
  .then(() => myWidget.load(user))
  .then(() => {
    myWidget.getTextInput().val('Bob');
    return myWidget.save();
  })
  .then(() => console.log(user.name)); // Bob
</code></pre>
<p>Important things to note about the <code>.save()</code> process:</p>
<ul>
<li><code>.validate()</code> will be called first, so if the currently entered value is not valid, it cannot be saved.</li>
<li>The validated value is the first argument to <code>.doSave()</code>, so you do not need to call <code>.read()</code> again.</li>
<li>Once <code>.save()</code> completes, <code>.isModified()</code> will revert to <code>false</code>.</li>
</ul>
<h3>Destroy</h3>
<p>When you are done with a widget, it's best to <code>.destroy()</code> it before simply removing its DOM element from the document. Widgets can acquire and hold resources such as:</p>
<ul>
<li>Component subscriptions</li>
<li>Global event handlers</li>
<li>Database connections</li>
</ul>
<p><code>doDestroy()</code> is a place for the widget to relinquish any resources it is currently holding so that they can be freed. If your widget does not release these resources in <code>doDestroy()</code>, they can stick around after the widget is gone, consuming memory, bandwidth, and CPU cycles unnecessarily.</p>
<h3>Attributes</h3>
<p>Widgets also have the following attributes.</p>
<h4><code>.isModified()</code></h4>
<p>As described above, reflects whether the widget has any user-entered changes. In most cases, it is the responsibility of your widget to listen for events that indicate user modification, and call <code>this.setModified(true)</code>.</p>
<h4><code>.isEnabled()</code></h4>
<p>A widget can be enabled or disabled. The implementation of how it responds to changes in its enabled state is <code>.doEnabled()</code>.</p>
<p>You may call <code>.setEnabled()</code> on your own widgets to enable or disable them. The framework itself may also call <code>.setEnabled()</code> on your widget in response to various conditions, such as the <code>enabled</code> property if your widget is embedded on a Px page.</p>
<h4><code>.isReadonly()</code></h4>
<p>A widget can be readonly or writable. The implementation on how it responds to changes in its readonly state is <code>.doReadonly()</code>.</p>
<p>You may call <code>.setReadonly()</code> on your own widgets to make them readonly or writable. The framework itself may also call <code>.setReadonly()</code> on your widget in response to various conditions, such as if it is a field editor on a Component slot and that slot has the <code>READONLY</code> slot flag.</p>
<p>Note that there can be significant overlap between readonly and enabled, so you may want to consider the state of both attributes when implementing <code>doEnabled()</code> or <code>doReadonly()</code>. In fact, many widgets respond exactly the same whether being set to disabled or readonly. The <a href="tutorial-50-spandrel.html">Building Composite Widgets With spandrel</a> API provides the <code>writable</code> flag to ease this confusion.</p>
<h4><code>.properties()</code></h4>
<p>A Widget has a <a href="module-bajaux_Properties.html">Properties</a> API which allows it to declare a public set of properties and their values. These Properties will typically be used internally by the Widget to configure its behavior. For instance, when a Widget creates a DOM element of <code>&lt;input type=&quot;range&quot;&gt;</code>, it may declare properties of <code>min</code> and <code>max</code> to define the range of the input. But the Properties declared by a Widget also form a type of public API for that Widget, one that is used in many ways:</p>
<ul>
<li>When your Widget is embedded in a Px page, its Properties can be edited by the page designer.</li>
<li>When your Widget is used to edit a Slot value, it will receive the Slot Facets as Properties.</li>
<li>When a parent Widget creates child Widgets, it can configure the Properties of the children for customized behavior.</li>
<li>When a Widget is placed on a Dashboard, it can declare Properties as dashboardable, so they can be saved per-user.</li>
</ul>
<p>There is one special Property, <code>rootCssClass</code>, that can be specified on a Widget. It will be used in <code>initialize()</code> to add that CSS class to its DOM element when it is initialized. This is typically used to easily identify instances of that Widget, either by selecting them out of the DOM, or styling them in CSS. A common pattern is shown below:</p>
<pre class="prettyprint source lang-javascript"><code>class MyWidget extends Widget {
  constructor(params) {
    super({
      params,
      defaults: { properties: { rootCssClass: '-myCompany-MyWidget' } }
    });
  }
}
//...
const allMyWidgets = [ ...dom.find('.-myCompany-MyWidget') ].map(Widget.in);
</code></pre>
<h4><code>.getFormFactor()</code></h4>
<p>In Niagara AX...</p>
<ul>
<li>A View is something that occupies most of the UI area in Workbench.</li>
<li>A Field Editor is quite small and appears alongside other Field Editors on a Property Sheet.</li>
</ul>
<p>However, these extend from different classes and inherit different sets of functionality. In bajaux, we've separated out the form factor from the capabilities of the Widget. The form factor describes the kind of element in which a Widget may be initialized, not the behavior of that Widget. This allows Widgets to alter their behavior and layout to function appropriately, whether taking up the full browser window or just one row in a property sheet.</p>
<p>For instance, in bajaux...</p>
<ul>
<li>The form factor <code>max</code> is for widgets that function as fullscreen views, like Property Sheet or User Manager.</li>
<li>The form factor <code>mini</code> is for widgets that function as field editors, such as in one individual row in a Property Sheet or embedded as a Property on a Px Page.</li>
<li>The form factor <code>compact</code> is for widgets that function in between - larger than a field editor but smaller than a fullscreen view. Most commonly this is a modal dialog, such as when you invoke an Action that takes a parameter.</li>
</ul>
<p>You can implement a widget that supports all of these form factors, or just one.</p>
<h4><code>.toDisplayName()</code>, <code>.toDescription()</code>, <code>.toIcon()</code></h4>
<p>Widgets have display name, description, and icon attributes that come into play with Workbench interop, described below.</p>
<h3>Subscription</h3>
<p>One of the nice features of Niagara AX is <code>BWbComponentView</code>. When extending this Java class, any subclasses automatically get Component subscription/unsubscription handled for them.</p>
<p>The bajaux framework has the same concept. A <code>Widget</code> can be made into a Subscriber <code>Widget</code> by a special <a href="module-bajaux_mixin_subscriberMixIn.html">Subscriber MixIn</a>. A Subscriber MixIn will use BajaScript to automatically ensure a Component is subscribed and ready before it's loaded. It will also ensure any Components are unsubscribed when the UI is destroyed.</p>
<h3>Commands</h3>
<p>A <a href="module-bajaux_commands_Command.html">Command</a> is a function that belongs to a Widget, but can be seen and invoked by the user. It is very similar to the existing Command API in <code>bajaui</code>. Some examples are a Save Command, which allows the user to save changes to the Widget, or the Property Sheet's Add Slot Command.</p>
<p>Each Command has metadata to define how it is displayed to the user:</p>
<ul>
<li>Display name: formatted in the user's locale.</li>
<li>Enabled/Disabled: commands that cannot be currently invoked are disabled.</li>
<li>Flags: a <code>Command</code> can have some flags to indicate whether it should appear in a menu bar, toolbar or both.</li>
<li>Icon: gives the <code>Command</code> an icon to make it easily identifiable by the user.</li>
</ul>
<p>As well as a standard <code>Command</code>, there's also <code>ToggleCommand</code>. A <code>ToggleCommand</code> can have its state toggled. Think of it like a tick box that can be turned on or off.</p>
<p>A number of <code>Command</code> and <code>ToggleCommand</code> objects can be arranged into a tree-like structure by use of a <code>CommandGroup</code>.</p>
<h3>Interop /  Integration</h3>
<p><code>bajaux</code> Widgets work in many environments.</p>
<ul>
<li>The HTML5 Profile, used in the web browser, will instantiate the appropriate Widget for the component you are viewing.</li>
<li>Workbench itself can load your <code>bajaux</code> Widgets using Java/JavaScript interop.</li>
<li>Widgets can be embedded in Px pages and viewed either in Workbench or the browser.</li>
</ul>
<p>The environment in which the Widget is instantiated can access certain attributes of that Widget to more tightly integrate that Widget into the user interface. When developing a Widget, it can pay to correctly define these attributes to make its functionality more accessible to the user.</p>
<ul>
<li>Any <code>Command</code>s in the Widget's <code>CommandGroup</code> (e.g. a Property Sheet's &quot;Add Slot&quot; command), with the appropriate flags, will be shown as buttons in the toolbar in both Workbench and browser. Workbench can also show them in the menu bar.</li>
<li>The Widget's display name and icon will be used in the view selector dropdown, and in Workbench's menu bar.</li>
</ul>
<h3>Asynchronous Programming</h3>
<p>Any modern JavaScript framework has to deal with asynchronous behavior. The bajaux framework has been designed with this in mind from the ground up. Therefore, most of the callbacks that can be overridden can optionally return a <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise">promise</a> that can perform an asynchronous operation before resolving. For instance, let's say some extra network calls have to be made during a widget's loading process. The widget's <a href="module-bajaux_Widget.html#doLoad">doLoad</a> method can return a promise. The promise can then be resolved when the network calls have completed.</p>
<p>The Promise API also defines how any asynchronous errors, like a failed network call, are handled. As a general rule (not just for <code>bajaux</code>), if you write a function that creates a Promise to perform some asynchronous work, you should <em>return that Promise</em> and document it accordingly, so that the caller can know to either return that Promise again, or handle any errors.</p>
<pre class="prettyprint source lang-javascript"><code>/**
 * @returns {Promise} this function returns a Promise, so be sure to handle it correctly!
 */
function retrieveNetworkData() {
  return makeNetworkCallTo('/myRestEndpoint');
}

// now, as the caller of this function, I can be sure to return that Promise...
/** @returns {Promise} */
function processNetworkData() {
  return retrieveNetworkData()
    .then((networkData) => {
      return doSomethingWith(networkData);
    });
}

// ...or I will at least know I need to handle any rejections.
retrieveNetworkData()
  .then((networkData) => {
    return doSomethingWith(networkData);
  })
  .catch((err) => logSevere(err));
</code></pre>
<p>If a Promise is not returned, and no one handles a rejection via a <code>.catch()</code> block, then any errors will &quot;disappear&quot; - no one will know anything went wrong. Keep an eye on how your Promises are created and returned, and you can be sure that all your errors are handled!</p>
<h3>Responsive Layout</h3>
<p>Many Widgets need some sort of responsive behavior: they'll need to lay themselves out differently depending on the width and height allotted to them. A Widget in a desktop browser at fullscreen will likely look very different from when it is displayed on a mobile phone.</p>
<p>Most responsive behavior can be handled through pure CSS, which every Widget supports. But one shortcoming of the CSS approach is that one common technique - Media Queries - won't always work. This is because Media Queries target the screen size of the device, while the dimensions of a Widget vary independent of the screen size. For instance, your Widget might be embedded on a Px page, or within the resizable main view pane of Workbench.</p>
<p>Where Media Queries fall short, or just where more complex layout calculations are needed, <a href="module-bajaux_mixin_responsiveMixIn.html">responsiveMixIn</a> makes it easy to implement responsive behavior in any scenario.</p>
<h3>Composition</h3>
<p>Many Widgets will want to add additional child Widgets for richer functionality. Although the core Widget API has the built-in capability to initialize child Widgets, the <a href="module-bajaux_spandrel.html">spandrel</a> API makes it even easier to assemble child Widgets and HTML. This increases code reuse and improves maintainability.</p>
<h2>Getting Started</h2>
<p>Click <a href="tutorial-10-mfw-gettingStarted.html">here</a> to get started!</p>
<p>Also try opening the docDeveloper palette in Niagara to start working with our sample bajaux playground components!</p></article>
    </section>







		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	bajaux Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:53+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>



<!-- Auto-generated Footer NavBar --><p class="navbar">  <a href="/doc/index.html" class="navbar">Index</a> |  <a href="/doc/jsdoc/bajaScript-ux/index.html" class="navbar">Prev</a> |  <a href="/doc/jsdoc/webEditors-ux/index.html" class="navbar">Next</a></p>
<!-- Auto-generated copyright note --><p class='copyright'></p>
</body>
</html>
