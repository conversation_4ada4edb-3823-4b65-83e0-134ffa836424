<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.agent.BPxView" name="BPxView" packageName="javax.baja.agent" public="true">
<description>
BPxView is a BAbstractPxView which stores &#xa; the view contents in an XML file with a px extension.  The &#xa; view itself is defined as a tree of bajaui:Widgets.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">8 May 04</tag>
<tag name="@version">$Revision: 9$ $Date: 6/11/07 12:41:23 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.agent.BAbstractPxView"/>
</extends>
<property name="pxFile" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;pxFile&lt;/code&gt; property.&#xa; Defaults the px file which contains the view
</description>
<tag name="@see">#getPxFile</tag>
<tag name="@see">#setPxFile</tag>
</property>

<!-- javax.baja.agent.BPxView(javax.baja.sys.BIcon, javax.baja.naming.BOrd, javax.baja.security.BPermissions, javax.baja.util.BTypeSpec) -->
<constructor name="BPxView" public="true">
<parameter name="icon">
<type class="javax.baja.sys.BIcon"/>
</parameter>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<parameter name="permissions">
<type class="javax.baja.security.BPermissions"/>
</parameter>
<parameter name="media">
<type class="javax.baja.util.BTypeSpec"/>
</parameter>
<description>
Constructor with all fields.
</description>
</constructor>

<!-- javax.baja.agent.BPxView(javax.baja.naming.BOrd) -->
<constructor name="BPxView" public="true">
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<description>
Constructor with PxFile ord.
</description>
</constructor>

<!-- javax.baja.agent.BPxView() -->
<constructor name="BPxView" public="true">
<description>
No argument constructor.
</description>
</constructor>

<!-- javax.baja.agent.BPxView.getPxFile() -->
<method name="getPxFile"  public="true">
<description>
Get the &lt;code&gt;pxFile&lt;/code&gt; property.&#xa; Defaults the px file which contains the view
</description>
<tag name="@see">#pxFile</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.agent.BPxView.setPxFile(javax.baja.naming.BOrd) -->
<method name="setPxFile"  public="true">
<description>
Set the &lt;code&gt;pxFile&lt;/code&gt; property.&#xa; Defaults the px file which contains the view
</description>
<tag name="@see">#pxFile</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.BPxView.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.agent.BPxView.pxFile -->
<field name="pxFile"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;pxFile&lt;/code&gt; property.&#xa; Defaults the px file which contains the view
</description>
<tag name="@see">#getPxFile</tag>
<tag name="@see">#setPxFile</tag>
</field>

<!-- javax.baja.agent.BPxView.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
