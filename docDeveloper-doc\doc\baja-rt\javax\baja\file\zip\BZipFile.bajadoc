<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.zip.BZipFile" name="BZipFile" packageName="javax.baja.file.zip" public="true">
<description>
BZipFile is a BIFile that stores its own file space&#xa; addressed using the &#x22;zip&#x22; ord scheme.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">24 Jan 03</tag>
<tag name="@version">$Revision: 10$ $Date: 5/25/11 11:31:36 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.file.BSubSpaceFile"/>
</extends>
<!-- javax.baja.file.zip.BZipFile(javax.baja.file.BIFileStore) -->
<constructor name="BZipFile" public="true">
<parameter name="store">
<type class="javax.baja.file.BIFileStore"/>
</parameter>
<description>
Construct a file with the specified store.
</description>
</constructor>

<!-- javax.baja.file.zip.BZipFile(javax.baja.file.BIFileStore, java.nio.charset.Charset) -->
<constructor name="BZipFile" public="true">
<parameter name="store">
<type class="javax.baja.file.BIFileStore"/>
<description>
the File Store encapsulation of the zip file
</description>
</parameter>
<parameter name="charset">
<type class="java.nio.charset.Charset"/>
<description>
the charset of the zip file, defaults to UTF-8 in the underlying java ZipFile if null
</description>
</parameter>
<description>
Construct a file with the specified store and charset.
</description>
<tag name="@since">Niagara 4.10u8 / 4.13u3 / 4.14</tag>
</constructor>

<!-- javax.baja.file.zip.BZipFile() -->
<constructor name="BZipFile" public="true">
<description>
Construct (must call setStore()).
</description>
</constructor>

<!-- javax.baja.file.zip.BZipFile.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFile.isModified() -->
<method name="isModified"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Always return &lt;code&gt;false&lt;/code&gt;.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFile.doOpen() -->
<method name="doOpen"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Open the associated space.
</description>
<return>
<type class="javax.baja.space.BSpace"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFile.doSave() -->
<method name="doSave"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Throws Save the associated space.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.file.zip.BZipFile.doClose() -->
<method name="doClose"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Close and deallocate the cached BZipSpace.&#xa;&#xa; The next call to getZipSpace() will create another instance.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFile.hasNavChildren() -->
<method name="hasNavChildren"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return true.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFile.getZipSpace() -->
<method name="getZipSpace"  public="true">
<description>
Convenience for &lt;code&gt;(BZipSpace)getSubSpace()&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.file.zip.BZipSpace"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFile.getMimeType() -->
<method name="getMimeType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Default returns &lt;code&gt;&#x22;application/zip&#x22;&lt;/code&gt;
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFile.getNavOrd() -->
<method name="getNavOrd"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the nav ord.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFile.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the icon.
</description>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFile.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.file.zip.BZipFile.zipSpace -->
<field name="zipSpace"  protected="true">
<type class="javax.baja.file.zip.BZipSpace"/>
<description/>
</field>

</class>
</bajadoc>
