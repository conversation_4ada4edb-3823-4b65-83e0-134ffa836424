<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetMultiStateWritableDescriptor" name="BBacnetMultiStateWritableDescriptor" packageName="javax.baja.bacnet.export" public="true" abstract="true">
<description>
BBacnetMultiStateWritableDescriptor exposes a ControlPoint as a&#xa; commandable MultiState point.  It is the superclass for MultiState Output&#xa; and commandable MultiState Value points.
</description>
<tag name="@author"><PERSON> on 25 Jul 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.export.BBacnetMultiStatePointDescriptor"/>
</extends>
<implements>
<type class="javax.baja.bacnet.export.BacnetWritableDescriptor"/>
</implements>
<property name="bacnetWritable" flags="rh">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;bacnetWritable&lt;/code&gt; property.
</description>
<tag name="@see">#getBacnetWritable</tag>
<tag name="@see">#setBacnetWritable</tag>
</property>

<!-- javax.baja.bacnet.export.BBacnetMultiStateWritableDescriptor() -->
<constructor name="BBacnetMultiStateWritableDescriptor" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetMultiStateWritableDescriptor.getBacnetWritable() -->
<method name="getBacnetWritable"  public="true">
<description>
Get the &lt;code&gt;bacnetWritable&lt;/code&gt; property.
</description>
<tag name="@see">#bacnetWritable</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateWritableDescriptor.setBacnetWritable(java.lang.String) -->
<method name="setBacnetWritable"  public="true">
<description>
Set the &lt;code&gt;bacnetWritable&lt;/code&gt; property.
</description>
<tag name="@see">#bacnetWritable</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateWritableDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateWritableDescriptor.isPointTypeLegal(javax.baja.control.BControlPoint) -->
<method name="isPointTypeLegal"  protected="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
BBacnetEnumWritableDescriptor may only expose BEnumWritable.
</description>
<parameter name="pt">
<type class="javax.baja.control.BControlPoint"/>
<description>
the exposed point
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the Niagara point type is legal for this point type.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateWritableDescriptor.isCommandable() -->
<method name="isCommandable"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is this export descriptor representing a BACnet object&#xa; with a Commandable Present_Value property (per the Clause 19&#xa; prioritization procedure)?&lt;p&gt;&#xa; Writable descriptors must override this to return true.
</description>
<return>
<type class="boolean"/>
<description>
true if commandable, otherwise false
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateWritableDescriptor.readProperty(int, int) -->
<method name="readProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateWritableDescriptor.writeProperty(int, int, byte[], int) -->
<method name="writeProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateWritableDescriptor.knobAdded(javax.baja.sys.Knob, javax.baja.sys.Context) -->
<method name="knobAdded"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="knob">
<type class="javax.baja.sys.Knob"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateWritableDescriptor.knobRemoved(javax.baja.sys.Knob, javax.baja.sys.Context) -->
<method name="knobRemoved"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="knob">
<type class="javax.baja.sys.Knob"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateWritableDescriptor.doMakeWritable(javax.baja.sys.BValue) -->
<method name="doMakeWritable"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="writable">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateWritableDescriptor.bacnetWritable -->
<field name="bacnetWritable"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;bacnetWritable&lt;/code&gt; property.
</description>
<tag name="@see">#getBacnetWritable</tag>
<tag name="@see">#setBacnetWritable</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetMultiStateWritableDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
