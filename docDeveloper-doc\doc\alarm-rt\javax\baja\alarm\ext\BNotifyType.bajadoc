<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.ext.BNotifyType" name="BNotifyType" packageName="javax.baja.alarm.ext" public="true" final="true">
<description>
BNotifyType is an BEnum that represents valid Baja notification types
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">9 Nov 00</tag>
<tag name="@version">$Revision: 12$ $Date: 6/3/04 1:10:41 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;alarm&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;alert&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ackNotification&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.alarm.ext.BNotifyType.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.alarm.ext.BNotifyType"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BNotifyType.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.alarm.ext.BNotifyType"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BNotifyType.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BNotifyType.ALARM -->
<field name="ALARM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for alarm.
</description>
</field>

<!-- javax.baja.alarm.ext.BNotifyType.ALERT -->
<field name="ALERT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for alert.
</description>
</field>

<!-- javax.baja.alarm.ext.BNotifyType.ACK_NOTIFICATION -->
<field name="ACK_NOTIFICATION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ackNotification.
</description>
</field>

<!-- javax.baja.alarm.ext.BNotifyType.alarm -->
<field name="alarm"  public="true" static="true" final="true">
<type class="javax.baja.alarm.ext.BNotifyType"/>
<description>
BNotifyType constant for alarm.
</description>
</field>

<!-- javax.baja.alarm.ext.BNotifyType.alert -->
<field name="alert"  public="true" static="true" final="true">
<type class="javax.baja.alarm.ext.BNotifyType"/>
<description>
BNotifyType constant for alert.
</description>
</field>

<!-- javax.baja.alarm.ext.BNotifyType.ackNotification -->
<field name="ackNotification"  public="true" static="true" final="true">
<type class="javax.baja.alarm.ext.BNotifyType"/>
<description>
BNotifyType constant for ackNotification.
</description>
</field>

<!-- javax.baja.alarm.ext.BNotifyType.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.alarm.ext.BNotifyType"/>
<description/>
</field>

<!-- javax.baja.alarm.ext.BNotifyType.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
