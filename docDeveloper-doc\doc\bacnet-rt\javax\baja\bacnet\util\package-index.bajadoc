<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="javax.baja.bacnet.util">
<description/>
<class packageName="javax.baja.bacnet.util" name="BacnetBitStringUtil"><description>BacnetBitStringUtil.</description></class>
<class packageName="javax.baja.bacnet.util" name="BBacnetWorker"><description>BBacnetWorker is the implementation of BWorker for Bacnet.</description></class>
<class packageName="javax.baja.bacnet.util" name="BPriorityArrayDetector"><description>The PriorityArrayDetector enables stations engineered offline&#xa;  to properly detect the presence or absence of a Priority Array once&#xa;  the station has been installed.</description></class>
<class packageName="javax.baja.bacnet.util" name="EnumRangeWrapper"><description>Wrap the BEnumRange and potential ErrorType while&#xa; creating the enum range</description></class>
<class packageName="javax.baja.bacnet.util" name="GrandchildChangedContext"/>
<class packageName="javax.baja.bacnet.util" name="LocalBacnetPoll"><description>LocalBacnetPoll&#xa; This base class provides a mechanism for performing local polls of BACnet&#xa; properties from BACnet export objects in Niagara&#x27;s export table.</description></class>
<class packageName="javax.baja.bacnet.util" name="MetaDataContext"/>
<class packageName="javax.baja.bacnet.util" name="PollList"><description>PollList.</description></class>
<class packageName="javax.baja.bacnet.util" name="PollListEntry"/>
<class packageName="javax.baja.bacnet.util" name="PropertyInfo"><description>PropertyInfo provides information about a Bacnet property.</description></class>
<class packageName="javax.baja.bacnet.util" name="SpecialEventDetails"/>
<class packageName="javax.baja.bacnet.util" name="BIBacnetPollable" category="interface"><description>BIBacnetPollable</description></class>
</package>
</bajadoc>
