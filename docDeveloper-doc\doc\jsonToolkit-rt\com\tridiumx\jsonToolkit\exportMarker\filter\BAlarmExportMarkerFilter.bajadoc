<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmExportMarkerFilter" name="BAlarmExportMarkerFilter" packageName="com.tridiumx.jsonToolkit.exportMarker.filter" public="true">
<description>
Passes alarms to a JsonRecipient if they have an Export Marker on the source point, which has been &#x22;registered&#x22; in&#xa; the cloud recipient as indicated by the presence of an Id on the Export Marker.&#xa;&#xa; The Alarm Export Marker Filter allows alarms generated by the station to be filtered&#xa; before they are passed to a recipient. Typically that would be a [Json Alarm&#xa; Recipient](alarms.html), but it could be SNMP, BACnet etc. with the source&#xa; Alarm Class being linked to the `in` slot of the filter.
</description>
<tag name="@author"><PERSON>lard</tag>
<extends>
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter"/>
</extends>
<property name="mode" flags="">
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode"/>
<description>
Slot for the &lt;code&gt;mode&lt;/code&gt; property.
</description>
<tag name="@see">#getMode</tag>
<tag name="@see">#setMode</tag>
</property>

<action name="in" flags="sA">
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;in&lt;/code&gt; action.
</description>
<tag name="@see">#in(BAlarmRecord parameter)</tag>
</action>

<topic name="out" flags="s">
<eventType>
<type class="javax.baja.alarm.BAlarmRecord"/>
</eventType><description>
Slot for the &lt;code&gt;out&lt;/code&gt; topic.
</description>
<tag name="@see">#fireOut</tag>
</topic>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmExportMarkerFilter() -->
<constructor name="BAlarmExportMarkerFilter" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmExportMarkerFilter.getMode() -->
<method name="getMode"  public="true">
<description>
Get the &lt;code&gt;mode&lt;/code&gt; property.
</description>
<tag name="@see">#mode</tag>
<return>
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmExportMarkerFilter.setMode(com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode) -->
<method name="setMode"  public="true">
<description>
Set the &lt;code&gt;mode&lt;/code&gt; property.
</description>
<tag name="@see">#mode</tag>
<parameter name="v">
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmExportMarkerFilter.in(javax.baja.alarm.BAlarmRecord) -->
<method name="in"  public="true">
<description>
Invoke the &lt;code&gt;in&lt;/code&gt; action.
</description>
<tag name="@see">#in</tag>
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmExportMarkerFilter.fireOut(javax.baja.alarm.BAlarmRecord) -->
<method name="fireOut"  public="true">
<description>
Fire an event for the &lt;code&gt;out&lt;/code&gt; topic.
</description>
<tag name="@see">#out</tag>
<parameter name="event">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmExportMarkerFilter.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmExportMarkerFilter.doSendSince(javax.baja.sys.BAbsTime) -->
<method name="doSendSince"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unused&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
The send since action will query the alarm database and pass existing&#xa; records `in` to this filter so that they can be checked for a suitable&#xa; Export Marker and then passed to the receiving Json Schema as required&#xa; to create a new record for each alarm. The timestamp being in the past&#xa; should help identify when this mode is active.
</description>
<parameter name="since">
<type class="javax.baja.sys.BAbsTime"/>
<description>
The earliest date to submit alarm
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmExportMarkerFilter.doIn(javax.baja.alarm.BAlarmRecord) -->
<method name="doIn"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unused&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<parameter name="record">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmExportMarkerFilter.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmExportMarkerFilter.mode -->
<field name="mode"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;mode&lt;/code&gt; property.
</description>
<tag name="@see">#getMode</tag>
<tag name="@see">#setMode</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmExportMarkerFilter.in -->
<field name="in"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;in&lt;/code&gt; action.
</description>
<tag name="@see">#in(BAlarmRecord parameter)</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmExportMarkerFilter.out -->
<field name="out"  public="true" static="true" final="true">
<type class="javax.baja.sys.Topic"/>
<description>
Slot for the &lt;code&gt;out&lt;/code&gt; topic.
</description>
<tag name="@see">#fireOut</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmExportMarkerFilter.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
