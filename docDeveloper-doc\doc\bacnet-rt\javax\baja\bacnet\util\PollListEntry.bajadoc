<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.util.PollListEntry" name="PollListEntry" packageName="javax.baja.bacnet.util" public="true">
<description/>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="javax.baja.sys.Context"/>
</implements>
<!-- javax.baja.bacnet.util.PollListEntry(javax.baja.bacnet.point.BBacnetProxyExt) -->
<constructor name="PollListEntry" public="true">
<parameter name="pt">
<type class="javax.baja.bacnet.point.BBacnetProxyExt"/>
<description/>
</parameter>
<description>
Constructor.&#xa; Used for proxy point primary value.
</description>
</constructor>

<!-- javax.baja.bacnet.util.PollListEntry(javax.baja.bacnet.point.BBacnetProxyExt, int) -->
<constructor name="PollListEntry" public="true">
<parameter name="pt">
<type class="javax.baja.bacnet.point.BBacnetProxyExt"/>
<description/>
</parameter>
<parameter name="pId">
<type class="int"/>
<description/>
</parameter>
<description>
Constructor.&#xa; Used for proxy point additional metadata from same object.
</description>
</constructor>

<!-- javax.baja.bacnet.util.PollListEntry(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, javax.baja.bacnet.BBacnetDevice, javax.baja.bacnet.util.BIBacnetPollable) -->
<constructor name="PollListEntry" public="true">
<parameter name="oid">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<parameter name="pid">
<type class="int"/>
<description/>
</parameter>
<parameter name="dev">
<type class="javax.baja.bacnet.BBacnetDevice"/>
<description/>
</parameter>
<parameter name="p">
<type class="javax.baja.bacnet.util.BIBacnetPollable"/>
<description/>
</parameter>
<description>
Constructor.&#xa; Used for BBacnetObject polled properties.
</description>
</constructor>

<!-- javax.baja.bacnet.util.PollListEntry(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int, javax.baja.bacnet.BBacnetDevice, javax.baja.bacnet.util.BIBacnetPollable) -->
<constructor name="PollListEntry" public="true">
<parameter name="oid">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<parameter name="pid">
<type class="int"/>
<description/>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description/>
</parameter>
<parameter name="dev">
<type class="javax.baja.bacnet.BBacnetDevice"/>
<description/>
</parameter>
<parameter name="p">
<type class="javax.baja.bacnet.util.BIBacnetPollable"/>
<description/>
</parameter>
<description>
Metadata Constructor.&#xa; Used for any additional poll properties, such as event enrollment&#xa; objects monitoring a proxy point.
</description>
</constructor>

<!-- javax.baja.bacnet.util.PollListEntry(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int, javax.baja.bacnet.BBacnetDevice, javax.baja.bacnet.util.BIBacnetPollable, javax.baja.sys.Context) -->
<constructor name="PollListEntry" public="true">
<parameter name="oid">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<parameter name="pid">
<type class="int"/>
<description/>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description/>
</parameter>
<parameter name="dev">
<type class="javax.baja.bacnet.BBacnetDevice"/>
<description/>
</parameter>
<parameter name="p">
<type class="javax.baja.bacnet.util.BIBacnetPollable"/>
<description/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description/>
</parameter>
<description>
Full Constructor.&#xa; Used for virtual components.
</description>
</constructor>

<!-- javax.baja.bacnet.util.PollListEntry.getObjectId() -->
<method name="getObjectId"  public="true" final="true">
<description/>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the objectId
</description>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.getPropertyId() -->
<method name="getPropertyId"  public="true" final="true">
<description/>
<return>
<type class="int"/>
<description>
the propertyId
</description>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.getPropertyArrayIndex() -->
<method name="getPropertyArrayIndex"  public="true" final="true">
<description/>
<return>
<type class="int"/>
<description>
the propertyArrayIndex
</description>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.getDevice() -->
<method name="getDevice"  public="true" final="true">
<description/>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
<description>
the device
</description>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.getAddressHash() -->
<method name="getAddressHash"  public="true" final="true">
<description/>
<return>
<type class="int"/>
<description>
the address
</description>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.getPollable() -->
<method name="getPollable"  public="true" final="true">
<description/>
<return>
<type class="javax.baja.bacnet.util.BIBacnetPollable"/>
<description>
the pollable
</description>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.getDataSize() -->
<method name="getDataSize"  public="true" final="true">
<description/>
<return>
<type class="int"/>
<description>
the data size for this poll list entry
</description>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.setDataSize(int) -->
<method name="setDataSize"  public="true" final="true">
<description>
Set the data size for this poll list entry.
</description>
<parameter name="dataSize">
<type class="int"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.doubleDataSize(int) -->
<method name="doubleDataSize"  public="true" final="true">
<description/>
<parameter name="max">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.getContext() -->
<method name="getContext"  public="true" final="true">
<description/>
<return>
<type class="javax.baja.sys.Context"/>
<description>
the context if not null, otherwise return this.
</description>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.setContext(javax.baja.sys.Context) -->
<method name="setContext"  public="true" final="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
<description>
the context to set
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.getPollList() -->
<method name="getPollList"  public="true" final="true">
<description/>
<return>
<type class="javax.baja.bacnet.util.PollList"/>
<description>
the pollList
</description>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.setPollList(javax.baja.bacnet.util.PollList) -->
<method name="setPollList"  public="true" final="true">
<description/>
<parameter name="pollList">
<type class="javax.baja.bacnet.util.PollList"/>
<description>
the pollList to set
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description/>
<parameter name="o">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.hashCode() -->
<method name="hashCode"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.toString() -->
<method name="toString"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.debugString() -->
<method name="debugString"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.getBase() -->
<method name="getBase"  public="true">
<description>
If this Context wraps another Context, then return&#xa; the base Context.  Otherwise return null.
</description>
<return>
<type class="javax.baja.sys.Context"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.getUser() -->
<method name="getUser"  public="true">
<description>
Get the user for the context of this operation,&#xa; or null if no user information is available.
</description>
<return>
<type class="javax.baja.user.BUser"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.getFacets() -->
<method name="getFacets"  public="true">
<description>
Get the facets table.  This should never be null.&#xa; Return BFacets.DEFAULT if no facets are available.
</description>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.getFacet(java.lang.String) -->
<method name="getFacet"  public="true">
<description>
Get a facet value by name, or null.
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.getLanguage() -->
<method name="getLanguage"  public="true">
<description>
Get the language code for the context operation.&#xa; This method should never return null.  As a default&#xa; return Sys.getLanguage().
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollListEntry.DEFAULT_DATASIZE -->
<field name="DEFAULT_DATASIZE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.PollListEntry.pointCx -->
<field name="pointCx"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description>
PointContext: used for proxy point primary value.
</description>
</field>

<!-- javax.baja.bacnet.util.PollListEntry.forceCx -->
<field name="forceCx"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description>
ForceContext: used for proxy point force reads.
</description>
</field>

<!-- javax.baja.bacnet.util.PollListEntry.metaCx -->
<field name="metaCx"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description>
MetaContext: used for proxy point metadata.
</description>
</field>

</class>
</bajadoc>
