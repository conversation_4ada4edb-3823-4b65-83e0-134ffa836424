<html>
<head>
</head>
<body style="background: transparent;">
    <script src="scripts/docstrap.lib.js"></script>
    <script src="scripts/lunr.min.js"></script>
    <script src="scripts/fulltext-search.js"></script>

    <script type="text/x-docstrap-searchdb">
    {"modules.list.html":{"id":"modules.list.html","title":"Modules","body":" js Modules dialogslexlognmodule/js/rc/csrf/csrfUtilnmodule/js/rc/jasmine/promiseUtilsnmodule/js/rc/lex/lexnmodule/js/rc/log/Levelnmodule/js/rc/log/Log Classes dialogs~Dialognmodule/js/rc/lex/lex~Lexiconnmodule/js/rc/log/Log.Level Modules Classes Dialog Lexicon module:nmodule/js/rc/log/Log Level × Search results Close "},"classes.list.html":{"id":"classes.list.html","title":"Classes","body":" js Modules dialogslexlognmodule/js/rc/csrf/csrfUtilnmodule/js/rc/jasmine/promiseUtilsnmodule/js/rc/lex/lexnmodule/js/rc/log/Levelnmodule/js/rc/log/Log Classes dialogs~Dialognmodule/js/rc/lex/lex~Lexiconnmodule/js/rc/log/Log.Level Classes Classes Dialog Lexicon module:nmodule/js/rc/log/Log Level × Search results Close "},"index.html":{"id":"index.html","title":"Index","body":" js Modules dialogslexlognmodule/js/rc/csrf/csrfUtilnmodule/js/rc/jasmine/promiseUtilsnmodule/js/rc/lex/lexnmodule/js/rc/log/Levelnmodule/js/rc/log/Log Classes dialogs~Dialognmodule/js/rc/lex/lex~Lexiconnmodule/js/rc/log/Log.Level × Search results Close "},"module-dialogs.html":{"id":"module-dialogs.html","title":"Module: dialogs","body":" js Modules dialogslexlognmodule/js/rc/csrf/csrfUtilnmodule/js/rc/jasmine/promiseUtilsnmodule/js/rc/lex/lexnmodule/js/rc/log/Levelnmodule/js/rc/log/Log Classes dialogs~Dialognmodule/js/rc/lex/lex~Lexiconnmodule/js/rc/log/Log.Level Module: dialogs Create stunning, modal Dialog boxes in JavaScript. This is a UI library used to create dynamic, modal Dialog boxes in your browser. Examples A simple modal OK dialog box. dialogs.showOk(\"Some Dialog Box Content\") .ok(function () { console.log(\"The OK button has been clicked\"); }); A Dialog box with a title and some HTML content. dialogs.showOk({ title: \"The Dialog Box's Title!\", content: \"&amp;lt;p&amp;gt;Some HTML Content&amp;lt;/p&amp;gt;\" }) .ok(function () { console.log(\"The OK button has been clicked\"); }); A simple Yes, No, Cancel dialog box. dialogs.showYesNoCancel(\"Would you like some tea with that?\") .yes(function () { console.log(\"The user clicked Yes\"); }) .no(function() { console.log(\"The user clicked No\"); }) .cancel(function () { console.log(\"The user clicked Cancel\"); }); Show a loading dialog box and have it close after the AJAX call has finished. dialogs.showLoading(0, $.ajax(uri, options)); Use promises to show a loading dialog box and then pop up another dialog. var dlg = dialogs.showLoading(); // After 2 seconds, close the loading box. setTimeout(function () { dlg.close(); }, 2000); dlg.promise().then(([ dlg, buttonClicked ]) =&gt; { // Prints 'ok' console.log(buttonClicked); dialogs.showOk(\"The foobar has finished loading!\"); }); Show a dialog. Have the content dynamically created by passing in a function for the content. dialogs.show(function(dlg, jq) { jq.html(\"&amp;lt;div&amp;gt;I love Niagara 4!&amp;lt;/div&amp;gt;\"); }); Show a dialog. Have the content dynamically created by passing in a function for the content. The dialog will only show when the return promise has been resolved. dialogs.show(function(dlg, jq) { return Promise.resolve($.ajax(\"/myajax\") .then(function (response) { jq.html(\"The answer is...\" + JSON.parse(response).answer); }); }); A Dialog BOX with background privacy setting. dialogs.showOk({ title: \"The Dialog Box's Title!\", content: \"&amp;lt;p&amp;gt;Some HTML Content&amp;lt;/p&amp;gt;\", private: true //ensures background contents are screened when the dialog is showing }) .ok(function () { console.log(\"The OK button has been clicked\"); }); Requires module:jquery module:lex!js module:css!nmodule/js/rc/dialogs/dialogs Classes Dialog Type Definitions Button An Object that defines a Button. Type: Object Properties: Name Type Argument Description name String &lt;optional&gt; A button's unique name so it can be identified. displayName String &lt;optional&gt; The button's display name. This name will used on the button. If not specified, the button's name will be used instead. handler function &lt;optional&gt; A function that will be invoked when the button is clicked. Returning false will keep the dialog from closing after this handler has been invoked. disable boolean &lt;optional&gt; if set to true, the button will be disabled until enableButton is called. hide boolean &lt;optional&gt; if set to true, the button will be hidden until showButton is called. esc Boolean &lt;optional&gt; If true, this handler will be invoked when the user hits the escape key. See: module:dialogs~Dialog#on × Search results Close "},"module-dialogs-Dialog.html":{"id":"module-dialogs-Dialog.html","title":"Class: Dialog","body":" js Modules dialogslexlognmodule/js/rc/csrf/csrfUtilnmodule/js/rc/jasmine/promiseUtilsnmodule/js/rc/lex/lexnmodule/js/rc/log/Levelnmodule/js/rc/log/Log Classes dialogs~Dialognmodule/js/rc/lex/lex~Lexiconnmodule/js/rc/log/Log.Level Class: Dialog dialogs~ Dialog new Dialog() A class for a Dialog box. An instance of a Dialog can be accessed indirectly by use of one of the showXxx methods. Example Show a basic simple OK Dialog box dialogs.showOk(\"Here's a nice OK dialog box!\"); Methods buttonJq(name) Return the button DOM for the given name. Parameters: Name Type Description name String The name of the button. Returns: the Button's jQuery DOM object or null if nothing found. Type JQuery | null cancel( [handler]) Add a 'cancel' handler to the Dialog or if no handler is specified, simulate clicking the Dialog's 'cancel' button. Parameters: Name Type Argument Description handler function &lt;optional&gt; if specified, the handler to be invoked when the Dialog's 'cancel' button is clicked. The first argument of the function callback is the Dialog instance. See: module:dialogs~Dialog#on Returns: Type module:dialogs~Dialog click(name) Click one of the Dialog's buttons. Parameters: Name Type Description name String The name of the Dialog button to click. Returns: Type module:dialogs~Dialog Example Show a Dialog with an OK button and click it 2 seconds later var dlg = dialogs.showOk(\"This is an OK Dialog\") setTimeout(function () { dlg.click(\"ok\") }, 2000); close( [name] [, fail]) Close the Dialog. This will remove the Dialog box from the screen. Parameters: Name Type Argument Description name String &lt;optional&gt; The name of the button used to close the dialog box. This parameter is designed to be called from the Dialog JS framework itself. fail * &lt;optional&gt; optional failure reason. If truthy, the dialog's promise will be rejected with this failure reason; otherwise, the promise will be resolved. Returns: Type module:dialogs~Dialog Example Open a Dialog and close it after 2 seconds var dlg = dialogs.showOk(\"A notification\"); setTimeout(function () { dlg.close(); }, 2000); content() If this dialog has content, return the jQuery DOM wrapper for it. Returns: The DOM wrapper for the content. This wrapper will be empty if the dialog is shown with no content. Type JQuery disableButton(name) Disable a button. Parameters: Name Type Description name String The name of the button. Returns: Type module:dialogs~Dialog enableButton(name) Enable a button. Parameters: Name Type Description name String The name of the button. Returns: Type module:dialogs~Dialog header() If this dialog has a header, return the jQuery DOM wrapper for it. Since: Niagara 4.12 Returns: The DOM wrapper for the header. This wrapper will be empty if the dialog is shown with no header. Type JQuery hide() Hide the Dialog without closing it. The preferred method to use is close. Returns: Type module:dialogs~Dialog Example Hide a dialog box after 2 seconds dialogs.showYesNo(\"Meeting alert! Do you want to be reminded in 10 seconds?\") .yes(function (dialog) { dialog.hide(); setTimeout(function () { dialog.show(); }, 10000); return false; }); hideButton(name) Hide a button. Parameters: Name Type Description name String The name of the button. Returns: Type module:dialogs~Dialog isClosed() Return true if the Dialog is closed and removed from the DOM. Returns: Return true if the Dialog has been closed. Type Boolean isHidden() Return true if the Dialog is hidden. Returns: Return true if the Dialog has been hidden. Type Dialog jq() Return the internal jQuery wrapped DOM element for the entire Dialog. Returns: the Dialog's jQuery DOM object. Type JQuery no( [handler]) Add a 'no' handler to the Dialog or if no handler is specified, simulate clicking the Dialog's 'no' button. The first argument of the function callback is the Dialog instance. Parameters: Name Type Argument Description handler function &lt;optional&gt; if specified, the handler to be invoked when the Dialog's 'no' button is clicked. See: module:dialogs~Dialog#on Returns: Type module:dialogs~Dialog ok( [handler]) Add a 'ok' handler to the Dialog or if no handler is specified, simulate clicking the Dialog's 'ok' button. Parameters: Name Type Argument Description handler function &lt;optional&gt; if specified, the handler to be invoked when the Dialog's 'ok' button is clicked. The first argument of the function callback is the Dialog instance. See: module:dialogs~Dialog#on Returns: Type module:dialogs~Dialog on(name, handler) Add a callback handler for a button via its name. This callback handler will be invoked when the button is clicked. Any handler function can return a Promise. This can control when and if the Dialog box closes after the handler has been invoked. It should be noted that multiple handlers can be registered on a button. If the handlers return nothing, the Dialog will be closed after all the Handlers have been invoked. If one or more handlers return a Promise, the Dialog will only close after all the Promises have been resolved. If one of the Promises is rejected, the Dialog will not close. Parameters: Name Type Description name String The name of the button to register the handler on. handler function The handler of the function to be invoked when the button is clicked. When invoked, the first argument of the handler is the Dialog instance. See: module:dialogs~Dialog#ok module:dialogs~Dialog#cancel module:dialogs~Dialog#yes module:dialogs~Dialog#no Returns: Type module:dialogs~Dialog Example Register a function be to be called when the 'foo' button is clicked. dialogs.show({ content: \"Show some stuff\", buttons: [ { name: \"foo\", handler: function () { alert(\"First annoying alert!\"); } } ] }).on(\"foo\", function () { alert(\"This will also be called when foo button is clicked.\"); }); promise() Return a promise for the dialog that will be resolved when the dialog closes. This is useful when wanting to use dialogs in a promise chain when creating a user interface. Returns: The promise to be resolved. Type Promise show() Show the Dialog. Returns: Type module:dialogs~Dialog Example Create a blank dialog box and then show it. dialogs.make(\"A dialog box with no buttons!\") .show(); showButton(name) Show a button. Parameters: Name Type Description name String The name of the button. Returns: Type module:dialogs~Dialog toBack() Move the Dialog to the back. Returns: Type module:dialogs~Dialog toFront() Move the Dialog to the front. Returns: Type module:dialogs~Dialog yes( [handler]) Add a 'yes' handler to the Dialog or if no handler is specified, simulate clicking the Dialog's 'yes' button. Parameters: Name Type Argument Description handler function &lt;optional&gt; if specified, the handler to be invoked when the Dialog's 'yes' button is clicked. The first argument of the function callback is the Dialog instance. See: module:dialogs~Dialog#on Returns: Type module:dialogs~Dialog × Search results Close "},"module-lex.html":{"id":"module-lex.html","title":"Module: lex","body":" js Modules dialogslexlognmodule/js/rc/csrf/csrfUtilnmodule/js/rc/jasmine/promiseUtilsnmodule/js/rc/lex/lexnmodule/js/rc/log/Levelnmodule/js/rc/log/Log Classes dialogs~Dialognmodule/js/rc/lex/lex~Lexiconnmodule/js/rc/log/Log.Level Module: lex A JavaScript library used for translating HTML5 web applications into different languages. This is a plug-in for RequireJS. The plug-in will be used whenever a requirement for lex!moduleName is used in RequireJS. This plug-in is used for importing Lexicons. For extra network efficiency, it's recommended to provide a comma separated list of of Lexicons to import. For example, lex!foo,goo. See module:nmodule/js/rc/lex/lex for more information and some examples. See: module:nmodule/js/rc/lex/lex × Search results Close "},"module-log.html":{"id":"module-log.html","title":"Module: log","body":" js Modules dialogslexlognmodule/js/rc/csrf/csrfUtilnmodule/js/rc/jasmine/promiseUtilsnmodule/js/rc/lex/lexnmodule/js/rc/log/Levelnmodule/js/rc/log/Log Classes dialogs~Dialognmodule/js/rc/lex/lex~Lexiconnmodule/js/rc/log/Log.Level Module: log RequireJS plugin for retrieving Log instances. See Log for more details on working with logs. See: module:nmodule/js/rc/log/Log module:nmodule/js/rc/log/Log~Handler Examples Pass the plugin a logger name to get a preconfigured Log instance for that name. require(['log!nmodule.myModule.rc.views.FooView'], function (fooLog) { fooLog.info('info msg'); fooLog.warning('warning msg'); if (fooLog.isLoggable('FINE')) { fooLog.fine(buildExpensiveLogMessage()); } }); Pass the plugin \"console\" or \"browser.console\" to get Log itself, whose API matches the browser console. require(['log!console'], function (console) { console.log('this will log to the browser console', 'and up to the station too'); }); Register custom log handlers using the logHandlers config. See web:IRequireJsConfig for one way to accomplish this. require.config['nmodule/js/rc/log/Log'].logHandlers = [ 'nmodule/myModule/rc/myCustomHandler' ]; // in myModule/src/rc/myCustomHandler.js: define([], function () { 'use strict'; return { publish: function (name, level, msg) { //implement behavior here. //return writeToLogFile(name, level, msg); //return appendToHistory(name, level, msg); //return postToCloud(name, level, msg); } }; }); In most cases, log levels are automatically set for you, and they will match the settings you configured in the Logger Configuration Workbench tool. But if you are building a RequireJS config from scratch, you can specify log levels per-package using the logLevels config. require.config['nmodule/js/rc/log/Log'].logLevels = { 'my.package.name': 'FINE', 'my.other.package.name': 'SEVERE' }; × Search results Close "},"module-nmodule_js_rc_csrf_csrfUtil.html":{"id":"module-nmodule_js_rc_csrf_csrfUtil.html","title":"Module: nmodule/js/rc/csrf/csrfUtil","body":" js Modules dialogslexlognmodule/js/rc/csrf/csrfUtilnmodule/js/rc/jasmine/promiseUtilsnmodule/js/rc/lex/lexnmodule/js/rc/log/Levelnmodule/js/rc/log/Log Classes dialogs~Dialognmodule/js/rc/lex/lex~Lexiconnmodule/js/rc/log/Log.Level Module: nmodule/js/rc/csrf/csrfUtil An API to get the CSRF token. Members &lt;static&gt; CSRF_TOKEN_HEADER_KEY :string Csrf token http header key name. Type: string Methods &lt;static&gt; getCsrfToken() Get the URI encoded form of the csrf token. Returns: CSRF string (encoded) Type String Example Access the CSRF token and pass to AJAX as a header define([\"nmodule/js/rc/csrf/csrfUtil\"], function(csrfUtil){ var csrfToken = csrfUtil.getCsrfToken(); var headers = {}; headers[csrfUtil.CSRF_TOKEN_HEADER_KEY] = csrfToken; $.ajax(\"someURI\", { method: \"POST\", data: \"someContent\", headers : headers }); }); × Search results Close "},"module-nmodule_js_rc_jasmine_promiseUtils.html":{"id":"module-nmodule_js_rc_jasmine_promiseUtils.html","title":"Module: nmodule/js/rc/jasmine/promiseUtils","body":" js Modules dialogslexlognmodule/js/rc/csrf/csrfUtilnmodule/js/rc/jasmine/promiseUtilsnmodule/js/rc/lex/lexnmodule/js/rc/log/Levelnmodule/js/rc/log/Log Classes dialogs~Dialognmodule/js/rc/lex/lex~Lexiconnmodule/js/rc/log/Log.Level Module: nmodule/js/rc/jasmine/promiseUtils API Status: Private Module with utility functions for running async Jasmine specs. Methods &lt;static&gt; addCustomMatchers( [spec]) Adds custom matchers to the Jasmine instance (what is bound to this in a beforeEach function, for instance). Parameters: Name Type Argument Description spec &lt;optional&gt; The current Jasmine spec; if not given, jasmine.getEnv().currentSpec will be used See: CustomMatchers Example beforeEach(function () { promiseUtils.addCustomMatchers(this); }); //or beforeEach(promiseUtils.addCustomMatchers); &lt;static&gt; doPromise(promise [, timeoutMessage] [, timeout]) Runs a promise, using the Jasmine runs/waitsFor functions to ensure its completion. This function will verify that the promise is resolved - failing the promise will fail the test. Parameters: Name Type Argument Default Description promise Promise timeoutMessage String &lt;optional&gt; optional message to present if timeout occurs timeout Number &lt;optional&gt; 5000 optional timeout in milliseconds Returns: promise that is verified to have been resolved (if the input promise rejects, the test will fail). Type Promise Example promiseUtils.doPromise(editor.read() .then(function (result) { expect(result).toBe('my expected read value'); }, function (err) { //not necessary to assert anything here - failing the promise will //automatically fail the test. //if you want to verify fail behavior, use toBeRejected() or //toBeRejectedWith() custom matchers. })); &lt;static&gt; enforcePromiseAPI() Ensure that the following contract is followed when using doPromise and executePromise: You may not call .then() on the result of doPromise(). This ensures that doPromise() works correctly with the runs/waits async API presented by Jasmine 1.3. &lt;static&gt; executePromise(promise [, timeoutMessage] [, within]) Runs a promise, using the Jasmine runs/waitsFor functions to ensure its completion. This method only cares that the promise is settled (resolved or rejected) - if you wish to assert that the promise resolves successfully, use doPromise instead. Parameters: Name Type Argument Default Description promise Promise timeoutMessage String &lt;optional&gt; optional message to present if timeout occurs within Number &lt;optional&gt; 5000 optional timeout in milliseconds Returns: promise that may be resolved or rejected Type Promise &lt;static&gt; isTrackingReturnValues(spy) Parameters: Name Type Description spy function Since: Niagara 4.13 Returns: true if this is a spy function that is tracking return values Type boolean &lt;static&gt; noConflict() By default, promiseUtils augments Jasmine block execution to support returning promises from blocks and validating manual calls to doPromise, executePromise, and promise matchers. Call this to restore original Jasmine block execution. There will not typically be a reason to call this in practice, but it is provided just in case. &lt;static&gt; prettyPrintBajaObjects() when doing expect(method).toHaveBeenCalledWith(component), jasmine JSON stringifies the component to print the error. a component's JSON structure is so huge that this will actually lock up the browser and kill tests. i found myself having to do expect(method.mostRecentCall.args[0] === component).toBe(true). yuck. let's simplify the pretty printing a bit. &lt;static&gt; trackSpyReturnValues() Jasmine does not store return values by default. Patch it in so that each call object, in addition to object and args, stores a result property. Since: Niagara 4.13 &lt;static&gt; waitForCalled(func [, times] [, timeout]) Return a promise that resolves after the given Jasmine spy has been called the specified number of times. Parameters: Name Type Argument Default Description func function a Jasmine spy times Number &lt;optional&gt; 1 the number of times to expect the function to have been called. Defaults to 1. timeout Number &lt;optional&gt; 5000 the time, in milliseconds, after which to give up waiting and reject. Returns: Type Promise &lt;static&gt; waitForResolved(func [, times] [, timeout]) This will both wait for a spy function to be called, and for the promise it returns to be resolved. The spy must be tracking return values already - use .andTrackReturnValues(). This is so the matcher can monitor the resolution status of the returned promises. Parameters: Name Type Argument Default Description func times Number &lt;optional&gt; 1 the number of times to expect the function to have been called. Defaults to 1. timeout Number &lt;optional&gt; 5000 the time, in milliseconds, after which to give up waiting and reject. Returns: if times is omitted, this will resolve to the result of the first call to the function. Otherwise, this will resolve to an array (of times length) of that many calls to the function. Type Promise.&lt;(*|Array.&lt;*&gt;)&gt; &lt;static&gt; waitForReturnedPromises() Alters the default behavior of it(). If a Promise is returned from an it() call, Jasmine will wait for that promise to resolve (up to the default timeout) before completing the spec. Example promiseUtils.waitForReturnedPromises(); it('waits for a returned promise to resolve', function () { return promiseUtils.waitInterval(1000) .then(function () { expect('a').toBe('b'); //correctly fails, because Jasmine waited }); }); &lt;static&gt; waitForTrue(func [, msg] [, timeout]) Run a promise, using setTimeout to check for the truthiness of the condition function. This will not use waitsFor/runs and as such can be used in conjunction with doPromise/executePromise. Parameters: Name Type Argument Description func function resolve the promise when this function returns a truthy value, or a promise that resolves to a truthy value msg String &lt;optional&gt; the message to reject with upon timeout timeout Number &lt;optional&gt; the time, in milliseconds, after which to give up waiting and reject Returns: Type Promise &lt;static&gt; waitInterval( [interval]) Return a promise that waits a certain number of milliseconds before resolving. This will not use waitsFor/runs and as such can be used in conjunction with doPromise/executePromise. Parameters: Name Type Argument Default Description interval Number &lt;optional&gt; 0 Returns: Type Promise Type Definitions PromiseResolutionParams Type: object Properties: Name Type Argument Description within number &lt;optional&gt; promise must settle within this many milliseconds timeoutMessage string &lt;optional&gt; fail the spec with this message if the promise does not settle in time × Search results Close "},"module-nmodule_js_rc_lex_lex.html":{"id":"module-nmodule_js_rc_lex_lex.html","title":"Module: nmodule/js/rc/lex/lex","body":" js Modules dialogslexlognmodule/js/rc/csrf/csrfUtilnmodule/js/rc/jasmine/promiseUtilsnmodule/js/rc/lex/lexnmodule/js/rc/log/Levelnmodule/js/rc/log/Log Classes dialogs~Dialognmodule/js/rc/lex/lex~Lexiconnmodule/js/rc/log/Log.Level Module: nmodule/js/rc/lex/lex A JavaScript library used to access translated Lexicon values from the Niagara Framework. This library will make network calls back to a Web Server to access translated values. Attempts will also be made to use local storage to cache recorded Lexicon values. If a user logs on with a different locale or the registry has been updated, this storage will be automatically cleared. Please try out the examples from the BajauxExamples folder available from the docDeveloper palette to see how this gets used. Also there are some more code examples embedded into the method comments below. RequireJS configuration options can be specified for this library... noStorage: if truthy, no attempt at using storage will be used. forceInit: if truthy, an 'init' network call will be always be made the first time this library loads. lang: if specified, the user locale for the Lexicons. If this isn't specified the library will make a network call for it when it first loads. storageId: if specified, the id that helps determine whether the storage database currently being used is out of date. This library is designed to be used through the RequireJS Lexicon plugin... See: module:lex Examples Access the Lexicon JS library using RequireJS. require([\"lex!\"], function (lexjs) { lexjs.module(\"js\") .then(function (lex) { console.log(\"The Dialog OK button text: \" + lex.get(\"dialogs.ok\")); }); }); Directly access a module's Lexicon using the plug-in syntax for RequireJS require([\"lex!js,bajaui\"], function (lexicons) { // The lexicon's array holds the Lexicon for both the js and bajaui modules. console.log(\"The Dialog OK button text: \" + lexicons[0].get(\"dialogs.ok\")); }); Requires module:Promise, Classes Lexicon Methods &lt;static&gt; format(str) Asynchronously format a String using Niagara's BFormat conventions. Parameters: Name Type Description str String the string that contains the BFormat style text to use. The syntax should be %lexicon(moduleName:keyName)% or %lexicon(moduleName:keyName:formatString1:formatString2)%. Returns: Type Promise Example lexjs.format(\"%lexicon(bajaui:dialog.ok)% and %lexicon(bajaui:menu.new.label)%\") .then(function (str) { // Prints: \"OK and New\" console.log(str); }); lexjs.format(\"%lexicon(bajaui:fileSearch.scanningFiles:arg1:arg2)%\") .then(function (str) { // Prints: \"Scanning files (found arg1 of arg2)...\" console.log(str); }); &lt;static&gt; getLexiconFromCache(moduleName) If the Lexicon is loaded and cached then return it. Otherwise return null. Please note, this will not result in any network calls. Parameters: Name Type Description moduleName String The name of the module. Returns: The Lexicon or null if it can't be found. Type Lexicon &lt;static&gt; module(moduleName) Asynchronously resolve a Lexicon via module name. A promise is returned and resolved once Lexicon has been found. If the Lexicon can't be found or there's a network error, the promise will reject. Parameters: Name Type Description moduleName String the name of the module being requested. Returns: Type Promise Example Access a Lexicon via its module name lexjs.module(\"myModule\") .then(function (lex) { // Access the Lexicon entry called 'foo' from 'myModule' console.log(lex.get(\"foo\")); }); × Search results Close "},"module-nmodule_js_rc_lex_lex-Lexicon.html":{"id":"module-nmodule_js_rc_lex_lex-Lexicon.html","title":"Class: Lexicon","body":" js Modules dialogslexlognmodule/js/rc/csrf/csrfUtilnmodule/js/rc/jasmine/promiseUtilsnmodule/js/rc/lex/lexnmodule/js/rc/log/Levelnmodule/js/rc/log/Log Classes dialogs~Dialognmodule/js/rc/lex/lex~Lexiconnmodule/js/rc/log/Log.Level Class: Lexicon nmodule/js/rc/lex/lex~ Lexicon new Lexicon(moduleName, data) A Lexicon is a map of locale specific name/value pairs for a module. An instance of a Lexicon can be accessed indirectly by use of the module method. Parameters: Name Type Description moduleName String The name of the Niagara Module this Lexicon relates too. data Object An object contained key values pairs for the Lexicon. Example Access a module's Lexicon lexjs.module(\"js\") .then(function (lex) { console.log(\"Some text from a lexicon: \" + lex.get(\"dialogs.ok\")); }); Methods get(obj) Return a value from the Lexicon for a given key. The argument for this method can be either a String key followed by arguments or an Object Literal. Parameters: Name Type Description obj Object | String the Object Literal that contains the method's arguments or a String key. Properties Name Type Description key String the key to look up. def String the default value to return if the key can't be found. By default this is null. args Array | String arguments used for String formatting. If the first parameter is a String key, this list can just be further arguments for the function. Returns: the value for the Lexicon or return def if can't be found. Type String Examples Access a Lexicon value via its key name. lexjs.module(\"js\") .then(function (lex) { console.log(lex.get(\"dialogs.ok\")); }); Access a Lexicon value via its key name with some formatted parameters. lexjs.module(\"bajaui\") .then(function (lex) { var val = lex.get(\"fileSearch.scanningFiles\", \"alpha\", \"omega\")) // Prints out: Scanning files (found alpha of omega)... console.log(val); })); Provide a default value if the key can't be found and use an Object Literal instead lexjs.module(\"bajaui\") .then(function (lex) { // Use an Object Literal instead of multiple arguments and provide a default value. var val = lex.get({ key: \"fileSearch.scanningFiles\", def: \"Return this if the key can't be found in the Lexicon\", args: [\"alpha\", \"omega\"] }); console.log(val); }); getModuleName() Return the Lexicon's module name. Returns: Type String Example Return a Lexicon's module name lexjs.module(\"bajaui\") .then(function (lex) { // Prints out: bajaui console.log(lex.getModuleName()); })); getRaw(obj) Return the raw and unescaped value of the key which is not safe to display. Parameters: Name Type Description obj Object | String the Object Literal that contains the method's arguments or a String key. Properties Name Type Description key String the key to look up. def String the default value to return if the key can't be found. By default this is null. args Array | String arguments used for String formatting. If the first parameter is a String key, this list can just be further arguments for the function. Since: Niagara 4.8 See: Lexicon.get Returns: Type String getSafe(obj) Return escaped value of the key which is safe to display. Parameters: Name Type Description obj Object | String the Object Literal that contains the method's arguments or a String key. Properties Name Type Description key String the key to look up. def String the default value to return if the key can't be found. By default this is null. args Array | String arguments used for String formatting. If the first parameter is a String key, this list can just be further arguments for the function. Since: Niagara 4.8 See: Lexicon.get Returns: Type String × Search results Close "},"module-nmodule_js_rc_log_Level.html":{"id":"module-nmodule_js_rc_log_Level.html","title":"Module: nmodule/js/rc/log/Level","body":" js Modules dialogslexlognmodule/js/rc/csrf/csrfUtilnmodule/js/rc/jasmine/promiseUtilsnmodule/js/rc/lex/lexnmodule/js/rc/log/Levelnmodule/js/rc/log/Log Classes dialogs~Dialognmodule/js/rc/lex/lex~Lexiconnmodule/js/rc/log/Log.Level Module: nmodule/js/rc/log/Level API Status: Private × Search results Close "},"module-nmodule_js_rc_log_Log.html":{"id":"module-nmodule_js_rc_log_Log.html","title":"Module: nmodule/js/rc/log/Log","body":" js Modules dialogslexlognmodule/js/rc/csrf/csrfUtilnmodule/js/rc/jasmine/promiseUtilsnmodule/js/rc/lex/lexnmodule/js/rc/log/Levelnmodule/js/rc/log/Log Classes dialogs~Dialognmodule/js/rc/lex/lex~Lexiconnmodule/js/rc/log/Log.Level Module: nmodule/js/rc/log/Log new (require(\"nmodule/js/rc/log/Log\"))() Class for logging messages throughout Niagara JS apps. Do not instantiate this class directly: rather use the getLogger() function. Logs support SLF4J-style parameterization. See example. See: module:log Examples Supports SLF4J-style format anchors. return Log.getLogger('my.package.name') .then(function (log) { return log.log(Log.Level.INFO, 'foo was {} and bar was {}', 'foo', 'bar'); }); Supports a trailing Error argument. doSomethingAsync() .catch(function (err) { return Log.logMessage('my.package.name', Log.Level.SEVERE, '{} rejected with error', 'doSomethingAsync', err); }); Has convenience methods for behaving like the console. define(['nmodule/js/rc/log/Log'], function (console) { //Note that all of these create and return Promises behind the scenes. //The log name will be browser.console. console.log('this logs at', 'FINE', 'level'); console.info('this logs at', 'INFO', 'level'); console.warn('this logs at', 'WARNING', 'level'); console.error('this logs at', 'SEVERE', 'level'); }); Classes Level Methods &lt;static&gt; error() Logs a message to the browser.console log at SEVERE level. This matches a browser's console.error API. Returns: Type Promise Example Log.error('this', 'is', 'an', 'error', 'message'); &lt;static&gt; getLogger(name) Resolve a Log instance with the given name. Parameters: Name Type Description name string name for the log to retrieve. Calling getLogger() twice for the same name will resolve the same Log instance. Returns: Type Promise.&lt;module:nmodule/js/rc/log/Log&gt; &lt;static&gt; info() Logs a message to the browser.console log at INFO level. This matches a browser's console.info API. Returns: Type Promise Example Log.info('this', 'is', 'an', 'info', 'message'); &lt;static&gt; log() Logs a message to the browser.console log at FINE level. This matches a browser's console.log API. Returns: Type Promise Example Log.log('this', 'is', 'a', 'fine', 'message'); &lt;static&gt; logMessage(name, level, msg [, args]) Convenience method to stop you from having to resolve the Log.getLogger() promise every time you wish to log a message. This will combine the lookup and log into one step. Note that you cannot perform an isLoggable() check with this method, so if your log message is expensive to generate, you may want to fully resolve the logger first. Parameters: Name Type Argument Description name string level module:nmodule/js/rc/log/Log.Level msg String log message args * &lt;optional&gt; &lt;repeatable&gt; additional arguments to use for parameterization Returns: promise to be resolved when the message has been logged Type Promise &lt;static&gt; warn() Logs a message to the browser.console log at WARNING level. This matches a browser's console.warn API. Returns: Type Promise Example Log.warn('this', 'is', 'a', 'warning', 'message'); addHandler(handler) Add a new handler to this Log. The same handler instance cannot be added to the same log more than once. Parameters: Name Type Description handler module:nmodule/js/rc/log/Log~Handler config(msg [, args]) Logs the given message with level CONFIG. Parameters: Name Type Argument Description msg string log message args * &lt;optional&gt; &lt;repeatable&gt; additional arguments to use for parameterization See: module:nmodule/js/rc/log/Log#log Returns: Type Promise fine(msg [, args]) Logs the given message with level FINE. Parameters: Name Type Argument Description msg string log message args * &lt;optional&gt; &lt;repeatable&gt; additional arguments to use for parameterization See: module:nmodule/js/rc/log/Log#log Returns: Type Promise finer(msg [, args]) Logs the given message with level FINER. Parameters: Name Type Argument Description msg string log message args * &lt;optional&gt; &lt;repeatable&gt; additional arguments to use for parameterization See: module:nmodule/js/rc/log/Log#log Returns: Type Promise finest(msg [, args]) Logs the given message with level FINEST. Parameters: Name Type Argument Description msg string log message args * &lt;optional&gt; &lt;repeatable&gt; additional arguments to use for parameterization See: module:nmodule/js/rc/log/Log#log Returns: Type Promise getLevel() Get the level configured for this log. Returns: Type module:nmodule/js/rc/log/Log.Level getName() Get the log's name. Returns: Type string info(msg [, args]) Logs the given message with level INFO. Parameters: Name Type Argument Description msg string log message args * &lt;optional&gt; &lt;repeatable&gt; additional arguments to use for parameterization See: module:nmodule/js/rc/log/Log#log Returns: Type Promise isLoggable(level) Return true if a log message at the given log level will actually be logged by this logger. Use this to improve performance if a log message would be expensive to create. Parameters: Name Type Description level module:nmodule/js/rc/log/Log.Level | string Returns: Type boolean log(level, msg [, args]) Log the given message, giving all Handlers attached to this Log a chance to publish it. Parameters: Name Type Argument Description level module:nmodule/js/rc/log/Log.Level | string Log level object, or the name of it as a string msg String log message args * &lt;optional&gt; &lt;repeatable&gt; additional arguments to use for parameterization. If the final argument is an Error, it will be logged by itself. Returns: promise to be resolved when all handlers are finished publishing Type Promise Example const name = promptForName(); log.log(Level.FINE, 'Hello, {}!', name); try { doSomething(); } catch (err) { log.log('SEVERE', 'An error occurred at {}', new Date(), err); } setLevel(level) Sets the logging level configured on this Log instance. Parameters: Name Type Description level module:nmodule/js/rc/log/Log.Level severe(msg [, args]) Logs the given message with level SEVERE. Parameters: Name Type Argument Description msg string log message args * &lt;optional&gt; &lt;repeatable&gt; additional arguments to use for parameterization See: module:nmodule/js/rc/log/Log#log Returns: Type Promise time(id [, level] [, msg] [, args]) Starts timing a particular operation. Parameters: Name Type Argument Default Description id string a \"unique\" ID to describe this operation. level module:nmodule/js/rc/log/Log.Level | string &lt;optional&gt; INFO Log level object, or the name of it as a string msg string &lt;optional&gt; a message to log. If omitted, will simply log the ID args * &lt;optional&gt; &lt;repeatable&gt; additional arguments to format the message Throws: if ID not provided Type Error Returns: a ticket that may be passed to timeEnd Type number timeEnd( [id] [, msg] [, args]) Stops timing a particular operation. Parameters: Name Type Argument Default Description id string | number &lt;optional&gt; the ID passed to time() (if duplicates are found, this will stop the least recently started timer). Or, pass the exact ticket returned from time(). If omitted, this call will be a no-op (this makes it safe to wrap time() calls in isLoggable checks). msg string &lt;optional&gt; \"id: {}ms\" a message to log. For format arguments, the number of milliseconds elapsed is always the last. args * &lt;optional&gt; &lt;repeatable&gt; any additional arguments to use to format the message remember elapsed time will always be added warning(msg [, args]) Logs the given message with level WARNING. Parameters: Name Type Argument Description msg string log message args * &lt;optional&gt; &lt;repeatable&gt; additional arguments to use for parameterization See: module:nmodule/js/rc/log/Log#log Returns: Type Promise Type Definitions Handler Responsible for actually publishing a log message to some destination (console, baja.outln, logfile, etc). Type: Object Properties: Name Type Description publish module:nmodule/js/rc/log/Log~PublishCallback implements how log messages will be handled PublishCallback(name, level, msg) When adding a custom Handler, implement the publish function to define how log messages will be handled. Parameters: Name Type Description name string log name level module:nmodule/js/rc/log/Log.Level log level msg string an already fully-formatted log message. Returns: Type Promise × Search results Close "},"module-nmodule_js_rc_log_Log.Level.html":{"id":"module-nmodule_js_rc_log_Log.Level.html","title":"Class: Level","body":" js Modules dialogslexlognmodule/js/rc/csrf/csrfUtilnmodule/js/rc/jasmine/promiseUtilsnmodule/js/rc/lex/lexnmodule/js/rc/log/Levelnmodule/js/rc/log/Log Classes dialogs~Dialognmodule/js/rc/lex/lex~Lexiconnmodule/js/rc/log/Log.Level Class: Level nmodule/js/rc/log/Log. Level new Level(name, value) Describes a logging level. This constructor not to be invoked directly - use one of the constants like SEVERE instead. Parameters: Name Type Description name string value number Members &lt;static&gt; ALL All logging output. &lt;static&gt; CONFIG Configuration. &lt;static&gt; FINE Detailed tracing info. &lt;static&gt; FINER Highly detailed tracing info. &lt;static&gt; FINEST Ludicrously detailed tracing info. &lt;static&gt; INFO Informational. &lt;static&gt; OFF No logging. &lt;static&gt; SEVERE Major error condition. &lt;static&gt; WARNING Potential problem. Methods getName() Returns: Level name × Search results Close "}}
    </script>

    <script type="text/javascript">
        $(document).ready(function() {
            Searcher.init();
        });

        $(window).on("message", function(msg) {
            var msgData = msg.originalEvent.data;

            if (msgData.msgid != "docstrap.quicksearch.start") {
                return;
            }

            var results = Searcher.search(msgData.searchTerms);

            window.parent.postMessage({"results": results, "msgid": "docstrap.quicksearch.done"}, "*");
        });
    </script>
</body>
</html>
