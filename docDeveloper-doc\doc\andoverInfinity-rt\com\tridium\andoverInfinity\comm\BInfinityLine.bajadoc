<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.BInfinityLine" name="BInfinityLine" packageName="com.tridium.andoverInfinity.comm" public="true">
<description>
BInfinityLine is used to pass line/format info from the screen buffer to&#xa; the workbench terminal view
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="javax.baja.sys.BVector"/>
</extends>
<property name="previousLineNumber" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;previousLineNumber&lt;/code&gt; property.
</description>
<tag name="@see">#getPreviousLineNumber</tag>
<tag name="@see">#setPreviousLineNumber</tag>
</property>

<property name="previousColumnNumber" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;previousColumnNumber&lt;/code&gt; property.
</description>
<tag name="@see">#getPreviousColumnNumber</tag>
<tag name="@see">#setPreviousColumnNumber</tag>
</property>

<property name="newLineNumber" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;newLineNumber&lt;/code&gt; property.
</description>
<tag name="@see">#getNewLineNumber</tag>
<tag name="@see">#setNewLineNumber</tag>
</property>

<property name="newColumnNumber" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;newColumnNumber&lt;/code&gt; property.
</description>
<tag name="@see">#getNewColumnNumber</tag>
<tag name="@see">#setNewColumnNumber</tag>
</property>

<property name="lineText" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;lineText&lt;/code&gt; property.
</description>
<tag name="@see">#getLineText</tag>
<tag name="@see">#setLineText</tag>
</property>

<property name="lineFormat" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;lineFormat&lt;/code&gt; property.
</description>
<tag name="@see">#getLineFormat</tag>
<tag name="@see">#setLineFormat</tag>
</property>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine() -->
<constructor name="BInfinityLine" public="true">
<description>
Default constructor sets lines/col&#x27;s to 0 and text/format buffers to empty string
</description>
</constructor>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine(int, int, int, int, java.lang.String, java.lang.String) -->
<constructor name="BInfinityLine" public="true">
<parameter name="oldLine">
<type class="int"/>
<description/>
</parameter>
<parameter name="oldCol">
<type class="int"/>
<description/>
</parameter>
<parameter name="newLine">
<type class="int"/>
<description/>
</parameter>
<parameter name="newCol">
<type class="int"/>
<description/>
</parameter>
<parameter name="contents">
<type class="java.lang.String"/>
<description/>
</parameter>
<parameter name="format">
<type class="java.lang.String"/>
<description/>
</parameter>
<description>
Constructor to set old, new cursor location and new contents and format
</description>
</constructor>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.getPreviousLineNumber() -->
<method name="getPreviousLineNumber"  public="true">
<description>
Get the &lt;code&gt;previousLineNumber&lt;/code&gt; property.
</description>
<tag name="@see">#previousLineNumber</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.setPreviousLineNumber(int) -->
<method name="setPreviousLineNumber"  public="true">
<description>
Set the &lt;code&gt;previousLineNumber&lt;/code&gt; property.
</description>
<tag name="@see">#previousLineNumber</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.getPreviousColumnNumber() -->
<method name="getPreviousColumnNumber"  public="true">
<description>
Get the &lt;code&gt;previousColumnNumber&lt;/code&gt; property.
</description>
<tag name="@see">#previousColumnNumber</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.setPreviousColumnNumber(int) -->
<method name="setPreviousColumnNumber"  public="true">
<description>
Set the &lt;code&gt;previousColumnNumber&lt;/code&gt; property.
</description>
<tag name="@see">#previousColumnNumber</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.getNewLineNumber() -->
<method name="getNewLineNumber"  public="true">
<description>
Get the &lt;code&gt;newLineNumber&lt;/code&gt; property.
</description>
<tag name="@see">#newLineNumber</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.setNewLineNumber(int) -->
<method name="setNewLineNumber"  public="true">
<description>
Set the &lt;code&gt;newLineNumber&lt;/code&gt; property.
</description>
<tag name="@see">#newLineNumber</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.getNewColumnNumber() -->
<method name="getNewColumnNumber"  public="true">
<description>
Get the &lt;code&gt;newColumnNumber&lt;/code&gt; property.
</description>
<tag name="@see">#newColumnNumber</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.setNewColumnNumber(int) -->
<method name="setNewColumnNumber"  public="true">
<description>
Set the &lt;code&gt;newColumnNumber&lt;/code&gt; property.
</description>
<tag name="@see">#newColumnNumber</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.getLineText() -->
<method name="getLineText"  public="true">
<description>
Get the &lt;code&gt;lineText&lt;/code&gt; property.
</description>
<tag name="@see">#lineText</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.setLineText(java.lang.String) -->
<method name="setLineText"  public="true">
<description>
Set the &lt;code&gt;lineText&lt;/code&gt; property.
</description>
<tag name="@see">#lineText</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.getLineFormat() -->
<method name="getLineFormat"  public="true">
<description>
Get the &lt;code&gt;lineFormat&lt;/code&gt; property.
</description>
<tag name="@see">#lineFormat</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.setLineFormat(java.lang.String) -->
<method name="setLineFormat"  public="true">
<description>
Set the &lt;code&gt;lineFormat&lt;/code&gt; property.
</description>
<tag name="@see">#lineFormat</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
Generate a debug string for this object
</description>
<tag name="@see">javax.baja.sys.BObject#toString(javax.baja.sys.Context)</tag>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
context is ignored
</description>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.previousLineNumber -->
<field name="previousLineNumber"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;previousLineNumber&lt;/code&gt; property.
</description>
<tag name="@see">#getPreviousLineNumber</tag>
<tag name="@see">#setPreviousLineNumber</tag>
</field>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.previousColumnNumber -->
<field name="previousColumnNumber"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;previousColumnNumber&lt;/code&gt; property.
</description>
<tag name="@see">#getPreviousColumnNumber</tag>
<tag name="@see">#setPreviousColumnNumber</tag>
</field>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.newLineNumber -->
<field name="newLineNumber"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;newLineNumber&lt;/code&gt; property.
</description>
<tag name="@see">#getNewLineNumber</tag>
<tag name="@see">#setNewLineNumber</tag>
</field>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.newColumnNumber -->
<field name="newColumnNumber"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;newColumnNumber&lt;/code&gt; property.
</description>
<tag name="@see">#getNewColumnNumber</tag>
<tag name="@see">#setNewColumnNumber</tag>
</field>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.lineText -->
<field name="lineText"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lineText&lt;/code&gt; property.
</description>
<tag name="@see">#getLineText</tag>
<tag name="@see">#setLineText</tag>
</field>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.lineFormat -->
<field name="lineFormat"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lineFormat&lt;/code&gt; property.
</description>
<tag name="@see">#getLineFormat</tag>
<tag name="@see">#setLineFormat</tag>
</field>

<!-- com.tridium.andoverInfinity.comm.BInfinityLine.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
