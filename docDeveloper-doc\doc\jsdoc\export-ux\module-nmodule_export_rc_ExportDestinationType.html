<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>export Module: nmodule/export/rc/ExportDestinationType</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">export</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-nmodule_export_rc_ExportDestinationType.html">nmodule/export/rc/ExportDestinationType</a></li><li><a href="module-nmodule_export_rc_Transformer.html">nmodule/export/rc/Transformer</a></li><li><a href="module-nmodule_export_rc_TransformOperation.html">nmodule/export/rc/TransformOperation</a></li><li><a href="module-nmodule_export_rc_TransformOperationProvider.html">nmodule/export/rc/TransformOperationProvider</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: nmodule/export/rc/ExportDestinationType</h1>
<section>

<header>
    
        
            
                <div class="class-description"><p>API Status: <strong>Development</strong></p>
<p>A destination type describes where a transform operation may send its output.<br>
Examples include: the file system; the clipboard; the cloud; an external<br>
application.</p>
<p>When sending transformed data to a destination, there are three touchpoints<br>
in that workflow:</p>
<ul>
<li><code>checkValid()</code>: does this destination even work? Example: a destination<br>
that sends data to the clipboard is not valid in a browser that does not<br>
support the Clipboard API, so don't even present it to the user as an<br>
option.</li>
<li><code>prepare()</code>: does this destination work for the transform as requested<br>
by the user? Example: a destination that writes to the file system would<br>
prompt the user to make sure it's okay to overwrite a particular file.</li>
<li><code>transform()</code>: All systems go - write the data.</li>
</ul></div>
            
        
    
</header>


<article>
    <div class="container-overview">
    
        

        
            
<hr>
<dt>
    <h4 class="name" id="module:nmodule/export/rc/ExportDestinationType"><span class="type-signature">&lt;abstract> </span>new (require("nmodule/export/rc/ExportDestinationType"))()</h4>
    
    
</dt>
<dd>

    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id="checkValid"><span class="type-signature"></span>checkValid()</h4>
    
    
</dt>
<dd>

    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    
    <h5>Throws:</h5>
    
            

<dl>
    <dt>
        <div class="param-desc">
        <p>if this destination is not valid in the current environment</p>
        </div>
    </dt>
    <dt>
        <dl>
            <dt>
                Type
            </dt>
            <dd>
                
<span class="param-type">Error</span>



            </dd>
        </dl>
    </dt>
</dl>


        

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getDestinationConfig"><span class="type-signature"></span>getDestinationConfig(transformOp)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>When invoking a transform operation using the Export Dialog, options can<br>
be provided by the user to configure how to send the transformed data to<br>
its destination.</p>
<p>If this destination should provide user-configurable options, override<br>
this function to provide them in the form of a <code>Component</code>. This<br>
<code>Component</code> will be shown to the user in a Property Sheet for<br>
configuration.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>transformOp</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_export_rc_TransformOperation.html">module:nmodule/export/rc/TransformOperation</a></span>



            
            </td>

            

            

            <td class="description last"><p>The transform operation this destination type is a target for</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The destination config object.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">baja.Component</span>
|

<span class="param-type">Promise.&lt;(baja.Component|undefined)></span>
|

<span class="param-type">undefined</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getDestinationContextObject"><span class="type-signature"></span>getDestinationContextObject(config)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>When the user invokes the transform, the <code>Component</code> as edited by the<br>
user will need to be converted to a context object to be used in the<br>
<code>transform()</code> method. This function provides a hook to perform extra<br>
processing during that conversion.</p>
<p>By default, will return a simple mapping of the <code>Component</code>'s slot names<br>
to their values.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>config</code></td>
            

            <td class="type">
            
                
<span class="param-type">baja.Component</span>



            
            </td>

            

            

            <td class="description last"><p>The destination config component as edited by the user</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>the context object</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">object</span>
|

<span class="param-type">Promise.&lt;object></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getDisplayName"><span class="type-signature">&lt;abstract> </span>getDisplayName()</h4>
    
    
</dt>
<dd>

    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>display name for the destination</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">string</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="prepare"><span class="type-signature"></span>prepare(transformOp, cx)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Perform any necessary preparation before the actual transform. This is a<br>
hook for things like user prompts (&quot;do you want to overwrite this file?&quot;,<br>
etc). By default, simply returns true.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>transformOp</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_export_rc_TransformOperation.html">module:nmodule/export/rc/TransformOperation</a></span>



            
            </td>

            

            

            <td class="description last"><p>the transform operation</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>cx</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>



            
            </td>

            

            

            <td class="description last"><p>the export context</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>return or resolve <code>true</code> to indicate<br>
that the transform should proceed. Return <code>false</code> to indicate that the<br>
transform was canceled (either by the user, or conditions simply do not<br>
support this transform/destination). Throw or reject only in case of an<br>
unexpected error condition.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise.&lt;boolean></span>
|

<span class="param-type">boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="transform"><span class="type-signature">&lt;abstract> </span>transform(transformOp, cx)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Perform the entire transformation operation: execute the transform<br>
to obtain the transformed data, and send that data to this destination.</p>
<p><em>Important note</em>: this method should not require any user interaction.<br>
It should only be data-in to data-out. Any user prompts should be<br>
completed in <code>prepare()</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>transformOp</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_export_rc_TransformOperation.html">module:nmodule/export/rc/TransformOperation</a></span>



            
            </td>

            

            

            <td class="description last"><p>the transform operation</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>cx</code></td>
            

            <td class="type">
            
                
<span class="param-type">object</span>



            
            </td>

            

            

            <td class="description last"><p>the export context</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>
|

<span class="param-type">*</span>



    </dd>
</dl>


        

    
</dd>

        </dl>
    

    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	export Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:56+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>