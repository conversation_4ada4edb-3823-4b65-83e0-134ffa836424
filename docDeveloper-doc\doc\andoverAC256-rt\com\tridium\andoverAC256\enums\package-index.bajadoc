<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="andoverAC256" runtimeProfile="rt" name="com.tridium.andoverAC256.enums">
<description/>
<class packageName="com.tridium.andoverAC256.enums" name="BAndoverAcnetSpeedEnum"><description>BAndoverAcnetSpeedEnum represents the possible baud rate choices&#xa; for the Andover AC256 C-Port.</description></class>
<class packageName="com.tridium.andoverAC256.enums" name="BAndoverConsolePortSpeedEnum"><description>BAndoverConsolePortSpeedEnum represents the possible baud rate choices&#xa; for the Andover AC256 C-Port.</description></class>
<class packageName="com.tridium.andoverAC256.enums" name="BAndoverDeviceTypeEnum"><description>Arbitrary enumeration of device type, necessary because&#xa; the AC256 and AC8 controllers differ(slightly) in logon&#xa; sequence, and differ in the info contained in the PRINT&#xa; STATUS response, and differ in sub-panels supported (impacts&#xa; the learn process)</description></class>
<class packageName="com.tridium.andoverAC256.enums" name="BAndoverLBusSpeedEnum"><description>BAndoverLBusSpeedEnum represents the possible baud rate choices&#xa; for the Andover AC256 C-Port.</description></class>
<class packageName="com.tridium.andoverAC256.enums" name="BAndoverPromptEnum"><description>Arbitrary enumeration of context states possible on the AC256&#xa; or AC8 controllers.</description></class>
<class packageName="com.tridium.andoverAC256.enums" name="BAndoverReadEnabledStateEnum"><description>BAndoverAcnetSpeedEnum represents the possible baud rate choices&#xa; for the Andover AC256 C-Port.</description></class>
<class packageName="com.tridium.andoverAC256.enums" name="BAndoverSendDisableEnum"><description>BAndoverAcnetSpeedEnum represents the possible baud rate choices&#xa; for the Andover AC256 C-Port.</description></class>
<class packageName="com.tridium.andoverAC256.enums" name="BAndoverXBusSpeedEnum"><description>BAndoverXBusSpeedEnum represents the possible baud rate choices&#xa; for the Andover AC256 C-Port.</description></class>
</package>
</bajadoc>
