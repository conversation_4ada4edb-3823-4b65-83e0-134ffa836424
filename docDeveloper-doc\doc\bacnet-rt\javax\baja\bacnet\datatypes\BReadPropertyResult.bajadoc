<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BReadPropertyResult" name="BReadPropertyResult" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
This class represents the ReadPropertyResult sequence.&#xa; This is not the class used for handling ReadPropertyMultiple&#xa; messages during polling.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">31 May 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</implements>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="propertyId" flags="r">
<type class="int"/>
<description>
Slot for the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyId</tag>
<tag name="@see">#setPropertyId</tag>
</property>

<property name="propertyArrayIndex" flags="r">
<type class="int"/>
<description>
Slot for the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyArrayIndex</tag>
<tag name="@see">#setPropertyArrayIndex</tag>
</property>

<property name="value" flags="r">
<type class="javax.baja.sys.BValue"/>
<description>
Slot for the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#getValue</tag>
<tag name="@see">#setValue</tag>
</property>

<property name="error" flags="r">
<type class="javax.baja.bacnet.datatypes.BErrorType"/>
<description>
Slot for the &lt;code&gt;error&lt;/code&gt; property.
</description>
<tag name="@see">#getError</tag>
<tag name="@see">#setError</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult() -->
<constructor name="BReadPropertyResult" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult(int) -->
<constructor name="BReadPropertyResult" public="true">
<parameter name="propertyId">
<type class="int"/>
<description>
the property-identifier to be referenced.
</description>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult(int, int) -->
<constructor name="BReadPropertyResult" public="true">
<parameter name="propertyId">
<type class="int"/>
<description>
the property-identifier to be referenced.
</description>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
<description>
the array index.
</description>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.getPropertyId() -->
<method name="getPropertyId"  public="true">
<description>
Get the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#propertyId</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.setPropertyId(int) -->
<method name="setPropertyId"  public="true">
<description>
Set the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#propertyId</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.getPropertyArrayIndex() -->
<method name="getPropertyArrayIndex"  public="true">
<description>
Get the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#propertyArrayIndex</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.setPropertyArrayIndex(int) -->
<method name="setPropertyArrayIndex"  public="true">
<description>
Set the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#propertyArrayIndex</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.getValue() -->
<method name="getValue"  public="true">
<description>
Get the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#value</tag>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.setValue(javax.baja.sys.BValue) -->
<method name="setValue"  public="true">
<description>
Set the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#value</tag>
<parameter name="v">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.getError() -->
<method name="getError"  public="true">
<description>
Get the &lt;code&gt;error&lt;/code&gt; property.
</description>
<tag name="@see">#error</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BErrorType"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.setError(javax.baja.bacnet.datatypes.BErrorType) -->
<method name="setError"  public="true">
<description>
Set the &lt;code&gt;error&lt;/code&gt; property.
</description>
<tag name="@see">#error</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BErrorType"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.getPropertyValue() -->
<method name="getPropertyValue"  public="true">
<description/>
<return>
<type class="byte" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.getPriority() -->
<method name="getPriority"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.getPropertyAccessError() -->
<method name="getPropertyAccessError"  public="true">
<description/>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.getErrorClass() -->
<method name="getErrorClass"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.getErrorCode() -->
<method name="getErrorCode"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.isError() -->
<method name="isError"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.isPropertyArrayIndexUsed() -->
<method name="isPropertyArrayIndexUsed"  public="true">
<description/>
<return>
<type class="boolean"/>
<description>
true if the property array index is used.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
a descriptive string.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.toDebugString() -->
<method name="toDebugString"  public="true">
<description>
Debug string.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.propertyId -->
<field name="propertyId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyId</tag>
<tag name="@see">#setPropertyId</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.propertyArrayIndex -->
<field name="propertyArrayIndex"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyArrayIndex</tag>
<tag name="@see">#setPropertyArrayIndex</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.value -->
<field name="value"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#getValue</tag>
<tag name="@see">#setValue</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.error -->
<field name="error"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;error&lt;/code&gt; property.
</description>
<tag name="@see">#getError</tag>
<tag name="@see">#setError</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.PROPERTY_ID_TAG -->
<field name="PROPERTY_ID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
NReadPropertyResult Asn Context Tags&#xa; See Bacnet Clause 21.
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.PROPERTY_ARRAY_INDEX_TAG -->
<field name="PROPERTY_ARRAY_INDEX_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.PROPERTY_VALUE_TAG -->
<field name="PROPERTY_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BReadPropertyResult.PROPERTY_ACCESS_ERROR_TAG -->
<field name="PROPERTY_ACCESS_ERROR_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
