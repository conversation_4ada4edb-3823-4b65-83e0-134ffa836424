<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetDateRange" name="BBacnetDateRange" packageName="javax.baja.bacnet.datatypes" public="true">
<description>
BBacnetDateRange represents a BacnetDateRange value in a Bacnet property.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">19 Mar 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="startDate" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
<description>
Slot for the &lt;code&gt;startDate&lt;/code&gt; property.
</description>
<tag name="@see">#getStartDate</tag>
<tag name="@see">#setStartDate</tag>
</property>

<property name="endDate" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
<description>
Slot for the &lt;code&gt;endDate&lt;/code&gt; property.
</description>
<tag name="@see">#getEndDate</tag>
<tag name="@see">#setEndDate</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetDateRange() -->
<constructor name="BBacnetDateRange" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetDateRange(javax.baja.bacnet.datatypes.BBacnetDate, javax.baja.bacnet.datatypes.BBacnetDate) -->
<constructor name="BBacnetDateRange" public="true">
<parameter name="startDate">
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
</parameter>
<parameter name="endDate">
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetDateRange.getStartDate() -->
<method name="getStartDate"  public="true">
<description>
Get the &lt;code&gt;startDate&lt;/code&gt; property.
</description>
<tag name="@see">#startDate</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateRange.setStartDate(javax.baja.bacnet.datatypes.BBacnetDate) -->
<method name="setStartDate"  public="true">
<description>
Set the &lt;code&gt;startDate&lt;/code&gt; property.
</description>
<tag name="@see">#startDate</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateRange.getEndDate() -->
<method name="getEndDate"  public="true">
<description>
Get the &lt;code&gt;endDate&lt;/code&gt; property.
</description>
<tag name="@see">#endDate</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateRange.setEndDate(javax.baja.bacnet.datatypes.BBacnetDate) -->
<method name="setEndDate"  public="true">
<description>
Set the &lt;code&gt;endDate&lt;/code&gt; property.
</description>
<tag name="@see">#endDate</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateRange.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateRange.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true" final="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateRange.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true" final="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateRange.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateRange.fromString(java.lang.String) -->
<method name="fromString"  public="true" static="true">
<description>
Read the startDate and endDate values from the&#xa; given String and return a new BBacnetDateRange.
</description>
<parameter name="s">
<type class="java.lang.String"/>
<description>
the input string.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDateRange"/>
<description>
a BBacnetDateRange read from the string.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateRange.startDate -->
<field name="startDate"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;startDate&lt;/code&gt; property.
</description>
<tag name="@see">#getStartDate</tag>
<tag name="@see">#setStartDate</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDateRange.endDate -->
<field name="endDate"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;endDate&lt;/code&gt; property.
</description>
<tag name="@see">#getEndDate</tag>
<tag name="@see">#setEndDate</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDateRange.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
