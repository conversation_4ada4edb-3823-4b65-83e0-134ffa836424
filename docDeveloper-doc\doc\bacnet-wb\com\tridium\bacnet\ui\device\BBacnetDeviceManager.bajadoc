<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="wb" qualifiedName="com.tridium.bacnet.ui.device.BBacnetDeviceManager" name="BBacnetDeviceManager" packageName="com.tridium.bacnet.ui.device" public="true">
<description>
BBacnetDeviceManager is the specialization of BDeviceManager to&#xa; provide a way for the user to discover, create, view, and edit Bacnet&#xa; devices in Niagara.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@author"><PERSON></tag>
<tag name="@creation">23 Oct 03</tag>
<tag name="@version">$Revision: 1$ $Date: 12/18/2003 9:44:58 AM$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.driver.ui.device.BDeviceManager"/>
</extends>
</class>
</bajadoc>
