<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.point.BPupNumericProxyExt" name="BPupNumericProxyExt" packageName="com.tridium.aapup.point" public="true">
<description>
BPupNumericProxyExt is the proxy extension for bringing&#xa; PUP point information into Niagara Numeric Points.
</description>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@creation">7/28/2005 11:25AM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.91</tag>
<extends>
<type class="com.tridium.aapup.point.BPupProxyExt"/>
</extends>
</class>
</bajadoc>
