<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.InfinityUtil" name="InfinityUtil" packageName="com.tridium.andoverInfinity.comm" public="true">
<description/>
<tag name="@author">cturman</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- com.tridium.andoverInfinity.comm.InfinityUtil() -->
<constructor name="InfinityUtil" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.comm.InfinityUtil.addMoveCursorCommands(com.tridium.andoverInfinity.comm.BVt100, java.lang.StringBuffer) -->
<method name="addMoveCursorCommands"  public="true" static="true">
<description>
add cursor move commands to a string buffer.  This assumes the screen&#xa; is already in command line mode.  This first moves the cursor to the &#xa; last non-blank line (if not already there), and then adds commands to&#xa; erase all of the lines.  This is to maintain the screen at the optimum&#xa; state such that the Infinity panel has to transmit as few characters&#xa; as possible in response to a &#x22;print&#x22; command.
</description>
<parameter name="screen">
<type class="com.tridium.andoverInfinity.comm.BVt100"/>
<description>
BVt100 screen buffer
</description>
</parameter>
<parameter name="sb">
<type class="java.lang.StringBuffer"/>
<description>
A StringBuffer used to form up a request
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.InfinityUtil.addChangeDeviceCommands(com.tridium.andoverInfinity.comm.BVt100, java.lang.StringBuffer) -->
<method name="addChangeDeviceCommands"  public="true" static="true">
<description>
Check to see if we are at network level or infinet level in a&#xa; command window.  It is assumed that the mode for the screen&#xa; has already been checked to be command line mode.
</description>
<parameter name="screen">
<type class="com.tridium.andoverInfinity.comm.BVt100"/>
<description>
the VT100 screen buffer
</description>
</parameter>
<parameter name="sb">
<type class="java.lang.StringBuffer"/>
<description>
is a StringBuffer passed in from a message class
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
