<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.point.BBacnetPointDeviceExt" name="BBacnetPointDeviceExt" packageName="javax.baja.bacnet.point" public="true">
<description>
BBacnetPointDeviceExt.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 1$ $Date: 12/19/01 4:32:48 PM$</tag>
<tag name="@creation">17 Dec 2001</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.driver.point.BPointDeviceExt"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BIBacnetObjectContainer"/>
</implements>
<action name="submitPointDiscoveryJob" flags="h">
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;submitPointDiscoveryJob&lt;/code&gt; action.
</description>
<tag name="@see">#submitPointDiscoveryJob()</tag>
</action>

<!-- javax.baja.bacnet.point.BBacnetPointDeviceExt() -->
<constructor name="BBacnetPointDeviceExt" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.point.BBacnetPointDeviceExt.submitPointDiscoveryJob() -->
<method name="submitPointDiscoveryJob"  public="true">
<description>
Invoke the &lt;code&gt;submitPointDiscoveryJob&lt;/code&gt; action.
</description>
<tag name="@see">#submitPointDiscoveryJob</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointDeviceExt.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointDeviceExt.getDeviceType() -->
<method name="getDeviceType"  public="true">
<description>
Get the parent device Type.
</description>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointDeviceExt.getProxyExtType() -->
<method name="getProxyExtType"  public="true">
<description>
Get the Type of proxy extensions for this device.
</description>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointDeviceExt.getPointFolderType() -->
<method name="getPointFolderType"  public="true">
<description>
Get the Type of point folder for this device.
</description>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointDeviceExt.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<description>
BBacnetPointDeviceExt can only be contained in a BBacnetDevice.
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointDeviceExt.doSubmitPointDiscoveryJob(javax.baja.sys.Context) -->
<method name="doSubmitPointDiscoveryJob"  public="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointDeviceExt.lookupBacnetObject(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int, java.lang.String) -->
<method name="lookupBacnetObject"  public="true">
<description/>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="propertyId">
<type class="int"/>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
</parameter>
<parameter name="domain">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointDeviceExt.device() -->
<method name="device"  public="true" final="true">
<description/>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
<description>
the BBacnetDevice containing this BBacnetPointDeviceExt.
</description>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointDeviceExt.findPoint(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int) -->
<method name="findPoint"  public="true" final="true">
<description>
Find a proxy point based on objectId, propertyId,&#xa; and propertyArrayIndex.
</description>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<parameter name="propertyId">
<type class="int"/>
<description/>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
<description/>
</parameter>
<return>
<type class="javax.baja.control.BControlPoint"/>
<description>
the BControlPoint in this point device extension that has a&#xa; BBacnetProxyExt with these parameters, or null if no&#xa; point has these parameters.
</description>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointDeviceExt.findPoints(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="findPoints"  public="true" final="true">
<description>
Find all proxy points with a given objectId.
</description>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<return>
<type class="javax.baja.control.BControlPoint" dimension="1"/>
<description>
an array of BControlPoints in this point device extension that have a&#xa; BBacnetProxyExt with this objectId, or null if no&#xa; point has these parameters.
</description>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointDeviceExt.findPoints(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int) -->
<method name="findPoints"  public="true" final="true">
<description>
Find all proxy points with a given objectId, propertyId and propertyIdx.
</description>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<parameter name="propertyId">
<type class="int"/>
<description/>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
<description/>
</parameter>
<return>
<type class="javax.baja.control.BControlPoint" dimension="1"/>
<description>
an array of BControlPoints in this point device extension that have a&#xa; BBacnetProxyExt with this objectId, propertyId and propertyIdx.&#xa; returns null if no point has these parameters.
</description>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointDeviceExt.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointDeviceExt.getAgents(javax.baja.sys.Context) -->
<method name="getAgents"  public="true">
<description>
Get the agent list.  Remove Device Manager and Network Summary.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.agent.AgentList"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetPointDeviceExt.submitPointDiscoveryJob -->
<field name="submitPointDiscoveryJob"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;submitPointDiscoveryJob&lt;/code&gt; action.
</description>
<tag name="@see">#submitPointDiscoveryJob()</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetPointDeviceExt.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
