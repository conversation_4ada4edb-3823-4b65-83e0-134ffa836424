<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.BServiceScheme$ServiceSession" name="BServiceScheme.ServiceSession" packageName="javax.baja.naming" public="true" interface="true" abstract="true" static="true" innerClass="true" category="interface">
<description>
This interface is implemented by BISessions which &#xa; can lookup a service by type.
</description>
<!-- javax.baja.naming.BServiceScheme.ServiceSession.getService(javax.baja.sys.Type) -->
<method name="getService"  public="true" abstract="true">
<description/>
<parameter name="type">
<type class="javax.baja.sys.Type"/>
</parameter>
<return>
<type class="javax.baja.sys.BComponent"/>
</return>
</method>

</class>
</bajadoc>
