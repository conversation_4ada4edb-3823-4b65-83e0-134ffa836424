<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarmOrion" runtimeProfile="rt" qualifiedName="javax.baja.alarmOrion.BOrionAlarmDatabase" name="BOrionAlarmDatabase" packageName="javax.baja.alarmOrion" public="true">
<description>
An Orion based implementation of an alarm database.
</description>
<tag name="@author"><PERSON> on March 18, 2009</tag>
<extends>
<type class="javax.baja.alarm.BAlarmDatabase"/>
</extends>
<!-- javax.baja.alarmOrion.BOrionAlarmDatabase() -->
<constructor name="BOrionAlarmDatabase" public="true">
<description/>
</constructor>

<!-- javax.baja.alarmOrion.BOrionAlarmDatabase.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmDatabase.isOpen() -->
<method name="isOpen"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmDatabase.doOpen() -->
<method name="doOpen"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmDatabase.doClose() -->
<method name="doClose"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmDatabase.getDbConnection(javax.baja.sys.Context) -->
<method name="getDbConnection"  public="true">
<description/>
<tag name="@since">Niagara 4.0</tag>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.alarm.AlarmDbConnection"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmDatabase.updateConfig(javax.baja.alarm.BAlarmDbConfig, javax.baja.sys.Property) -->
<method name="updateConfig"  public="true">
<description>
Update the database with the new configuration.
</description>
<tag name="@since">Niagara 4.0</tag>
<parameter name="config">
<type class="javax.baja.alarm.BAlarmDbConfig"/>
<description>
new BAlarmDbConfig
</description>
</parameter>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
<description>
Property to update
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.alarm.AlarmException"/>
</throws>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmDatabase.recalculateAlarmClassStatistics() -->
<method name="recalculateAlarmClassStatistics"  public="true">
<description>
Recalculate the alarm statistics for each alarm class.  This is a very expensive operation&#xa; on large databases because it involves recalculating based off of the state of each alarm&#xa; in the database.  This should only be done on a separate thread.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmDatabase.toNormal(javax.baja.alarm.BAlarmRecord) -->
<method name="toNormal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="alarmRecord">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmDatabase.fw(int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object) -->
<method name="fw"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
TODO Update the database with the defined capacity, taken from the service. (see 17750)
</description>
<parameter name="x">
<type class="int"/>
</parameter>
<parameter name="a">
<type class="java.lang.Object"/>
</parameter>
<parameter name="b">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c">
<type class="java.lang.Object"/>
</parameter>
<parameter name="d">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="java.lang.Object"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmDatabase.getStatus() -->
<method name="getStatus"  public="true">
<description>
Get the status of the Rdbms that this application is configured to use.
</description>
<return>
<type class="javax.baja.status.BStatus"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmDatabase.getOrionDatabase() -->
<method name="getOrionDatabase"  public="true">
<description>
Get the orion database to be used by this application.
</description>
<return>
<type class="com.tridium.orion.BOrionDatabase"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmDatabase.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmDatabase.exclusiveAccessMutex -->
<field name="exclusiveAccessMutex"  public="true" final="true">
<type class="java.lang.Object"/>
<description/>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmDatabase.open -->
<field name="open"  public="true">
<type class="boolean"/>
<description/>
</field>

</class>
</bajadoc>
