<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.datatypes.BTrendEvent" name="BTrendEvent" packageName="com.tridium.bacnet.datatypes" public="true" final="true">
<description>
BTrendEvent is the wrapper class for expressing trend log events&#xa; (such as failures, time changes, log status) as Java primitive&#xa; long objects.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 1$ $Date: 9/4/03 1:32:15 PM$</tag>
<tag name="@creation">4 Sep 03</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BSimple"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
</class>
</bajadoc>
