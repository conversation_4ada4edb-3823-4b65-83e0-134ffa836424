<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.io.RandomAccessFileOutputStream" name="RandomAccessFileOutputStream" packageName="javax.baja.io" public="true">
<description>
RandomAccessFileOutputStream is an output stream for writing to&#xa; a RandomAccessFile.  It manages the file pointer for the&#xa; RandomAccessFile internally so that file pointer is always in&#xa; the correct position for writing with respect to the output stream.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">10 Oct 2002</tag>
<tag name="@version">$Revision: 1$ $Date: 10/11/02 2:12:47 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="java.io.OutputStream"/>
</extends>
<!-- javax.baja.io.RandomAccessFileOutputStream(java.io.RandomAccessFile) -->
<constructor name="RandomAccessFileOutputStream" public="true">
<parameter name="out">
<type class="java.io.RandomAccessFile"/>
</parameter>
<description>
Construct an output stream for the specified file.
</description>
</constructor>

<!-- javax.baja.io.RandomAccessFileOutputStream(java.io.RandomAccessFile, long) -->
<constructor name="RandomAccessFileOutputStream" public="true">
<parameter name="out">
<type class="java.io.RandomAccessFile"/>
</parameter>
<parameter name="initFp">
<type class="long"/>
</parameter>
<description>
Construct an output stream for the specified file.
</description>
</constructor>

<!-- javax.baja.io.RandomAccessFileOutputStream.close() -->
<method name="close"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
This method DOES NOT actually close the underlying file.  It is&#xa; assumed that if this class is being used, the file should not&#xa; be closed.  If this is not the case, a normal FileOutputStream&#xa; can likely be used instead.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.RandomAccessFileOutputStream.flush() -->
<method name="flush"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<tag name="@see">OutputStream#flush()</tag>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.RandomAccessFileOutputStream.seek(long) -->
<method name="seek"  public="true">
<description/>
<tag name="@see">java.io.RandomAccessFile#seek(long)</tag>
<parameter name="fp">
<type class="long"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.RandomAccessFileOutputStream.write(int) -->
<method name="write"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<tag name="@see">OutputStream#write(int)</tag>
<parameter name="b">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.RandomAccessFileOutputStream.write(byte[]) -->
<method name="write"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<tag name="@see">OutputStream#write(byte[])</tag>
<parameter name="b">
<type class="byte" dimension="1"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.RandomAccessFileOutputStream.write(byte[], int, int) -->
<method name="write"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<tag name="@see">OutputStream#write(byte[], int, int)</tag>
<parameter name="b">
<type class="byte" dimension="1"/>
</parameter>
<parameter name="offset">
<type class="int"/>
</parameter>
<parameter name="len">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

</class>
</bajadoc>
