<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.ui.BAlarmDialog" name="BAlarmDialog" packageName="com.tridium.alarm.ui" public="true">
<description>
BAlarmDialog
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">21 Oct 03</tag>
<tag name="@version">$Revision: 28$ $Date: 11/1/10 3:47:00 PM EDT$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="javax.baja.ui.BFrame"/>
</extends>
<action name="update" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;update&lt;/code&gt; action.
</description>
<tag name="@see">#update()</tag>
</action>

<action name="handleHyperlink" flags="h">
<parameter name="parameter">
<type class="javax.baja.ui.event.BMouseEvent"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;handleHyperlink&lt;/code&gt; action.
</description>
<tag name="@see">#handleHyperlink(BMouseEvent parameter)</tag>
</action>

</class>
</bajadoc>
