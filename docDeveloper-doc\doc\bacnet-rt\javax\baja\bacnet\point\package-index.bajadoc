<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="javax.baja.bacnet.point">
<description/>
<class packageName="javax.baja.bacnet.point" name="BBacnetBooleanProxyExt"><description>BBacnetBooleanProxyExt maps a BACnet value to a BooleanPoint.</description></class>
<class packageName="javax.baja.bacnet.point" name="BBacnetEnumProxyExt"><description>BBacnetEnumProxyExt handles the point configuration&#xa; of a point of type BOOLEAN, UNSIGNED, or ENUMERATED&#xa; in a Bacnet device.</description></class>
<class packageName="javax.baja.bacnet.point" name="BBacnetNumericProxyExt"><description>BBacnetNumericProxyExt handles the point configuration&#xa; of a point of type UNSIGNED, INTEGER, REAL, DOUBLE, or ENUMERATED&#xa; in a Bacnet device.</description></class>
<class packageName="javax.baja.bacnet.point" name="BBacnetPointDeviceExt"><description>BBacnetPointDeviceExt.</description></class>
<class packageName="javax.baja.bacnet.point" name="BBacnetPointFolder"><description>BBacnetPointFolder.</description></class>
<class packageName="javax.baja.bacnet.point" name="BBacnetProxyExt"><description>BBacnetProxyExt contains all of the information necessary&#xa; to access a data value from a Bacnet device.</description></class>
<class packageName="javax.baja.bacnet.point" name="BBacnetStringProxyExt"><description>BBacnetStringProxyExt handles the point configuration&#xa; of a point of generic type in a Bacnet device.</description></class>
<class packageName="javax.baja.bacnet.point" name="BBacnetTuningPolicy"><description>BBacnetTuningPolicy.</description></class>
<class packageName="javax.baja.bacnet.point" name="BBacnetTuningPolicyMap"><description>BBacnetTuningPolicyMap.</description></class>
<class packageName="javax.baja.bacnet.point" name="PointCmd"/>
</package>
</bajadoc>
