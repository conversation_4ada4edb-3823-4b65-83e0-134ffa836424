<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnetAws" runtimeProfile="rt" name="com.tridium.bacnetAws.job">
<description/>
<class packageName="com.tridium.bacnetAws.job" name="BBackupJob"><description>BBackupJob backs up the configuration in a BacnetWsDevice.</description></class>
<class packageName="com.tridium.bacnetAws.job" name="BBacnetAwsDiscoverTrendLogsJob"><description>BBacnetAwsDiscoverTrendLogsJob augments BBacnetDiscoverTrendLogsJob to also&#xa; handle EventLogs.</description></class>
<class packageName="com.tridium.bacnetAws.job" name="BBacnetCreateObjectJob"><description>BBacnetCreateObjectJob is used to create objects in a Bacnet device.</description></class>
<class packageName="com.tridium.bacnetAws.job" name="BBacnetDeleteObjectJob"><description>BBacnetCreateObjectJob is used to create objects in a Bacnet device.</description></class>
<class packageName="com.tridium.bacnetAws.job" name="BDeviceCommControlJob"><description>BDeviceCommControlJob enables or disables communications in a  BacnetWsDevice.</description></class>
<class packageName="com.tridium.bacnetAws.job" name="BReinitializeDeviceJob"><description>BReinitializeDeviceJob reinitializes a BacnetWsDevice.</description></class>
<class packageName="com.tridium.bacnetAws.job" name="BRestoreJob"><description>BRestoreJob restores the configuration in a BacnetWsDevice.</description></class>
</package>
</bajadoc>
