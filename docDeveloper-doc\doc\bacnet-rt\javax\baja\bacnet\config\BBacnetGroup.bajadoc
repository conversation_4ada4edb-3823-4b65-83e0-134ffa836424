<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BBacnetGroup" name="BBacnetGroup" packageName="javax.baja.bacnet.config" public="true">
<description/>
<tag name="@author"><PERSON>mill</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">26 Jul 2005</tag>
<tag name="@since">Niagara 3.1</tag>
<extends>
<type class="javax.baja.bacnet.BBacnetObject"/>
</extends>
<property name="listOfGroupMembers" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
<description>
Slot for the &lt;code&gt;listOfGroupMembers&lt;/code&gt; property.
</description>
<tag name="@see">#getListOfGroupMembers</tag>
<tag name="@see">#setListOfGroupMembers</tag>
</property>

<property name="presentValue" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</property>

<!-- javax.baja.bacnet.config.BBacnetGroup() -->
<constructor name="BBacnetGroup" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetGroup.getListOfGroupMembers() -->
<method name="getListOfGroupMembers"  public="true">
<description>
Get the &lt;code&gt;listOfGroupMembers&lt;/code&gt; property.
</description>
<tag name="@see">#listOfGroupMembers</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetGroup.setListOfGroupMembers(javax.baja.bacnet.datatypes.BBacnetListOf) -->
<method name="setListOfGroupMembers"  public="true">
<description>
Set the &lt;code&gt;listOfGroupMembers&lt;/code&gt; property.
</description>
<tag name="@see">#listOfGroupMembers</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetGroup.getPresentValue() -->
<method name="getPresentValue"  public="true">
<description>
Get the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#presentValue</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetGroup.setPresentValue(javax.baja.bacnet.datatypes.BBacnetListOf) -->
<method name="setPresentValue"  public="true">
<description>
Set the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#presentValue</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetGroup.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetGroup.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetGroup.getPresentValueProperty() -->
<method name="getPresentValueProperty"  public="true">
<description>
Subclasses that have a present value property should&#xa; override this method and return this property.  The&#xa; default returns null.
</description>
<return>
<type class="javax.baja.sys.Property"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetGroup.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetGroup.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetGroup.listOfGroupMembers -->
<field name="listOfGroupMembers"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;listOfGroupMembers&lt;/code&gt; property.
</description>
<tag name="@see">#getListOfGroupMembers</tag>
<tag name="@see">#setListOfGroupMembers</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetGroup.presentValue -->
<field name="presentValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetGroup.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
