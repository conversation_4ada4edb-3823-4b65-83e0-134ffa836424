<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BajaFileUtil" name="BajaFileUtil" packageName="javax.baja.file" public="true">
<description>
FileUtil provides handy utility methods for&#xa; implementation of BIFile.
</description>
<tag name="@author"><PERSON> on 24 Jan 03</tag>
<tag name="@version">$Revision: 20$ $Date: 8/14/09 9:56:04 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.nre.util.FileUtil"/>
</extends>
<!-- javax.baja.file.BajaFileUtil() -->
<constructor name="BajaFileUtil" public="true">
<description/>
</constructor>

<!-- javax.baja.file.BajaFileUtil.read(javax.baja.file.BIFileStore) -->
<method name="read"  public="true" static="true">
<description>
Convenience for &lt;code&gt;read(f.getInputStream(), f.getSize())&lt;/code&gt;.
</description>
<parameter name="f">
<type class="javax.baja.file.BIFileStore"/>
</parameter>
<return>
<type class="byte" dimension="1"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BajaFileUtil.write(javax.baja.file.BIFileStore, byte[]) -->
<method name="write"  public="true" static="true">
<description>
Write the specified byte array to the file&#x27;s output stream.
</description>
<parameter name="f">
<type class="javax.baja.file.BIFileStore"/>
</parameter>
<parameter name="contents">
<type class="byte" dimension="1"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BajaFileUtil.pipe(javax.baja.file.BIFile, javax.baja.file.BIFile) -->
<method name="pipe"  public="true" static="true">
<description>
Pipe data from one file to another.
</description>
<parameter name="src">
<type class="javax.baja.file.BIFile"/>
</parameter>
<parameter name="dest">
<type class="javax.baja.file.BIFile"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BajaFileUtil.getDefaultFileWriter() -->
<method name="getDefaultFileWriter"  public="true" static="true">
<description>
Get a file writer that will work for most cases. Operator Write permissions&#xa; will be required on any file space to be written to.
</description>
<tag name="@since">Niagara 4.8</tag>
<return>
<type class="javax.baja.file.BajaFileUtil$BajaFileWriter"/>
<description>
a default file writer instance
</description>
</return>
</method>

<!-- javax.baja.file.BajaFileUtil.createFileToOverwrite(javax.baja.naming.BOrd, javax.baja.sys.Context) -->
<method name="createFileToOverwrite"  public="true" static="true">
<description>
Create a file, possibly overwriting any existing file.
</description>
<tag name="@since">Niagara 4.8</tag>
<parameter name="fileOrd">
<type class="javax.baja.naming.BOrd"/>
<description>
ORD to the desired file, including file name - will be&#xa;                resolved using default ORD base
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
the context to resolve the file ORD
</description>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
<description>
a file to write to; any existing file will be overwritten
</description>
</return>
<throws>
<type class="java.io.IOException"/>
<description>
if the file could not be created
</description>
</throws>
</method>

<!-- javax.baja.file.BajaFileUtil.createFileToOverwrite(javax.baja.naming.BOrd, java.lang.String, javax.baja.sys.BObject, javax.baja.sys.Context) -->
<method name="createFileToOverwrite"  public="true" static="true">
<description>
Create a file, possibly overwriting any existing file.
</description>
<tag name="@since">Niagara 4.8</tag>
<parameter name="fileOrd">
<type class="javax.baja.naming.BOrd"/>
<description>
ORD to the desired file
</description>
</parameter>
<parameter name="ext">
<type class="java.lang.String"/>
<description>
desired file extension
</description>
</parameter>
<parameter name="base">
<type class="javax.baja.sys.BObject"/>
<description>
the base object to resolve the file ORD
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
the context to resolve the file ORD
</description>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
<description>
a file to write to; any existing file will be overwritten
</description>
</return>
<throws>
<type class="java.io.IOException"/>
<description>
if the file could not be created
</description>
</throws>
</method>

<!-- javax.baja.file.BajaFileUtil.createFile(javax.baja.naming.BOrd, javax.baja.sys.Context) -->
<method name="createFile"  public="true" static="true">
<description>
Create a file.
</description>
<tag name="@since">Niagara 4.8</tag>
<parameter name="fileOrd">
<type class="javax.baja.naming.BOrd"/>
<description>
ORD to the desired file, including file name - will be&#xa;                resolved against default ORD base
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
the context to resolve the file ORD
</description>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
<description>
a file to write to
</description>
</return>
<throws>
<type class="java.io.IOException"/>
<description>
if the file could not be created
</description>
</throws>
</method>

<!-- javax.baja.file.BajaFileUtil.createFile(javax.baja.naming.BOrd, java.lang.String, javax.baja.sys.BObject, javax.baja.sys.Context) -->
<method name="createFile"  public="true" static="true">
<description>
Create a file.
</description>
<tag name="@since">Niagara 4.8</tag>
<parameter name="fileOrd">
<type class="javax.baja.naming.BOrd"/>
<description>
ORD to the desired file
</description>
</parameter>
<parameter name="ext">
<type class="java.lang.String"/>
<description>
desired file extension
</description>
</parameter>
<parameter name="base">
<type class="javax.baja.sys.BObject"/>
<description>
the base object to resolve the file ORD
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
the context to resolve the file ORD
</description>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
<description>
a file to write to
</description>
</return>
<throws>
<type class="java.io.IOException"/>
<description>
if the file could not be created
</description>
</throws>
</method>

<!-- javax.baja.file.BajaFileUtil.createTempFile(java.lang.String) -->
<method name="createTempFile"  public="true" static="true">
<description>
Create a temporary file that will be deleted when the JVM exists.
</description>
<tag name="@since">Niagara 4.8</tag>
<parameter name="ext">
<type class="java.lang.String"/>
<description>
desired file extension
</description>
</parameter>
<return>
<type class="java.io.File"/>
<description>
a temporary file
</description>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BajaFileUtil.readString(javax.baja.file.BIFile) -->
<method name="readString"  public="true" static="true">
<description>
Convenience for &lt;code&gt;readString(new InputStreamReader(file.getInputStream()))&lt;/code&gt;.
</description>
<parameter name="file">
<type class="javax.baja.file.BIFile"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BajaFileUtil.readLines(javax.baja.file.BIFile) -->
<method name="readLines"  public="true" static="true">
<description>
Convenience for &lt;code&gt;readLines(new InputStreamReader(file.getInputStream()))&lt;/code&gt;.
</description>
<parameter name="file">
<type class="javax.baja.file.BIFile"/>
</parameter>
<return>
<type class="java.lang.String" dimension="1"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BajaFileUtil.isSpecialModule(java.lang.String) -->
<method name="isSpecialModule"  public="true" static="true">
<description>
Is the given module a &#x27;special&#x27; module that lives in /bin/ext ?&#xa; Env.getSpecialModuleJar() in build.jar uses a fixed list of&#xa;  build, niagarad, nre, cryptoCore
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

</class>
</bajadoc>
