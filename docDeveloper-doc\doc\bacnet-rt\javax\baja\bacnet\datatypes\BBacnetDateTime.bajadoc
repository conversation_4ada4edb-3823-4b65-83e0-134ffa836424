<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetDateTime" name="BBacnetDateTime" packageName="javax.baja.bacnet.datatypes" public="true">
<description>
BBacnetDateTime represents a BacnetDateTime value in a Bacnet property.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 3$ $Date: 11/6/01 2:50:13 PM$</tag>
<tag name="@creation">09 Aug 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<implements>
<parameterizedType class="java.lang.Comparable">
<args>
<type class="java.lang.Object"/>
</args>
</parameterizedType>
</implements>
<property name="date" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
<description>
Slot for the &lt;code&gt;date&lt;/code&gt; property.
</description>
<tag name="@see">#getDate</tag>
<tag name="@see">#setDate</tag>
</property>

<property name="time" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
<description>
Slot for the &lt;code&gt;time&lt;/code&gt; property.
</description>
<tag name="@see">#getTime</tag>
<tag name="@see">#setTime</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime() -->
<constructor name="BBacnetDateTime" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime(javax.baja.bacnet.datatypes.BBacnetDate, javax.baja.bacnet.datatypes.BBacnetTime) -->
<constructor name="BBacnetDateTime" public="true">
<parameter name="date">
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
</parameter>
<parameter name="time">
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime(javax.baja.sys.BAbsTime) -->
<constructor name="BBacnetDateTime" public="true">
<parameter name="bt">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.getDate() -->
<method name="getDate"  public="true">
<description>
Get the &lt;code&gt;date&lt;/code&gt; property.
</description>
<tag name="@see">#date</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.setDate(javax.baja.bacnet.datatypes.BBacnetDate) -->
<method name="setDate"  public="true">
<description>
Set the &lt;code&gt;date&lt;/code&gt; property.
</description>
<tag name="@see">#date</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.getTime() -->
<method name="getTime"  public="true">
<description>
Get the &lt;code&gt;time&lt;/code&gt; property.
</description>
<tag name="@see">#time</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.setTime(javax.baja.bacnet.datatypes.BBacnetTime) -->
<method name="setTime"  public="true">
<description>
Set the &lt;code&gt;time&lt;/code&gt; property.
</description>
<tag name="@see">#time</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true" final="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true" final="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.isAnyUnspecified() -->
<method name="isAnyUnspecified"  public="true" final="true">
<description>
Is any field in either the date or the time UNPSECIFIED?
</description>
<return>
<type class="boolean"/>
<description>
true if any field in either the date or the time in unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.toBAbsTime() -->
<method name="toBAbsTime"  public="true" final="true">
<description>
Return a BAbsTime from this BacnetDateTime object.&#xa; No needed fields may be UNPSECIFIED.
</description>
<return>
<type class="javax.baja.sys.BAbsTime"/>
<description>
a BAbsTime representing the same absolute time.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.fromBAbsTime(javax.baja.sys.BAbsTime) -->
<method name="fromBAbsTime"  public="true" final="true">
<description>
Set this BBacnetDateTime from the given BAbsTime.
</description>
<parameter name="t">
<type class="javax.baja.sys.BAbsTime"/>
<description>
the BAbsTime.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.dateTimeEquals(java.lang.Object) -->
<method name="dateTimeEquals"  public="true" final="true">
<description>
BBacnetDateTime equivalence is based on all values being equal,&#xa; or unspecified.&#xa; &lt;B&gt;NOTE&lt;/B&gt;: This is the method to determine DateTime equivalence according&#xa; to BACnet, &lt;B&gt;not&lt;/B&gt; the equals() method, which requires UNSPECIFIED values&#xa; to match &lt;B&gt;only&lt;/B&gt; with UNSPECIFIED values.
</description>
<tag name="@see">equals</tag>
<parameter name="obj">
<type class="java.lang.Object"/>
<description>
the comparison object.
</description>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.compareTo(java.lang.Object) -->
<method name="compareTo"  public="true" final="true">
<description>
Compare to another BBacnetDate.
</description>
<parameter name="obj">
<type class="java.lang.Object"/>
<description>
the comparison object.
</description>
</parameter>
<return>
<type class="int"/>
<description>
a negative integer, zero, or a&#xa; positive integer as this object is less&#xa; than, equal to, or greater than the&#xa; specified object.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.isBefore(java.lang.Object) -->
<method name="isBefore"  public="true" final="true">
<description/>
<parameter name="x">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the specified date is before this date.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.isAfter(java.lang.Object) -->
<method name="isAfter"  public="true" final="true">
<description/>
<parameter name="x">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the specified date is after this date.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.isNotBefore(java.lang.Object) -->
<method name="isNotBefore"  public="true" final="true">
<description/>
<parameter name="x">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the specified date is not before this date.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.isNotAfter(java.lang.Object) -->
<method name="isNotAfter"  public="true" final="true">
<description/>
<parameter name="x">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the specified date is not after this date.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.fromString(java.lang.String) -->
<method name="fromString"  public="true" static="true" final="true">
<description>
Read the date and time values from the&#xa; given String and return a new BBacnetDateTime.
</description>
<parameter name="s">
<type class="java.lang.String"/>
<description>
the input string.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
<description>
a BBacnetDateTime read from the string.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.makeBAbsTime(javax.baja.bacnet.datatypes.BBacnetDate, javax.baja.bacnet.datatypes.BBacnetTime) -->
<method name="makeBAbsTime"  public="true" static="true" final="true">
<description>
Return a BAbsTime from the given BBacnetDate and BBacnetTime.
</description>
<parameter name="d">
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
<description>
date
</description>
</parameter>
<parameter name="t">
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
<description>
time
</description>
</parameter>
<return>
<type class="javax.baja.sys.BAbsTime"/>
<description>
a BAbsTime representing the same absolute time.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.makeBAbsTime(javax.baja.sys.BAbsTime, javax.baja.bacnet.datatypes.BBacnetTime) -->
<method name="makeBAbsTime"  public="true" static="true" final="true">
<description>
Return a BAbsTime from the given BBacnetDate and BBacnetTime.
</description>
<parameter name="d">
<type class="javax.baja.sys.BAbsTime"/>
<description>
date
</description>
</parameter>
<parameter name="t">
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
<description>
time
</description>
</parameter>
<return>
<type class="javax.baja.sys.BAbsTime"/>
<description>
a BAbsTime representing the same absolute time.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.date -->
<field name="date"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;date&lt;/code&gt; property.
</description>
<tag name="@see">#getDate</tag>
<tag name="@see">#setDate</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.time -->
<field name="time"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;time&lt;/code&gt; property.
</description>
<tag name="@see">#getTime</tag>
<tag name="@see">#setTime</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDateTime.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
