<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.postprocess.BIPostProcessor" name="BIPostProcessor" packageName="com.tridiumx.jsonToolkit.outbound.schema.postprocess" public="true" interface="true" abstract="true" category="interface">
<description>
Interface for components which handle the result of a json schema generation.
</description>
<tag name="@author"><PERSON></tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BIPostProcessor.postProcess(com.tridiumx.jsonToolkit.outbound.schema.BJsonSchema, java.lang.Exception) -->
<method name="postProcess"  public="true" abstract="true">
<description/>
<parameter name="schema">
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchema"/>
<description>
the schema in it&#x27;s state immediately after the json schema generation has occurred
</description>
</parameter>
<parameter name="exception">
<type class="java.lang.Exception"/>
<description>
any errors which aborted json output generation
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.postprocess.BIPostProcessor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
