<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="backup" runtimeProfile="rt" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>Niagara Backup Service</description>
<package name="javax.baja.backup"/>
<class packageName="javax.baja.backup" name="BBackupService"><description>BBackupService is used to define the files included in a&#xa; configuration backup such as config.bog and supporting static&#xa; files such as px, html, png, and jpegs.</description></class>
<class packageName="javax.baja.backup" name="BBackupService.ICanceler" category="interface"></class>
<class packageName="javax.baja.backup" name="BBackupService.JobCanceler"></class>
<class packageName="javax.baja.backup" name="BBackupService.RestoreThread"></class>
</module>
</bajadoc>
