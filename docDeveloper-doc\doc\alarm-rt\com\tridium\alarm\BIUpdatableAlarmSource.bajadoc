<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.BIUpdatableAlarmSource" name="BIUpdatableAlarmSource" packageName="com.tridium.alarm" public="true" interface="true" abstract="true" category="interface">
<description>
The BIUpdatableAlarmSource interface must be implemented by all&#xa; BObjects capable of handling updated alarms&#xa; &lt;p&gt;&#xa; This interface must be implemented by declaring the&#xa; following actions on the BIAlarmSource:&#xa; &lt;pre&gt;&#xa;    updateAlarm(updateRequest: BAlarmRecord)     &#xa;      flags = readonly, hidden&#xa; &lt;/pre&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">19 Feb 01</tag>
<tag name="@version">$Revision: 1$ $Date: 11/4/08 8:26:23 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<implements>
<type class="javax.baja.alarm.BIAlarmSource"/>
</implements>
</class>
</bajadoc>
