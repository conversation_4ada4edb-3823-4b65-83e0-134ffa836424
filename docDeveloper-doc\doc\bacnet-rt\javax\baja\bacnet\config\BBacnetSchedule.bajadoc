<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BBacnetSchedule" name="BBacnetSchedule" packageName="javax.baja.bacnet.config" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 7$ $Date: 12/10/01 9:26:02 AM$</tag>
<tag name="@creation">30 Jan 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.config.BBacnetCreatableObject"/>
</extends>
<property name="presentValue" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetAny"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</property>

<property name="facets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.
</description>
<tag name="@see">#getFacets</tag>
<tag name="@see">#setFacets</tag>
</property>

<property name="effectivePeriod" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDateRange"/>
<description>
Slot for the &lt;code&gt;effectivePeriod&lt;/code&gt; property.
</description>
<tag name="@see">#getEffectivePeriod</tag>
<tag name="@see">#setEffectivePeriod</tag>
</property>

<property name="listOfObjectPropertyReferences" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
<description>
Slot for the &lt;code&gt;listOfObjectPropertyReferences&lt;/code&gt; property.
</description>
<tag name="@see">#getListOfObjectPropertyReferences</tag>
<tag name="@see">#setListOfObjectPropertyReferences</tag>
</property>

<property name="priorityForWriting" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;priorityForWriting&lt;/code&gt; property.
</description>
<tag name="@see">#getPriorityForWriting</tag>
<tag name="@see">#setPriorityForWriting</tag>
</property>

<!-- javax.baja.bacnet.config.BBacnetSchedule() -->
<constructor name="BBacnetSchedule" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetSchedule.getPresentValue() -->
<method name="getPresentValue"  public="true">
<description>
Get the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#presentValue</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetAny"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetSchedule.setPresentValue(javax.baja.bacnet.datatypes.BBacnetAny) -->
<method name="setPresentValue"  public="true">
<description>
Set the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#presentValue</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetAny"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetSchedule.getFacets() -->
<method name="getFacets"  public="true">
<description>
Get the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.
</description>
<tag name="@see">#facets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetSchedule.setFacets(javax.baja.sys.BFacets) -->
<method name="setFacets"  public="true">
<description>
Set the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.
</description>
<tag name="@see">#facets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetSchedule.getEffectivePeriod() -->
<method name="getEffectivePeriod"  public="true">
<description>
Get the &lt;code&gt;effectivePeriod&lt;/code&gt; property.
</description>
<tag name="@see">#effectivePeriod</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDateRange"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetSchedule.setEffectivePeriod(javax.baja.bacnet.datatypes.BBacnetDateRange) -->
<method name="setEffectivePeriod"  public="true">
<description>
Set the &lt;code&gt;effectivePeriod&lt;/code&gt; property.
</description>
<tag name="@see">#effectivePeriod</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDateRange"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetSchedule.getListOfObjectPropertyReferences() -->
<method name="getListOfObjectPropertyReferences"  public="true">
<description>
Get the &lt;code&gt;listOfObjectPropertyReferences&lt;/code&gt; property.
</description>
<tag name="@see">#listOfObjectPropertyReferences</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetSchedule.setListOfObjectPropertyReferences(javax.baja.bacnet.datatypes.BBacnetListOf) -->
<method name="setListOfObjectPropertyReferences"  public="true">
<description>
Set the &lt;code&gt;listOfObjectPropertyReferences&lt;/code&gt; property.
</description>
<tag name="@see">#listOfObjectPropertyReferences</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetSchedule.getPriorityForWriting() -->
<method name="getPriorityForWriting"  public="true">
<description>
Get the &lt;code&gt;priorityForWriting&lt;/code&gt; property.
</description>
<tag name="@see">#priorityForWriting</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetSchedule.setPriorityForWriting(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setPriorityForWriting"  public="true">
<description>
Set the &lt;code&gt;priorityForWriting&lt;/code&gt; property.
</description>
<tag name="@see">#priorityForWriting</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetSchedule.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetSchedule.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetSchedule.started() -->
<method name="started"  public="true">
<description>
Started.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.config.BBacnetSchedule.getSlotFacets(javax.baja.sys.Slot) -->
<method name="getSlotFacets"  public="true">
<description>
Apply the &#x22;facets&#x22; property to the &#x22;presentValue&#x22; property.
</description>
<parameter name="slot">
<type class="javax.baja.sys.Slot"/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetSchedule.getPresentValueProperty() -->
<method name="getPresentValueProperty"  public="true">
<description>
Subclasses that have a present value property should&#xa; override this method and return this property.  The&#xa; default returns null.
</description>
<return>
<type class="javax.baja.sys.Property"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetSchedule.addObjectInitialValues(javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addObjectInitialValues"  protected="true">
<description/>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetSchedule.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetSchedule.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetSchedule.presentValue -->
<field name="presentValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetSchedule.facets -->
<field name="facets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.
</description>
<tag name="@see">#getFacets</tag>
<tag name="@see">#setFacets</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetSchedule.effectivePeriod -->
<field name="effectivePeriod"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;effectivePeriod&lt;/code&gt; property.
</description>
<tag name="@see">#getEffectivePeriod</tag>
<tag name="@see">#setEffectivePeriod</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetSchedule.listOfObjectPropertyReferences -->
<field name="listOfObjectPropertyReferences"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;listOfObjectPropertyReferences&lt;/code&gt; property.
</description>
<tag name="@see">#getListOfObjectPropertyReferences</tag>
<tag name="@see">#setListOfObjectPropertyReferences</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetSchedule.priorityForWriting -->
<field name="priorityForWriting"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;priorityForWriting&lt;/code&gt; property.
</description>
<tag name="@see">#getPriorityForWriting</tag>
<tag name="@see">#setPriorityForWriting</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetSchedule.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
