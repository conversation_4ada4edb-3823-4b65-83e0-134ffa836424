<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.alarm.BBacnetStatusAlgorithm" name="BBacnetStatusAlgorithm" packageName="javax.baja.bacnet.alarm" public="true">
<description>
BBacnetStatusAlgorithm defines the offnormal algorithm&#xa; for the &lt;code&gt; BacnetBitStringUtil.BACNET_STATUS_FLAGS &lt;/code&gt; bits of a point.&#xa; Refer BACnet ANSI/ASHRAE Standard 135-2016 , 13.3.11
</description>
<tag name="@author">V<PERSON><PERSON></tag>
<tag name="@creation">17 Feb 2019</tag>
<tag name="@since">BACNet 14, Niagara R47.u1</tag>
<extends>
<type class="javax.baja.alarm.ext.BOffnormalAlgorithm"/>
</extends>
<property name="alarmValues" flags="d">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
Slot for the &lt;code&gt;alarmValues&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmValues</tag>
<tag name="@see">#setAlarmValues</tag>
</property>

<property name="lastMonitoredValue" flags="dhr">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
Slot for the &lt;code&gt;lastMonitoredValue&lt;/code&gt; property.
</description>
<tag name="@see">#getLastMonitoredValue</tag>
<tag name="@see">#setLastMonitoredValue</tag>
</property>

<!-- javax.baja.bacnet.alarm.BBacnetStatusAlgorithm() -->
<constructor name="BBacnetStatusAlgorithm" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.alarm.BBacnetStatusAlgorithm.getAlarmValues() -->
<method name="getAlarmValues"  public="true">
<description>
Get the &lt;code&gt;alarmValues&lt;/code&gt; property.
</description>
<tag name="@see">#alarmValues</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetStatusAlgorithm.setAlarmValues(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="setAlarmValues"  public="true">
<description>
Set the &lt;code&gt;alarmValues&lt;/code&gt; property.
</description>
<tag name="@see">#alarmValues</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetStatusAlgorithm.getLastMonitoredValue() -->
<method name="getLastMonitoredValue"  public="true">
<description>
Get the &lt;code&gt;lastMonitoredValue&lt;/code&gt; property.
</description>
<tag name="@see">#lastMonitoredValue</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetStatusAlgorithm.setLastMonitoredValue(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="setLastMonitoredValue"  public="true">
<description>
Set the &lt;code&gt;lastMonitoredValue&lt;/code&gt; property.
</description>
<tag name="@see">#lastMonitoredValue</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetStatusAlgorithm.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetStatusAlgorithm.started() -->
<method name="started"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetStatusAlgorithm.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetStatusAlgorithm.writeAlarmData(javax.baja.status.BStatusValue, java.util.Map) -->
<method name="writeAlarmData"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="out">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<parameter name="map">
<parameterizedType class="java.util.Map">
<args>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetStatusAlgorithm.checkAlarms(javax.baja.status.BStatusValue, long, long) -->
<method name="checkAlarms"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return the new alarm state or null if no change
</description>
<parameter name="out">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<parameter name="toAlarmTimeDelay">
<type class="long"/>
</parameter>
<parameter name="toNormalTimeDelay">
<type class="long"/>
</parameter>
<return>
<type class="javax.baja.alarm.ext.BAlarmState"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetStatusAlgorithm.isNormal(javax.baja.status.BStatusValue) -->
<method name="isNormal"  protected="true">
<description/>
<parameter name="out">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<return>
<type class="javax.baja.bacnet.alarm.BBacnetStatusAlgorithm$BacnetStatus"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetStatusAlgorithm.setStausFlags(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="setStausFlags"  public="true">
<description/>
<parameter name="bacnetStatusFlags">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetStatusAlgorithm.setStatusFlagsOnExtWhenOutOfServiceIsChanged(boolean) -->
<method name="setStatusFlagsOnExtWhenOutOfServiceIsChanged"  public="true">
<description/>
<parameter name="outOfService">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetStatusAlgorithm.alarmValues -->
<field name="alarmValues"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmValues&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmValues</tag>
<tag name="@see">#setAlarmValues</tag>
</field>

<!-- javax.baja.bacnet.alarm.BBacnetStatusAlgorithm.lastMonitoredValue -->
<field name="lastMonitoredValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lastMonitoredValue&lt;/code&gt; property.
</description>
<tag name="@see">#getLastMonitoredValue</tag>
<tag name="@see">#setLastMonitoredValue</tag>
</field>

<!-- javax.baja.bacnet.alarm.BBacnetStatusAlgorithm.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
