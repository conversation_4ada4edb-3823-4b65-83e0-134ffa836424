<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BBacnetBinary" name="BBacnetBinary" packageName="javax.baja.bacnet.config" public="true" abstract="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 7$ $Date: 12/10/01 9:26:03 AM$</tag>
<tag name="@creation">31 Jan 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.BBacnetObject"/>
</extends>
<property name="presentValue" flags="">
<type class="javax.baja.bacnet.enums.BBacnetBinaryPv"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.&#xa; presentValue represents the object&#x27;s current value.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</property>

<property name="facets" flags="hr">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.&#xa; They are determined from the Active_Text and Inactive_Text&#xa; properties (if present).
</description>
<tag name="@see">#getFacets</tag>
<tag name="@see">#setFacets</tag>
</property>

<property name="statusFlags" flags="r">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
Slot for the &lt;code&gt;statusFlags&lt;/code&gt; property.&#xa; statusFlags represents flags indicating the alarm and fault status of&#xa; the Bacnet object.
</description>
<tag name="@see">#getStatusFlags</tag>
<tag name="@see">#setStatusFlags</tag>
</property>

<property name="eventState" flags="r">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getEventState</tag>
<tag name="@see">#setEventState</tag>
</property>

<property name="outOfService" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#getOutOfService</tag>
<tag name="@see">#setOutOfService</tag>
</property>

<!-- javax.baja.bacnet.config.BBacnetBinary() -->
<constructor name="BBacnetBinary" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetBinary.getPresentValue() -->
<method name="getPresentValue"  public="true">
<description>
Get the &lt;code&gt;presentValue&lt;/code&gt; property.&#xa; presentValue represents the object&#x27;s current value.
</description>
<tag name="@see">#presentValue</tag>
<return>
<type class="javax.baja.bacnet.enums.BBacnetBinaryPv"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetBinary.setPresentValue(javax.baja.bacnet.enums.BBacnetBinaryPv) -->
<method name="setPresentValue"  public="true">
<description>
Set the &lt;code&gt;presentValue&lt;/code&gt; property.&#xa; presentValue represents the object&#x27;s current value.
</description>
<tag name="@see">#presentValue</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.BBacnetBinaryPv"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetBinary.getFacets() -->
<method name="getFacets"  public="true">
<description>
Get the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.&#xa; They are determined from the Active_Text and Inactive_Text&#xa; properties (if present).
</description>
<tag name="@see">#facets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetBinary.setFacets(javax.baja.sys.BFacets) -->
<method name="setFacets"  public="true">
<description>
Set the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.&#xa; They are determined from the Active_Text and Inactive_Text&#xa; properties (if present).
</description>
<tag name="@see">#facets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetBinary.getStatusFlags() -->
<method name="getStatusFlags"  public="true">
<description>
Get the &lt;code&gt;statusFlags&lt;/code&gt; property.&#xa; statusFlags represents flags indicating the alarm and fault status of&#xa; the Bacnet object.
</description>
<tag name="@see">#statusFlags</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetBinary.setStatusFlags(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="setStatusFlags"  public="true">
<description>
Set the &lt;code&gt;statusFlags&lt;/code&gt; property.&#xa; statusFlags represents flags indicating the alarm and fault status of&#xa; the Bacnet object.
</description>
<tag name="@see">#statusFlags</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetBinary.getEventState() -->
<method name="getEventState"  public="true">
<description>
Get the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#eventState</tag>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetBinary.setEventState(javax.baja.sys.BEnum) -->
<method name="setEventState"  public="true">
<description>
Set the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#eventState</tag>
<parameter name="v">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetBinary.getOutOfService() -->
<method name="getOutOfService"  public="true">
<description>
Get the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#outOfService</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetBinary.setOutOfService(boolean) -->
<method name="setOutOfService"  public="true">
<description>
Set the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#outOfService</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetBinary.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetBinary.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetBinary.getSlotFacets(javax.baja.sys.Slot) -->
<method name="getSlotFacets"  public="true">
<description>
Apply the &#x22;facets&#x22; property to the &#x22;presentValue&#x22; property.
</description>
<parameter name="slot">
<type class="javax.baja.sys.Slot"/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetBinary.setOutputFacets() -->
<method name="setOutputFacets"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetBinary.getPresentValueProperty() -->
<method name="getPresentValueProperty"  public="true">
<description>
Subclasses that have a present value property should&#xa; override this method and return this property.  The&#xa; default returns null.
</description>
<return>
<type class="javax.baja.sys.Property"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetBinary.presentValue -->
<field name="presentValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.&#xa; presentValue represents the object&#x27;s current value.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetBinary.facets -->
<field name="facets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.&#xa; They are determined from the Active_Text and Inactive_Text&#xa; properties (if present).
</description>
<tag name="@see">#getFacets</tag>
<tag name="@see">#setFacets</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetBinary.statusFlags -->
<field name="statusFlags"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;statusFlags&lt;/code&gt; property.&#xa; statusFlags represents flags indicating the alarm and fault status of&#xa; the Bacnet object.
</description>
<tag name="@see">#getStatusFlags</tag>
<tag name="@see">#setStatusFlags</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetBinary.eventState -->
<field name="eventState"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getEventState</tag>
<tag name="@see">#setEventState</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetBinary.outOfService -->
<field name="outOfService"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#getOutOfService</tag>
<tag name="@see">#setOutOfService</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetBinary.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
