<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetTime" name="BBacnetTime" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BBacnetTime represents a date value in a Bacnet property.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 6$ $Date: 11/8/01 9:04:51 AM$</tag>
<tag name="@creation">24 Sep 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BSimple"/>
</extends>
<implements>
<type class="javax.baja.sys.BIComparable"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
<!-- javax.baja.bacnet.datatypes.BBacnetTime.make() -->
<method name="make"  public="true" static="true">
<description>
Factory method for all unspecified.
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.make(int, int, int, int) -->
<method name="make"  public="true" static="true">
<description>
Factory method.&#xa; Note that BACnet uses unsigned bytes, so 255 for BACnet corresponds&#xa; to -1 for Java&#x27;s signed byte data type.  This factory method will&#xa; accept either 255 or -1 for &#x27;unspecified&#x27;.
</description>
<parameter name="hour">
<type class="int"/>
<description>
0-23, or 255/-1 for unspecified
</description>
</parameter>
<parameter name="minute">
<type class="int"/>
<description>
0-59, or 255/-1 for unspecified
</description>
</parameter>
<parameter name="second">
<type class="int"/>
<description>
0-59, or 255/-1 for unspecified
</description>
</parameter>
<parameter name="hundredth">
<type class="int"/>
<description>
0-99, or 255/-1 for unspecified
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.make(javax.baja.sys.BAbsTime) -->
<method name="make"  public="true" static="true">
<description>
Factory method from BAbsTime.
</description>
<parameter name="bt">
<type class="javax.baja.sys.BAbsTime"/>
<description>
BAbsTime.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.make(javax.baja.sys.BTime) -->
<method name="make"  public="true" static="true">
<description>
Factory method from BAbsTime.
</description>
<parameter name="bt">
<type class="javax.baja.sys.BTime"/>
<description>
BAbsTime.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description>
BBacnetTime equality is based on all values being equal.&#xa; NOTE: UNSPECIFIED values match ONLY other UNSPECIFIED values!&#xa; This is NOT the same as Bacnet equivalence!  For Bacnet time equivalence,&#xa; use the timeEquals() method.
</description>
<tag name="@see">timeEquals</tag>
<parameter name="obj">
<type class="java.lang.Object"/>
<description>
the comparison object
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the object is a &lt;code&gt;BBacnetTime&lt;/code&gt; with all values equal to this one.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.hashCode() -->
<method name="hashCode"  public="true">
<description>
BBacnetTime hashcode is a concatenation of all fields.
</description>
<return>
<type class="int"/>
<description>
a hash code computed by concatenating all fields.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.encode(java.io.DataOutput) -->
<method name="encode"  public="true">
<description>
BBacnetTime is serialized using calls to writeByte().
</description>
<parameter name="out">
<type class="java.io.DataOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.decode(java.io.DataInput) -->
<method name="decode"  public="true">
<description>
BBacnetTime is unserialized using calls to readByte().
</description>
<parameter name="in">
<type class="java.io.DataInput"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.encodeToString() -->
<method name="encodeToString"  public="true">
<description>
Write the primitive in text format.
</description>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.decodeFromString(java.lang.String) -->
<method name="decodeFromString"  public="true">
<description>
Read the primitive from text format.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.getHour() -->
<method name="getHour"  public="true">
<description>
Get the hour.
</description>
<return>
<type class="int"/>
<description>
the hour represented by this BBacnetTime,&#xa; or -1 if unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.getMinute() -->
<method name="getMinute"  public="true">
<description>
Get the minute.
</description>
<return>
<type class="int"/>
<description>
the minute represented by this BBacnetTime,&#xa; or -1 if unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.getSecond() -->
<method name="getSecond"  public="true">
<description>
Get the second.
</description>
<return>
<type class="int"/>
<description>
the second represented by this BBacnetTime,&#xa; or -1 if unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.getHundredth() -->
<method name="getHundredth"  public="true">
<description>
Get the hundredth.
</description>
<return>
<type class="int"/>
<description>
the hundredth represented by this BBacnetTime,&#xa; or -1 if unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.isHourUnspecified() -->
<method name="isHourUnspecified"  public="true">
<description>
Is the hour unspecified?
</description>
<return>
<type class="boolean"/>
<description>
true if the hour is unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.isMinuteUnspecified() -->
<method name="isMinuteUnspecified"  public="true">
<description>
Is the minute unspecified?
</description>
<return>
<type class="boolean"/>
<description>
true if the minute is unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.isSecondUnspecified() -->
<method name="isSecondUnspecified"  public="true">
<description>
Is the second unspecified?
</description>
<return>
<type class="boolean"/>
<description>
true if the second is unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.isHundredthUnspecified() -->
<method name="isHundredthUnspecified"  public="true">
<description>
Is the hundredth unspecified?
</description>
<return>
<type class="boolean"/>
<description>
true if the hundredth is unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.isAnyUnspecified() -->
<method name="isAnyUnspecified"  public="true">
<description>
Is any field unspecified?
</description>
<return>
<type class="boolean"/>
<description>
true if any field is unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.timeEquals(java.lang.Object) -->
<method name="timeEquals"  public="true">
<description>
BBacnetTime equivalence is based on all values being equal,&#xa; or unspecified.&#xa; &lt;B&gt;NOTE&lt;/B&gt;: This is the method to determine time equivalence according&#xa; to BACnet, &lt;B&gt;not&lt;/B&gt; the equals() method, which requires UNSPECIFIED values&#xa; to match &lt;B&gt;only&lt;/B&gt; with UNSPECIFIED values.
</description>
<tag name="@see">equals</tag>
<parameter name="obj">
<type class="java.lang.Object"/>
<description>
the comparison object.
</description>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.compareTo(java.lang.Object) -->
<method name="compareTo"  public="true">
<description>
Compare to another BBacnetTime.
</description>
<parameter name="obj">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="int"/>
<description>
a negative integer, zero, or a&#xa; positive integer as this object is less&#xa; than, equal to, or greater than the&#xa; specified object.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.isBefore(java.lang.Object) -->
<method name="isBefore"  public="true">
<description/>
<parameter name="x">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the specified time is before this time.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.isAfter(java.lang.Object) -->
<method name="isAfter"  public="true">
<description/>
<parameter name="x">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the specified time is after this time.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.isNotBefore(java.lang.Object) -->
<method name="isNotBefore"  public="true">
<description/>
<parameter name="x">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the specified time is not before this time.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.isNotAfter(java.lang.Object) -->
<method name="isNotAfter"  public="true">
<description/>
<parameter name="x">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the specified time is not after this time.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.toBTime() -->
<method name="toBTime"  public="true">
<description>
Get the equivalent BTime.
</description>
<return>
<type class="javax.baja.sys.BTime"/>
<description>
a BTime representing this BBacnetTime&#x27;s value.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.fromString(java.lang.String) -->
<method name="fromString"  public="true" static="true">
<description>
Read the time values from the&#xa; given String and return a new BBacnetTime.
</description>
<parameter name="s">
<type class="java.lang.String"/>
<description>
the input string.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
<description>
a BBacnetTime read from the string.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.getBTime(javax.baja.bacnet.datatypes.BBacnetTime) -->
<method name="getBTime"  public="true" static="true">
<description>
Get the equivalent BTime.
</description>
<parameter name="t">
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
<description>
the BBacnetTime.
</description>
</parameter>
<return>
<type class="javax.baja.sys.BTime"/>
<description>
a BTime representing the same time as the BBacnetTime.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.getBTime(javax.baja.bacnet.datatypes.BBacnetTime, boolean) -->
<method name="getBTime"  public="true" static="true">
<description>
Get a BTime object from a BBacnetTime.
</description>
<parameter name="t">
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
<description>
the BBacnetTime.
</description>
</parameter>
<parameter name="zero">
<type class="boolean"/>
<description>
if true, UNSPECIFIED values will be coded as 0;&#xa;             if false, UNSPECIFIED values will be coded as the max value.
</description>
</parameter>
<return>
<type class="javax.baja.sys.BTime"/>
<description>
a BTime representing the same time as the BBacnetTime.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.UNSPECIFIED -->
<field name="UNSPECIFIED"  public="true" static="true" final="true">
<type class="byte"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.TEXT_LENGTH -->
<field name="TEXT_LENGTH"  public="true" static="true" final="true">
<type class="int"/>
<description>
The length of the string returned by toFacetString().
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetTime.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
