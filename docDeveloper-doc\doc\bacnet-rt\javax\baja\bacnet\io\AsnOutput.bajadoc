<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.io.AsnOutput" name="AsnOutput" packageName="javax.baja.bacnet.io" public="true" interface="true" abstract="true" category="interface">
<description>
This interface specifies the methods for encoding Niagara&#xa; quantities into BACnet ASN.1 primitives.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">09 May 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<!-- javax.baja.bacnet.io.AsnOutput.writeNull() -->
<method name="writeNull"  public="true" abstract="true">
<description>
Writes a Null to the output stream.&#xa; See Bacnet Clause 20.2.2
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeNull(int) -->
<method name="writeNull"  public="true" abstract="true">
<description>
Writes a context-tagged Null value to the output stream.&#xa; See Bacnet Clause 20.2.2
</description>
<parameter name="contextTag">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeBoolean(boolean) -->
<method name="writeBoolean"  public="true" abstract="true">
<description>
Writes an application-tagged boolean value to the output stream.&#xa; See Bacnet Clause 20.2.3&#xa; for encoding rules.
</description>
<parameter name="value">
<type class="boolean"/>
<description>
to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeBoolean(int, boolean) -->
<method name="writeBoolean"  public="true" abstract="true">
<description>
Writes a context-tagged boolean value to the output stream.&#xa; See Bacnet Clause 20.2.3 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
<description>
tag to be used
</description>
</parameter>
<parameter name="value">
<type class="boolean"/>
<description>
to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeBoolean(javax.baja.sys.BBoolean) -->
<method name="writeBoolean"  public="true" abstract="true">
<description>
Writes an application-tagged boolean value to the output stream.&#xa; See Bacnet Clause 20.2.3&#xa; for encoding rules.
</description>
<parameter name="value">
<type class="javax.baja.sys.BBoolean"/>
<description>
to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeBoolean(int, javax.baja.sys.BBoolean) -->
<method name="writeBoolean"  public="true" abstract="true">
<description>
Writes a context-tagged boolean value to the output stream.&#xa; See Bacnet Clause 20.2.3 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
<description>
tag to be used
</description>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BBoolean"/>
<description>
to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeUnsignedInteger(long) -->
<method name="writeUnsignedInteger"  public="true" abstract="true">
<description>
Writes an application-tagged unsigned integer value&#xa; to the output stream.&lt;p&gt;&#xa; See Bacnet Clause 20.2.4 for encoding rules.
</description>
<parameter name="value">
<type class="long"/>
<description>
to be written.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeUnsignedInteger(int, long) -->
<method name="writeUnsignedInteger"  public="true" abstract="true">
<description>
Writes a context-tagged unsigned integer value&#xa; to the output stream.&lt;p&gt;&#xa; See Bacnet Clause 20.2.4 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
</parameter>
<parameter name="value">
<type class="long"/>
<description>
to be written.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeUnsigned(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="writeUnsigned"  public="true" abstract="true">
<description>
Writes an application-tagged unsigned integer value&#xa; to the output stream.&lt;p&gt;&#xa; See Bacnet Clause 20.2.4 for encoding rules.
</description>
<parameter name="value">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
to be written.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeUnsigned(int, javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="writeUnsigned"  public="true" abstract="true">
<description>
Writes a context-tagged unsigned integer value&#xa; to the output stream.&lt;p&gt;&#xa; See Bacnet Clause 20.2.4 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
</parameter>
<parameter name="value">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
to be written.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeSignedInteger(int) -->
<method name="writeSignedInteger"  public="true" abstract="true">
<description>
Writes an application-tagged signed integer value&#xa; to the output stream.&lt;p&gt;&#xa; See Bacnet Clause 20.2.4 for encoding rules.
</description>
<parameter name="value">
<type class="int"/>
<description>
to be written.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeSignedInteger(int, int) -->
<method name="writeSignedInteger"  public="true" abstract="true">
<description>
Writes a context-tagged signed integer value&#xa; to the output stream.&lt;p&gt;&#xa; See Bacnet Clause 20.2.4 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
</parameter>
<parameter name="value">
<type class="int"/>
<description>
to be written.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeSignedInteger(javax.baja.sys.BInteger) -->
<method name="writeSignedInteger"  public="true" abstract="true">
<description>
Writes an application-tagged signed integer value&#xa; to the output stream.&lt;p&gt;&#xa; See Bacnet Clause 20.2.4 for encoding rules.
</description>
<parameter name="value">
<type class="javax.baja.sys.BInteger"/>
<description>
to be written.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeSignedInteger(int, javax.baja.sys.BInteger) -->
<method name="writeSignedInteger"  public="true" abstract="true">
<description>
Writes a context-tagged signed integer value&#xa; to the output stream.&lt;p&gt;&#xa; See Bacnet Clause 20.2.4 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BInteger"/>
<description>
to be written.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeReal(double) -->
<method name="writeReal"  public="true" abstract="true">
<description>
Writes an application-tagged real number to the output stream.&#xa; See Bacnet Clause 20.2.6 for encoding rules.
</description>
<parameter name="value">
<type class="double"/>
<description>
value to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeReal(int, double) -->
<method name="writeReal"  public="true" abstract="true">
<description>
Writes a context-tagged real number (float) value to the output stream.&#xa; See Bacnet Clause 20.2.6 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
<description>
context tag to use
</description>
</parameter>
<parameter name="value">
<type class="double"/>
<description>
value to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeReal(javax.baja.sys.BNumber) -->
<method name="writeReal"  public="true" abstract="true">
<description>
Writes an application-tagged real number to the output stream.&#xa; See Bacnet Clause 20.2.6 for encoding rules.
</description>
<parameter name="value">
<type class="javax.baja.sys.BNumber"/>
<description>
value to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeReal(int, javax.baja.sys.BNumber) -->
<method name="writeReal"  public="true" abstract="true">
<description>
Writes a context-tagged real number (float) value to the output stream.&#xa; See Bacnet Clause 20.2.6 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
<description>
context tag to use
</description>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BNumber"/>
<description>
value to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeDouble(double) -->
<method name="writeDouble"  public="true" abstract="true">
<description>
Writes an application-tagged double precision number to the output stream.&#xa; See Bacnet Clause 20.2.7 for encoding rules.
</description>
<parameter name="value">
<type class="double"/>
<description>
value to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeDouble(int, double) -->
<method name="writeDouble"  public="true" abstract="true">
<description>
Writes a context-tagged double precision number to the output stream.&#xa; See Bacnet Clause 20.2.7 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
</parameter>
<parameter name="value">
<type class="double"/>
<description>
value to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeDouble(javax.baja.sys.BNumber) -->
<method name="writeDouble"  public="true" abstract="true">
<description>
Writes an application-tagged double precision number to the output stream.&#xa; See Bacnet Clause 20.2.7 for encoding rules.
</description>
<parameter name="value">
<type class="javax.baja.sys.BNumber"/>
<description>
value to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeDouble(int, javax.baja.sys.BNumber) -->
<method name="writeDouble"  public="true" abstract="true">
<description>
Writes a context-tagged double precision number to the output stream.&#xa; See Bacnet Clause 20.2.7 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BNumber"/>
<description>
value to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeOctetString(byte[]) -->
<method name="writeOctetString"  public="true" abstract="true">
<description>
Writes an application-tagged byte array to the output stream.&#xa; See Bacnet Clause 20.2.8 for encoding rules.
</description>
<parameter name="octetString">
<type class="byte" dimension="1"/>
<description>
octet string to be encoded
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeOctetString(int, byte[]) -->
<method name="writeOctetString"  public="true" abstract="true">
<description>
Writes a context-tagged octet string (byte array) value to the output stream.&#xa; See Bacnet Clause 20.2.8 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
<description>
context tag to use
</description>
</parameter>
<parameter name="octetString">
<type class="byte" dimension="1"/>
<description>
octet string to be encoded
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeOctetString(javax.baja.bacnet.datatypes.BBacnetOctetString) -->
<method name="writeOctetString"  public="true" abstract="true">
<description>
Writes an application-tagged byte array to the output stream.&#xa; See Bacnet Clause 20.2.8 for encoding rules.
</description>
<parameter name="octetString">
<type class="javax.baja.bacnet.datatypes.BBacnetOctetString"/>
<description>
octet string to be encoded
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeOctetString(int, javax.baja.bacnet.datatypes.BBacnetOctetString) -->
<method name="writeOctetString"  public="true" abstract="true">
<description>
Writes a context-tagged octet string (byte array) value to the output stream.&#xa; See Bacnet Clause 20.2.8 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
<description>
context tag to use
</description>
</parameter>
<parameter name="octetString">
<type class="javax.baja.bacnet.datatypes.BBacnetOctetString"/>
<description>
octet string to be encoded
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeCharacterString(java.lang.String) -->
<method name="writeCharacterString"  public="true" abstract="true">
<description>
Writes an application-tagged character string&#xa; to the output stream using the local device&#x27;s&#xa; encoding. See Bacnet Clause 20.2.9 for&#xa; encoding rules.
</description>
<parameter name="value">
<type class="java.lang.String"/>
<description>
to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeCharacterString(java.lang.String, javax.baja.bacnet.enums.BCharacterSetEncoding) -->
<method name="writeCharacterString"  public="true" abstract="true">
<description>
Writes an application-tagged character string&#xa; to the output stream using the specified&#xa; encoding. See Bacnet Clause 20.2.9 for&#xa; encoding rules.
</description>
<parameter name="value">
<type class="java.lang.String"/>
<description/>
</parameter>
<parameter name="encoding">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeCharacterString(int, java.lang.String) -->
<method name="writeCharacterString"  public="true" abstract="true">
<description>
Writes a context-tagged character string&#xa; to the output stream using the local device&#x27;s&#xa; encoding. See Bacnet Clause 20.2.9 for&#xa; encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
<description>
context tag to use
</description>
</parameter>
<parameter name="value">
<type class="java.lang.String"/>
<description>
string to be encoded
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeCharacterString(int, java.lang.String, javax.baja.bacnet.enums.BCharacterSetEncoding) -->
<method name="writeCharacterString"  public="true" abstract="true">
<description>
Writes a context-tagged character string&#xa; to the output stream using the specified&#xa; encoding. See Bacnet Clause 20.2.9 for&#xa; encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
<description>
context tag to use
</description>
</parameter>
<parameter name="value">
<type class="java.lang.String"/>
<description>
string to be encoded
</description>
</parameter>
<parameter name="encoding">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeCharacterString(javax.baja.sys.BString) -->
<method name="writeCharacterString"  public="true" abstract="true">
<description>
Writes an application-tagged character string&#xa; to the output stream using the local device&#x27;s&#xa; encoding. See Bacnet Clause 20.2.9 for&#xa; encoding rules.
</description>
<parameter name="value">
<type class="javax.baja.sys.BString"/>
<description>
to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeCharacterString(javax.baja.sys.BString, javax.baja.bacnet.enums.BCharacterSetEncoding) -->
<method name="writeCharacterString"  public="true" abstract="true">
<description>
Writes an application-tagged character string&#xa; to the output stream using the specified&#xa; encoding. See Bacnet Clause 20.2.9 for&#xa; encoding rules.
</description>
<parameter name="value">
<type class="javax.baja.sys.BString"/>
<description/>
</parameter>
<parameter name="encoding">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeCharacterString(int, javax.baja.sys.BString) -->
<method name="writeCharacterString"  public="true" abstract="true">
<description>
Writes a context-tagged character string&#xa; to the output stream using the local device&#x27;s&#xa; encoding. See Bacnet Clause 20.2.9 for&#xa; encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
<description>
context tag to use
</description>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BString"/>
<description>
string to be encoded
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeCharacterString(int, javax.baja.sys.BString, javax.baja.bacnet.enums.BCharacterSetEncoding) -->
<method name="writeCharacterString"  public="true" abstract="true">
<description>
Writes a context-tagged character string&#xa; to the output stream using the specified&#xa; encoding. See Bacnet Clause 20.2.9 for&#xa; encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
<description>
context tag to use
</description>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BString"/>
<description>
string to be encoded
</description>
</parameter>
<parameter name="encoding">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeBitString(boolean[]) -->
<method name="writeBitString"  public="true" abstract="true">
<description>
Writes an application-tagged bit string value to the output stream.&#xa; See Bacnet Clause 20.2.10 for encoding rules.
</description>
<parameter name="bitString">
<type class="boolean" dimension="1"/>
<description>
bits to be encoded
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeBitString(int, boolean[]) -->
<method name="writeBitString"  public="true" abstract="true">
<description>
Writes a context-tagged bit string value to the output stream.&#xa; See Bacnet Clause 20.2.10 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
<description>
context tag to use
</description>
</parameter>
<parameter name="bitString">
<type class="boolean" dimension="1"/>
<description>
bits to encode
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeBitString(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="writeBitString"  public="true" abstract="true">
<description>
Writes an application-tagged bit string value to the output stream.&#xa; See Bacnet Clause 20.2.10 for encoding rules.
</description>
<parameter name="bitString">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
bits to be encoded
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeBitString(int, javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="writeBitString"  public="true" abstract="true">
<description>
Writes a context-tagged bit string value to the output stream.&#xa; See Bacnet Clause 20.2.10 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
<description>
context tag to use
</description>
</parameter>
<parameter name="bitString">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
bits to encode
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeEnumerated(int) -->
<method name="writeEnumerated"  public="true" abstract="true">
<description>
Writes an application-tagged enumerated value to the output stream.&#xa; See Bacnet Clause 20.2.11 for encoding rules.
</description>
<parameter name="enumValue">
<type class="int"/>
<description>
enum value to encode
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeEnumerated(int, int) -->
<method name="writeEnumerated"  public="true" abstract="true">
<description>
Writes a context-tagged enumerated value to the output stream.&#xa; See Bacnet Clause 20.2.4 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
<description>
context tag to use
</description>
</parameter>
<parameter name="enumValue">
<type class="int"/>
<description>
value to encode
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeEnumerated(javax.baja.sys.BEnum) -->
<method name="writeEnumerated"  public="true" abstract="true">
<description>
Writes an application-tagged enumerated value to the output stream.&#xa; See Bacnet Clause 20.2.11 for encoding rules.
</description>
<parameter name="e">
<type class="javax.baja.sys.BEnum"/>
<description>
enum to encode
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeEnumerated(int, javax.baja.sys.BEnum) -->
<method name="writeEnumerated"  public="true" abstract="true">
<description>
Writes a context-tagged enumerated value to the output stream.&#xa; See Bacnet Clause 20.2.4 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
<description>
context tag to use
</description>
</parameter>
<parameter name="e">
<type class="javax.baja.sys.BEnum"/>
<description>
enum to encode
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeDate(int, int, int, int) -->
<method name="writeDate"  public="true" abstract="true">
<description>
Write application-tagged date values to the output stream.
</description>
<parameter name="year">
<type class="int"/>
<description/>
</parameter>
<parameter name="month">
<type class="int"/>
<description/>
</parameter>
<parameter name="day">
<type class="int"/>
<description/>
</parameter>
<parameter name="weekday">
<type class="int"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeDate(int, int, int, int, int) -->
<method name="writeDate"  public="true" abstract="true">
<description>
Write context-tagged date values to the output stream.
</description>
<parameter name="contextTag">
<type class="int"/>
<description/>
</parameter>
<parameter name="year">
<type class="int"/>
<description/>
</parameter>
<parameter name="month">
<type class="int"/>
<description/>
</parameter>
<parameter name="day">
<type class="int"/>
<description/>
</parameter>
<parameter name="weekday">
<type class="int"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeDate(javax.baja.bacnet.datatypes.BBacnetDate) -->
<method name="writeDate"  public="true" abstract="true">
<description>
Writes an application-tagged Date value to the output stream.&#xa; See Bacnet Clause 20.2.12 for encoding rules.&#xa; See Bacnet Clause 21, section Application Types,&#xa; for the format of the Date bytes.
</description>
<parameter name="date">
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
<description>
the BBacnetDate to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeDate(int, javax.baja.bacnet.datatypes.BBacnetDate) -->
<method name="writeDate"  public="true" abstract="true">
<description>
Writes a context-tagged Date value to the output stream.&#xa; See Bacnet Clause 20.2.12 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
<description/>
</parameter>
<parameter name="date">
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
<description>
the BBacnetDate to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeDate(javax.baja.sys.BAbsTime) -->
<method name="writeDate"  public="true" abstract="true">
<description>
Writes an application-tagged Date value to the output stream.&#xa; See Bacnet Clause 20.2.12 for encoding rules.&#xa; See Bacnet Clause 21, section Application Types,&#xa; for the format of the Date bytes.
</description>
<parameter name="date">
<type class="javax.baja.sys.BAbsTime"/>
<description>
the BAbsTime to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeDate(int, javax.baja.sys.BAbsTime) -->
<method name="writeDate"  public="true" abstract="true">
<description>
Writes a context-tagged Date value to the output stream.&#xa; See Bacnet Clause 20.2.12 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
<description/>
</parameter>
<parameter name="date">
<type class="javax.baja.sys.BAbsTime"/>
<description>
the BAbsTime to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeTime(int, int, int, int) -->
<method name="writeTime"  public="true" abstract="true">
<description>
Write application-tagged time values to the output stream.
</description>
<parameter name="hour">
<type class="int"/>
<description/>
</parameter>
<parameter name="minute">
<type class="int"/>
<description/>
</parameter>
<parameter name="second">
<type class="int"/>
<description/>
</parameter>
<parameter name="hundredth">
<type class="int"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeTime(int, int, int, int, int) -->
<method name="writeTime"  public="true" abstract="true">
<description>
Write context-tagged time values to the output stream.
</description>
<parameter name="contextTag">
<type class="int"/>
<description/>
</parameter>
<parameter name="hour">
<type class="int"/>
<description/>
</parameter>
<parameter name="minute">
<type class="int"/>
<description/>
</parameter>
<parameter name="second">
<type class="int"/>
<description/>
</parameter>
<parameter name="hundredth">
<type class="int"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeTime(javax.baja.bacnet.datatypes.BBacnetTime) -->
<method name="writeTime"  public="true" abstract="true">
<description>
Writes an application-tagged Time value to the output stream.&#xa; See Bacnet Clause 20.2.12 for encoding rules.&#xa; See Bacnet Clause 21, section Application Types,&#xa; for the format of the Time bytes.
</description>
<parameter name="time">
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
<description>
BBacnetTime to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeTime(int, javax.baja.bacnet.datatypes.BBacnetTime) -->
<method name="writeTime"  public="true" abstract="true">
<description>
Writes a context-tagged Time value to the output stream.&#xa; See Bacnet Clause 20.2.12 for encoding rules.&#xa; See Bacnet Clause 21, section Application Types,&#xa; for the format of the Time bytes.
</description>
<parameter name="contextTag">
<type class="int"/>
<description/>
</parameter>
<parameter name="time">
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
<description>
BBacnetTime to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeTime(javax.baja.sys.BTime) -->
<method name="writeTime"  public="true" abstract="true">
<description>
Writes an application-tagged Time value to the output stream.&#xa; See Bacnet Clause 20.2.12 for encoding rules.&#xa; See Bacnet Clause 21, section Application Types,&#xa; for the format of the Time bytes.
</description>
<parameter name="time">
<type class="javax.baja.sys.BTime"/>
<description>
BTime to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeTime(int, javax.baja.sys.BTime) -->
<method name="writeTime"  public="true" abstract="true">
<description>
Writes a context-tagged Time value to the output stream.&#xa; See Bacnet Clause 20.2.12 for encoding rules.&#xa; See Bacnet Clause 21, section Application Types,&#xa; for the format of the Time bytes.
</description>
<parameter name="contextTag">
<type class="int"/>
<description/>
</parameter>
<parameter name="time">
<type class="javax.baja.sys.BTime"/>
<description>
BTime to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeTime(javax.baja.sys.BAbsTime) -->
<method name="writeTime"  public="true" abstract="true">
<description>
Writes an application-tagged Time value to the output stream.&#xa; See Bacnet Clause 20.2.12 for encoding rules.&#xa; See Bacnet Clause 21, section Application Types,&#xa; for the format of the Time bytes.
</description>
<parameter name="time">
<type class="javax.baja.sys.BAbsTime"/>
<description>
BAbsTime to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeTime(int, javax.baja.sys.BAbsTime) -->
<method name="writeTime"  public="true" abstract="true">
<description>
Writes a context-tagged Time value to the output stream.&#xa; See Bacnet Clause 20.2.12 for encoding rules.&#xa; See Bacnet Clause 21, section Application Types,&#xa; for the format of the Time bytes.
</description>
<parameter name="contextTag">
<type class="int"/>
<description/>
</parameter>
<parameter name="time">
<type class="javax.baja.sys.BAbsTime"/>
<description>
BAbsTime to be written
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeObjectIdentifier(int, int) -->
<method name="writeObjectIdentifier"  public="true" abstract="true">
<description>
Writes an application-tagged object id value to the output stream.&#xa; See Bacnet Clause 20.2.14 for encoding rules.
</description>
<parameter name="objectType">
<type class="int"/>
<description/>
</parameter>
<parameter name="instanceNumber">
<type class="int"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeObjectIdentifier(int, int, int) -->
<method name="writeObjectIdentifier"  public="true" abstract="true">
<description>
Writes a context-tagged object id value to the output stream.&#xa; See Bacnet Clause 20.2.14 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
<description/>
</parameter>
<parameter name="objectType">
<type class="int"/>
<description/>
</parameter>
<parameter name="instanceNumber">
<type class="int"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeObjectIdentifier(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="writeObjectIdentifier"  public="true" abstract="true">
<description>
Writes an application-tagged object id value to the output stream.&#xa; See Bacnet Clause 20.2.14 for encoding rules.
</description>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
object ID to be encoded
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeObjectIdentifier(int, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="writeObjectIdentifier"  public="true" abstract="true">
<description>
Writes a context-tagged object id value to the output stream.&#xa; See Bacnet Clause 20.2.14 for encoding rules.
</description>
<parameter name="contextTag">
<type class="int"/>
<description/>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeEncodedValue(byte[]) -->
<method name="writeEncodedValue"  public="true" abstract="true">
<description>
Write a previously encoded value to the&#xa; data stream, without a tag number.&#xa; &lt;p&gt;&#xa; 2001-08-29:&#xa; This is used for writing priority array elements&#xa; right now.  Perhaps additional uses will emerge...
</description>
<parameter name="encodedValue">
<type class="byte" dimension="1"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeEncodedValue(int, byte[]) -->
<method name="writeEncodedValue"  public="true" abstract="true">
<description>
Writes a previously encoded value to the&#xa; data stream using the given tag number for&#xa; the opening and closing tags.
</description>
<parameter name="tagNumber">
<type class="int"/>
<description>
tag number to use
</description>
</parameter>
<parameter name="encodedValue">
<type class="byte" dimension="1"/>
<description>
array of bytes containing&#xa;                     encoded value
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeOpeningTag(int) -->
<method name="writeOpeningTag"  public="true" abstract="true">
<description>
Writes an opening tag with the given tag&#xa; number to the data stream.&#xa; See Bacnet clause ********.2
</description>
<parameter name="tagNumber">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.writeClosingTag(int) -->
<method name="writeClosingTag"  public="true" abstract="true">
<description>
Writes a closing tag with the given tag&#xa; number to the data stream.&#xa; See Bacnet clause ********.2
</description>
<parameter name="tagNumber">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.AsnOutput.write(byte[]) -->
<method name="write"  public="true" abstract="true">
<description>
Writes an array of bytes to the output stream.&#xa; Overrides write() in OutputStream, which&#xa; throws an IOException.
</description>
<parameter name="array">
<type class="byte" dimension="1"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
