<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>driver Module: nmodule/driver/rc/wb/mgr/PointMgrModel</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">driver</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-nmodule_driver_rc_wb_mgr_DeviceMgr.html">nmodule/driver/rc/wb/mgr/DeviceMgr</a></li><li><a href="module-nmodule_driver_rc_wb_mgr_DeviceMgrModel.html">nmodule/driver/rc/wb/mgr/DeviceMgrModel</a></li><li><a href="module-nmodule_driver_rc_wb_mgr_DriverMgr.html">nmodule/driver/rc/wb/mgr/DriverMgr</a></li><li><a href="module-nmodule_driver_rc_wb_mgr_PointMgr.html">nmodule/driver/rc/wb/mgr/PointMgr</a></li><li><a href="module-nmodule_driver_rc_wb_mgr_PointMgrModel.html">nmodule/driver/rc/wb/mgr/PointMgrModel</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: nmodule/driver/rc/wb/mgr/PointMgrModel</h1>
<section>

<header>
    
        
            
        
    
</header>


<article>
    <div class="container-overview">
    
        

        
            
<hr>
<dt>
    <h4 class="name" id="module:nmodule/driver/rc/wb/mgr/PointMgrModel"><span class="type-signature"></span>new (require("nmodule/driver/rc/wb/mgr/PointMgrModel"))(params)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>API Status: <strong>Development</strong></p>
<p>A <code>MgrModel</code> type for a <code>PointMgr</code> derived type as an agent on a driver's<br>
BPointDeviceExt type.</p>
    </div>
    

    
        <h5>Extends:</h5>
        


    <ul>
        <li>module:nmodule/webEditors/rc/wb/mgr/model/MgrModel</li>
    </ul>


    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>object containing the constructor parameters</p>
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>component</code></td>
            

            <td class="type">
            
                
<span class="param-type">baja.Component</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>the component containing the points to<br>
be shown in the manager, typically a device's point ext or a point folder.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>folderType</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>
|

<span class="param-type">Type</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>optional parameter indicating the folder<br>
type for the manager. This will be used by the 'new folder' command.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id=".addBooleanPointTypes"><span class="type-signature">&lt;static> </span>addBooleanPointTypes(writable, types)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Add the boolean point types to the given array. If the writable parameter<br>
is false, the writable boolean type will not be added.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>writable</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            

            

            <td class="description last"><p>If true, will add the BooleanWritable type to the array.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>types</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array</span>



            
            </td>

            

            

            <td class="description last"><p>An array of that will have the numeric types appended.<br>
If not specified, a new array will be created and returned.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The provided array or a new one containing the appropriate boolean point types.</p>
</div>




        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".addEnumPointTypes"><span class="type-signature">&lt;static> </span>addEnumPointTypes(writable, types)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Add the standard enum point types to the given array. If the writable parameter<br>
is false, the writable enum type will not be added.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>writable</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            

            

            <td class="description last"><p>If true, will add the EnumWritable type to the array.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>types</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array</span>



            
            </td>

            

            

            <td class="description last"><p>An array of that will have the numeric types appended.<br>
If not specified, a new array will be created and returned.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The provided array or a new one containing the appropriate enum point types.</p>
</div>




        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".addNumericPointTypes"><span class="type-signature">&lt;static> </span>addNumericPointTypes(writable, types)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Add the standard numeric point types to the given array. If the writable parameter<br>
is false, the writable numeric type will not be added.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>writable</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            

            

            <td class="description last"><p>If true, will add the NumericWritable type to the array.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>types</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array</span>



            
            </td>

            

            

            <td class="description last"><p>An array of that will have the numeric types appended.<br>
If not specified, a new array will be created and returned.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The provided array or a new one containing the appropriate numeric point types.</p>
</div>




        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".addStringPointTypes"><span class="type-signature">&lt;static> </span>addStringPointTypes(writable, types)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Add the standard numeric point types to the given array. If the writable parameter<br>
is false, the writable numeric type will not be added.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>writable</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            

            

            <td class="description last"><p>If true, will add the StringWritable type to the array.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>types</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array</span>



            
            </td>

            

            

            <td class="description last"><p>An array of that will have the numeric types appended.<br>
If not specified, a new array will be created and returned.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>The provided array or a new one containing the appropriate numeric point types.</p>
</div>




        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".getDefaultNewTypes"><span class="type-signature">&lt;static> </span>getDefaultNewTypes()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return <code>MgrTypeInfo</code> instances for the default new types for a point manager.<br>
This includes writable and non-writable versions of the four basic point<br>
data types (boolean, numeric, enum, string).</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise.&lt;Array.&lt;module:nmodule/webEditors/rc/wb/mgr/MgrTypeInfo>></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getNavDisplayName"><span class="type-signature"></span>getNavDisplayName()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the display name from the display name of the root component container.<br>
This is used for the title of the tab in the HTML5 hx profile.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getProxyExtType"><span class="type-signature"></span>getProxyExtType()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the proxy extension type used by the concrete driver implementation. This<br>
is used by the default implementation of the <code>#newInstance()</code> function.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">string</span>
|

<span class="param-type">Type</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="makeRow"><span class="type-signature"></span>makeRow(subject)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Make a row for the given subject with the appropriate icon for the row. Overrides<br>
TableModel.makeRow().</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>subject</code></td>
            

            <td class="type">
            
            </td>

            

            

            <td class="description last"><p>The subject of the row. Should be a point or folder instance.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">module:nmodule/webEditors/rc/wb/table/model/Row</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="newInstance"><span class="type-signature"></span>newInstance(typeInfo)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Override point to customize how new instances of the selected type spec<br>
are instantiated. The default implementation will create a point and proxy ext<br>
using the type specified by the getProxyExtType() function.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>typeInfo</code></td>
            

            <td class="type">
            
                
<span class="param-type">module:nmodule/webEditors/rc/wb/mgr/MgrTypeInfo</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">baja.Value</span>
|

<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        </dl>
    

    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	driver Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:55+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>