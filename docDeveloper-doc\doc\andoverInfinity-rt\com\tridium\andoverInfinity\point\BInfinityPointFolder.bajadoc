<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.point.BInfinityPointFolder" name="BInfinityPointFolder" packageName="com.tridium.andoverInfinity.point" public="true">
<description>
BInfinityPointFolder
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">Apr 05, 2007</tag>
<tag name="@version">$Revision$ $May 22, 2007 10:00:08 AM$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.BDdfPointFolder"/>
</extends>
<!-- com.tridium.andoverInfinity.point.BInfinityPointFolder() -->
<constructor name="BInfinityPointFolder" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.point.BInfinityPointFolder.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.point.BInfinityPointFolder.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
