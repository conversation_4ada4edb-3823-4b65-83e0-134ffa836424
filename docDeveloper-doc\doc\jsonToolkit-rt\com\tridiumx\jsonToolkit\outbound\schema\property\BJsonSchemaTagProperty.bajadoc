<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagProperty" name="BJsonSchemaTagProperty" packageName="com.tridiumx.jsonToolkit.outbound.schema.property" public="true">
<description>
Allows a single tag value from the bound component to be inserted in the Schema&#xa; output.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<parameterizedType class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaProperty">
<args>
<type class="javax.baja.data.BIDataValue"/>
</args>
</parameterizedType>
</extends>
<property name="binding" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;binding&lt;/code&gt; property.&#xa; This ord is not _bound_ like the other bindings
</description>
<tag name="@see">#getBinding</tag>
<tag name="@see">#setBinding</tag>
</property>

<property name="tagId" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;tagId&lt;/code&gt; property.&#xa; The tag id to use from the binding target
</description>
<tag name="@see">#getTagId</tag>
<tag name="@see">#setTagId</tag>
</property>

<property name="searchParents" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;searchParents&lt;/code&gt; property.&#xa; If true then search up the hierarchy for the closest component with matching tag id (if tag not found on binding target)
</description>
<tag name="@see">#getSearchParents</tag>
<tag name="@see">#setSearchParents</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagProperty() -->
<constructor name="BJsonSchemaTagProperty" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagProperty.getBinding() -->
<method name="getBinding"  public="true">
<description>
Get the &lt;code&gt;binding&lt;/code&gt; property.&#xa; This ord is not _bound_ like the other bindings
</description>
<tag name="@see">#binding</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagProperty.setBinding(javax.baja.naming.BOrd) -->
<method name="setBinding"  public="true">
<description>
Set the &lt;code&gt;binding&lt;/code&gt; property.&#xa; This ord is not _bound_ like the other bindings
</description>
<tag name="@see">#binding</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagProperty.getTagId() -->
<method name="getTagId"  public="true">
<description>
Get the &lt;code&gt;tagId&lt;/code&gt; property.&#xa; The tag id to use from the binding target
</description>
<tag name="@see">#tagId</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagProperty.setTagId(java.lang.String) -->
<method name="setTagId"  public="true">
<description>
Set the &lt;code&gt;tagId&lt;/code&gt; property.&#xa; The tag id to use from the binding target
</description>
<tag name="@see">#tagId</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagProperty.getSearchParents() -->
<method name="getSearchParents"  public="true">
<description>
Get the &lt;code&gt;searchParents&lt;/code&gt; property.&#xa; If true then search up the hierarchy for the closest component with matching tag id (if tag not found on binding target)
</description>
<tag name="@see">#searchParents</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagProperty.setSearchParents(boolean) -->
<method name="setSearchParents"  public="true">
<description>
Set the &lt;code&gt;searchParents&lt;/code&gt; property.&#xa; If true then search up the hierarchy for the closest component with matching tag id (if tag not found on binding target)
</description>
<tag name="@see">#searchParents</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagProperty.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagProperty.make(javax.baja.naming.BOrd, java.lang.String) -->
<method name="make"  public="true" static="true">
<description/>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<parameter name="tagId">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagProperty"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagProperty.getJsonValue() -->
<method name="getJsonValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.data.BIDataValue"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagProperty.binding -->
<field name="binding"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;binding&lt;/code&gt; property.&#xa; This ord is not _bound_ like the other bindings
</description>
<tag name="@see">#getBinding</tag>
<tag name="@see">#setBinding</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagProperty.tagId -->
<field name="tagId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;tagId&lt;/code&gt; property.&#xa; The tag id to use from the binding target
</description>
<tag name="@see">#getTagId</tag>
<tag name="@see">#setTagId</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagProperty.searchParents -->
<field name="searchParents"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;searchParents&lt;/code&gt; property.&#xa; If true then search up the hierarchy for the closest component with matching tag id (if tag not found on binding target)
</description>
<tag name="@see">#getSearchParents</tag>
<tag name="@see">#setSearchParents</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagProperty.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
