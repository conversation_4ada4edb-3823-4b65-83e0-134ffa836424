<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.datatypes.BPupRegionRecord" name="BPupRegionRecord" packageName="com.tridium.aapup.datatypes" public="true">
<description>
BPupRegionRecord is a dynamic slot added to the BPupDevice by the learn regions job
</description>
<tag name="@author">Cli<PERSON></tag>
<tag name="@creation">8/12/2005 2:37PM</tag>
<tag name="@version">$Revision$ $Date: 8/12/2005 2:37PM$</tag>
<tag name="@since">AX 3.0.93</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="unitNumber" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;unitNumber&lt;/code&gt; property.
</description>
<tag name="@see">#getUnitNumber</tag>
<tag name="@see">#setUnitNumber</tag>
</property>

<property name="serialNumber" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;serialNumber&lt;/code&gt; property.
</description>
<tag name="@see">#getSerialNumber</tag>
<tag name="@see">#setSerialNumber</tag>
</property>

<property name="controllerType" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;controllerType&lt;/code&gt; property.
</description>
<tag name="@see">#getControllerType</tag>
<tag name="@see">#setControllerType</tag>
</property>

<property name="firmwareVersion" flags="">
<type class="double"/>
<description>
Slot for the &lt;code&gt;firmwareVersion&lt;/code&gt; property.
</description>
<tag name="@see">#getFirmwareVersion</tag>
<tag name="@see">#setFirmwareVersion</tag>
</property>

<property name="regionNumber" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;regionNumber&lt;/code&gt; property.
</description>
<tag name="@see">#getRegionNumber</tag>
<tag name="@see">#setRegionNumber</tag>
</property>

<property name="regionSize" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;regionSize&lt;/code&gt; property.
</description>
<tag name="@see">#getRegionSize</tag>
<tag name="@see">#setRegionSize</tag>
</property>

<property name="regionType" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;regionType&lt;/code&gt; property.
</description>
<tag name="@see">#getRegionType</tag>
<tag name="@see">#setRegionType</tag>
</property>

<property name="regionName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;regionName&lt;/code&gt; property.
</description>
<tag name="@see">#getRegionName</tag>
<tag name="@see">#setRegionName</tag>
</property>

<property name="regionLock" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;regionLock&lt;/code&gt; property.
</description>
<tag name="@see">#getRegionLock</tag>
<tag name="@see">#setRegionLock</tag>
</property>

<property name="availableBufferSize" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;availableBufferSize&lt;/code&gt; property.
</description>
<tag name="@see">#getAvailableBufferSize</tag>
<tag name="@see">#setAvailableBufferSize</tag>
</property>

</class>
</bajadoc>
