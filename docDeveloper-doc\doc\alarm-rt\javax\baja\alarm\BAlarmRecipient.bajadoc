<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BAlarmRecipient" name="BAlarmRecipient" packageName="javax.baja.alarm" public="true" abstract="true">
<description>
BAlarmRecipient is the super-class of all alarm recipients&#xa; in the Baja framework.  Modeled after an entry in the&#xa; recipient list in a BACnet Notification class.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">19 Feb 01</tag>
<tag name="@version">$Revision: 44$ $Date: 3/10/11 3:10:15 PM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="timeRange" flags="">
<type class="javax.baja.util.BTimeRange"/>
<description>
Slot for the &lt;code&gt;timeRange&lt;/code&gt; property.&#xa; Time during which the recipient will receive alarms.
</description>
<tag name="@see">#getTimeRange</tag>
<tag name="@see">#setTimeRange</tag>
</property>

<property name="daysOfWeek" flags="">
<type class="javax.baja.util.BDaysOfWeekBits"/>
<description>
Slot for the &lt;code&gt;daysOfWeek&lt;/code&gt; property.&#xa; Days during which the recipient will receive alarms.
</description>
<tag name="@see">#getDaysOfWeek</tag>
<tag name="@see">#setDaysOfWeek</tag>
</property>

<property name="transitions" flags="">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
<description>
Slot for the &lt;code&gt;transitions&lt;/code&gt; property.&#xa; Alarm transition types the recipient wishes to receive.
</description>
<tag name="@see">#getTransitions</tag>
<tag name="@see">#setTransitions</tag>
</property>

<property name="routeAcks" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;routeAcks&lt;/code&gt; property.&#xa; Route Alarm Acknowledgements
</description>
<tag name="@see">#getRouteAcks</tag>
<tag name="@see">#setRouteAcks</tag>
</property>

<action name="routeAlarmAck" flags="h">
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;routeAlarmAck&lt;/code&gt; action.&#xa; Route an alarm ack request
</description>
<tag name="@see">#routeAlarmAck(BAlarmRecord parameter)</tag>
</action>

<action name="routeAlarm" flags="s">
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;routeAlarm&lt;/code&gt; action.&#xa; Route an alarm record
</description>
<tag name="@see">#routeAlarm(BAlarmRecord parameter)</tag>
</action>

<topic name="newUnackedAlarm" flags="h">
<eventType>
<type class="javax.baja.alarm.BAlarmRecord"/>
</eventType><description>
Slot for the &lt;code&gt;newUnackedAlarm&lt;/code&gt; topic.
</description>
<tag name="@see">#fireNewUnackedAlarm</tag>
</topic>

<!-- javax.baja.alarm.BAlarmRecipient() -->
<constructor name="BAlarmRecipient" public="true">
<description/>
</constructor>

<!-- javax.baja.alarm.BAlarmRecipient.getTimeRange() -->
<method name="getTimeRange"  public="true">
<description>
Get the &lt;code&gt;timeRange&lt;/code&gt; property.&#xa; Time during which the recipient will receive alarms.
</description>
<tag name="@see">#timeRange</tag>
<return>
<type class="javax.baja.util.BTimeRange"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.setTimeRange(javax.baja.util.BTimeRange) -->
<method name="setTimeRange"  public="true">
<description>
Set the &lt;code&gt;timeRange&lt;/code&gt; property.&#xa; Time during which the recipient will receive alarms.
</description>
<tag name="@see">#timeRange</tag>
<parameter name="v">
<type class="javax.baja.util.BTimeRange"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.getDaysOfWeek() -->
<method name="getDaysOfWeek"  public="true">
<description>
Get the &lt;code&gt;daysOfWeek&lt;/code&gt; property.&#xa; Days during which the recipient will receive alarms.
</description>
<tag name="@see">#daysOfWeek</tag>
<return>
<type class="javax.baja.util.BDaysOfWeekBits"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.setDaysOfWeek(javax.baja.util.BDaysOfWeekBits) -->
<method name="setDaysOfWeek"  public="true">
<description>
Set the &lt;code&gt;daysOfWeek&lt;/code&gt; property.&#xa; Days during which the recipient will receive alarms.
</description>
<tag name="@see">#daysOfWeek</tag>
<parameter name="v">
<type class="javax.baja.util.BDaysOfWeekBits"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.getTransitions() -->
<method name="getTransitions"  public="true">
<description>
Get the &lt;code&gt;transitions&lt;/code&gt; property.&#xa; Alarm transition types the recipient wishes to receive.
</description>
<tag name="@see">#transitions</tag>
<return>
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.setTransitions(javax.baja.alarm.BAlarmTransitionBits) -->
<method name="setTransitions"  public="true">
<description>
Set the &lt;code&gt;transitions&lt;/code&gt; property.&#xa; Alarm transition types the recipient wishes to receive.
</description>
<tag name="@see">#transitions</tag>
<parameter name="v">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.getRouteAcks() -->
<method name="getRouteAcks"  public="true">
<description>
Get the &lt;code&gt;routeAcks&lt;/code&gt; property.&#xa; Route Alarm Acknowledgements
</description>
<tag name="@see">#routeAcks</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.setRouteAcks(boolean) -->
<method name="setRouteAcks"  public="true">
<description>
Set the &lt;code&gt;routeAcks&lt;/code&gt; property.&#xa; Route Alarm Acknowledgements
</description>
<tag name="@see">#routeAcks</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.routeAlarmAck(javax.baja.alarm.BAlarmRecord) -->
<method name="routeAlarmAck"  public="true">
<description>
Invoke the &lt;code&gt;routeAlarmAck&lt;/code&gt; action.&#xa; Route an alarm ack request
</description>
<tag name="@see">#routeAlarmAck</tag>
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.routeAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="routeAlarm"  public="true">
<description>
Invoke the &lt;code&gt;routeAlarm&lt;/code&gt; action.&#xa; Route an alarm record
</description>
<tag name="@see">#routeAlarm</tag>
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.fireNewUnackedAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="fireNewUnackedAlarm"  public="true">
<description>
Fire an event for the &lt;code&gt;newUnackedAlarm&lt;/code&gt; topic.
</description>
<tag name="@see">#newUnackedAlarm</tag>
<parameter name="event">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.doRouteAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="doRouteAlarm"  public="true" final="true">
<description>
Check the timeRange and transition to see if this alarm should be routed,&#xa; and if so, send to the handleAlarm method.
</description>
<parameter name="alarmRecord">
<type class="javax.baja.alarm.BAlarmRecord"/>
<description>
The alarm to route.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.doRouteAlarmAck(javax.baja.alarm.BAlarmRecord) -->
<method name="doRouteAlarmAck"  public="true">
<description>
Route the alarm ack request back to the AlarmService.
</description>
<parameter name="alarmRecord">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.handleAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="handleAlarm"  public="true" abstract="true">
<description>
Handle the AlarmRecipient specific routing of the alarm.
</description>
<parameter name="alarmRecord">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.getSubscribedAlarmClasses() -->
<method name="getSubscribedAlarmClasses"  public="true">
<description>
Get the AlarmClasses linked to this AlarmRecipient.
</description>
<return>
<type class="java.lang.String" dimension="1"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.getSubscribedEscalatedAlarmClasses(int) -->
<method name="getSubscribedEscalatedAlarmClasses"  public="true">
<description>
Get the EscalatedAlarmClasses linked to this AlarmRecipient.&#xa; Valid levels are 1, 2 and 3.
</description>
<parameter name="level">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String" dimension="1"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.accept(javax.baja.alarm.BAlarmRecord) -->
<method name="accept"  public="true">
<description>
Check to see if the alarm falls within the time and day ranges and the transitions.
</description>
<parameter name="rec">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the icon.
</description>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmRecipient.timeRange -->
<field name="timeRange"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;timeRange&lt;/code&gt; property.&#xa; Time during which the recipient will receive alarms.
</description>
<tag name="@see">#getTimeRange</tag>
<tag name="@see">#setTimeRange</tag>
</field>

<!-- javax.baja.alarm.BAlarmRecipient.daysOfWeek -->
<field name="daysOfWeek"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;daysOfWeek&lt;/code&gt; property.&#xa; Days during which the recipient will receive alarms.
</description>
<tag name="@see">#getDaysOfWeek</tag>
<tag name="@see">#setDaysOfWeek</tag>
</field>

<!-- javax.baja.alarm.BAlarmRecipient.transitions -->
<field name="transitions"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;transitions&lt;/code&gt; property.&#xa; Alarm transition types the recipient wishes to receive.
</description>
<tag name="@see">#getTransitions</tag>
<tag name="@see">#setTransitions</tag>
</field>

<!-- javax.baja.alarm.BAlarmRecipient.routeAcks -->
<field name="routeAcks"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;routeAcks&lt;/code&gt; property.&#xa; Route Alarm Acknowledgements
</description>
<tag name="@see">#getRouteAcks</tag>
<tag name="@see">#setRouteAcks</tag>
</field>

<!-- javax.baja.alarm.BAlarmRecipient.routeAlarmAck -->
<field name="routeAlarmAck"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;routeAlarmAck&lt;/code&gt; action.&#xa; Route an alarm ack request
</description>
<tag name="@see">#routeAlarmAck(BAlarmRecord parameter)</tag>
</field>

<!-- javax.baja.alarm.BAlarmRecipient.routeAlarm -->
<field name="routeAlarm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;routeAlarm&lt;/code&gt; action.&#xa; Route an alarm record
</description>
<tag name="@see">#routeAlarm(BAlarmRecord parameter)</tag>
</field>

<!-- javax.baja.alarm.BAlarmRecipient.newUnackedAlarm -->
<field name="newUnackedAlarm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Topic"/>
<description>
Slot for the &lt;code&gt;newUnackedAlarm&lt;/code&gt; topic.
</description>
<tag name="@see">#fireNewUnackedAlarm</tag>
</field>

<!-- javax.baja.alarm.BAlarmRecipient.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
