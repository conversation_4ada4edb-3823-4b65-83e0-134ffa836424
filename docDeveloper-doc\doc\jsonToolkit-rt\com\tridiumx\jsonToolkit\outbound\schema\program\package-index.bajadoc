<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.outbound.schema.program">
<description/>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.program" name="BAbstractInlineJsonWriter"><description>Allows the schema to defer control to a Program in the tree of Schema members, meaning the user can&#xa; do as they please to insert dynamic content in the Schema output.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.program" name="BInlineJsonWriter"><description>Allows the schema to defer control to a Program in the tree of Schema members, meaning the user can&#xa; do as they please to insert dynamic content in the Schema output.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.program" name="BTypeOverride"><description>At the core of the jsonToolkit is a method which maps &lt;code&gt;<see ref="javax.baja.sys.BObject">javax.baja.sys.BObject</see>&lt;/code&gt;&#xa; Types to Json.</description></class>
</package>
</bajadoc>
