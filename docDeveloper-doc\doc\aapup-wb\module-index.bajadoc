<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="aapup" runtimeProfile="wb" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>American AutoMatrix PUP Driver</description>
<package name="com.tridium.aapup.ui.device"/>
<package name="com.tridium.aapup.ui.region"/>
<package name="com.tridium.aapup.ui.point"/>
<class packageName="com.tridium.aapup.ui.device" name="BPupDeviceManager"><description>BPupDeviceManager uses the BAbstractLearn framework to&#xa; provide a way for the user to create PUP Devices.</description></class>
<class packageName="com.tridium.aapup.ui.point" name="BPupPointManager"><description>BPupPointManager uses the BAbstractLearn framework to&#xa; provide a way for the user to create proxy points within&#xa; a BPupDevice.</description></class>
<class packageName="com.tridium.aapup.ui.region" name="BPupRegionManager"><description>BPupRegionManager.</description></class>
</module>
</bajadoc>
