<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionEventFilter" name="SubscriptionEventFilter" packageName="com.tridiumx.jsonToolkit.outbound.schema.subscription" public="true" interface="true" abstract="true" category="interface">
<description>
A filter for subscription events. We do not necessarily want to generate new schema output&#xa; on every single event. The filters chuck away anything which will not directly change the json string and so&#xa; save on processing time / spam.
</description>
<tag name="@author"><PERSON></tag>
<annotation><type class="java.lang.FunctionalInterface"/>
</annotation>
<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionEventFilter.test(javax.baja.sys.BComponentEvent) -->
<method name="test"  public="true" abstract="true">
<description/>
<parameter name="event">
<type class="javax.baja.sys.BComponentEvent"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.support.FilterResult"/>
</return>
</method>

</class>
</bajadoc>
