<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.job.BBacnetDiscoverJob" name="BBacnetDiscoverJob" packageName="com.tridium.bacnet.job" public="true" abstract="true">
<description>
BBacnetDiscoverJob is the task that manages&#xa; discovery in a remote BACnet device.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">20 May 04</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.job.BSimpleJob"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<property name="deviceOrd" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;deviceOrd&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceOrd</tag>
<tag name="@see">#setDeviceOrd</tag>
</property>

</class>
</bajadoc>
