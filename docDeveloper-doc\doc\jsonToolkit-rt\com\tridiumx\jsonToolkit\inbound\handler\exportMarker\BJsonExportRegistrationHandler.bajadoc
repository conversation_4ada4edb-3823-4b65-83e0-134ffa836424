<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportRegistrationHandler" name="BJsonExportRegistrationHandler" packageName="com.tridiumx.jsonToolkit.inbound.handler.exportMarker" public="true">
<description>
Allows the cloud (or other &#x22;external system&#x22;) target to assign it&#x27;s own&#xa; identifier or primary key to export marked points in the Niagara station&#xa; which can be used to locate them in future, or included in exports to that&#xa; cloud system.&#xa;&#xa; {&#xa;   &#x22;messageType&#x22; : &#x22;registerId&#x22;&#xa;   &#x22;niagaraId&#x22; : &#x22;h:a032&#x22;,&#xa;   &#x22;platformId&#x22; : &#x22;mooseForce123&#x22;&#xa; }&#xa;&#xa; Note the messageType is not used by this class, that would be used simply to&#xa; route it to this handler and so can be changed as needed.
</description>
<tag name="@author">Jason Woollard</tag>
<extends>
<type class="com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportDeregistrationHandler"/>
</extends>
<property name="localKey" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;localKey&lt;/code&gt; property.
</description>
<tag name="@see">#getLocalKey</tag>
<tag name="@see">#setLocalKey</tag>
</property>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportRegistrationHandler() -->
<constructor name="BJsonExportRegistrationHandler" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportRegistrationHandler.getLocalKey() -->
<method name="getLocalKey"  public="true">
<description>
Get the &lt;code&gt;localKey&lt;/code&gt; property.
</description>
<tag name="@see">#localKey</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportRegistrationHandler.setLocalKey(java.lang.String) -->
<method name="setLocalKey"  public="true">
<description>
Set the &lt;code&gt;localKey&lt;/code&gt; property.
</description>
<tag name="@see">#localKey</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportRegistrationHandler.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportRegistrationHandler.routeValue(javax.baja.sys.BString, javax.baja.sys.Context) -->
<method name="routeValue"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Apply cloud provided platformId to local ExportMarker
</description>
<parameter name="message">
<type class="javax.baja.sys.BString"/>
<description>
the incoming message to route
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
the current context
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportRegistrationHandler.getRerunTriggers() -->
<method name="getRerunTriggers"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Property" dimension="1"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportRegistrationHandler.localKey -->
<field name="localKey"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;localKey&lt;/code&gt; property.
</description>
<tag name="@see">#getLocalKey</tag>
<tag name="@see">#setLocalKey</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportRegistrationHandler.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
