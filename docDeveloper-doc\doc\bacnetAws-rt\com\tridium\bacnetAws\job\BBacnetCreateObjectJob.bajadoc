<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="com.tridium.bacnetAws.job.BBacnetCreateObjectJob" name="BBacnetCreateObjectJob" packageName="com.tridium.bacnetAws.job" public="true">
<description>
BBacnetCreateObjectJob is used to create objects in a Bacnet device.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">07 June 2010</tag>
<extends>
<type class="com.tridium.bacnet.job.BDeviceManagerJob"/>
</extends>
</class>
</bajadoc>
