<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>bajaux Module: bajaux/events</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">bajaux</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-bajaux_commands_Command.html">bajaux/commands/Command</a></li><li><a href="module-bajaux_commands_CommandGroup.html">bajaux/commands/CommandGroup</a></li><li><a href="module-bajaux_commands_ToggleCommand.html">bajaux/commands/ToggleCommand</a></li><li><a href="module-bajaux_commands_ToggleCommandGroup.html">bajaux/commands/ToggleCommandGroup</a></li><li><a href="module-bajaux_container_wb_Clipboard.html">bajaux/container/wb/Clipboard</a></li><li><a href="module-bajaux_container_wb_StringList.html">bajaux/container/wb/StringList</a></li><li><a href="module-bajaux_dragdrop_dragDropUtils.html">bajaux/dragdrop/dragDropUtils</a></li><li><a href="module-bajaux_dragdrop_Envelope.html">bajaux/dragdrop/Envelope</a></li><li><a href="module-bajaux_dragdrop_NavNodeEnvelope.html">bajaux/dragdrop/NavNodeEnvelope</a></li><li><a href="module-bajaux_dragdrop_StringEnvelope.html">bajaux/dragdrop/StringEnvelope</a></li><li><a href="module-bajaux_events.html">bajaux/events</a></li><li><a href="module-bajaux_icon_iconUtils.html">bajaux/icon/iconUtils</a></li><li><a href="module-bajaux_lifecycle_WidgetManager.html">bajaux/lifecycle/WidgetManager</a></li><li><a href="module-bajaux_mixin_batchLoadMixin.html">bajaux/mixin/batchLoadMixin</a></li><li><a href="module-bajaux_mixin_batchSaveMixin.html">bajaux/mixin/batchSaveMixin</a></li><li><a href="module-bajaux_mixin_responsiveMixIn.html">bajaux/mixin/responsiveMixIn</a></li><li><a href="module-bajaux_mixin_subscriberMixIn.html">bajaux/mixin/subscriberMixIn</a></li><li><a href="module-bajaux_Properties.html">bajaux/Properties</a></li><li><a href="module-bajaux_registry_Registry.html">bajaux/registry/Registry</a></li><li><a href="module-bajaux_registry_RegistryEntry.html">bajaux/registry/RegistryEntry</a></li><li><a href="module-bajaux_spandrel.html">bajaux/spandrel</a></li><li><a href="module-bajaux_spandrel_jsx.html">bajaux/spandrel/jsx</a></li><li><a href="module-bajaux_util_CommandButton.html">bajaux/util/CommandButton</a></li><li><a href="module-bajaux_util_CommandButtonGroup.html">bajaux/util/CommandButtonGroup</a></li><li><a href="module-bajaux_util_SaveCommand.html">bajaux/util/SaveCommand</a></li><li><a href="module-bajaux_Validators.html">bajaux/Validators</a></li><li><a href="module-bajaux_Widget.html">bajaux/Widget</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="interfaces.list.html" class="dropdown-toggle" data-toggle="dropdown">Interfaces<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-bajaux_commands_Command-Undoable.html">bajaux/commands/Command~Undoable</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="tutorials.list.html" class="dropdown-toggle" data-toggle="dropdown">Tutorials<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="tutorial-10-mfw-gettingStarted.html">Getting Started - MyFirstWidget</a></li><li><a href="tutorial-20-mfw-modifying.html">Saving Modifications to Station</a></li><li><a href="tutorial-30-mfw-dashboarding.html">Making your Widget Dashboardable</a></li><li><a href="tutorial-40-tipsAndTricks.html">Tips and Tricks</a></li><li><a href="tutorial-50-spandrel.html">Building Composite Widgets With spandrel</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: bajaux/events</h1>
<section>

<header>
    
</header>


<article>
    <div class="container-overview">
    
        
            <div class="description"><p>A list of all bajaux related events.</p></div>
        

        
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


        
    
    </div>

    

    

    

    

    

    
        <h3 class="subsection-title">Members</h3>

        <dl>
            
<hr>
<dt class="name" id=".DESTROY_EVENT">
    <h4 id=".DESTROY_EVENT"><span class="type-signature">&lt;static> </span>DESTROY_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when a Widget is destroyed.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".DESTROY_FAIL_EVENT">
    <h4 id=".DESTROY_FAIL_EVENT"><span class="type-signature">&lt;static> </span>DESTROY_FAIL_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when Widget is destroyed and its callbacks fail.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".DISABLE_EVENT">
    <h4 id=".DISABLE_EVENT"><span class="type-signature">&lt;static> </span>DISABLE_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when Widget is disabled.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".DISABLE_FAIL_EVENT">
    <h4 id=".DISABLE_FAIL_EVENT"><span class="type-signature">&lt;static> </span>DISABLE_FAIL_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when Widget is disabled and its callbacks fail.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".ENABLE_EVENT">
    <h4 id=".ENABLE_EVENT"><span class="type-signature">&lt;static> </span>ENABLE_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when Widget is enabled.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".ENABLE_FAIL_EVENT">
    <h4 id=".ENABLE_FAIL_EVENT"><span class="type-signature">&lt;static> </span>ENABLE_FAIL_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when Widget is enabled and its callbacks fail.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".FAILURE_EVENTS">
    <h4 id=".FAILURE_EVENTS"><span class="type-signature">&lt;static> </span>FAILURE_EVENTS<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>All of the failure events for bajaux. These events are<br>
fired whenever something has gone wrong.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".INITIALIZE_EVENT">
    <h4 id=".INITIALIZE_EVENT"><span class="type-signature">&lt;static> </span>INITIALIZE_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when Widget initializes.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".INITIALIZE_FAIL_EVENT">
    <h4 id=".INITIALIZE_FAIL_EVENT"><span class="type-signature">&lt;static> </span>INITIALIZE_FAIL_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when Widget tries to initialize, but fails.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".INVALID_EVENT">
    <h4 id=".INVALID_EVENT"><span class="type-signature">&lt;static> </span>INVALID_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when an Widget validates and finds an invalid value.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".LAYOUT_EVENT">
    <h4 id=".LAYOUT_EVENT"><span class="type-signature">&lt;static> </span>LAYOUT_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when the layout has changed.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".LOAD_EVENT">
    <h4 id=".LOAD_EVENT"><span class="type-signature">&lt;static> </span>LOAD_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when an Widget loads a value.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".LOAD_FAIL_EVENT">
    <h4 id=".LOAD_FAIL_EVENT"><span class="type-signature">&lt;static> </span>LOAD_FAIL_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when an Widget tries to load a value, but fails.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".METADATA_CHANGED">
    <h4 id=".METADATA_CHANGED"><span class="type-signature">&lt;static> </span>METADATA_CHANGED<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when a Property's metadata is changed</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".MODIFY_EVENT">
    <h4 id=".MODIFY_EVENT"><span class="type-signature">&lt;static> </span>MODIFY_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when an Widget is modified.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".PROPERTY_ADDED">
    <h4 id=".PROPERTY_ADDED"><span class="type-signature">&lt;static> </span>PROPERTY_ADDED<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when a Property is added</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".PROPERTY_CHANGED">
    <h4 id=".PROPERTY_CHANGED"><span class="type-signature">&lt;static> </span>PROPERTY_CHANGED<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when a Property is changed</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".PROPERTY_REMOVED">
    <h4 id=".PROPERTY_REMOVED"><span class="type-signature">&lt;static> </span>PROPERTY_REMOVED<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when a Property is removed</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".READONLY_EVENT">
    <h4 id=".READONLY_EVENT"><span class="type-signature">&lt;static> </span>READONLY_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when an Widget is made readonly.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".READONLY_FAIL_EVENT">
    <h4 id=".READONLY_FAIL_EVENT"><span class="type-signature">&lt;static> </span>READONLY_FAIL_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when an Widget is made readonly and its callbacks fail.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".SAVE_EVENT">
    <h4 id=".SAVE_EVENT"><span class="type-signature">&lt;static> </span>SAVE_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when an Widget saves.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".SAVE_FAIL_EVENT">
    <h4 id=".SAVE_FAIL_EVENT"><span class="type-signature">&lt;static> </span>SAVE_FAIL_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when an Widget tries to save, but fails.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".UNMODIFY_EVENT">
    <h4 id=".UNMODIFY_EVENT"><span class="type-signature">&lt;static> </span>UNMODIFY_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when an Widget is unmodified.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".VALID_EVENT">
    <h4 id=".VALID_EVENT"><span class="type-signature">&lt;static> </span>VALID_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when an Widget validates and finds a valid value.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".VALIDATORS_MODIFIED">
    <h4 id=".VALIDATORS_MODIFIED"><span class="type-signature">&lt;static> </span>VALIDATORS_MODIFIED<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when an Widget's validators are modified.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".WRITABLE_EVENT">
    <h4 id=".WRITABLE_EVENT"><span class="type-signature">&lt;static> </span>WRITABLE_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when an Widget is made writable.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        
            
<hr>
<dt class="name" id=".WRITABLE_FAIL_EVENT">
    <h4 id=".WRITABLE_FAIL_EVENT"><span class="type-signature">&lt;static> </span>WRITABLE_FAIL_EVENT<span class="type-signature"></span></h4>

    
</dt>
<dd>
    
    <div class="description">
        <p>Triggers when an Widget is made writable and its callbacks fail.</p>
    </div>
    

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    
</dd>

        </dl>
    

    

    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	bajaux Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:53+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>