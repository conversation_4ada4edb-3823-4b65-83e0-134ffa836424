<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField" name="BMetadataField" packageName="com.tridiumx.jsonToolkit.outbound.schema.config" public="true" final="true">
<description>
Different metadata fields supported in json metadata bindings.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@since">Niagara 4.10</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;name&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;displayName&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;slotPath&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;handle&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;typeName&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;typeSpec&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;parentsName&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deviceName&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;nearestFolderName&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;historyName&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;historyId&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;toString&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
<elementValue name="defaultValue">
<annotationValue kind="expr">
<expression>&#x22;name&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.NAME -->
<field name="NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for name.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.DISPLAY_NAME -->
<field name="DISPLAY_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for displayName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.SLOT_PATH -->
<field name="SLOT_PATH"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for slotPath.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.HANDLE -->
<field name="HANDLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for handle.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.TYPE_NAME -->
<field name="TYPE_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for typeName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.TYPE_SPEC -->
<field name="TYPE_SPEC"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for typeSpec.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.PARENTS_NAME -->
<field name="PARENTS_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for parentsName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.DEVICE_NAME -->
<field name="DEVICE_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deviceName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.NEAREST_FOLDER_NAME -->
<field name="NEAREST_FOLDER_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for nearestFolderName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.HISTORY_NAME -->
<field name="HISTORY_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for historyName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.HISTORY_ID -->
<field name="HISTORY_ID"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for historyId.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.TO_STRING -->
<field name="TO_STRING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for toString.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.name -->
<field name="name"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
<description>
BMetadataField constant for name.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.displayName -->
<field name="displayName"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
<description>
BMetadataField constant for displayName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.slotPath -->
<field name="slotPath"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
<description>
BMetadataField constant for slotPath.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.handle -->
<field name="handle"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
<description>
BMetadataField constant for handle.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.typeName -->
<field name="typeName"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
<description>
BMetadataField constant for typeName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.typeSpec -->
<field name="typeSpec"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
<description>
BMetadataField constant for typeSpec.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.parentsName -->
<field name="parentsName"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
<description>
BMetadataField constant for parentsName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.deviceName -->
<field name="deviceName"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
<description>
BMetadataField constant for deviceName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.nearestFolderName -->
<field name="nearestFolderName"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
<description>
BMetadataField constant for nearestFolderName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.historyName -->
<field name="historyName"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
<description>
BMetadataField constant for historyName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.historyId -->
<field name="historyId"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
<description>
BMetadataField constant for historyId.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.toString -->
<field name="toString"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
<description>
BMetadataField constant for toString.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BMetadataField.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
