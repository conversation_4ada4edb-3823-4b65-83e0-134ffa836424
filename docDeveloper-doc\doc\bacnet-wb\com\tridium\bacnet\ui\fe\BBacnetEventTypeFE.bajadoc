<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="wb" qualifiedName="com.tridium.bacnet.ui.fe.BBacnetEventTypeFE" name="BBacnetEventTypeFE" packageName="com.tridium.bacnet.ui.fe" public="true">
<description>
BBacnetEventTypeFE allows the user to manage&#xa; the event parameters of an event enrollment object
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">13 Apr 2016</tag>
<tag name="@since">Niagara 3 Bacnet *********</tag>
<extends>
<type class="javax.baja.workbench.fieldeditor.BWbFieldEditor"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<action name="eventTypeChanged" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;eventTypeChanged&lt;/code&gt; action.
</description>
<tag name="@see">#eventTypeChanged()</tag>
</action>

</class>
</bajadoc>
