<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.device.overrides.DeviceOverride" name="DeviceOverride" packageName="javax.baja.bacnet.device.overrides" public="true" interface="true" abstract="true" category="interface">
<description>
Marker interface to support DeviceOverrideAware&#xa; components.&#xa; &lt;p&gt;&#xa; Device overrides enable the Niagara Integrator to&#xa; instruct the Niagara BACnet driver to behave abnormally.&#xa; &lt;p&gt;&#xa; The device override family of interfaces provide a&#xa; mechanism to persist these temporary abnormalities.&#xa; &lt;p&gt;&#xa; Once the device has been updated these overrides should be&#xa; removed from the device.&#xa; &lt;p&gt;&#xa; The DeviceOverride interface provides override components&#xa; visibility into the parent&#x27;s device object.
</description>
<tag name="@author">Joseph Chandler</tag>
</class>
</bajadoc>
