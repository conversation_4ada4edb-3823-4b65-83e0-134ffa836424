<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnetAws" runtimeProfile="rt" name="com.tridium.bacnetAws">
<description/>
<class packageName="com.tridium.bacnetAws" name="BBacnetAwsDevice"><description>BBacnetDevice represents the Baja shadow object for a Bacnet device under a BacnetAwsNetwork.</description></class>
<class packageName="com.tridium.bacnetAws" name="BBacnetAwsDeviceFolder"><description>BBacnetAwsDeviceFolder.</description></class>
<class packageName="com.tridium.bacnetAws" name="BBacnetAwsNetwork"><description>BBacnetAwsNetwork is the base container for the Tridium Bacnet Advanced Workstation&#xa; service.</description></class>
<class packageName="com.tridium.bacnetAws" name="BLocalBacnetAwsDevice"><description>BLocalBacnetAwsDevice is the representation of Niagara as a Bacnet&#xa; device on the Bacnet internetwork.</description></class>
</package>
</bajadoc>
