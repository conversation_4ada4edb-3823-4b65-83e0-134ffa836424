<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.collection.ColumnList" name="ColumnList" packageName="javax.baja.collection" public="true" interface="true" abstract="true" category="interface">
<description>
A list of columns for a &lt;code&gt;<see ref="javax.baja.collection.BITable">BITable</see>&lt;/code&gt;
</description>
<tag name="@author">&lt;a href=&#x22;mailto:<EMAIL>&#x22;&gt;<PERSON>&lt;/a&gt;</tag>
<tag name="@author">&lt;a href=&#x22;mailto:<EMAIL>&#x22;&gt;<PERSON>&lt;/a&gt;</tag>
<!-- javax.baja.collection.ColumnList.size() -->
<method name="size"  public="true" abstract="true">
<description>
Get the number of columns in the list.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.collection.ColumnList.get(int) -->
<method name="get"  public="true" abstract="true">
<description>
Get the column at the specified index.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.collection.Column"/>
</return>
</method>

<!-- javax.baja.collection.ColumnList.get(java.lang.String) -->
<method name="get"  public="true" abstract="true">
<description>
Get the column with the specified name.
</description>
<parameter name="name">
<type class="java.lang.String"/>
<description>
The name of the target column.
</description>
</parameter>
<return>
<type class="javax.baja.collection.Column"/>
<description>
Return the column having the specified name,&#xa; or null if no such column exists.
</description>
</return>
</method>

<!-- javax.baja.collection.ColumnList.indexOf(java.lang.String) -->
<method name="indexOf"  public="true" abstract="true">
<description>
Get the index of the column with the specified name.
</description>
<parameter name="name">
<type class="java.lang.String"/>
<description>
The name of the target column.
</description>
</parameter>
<return>
<type class="int"/>
<description>
Return the column having the specified name,&#xa; or -1 if no such column exists.
</description>
</return>
</method>

<!-- javax.baja.collection.ColumnList.list() -->
<method name="list"  public="true" abstract="true">
<description>
Get an array of the columns in the list.
</description>
<return>
<type class="javax.baja.collection.Column" dimension="1"/>
</return>
</method>

</class>
</bajadoc>
