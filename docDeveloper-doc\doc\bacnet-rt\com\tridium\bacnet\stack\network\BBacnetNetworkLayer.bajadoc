<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.network.BBacnetNetworkLayer" name="BBacnetNetworkLayer" packageName="com.tridium.bacnet.stack.network" public="true">
<description>
Tridium Network Layer Implementation.&lt;p&gt;&#xa; BBacnetNetworkLayer implements the BACnet network layer described&#xa; in Clause 6 of the BACnet specification.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">19 Apr 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="com.tridium.bacnet.stack.network.NetworkMsgType"/>
</implements>
<implements>
<type class="com.tridium.bacnet.timers.TimerListener"/>
</implements>
<property name="routerTable" flags="">
<type class="com.tridium.bacnet.stack.network.BBacnetRouterTable"/>
<description>
Slot for the &lt;code&gt;routerTable&lt;/code&gt; property.
</description>
<tag name="@see">#getRouterTable</tag>
<tag name="@see">#setRouterTable</tag>
</property>

<property name="ipPort" flags="s">
<type class="com.tridium.bacnet.stack.network.BNetworkPort"/>
<description>
Slot for the &lt;code&gt;ipPort&lt;/code&gt; property.
</description>
<tag name="@see">#getIpPort</tag>
<tag name="@see">#setIpPort</tag>
</property>

<property name="routingEnabled" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;routingEnabled&lt;/code&gt; property.&#xa; Routing by default is enabled.  If received messages indicate a possible&#xa; network misconfiguration, routing will be disabled to prevent message&#xa; floods due to situations like router loops, etc.  When the problem has&#xa; been fixed, routing can be reenabled.
</description>
<tag name="@see">#getRoutingEnabled</tag>
<tag name="@see">#setRoutingEnabled</tag>
</property>

<property name="maintainRoutingEnabled" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;maintainRoutingEnabled&lt;/code&gt; property.&#xa; Allows routing to remain enabled under misconfigurations.  This is useful&#xa; for installations where the JACE needs to continue to function without&#xa; intervention.  The user fixes the network misconfiguration and the JACE&#xa; will pick back up.
</description>
<tag name="@see">#getMaintainRoutingEnabled</tag>
<tag name="@see">#setMaintainRoutingEnabled</tag>
</property>

<property name="minimumRouterUpdateTime" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;minimumRouterUpdateTime&lt;/code&gt; property.&#xa; minimum time between router table updates.  This is useful in detecting&#xa; network misconfigurations.  If a router loop exists, and a message&#xa; causing a table update is sent, the routers in the loop will continue&#xa; to forward the message amongst themselves until the hop count is exhausted.&#xa; This rapid flood of messages will violate the minimumRouterUpdateTime,&#xa; and allow Niagara to detect the misconfiguration.  Routing will then&#xa; be disabled, but can be re-enabled when the misconfiguration has been&#xa; corrected.
</description>
<tag name="@see">#getMinimumRouterUpdateTime</tag>
<tag name="@see">#setMinimumRouterUpdateTime</tag>
</property>

<property name="routerDiscoveryTimeout" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;routerDiscoveryTimeout&lt;/code&gt; property.&#xa; time that Niagara will wait for an attempt to discover an unknown router&#xa; to succeed before giving up on a message.
</description>
<tag name="@see">#getRouterDiscoveryTimeout</tag>
<tag name="@see">#setRouterDiscoveryTimeout</tag>
</property>

<property name="terminationTimeValue" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;terminationTimeValue&lt;/code&gt; property.&#xa; time that Niagara will request that a PTP connection remain open, in&#xa; the absence of any packet traffic.
</description>
<tag name="@see">#getTerminationTimeValue</tag>
<tag name="@see">#setTerminationTimeValue</tag>
</property>

<action name="sendNetworkNumberIs" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;sendNetworkNumberIs&lt;/code&gt; action.
</description>
<tag name="@see">#sendNetworkNumberIs()</tag>
</action>

<action name="whatIsNetworkNumber" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;whatIsNetworkNumber&lt;/code&gt; action.
</description>
<tag name="@see">#whatIsNetworkNumber()</tag>
</action>

</class>
</bajadoc>
