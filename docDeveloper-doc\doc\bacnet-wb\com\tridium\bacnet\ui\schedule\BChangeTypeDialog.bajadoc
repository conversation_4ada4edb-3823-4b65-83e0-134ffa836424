<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="wb" qualifiedName="com.tridium.bacnet.ui.schedule.BChangeTypeDialog" name="BChangeTypeDialog" packageName="com.tridium.bacnet.ui.schedule" public="true">
<description>
BChangeTypePane
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">10 Aug 2010</tag>
<tag name="@version">$Revision: 14$ $Date: 10/17/00 12:47:14 PM$</tag>
<tag name="@since">Niagara 3.6</tag>
<extends>
<type class="javax.baja.ui.BDialog"/>
</extends>
<action name="typeChanged" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;typeChanged&lt;/code&gt; action.
</description>
<tag name="@see">#typeChanged()</tag>
</action>

<action name="oprChanged" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;oprChanged&lt;/code&gt; action.
</description>
<tag name="@see">#oprChanged()</tag>
</action>

<action name="superChanged" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;superChanged&lt;/code&gt; action.
</description>
<tag name="@see">#superChanged()</tag>
</action>

<action name="defaultChanged" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;defaultChanged&lt;/code&gt; action.
</description>
<tag name="@see">#defaultChanged()</tag>
</action>

<action name="weeklyChanged" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;weeklyChanged&lt;/code&gt; action.
</description>
<tag name="@see">#weeklyChanged()</tag>
</action>

<action name="exceptionChanged" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;exceptionChanged&lt;/code&gt; action.
</description>
<tag name="@see">#exceptionChanged()</tag>
</action>

<action name="buttonPressed" flags="h">
<parameter name="parameter">
<type class="javax.baja.ui.event.BWidgetEvent"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;buttonPressed&lt;/code&gt; action.
</description>
<tag name="@see">#buttonPressed(BWidgetEvent parameter)</tag>
</action>

</class>
</bajadoc>
