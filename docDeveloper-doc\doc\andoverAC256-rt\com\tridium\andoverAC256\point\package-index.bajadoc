<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="andoverAC256" runtimeProfile="rt" name="com.tridium.andoverAC256.point">
<description/>
<class packageName="com.tridium.andoverAC256.point" name="BAndoverBooleanProxyExt"><description>BAndoverBooleanProxyExt represents a single binary value&#xa; from an Andover AC256 system.</description></class>
<class packageName="com.tridium.andoverAC256.point" name="BAndoverEnumProxyExt"><description>BAndoverTriStateProxyExt represents a single tri-state value&#xa; from an Andover AC256 system.</description></class>
<class packageName="com.tridium.andoverAC256.point" name="BAndoverNumericProxyExt"><description>BAndoverNumericProxyExt represents a single binary value&#xa; from an Andover AC256 system.</description></class>
<class packageName="com.tridium.andoverAC256.point" name="BAndoverPointDeviceExt"><description>AndoverAC256 implementation of BPointDeviceExt</description></class>
<class packageName="com.tridium.andoverAC256.point" name="BAndoverPointFolder"><description>AndoverAC256 implementation of BPointFolder</description></class>
<class packageName="com.tridium.andoverAC256.point" name="BAndoverProxyExt"><description>AndoverAC256 implementation of BProxyExt</description></class>
<class packageName="com.tridium.andoverAC256.point" name="BAndoverStringProxyExt"><description>BAndoverBooleanProxyExt represents a single string value&#xa; from an Andover AC256 system.</description></class>
</package>
</bajadoc>
