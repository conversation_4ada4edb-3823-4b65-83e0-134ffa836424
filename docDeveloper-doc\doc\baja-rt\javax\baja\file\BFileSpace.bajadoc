<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BFileSpace" name="BFileSpace" packageName="javax.baja.file" public="true" abstract="true">
<description>
BFileSpace defines a tree of BIFiles using a specific&#xa; implementation of BIFilesStore.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">24 Jan 03</tag>
<tag name="@version">$Revision: 26$ $Date: 10/28/10 2:47:00 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.space.BSpace"/>
</extends>
<implements>
<type class="javax.baja.file.BIFileSpace"/>
</implements>
<implements>
<type class="javax.baja.file.BIDirectory"/>
</implements>
<implements>
<type class="javax.baja.category.BICategorizable"/>
</implements>
<implements>
<type class="javax.baja.security.BIProtected"/>
</implements>
<!-- javax.baja.file.BFileSpace(java.lang.String, javax.baja.util.LexiconText) -->
<constructor name="BFileSpace" public="true">
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="lexText">
<type class="javax.baja.util.LexiconText"/>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.file.BFileSpace(java.lang.String) -->
<constructor name="BFileSpace" public="true">
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.file.BFileSpace.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.makeDir(javax.baja.file.FilePath, javax.baja.sys.Context) -->
<method name="makeDir"  public="true" abstract="true">
<description>
Make a directory for the specified path or return&#xa; the existing directory.  This creates zero or more&#xa; directories as needed.  Check security permissions&#xa; if context maps to a user.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.file.BDirectory"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BFileSpace.makeDir(javax.baja.file.FilePath) -->
<method name="makeDir"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Convenience for &lt;code&gt;makeDir(path, null)&lt;/code&gt;
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.BDirectory"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BFileSpace.makeFile(javax.baja.file.FilePath, javax.baja.sys.Context) -->
<method name="makeFile"  public="true" abstract="true">
<description>
Make a file for the specified path or return the&#xa; existing file.  This creates zero or more directories&#xa; as needed.  Check security permissions if context&#xa; maps to a user.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BFileSpace.makeFile(javax.baja.file.FilePath) -->
<method name="makeFile"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Convenience for &lt;code&gt;makeFile(path, null)&lt;/code&gt;
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BFileSpace.move(javax.baja.file.FilePath, javax.baja.file.FilePath, javax.baja.sys.Context) -->
<method name="move"  public="true" abstract="true">
<description>
Move/rename the specified file.  If the &#x22;to&#x22; path is not&#xa; absolute, then it is relative to the from.getParent().&#xa; Check security permissions if context maps to a user.
</description>
<parameter name="from">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="to">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BFileSpace.move(javax.baja.file.FilePath, javax.baja.file.FilePath) -->
<method name="move"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Convenience for &lt;code&gt;move(from, to, null)&lt;/code&gt;
</description>
<parameter name="from">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="to">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BFileSpace.delete(javax.baja.file.FilePath, javax.baja.sys.Context) -->
<method name="delete"  public="true" abstract="true">
<description>
Recursively delete the specified file.  Ignore&#xa; the call if the path doesn&#x27;t exist.  Check security&#xa; permissions if context maps to a user.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BFileSpace.delete(javax.baja.file.FilePath) -->
<method name="delete"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Convenience for &lt;code&gt;delete(path, null)&lt;/code&gt;
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BFileSpace.getAbsoluteOrd(javax.baja.file.FilePath) -->
<method name="getAbsoluteOrd"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get an absolute ord for a file path within this space.
</description>
<parameter name="filePath">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.getOrdInHost(javax.baja.file.FilePath) -->
<method name="getOrdInHost"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get an ord relative to the host for a file path within this space.
</description>
<parameter name="filePath">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.getOrdInSession(javax.baja.file.FilePath) -->
<method name="getOrdInSession"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get an ord relative to the session for a file path within this space.
</description>
<parameter name="filePath">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.appendFilePathToOrd(javax.baja.naming.BOrd, javax.baja.file.FilePath) -->
<method name="appendFilePathToOrd"  protected="true">
<description>
Join a file space ord with the given file path.  This relies on&#xa; the fact that the a file space ord should not end with slash, and&#xa; a filepath should.
</description>
<parameter name="baseOrd">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<parameter name="filePath">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.findFile(javax.baja.file.FilePath) -->
<method name="findFile"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Routes to findStore() and makeFile().
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.findStore(javax.baja.file.FilePath) -->
<method name="findStore"  public="true" abstract="true">
<description>
Map a FilePath to an instanceof of BIFileStore.  If the&#xa; path doesn&#x27;t map to a file in this space, then return null.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.BIFileStore"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.resolveFile(javax.baja.file.FilePath) -->
<method name="resolveFile"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
This method calls findFile(path).  If null is&#xa; returned then UnresolvedException() is thrown.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.getChild(javax.baja.file.BIFile, java.lang.String) -->
<method name="getChild"  public="true" abstract="true">
<description>
Get the child file of the specified parent or&#xa; return null if not found.
</description>
<parameter name="parent">
<type class="javax.baja.file.BIFile"/>
</parameter>
<parameter name="childName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.getChildren(javax.baja.file.BIFile) -->
<method name="getChildren"  public="true" abstract="true">
<description>
Get the children files of the specified parent&#xa; or return an empty array.
</description>
<parameter name="parent">
<type class="javax.baja.file.BIFile"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile" dimension="1"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.makeFile(javax.baja.file.BIFileStore) -->
<method name="makeFile"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Given an implementation of file store, create the proper&#xa; type of BIFile.  The standard implementation of this method&#xa; uses the registry to map the store&#x27;s file extension to&#xa; a type of BIFile.  Once the instance is created, the store&#xa; is set using BIFile.setStore().  If the store is a directory&#xa; then return an instance of BDirectory.  If the file has&#xa; no extension, then return an instance of BDataFile.
</description>
<parameter name="store">
<type class="javax.baja.file.BIFileStore"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.getPermissionsFor(javax.baja.file.FilePath, javax.baja.sys.Context) -->
<method name="getPermissionsFor"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the permissions for a FilePath.  This method works&#xa; for both existing paths and non-existing paths making&#xa; it useful to precheck operations which are going to create&#xa; new files.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.security.BPermissions"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.checkReadPermission(javax.baja.file.FilePath, javax.baja.sys.Context) -->
<method name="checkReadPermission"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
If the result of &lt;code&gt;getPermissionsFor(path, cx)&lt;/code&gt;&#xa; doesn&#x27;t contain operator read then throw a PermissionException.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.checkWritePermission(javax.baja.file.FilePath, javax.baja.sys.Context) -->
<method name="checkWritePermission"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
If the result of &lt;code&gt;getPermissionsFor(path, cx)&lt;/code&gt;&#xa; doesn&#x27;t contain operator write then throw a PermissionException.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.getCategoryMask() -->
<method name="getCategoryMask"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
FileSpaces are mapped to categories by ord in &lt;code&gt;CategoryService.ordMap&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.getAppliedCategoryMask() -->
<method name="getAppliedCategoryMask"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
FileSpaces are mapped to categories by ord in &lt;code&gt;CategoryService.ordMap&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.getPermissions(javax.baja.sys.Context) -->
<method name="getPermissions"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.security.BPermissions"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.canRead(javax.baja.naming.OrdTarget) -->
<method name="canRead"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.canWrite(javax.baja.naming.OrdTarget) -->
<method name="canWrite"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.canInvoke(javax.baja.naming.OrdTarget) -->
<method name="canInvoke"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.getAgents(javax.baja.sys.Context) -->
<method name="getAgents"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.agent.AgentList"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.file.BFileSpace.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
