<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="app" runtimeProfile="rt" qualifiedName="javax.baja.web.app.BIWebApp" name="BIWebApp" packageName="javax.baja.web.app" public="true" interface="true" abstract="true" category="interface">
<description>
Interface for a Web App&#xa; &lt;p&gt;&#xa; This interface should only be implemented on classes that extend BApp
</description>
<tag name="@author">g<PERSON><PERSON><PERSON></tag>
<tag name="@creation">28 Jul 2011</tag>
<tag name="@version">1</tag>
<tag name="@since">Niagara 3.7</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<!-- javax.baja.web.app.BIWebApp.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
