<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.ui.BAlarmTitlePane" name="BAlarmTitlePane" packageName="com.tridium.alarm.ui" public="true">
<description>
BAlarmTilePane
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">27 Jan 05</tag>
<tag name="@version">$Revision: 3$ $Date: 6/15/10 2:14:14 PM EDT$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="javax.baja.ui.pane.BEdgePane"/>
</extends>
<action name="tableModified" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;tableModified&lt;/code&gt; action.
</description>
<tag name="@see">#tableModified()</tag>
</action>

</class>
</bajadoc>
