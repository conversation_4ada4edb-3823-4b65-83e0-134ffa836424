<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="javax.baja.bacnetAws.config.BBacnetPulseConverter" name="BBacnetPulseConverter" packageName="javax.baja.bacnetAws.config" public="true">
<description/>
<extends>
<type class="javax.baja.bacnet.config.BBacnetAnalog"/>
</extends>
<property name="scaleFactor" flags="">
<type class="float"/>
<description>
Slot for the &lt;code&gt;scaleFactor&lt;/code&gt; property.
</description>
<tag name="@see">#getScaleFactor</tag>
<tag name="@see">#setScaleFactor</tag>
</property>

<property name="adjustValue" flags="">
<type class="float"/>
<description>
Slot for the &lt;code&gt;adjustValue&lt;/code&gt; property.
</description>
<tag name="@see">#getAdjustValue</tag>
<tag name="@see">#setAdjustValue</tag>
</property>

<property name="count" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;count&lt;/code&gt; property.
</description>
<tag name="@see">#getCount</tag>
<tag name="@see">#setCount</tag>
</property>

<property name="updateTime" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
<description>
Slot for the &lt;code&gt;updateTime&lt;/code&gt; property.
</description>
<tag name="@see">#getUpdateTime</tag>
<tag name="@see">#setUpdateTime</tag>
</property>

<property name="countChangeTime" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
<description>
Slot for the &lt;code&gt;countChangeTime&lt;/code&gt; property.
</description>
<tag name="@see">#getCountChangeTime</tag>
<tag name="@see">#setCountChangeTime</tag>
</property>

<property name="countBeforeChange" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;countBeforeChange&lt;/code&gt; property.
</description>
<tag name="@see">#getCountBeforeChange</tag>
<tag name="@see">#setCountBeforeChange</tag>
</property>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter() -->
<constructor name="BBacnetPulseConverter" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.getScaleFactor() -->
<method name="getScaleFactor"  public="true">
<description>
Get the &lt;code&gt;scaleFactor&lt;/code&gt; property.
</description>
<tag name="@see">#scaleFactor</tag>
<return>
<type class="float"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.setScaleFactor(float) -->
<method name="setScaleFactor"  public="true">
<description>
Set the &lt;code&gt;scaleFactor&lt;/code&gt; property.
</description>
<tag name="@see">#scaleFactor</tag>
<parameter name="v">
<type class="float"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.getAdjustValue() -->
<method name="getAdjustValue"  public="true">
<description>
Get the &lt;code&gt;adjustValue&lt;/code&gt; property.
</description>
<tag name="@see">#adjustValue</tag>
<return>
<type class="float"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.setAdjustValue(float) -->
<method name="setAdjustValue"  public="true">
<description>
Set the &lt;code&gt;adjustValue&lt;/code&gt; property.
</description>
<tag name="@see">#adjustValue</tag>
<parameter name="v">
<type class="float"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.getCount() -->
<method name="getCount"  public="true">
<description>
Get the &lt;code&gt;count&lt;/code&gt; property.
</description>
<tag name="@see">#count</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.setCount(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setCount"  public="true">
<description>
Set the &lt;code&gt;count&lt;/code&gt; property.
</description>
<tag name="@see">#count</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.getUpdateTime() -->
<method name="getUpdateTime"  public="true">
<description>
Get the &lt;code&gt;updateTime&lt;/code&gt; property.
</description>
<tag name="@see">#updateTime</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.setUpdateTime(javax.baja.bacnet.datatypes.BBacnetDateTime) -->
<method name="setUpdateTime"  public="true">
<description>
Set the &lt;code&gt;updateTime&lt;/code&gt; property.
</description>
<tag name="@see">#updateTime</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.getCountChangeTime() -->
<method name="getCountChangeTime"  public="true">
<description>
Get the &lt;code&gt;countChangeTime&lt;/code&gt; property.
</description>
<tag name="@see">#countChangeTime</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.setCountChangeTime(javax.baja.bacnet.datatypes.BBacnetDateTime) -->
<method name="setCountChangeTime"  public="true">
<description>
Set the &lt;code&gt;countChangeTime&lt;/code&gt; property.
</description>
<tag name="@see">#countChangeTime</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.getCountBeforeChange() -->
<method name="getCountBeforeChange"  public="true">
<description>
Get the &lt;code&gt;countBeforeChange&lt;/code&gt; property.
</description>
<tag name="@see">#countBeforeChange</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.setCountBeforeChange(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setCountBeforeChange"  public="true">
<description>
Set the &lt;code&gt;countBeforeChange&lt;/code&gt; property.
</description>
<tag name="@see">#countBeforeChange</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.scaleFactor -->
<field name="scaleFactor"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;scaleFactor&lt;/code&gt; property.
</description>
<tag name="@see">#getScaleFactor</tag>
<tag name="@see">#setScaleFactor</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.adjustValue -->
<field name="adjustValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;adjustValue&lt;/code&gt; property.
</description>
<tag name="@see">#getAdjustValue</tag>
<tag name="@see">#setAdjustValue</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.count -->
<field name="count"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;count&lt;/code&gt; property.
</description>
<tag name="@see">#getCount</tag>
<tag name="@see">#setCount</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.updateTime -->
<field name="updateTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;updateTime&lt;/code&gt; property.
</description>
<tag name="@see">#getUpdateTime</tag>
<tag name="@see">#setUpdateTime</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.countChangeTime -->
<field name="countChangeTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;countChangeTime&lt;/code&gt; property.
</description>
<tag name="@see">#getCountChangeTime</tag>
<tag name="@see">#setCountChangeTime</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.countBeforeChange -->
<field name="countBeforeChange"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;countBeforeChange&lt;/code&gt; property.
</description>
<tag name="@see">#getCountBeforeChange</tag>
<tag name="@see">#setCountBeforeChange</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetPulseConverter.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
