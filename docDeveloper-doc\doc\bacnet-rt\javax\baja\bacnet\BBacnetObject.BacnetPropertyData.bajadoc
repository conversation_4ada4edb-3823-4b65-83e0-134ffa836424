<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.BBacnetObject$BacnetPropertyData" name="BBacnetObject.BacnetPropertyData" packageName="javax.baja.bacnet" public="true" static="true" innerClass="true">
<description/>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- javax.baja.bacnet.BBacnetObject.BacnetPropertyData.getPropertyId() -->
<method name="getPropertyId"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.BacnetPropertyData.getAsnType() -->
<method name="getAsnType"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

</class>
</bajadoc>
