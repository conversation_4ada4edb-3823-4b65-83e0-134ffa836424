<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.util.SpecialEventDetails" name="SpecialEventDetails" packageName="javax.baja.bacnet.util" public="true">
<description/>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- javax.baja.bacnet.util.SpecialEventDetails(javax.baja.schedule.BAbstractSchedule, java.util.List&lt;javax.baja.schedule.BTimeSchedule&gt;) -->
<constructor name="SpecialEventDetails" public="true">
<parameter name="daysSchedule">
<type class="javax.baja.schedule.BAbstractSchedule"/>
</parameter>
<parameter name="timeSchedules">
<parameterizedType class="java.util.List">
<args>
<type class="javax.baja.schedule.BTimeSchedule"/>
</args>
</parameterizedType>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.util.SpecialEventDetails.getDaysSchedule() -->
<method name="getDaysSchedule"  public="true">
<description/>
<return>
<type class="javax.baja.schedule.BAbstractSchedule"/>
</return>
</method>

<!-- javax.baja.bacnet.util.SpecialEventDetails.getTimeSchedules() -->
<method name="getTimeSchedules"  public="true">
<description/>
<return>
<parameterizedType class="java.util.List">
<args>
<type class="javax.baja.schedule.BTimeSchedule"/>
</args>
</parameterizedType>
</return>
</method>

</class>
</bajadoc>
