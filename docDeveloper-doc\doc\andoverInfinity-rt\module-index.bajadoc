<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="andoverInfinity" runtimeProfile="rt" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>Andover Infinity Driver</description>
<package name="com.tridium.andoverInfinity.enums"/>
<package name="com.tridium.andoverInfinity.comm"/>
<package name="com.tridium.andoverInfinity.comm.rsp"/>
<package name="com.tridium.andoverInfinity.comm.req"/>
<package name="com.tridium.andoverInfinity"/>
<package name="com.tridium.andoverInfinity.identify"/>
<package name="com.tridium.andoverInfinity.point"/>
<package name="com.tridium.andoverInfinity.discover"/>
<class packageName="com.tridium.andoverInfinity" name="BInfinetDevice"><description>BInfinetDevice is the base class for Infinity devices.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.rsp" name="BInfinityAckWithData"><description>Used to send bytes to the controller as intermediate steps in multi-step&#xa; request/response sequences.</description></class>
<class packageName="com.tridium.andoverInfinity.comm" name="BInfinityCommunicator"><description>Communicator for Infinity driver</description></class>
<class packageName="com.tridium.andoverInfinity.identify" name="BInfinityDeviceDiscoverParams"><description>BInfinityDeviceDiscoverParams&#xa; &#xa; There are no params that a user needs to enter to do a learn, as it&#xa; is a well-defined keystroke sequence that never varies between systems.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityDeviceDiscoverRequest"><description>Device discover request for Infinity.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.rsp" name="BInfinityDeviceDiscoverResponse"><description>BInfinityDeviceDiscoverResponse includes an array of BInfinityDeviceDiscoveryObject&#x27;s&#xa; that were discovered in the processing of a BInfinityDeviceDiscoverRequest</description></class>
<class packageName="com.tridium.andoverInfinity.identify" name="BInfinityDeviceDiscoveryObject"><description>BInfinityDeviceDiscoveryObject&#xa; &#xa; This class extends &lt;code&gt;BInfinityDeviceId&lt;/code&gt; to provide an additional&#xa; property to display the &#x22;Online&#x22; or &#x22;Offline&#x22; status of &#xa; a device as it is displayed as a discovery object.</description></class>
<class packageName="com.tridium.andoverInfinity.discover" name="BInfinityDeviceDiscoveryPreferences"><description>BInfinityDeviceDiscoveryPreferences</description></class>
<class packageName="com.tridium.andoverInfinity" name="BInfinityDeviceFolder"><description>BInfinityDeviceFolder</description></class>
<class packageName="com.tridium.andoverInfinity.identify" name="BInfinityDeviceId"></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityDevicePingRequest"><description>Ping message for infinity.</description></class>
<class packageName="com.tridium.andoverInfinity.identify" name="BInfinityDiscoveryPointId"><description>BInfinityDiscoveryPointId extends BInfinityPointId to include properties&#xa; that are to be displayed in the learn view pane, but not in the point display&#xa; pane.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityInitializeScreenRequest"><description>BInfinityInitializeScreenRequest sends a &#x22;\n&#x22; + CTL-Z to force&#xa; the infinity panel to resend all lines.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityKeystrokeRequest"><description>Used to send keystrokes from client to server side while in terminal session&#xa; in the Vt100 manager view.</description></class>
<class packageName="com.tridium.andoverInfinity.comm" name="BInfinityLine"><description>BInfinityLine is used to pass line/format info from the screen buffer to&#xa; the workbench terminal view</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityLogonSequenceRequest"><description>Logon is sent from BInfinityInitializeScreenRequest (&#x22;window&#x22;)&#xa; if the initialize screen request(CTL-Z) was unsuccessful.</description></class>
<class packageName="com.tridium.andoverInfinity" name="BInfinityNetwork"><description>BInfinityNetwork extends BDdfSerialNetwork to wrap a custom communicator&#xa; and includes a single frozen slot for a BInfinityNetworkDevice which is&#xa; always present in a system.</description></class>
<class packageName="com.tridium.andoverInfinity" name="BInfinityNetworkDevice"><description>This class models the &#x22;top-level&#x22; device in a network of Infinity devices.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.rsp" name="BInfinityPingResponse"><description>Response class to used to complete a successful ping request</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityPointAutoRequest"><description>BInfinityPointAutoRequest is used to send an &#x22;Enable&#x22; command&#xa; to the field panel whenever a point returns to auto.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.rsp" name="BInfinityPointAutoResponse"><description>The only purpose in life for this class is to satisfy the success of the &#xa; BInfinityPointAutoRequest, BInfinityPointEnableRequest, or BInfinityPointDisableRequest</description></class>
<class packageName="com.tridium.andoverInfinity" name="BInfinityPointDeviceExt"><description>BInfinityPointDeviceExt</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityPointDisableRequest"><description>BInfinityPointDisableRequest is used to send an &#x22;Disable&#x22; command&#xa; to the field panel whenever an action is invoked from the proxy ext.</description></class>
<class packageName="com.tridium.andoverInfinity.identify" name="BInfinityPointDiscoverParams"><description>BInfinityPointDiscoverParams</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityPointDiscoverRequest"><description>Point discover request for Infinity</description></class>
<class packageName="com.tridium.andoverInfinity.comm.rsp" name="BInfinityPointDiscoverResponse"><description>Used to pass point discoveryObjects to the point discovery job</description></class>
<class packageName="com.tridium.andoverInfinity.discover" name="BInfinityPointDiscoveryLeaf"><description>BInfinityPointDiscoveryLeaf</description></class>
<class packageName="com.tridium.andoverInfinity.discover" name="BInfinityPointDiscoveryPreferences"><description>BInfinityPointDiscoveryPreferences</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityPointEnableRequest"><description>BInfinityPointEnableRequest is used to send an &#x22;Enable&#x22; command&#xa; to the field panel whenever an action is invoked from the proxy ext.</description></class>
<class packageName="com.tridium.andoverInfinity.point" name="BInfinityPointFolder"><description>BInfinityPointFolder</description></class>
<class packageName="com.tridium.andoverInfinity.identify" name="BInfinityPointId"><description>BInfinityPointId adds a &#x22;rawResponse&#x22; field to the point display pane.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityPollForControllerInfoRequest"><description>Navigates the View/Controllers and Edit/Controllers menus to retrieve info&#xa; about the device serial number, model, and unit number.</description></class>
<class packageName="com.tridium.andoverInfinity.point" name="BInfinityProxyExt"><description>BInfinityProxyExt</description></class>
<class packageName="com.tridium.andoverInfinity.identify" name="BInfinityReadPointParams"><description>BInfinityReadPointParams&#xa; &#xa; All we need to poll a point is the device name and the point name.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityReadPointRequest"><description>Message to read a points value</description></class>
<class packageName="com.tridium.andoverInfinity.comm.rsp" name="BInfinityReadPointResponse"><description>Response class to parse a response string into an BStatusValue</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityReloadInfinetRequest"><description>Message to put the infinity network device into reload mode</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityReloadLineRequest"><description>Used to send a single line from a reload file to the controller in reload mode</description></class>
<class packageName="com.tridium.andoverInfinity.comm.rsp" name="BInfinityReloadStartResponse"><description>BInfinityReloadStartResponse is used to complete a reload transaction</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinitySaveInfinetRequest"><description>Used to send message to put the Infinity panel into backup mode, and &#xa; send the backup bytes to the client side when done</description></class>
<class packageName="com.tridium.andoverInfinity.enums" name="BInfinitySendDisableEnum"><description>BInfinitySendDisableEnum</description></class>
<class packageName="com.tridium.andoverInfinity.comm" name="BInfinitySerialReceiver"><description>Infinity driver implementation of a BDdfSerialReceiver, uses a &#xa; BVt100ScreenBuffer to store characters read from the input stream.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinitySetTimeRequest"><description>Used to navigate the Edit/System Date and Time menus to set the &#xa; controller time to the curren station date/time.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.rsp" name="BInfinitySuccessResponse"><description>BInfinitySuccessResponse sole purpose in life is to satisfy the completion of &#xa; requests or request-sequences.</description></class>
<class packageName="com.tridium.andoverInfinity.comm" name="BInfinityTransmitter"><description>A custom transmitter for Infinity that overrides forceTransmit method</description></class>
<class packageName="com.tridium.andoverInfinity.identify" name="BInfinityWritePointParams"><description>BInfinityWritePointParams</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityWritePointRequest"><description>Used to send a write value to the controller panel</description></class>
<class packageName="com.tridium.andoverInfinity.comm.rsp" name="BInfinityWritePointResponse"><description>/**&#xa; BInfinityWritePointResponse is used to complete a write point transaction</description></class>
<class packageName="com.tridium.andoverInfinity.comm" name="BVt100"><description>Contains a screen buffer to maintain both the content and state&#xa;  of a screen as VT100 commands and control sequences are received.</description></class>
<class packageName="com.tridium.andoverInfinity.comm" name="CursorPosition"><description>Maintains cursor line and column position information for the BVt100Screen</description></class>
<class packageName="com.tridium.andoverInfinity.comm" name="InfinityUtil"></class>
<class packageName="com.tridium.andoverInfinity.comm" name="MarkerPosition"><description>Position encapsulates a logical two dimensional character &#xa; position in a document with a zero based line and column&#xa; index.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="RequiresNetworkAccess" category="interface"><description>Most Infinity requests require access to the screen buffer to get current screen&#xa; mode.</description></class>
<class packageName="com.tridium.andoverInfinity.comm" name="Vt100Const" category="interface"><description>Constants for use in the Infinity driver</description></class>
</module>
</bajadoc>
