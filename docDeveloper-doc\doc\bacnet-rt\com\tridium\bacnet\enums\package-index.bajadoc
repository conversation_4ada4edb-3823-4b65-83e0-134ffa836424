<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="com.tridium.bacnet.enums">
<description/>
<class packageName="com.tridium.bacnet.enums" name="BAcknowledgmentFilter"><description>BAcknowledgmentFilter represents the enumeration for restricting the types&#xa; of event-initiating objects that shall be included in a response to a&#xa; GetEnrollmentSummary-Request, based on their acknowledgement state.</description></class>
<class packageName="com.tridium.bacnet.enums" name="BBacnetMstpBaudRate"><description>BBacnetMstpBaudRate represents the possible baud rate choices&#xa; for a comm port.</description></class>
<class packageName="com.tridium.bacnet.enums" name="BBacnetMstpUsageTimeout"><description>BBacnetMstpBaudRate represents the possible baud rate choices&#xa; for a comm port.</description></class>
<class packageName="com.tridium.bacnet.enums" name="BEventStateFilter"><description>BEventStateFilter represents the enumeration for restricting the types&#xa; of event-initiating objects that shall be included in a response to a&#xa; GetEnrollmentSummary-Request, based on their event state.</description></class>
<class packageName="com.tridium.bacnet.enums" name="BIpDeviceType"><description>BIpDeviceType represents the possible types of BVLL interaction Niagara&#xa; can use.</description></class>
</package>
</bajadoc>
