<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BAlarmService" name="BAlarmService" packageName="javax.baja.alarm" public="true">
<description>
The BAlarmService uses BAlarmClasses to route all alarm&#xa; messages between AlarmSources and BAlarmRecipients.&#xa; &lt;p&gt;&#xa; Each station contains a single BAlarmService.
</description>
<tag name="@author"><PERSON> on 19 Feb 01</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BAbstractService"/>
</extends>
<implements>
<type class="javax.baja.alarm.BIAlarmClassFolder"/>
</implements>
<implements>
<type class="javax.baja.dataRecovery.BIDataRecoverySourceService"/>
</implements>
<implements>
<type class="javax.baja.util.BIRestrictedComponent"/>
</implements>
<property name="alarmDbConfig" flags="">
<type class="javax.baja.alarm.BAlarmDbConfig"/>
<description>
Slot for the &lt;code&gt;alarmDbConfig&lt;/code&gt; property.&#xa; Alarm Database Configuration.
</description>
<tag name="@see">#getAlarmDbConfig</tag>
<tag name="@see">#setAlarmDbConfig</tag>
</property>

<property name="defaultAlarmClass" flags="">
<type class="javax.baja.alarm.BAlarmClass"/>
<description>
Slot for the &lt;code&gt;defaultAlarmClass&lt;/code&gt; property.&#xa; The default alarm class.
</description>
<tag name="@see">#getDefaultAlarmClass</tag>
<tag name="@see">#setDefaultAlarmClass</tag>
</property>

<property name="masterAlarmInstructions" flags="">
<type class="javax.baja.alarm.BAlarmInstructions"/>
<description>
Slot for the &lt;code&gt;masterAlarmInstructions&lt;/code&gt; property.&#xa; List of generic instructions that are available to all alarm exts.
</description>
<tag name="@see">#getMasterAlarmInstructions</tag>
<tag name="@see">#setMasterAlarmInstructions</tag>
</property>

<property name="escalationTimeTrigger" flags="h">
<type class="javax.baja.control.trigger.BTimeTrigger"/>
<description>
Slot for the &lt;code&gt;escalationTimeTrigger&lt;/code&gt; property.&#xa; The frequency that alarms should be escalated
</description>
<tag name="@see">#getEscalationTimeTrigger</tag>
<tag name="@see">#setEscalationTimeTrigger</tag>
</property>

<property name="coalesceAlarms" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;coalesceAlarms&lt;/code&gt; property.&#xa; Determines whether to coalesce alarms or not.
</description>
<tag name="@see">#getCoalesceAlarms</tag>
<tag name="@see">#setCoalesceAlarms</tag>
</property>

<action name="routeAlarm" flags="h">
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;routeAlarm&lt;/code&gt; action.&#xa; Route an alarm record to recipients.
</description>
<tag name="@see">#routeAlarm(BAlarmRecord parameter)</tag>
</action>

<action name="ackAlarm" flags="h">
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;ackAlarm&lt;/code&gt; action.&#xa; Route an ack Request to it&#x27;s source.
</description>
<tag name="@see">#ackAlarm(BAlarmRecord parameter)</tag>
</action>

<action name="enableToOffnormal" flags="h">
<parameter name="parameter">
<type class="javax.baja.sys.BVector"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;enableToOffnormal&lt;/code&gt; action.&#xa; Set into service all of the extensions referenced in the specified list of ords.
</description>
<tag name="@see">#enableToOffnormal(BVector parameter)</tag>
</action>

<action name="disableToOffnormal" flags="h">
<parameter name="parameter">
<type class="javax.baja.sys.BVector"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;disableToOffnormal&lt;/code&gt; action.&#xa; Set disabled all of the extensions referenced in the specified list of ords.
</description>
<tag name="@see">#disableToOffnormal(BVector parameter)</tag>
</action>

<action name="enableToFault" flags="h">
<parameter name="parameter">
<type class="javax.baja.sys.BVector"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;enableToFault&lt;/code&gt; action.&#xa; Set into service all of the extensions referenced in the specified list of ords.
</description>
<tag name="@see">#enableToFault(BVector parameter)</tag>
</action>

<action name="disableToFault" flags="h">
<parameter name="parameter">
<type class="javax.baja.sys.BVector"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;disableToFault&lt;/code&gt; action.&#xa; Set disabled all of the extensions referenced in the specified list of ords.
</description>
<tag name="@see">#disableToFault(BVector parameter)</tag>
</action>

<action name="auditForceClear" flags="hA">
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;auditForceClear&lt;/code&gt; action.&#xa; Creates an audit record in the AuditHistory Database if available.&#xa; Flagged as no audit since this method create an audit record.
</description>
<tag name="@see">#auditForceClear(BAlarmRecord parameter)</tag>
</action>

<action name="escalateAlarms" flags="hAa">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;escalateAlarms&lt;/code&gt; action.&#xa; Creates an audit record in the AuditHistory Database if available.&#xa; Flagged as no audit since this method create an audit record.
</description>
<tag name="@see">#escalateAlarms()</tag>
</action>

<topic name="alarm" flags="s">
<eventType>
<type class="javax.baja.alarm.BAlarmRecord"/>
</eventType><description>
Slot for the &lt;code&gt;alarm&lt;/code&gt; topic.&#xa; Fired whenever an alarm is received (eg. when the alarm is routed to an alarm class).
</description>
<tag name="@see">#fireAlarm</tag>
</topic>

<!-- javax.baja.alarm.BAlarmService() -->
<constructor name="BAlarmService" public="true">
<description/>
</constructor>

<!-- javax.baja.alarm.BAlarmService.getAlarmDbConfig() -->
<method name="getAlarmDbConfig"  public="true">
<description>
Get the &lt;code&gt;alarmDbConfig&lt;/code&gt; property.&#xa; Alarm Database Configuration.
</description>
<tag name="@see">#alarmDbConfig</tag>
<return>
<type class="javax.baja.alarm.BAlarmDbConfig"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.setAlarmDbConfig(javax.baja.alarm.BAlarmDbConfig) -->
<method name="setAlarmDbConfig"  public="true">
<description>
Set the &lt;code&gt;alarmDbConfig&lt;/code&gt; property.&#xa; Alarm Database Configuration.
</description>
<tag name="@see">#alarmDbConfig</tag>
<parameter name="v">
<type class="javax.baja.alarm.BAlarmDbConfig"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.getDefaultAlarmClass() -->
<method name="getDefaultAlarmClass"  public="true">
<description>
Get the &lt;code&gt;defaultAlarmClass&lt;/code&gt; property.&#xa; The default alarm class.
</description>
<tag name="@see">#defaultAlarmClass</tag>
<return>
<type class="javax.baja.alarm.BAlarmClass"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.setDefaultAlarmClass(javax.baja.alarm.BAlarmClass) -->
<method name="setDefaultAlarmClass"  public="true">
<description>
Set the &lt;code&gt;defaultAlarmClass&lt;/code&gt; property.&#xa; The default alarm class.
</description>
<tag name="@see">#defaultAlarmClass</tag>
<parameter name="v">
<type class="javax.baja.alarm.BAlarmClass"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.getMasterAlarmInstructions() -->
<method name="getMasterAlarmInstructions"  public="true">
<description>
Get the &lt;code&gt;masterAlarmInstructions&lt;/code&gt; property.&#xa; List of generic instructions that are available to all alarm exts.
</description>
<tag name="@see">#masterAlarmInstructions</tag>
<return>
<type class="javax.baja.alarm.BAlarmInstructions"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.setMasterAlarmInstructions(javax.baja.alarm.BAlarmInstructions) -->
<method name="setMasterAlarmInstructions"  public="true">
<description>
Set the &lt;code&gt;masterAlarmInstructions&lt;/code&gt; property.&#xa; List of generic instructions that are available to all alarm exts.
</description>
<tag name="@see">#masterAlarmInstructions</tag>
<parameter name="v">
<type class="javax.baja.alarm.BAlarmInstructions"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.getEscalationTimeTrigger() -->
<method name="getEscalationTimeTrigger"  public="true">
<description>
Get the &lt;code&gt;escalationTimeTrigger&lt;/code&gt; property.&#xa; The frequency that alarms should be escalated
</description>
<tag name="@see">#escalationTimeTrigger</tag>
<return>
<type class="javax.baja.control.trigger.BTimeTrigger"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.setEscalationTimeTrigger(javax.baja.control.trigger.BTimeTrigger) -->
<method name="setEscalationTimeTrigger"  public="true">
<description>
Set the &lt;code&gt;escalationTimeTrigger&lt;/code&gt; property.&#xa; The frequency that alarms should be escalated
</description>
<tag name="@see">#escalationTimeTrigger</tag>
<parameter name="v">
<type class="javax.baja.control.trigger.BTimeTrigger"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.getCoalesceAlarms() -->
<method name="getCoalesceAlarms"  public="true">
<description>
Get the &lt;code&gt;coalesceAlarms&lt;/code&gt; property.&#xa; Determines whether to coalesce alarms or not.
</description>
<tag name="@see">#coalesceAlarms</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.setCoalesceAlarms(boolean) -->
<method name="setCoalesceAlarms"  public="true">
<description>
Set the &lt;code&gt;coalesceAlarms&lt;/code&gt; property.&#xa; Determines whether to coalesce alarms or not.
</description>
<tag name="@see">#coalesceAlarms</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.routeAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="routeAlarm"  public="true">
<description>
Invoke the &lt;code&gt;routeAlarm&lt;/code&gt; action.&#xa; Route an alarm record to recipients.
</description>
<tag name="@see">#routeAlarm</tag>
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.ackAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="ackAlarm"  public="true">
<description>
Invoke the &lt;code&gt;ackAlarm&lt;/code&gt; action.&#xa; Route an ack Request to it&#x27;s source.
</description>
<tag name="@see">#ackAlarm</tag>
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.enableToOffnormal(javax.baja.sys.BVector) -->
<method name="enableToOffnormal"  public="true">
<description>
Invoke the &lt;code&gt;enableToOffnormal&lt;/code&gt; action.&#xa; Set into service all of the extensions referenced in the specified list of ords.
</description>
<tag name="@see">#enableToOffnormal</tag>
<parameter name="parameter">
<type class="javax.baja.sys.BVector"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.disableToOffnormal(javax.baja.sys.BVector) -->
<method name="disableToOffnormal"  public="true">
<description>
Invoke the &lt;code&gt;disableToOffnormal&lt;/code&gt; action.&#xa; Set disabled all of the extensions referenced in the specified list of ords.
</description>
<tag name="@see">#disableToOffnormal</tag>
<parameter name="parameter">
<type class="javax.baja.sys.BVector"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.enableToFault(javax.baja.sys.BVector) -->
<method name="enableToFault"  public="true">
<description>
Invoke the &lt;code&gt;enableToFault&lt;/code&gt; action.&#xa; Set into service all of the extensions referenced in the specified list of ords.
</description>
<tag name="@see">#enableToFault</tag>
<parameter name="parameter">
<type class="javax.baja.sys.BVector"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.disableToFault(javax.baja.sys.BVector) -->
<method name="disableToFault"  public="true">
<description>
Invoke the &lt;code&gt;disableToFault&lt;/code&gt; action.&#xa; Set disabled all of the extensions referenced in the specified list of ords.
</description>
<tag name="@see">#disableToFault</tag>
<parameter name="parameter">
<type class="javax.baja.sys.BVector"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.auditForceClear(javax.baja.alarm.BAlarmRecord) -->
<method name="auditForceClear"  public="true">
<description>
Invoke the &lt;code&gt;auditForceClear&lt;/code&gt; action.&#xa; Creates an audit record in the AuditHistory Database if available.&#xa; Flagged as no audit since this method create an audit record.
</description>
<tag name="@see">#auditForceClear</tag>
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.escalateAlarms() -->
<method name="escalateAlarms"  public="true">
<description>
Invoke the &lt;code&gt;escalateAlarms&lt;/code&gt; action.&#xa; Creates an audit record in the AuditHistory Database if available.&#xa; Flagged as no audit since this method create an audit record.
</description>
<tag name="@see">#escalateAlarms</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.fireAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="fireAlarm"  public="true">
<description>
Fire an event for the &lt;code&gt;alarm&lt;/code&gt; topic.&#xa; Fired whenever an alarm is received (eg. when the alarm is routed to an alarm class).
</description>
<tag name="@see">#alarm</tag>
<parameter name="event">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.getServiceTypes() -->
<method name="getServiceTypes"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Register this component under &#x22;alarm:AlarmService&#x22;.
</description>
<return>
<type class="javax.baja.sys.Type" dimension="1"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.serviceStarted() -->
<method name="serviceStarted"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Service start.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.atSteadyState() -->
<method name="atSteadyState"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.serviceStopped() -->
<method name="serviceStopped"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Service stop.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.getLicenseFeature() -->
<method name="getLicenseFeature"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.license.Feature"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.enabled() -->
<method name="enabled"  protected="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.disabled() -->
<method name="disabled"  protected="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.getService() -->
<method name="getService"  public="true" static="true">
<description>
Get the AlarmService or throw ServiceNotFoundException.
</description>
<tag name="@since">Niagara 4.11</tag>
<return>
<type class="javax.baja.alarm.BAlarmService"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.initDataRecoverySource(javax.baja.dataRecovery.BIDataRecoveryService) -->
<method name="initDataRecoverySource"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="service">
<type class="javax.baja.dataRecovery.BIDataRecoveryService"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.getAlarmDb() -->
<method name="getAlarmDb"  public="true" final="true">
<description>
Get the alarm database.
</description>
<return>
<type class="javax.baja.alarm.BAlarmDatabase"/>
</return>
<throws>
<type class="javax.baja.alarm.AlarmException"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmService.getAlarmArchive() -->
<method name="getAlarmArchive"  public="true" final="true">
<description>
Get the AlarmArchive present on the AlarmArchiveProvider, if present.
</description>
<tag name="@since">Niagara 4.11</tag>
<return>
<parameterizedType class="java.util.Optional">
<args>
<type class="javax.baja.alarm.BAlarmArchive"/>
</args>
</parameterizedType>
<description>
The AlarmArchive instance present on the alarm archive provider.
</description>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.createAlarmDb() -->
<method name="createAlarmDb"  protected="true">
<description/>
<return>
<type class="javax.baja.alarm.BAlarmDatabase"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.alarmDbConfigChanged(javax.baja.sys.Property) -->
<method name="alarmDbConfigChanged"  protected="true">
<description>
Notify the AlarmService that the AlarmDbConfig has changed.
</description>
<tag name="@since">Niagara 4.0</tag>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.alarm.AlarmException"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmService.post(javax.baja.sys.Action, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="post"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="action">
<type class="javax.baja.sys.Action"/>
</parameter>
<parameter name="argument">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.util.IFuture"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.doEscalateAlarms() -->
<method name="doEscalateAlarms"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.doAckAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="doAckAlarm"  public="true">
<description>
If an ack is required, route the alarm to the source.&#xa; Otherwise, set it to be acked and route it back to the recipients.
</description>
<parameter name="alarm">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmService.doRouteAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="doRouteAlarm"  public="true">
<description>
Route an alarm record to Recipients.
</description>
<parameter name="alarm">
<type class="javax.baja.alarm.BAlarmRecord"/>
<description>
The alarm to route.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmService.doRouteToRecipient(javax.baja.alarm.BAlarmRecord) -->
<method name="doRouteToRecipient"  public="true">
<description>
Route an alarm record to the required recipient.
</description>
<parameter name="alarm">
<type class="javax.baja.alarm.BAlarmRecord"/>
<description>
The alarm to route.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.doRouteToSource(javax.baja.alarm.BAlarmRecord) -->
<method name="doRouteToSource"  public="true">
<description/>
<parameter name="alarm">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmService.doRouteToSource(javax.baja.alarm.BAlarmRecord, javax.baja.sys.BObject) -->
<method name="doRouteToSource"  public="true">
<description>
Route an alarm record to the source of the alarm.
</description>
<parameter name="alarm">
<type class="javax.baja.alarm.BAlarmRecord"/>
<description>
The alarm to route.
</description>
</parameter>
<parameter name="source">
<type class="javax.baja.sys.BObject"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmService.doEnableToOffnormal(javax.baja.sys.BVector, javax.baja.sys.Context) -->
<method name="doEnableToOffnormal"  public="true">
<description>
Enable the specified history extensions.
</description>
<parameter name="extOrds">
<type class="javax.baja.sys.BVector"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.doDisableToOffnormal(javax.baja.sys.BVector, javax.baja.sys.Context) -->
<method name="doDisableToOffnormal"  public="true">
<description>
Disable the specified history extensions.
</description>
<parameter name="extOrds">
<type class="javax.baja.sys.BVector"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.doEnableToFault(javax.baja.sys.BVector, javax.baja.sys.Context) -->
<method name="doEnableToFault"  public="true">
<description>
Enable the specified history extensions.
</description>
<parameter name="extOrds">
<type class="javax.baja.sys.BVector"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.doDisableToFault(javax.baja.sys.BVector, javax.baja.sys.Context) -->
<method name="doDisableToFault"  public="true">
<description>
Disable the specified history extensions.
</description>
<parameter name="extOrds">
<type class="javax.baja.sys.BVector"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.doAuditForceClear(javax.baja.alarm.BAlarmRecord, javax.baja.sys.Context) -->
<method name="doAuditForceClear"  public="true">
<description/>
<parameter name="rec">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.getAlarmClasses() -->
<method name="getAlarmClasses"  public="true">
<description>
Return an array of all alarm classes in the AlarmService and all AlarmClassFolders
</description>
<return>
<type class="javax.baja.alarm.BAlarmClass" dimension="1"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.lookupAlarmClass(java.lang.String) -->
<method name="lookupAlarmClass"  public="true">
<description>
Resolve an alarm class name to an alarm class.
</description>
<parameter name="alarmClassName">
<type class="java.lang.String"/>
<description>
The path to the alarm class.
</description>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmClass"/>
<description>
Returns BAlarmService.defaultAlarmClass if no alarm class with the specified name can be found.
</description>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.lookupAlarmClass(javax.baja.sys.BComponent, java.lang.String) -->
<method name="lookupAlarmClass"  protected="true">
<description/>
<parameter name="folder">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<parameter name="alarmClassName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmClass"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.getAlarmClassDisplayName(java.lang.Object, javax.baja.sys.Context) -->
<method name="getAlarmClassDisplayName"  public="true">
<annotation><type class="javax.baja.rpc.NiagaraRpc"/>
<elementValue name="permissions">
<annotationValue kind="expr">
<expression>&#x22;unrestricted&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="transports">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.rpc.Transport"/>
<elementValue name="type">
<annotationValue kind="enum">
<enumField name="fox"/>
<type class="javax.baja.rpc.TransportType"/>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Utility method for finding display names of alarm classes.&#xa; If invoked on the client side and the server is running alarm-3.7.35 or greater, will make an RPC call.
</description>
<tag name="@since">Niagara 3.7</tag>
<parameter name="alarmClass">
<type class="java.lang.Object"/>
<description>
BAlarmClass name
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
the context
</description>
</parameter>
<return>
<type class="javax.baja.sys.BString"/>
<description>
Returns getDisplayName(cx) of BAlarmClass with name alarmClass of BAlarmService.getDefaultAlarmClass().getDisplayName(c) if alarmClass does not exist or remote station does not support RPC call.
</description>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.getAlarmClassDisplayNameMap(javax.baja.sys.Context) -->
<method name="getAlarmClassDisplayNameMap"  public="true">
<annotation><type class="javax.baja.rpc.NiagaraRpc"/>
<elementValue name="permissions">
<annotationValue kind="expr">
<expression>&#x22;unrestricted&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="transports">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.rpc.Transport"/>
<elementValue name="type">
<annotationValue kind="enum">
<enumField name="fox"/>
<type class="javax.baja.rpc.TransportType"/>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Utility method for retrieving a name/displayName map with an entry for each BAlarmClass in BAlarmService, and&#xa; recursively in its BAlarmFolder(s). If invoked on the client side and the server is running alarm-4.9.0 or&#xa; greater, will make an RPC call.
</description>
<tag name="@since">Niagara 4.9</tag>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
the context
</description>
</parameter>
<return>
<parameterizedType class="java.util.Map">
<args>
<type class="java.lang.String"/>
<type class="java.lang.String"/>
</args>
</parameterizedType>
<description>
Returns &lt;code&gt;Map&amp;lt;alarmClass.getName(), alarmClass.getDisplayName(cx)&amp;gt;&lt;/code&gt; for each BAlarmClass found in the&#xa; BAlarmService. If the remote station does not support RPC call an empty map is returned.
</description>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.checkAdd(java.lang.String, javax.baja.sys.BValue, int, javax.baja.sys.BFacets, javax.baja.sys.Context) -->
<method name="checkAdd"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Disallow duplicate alarm classes
</description>
<parameter name="newName">
<type class="java.lang.String"/>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="flags">
<type class="int"/>
</parameter>
<parameter name="facets">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.checkRename(javax.baja.sys.Property, java.lang.String, javax.baja.sys.Context) -->
<method name="checkRename"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Disallow duplicate alarm classes
</description>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="newName">
<type class="java.lang.String"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="prop">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.added(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="added"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="prop">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.removed(javax.baja.sys.Property, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="removed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="prop">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="oldValue">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.checkParentForRestrictedComponent(javax.baja.sys.BComponent, javax.baja.sys.Context) -->
<method name="checkParentForRestrictedComponent"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Only allowed to live under the station&#x27;s BServiceContainer and&#xa; no duplicates of the exact same type.
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.getAgents(javax.baja.sys.Context) -->
<method name="getAgents"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.agent.AgentList"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the icon.
</description>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.fw(int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object) -->
<method name="fw"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="x">
<type class="int"/>
</parameter>
<parameter name="a">
<type class="java.lang.Object"/>
</parameter>
<parameter name="b">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c">
<type class="java.lang.Object"/>
</parameter>
<parameter name="d">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="java.lang.Object"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmService.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Include worker&#x27;s spy in diagnostics.
</description>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmService.alarmDbConfig -->
<field name="alarmDbConfig"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmDbConfig&lt;/code&gt; property.&#xa; Alarm Database Configuration.
</description>
<tag name="@see">#getAlarmDbConfig</tag>
<tag name="@see">#setAlarmDbConfig</tag>
</field>

<!-- javax.baja.alarm.BAlarmService.defaultAlarmClass -->
<field name="defaultAlarmClass"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;defaultAlarmClass&lt;/code&gt; property.&#xa; The default alarm class.
</description>
<tag name="@see">#getDefaultAlarmClass</tag>
<tag name="@see">#setDefaultAlarmClass</tag>
</field>

<!-- javax.baja.alarm.BAlarmService.masterAlarmInstructions -->
<field name="masterAlarmInstructions"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;masterAlarmInstructions&lt;/code&gt; property.&#xa; List of generic instructions that are available to all alarm exts.
</description>
<tag name="@see">#getMasterAlarmInstructions</tag>
<tag name="@see">#setMasterAlarmInstructions</tag>
</field>

<!-- javax.baja.alarm.BAlarmService.escalationTimeTrigger -->
<field name="escalationTimeTrigger"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;escalationTimeTrigger&lt;/code&gt; property.&#xa; The frequency that alarms should be escalated
</description>
<tag name="@see">#getEscalationTimeTrigger</tag>
<tag name="@see">#setEscalationTimeTrigger</tag>
</field>

<!-- javax.baja.alarm.BAlarmService.coalesceAlarms -->
<field name="coalesceAlarms"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;coalesceAlarms&lt;/code&gt; property.&#xa; Determines whether to coalesce alarms or not.
</description>
<tag name="@see">#getCoalesceAlarms</tag>
<tag name="@see">#setCoalesceAlarms</tag>
</field>

<!-- javax.baja.alarm.BAlarmService.routeAlarm -->
<field name="routeAlarm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;routeAlarm&lt;/code&gt; action.&#xa; Route an alarm record to recipients.
</description>
<tag name="@see">#routeAlarm(BAlarmRecord parameter)</tag>
</field>

<!-- javax.baja.alarm.BAlarmService.ackAlarm -->
<field name="ackAlarm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;ackAlarm&lt;/code&gt; action.&#xa; Route an ack Request to it&#x27;s source.
</description>
<tag name="@see">#ackAlarm(BAlarmRecord parameter)</tag>
</field>

<!-- javax.baja.alarm.BAlarmService.enableToOffnormal -->
<field name="enableToOffnormal"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;enableToOffnormal&lt;/code&gt; action.&#xa; Set into service all of the extensions referenced in the specified list of ords.
</description>
<tag name="@see">#enableToOffnormal(BVector parameter)</tag>
</field>

<!-- javax.baja.alarm.BAlarmService.disableToOffnormal -->
<field name="disableToOffnormal"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;disableToOffnormal&lt;/code&gt; action.&#xa; Set disabled all of the extensions referenced in the specified list of ords.
</description>
<tag name="@see">#disableToOffnormal(BVector parameter)</tag>
</field>

<!-- javax.baja.alarm.BAlarmService.enableToFault -->
<field name="enableToFault"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;enableToFault&lt;/code&gt; action.&#xa; Set into service all of the extensions referenced in the specified list of ords.
</description>
<tag name="@see">#enableToFault(BVector parameter)</tag>
</field>

<!-- javax.baja.alarm.BAlarmService.disableToFault -->
<field name="disableToFault"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;disableToFault&lt;/code&gt; action.&#xa; Set disabled all of the extensions referenced in the specified list of ords.
</description>
<tag name="@see">#disableToFault(BVector parameter)</tag>
</field>

<!-- javax.baja.alarm.BAlarmService.auditForceClear -->
<field name="auditForceClear"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;auditForceClear&lt;/code&gt; action.&#xa; Creates an audit record in the AuditHistory Database if available.&#xa; Flagged as no audit since this method create an audit record.
</description>
<tag name="@see">#auditForceClear(BAlarmRecord parameter)</tag>
</field>

<!-- javax.baja.alarm.BAlarmService.escalateAlarms -->
<field name="escalateAlarms"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;escalateAlarms&lt;/code&gt; action.&#xa; Creates an audit record in the AuditHistory Database if available.&#xa; Flagged as no audit since this method create an audit record.
</description>
<tag name="@see">#escalateAlarms()</tag>
</field>

<!-- javax.baja.alarm.BAlarmService.alarm -->
<field name="alarm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Topic"/>
<description>
Slot for the &lt;code&gt;alarm&lt;/code&gt; topic.&#xa; Fired whenever an alarm is received (eg. when the alarm is routed to an alarm class).
</description>
<tag name="@see">#fireAlarm</tag>
</field>

<!-- javax.baja.alarm.BAlarmService.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmService.logger -->
<field name="logger"  public="true" static="true" final="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

</class>
</bajadoc>
