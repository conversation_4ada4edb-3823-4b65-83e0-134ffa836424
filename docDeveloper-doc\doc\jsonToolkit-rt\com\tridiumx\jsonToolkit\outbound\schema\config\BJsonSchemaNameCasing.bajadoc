<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing" name="BJsonSchemaNameCasing" packageName="com.tridiumx.jsonToolkit.outbound.schema.config" public="true" final="true">
<description>
Different available case options for the forming of json keys.&#xa; As the names of things in niagara might not have been entered consistently this allows uniformity.&#xa;&#xa; e.g&#xa;&#xa; &#x22;camelCaseKey&#x22; : ...&#xa; &#x22;PascalCaseKey&#x22; : ...&#xa; &#x22;UPPERCASEKEY&#x22; : ...&#xa; &#x22;lowercasekey&#x22; : ...&#xa;&#xa; inherit means use the parent members name case setting
</description>
<tag name="@author"><PERSON> <PERSON></tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;camel&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;pascal&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;upper&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lower&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;preserve&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing.CAMEL -->
<field name="CAMEL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for camel.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing.PASCAL -->
<field name="PASCAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for pascal.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing.UPPER -->
<field name="UPPER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for upper.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing.LOWER -->
<field name="LOWER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lower.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing.PRESERVE -->
<field name="PRESERVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for preserve.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing.camel -->
<field name="camel"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing"/>
<description>
BJsonSchemaNameCasing constant for camel.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing.pascal -->
<field name="pascal"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing"/>
<description>
BJsonSchemaNameCasing constant for pascal.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing.upper -->
<field name="upper"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing"/>
<description>
BJsonSchemaNameCasing constant for upper.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing.lower -->
<field name="lower"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing"/>
<description>
BJsonSchemaNameCasing constant for lower.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing.preserve -->
<field name="preserve"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing"/>
<description>
BJsonSchemaNameCasing constant for preserve.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
