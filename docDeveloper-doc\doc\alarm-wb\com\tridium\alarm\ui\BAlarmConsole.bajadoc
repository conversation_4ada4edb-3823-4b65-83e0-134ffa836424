<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.ui.BAlarmConsole" name="BAlarmConsole" packageName="com.tridium.alarm.ui" public="true">
<description>
BAlarmConsole provides a graphical interface for displaying, auditing,&#xa; silencing and acknowledging alarms of one or more remote Baja stations.&#xa;&#xa; &lt;p&gt;&#xa; It communicates with remote systems via an alarm recipient designed&#xa; especially for the alarm console, &lt;code&gt;BConsoleRecipient&lt;/code&gt;.&#xa; When an alarm reaches the alarm service, one of&#xa; the things it does it route the alarm to all attached recipients which&#xa; are interested in the alarm class the new alarm is of.  Some of these&#xa; recipients may fire off emails or send a job to the printer.  Others&#xa; are created explicitly for remote alarm consoles to attach to to recieve&#xa; pertinent alarms.  For example, an alarm recipient may be set up on&#xa; a station that is only interested in alarms of class &#x22;Security&#x22;.&#xa; Down the hall, in the security office, an employee can attach his console&#xa; to this recipient to recieve all security alarms.  Once attached, the&#xa; employee will recieve all alarms passed through that recipient, and will&#xa; be able to acknowledge, silence, and potentially supress this alarm.&#xa; The employee may also attach his console to several other stations, and the&#xa; console will coalesce all alarms into one display.
</description>
<tag name="@author">Blake M Puhak</tag>
<tag name="@creation">06 Jul 01</tag>
<tag name="@version">$Revision: 257$ $Date: 4/5/11 11:41:06 AM EDT$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="javax.baja.workbench.view.BWbComponentView"/>
</extends>
<implements>
<type class="com.tridium.alarm.ui.IAudioPlayer$MediaPlayerListener"/>
</implements>
<implements>
<type class="javax.baja.workbench.view.BIExportableTableView"/>
</implements>
<implements>
<type class="com.tridium.alarm.ui.BIAlarmServiceView"/>
</implements>
<property name="title" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;title&lt;/code&gt; property.
</description>
<tag name="@see">#getTitle</tag>
<tag name="@see">#setTitle</tag>
</property>

<property name="showTitle" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;showTitle&lt;/code&gt; property.
</description>
<tag name="@see">#getShowTitle</tag>
<tag name="@see">#setShowTitle</tag>
</property>

<action name="processAlarm" flags="">
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;processAlarm&lt;/code&gt; action.&#xa; Process a new alarm.
</description>
<tag name="@see">#processAlarm(BAlarmRecord parameter)</tag>
</action>

<action name="timeRangeModified" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;timeRangeModified&lt;/code&gt; action.
</description>
<tag name="@see">#timeRangeModified()</tag>
</action>

</class>
</bajadoc>
