<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.program.BAceReadFileJob" name="BAceReadFileJob" packageName="com.tridium.ace.program" public="true">
<description>
BAceReadFileJob read file from device list
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">8/12/2019</tag>
<extends>
<type class="javax.baja.job.BSimpleJob"/>
</extends>
<action name="getData" flags="h">
<return>
<type class="javax.baja.sys.BBlob"/>
</return>
<description>
Slot for the &lt;code&gt;getData&lt;/code&gt; action.
</description>
<tag name="@see">#getData()</tag>
</action>

</class>
</bajadoc>
