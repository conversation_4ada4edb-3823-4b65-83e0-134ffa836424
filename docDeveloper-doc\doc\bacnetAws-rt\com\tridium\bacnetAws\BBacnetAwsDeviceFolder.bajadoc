<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="com.tridium.bacnetAws.BBacnetAwsDeviceFolder" name="BBacnetAwsDeviceFolder" packageName="com.tridium.bacnetAws" public="true">
<description>
BBacnetAwsDeviceFolder.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">23 Sep 2009</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.5</tag>
<extends>
<type class="com.tridium.bacnetOws.BBacnetOwsDeviceFolder"/>
</extends>
</class>
</bajadoc>
