<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason" name="BBacnetAccessCredentialDisableReason" packageName="javax.baja.bacnet.enums.access" public="true" final="true">
<description>
BBacnetAccessCredentialDisableReason represents the&#xa; Bacnet Access Credential Disable Reason enumeration.&#xa; &lt;p&gt;&#xa; BBacnetAccessCredentialDisableReason is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Joseph Chandler</tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;disabled&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;disabledNeedsProvisioning&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;disabledUnassigned&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;disabledNotYetActive&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;disabledExpired&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;disabledLockout&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;disabledMaxDays&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;disabledMaxUses&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;disabledInactivity&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;disabledManual&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.DISABLED -->
<field name="DISABLED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for disabled.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.DISABLED_NEEDS_PROVISIONING -->
<field name="DISABLED_NEEDS_PROVISIONING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for disabledNeedsProvisioning.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.DISABLED_UNASSIGNED -->
<field name="DISABLED_UNASSIGNED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for disabledUnassigned.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.DISABLED_NOT_YET_ACTIVE -->
<field name="DISABLED_NOT_YET_ACTIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for disabledNotYetActive.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.DISABLED_EXPIRED -->
<field name="DISABLED_EXPIRED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for disabledExpired.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.DISABLED_LOCKOUT -->
<field name="DISABLED_LOCKOUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for disabledLockout.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.DISABLED_MAX_DAYS -->
<field name="DISABLED_MAX_DAYS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for disabledMaxDays.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.DISABLED_MAX_USES -->
<field name="DISABLED_MAX_USES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for disabledMaxUses.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.DISABLED_INACTIVITY -->
<field name="DISABLED_INACTIVITY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for disabledInactivity.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.DISABLED_MANUAL -->
<field name="DISABLED_MANUAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for disabledManual.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.disabled -->
<field name="disabled"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason"/>
<description>
BBacnetAccessCredentialDisableReason constant for disabled.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.disabledNeedsProvisioning -->
<field name="disabledNeedsProvisioning"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason"/>
<description>
BBacnetAccessCredentialDisableReason constant for disabledNeedsProvisioning.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.disabledUnassigned -->
<field name="disabledUnassigned"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason"/>
<description>
BBacnetAccessCredentialDisableReason constant for disabledUnassigned.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.disabledNotYetActive -->
<field name="disabledNotYetActive"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason"/>
<description>
BBacnetAccessCredentialDisableReason constant for disabledNotYetActive.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.disabledExpired -->
<field name="disabledExpired"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason"/>
<description>
BBacnetAccessCredentialDisableReason constant for disabledExpired.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.disabledLockout -->
<field name="disabledLockout"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason"/>
<description>
BBacnetAccessCredentialDisableReason constant for disabledLockout.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.disabledMaxDays -->
<field name="disabledMaxDays"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason"/>
<description>
BBacnetAccessCredentialDisableReason constant for disabledMaxDays.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.disabledMaxUses -->
<field name="disabledMaxUses"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason"/>
<description>
BBacnetAccessCredentialDisableReason constant for disabledMaxUses.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.disabledInactivity -->
<field name="disabledInactivity"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason"/>
<description>
BBacnetAccessCredentialDisableReason constant for disabledInactivity.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.disabledManual -->
<field name="disabledManual"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason"/>
<description>
BBacnetAccessCredentialDisableReason constant for disabledManual.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAccessCredentialDisableReason.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
