<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BBacnetAnalogOutput" name="BBacnetAnalogOutput" packageName="javax.baja.bacnet.config" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 8$ $Date: 12/11/01 2:48:33 PM$</tag>
<tag name="@creation">18 Jul 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.config.BBacnetAnalog"/>
</extends>
<property name="priorityArray" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
<description>
Slot for the &lt;code&gt;priorityArray&lt;/code&gt; property.
</description>
<tag name="@see">#getPriorityArray</tag>
<tag name="@see">#setPriorityArray</tag>
</property>

<property name="relinquishDefault" flags="">
<type class="float"/>
<description>
Slot for the &lt;code&gt;relinquishDefault&lt;/code&gt; property.
</description>
<tag name="@see">#getRelinquishDefault</tag>
<tag name="@see">#setRelinquishDefault</tag>
</property>

<!-- javax.baja.bacnet.config.BBacnetAnalogOutput() -->
<constructor name="BBacnetAnalogOutput" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetAnalogOutput.getPriorityArray() -->
<method name="getPriorityArray"  public="true">
<description>
Get the &lt;code&gt;priorityArray&lt;/code&gt; property.
</description>
<tag name="@see">#priorityArray</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAnalogOutput.setPriorityArray(javax.baja.bacnet.datatypes.BBacnetArray) -->
<method name="setPriorityArray"  public="true">
<description>
Set the &lt;code&gt;priorityArray&lt;/code&gt; property.
</description>
<tag name="@see">#priorityArray</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAnalogOutput.getRelinquishDefault() -->
<method name="getRelinquishDefault"  public="true">
<description>
Get the &lt;code&gt;relinquishDefault&lt;/code&gt; property.
</description>
<tag name="@see">#relinquishDefault</tag>
<return>
<type class="float"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAnalogOutput.setRelinquishDefault(float) -->
<method name="setRelinquishDefault"  public="true">
<description>
Set the &lt;code&gt;relinquishDefault&lt;/code&gt; property.
</description>
<tag name="@see">#relinquishDefault</tag>
<parameter name="v">
<type class="float"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAnalogOutput.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAnalogOutput.getSlotFacets(javax.baja.sys.Slot) -->
<method name="getSlotFacets"  public="true">
<description>
Apply the &#x22;facets&#x22; property to the &#x22;priorityArray&#x22; property.
</description>
<parameter name="slot">
<type class="javax.baja.sys.Slot"/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetAnalogOutput.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetAnalogOutput.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetAnalogOutput.priorityArray -->
<field name="priorityArray"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;priorityArray&lt;/code&gt; property.
</description>
<tag name="@see">#getPriorityArray</tag>
<tag name="@see">#setPriorityArray</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetAnalogOutput.relinquishDefault -->
<field name="relinquishDefault"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;relinquishDefault&lt;/code&gt; property.
</description>
<tag name="@see">#getRelinquishDefault</tag>
<tag name="@see">#setRelinquishDefault</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetAnalogOutput.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
