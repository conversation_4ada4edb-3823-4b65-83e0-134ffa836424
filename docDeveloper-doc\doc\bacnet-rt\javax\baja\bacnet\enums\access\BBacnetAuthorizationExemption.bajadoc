<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption" name="BBacnetAuthorizationExemption" packageName="javax.baja.bacnet.enums.access" public="true" final="true">
<description>
BBacnetAuthorizationExemption represents the&#xa; BACnetAuthorizationExemption enumeration.&#xa; &lt;p&gt;&#xa; BBacnetAuthorizationExemption is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Joseph Chandler</tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;passback&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;occupancyCheck&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accessRights&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lockout&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deny&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;verification&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;authorizationDelay&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.PASSBACK -->
<field name="PASSBACK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for passback.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.OCCUPANCY_CHECK -->
<field name="OCCUPANCY_CHECK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for occupancyCheck.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.ACCESS_RIGHTS -->
<field name="ACCESS_RIGHTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accessRights.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.LOCKOUT -->
<field name="LOCKOUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lockout.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.DENY -->
<field name="DENY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deny.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.VERIFICATION -->
<field name="VERIFICATION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for verification.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.AUTHORIZATION_DELAY -->
<field name="AUTHORIZATION_DELAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for authorizationDelay.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.passback -->
<field name="passback"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption"/>
<description>
BBacnetAuthorizationExemption constant for passback.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.occupancyCheck -->
<field name="occupancyCheck"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption"/>
<description>
BBacnetAuthorizationExemption constant for occupancyCheck.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.accessRights -->
<field name="accessRights"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption"/>
<description>
BBacnetAuthorizationExemption constant for accessRights.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.lockout -->
<field name="lockout"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption"/>
<description>
BBacnetAuthorizationExemption constant for lockout.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.deny -->
<field name="deny"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption"/>
<description>
BBacnetAuthorizationExemption constant for deny.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.verification -->
<field name="verification"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption"/>
<description>
BBacnetAuthorizationExemption constant for verification.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.authorizationDelay -->
<field name="authorizationDelay"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption"/>
<description>
BBacnetAuthorizationExemption constant for authorizationDelay.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationExemption.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
