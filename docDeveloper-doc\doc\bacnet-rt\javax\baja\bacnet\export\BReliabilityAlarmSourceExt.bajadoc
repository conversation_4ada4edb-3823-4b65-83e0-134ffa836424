<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BReliabilityAlarmSourceExt" name="BReliabilityAlarmSourceExt" packageName="javax.baja.bacnet.export" public="true">
<description>
BReliabilityAlarmSourceExt defines the intrinsic alarming/notification&#xa; for the change of reliability property.
</description>
<tag name="@author">V<PERSON><PERSON> on 17 Jan 2019</tag>
<tag name="@since">BACNet 14, Niagara R47.u1</tag>
<extends>
<type class="javax.baja.control.BPointExtension"/>
</extends>
<implements>
<type class="javax.baja.alarm.BIAlarmSource"/>
</implements>
<implements>
<type class="javax.baja.alarm.ext.BIAlarmMessages"/>
</implements>
<implements>
<type class="javax.baja.bacnet.BacnetAlarmConst"/>
</implements>
<property name="alarmInhibit" flags="">
<type class="javax.baja.status.BStatusBoolean"/>
<description>
Slot for the &lt;code&gt;alarmInhibit&lt;/code&gt; property.&#xa; Inhibits alarm generation.
</description>
<tag name="@see">#getAlarmInhibit</tag>
<tag name="@see">#setAlarmInhibit</tag>
</property>

<property name="alarmState" flags="rd">
<type class="javax.baja.alarm.ext.BAlarmState"/>
<description>
Slot for the &lt;code&gt;alarmState&lt;/code&gt; property.&#xa; Shows the object&#x27;s current alarm state.
</description>
<tag name="@see">#getAlarmState</tag>
<tag name="@see">#setAlarmState</tag>
</property>

<property name="timeDelay" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;timeDelay&lt;/code&gt; property.&#xa; Minimum time period that an alarm condition must exist before the object alarms.
</description>
<tag name="@see">#getTimeDelay</tag>
<tag name="@see">#setTimeDelay</tag>
</property>

<property name="alarmEnable" flags="">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
<description>
Slot for the &lt;code&gt;alarmEnable&lt;/code&gt; property.&#xa; Flags that define the types of alarm transitions for this object that will generate alarm.
</description>
<tag name="@see">#getAlarmEnable</tag>
<tag name="@see">#setAlarmEnable</tag>
</property>

<property name="ackedTransitions" flags="trh">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
<description>
Slot for the &lt;code&gt;ackedTransitions&lt;/code&gt; property.&#xa; Flags, that when cleared, indicate that an unacknowledged alarm transition has occurred.
</description>
<tag name="@see">#getAckedTransitions</tag>
<tag name="@see">#setAckedTransitions</tag>
</property>

<property name="notifyType" flags="">
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
<description>
Slot for the &lt;code&gt;notifyType&lt;/code&gt; property.&#xa; If set to event (not alarm), an active unacknowledged alarm is not reported by the station&#x27;s Bacnet service.
</description>
<tag name="@see">#getNotifyType</tag>
<tag name="@see">#setNotifyType</tag>
</property>

<property name="toFaultTimes" flags="tr">
<type class="javax.baja.alarm.ext.BAlarmTimestamps"/>
<description>
Slot for the &lt;code&gt;toFaultTimes&lt;/code&gt; property.&#xa; eventTime, ackTime and count for last to fault event.
</description>
<tag name="@see">#getToFaultTimes</tag>
<tag name="@see">#setToFaultTimes</tag>
</property>

<property name="toFaultText" flags="">
<type class="javax.baja.util.BFormat"/>
<description>
Slot for the &lt;code&gt;toFaultText&lt;/code&gt; property.&#xa; Text descriptor included in a to-fault alarm for this object. Uses BFormat.
</description>
<tag name="@see">#getToFaultText</tag>
<tag name="@see">#setToFaultText</tag>
</property>

<property name="toNormalText" flags="">
<type class="javax.baja.util.BFormat"/>
<description>
Slot for the &lt;code&gt;toNormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-normal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#getToNormalText</tag>
<tag name="@see">#setToNormalText</tag>
</property>

<property name="hyperlinkOrd" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;hyperlinkOrd&lt;/code&gt; property.&#xa; Ord to link to for more information about this alarm.
</description>
<tag name="@see">#getHyperlinkOrd</tag>
<tag name="@see">#setHyperlinkOrd</tag>
</property>

<property name="alarmClass" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;alarmClass&lt;/code&gt; property.&#xa; This is the alarm class used for this object.
</description>
<tag name="@see">#getAlarmClass</tag>
<tag name="@see">#setAlarmClass</tag>
</property>

<property name="reliability" flags="">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description>
Slot for the &lt;code&gt;reliability&lt;/code&gt; property.&#xa; The recent reliability value of the alarm.
</description>
<tag name="@see">#getReliability</tag>
<tag name="@see">#setReliability</tag>
</property>

<action name="ackAlarm" flags="h">
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
<description>
Slot for the &lt;code&gt;ackAlarm&lt;/code&gt; action.&#xa; Acknowledge the alarm matching this ack request
</description>
<tag name="@see">#ackAlarm(BAlarmRecord parameter)</tag>
</action>

<action name="timerElapsed" flags="h">
<parameter name="parameter">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;timerElapsed&lt;/code&gt; action.&#xa; Acknowledge the alarm matching this ack request
</description>
<tag name="@see">#timerElapsed(BBacnetReliability parameter)</tag>
</action>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt() -->
<constructor name="BReliabilityAlarmSourceExt" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.getAlarmInhibit() -->
<method name="getAlarmInhibit"  public="true">
<description>
Get the &lt;code&gt;alarmInhibit&lt;/code&gt; property.&#xa; Inhibits alarm generation.
</description>
<tag name="@see">#alarmInhibit</tag>
<return>
<type class="javax.baja.status.BStatusBoolean"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.setAlarmInhibit(javax.baja.status.BStatusBoolean) -->
<method name="setAlarmInhibit"  public="true">
<description>
Set the &lt;code&gt;alarmInhibit&lt;/code&gt; property.&#xa; Inhibits alarm generation.
</description>
<tag name="@see">#alarmInhibit</tag>
<parameter name="v">
<type class="javax.baja.status.BStatusBoolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.getAlarmState() -->
<method name="getAlarmState"  public="true">
<description>
Get the &lt;code&gt;alarmState&lt;/code&gt; property.&#xa; Shows the object&#x27;s current alarm state.
</description>
<tag name="@see">#alarmState</tag>
<return>
<type class="javax.baja.alarm.ext.BAlarmState"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.setAlarmState(javax.baja.alarm.ext.BAlarmState) -->
<method name="setAlarmState"  public="true">
<description>
Set the &lt;code&gt;alarmState&lt;/code&gt; property.&#xa; Shows the object&#x27;s current alarm state.
</description>
<tag name="@see">#alarmState</tag>
<parameter name="v">
<type class="javax.baja.alarm.ext.BAlarmState"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.getTimeDelay() -->
<method name="getTimeDelay"  public="true">
<description>
Get the &lt;code&gt;timeDelay&lt;/code&gt; property.&#xa; Minimum time period that an alarm condition must exist before the object alarms.
</description>
<tag name="@see">#timeDelay</tag>
<return>
<type class="javax.baja.sys.BRelTime"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.setTimeDelay(javax.baja.sys.BRelTime) -->
<method name="setTimeDelay"  public="true">
<description>
Set the &lt;code&gt;timeDelay&lt;/code&gt; property.&#xa; Minimum time period that an alarm condition must exist before the object alarms.
</description>
<tag name="@see">#timeDelay</tag>
<parameter name="v">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.getAlarmEnable() -->
<method name="getAlarmEnable"  public="true">
<description>
Get the &lt;code&gt;alarmEnable&lt;/code&gt; property.&#xa; Flags that define the types of alarm transitions for this object that will generate alarm.
</description>
<tag name="@see">#alarmEnable</tag>
<return>
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.setAlarmEnable(javax.baja.alarm.BAlarmTransitionBits) -->
<method name="setAlarmEnable"  public="true">
<description>
Set the &lt;code&gt;alarmEnable&lt;/code&gt; property.&#xa; Flags that define the types of alarm transitions for this object that will generate alarm.
</description>
<tag name="@see">#alarmEnable</tag>
<parameter name="v">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.getAckedTransitions() -->
<method name="getAckedTransitions"  public="true">
<description>
Get the &lt;code&gt;ackedTransitions&lt;/code&gt; property.&#xa; Flags, that when cleared, indicate that an unacknowledged alarm transition has occurred.
</description>
<tag name="@see">#ackedTransitions</tag>
<return>
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.setAckedTransitions(javax.baja.alarm.BAlarmTransitionBits) -->
<method name="setAckedTransitions"  public="true">
<description>
Set the &lt;code&gt;ackedTransitions&lt;/code&gt; property.&#xa; Flags, that when cleared, indicate that an unacknowledged alarm transition has occurred.
</description>
<tag name="@see">#ackedTransitions</tag>
<parameter name="v">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.getNotifyType() -->
<method name="getNotifyType"  public="true">
<description>
Get the &lt;code&gt;notifyType&lt;/code&gt; property.&#xa; If set to event (not alarm), an active unacknowledged alarm is not reported by the station&#x27;s Bacnet service.
</description>
<tag name="@see">#notifyType</tag>
<return>
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.setNotifyType(javax.baja.bacnet.enums.BBacnetNotifyType) -->
<method name="setNotifyType"  public="true">
<description>
Set the &lt;code&gt;notifyType&lt;/code&gt; property.&#xa; If set to event (not alarm), an active unacknowledged alarm is not reported by the station&#x27;s Bacnet service.
</description>
<tag name="@see">#notifyType</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.getToFaultTimes() -->
<method name="getToFaultTimes"  public="true">
<description>
Get the &lt;code&gt;toFaultTimes&lt;/code&gt; property.&#xa; eventTime, ackTime and count for last to fault event.
</description>
<tag name="@see">#toFaultTimes</tag>
<return>
<type class="javax.baja.alarm.ext.BAlarmTimestamps"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.setToFaultTimes(javax.baja.alarm.ext.BAlarmTimestamps) -->
<method name="setToFaultTimes"  public="true">
<description>
Set the &lt;code&gt;toFaultTimes&lt;/code&gt; property.&#xa; eventTime, ackTime and count for last to fault event.
</description>
<tag name="@see">#toFaultTimes</tag>
<parameter name="v">
<type class="javax.baja.alarm.ext.BAlarmTimestamps"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.getToFaultText() -->
<method name="getToFaultText"  public="true">
<description>
Get the &lt;code&gt;toFaultText&lt;/code&gt; property.&#xa; Text descriptor included in a to-fault alarm for this object. Uses BFormat.
</description>
<tag name="@see">#toFaultText</tag>
<return>
<type class="javax.baja.util.BFormat"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.setToFaultText(javax.baja.util.BFormat) -->
<method name="setToFaultText"  public="true">
<description>
Set the &lt;code&gt;toFaultText&lt;/code&gt; property.&#xa; Text descriptor included in a to-fault alarm for this object. Uses BFormat.
</description>
<tag name="@see">#toFaultText</tag>
<parameter name="v">
<type class="javax.baja.util.BFormat"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.getToNormalText() -->
<method name="getToNormalText"  public="true">
<description>
Get the &lt;code&gt;toNormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-normal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#toNormalText</tag>
<return>
<type class="javax.baja.util.BFormat"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.setToNormalText(javax.baja.util.BFormat) -->
<method name="setToNormalText"  public="true">
<description>
Set the &lt;code&gt;toNormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-normal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#toNormalText</tag>
<parameter name="v">
<type class="javax.baja.util.BFormat"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.getHyperlinkOrd() -->
<method name="getHyperlinkOrd"  public="true">
<description>
Get the &lt;code&gt;hyperlinkOrd&lt;/code&gt; property.&#xa; Ord to link to for more information about this alarm.
</description>
<tag name="@see">#hyperlinkOrd</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.setHyperlinkOrd(javax.baja.naming.BOrd) -->
<method name="setHyperlinkOrd"  public="true">
<description>
Set the &lt;code&gt;hyperlinkOrd&lt;/code&gt; property.&#xa; Ord to link to for more information about this alarm.
</description>
<tag name="@see">#hyperlinkOrd</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.getAlarmClass() -->
<method name="getAlarmClass"  public="true">
<description>
Get the &lt;code&gt;alarmClass&lt;/code&gt; property.&#xa; This is the alarm class used for this object.
</description>
<tag name="@see">#alarmClass</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.setAlarmClass(java.lang.String) -->
<method name="setAlarmClass"  public="true">
<description>
Set the &lt;code&gt;alarmClass&lt;/code&gt; property.&#xa; This is the alarm class used for this object.
</description>
<tag name="@see">#alarmClass</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.getReliability() -->
<method name="getReliability"  public="true">
<description>
Get the &lt;code&gt;reliability&lt;/code&gt; property.&#xa; The recent reliability value of the alarm.
</description>
<tag name="@see">#reliability</tag>
<return>
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.setReliability(javax.baja.bacnet.enums.BBacnetReliability) -->
<method name="setReliability"  public="true">
<description>
Set the &lt;code&gt;reliability&lt;/code&gt; property.&#xa; The recent reliability value of the alarm.
</description>
<tag name="@see">#reliability</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.ackAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="ackAlarm"  public="true">
<description>
Invoke the &lt;code&gt;ackAlarm&lt;/code&gt; action.&#xa; Acknowledge the alarm matching this ack request
</description>
<tag name="@see">#ackAlarm</tag>
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.timerElapsed(javax.baja.bacnet.enums.BBacnetReliability) -->
<method name="timerElapsed"  public="true">
<description>
Invoke the &lt;code&gt;timerElapsed&lt;/code&gt; action.&#xa; Acknowledge the alarm matching this ack request
</description>
<tag name="@see">#timerElapsed</tag>
<parameter name="parameter">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.started() -->
<method name="started"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.stopped() -->
<method name="stopped"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.doAckAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="doAckAlarm"  public="true">
<description/>
<parameter name="ackRequest">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.doTimerElapsed(javax.baja.bacnet.enums.BBacnetReliability) -->
<method name="doTimerElapsed"  public="true">
<description/>
<parameter name="reliability">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.alarmProcessing(javax.baja.bacnet.enums.BBacnetReliability) -->
<method name="alarmProcessing"  public="true">
<description/>
<parameter name="reliability">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.reliabilityChanged(javax.baja.bacnet.enums.BBacnetReliability) -->
<method name="reliabilityChanged"  public="true">
<description>
When there is change in the property reliability, then this method is called
</description>
<parameter name="reliability">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.onExecute(javax.baja.status.BStatusValue, javax.baja.sys.Context) -->
<method name="onExecute"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="out">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.getToOffnormalText() -->
<method name="getToOffnormalText"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.util.BFormat"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.startTimer(long, javax.baja.bacnet.enums.BBacnetReliability) -->
<method name="startTimer"  protected="true">
<description>
Start a timer to handle alarm validation.
</description>
<parameter name="timeDelay">
<type class="long"/>
</parameter>
<parameter name="reliability">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.cancelTimer() -->
<method name="cancelTimer"  protected="true">
<description>
Cancels all timers associated with this&#xa;  alarm support object
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.isTimerExpired() -->
<method name="isTimerExpired"  protected="true">
<description>
Timer status function
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.alarmInhibit -->
<field name="alarmInhibit"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmInhibit&lt;/code&gt; property.&#xa; Inhibits alarm generation.
</description>
<tag name="@see">#getAlarmInhibit</tag>
<tag name="@see">#setAlarmInhibit</tag>
</field>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.alarmState -->
<field name="alarmState"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmState&lt;/code&gt; property.&#xa; Shows the object&#x27;s current alarm state.
</description>
<tag name="@see">#getAlarmState</tag>
<tag name="@see">#setAlarmState</tag>
</field>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.timeDelay -->
<field name="timeDelay"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;timeDelay&lt;/code&gt; property.&#xa; Minimum time period that an alarm condition must exist before the object alarms.
</description>
<tag name="@see">#getTimeDelay</tag>
<tag name="@see">#setTimeDelay</tag>
</field>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.alarmEnable -->
<field name="alarmEnable"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmEnable&lt;/code&gt; property.&#xa; Flags that define the types of alarm transitions for this object that will generate alarm.
</description>
<tag name="@see">#getAlarmEnable</tag>
<tag name="@see">#setAlarmEnable</tag>
</field>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.ackedTransitions -->
<field name="ackedTransitions"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;ackedTransitions&lt;/code&gt; property.&#xa; Flags, that when cleared, indicate that an unacknowledged alarm transition has occurred.
</description>
<tag name="@see">#getAckedTransitions</tag>
<tag name="@see">#setAckedTransitions</tag>
</field>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.notifyType -->
<field name="notifyType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;notifyType&lt;/code&gt; property.&#xa; If set to event (not alarm), an active unacknowledged alarm is not reported by the station&#x27;s Bacnet service.
</description>
<tag name="@see">#getNotifyType</tag>
<tag name="@see">#setNotifyType</tag>
</field>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.toFaultTimes -->
<field name="toFaultTimes"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;toFaultTimes&lt;/code&gt; property.&#xa; eventTime, ackTime and count for last to fault event.
</description>
<tag name="@see">#getToFaultTimes</tag>
<tag name="@see">#setToFaultTimes</tag>
</field>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.toFaultText -->
<field name="toFaultText"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;toFaultText&lt;/code&gt; property.&#xa; Text descriptor included in a to-fault alarm for this object. Uses BFormat.
</description>
<tag name="@see">#getToFaultText</tag>
<tag name="@see">#setToFaultText</tag>
</field>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.toNormalText -->
<field name="toNormalText"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;toNormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-normal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#getToNormalText</tag>
<tag name="@see">#setToNormalText</tag>
</field>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.hyperlinkOrd -->
<field name="hyperlinkOrd"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;hyperlinkOrd&lt;/code&gt; property.&#xa; Ord to link to for more information about this alarm.
</description>
<tag name="@see">#getHyperlinkOrd</tag>
<tag name="@see">#setHyperlinkOrd</tag>
</field>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.alarmClass -->
<field name="alarmClass"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmClass&lt;/code&gt; property.&#xa; This is the alarm class used for this object.
</description>
<tag name="@see">#getAlarmClass</tag>
<tag name="@see">#setAlarmClass</tag>
</field>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.reliability -->
<field name="reliability"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;reliability&lt;/code&gt; property.&#xa; The recent reliability value of the alarm.
</description>
<tag name="@see">#getReliability</tag>
<tag name="@see">#setReliability</tag>
</field>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.ackAlarm -->
<field name="ackAlarm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;ackAlarm&lt;/code&gt; action.&#xa; Acknowledge the alarm matching this ack request
</description>
<tag name="@see">#ackAlarm(BAlarmRecord parameter)</tag>
</field>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.timerElapsed -->
<field name="timerElapsed"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;timerElapsed&lt;/code&gt; action.&#xa; Acknowledge the alarm matching this ack request
</description>
<tag name="@see">#timerElapsed(BBacnetReliability parameter)</tag>
</field>

<!-- javax.baja.bacnet.export.BReliabilityAlarmSourceExt.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
