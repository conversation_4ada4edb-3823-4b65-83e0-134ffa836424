<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="andoverInfinity" runtimeProfile="rt" name="com.tridium.andoverInfinity.discover">
<description/>
<class packageName="com.tridium.andoverInfinity.discover" name="BInfinityDeviceDiscoveryPreferences"><description>BInfinityDeviceDiscoveryPreferences</description></class>
<class packageName="com.tridium.andoverInfinity.discover" name="BInfinityPointDiscoveryLeaf"><description>BInfinityPointDiscoveryLeaf</description></class>
<class packageName="com.tridium.andoverInfinity.discover" name="BInfinityPointDiscoveryPreferences"><description>BInfinityPointDiscoveryPreferences</description></class>
</package>
</bajadoc>
