<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.query.style.BRowArrayWithHeader" name="BRowArrayWithHeader" packageName="com.tridiumx.jsonToolkit.outbound.schema.query.style" public="true">
<description>
A query result writer as per the &lt;code&gt;<see ref="com.tridiumx.jsonToolkit.outbound.schema.query.style.BRowArray">BRowArray</see>&lt;/code&gt; with a header row&#xa; that includes the column names.&#xa;&#xa;  [&#xa;   [a,b,c],&#xa;   [a1,b1,c1],&#xa;   [a2,b2,c2],&#xa;   [a3,b3,c3]&#xa;  ]
</description>
<tag name="@author"><PERSON> <PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.style.BRowArray"/>
</extends>
<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BRowArrayWithHeader() -->
<constructor name="BRowArrayWithHeader" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BRowArrayWithHeader.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BRowArrayWithHeader.previewText() -->
<method name="previewText"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BString"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BRowArrayWithHeader.includeHeader() -->
<method name="includeHeader"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BRowArrayWithHeader.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
