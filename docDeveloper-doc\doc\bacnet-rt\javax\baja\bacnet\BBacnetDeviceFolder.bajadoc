<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.BBacnetDeviceFolder" name="BBacnetDeviceFolder" packageName="javax.baja.bacnet" public="true">
<description>
BBacnetDeviceFolder.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">13 Sep 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.driver.BDeviceFolder"/>
</extends>
<!-- javax.baja.bacnet.BBacnetDeviceFolder() -->
<constructor name="BBacnetDeviceFolder" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.BBacnetDeviceFolder.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetDeviceFolder.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<description>
BBacnetDeviceFolder may only be placed under a BBacnetNetwork or BBacnetDeviceFolder.
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetDeviceFolder.network() -->
<method name="network"  public="true" final="true">
<description/>
<return>
<type class="javax.baja.bacnet.BBacnetNetwork"/>
<description>
the BBacnetDevice containing this BBacnetPointDeviceExt.
</description>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetDeviceFolder.getAgents(javax.baja.sys.Context) -->
<method name="getAgents"  public="true">
<description>
Get the agent list.  Remove Device Manager and Network Summary.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.agent.AgentList"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetDeviceFolder.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetDeviceFolder.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
