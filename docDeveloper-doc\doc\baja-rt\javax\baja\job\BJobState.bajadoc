<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.job.BJobState" name="BJobState" packageName="javax.baja.job" public="true" final="true">
<description>
BJobState enumerates the state machine of a Job&#x27;s lifecycle.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">30 Apr 03</tag>
<tag name="@version">$Revision: 1$Date$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknown&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;running&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;canceling&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;canceled&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;success&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;failed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.job.BJobState.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.job.BJobState"/>
</return>
</method>

<!-- javax.baja.job.BJobState.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.job.BJobState"/>
</return>
</method>

<!-- javax.baja.job.BJobState.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.job.BJobState.isRunning() -->
<method name="isRunning"  public="true">
<description>
Is this running.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.job.BJobState.isComplete() -->
<method name="isComplete"  public="true">
<description>
Is this success, canceled, or failed.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.job.BJobState.UNKNOWN -->
<field name="UNKNOWN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknown.
</description>
</field>

<!-- javax.baja.job.BJobState.RUNNING -->
<field name="RUNNING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for running.
</description>
</field>

<!-- javax.baja.job.BJobState.CANCELING -->
<field name="CANCELING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for canceling.
</description>
</field>

<!-- javax.baja.job.BJobState.CANCELED -->
<field name="CANCELED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for canceled.
</description>
</field>

<!-- javax.baja.job.BJobState.SUCCESS -->
<field name="SUCCESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for success.
</description>
</field>

<!-- javax.baja.job.BJobState.FAILED -->
<field name="FAILED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for failed.
</description>
</field>

<!-- javax.baja.job.BJobState.unknown -->
<field name="unknown"  public="true" static="true" final="true">
<type class="javax.baja.job.BJobState"/>
<description>
BJobState constant for unknown.
</description>
</field>

<!-- javax.baja.job.BJobState.running -->
<field name="running"  public="true" static="true" final="true">
<type class="javax.baja.job.BJobState"/>
<description>
BJobState constant for running.
</description>
</field>

<!-- javax.baja.job.BJobState.canceling -->
<field name="canceling"  public="true" static="true" final="true">
<type class="javax.baja.job.BJobState"/>
<description>
BJobState constant for canceling.
</description>
</field>

<!-- javax.baja.job.BJobState.canceled -->
<field name="canceled"  public="true" static="true" final="true">
<type class="javax.baja.job.BJobState"/>
<description>
BJobState constant for canceled.
</description>
</field>

<!-- javax.baja.job.BJobState.success -->
<field name="success"  public="true" static="true" final="true">
<type class="javax.baja.job.BJobState"/>
<description>
BJobState constant for success.
</description>
</field>

<!-- javax.baja.job.BJobState.failed -->
<field name="failed"  public="true" static="true" final="true">
<type class="javax.baja.job.BJobState"/>
<description>
BJobState constant for failed.
</description>
</field>

<!-- javax.baja.job.BJobState.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.job.BJobState"/>
<description/>
</field>

<!-- javax.baja.job.BJobState.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
