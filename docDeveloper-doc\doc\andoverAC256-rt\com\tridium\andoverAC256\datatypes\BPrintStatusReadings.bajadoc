<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.datatypes.BPrintStatusReadings" name="BPrintStatusReadings" packageName="com.tridium.andoverAC256.datatypes" public="true">
<description>
BPrintStatusReadings - Component that encapsulates all relevant&#xa; data from the Andover PRINT STATUS response
</description>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@creation">10/13/2004 1:05PM</tag>
<tag name="@version">$Revision$ $Date: 10/13/2004 1:05PM$</tag>
<tag name="@since">Niagara 3.0 andoverAC256 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="unitNumber" flags="rtd">
<type class="int"/>
<description>
Slot for the &lt;code&gt;unitNumber&lt;/code&gt; property.&#xa; A display only parameter that is parsed from&#xa; the responsse to the PRINT STATUS message (issued&#xa; at startup and then once an hour by doPing())
</description>
<tag name="@see">#getUnitNumber</tag>
<tag name="@see">#setUnitNumber</tag>
</property>

<property name="numberAttachedIOUs" flags="rtd">
<type class="int"/>
<description>
Slot for the &lt;code&gt;numberAttachedIOUs&lt;/code&gt; property.&#xa; A display only parameter that is parsed from&#xa; the responsse to the PRINT STATUS message (issued&#xa; at startup and then once an hour by doPing()&#xa; Is used when learning points from IOUs
</description>
<tag name="@see">#getNumberAttachedIOUs</tag>
<tag name="@see">#setNumberAttachedIOUs</tag>
</property>

<property name="iouCommFaults" flags="rtd">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;iouCommFaults&lt;/code&gt; property.&#xa; A display only parameter that is parsed from&#xa; the responsse to the PRINT STATUS message (issued&#xa; at startup and then once an hour by doPing()&#xa; Is used when learning points from IOUs
</description>
<tag name="@see">#getIouCommFaults</tag>
<tag name="@see">#setIouCommFaults</tag>
</property>

<property name="iouOverrides" flags="rtd">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;iouOverrides&lt;/code&gt; property.&#xa; A display only parameter that is parsed from&#xa; the responsse to the PRINT STATUS message (issued&#xa; at startup and then once an hour by doPing()&#xa; Is not used elsewhere by the driver code.
</description>
<tag name="@see">#getIouOverrides</tag>
<tag name="@see">#setIouOverrides</tag>
</property>

<property name="numberLCUs" flags="rtd">
<type class="int"/>
<description>
Slot for the &lt;code&gt;numberLCUs&lt;/code&gt; property.&#xa; A display only parameter that is parsed from&#xa; the responsse to the PRINT STATUS message (issued&#xa; at startup and then once an hour by doPing()&#xa; Is used when learning points from LCUs
</description>
<tag name="@see">#getNumberLCUs</tag>
<tag name="@see">#setNumberLCUs</tag>
</property>

<property name="disabledVarClasses" flags="rtd">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;disabledVarClasses&lt;/code&gt; property.&#xa; A display only parameter that is parsed from&#xa; the responsse to the PRINT STATUS message (issued&#xa; at startup and then once an hour by doPing()&#xa; Is not used elsewhere by the driver code.
</description>
<tag name="@see">#getDisabledVarClasses</tag>
<tag name="@see">#setDisabledVarClasses</tag>
</property>

<property name="numberFailures" flags="rtd">
<type class="int"/>
<description>
Slot for the &lt;code&gt;numberFailures&lt;/code&gt; property.&#xa; A display only parameter that is parsed from&#xa; the responsse to the PRINT STATUS message (issued&#xa; at startup and then once an hour by doPing()&#xa; Is not used elsewhere by the driver code.
</description>
<tag name="@see">#getNumberFailures</tag>
<tag name="@see">#setNumberFailures</tag>
</property>

<property name="andoverDateTime" flags="rtd">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;andoverDateTime&lt;/code&gt; property.&#xa; A display only parameter that is parsed from&#xa; the responsse to the PRINT STATUS message (issued&#xa; at startup and then once an hour by doPing()&#xa; Is used for comparison to the jaceDateTime&#xa; during clock synchronization.
</description>
<tag name="@see">#getAndoverDateTime</tag>
<tag name="@see">#setAndoverDateTime</tag>
</property>

</class>
</bajadoc>
