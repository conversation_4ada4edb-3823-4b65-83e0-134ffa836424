<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.util.EnumRangeWrapper" name="EnumRangeWrapper" packageName="javax.baja.bacnet.util" public="true">
<description>
Wrap the BEnumRange and potential ErrorType while&#xa; creating the enum range
</description>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- javax.baja.bacnet.util.EnumRangeWrapper() -->
<constructor name="EnumRangeWrapper" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.util.EnumRangeWrapper.getEnumRange() -->
<method name="getEnumRange"  public="true">
<description>
Returns the value of enum range
</description>
<return>
<type class="javax.baja.sys.BEnumRange"/>
<description>
enumRange
</description>
</return>
</method>

<!-- javax.baja.bacnet.util.EnumRangeWrapper.setEnumRange(javax.baja.sys.BEnumRange) -->
<method name="setEnumRange"  public="true">
<description>
Set the value of enum range
</description>
<parameter name="enumRange">
<type class="javax.baja.sys.BEnumRange"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.EnumRangeWrapper.getErrorType() -->
<method name="getErrorType"  public="true">
<description>
Get the error type associated with enum range creation
</description>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
errorType
</description>
</return>
</method>

<!-- javax.baja.bacnet.util.EnumRangeWrapper.markError(javax.baja.bacnet.io.ErrorType) -->
<method name="markError"  public="true">
<description>
Mark the error type.
</description>
<parameter name="errorType">
<type class="javax.baja.bacnet.io.ErrorType"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.EnumRangeWrapper.make(javax.baja.sys.BEnumRange, javax.baja.bacnet.io.ErrorType) -->
<method name="make"  public="true" static="true">
<description>
factory method to make instance of EnumRangeWrapper
</description>
<parameter name="enumRange">
<type class="javax.baja.sys.BEnumRange"/>
<description/>
</parameter>
<parameter name="errorType">
<type class="javax.baja.bacnet.io.ErrorType"/>
<description/>
</parameter>
<return>
<type class="javax.baja.bacnet.util.EnumRangeWrapper"/>
<description>
enumRangeWrapper
</description>
</return>
</method>

</class>
</bajadoc>
