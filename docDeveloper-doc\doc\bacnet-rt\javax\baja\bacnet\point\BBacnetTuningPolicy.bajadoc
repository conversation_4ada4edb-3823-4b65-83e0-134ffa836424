<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.point.BBacnetTuningPolicy" name="BBacnetTuningPolicy" packageName="javax.baja.bacnet.point" public="true">
<description>
BBacnetTuningPolicy.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">08 Jul 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.driver.point.BTuningPolicy"/>
</extends>
<property name="pollFrequency" flags="">
<type class="javax.baja.driver.util.BPollFrequency"/>
<description>
Slot for the &lt;code&gt;pollFrequency&lt;/code&gt; property.
</description>
<tag name="@see">#getPollFrequency</tag>
<tag name="@see">#setPollFrequency</tag>
</property>

<property name="useCov" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;useCov&lt;/code&gt; property.&#xa; flag indicating if Niagara will use COV notification services to&#xa; receive data about points in this device for which COV is supported.
</description>
<tag name="@see">#getUseCov</tag>
<tag name="@see">#setUseCov</tag>
</property>

<property name="useConfirmedCov" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;useConfirmedCov&lt;/code&gt; property.&#xa; flag indicating if Niagara will request confirmed (true)&#xa; or unconfirmed (false) COV notifications.
</description>
<tag name="@see">#getUseConfirmedCov</tag>
<tag name="@see">#setUseConfirmedCov</tag>
</property>

<property name="covSubscriptionLifetime" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;covSubscriptionLifetime&lt;/code&gt; property.&#xa; the lifetime, in minutes, for which Niagara will subscribe for COV&#xa; notifications.  A value of zero means an indefinite lifetime,&#xa; although this is not guaranteed to persist across resets of the&#xa; server device.
</description>
<tag name="@see">#getCovSubscriptionLifetime</tag>
<tag name="@see">#setCovSubscriptionLifetime</tag>
</property>

<property name="useCovProperty" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;useCovProperty&lt;/code&gt; property.&#xa; flag indicating if Niagara will use COV Property notification services to&#xa; receive data about points in this device for which COV Property is supported.
</description>
<tag name="@see">#getUseCovProperty</tag>
<tag name="@see">#setUseCovProperty</tag>
</property>

<property name="useConfirmedCovProperty" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;useConfirmedCovProperty&lt;/code&gt; property.&#xa; flag indicating if Niagara will request confirmed (true)&#xa; or unconfirmed (false) COV notifications on COV Property request.
</description>
<tag name="@see">#getUseConfirmedCovProperty</tag>
<tag name="@see">#setUseConfirmedCovProperty</tag>
</property>

<property name="covPropertyIncrement" flags="">
<type class="double"/>
<description>
Slot for the &lt;code&gt;covPropertyIncrement&lt;/code&gt; property.&#xa; COV notifications to send Cov Increment&#xa; this property is be applicable for a COV Property of type numeric
</description>
<tag name="@see">#getCovPropertyIncrement</tag>
<tag name="@see">#setCovPropertyIncrement</tag>
</property>

<property name="covPropertySubscriptionLifetime" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;covPropertySubscriptionLifetime&lt;/code&gt; property.&#xa; the lifetime, in minutes, for which Niagara will subscribe COV Property for COV&#xa; notifications.  A value of zero means an indefinite lifetime,&#xa; although this is not guaranteed to persist across resets of the&#xa; server device.
</description>
<tag name="@see">#getCovPropertySubscriptionLifetime</tag>
<tag name="@see">#setCovPropertySubscriptionLifetime</tag>
</property>

<property name="acceptUnsolicitedCov" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;acceptUnsolicitedCov&lt;/code&gt; property.&#xa; if true, will allow cov notifications for a polled point to update the point
</description>
<tag name="@see">#getAcceptUnsolicitedCov</tag>
<tag name="@see">#setAcceptUnsolicitedCov</tag>
</property>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy() -->
<constructor name="BBacnetTuningPolicy" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.getPollFrequency() -->
<method name="getPollFrequency"  public="true">
<description>
Get the &lt;code&gt;pollFrequency&lt;/code&gt; property.
</description>
<tag name="@see">#pollFrequency</tag>
<return>
<type class="javax.baja.driver.util.BPollFrequency"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.setPollFrequency(javax.baja.driver.util.BPollFrequency) -->
<method name="setPollFrequency"  public="true">
<description>
Set the &lt;code&gt;pollFrequency&lt;/code&gt; property.
</description>
<tag name="@see">#pollFrequency</tag>
<parameter name="v">
<type class="javax.baja.driver.util.BPollFrequency"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.getUseCov() -->
<method name="getUseCov"  public="true">
<description>
Get the &lt;code&gt;useCov&lt;/code&gt; property.&#xa; flag indicating if Niagara will use COV notification services to&#xa; receive data about points in this device for which COV is supported.
</description>
<tag name="@see">#useCov</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.setUseCov(boolean) -->
<method name="setUseCov"  public="true">
<description>
Set the &lt;code&gt;useCov&lt;/code&gt; property.&#xa; flag indicating if Niagara will use COV notification services to&#xa; receive data about points in this device for which COV is supported.
</description>
<tag name="@see">#useCov</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.getUseConfirmedCov() -->
<method name="getUseConfirmedCov"  public="true">
<description>
Get the &lt;code&gt;useConfirmedCov&lt;/code&gt; property.&#xa; flag indicating if Niagara will request confirmed (true)&#xa; or unconfirmed (false) COV notifications.
</description>
<tag name="@see">#useConfirmedCov</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.setUseConfirmedCov(boolean) -->
<method name="setUseConfirmedCov"  public="true">
<description>
Set the &lt;code&gt;useConfirmedCov&lt;/code&gt; property.&#xa; flag indicating if Niagara will request confirmed (true)&#xa; or unconfirmed (false) COV notifications.
</description>
<tag name="@see">#useConfirmedCov</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.getCovSubscriptionLifetime() -->
<method name="getCovSubscriptionLifetime"  public="true">
<description>
Get the &lt;code&gt;covSubscriptionLifetime&lt;/code&gt; property.&#xa; the lifetime, in minutes, for which Niagara will subscribe for COV&#xa; notifications.  A value of zero means an indefinite lifetime,&#xa; although this is not guaranteed to persist across resets of the&#xa; server device.
</description>
<tag name="@see">#covSubscriptionLifetime</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.setCovSubscriptionLifetime(int) -->
<method name="setCovSubscriptionLifetime"  public="true">
<description>
Set the &lt;code&gt;covSubscriptionLifetime&lt;/code&gt; property.&#xa; the lifetime, in minutes, for which Niagara will subscribe for COV&#xa; notifications.  A value of zero means an indefinite lifetime,&#xa; although this is not guaranteed to persist across resets of the&#xa; server device.
</description>
<tag name="@see">#covSubscriptionLifetime</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.getUseCovProperty() -->
<method name="getUseCovProperty"  public="true">
<description>
Get the &lt;code&gt;useCovProperty&lt;/code&gt; property.&#xa; flag indicating if Niagara will use COV Property notification services to&#xa; receive data about points in this device for which COV Property is supported.
</description>
<tag name="@see">#useCovProperty</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.setUseCovProperty(boolean) -->
<method name="setUseCovProperty"  public="true">
<description>
Set the &lt;code&gt;useCovProperty&lt;/code&gt; property.&#xa; flag indicating if Niagara will use COV Property notification services to&#xa; receive data about points in this device for which COV Property is supported.
</description>
<tag name="@see">#useCovProperty</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.getUseConfirmedCovProperty() -->
<method name="getUseConfirmedCovProperty"  public="true">
<description>
Get the &lt;code&gt;useConfirmedCovProperty&lt;/code&gt; property.&#xa; flag indicating if Niagara will request confirmed (true)&#xa; or unconfirmed (false) COV notifications on COV Property request.
</description>
<tag name="@see">#useConfirmedCovProperty</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.setUseConfirmedCovProperty(boolean) -->
<method name="setUseConfirmedCovProperty"  public="true">
<description>
Set the &lt;code&gt;useConfirmedCovProperty&lt;/code&gt; property.&#xa; flag indicating if Niagara will request confirmed (true)&#xa; or unconfirmed (false) COV notifications on COV Property request.
</description>
<tag name="@see">#useConfirmedCovProperty</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.getCovPropertyIncrement() -->
<method name="getCovPropertyIncrement"  public="true">
<description>
Get the &lt;code&gt;covPropertyIncrement&lt;/code&gt; property.&#xa; COV notifications to send Cov Increment&#xa; this property is be applicable for a COV Property of type numeric
</description>
<tag name="@see">#covPropertyIncrement</tag>
<return>
<type class="double"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.setCovPropertyIncrement(double) -->
<method name="setCovPropertyIncrement"  public="true">
<description>
Set the &lt;code&gt;covPropertyIncrement&lt;/code&gt; property.&#xa; COV notifications to send Cov Increment&#xa; this property is be applicable for a COV Property of type numeric
</description>
<tag name="@see">#covPropertyIncrement</tag>
<parameter name="v">
<type class="double"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.getCovPropertySubscriptionLifetime() -->
<method name="getCovPropertySubscriptionLifetime"  public="true">
<description>
Get the &lt;code&gt;covPropertySubscriptionLifetime&lt;/code&gt; property.&#xa; the lifetime, in minutes, for which Niagara will subscribe COV Property for COV&#xa; notifications.  A value of zero means an indefinite lifetime,&#xa; although this is not guaranteed to persist across resets of the&#xa; server device.
</description>
<tag name="@see">#covPropertySubscriptionLifetime</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.setCovPropertySubscriptionLifetime(int) -->
<method name="setCovPropertySubscriptionLifetime"  public="true">
<description>
Set the &lt;code&gt;covPropertySubscriptionLifetime&lt;/code&gt; property.&#xa; the lifetime, in minutes, for which Niagara will subscribe COV Property for COV&#xa; notifications.  A value of zero means an indefinite lifetime,&#xa; although this is not guaranteed to persist across resets of the&#xa; server device.
</description>
<tag name="@see">#covPropertySubscriptionLifetime</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.getAcceptUnsolicitedCov() -->
<method name="getAcceptUnsolicitedCov"  public="true">
<description>
Get the &lt;code&gt;acceptUnsolicitedCov&lt;/code&gt; property.&#xa; if true, will allow cov notifications for a polled point to update the point
</description>
<tag name="@see">#acceptUnsolicitedCov</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.setAcceptUnsolicitedCov(boolean) -->
<method name="setAcceptUnsolicitedCov"  public="true">
<description>
Set the &lt;code&gt;acceptUnsolicitedCov&lt;/code&gt; property.&#xa; if true, will allow cov notifications for a polled point to update the point
</description>
<tag name="@see">#acceptUnsolicitedCov</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<description>
Can only go in a BacnetTuningPolicyMap.
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.pollFrequency -->
<field name="pollFrequency"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;pollFrequency&lt;/code&gt; property.
</description>
<tag name="@see">#getPollFrequency</tag>
<tag name="@see">#setPollFrequency</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.useCov -->
<field name="useCov"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;useCov&lt;/code&gt; property.&#xa; flag indicating if Niagara will use COV notification services to&#xa; receive data about points in this device for which COV is supported.
</description>
<tag name="@see">#getUseCov</tag>
<tag name="@see">#setUseCov</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.useConfirmedCov -->
<field name="useConfirmedCov"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;useConfirmedCov&lt;/code&gt; property.&#xa; flag indicating if Niagara will request confirmed (true)&#xa; or unconfirmed (false) COV notifications.
</description>
<tag name="@see">#getUseConfirmedCov</tag>
<tag name="@see">#setUseConfirmedCov</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.covSubscriptionLifetime -->
<field name="covSubscriptionLifetime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;covSubscriptionLifetime&lt;/code&gt; property.&#xa; the lifetime, in minutes, for which Niagara will subscribe for COV&#xa; notifications.  A value of zero means an indefinite lifetime,&#xa; although this is not guaranteed to persist across resets of the&#xa; server device.
</description>
<tag name="@see">#getCovSubscriptionLifetime</tag>
<tag name="@see">#setCovSubscriptionLifetime</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.useCovProperty -->
<field name="useCovProperty"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;useCovProperty&lt;/code&gt; property.&#xa; flag indicating if Niagara will use COV Property notification services to&#xa; receive data about points in this device for which COV Property is supported.
</description>
<tag name="@see">#getUseCovProperty</tag>
<tag name="@see">#setUseCovProperty</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.useConfirmedCovProperty -->
<field name="useConfirmedCovProperty"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;useConfirmedCovProperty&lt;/code&gt; property.&#xa; flag indicating if Niagara will request confirmed (true)&#xa; or unconfirmed (false) COV notifications on COV Property request.
</description>
<tag name="@see">#getUseConfirmedCovProperty</tag>
<tag name="@see">#setUseConfirmedCovProperty</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.covPropertyIncrement -->
<field name="covPropertyIncrement"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;covPropertyIncrement&lt;/code&gt; property.&#xa; COV notifications to send Cov Increment&#xa; this property is be applicable for a COV Property of type numeric
</description>
<tag name="@see">#getCovPropertyIncrement</tag>
<tag name="@see">#setCovPropertyIncrement</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.covPropertySubscriptionLifetime -->
<field name="covPropertySubscriptionLifetime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;covPropertySubscriptionLifetime&lt;/code&gt; property.&#xa; the lifetime, in minutes, for which Niagara will subscribe COV Property for COV&#xa; notifications.  A value of zero means an indefinite lifetime,&#xa; although this is not guaranteed to persist across resets of the&#xa; server device.
</description>
<tag name="@see">#getCovPropertySubscriptionLifetime</tag>
<tag name="@see">#setCovPropertySubscriptionLifetime</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.acceptUnsolicitedCov -->
<field name="acceptUnsolicitedCov"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;acceptUnsolicitedCov&lt;/code&gt; property.&#xa; if true, will allow cov notifications for a polled point to update the point
</description>
<tag name="@see">#getAcceptUnsolicitedCov</tag>
<tag name="@see">#setAcceptUnsolicitedCov</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicy.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
