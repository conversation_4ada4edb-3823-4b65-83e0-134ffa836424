<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BBacnetFile" name="BBacnetFile" packageName="javax.baja.bacnet.config" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 7$ $Date: 12/10/01 9:26:02 AM$</tag>
<tag name="@creation">30 Jan 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.BBacnetObject"/>
</extends>
<property name="fileType" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;fileType&lt;/code&gt; property.
</description>
<tag name="@see">#getFileType</tag>
<tag name="@see">#setFileType</tag>
</property>

<property name="fileSize" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;fileSize&lt;/code&gt; property.
</description>
<tag name="@see">#getFileSize</tag>
<tag name="@see">#setFileSize</tag>
</property>

<property name="modificationDate" flags="r">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
<description>
Slot for the &lt;code&gt;modificationDate&lt;/code&gt; property.
</description>
<tag name="@see">#getModificationDate</tag>
<tag name="@see">#setModificationDate</tag>
</property>

<property name="archive" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;archive&lt;/code&gt; property.&#xa; has this file been archived?&#xa; TRUE if no changes have been made since the last time&#xa; the object was archived.
</description>
<tag name="@see">#getArchive</tag>
<tag name="@see">#setArchive</tag>
</property>

<property name="readOnly" flags="r">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;readOnly&lt;/code&gt; property.
</description>
<tag name="@see">#getReadOnly</tag>
<tag name="@see">#setReadOnly</tag>
</property>

<property name="fileAccessMethod" flags="r">
<type class="javax.baja.bacnet.enums.BBacnetFileAccessMethod"/>
<description>
Slot for the &lt;code&gt;fileAccessMethod&lt;/code&gt; property.
</description>
<tag name="@see">#getFileAccessMethod</tag>
<tag name="@see">#setFileAccessMethod</tag>
</property>

<property name="fileOrd" flags="d">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;fileOrd&lt;/code&gt; property.&#xa; the ord to the local storage for this file&#x27;s contents.
</description>
<tag name="@see">#getFileOrd</tag>
<tag name="@see">#setFileOrd</tag>
</property>

<action name="read" flags="">
<return>
<type class="javax.baja.sys.BBlob"/>
</return>
<description>
Slot for the &lt;code&gt;read&lt;/code&gt; action.&#xa; Read file data and return the data as a BBlob.
</description>
<tag name="@see">#read()</tag>
</action>

<action name="write" flags="">
<parameter name="parameter">
<type class="javax.baja.sys.BBlob"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;write&lt;/code&gt; action.&#xa; Write file data given as a BBlob.
</description>
<tag name="@see">#write(BBlob parameter)</tag>
</action>

<action name="readFile" flags="h">
<parameter name="parameter">
<type class="javax.baja.sys.BStruct"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;readFile&lt;/code&gt; action.&#xa; Read &#x27;count&#x27; bytes or records from the file referenced by this&#xa; File object from the Bacnet device, beginning at the record&#xa; or byte designated by &#x27;start&#x27;.&#xa; Store it locally in the file referenced by filename.
</description>
<tag name="@see">#readFile(BStruct parameter)</tag>
</action>

<action name="writeFile" flags="h">
<parameter name="parameter">
<type class="javax.baja.sys.BStruct"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;writeFile&lt;/code&gt; action.&#xa; Write to the file referenced by this File object.&#xa; Use the file given by the argument as the source file.
</description>
<tag name="@see">#writeFile(BStruct parameter)</tag>
</action>

<!-- javax.baja.bacnet.config.BBacnetFile() -->
<constructor name="BBacnetFile" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetFile.getFileType() -->
<method name="getFileType"  public="true">
<description>
Get the &lt;code&gt;fileType&lt;/code&gt; property.
</description>
<tag name="@see">#fileType</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.setFileType(java.lang.String) -->
<method name="setFileType"  public="true">
<description>
Set the &lt;code&gt;fileType&lt;/code&gt; property.
</description>
<tag name="@see">#fileType</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.getFileSize() -->
<method name="getFileSize"  public="true">
<description>
Get the &lt;code&gt;fileSize&lt;/code&gt; property.
</description>
<tag name="@see">#fileSize</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.setFileSize(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setFileSize"  public="true">
<description>
Set the &lt;code&gt;fileSize&lt;/code&gt; property.
</description>
<tag name="@see">#fileSize</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.getModificationDate() -->
<method name="getModificationDate"  public="true">
<description>
Get the &lt;code&gt;modificationDate&lt;/code&gt; property.
</description>
<tag name="@see">#modificationDate</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.setModificationDate(javax.baja.bacnet.datatypes.BBacnetDateTime) -->
<method name="setModificationDate"  public="true">
<description>
Set the &lt;code&gt;modificationDate&lt;/code&gt; property.
</description>
<tag name="@see">#modificationDate</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.getArchive() -->
<method name="getArchive"  public="true">
<description>
Get the &lt;code&gt;archive&lt;/code&gt; property.&#xa; has this file been archived?&#xa; TRUE if no changes have been made since the last time&#xa; the object was archived.
</description>
<tag name="@see">#archive</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.setArchive(boolean) -->
<method name="setArchive"  public="true">
<description>
Set the &lt;code&gt;archive&lt;/code&gt; property.&#xa; has this file been archived?&#xa; TRUE if no changes have been made since the last time&#xa; the object was archived.
</description>
<tag name="@see">#archive</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.getReadOnly() -->
<method name="getReadOnly"  public="true">
<description>
Get the &lt;code&gt;readOnly&lt;/code&gt; property.
</description>
<tag name="@see">#readOnly</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.setReadOnly(boolean) -->
<method name="setReadOnly"  public="true">
<description>
Set the &lt;code&gt;readOnly&lt;/code&gt; property.
</description>
<tag name="@see">#readOnly</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.getFileAccessMethod() -->
<method name="getFileAccessMethod"  public="true">
<description>
Get the &lt;code&gt;fileAccessMethod&lt;/code&gt; property.
</description>
<tag name="@see">#fileAccessMethod</tag>
<return>
<type class="javax.baja.bacnet.enums.BBacnetFileAccessMethod"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.setFileAccessMethod(javax.baja.bacnet.enums.BBacnetFileAccessMethod) -->
<method name="setFileAccessMethod"  public="true">
<description>
Set the &lt;code&gt;fileAccessMethod&lt;/code&gt; property.
</description>
<tag name="@see">#fileAccessMethod</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.BBacnetFileAccessMethod"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.getFileOrd() -->
<method name="getFileOrd"  public="true">
<description>
Get the &lt;code&gt;fileOrd&lt;/code&gt; property.&#xa; the ord to the local storage for this file&#x27;s contents.
</description>
<tag name="@see">#fileOrd</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.setFileOrd(javax.baja.naming.BOrd) -->
<method name="setFileOrd"  public="true">
<description>
Set the &lt;code&gt;fileOrd&lt;/code&gt; property.&#xa; the ord to the local storage for this file&#x27;s contents.
</description>
<tag name="@see">#fileOrd</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.read() -->
<method name="read"  public="true">
<description>
Invoke the &lt;code&gt;read&lt;/code&gt; action.&#xa; Read file data and return the data as a BBlob.
</description>
<tag name="@see">#read</tag>
<return>
<type class="javax.baja.sys.BBlob"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.write(javax.baja.sys.BBlob) -->
<method name="write"  public="true">
<description>
Invoke the &lt;code&gt;write&lt;/code&gt; action.&#xa; Write file data given as a BBlob.
</description>
<tag name="@see">#write</tag>
<parameter name="parameter">
<type class="javax.baja.sys.BBlob"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.readFile(javax.baja.sys.BStruct) -->
<method name="readFile"  public="true">
<description>
Invoke the &lt;code&gt;readFile&lt;/code&gt; action.&#xa; Read &#x27;count&#x27; bytes or records from the file referenced by this&#xa; File object from the Bacnet device, beginning at the record&#xa; or byte designated by &#x27;start&#x27;.&#xa; Store it locally in the file referenced by filename.
</description>
<tag name="@see">#readFile</tag>
<parameter name="parameter">
<type class="javax.baja.sys.BStruct"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.writeFile(javax.baja.sys.BStruct) -->
<method name="writeFile"  public="true">
<description>
Invoke the &lt;code&gt;writeFile&lt;/code&gt; action.&#xa; Write to the file referenced by this File object.&#xa; Use the file given by the argument as the source file.
</description>
<tag name="@see">#writeFile</tag>
<parameter name="parameter">
<type class="javax.baja.sys.BStruct"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.started() -->
<method name="started"  public="true">
<description>
Register with the Bacnet service when this component is started.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.stopped() -->
<method name="stopped"  public="true">
<description>
Stopped.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<description>
Property Changed.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.readFile(javax.baja.bacnet.BBacnetDevice, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="readFile"  public="true" static="true">
<description/>
<parameter name="device">
<type class="javax.baja.bacnet.BBacnetDevice"/>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="byte" dimension="1"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.doRead() -->
<method name="doRead"  public="true">
<description/>
<return>
<type class="javax.baja.sys.BBlob"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.doReadFile(javax.baja.sys.BStruct) -->
<method name="doReadFile"  public="true">
<description/>
<parameter name="arg">
<type class="javax.baja.sys.BStruct"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.doWriteFile(javax.baja.sys.BStruct) -->
<method name="doWriteFile"  public="true">
<description/>
<parameter name="arg">
<type class="javax.baja.sys.BStruct"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.writeFile(javax.baja.bacnet.BBacnetDevice, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, byte[]) -->
<method name="writeFile"  public="true" static="true">
<description/>
<parameter name="device">
<type class="javax.baja.bacnet.BBacnetDevice"/>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="fileData">
<type class="byte" dimension="1"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.writeFile(javax.baja.bacnet.BBacnetDevice, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, javax.baja.bacnet.datatypes.BBacnetOctetString[]) -->
<method name="writeFile"  public="true" static="true">
<description/>
<parameter name="device">
<type class="javax.baja.bacnet.BBacnetDevice"/>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="count">
<type class="int"/>
</parameter>
<parameter name="fileRecordData">
<type class="javax.baja.bacnet.datatypes.BBacnetOctetString" dimension="1"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.doWrite(javax.baja.sys.BBlob) -->
<method name="doWrite"  public="true">
<description/>
<parameter name="arg">
<type class="javax.baja.sys.BBlob"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetFile.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetFile.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetFile.fileType -->
<field name="fileType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;fileType&lt;/code&gt; property.
</description>
<tag name="@see">#getFileType</tag>
<tag name="@see">#setFileType</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetFile.fileSize -->
<field name="fileSize"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;fileSize&lt;/code&gt; property.
</description>
<tag name="@see">#getFileSize</tag>
<tag name="@see">#setFileSize</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetFile.modificationDate -->
<field name="modificationDate"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;modificationDate&lt;/code&gt; property.
</description>
<tag name="@see">#getModificationDate</tag>
<tag name="@see">#setModificationDate</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetFile.archive -->
<field name="archive"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;archive&lt;/code&gt; property.&#xa; has this file been archived?&#xa; TRUE if no changes have been made since the last time&#xa; the object was archived.
</description>
<tag name="@see">#getArchive</tag>
<tag name="@see">#setArchive</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetFile.readOnly -->
<field name="readOnly"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;readOnly&lt;/code&gt; property.
</description>
<tag name="@see">#getReadOnly</tag>
<tag name="@see">#setReadOnly</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetFile.fileAccessMethod -->
<field name="fileAccessMethod"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;fileAccessMethod&lt;/code&gt; property.
</description>
<tag name="@see">#getFileAccessMethod</tag>
<tag name="@see">#setFileAccessMethod</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetFile.fileOrd -->
<field name="fileOrd"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;fileOrd&lt;/code&gt; property.&#xa; the ord to the local storage for this file&#x27;s contents.
</description>
<tag name="@see">#getFileOrd</tag>
<tag name="@see">#setFileOrd</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetFile.read -->
<field name="read"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;read&lt;/code&gt; action.&#xa; Read file data and return the data as a BBlob.
</description>
<tag name="@see">#read()</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetFile.write -->
<field name="write"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;write&lt;/code&gt; action.&#xa; Write file data given as a BBlob.
</description>
<tag name="@see">#write(BBlob parameter)</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetFile.readFile -->
<field name="readFile"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;readFile&lt;/code&gt; action.&#xa; Read &#x27;count&#x27; bytes or records from the file referenced by this&#xa; File object from the Bacnet device, beginning at the record&#xa; or byte designated by &#x27;start&#x27;.&#xa; Store it locally in the file referenced by filename.
</description>
<tag name="@see">#readFile(BStruct parameter)</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetFile.writeFile -->
<field name="writeFile"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;writeFile&lt;/code&gt; action.&#xa; Write to the file referenced by this File object.&#xa; Use the file given by the argument as the source file.
</description>
<tag name="@see">#writeFile(BStruct parameter)</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetFile.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
