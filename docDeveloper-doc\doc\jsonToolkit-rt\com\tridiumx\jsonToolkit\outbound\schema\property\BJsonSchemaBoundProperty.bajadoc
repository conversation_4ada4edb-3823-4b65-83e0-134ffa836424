<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBoundProperty" name="BJsonSchemaBoundProperty" packageName="com.tridiumx.jsonToolkit.outbound.schema.property" public="true">
<description>
A json property (key/value pair) whose value is bound to a selected station component/slot ord target.&#xa;&#xa; The json key is defined by this components name source rule (e.g display name of this / ord target display name etc)&#xa; The json value is the result of calling get() on the target ord slot.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaBoundMember"/>
</extends>
<implements>
<parameterizedType class="com.tridiumx.jsonToolkit.outbound.schema.BIJsonProperty">
<args>
<type class="java.lang.Object"/>
</args>
</parameterizedType>
</implements>
<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBoundProperty() -->
<constructor name="BJsonSchemaBoundProperty" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBoundProperty.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBoundProperty.make(javax.baja.naming.BOrd) -->
<method name="make"  public="true" static="true">
<description/>
<parameter name="target">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBoundProperty"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBoundProperty.getJsonValue() -->
<method name="getJsonValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.Object"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBoundProperty.getPropertiesToIncludeInJson(javax.baja.sys.BComplex) -->
<method name="getPropertiesToIncludeInJson"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="resolvedTarget">
<type class="javax.baja.sys.BComplex"/>
</parameter>
<return>
<parameterizedType class="java.util.List">
<args>
<type class="java.lang.String"/>
</args>
</parameterizedType>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBoundProperty.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBoundProperty.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
