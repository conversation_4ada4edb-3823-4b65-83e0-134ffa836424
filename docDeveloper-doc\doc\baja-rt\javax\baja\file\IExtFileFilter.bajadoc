<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.IExtFileFilter" name="IExtFileFilter" packageName="javax.baja.file" public="true" interface="true" abstract="true" category="interface">
<description>
An interface for file filters that support filtering based on one or more file extensions.
</description>
<tag name="@since">Niagara 4.8</tag>
<implements>
<type class="javax.baja.file.IFileFilter"/>
</implements>
<!-- javax.baja.file.IExtFileFilter.acceptExtension(java.lang.String) -->
<method name="acceptExtension"  public="true" abstract="true">
<description>
Check if the given extension is acceptable by this filter.
</description>
<tag name="@since">Niagara 4.8</tag>
<parameter name="extension">
<type class="java.lang.String"/>
<description>
the extension to check
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the extension is acceptable, false otherwise
</description>
</return>
</method>

<!-- javax.baja.file.IExtFileFilter.getDefaultExtension() -->
<method name="getDefaultExtension"  public="true" abstract="true">
<description>
A single file filter can support multiple &#x22;valid&#x22; extensions (e.g. &#x22;.xls&#x22; / &#x22;.xlsx&#x22;).&#xa; This method returns the extension that is the &#x22;main&#x22; or default extension&#xa; for this file filter. This could be used to, for example, auto-fill a file extension&#xa; for a file when the filter is selected.
</description>
<tag name="@since">Niagara 4.8</tag>
<return>
<type class="java.lang.String"/>
<description>
the default extension for this filter, or null if there is no default extension
</description>
</return>
</method>

</class>
</bajadoc>
