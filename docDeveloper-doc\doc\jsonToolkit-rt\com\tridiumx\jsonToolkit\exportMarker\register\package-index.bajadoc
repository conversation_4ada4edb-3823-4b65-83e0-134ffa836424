<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.exportMarker.register">
<description/>
<class packageName="com.tridiumx.jsonToolkit.exportMarker.register" name="BExportMarkerRegister"><description>Allows registration of export markers in the station to facilitate setpoint routing and export filtering of&#xa; export marked control points.</description></class>
</package>
</bajadoc>
