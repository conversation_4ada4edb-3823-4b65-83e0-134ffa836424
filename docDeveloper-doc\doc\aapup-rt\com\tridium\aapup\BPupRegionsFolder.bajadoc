<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.BPupRegionsFolder" name="BPupRegionsFolder" packageName="com.tridium.aapup" public="true">
<description>
BPupRegionsFolder is the standard container to use&#xa; under BPupDevice to organize BPupRegionRecords.
</description>
<tag name="@author">Cli<PERSON></tag>
<tag name="@creation">8/12/2005 2:50PM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.0.90</tag>
<extends>
<type class="javax.baja.util.BFolder"/>
</extends>
</class>
</bajadoc>
