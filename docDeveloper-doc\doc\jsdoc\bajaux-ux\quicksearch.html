<html>
<head>
</head>
<body style="background: transparent;">
    <script src="scripts/docstrap.lib.js"></script>
    <script src="scripts/lunr.min.js"></script>
    <script src="scripts/fulltext-search.js"></script>

    <script type="text/x-docstrap-searchdb">
    {"modules.list.html":{"id":"modules.list.html","title":"Modules","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Modules Classes module:bajaux/commands/Command module:bajaux/commands/CommandGroup module:bajaux/commands/ToggleCommand module:bajaux/commands/ToggleCommandGroup module:bajaux/container/wb/Clipboard module:bajaux/container/wb/StringList module:bajaux/dragdrop/Envelope module:bajaux/dragdrop/NavNodeEnvelope module:bajaux/dragdrop/StringEnvelope module:bajaux/lifecycle/WidgetManager module:bajaux/mixin/batchLoadMixin module:bajaux/mixin/batchSaveMixin module:bajaux/mixin/responsiveMixIn module:bajaux/Properties module:bajaux/registry/Registry module:bajaux/registry/RegistryEntry module:bajaux/util/CommandButton module:bajaux/util/CommandButtonGroup module:bajaux/util/SaveCommand module:bajaux/Validators module:bajaux/Widget × Search results Close "},"interfaces.list.html":{"id":"interfaces.list.html","title":"Interfaces","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Interfaces Classes module:bajaux/commands/Command module:bajaux/commands/CommandGroup module:bajaux/commands/ToggleCommand module:bajaux/commands/ToggleCommandGroup module:bajaux/container/wb/Clipboard module:bajaux/container/wb/StringList module:bajaux/dragdrop/Envelope module:bajaux/dragdrop/NavNodeEnvelope module:bajaux/dragdrop/StringEnvelope module:bajaux/lifecycle/WidgetManager module:bajaux/mixin/batchLoadMixin module:bajaux/mixin/batchSaveMixin module:bajaux/mixin/responsiveMixIn module:bajaux/Properties module:bajaux/registry/Registry module:bajaux/registry/RegistryEntry module:bajaux/util/CommandButton module:bajaux/util/CommandButtonGroup module:bajaux/util/SaveCommand module:bajaux/Validators module:bajaux/Widget × Search results Close "},"tutorials.list.html":{"id":"tutorials.list.html","title":"Tutorials","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Tutorials Classes module:bajaux/commands/Command module:bajaux/commands/CommandGroup module:bajaux/commands/ToggleCommand module:bajaux/commands/ToggleCommandGroup module:bajaux/container/wb/Clipboard module:bajaux/container/wb/StringList module:bajaux/dragdrop/Envelope module:bajaux/dragdrop/NavNodeEnvelope module:bajaux/dragdrop/StringEnvelope module:bajaux/lifecycle/WidgetManager module:bajaux/mixin/batchLoadMixin module:bajaux/mixin/batchSaveMixin module:bajaux/mixin/responsiveMixIn module:bajaux/Properties module:bajaux/registry/Registry module:bajaux/registry/RegistryEntry module:bajaux/util/CommandButton module:bajaux/util/CommandButtonGroup module:bajaux/util/SaveCommand module:bajaux/Validators module:bajaux/Widget × Search results Close "},"index.html":{"id":"index.html","title":"Index","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel The bajaux framework enables developers to create widgets for Niagara using HTML5 and JavaScript. Why bajaux? bajaui is the existing, Java-based, Niagara UI framework for use in Workbench. It has been in use since the very first release of Niagara AX. It is still in widespread use and will be supported for some time. But it has two primary shortcomings that bajaux solves. With bajaux, Workbench is no longer a requirement. If you write a view using bajaui, then the only way your customer can access it is through Workbench or a standalone Java application. In many cases, we want them to be able to just open their laptop or phone and start using the software right away, in their web browser. Because bajaux is based on HTML5 and JavaScript, it works natively in the web browser - no Java required. Write your view once, run it anywhere. Previously, if you wanted your UI to work natively in both Workbench and the browser, you had to write it at least twice: once using bajaui for use in Workbench, and once using hx for use in the browser. (If you wanted to target the now-deprecated Niagara Mobile framework as well, make that three times!) Now, bajaux works natively in the web browser. But it also works natively in Workbench! Workbench supports bajaux views through the use of a web browser embedded directly into Workbench. You can write one bajaux view and have it work in Workbench, Px, and the browser. This reduces development effort and ensures that your users get a consistent user experience. Not only will your widget display in Workbench, it will also integrate into the given environment. For instance, commands defined for any widgets will render in Workbench's Java toolbar. Also, users can drag and drop from the nav tree onto a bajaux widget, both in Workbench and the browser! Widget Widget is the core API of bajaux. Adhering to the Widget APIs will ensure your UI works consistently in all supported environments. A Widget has a well-defined lifecycle. It is created, performs its needed functionality, and then is destroyed. Because bajaux strives to be as unopinionated as possible, you have the freedom to decide exactly what your Widget will do at each point in its lifecycle. Understanding these points is key to understanding how Widgets work. Let's take a look at the points in a Widget lifecycle in some more detail. Construct A Widget is a JavaScript object like any other. You create an instance of it using the new keyword, and define your own behaviors by subclassing it. Here's a very simple example. class MyWidget extends Widget { constructor(params) { super({ params, defaults: { properties: { foo: 'bar' } } }); } } const myWidget = new MyWidget({ properties: { baz: 'buzz' } }); Don't worry, we'll examine the actual API in more detail a bit later. The key takeaways from this code snippet are: A Widget can have default values for its attributes when it is constructed. (Consider how Niagara Components can have default values for their frozen Slots.) You can set actual values for the Widget's attributes by passing them to the constructor function. Initialize Each Widget instance is bound to a single DOM element and remains bound to that DOM element for its entire lifecycle. The moment at which a Widget is bound to a DOM element is called initialization. class MyWidget extends Widget { doInitialize(dom) { dom.text('Hello world!'); } } const myWidget = new MyWidget(); const dom = $('#myWidgetGoesHere'); myWidget.initialize(dom) .then(() =&gt; { console.log('initialized!'); console.log(myWidget.jq().text()); // Hello world! console.log(Widget.in(dom)); // MyWidget }); Let's see what we can learn from this code snippet. First, see how the dom parameter is a jQuery object. bajaux uses jQuery for all its DOM interactions. (However, there is still a live DOM HTMLElement within that jQuery object, so you are free to use any method of interacting with an HTMLElement you choose.) Next, note how my Widget subclass overrode the doInitialize() method, and then to bind my Widget instance to a DOM element, I called the initialize() method. This is a key pattern that you will see in every Widget. If the method starts with do, you override that method to implement your own behavior, but you do not call it! If the method does not start with do, then that is a method you call. Implement doInitialize(), call initialize(). You'll see this pattern repeated throughout the framework. Initialization is a one-time operation. Once you've initialized a Widget into a DOM element, that Widget will remain bound to that DOM element until it is destroyed. A DOM element can only have one Widget initialized into it at a time, and a Widget can only be initialized into one DOM element at a time. It's a one-to-one relationship. (If you are familiar with other UI frameworks such as React, you may have seen other UI components create their own DOM element in which to live. bajaux Widgets do not create their own DOM element. They are given an existing DOM element, and then they can build out the contents (child nodes) of that DOM element. This is a key difference that is important for understanding bajaux.) Because it's a one-time operation, you can implement doInitialize to set up any invariant (unchanging) conditions for your Widget. A couple of things you might do: If your Widget is to edit a string, you could create an &lt;input type=\"text\"&gt; to hold that String. If your Widget needs to respond to user events, you could arm event handlers such as an onClick on your DOM element. Once initialization is complete, you can get the Widget that is bound to any particular DOM element using the Widget.in() function. The widget's .jq() method is its inverse: it returns the DOM element (as jQuery) that the Widget is bound to. Finally, note that initialize() returns a Promise. Most bajaux operations are asynchronous, since they may need to make network calls to retrieve information, such as Lexicon text, from the station. doInitialize(), like all methods that start with do, can optionally return a Promise to indicate when its work is complete, or (like our example) it may be implemented asynchronously. Load Most UI components are implemented to allow the user to view or interact with a piece of data. A few examples are: Display a string for the user to read. Provide a text editor for the user to edit a string. Provide child editors for the user to edit different properties of an object. In all cases, doLoad() is the override point to allow a piece of data to be loaded into a Widget. Let's look at an example in which we load in a string and simply display it to the user. class MyWidget extends Widget { doInitialize(dom) { dom.html('&lt;span&gt;Your string is: &lt;/span&gt;&lt;span class=\"myString\"/&gt;'); } doLoad(string) { this.jq().children('.myString').text(string); } } const myWidget = new MyWidget(); const dom = $('#myWidgetGoesHere'); myWidget.initialize(dom) .then(() =&gt; myWidget.load('hello world!')) .then(() =&gt; { console.log(dom.text()); // Your string is: hello world! return myWidget.load('hello again!'); }) .then(() =&gt; { console.log(dom.text()); // Your string is: hello again! console.log(myWidget.value()); // hello again! }); We use the one-time operation, doInitialize(), to set up the widget to receive a value to be loaded later. Again, load() is called, not implemented; doLoad() is implemented, not called. doLoad() receives the value being loaded in and updates the DOM to reflect that loaded value. load() can be called as many times as needed, over the lifecycle of the Widget, to reflect different values. Take a look at the .value() method. This returns the Widget's currently loaded value; that is, the last value passed to the Widget's load() method. Also, note the use of .text() instead of .html(). bajaux itself does not perform any XSS sanitization. It is up to you to ensure that any user-provided values are not placed directly into the DOM without being sanitized, as this can open up a reflected XSS attack. Using .text() is one good way to do this; there are many others. Although this widget example is display-only, many widgets that load a value are implemented to allow the user to change that value, and read out a new one. Read doRead() answers the question: \"what value has the user entered?\" As the user interacts with your widget, they will change its current value. They may type in new strings, check or uncheck checkboxes, move sliders, and so on. It's your Widget's responsibility to look at the current state of the DOM, and implement doRead() to resolve an actual value that represents what the user has entered. This is different from .value(), which returns the last value loaded in. If you are creating a field editor, then doRead() should typically resolve a value of a type that is compatible with what was loaded in. If your widget accepts a baja:String to its load() method, it would typically resolve a baja:String from its read() method. This is because field editors for Simples often pass the result of read() right back to load(), so reading out a value of a different type from what was loaded can result in some unexpected behavior. Fullscreen views have more latitude. class MyWidget extends Widget { doInitialize(dom) { dom.html('&lt;input type=\"text\"&gt;'); dom.on('change', 'input', () =&gt; this.setModified(true)); } doLoad(string) { this.$getTextInput().val(string); } doRead() { return this.$getTextInput().val(); } $getTextInput() { // see note about the $ prefix below return this.jq().children('input'); } } const myWidget = new MyWidget(); const dom = $('#myWidgetGoesHere'); dom.on(events.MODIFY_EVENT, (e, ed) =&gt; { ed.read() .then((userTypedString) =&gt; { let msg = 'User typed: ' + userTypedString; if (userTypedString !== ed.value()) { msg += ' (different)'; } console.log(msg); }) .catch((err) =&gt; console.error(err)); }); myWidget.initialize(dom) .then(() =&gt; myWidget.load('initial value')); Here is a straightforward example: this widget creates a text input for the user to type in, and doRead() reads out the value the user has typed. (Take a quick look at the method $getTextInput(). The $ prefix, by convention, indicates a private method. If you are inspecting an object in the console, and see a method prefixed with $, consider that a private method and don't call it externally. You can mark your own private methods with the @private JSDoc tag.) This example also introduces a couple new Widget behaviors: modification and events. Modification Take a look at doInitialize(). doInitialize() is a good place to arm DOM event handlers because it only runs once. Here, we arm a handler to listen for a change event from the input tag, which will be fired every time the user types a character. When we get a change event, we mark the widget as modified by calling setModified(true). Marking a widget as modified is very important. One reason why is that when a widget is marked modified, then if you try to navigate away without saving your changes, the profile can show the \"do you want to save changes?\" dialog to ensure that the user changes are not lost. This functionality only works if the widget correctly marks itself as modified, so isModified() returns true, when user changes are made. Another reason is that calling setModified(true) will trigger a corresponding event. Events At different moments in a widget's lifecycle, it will emit different events. These events are enumerated in the bajaux/events module. These are triggered like any other jQuery event, and you can arm event handlers to respond when a widget further down in the DOM triggers one of these events. In the preceding example, we listen for MODIFY_EVENT so we know when the widget is modified. The event handler for a bajaux event always receives the Event object itself, followed by the Widget that triggered it. Our event handler then calls .read() to read out the currently entered value and log it to the console. Also note how it can be compared against the result of .value() to see if the user has actually entered a different string. Also in this example, note how we do not return the Promise created by the call to .read(). 99% of the time, when performing an asynchronous operation, you should return the Promise so that the caller of the function can know when the work is completed and handle any errors. This is one notable exception to that rule: with jQuery event handlers, the caller of the function is jQuery itself, and jQuery does not know how to handle a returned Promise. Specifically for jQuery event handlers, we do not return the Promise, and we catch and log the error ourselves. There will be a bit more detail on this in the upcoming section on asynchronous programming. Validate Your widget may need constraints on what values are acceptable for the user to enter. For instance, say we want the user to enter a percentage between 0% and 100%. For this, we can use a validator function. To add validators, simply call validators().add(validatorFunction). class PercentInput extends Widget { constructor() { super(...arguments); this.validators().add((percent) =&gt; { if (percent &lt; 0 || percent &gt; 100) { throw new Error('Must be valid percentage [0-100]'); } }); } doInitialize(dom) { dom.html('&lt;input type=\"text\"&gt;&lt;span&gt;%&lt;/span&gt;'); dom.on('change', 'input', () =&gt; this.setModified(true)); } doLoad(number) { this.jq().children('input').val(String(number)); } doRead() { const text = this.jq().children('input').val(); const number = parseFloat(text); if (isNaN(num)) { throw new Error('Not a valid number: ' + text); } return number; } } const myWidget = new PercentInput(); const dom = $('#myWidgetGoesHere'); dom.on(events.MODIFY_EVENT, (e, ed) =&gt; { ed.validate() .then((percent) =&gt; console.log('valid percent: ' + percent)) .catch((err) =&gt; console.error(err)); }); myWidget.initialize(dom) .then(() =&gt; myWidget.load(0)); In this example, we create an input for the user to type a number into, with the constraint that it be a number between 0 and 100. If the value is not actually a number, then we throw an error from doRead() indicating that we couldn't read a number at all (note we could also return a rejected Promise). For validation, the validator function we add in the constructor will receive the value resolved from doRead() (a number) and check that it is between 0 and 100. If not, it throws an error (again, could also be a rejected Promise). We trigger the validation by calling validate(): this will read() the value out first, and then pass it to the validator function. This answers the question: does the user have a valid value entered? You may be wondering: why are we checking the value in both doRead() and the validator? Couldn't we do both checks in doRead()? The answer is yes, we could, but it depends on your own use case. If doRead() rejects, it's like the widget is saying, \"my current state is so messed up, I have no idea what the user was even trying to enter.\" If doRead() rejects, so will read(). But if doRead() resolves but validate() fails, it's like saying, \"I know what the user has entered, but it's not a correct value for my use case.\" In our example, this makes sense: if the user has an actual number entered, we can know that, and subsequently validate its numeric value. If we wanted to, we could do this: ed.validate() .catch(() =&gt; { return ed.read() .then((invalidValue) =&gt; { console.log('user entered ' + invalidValue + ' but it failed validation.'); }); }); This would not be possible if read() itself rejected. Save When the user-entered changes are satisfactory, call .save() to save those changes. The behavior will be defined, again, by implementing doSave(). You can typically think of this as mutating the current .value(). Whatever value I last passed to .load() will be mutated when I call .save(). Your implementation may vary, but this is the most common pattern to follow. It follows that if you just load in an immutable simple value like a String, calling .save() does not make sense because there is no way to mutate that String. But if your widget loads a mutable value like an object or a Component, .save() can commit changes to that value. class UserNamer extends Widget { doInitialize(dom) { dom.html('&lt;input type=\"text\"&gt;'); dom.on('change', 'input', () =&gt; this.setModified(true)); } doLoad(user) { this.getTextInput().val(user.name); } doRead() { return this.getTextInput().val(); } doSave(name) { this.value().name = name; } getTextInput() { return this.jq().find('input'); } } const myWidget = new UserNamer(); const dom = $('#myWidgetGoesHere'); const user = { name: 'Alice' }; myWidget.initialize(dom) .then(() =&gt; myWidget.load(user)) .then(() =&gt; { myWidget.getTextInput().val('Bob'); return myWidget.save(); }) .then(() =&gt; console.log(user.name)); // Bob Important things to note about the .save() process: .validate() will be called first, so if the currently entered value is not valid, it cannot be saved. The validated value is the first argument to .doSave(), so you do not need to call .read() again. Once .save() completes, .isModified() will revert to false. Destroy When you are done with a widget, it's best to .destroy() it before simply removing its DOM element from the document. Widgets can acquire and hold resources such as: Component subscriptions Global event handlers Database connections doDestroy() is a place for the widget to relinquish any resources it is currently holding so that they can be freed. If your widget does not release these resources in doDestroy(), they can stick around after the widget is gone, consuming memory, bandwidth, and CPU cycles unnecessarily. Attributes Widgets also have the following attributes. .isModified() As described above, reflects whether the widget has any user-entered changes. In most cases, it is the responsibility of your widget to listen for events that indicate user modification, and call this.setModified(true). .isEnabled() A widget can be enabled or disabled. The implementation of how it responds to changes in its enabled state is .doEnabled(). You may call .setEnabled() on your own widgets to enable or disable them. The framework itself may also call .setEnabled() on your widget in response to various conditions, such as the enabled property if your widget is embedded on a Px page. .isReadonly() A widget can be readonly or writable. The implementation on how it responds to changes in its readonly state is .doReadonly(). You may call .setReadonly() on your own widgets to make them readonly or writable. The framework itself may also call .setReadonly() on your widget in response to various conditions, such as if it is a field editor on a Component slot and that slot has the READONLY slot flag. Note that there can be significant overlap between readonly and enabled, so you may want to consider the state of both attributes when implementing doEnabled() or doReadonly(). In fact, many widgets respond exactly the same whether being set to disabled or readonly. The Building Composite Widgets With spandrel API provides the writable flag to ease this confusion. .properties() A Widget has a Properties API which allows it to declare a public set of properties and their values. These Properties will typically be used internally by the Widget to configure its behavior. For instance, when a Widget creates a DOM element of &lt;input type=\"range\"&gt;, it may declare properties of min and max to define the range of the input. But the Properties declared by a Widget also form a type of public API for that Widget, one that is used in many ways: When your Widget is embedded in a Px page, its Properties can be edited by the page designer. When your Widget is used to edit a Slot value, it will receive the Slot Facets as Properties. When a parent Widget creates child Widgets, it can configure the Properties of the children for customized behavior. When a Widget is placed on a Dashboard, it can declare Properties as dashboardable, so they can be saved per-user. There is one special Property, rootCssClass, that can be specified on a Widget. It will be used in initialize() to add that CSS class to its DOM element when it is initialized. This is typically used to easily identify instances of that Widget, either by selecting them out of the DOM, or styling them in CSS. A common pattern is shown below: class MyWidget extends Widget { constructor(params) { super({ params, defaults: { properties: { rootCssClass: '-myCompany-MyWidget' } } }); } } //... const allMyWidgets = [ ...dom.find('.-myCompany-MyWidget') ].map(Widget.in); .getFormFactor() In Niagara AX... A View is something that occupies most of the UI area in Workbench. A Field Editor is quite small and appears alongside other Field Editors on a Property Sheet. However, these extend from different classes and inherit different sets of functionality. In bajaux, we've separated out the form factor from the capabilities of the Widget. The form factor describes the kind of element in which a Widget may be initialized, not the behavior of that Widget. This allows Widgets to alter their behavior and layout to function appropriately, whether taking up the full browser window or just one row in a property sheet. For instance, in bajaux... The form factor max is for widgets that function as fullscreen views, like Property Sheet or User Manager. The form factor mini is for widgets that function as field editors, such as in one individual row in a Property Sheet or embedded as a Property on a Px Page. The form factor compact is for widgets that function in between - larger than a field editor but smaller than a fullscreen view. Most commonly this is a modal dialog, such as when you invoke an Action that takes a parameter. You can implement a widget that supports all of these form factors, or just one. .toDisplayName(), .toDescription(), .toIcon() Widgets have display name, description, and icon attributes that come into play with Workbench interop, described below. Subscription One of the nice features of Niagara AX is BWbComponentView. When extending this Java class, any subclasses automatically get Component subscription/unsubscription handled for them. The bajaux framework has the same concept. A Widget can be made into a Subscriber Widget by a special Subscriber MixIn. A Subscriber MixIn will use BajaScript to automatically ensure a Component is subscribed and ready before it's loaded. It will also ensure any Components are unsubscribed when the UI is destroyed. Commands A Command is a function that belongs to a Widget, but can be seen and invoked by the user. It is very similar to the existing Command API in bajaui. Some examples are a Save Command, which allows the user to save changes to the Widget, or the Property Sheet's Add Slot Command. Each Command has metadata to define how it is displayed to the user: Display name: formatted in the user's locale. Enabled/Disabled: commands that cannot be currently invoked are disabled. Flags: a Command can have some flags to indicate whether it should appear in a menu bar, toolbar or both. Icon: gives the Command an icon to make it easily identifiable by the user. As well as a standard Command, there's also ToggleCommand. A ToggleCommand can have its state toggled. Think of it like a tick box that can be turned on or off. A number of Command and ToggleCommand objects can be arranged into a tree-like structure by use of a CommandGroup. Interop / Integration bajaux Widgets work in many environments. The HTML5 Profile, used in the web browser, will instantiate the appropriate Widget for the component you are viewing. Workbench itself can load your bajaux Widgets using Java/JavaScript interop. Widgets can be embedded in Px pages and viewed either in Workbench or the browser. The environment in which the Widget is instantiated can access certain attributes of that Widget to more tightly integrate that Widget into the user interface. When developing a Widget, it can pay to correctly define these attributes to make its functionality more accessible to the user. Any Commands in the Widget's CommandGroup (e.g. a Property Sheet's \"Add Slot\" command), with the appropriate flags, will be shown as buttons in the toolbar in both Workbench and browser. Workbench can also show them in the menu bar. The Widget's display name and icon will be used in the view selector dropdown, and in Workbench's menu bar. Asynchronous Programming Any modern JavaScript framework has to deal with asynchronous behavior. The bajaux framework has been designed with this in mind from the ground up. Therefore, most of the callbacks that can be overridden can optionally return a promise that can perform an asynchronous operation before resolving. For instance, let's say some extra network calls have to be made during a widget's loading process. The widget's doLoad method can return a promise. The promise can then be resolved when the network calls have completed. The Promise API also defines how any asynchronous errors, like a failed network call, are handled. As a general rule (not just for bajaux), if you write a function that creates a Promise to perform some asynchronous work, you should return that Promise and document it accordingly, so that the caller can know to either return that Promise again, or handle any errors. /** * @returns {Promise} this function returns a Promise, so be sure to handle it correctly! */ function retrieveNetworkData() { return makeNetworkCallTo('/myRestEndpoint'); } // now, as the caller of this function, I can be sure to return that Promise... /** @returns {Promise} */ function processNetworkData() { return retrieveNetworkData() .then((networkData) =&gt; { return doSomethingWith(networkData); }); } // ...or I will at least know I need to handle any rejections. retrieveNetworkData() .then((networkData) =&gt; { return doSomethingWith(networkData); }) .catch((err) =&gt; logSevere(err)); If a Promise is not returned, and no one handles a rejection via a .catch() block, then any errors will \"disappear\" - no one will know anything went wrong. Keep an eye on how your Promises are created and returned, and you can be sure that all your errors are handled! Responsive Layout Many Widgets need some sort of responsive behavior: they'll need to lay themselves out differently depending on the width and height allotted to them. A Widget in a desktop browser at fullscreen will likely look very different from when it is displayed on a mobile phone. Most responsive behavior can be handled through pure CSS, which every Widget supports. But one shortcoming of the CSS approach is that one common technique - Media Queries - won't always work. This is because Media Queries target the screen size of the device, while the dimensions of a Widget vary independent of the screen size. For instance, your Widget might be embedded on a Px page, or within the resizable main view pane of Workbench. Where Media Queries fall short, or just where more complex layout calculations are needed, responsiveMixIn makes it easy to implement responsive behavior in any scenario. Composition Many Widgets will want to add additional child Widgets for richer functionality. Although the core Widget API has the built-in capability to initialize child Widgets, the spandrel API makes it even easier to assemble child Widgets and HTML. This increases code reuse and improves maintainability. Getting Started Click here to get started! Also try opening the docDeveloper palette in Niagara to start working with our sample bajaux playground components! × Search results Close "},"module-bajaux_commands_Command.html":{"id":"module-bajaux_commands_Command.html","title":"Module: bajaux/commands/Command","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/commands/Command new (require(\"bajaux/commands/Command\"))(params [, invokeFunction]) A Command is essentially an asynchronous function with a nicely formatted display name attached. Parameters: Name Type Argument Description params String | Object An Object Literal or the display name for the Command. Properties Name Type Argument Description displayName String The display name format for the Command. This format will be used in toDisplayName. This can also be the first argument, in which case func must be the second. func function &lt;optional&gt; The function this command will execute. The function can return a promise if it's going to be invoked asynchronously. As of Niagara 4.11 this can be omitted if creating an undoable function. undoable module:bajaux/commands/Command~Undoable | function &lt;optional&gt; As of Niagara 4.11, any necessary configuration to make this command undoable. This can be either an undoable directly, or a function that resolves to one. Please note that an undoable() function itself should not do the actual work - the redo() function of the returned undoable should do the work. Note that any asynchronous functions may be declared as synchronous when passed to the constructor, for simplicity. description String &lt;optional&gt; A description of the Command. This format will be used in toDescription. enabled Boolean &lt;optional&gt; The enabled state of the Command. Defaults to true. flags Number &lt;optional&gt; The Command flags. Defaults to Command.flags.ALL. icon String &lt;optional&gt; The Command Icon. This can also be a String encoding an icon will be created from. invokeFunction function &lt;optional&gt; the function to invoke, if using the two-argument constructor Example new Command(\"baja.Format compatible display name\", function () { alert(\"I'm a command!\"); }); new Command(\"Format compatible display name\", function () { return new Promise(function (resolve, reject) { setTimeout(function () { wobble.foo(); resolve; }, 1000); }); }); new Command({ displayName: \"I'll be converted to a Format: %lexicon(baja:january)%\", description: \"I'll be converted to a Format too: %lexicon(baja:true)%\", func: function () { alert(\"I'm a command!\"); } }); new Command({ module: \"myModule\", // Create a Command that gets its displayName, description lex: \"myCommand\", // and icon from a module's lexicon. func: function () { alert(\"I'm a command!\"); } }); new Command({ undoable: () =&gt; { return promptUser('Are you sure you want to make this change?') .then((userSaidYes) =&gt; { if (!userSaidYes) { return; } // redoText/undoText may be strings, getters, or async getters // canRedo/canUndo may be booleans, getters, or async getters return { redo: () =&gt; console.log('perform the work of the command'), undo: () =&gt; console.log('revert/back out the work of the command'), redoText: () =&gt; 'Text describing what the command will do', undoText: () =&gt; 'Text describing what undoing the command will do', canRedo: () =&gt; true, // true if doing the work of the command is allowed canUndo: () =&gt; true // true if reverting the work of the command is allowed } }); }); Members &lt;static&gt; flags Namespace containing numbers for comparing flag bits. C&amp;P'ed directly from MgrController in workbench module. &lt;static&gt; flags.ALL Match all flags. &lt;static&gt; flags.MENU_BAR Makes the command be available in the main menu. &lt;static&gt; flags.NONE Match no flags &lt;static&gt; flags.TOOL_BAR Makes the command be available in the main toolbar. Methods &lt;static&gt; isUndoable(undoable) Parameters: Name Type Description undoable module:bajaux/commands/Command | module:bajaux/commands/Command~Undoable Since: Niagara 4.11 Returns: true if the given parameter is an undoable Command, or is an Undoable object Type boolean defaultNotifyUser(err [, params]) Provides a default way of notifying the user about a Command invocation failure. Shows a dialog with details about the error. You might override this at runtime with your own error dialog handler. Parameters: Name Type Argument Description err Error | * params object &lt;optional&gt; Properties Name Type Argument Description messageSummary string &lt;optional&gt; any additional information you would like to include in the error dialog Since: Niagara 4.12 Returns: Type Promise getAccelerator() Return the accelerator for the Command or null if nothing is defined. See: module:bajaux/commands/Command#setAccelerator Returns: The accelerator or null if nothing is defined. Type Object getDescriptionFormat() Get the unformatted description of the command. Returns: Type String getDisplayNameFormat() Return the format display name of the command. Returns: Type String getFlags() Get this command's flags. Returns: Type Number getFunction() Return the raw function associated with this command. Returns: Type function getIcon() Return the Command's icon URI Returns: Type String getId() Return a unique numerical id for the Command. This is id unique to every Command object created. hasFlags(flags) Check to see if this command's flags match any of the bits of the input flags. Parameters: Name Type Description flags Number The flags to check against Returns: Type Boolean invoke() Invoke the Command. Triggers a bajaux:invokecommand or bajaux:failcommand event, as appropriate. Arguments can be passed into invoke() that will be passed into the function's Command Handler. Returns: A promise object that will be resolved (or rejected) once the Command's function handler has finished invoking. Type Promise invokeFromEvent(e) If your Command optionally implements this function, then CommandButton will call it on click instead of simply calling invoke. Use this in case your Command needs to respond differently based on where on the screen the user is pointing. Parameters: Name Type Description e JQuery.Event the DOM event triggered by the user's request to invoke this function Since: Niagara 4.11 Returns: Type Promise | * isCommand() Always returns true. isEnabled() Gets this command's enabled status. Returns: Type Boolean isLoading() Return true if the Command is still loading. Returns: true if still loading. Type Boolean isToggleCommand() Always returns false. isUndoable() Since: Niagara 4.11 Returns: true if this command is undoable Type boolean jq( [jqDom]) If a jQuery DOM argument is specified, this will set the DOM. If not specified then no DOM will be set. This method will always return the jQuery DOM associated with this Command. Parameters: Name Type Argument Description jqDom JQuery &lt;optional&gt; If specified, this will set the jQuery DOM. Returns: A jQuery DOM object for firing events on. Type JQuery loading() Return the loading promise for the Command. The returned promise will be resolved once the Command has finished loading. Returns: The promise used for loading a Command. Type Promise merge(cmd) Attempt to merge this command with another command, and return a new Command that does both tasks. If the two commands are mutually incompatible, return a falsy value. Parameters: Name Type Description cmd module:bajaux/commands/Command Returns: Type module:bajaux/commands/Command Example Here is an example to show the basic concept. Commands that simply add two numbers together can easily be merged together thanks to the associative property. var AddCommand = function AddCommand(inc) { this.$inc = inc; Command.call(this, { displayName: 'Add ' + inc + ' to the given number', func: function (num) { return num + inc; } }); }; AddCommand.prototype = Object.create(Command.prototype); AddCommand.prototype.merge = function (cmd) { if (cmd instanceof AddCommand) { return new AddCommand(this.$inc + cmd.$inc); } }; var addOneCommand = new AddCommand(1), addFiveCommand = new AddCommand(5), addSixCommand = addOneCommand.merge(addFiveCommand); addSixCommand.invoke(10) .then(function (result) { console.log('is 16? ', result === 16); }); off( [event] [, handler]) Unregister a function callback handler for the specified event. Parameters: Name Type Argument Description event String &lt;optional&gt; The name of the event to unregister. If name isn't specified, all events for the Command will be unregistered. handler function &lt;optional&gt; The function to unregister. If not specified, all handlers for the event will be unregistered. on(event, handler) Register a function callback handler for the specified event. Parameters: Name Type Description event String The event id to register the function for. handler function The event handler to be called when the event is fired. setAccelerator(acc) Set the accelerator information for the Command. Parameters: Name Type Description acc Object | String | Number | null | undefined The accelerator keyboard information. This can be a keyCode number, a character (i.e. 'a') or an Object that contains the accelerator information. If no accelerator should be used the null/undefined should be specified. Properties Name Type Argument Description keyCode String | Number The key code of the accelerator. This can be a character code number or a character (i.e. 'a'). ctrl Boolean &lt;optional&gt; true if the control key needs to be pressed. shift Boolean &lt;optional&gt; true if the shift key needs to be pressed. alt Boolean &lt;optional&gt; true if the alt key needs to be pressed. meta Boolean &lt;optional&gt; true if a meta key needs to be pressed. See: module:bajaux/commands/Command#getAccelerator setDescriptionFormat(description) Set the description format of the command. Triggers a bajaux:changecommand event. Parameters: Name Type Description description String the command description - supports baja Format syntax setDisplayNameFormat(displayName) Set the display name format of the command. Triggers a bajaux:changecommand event. Parameters: Name Type Description displayName String display name - supports baja Format syntax setEnabled(enabled) Sets this command's enabled status. Triggers a bajaux:changecommand event. Parameters: Name Type Description enabled Boolean setFlags(flags) Set this command's flags. Parameters: Name Type Description flags Number setFunction(func) Set the Command's function handler. Parameters: Name Type Description func function The new function handler for the command. setIcon(icon) Sets the icon for this Command. Triggers a bajaux:changecommand event. Parameters: Name Type Description icon String The Command's icon (either a URI or a module:// ORD string) toDescription() Access the Command's description. In order to access the description, a promise will be returned that will be resolved once the command has been loaded and the description has been found. Returns: Promise to be resolved with the description Type Promise toDisplayName() Access the Command's display name. In order to access the display name, a promise will be returned that will be resolved once the command has been loaded and the display name has been found. Returns: Promise to be resolved with the display name Type Promise trigger(name) Triggers an event from this Command. Parameters: Name Type Description name String visit(func) Visit this Command with the specified function. Parameters: Name Type Description func function Will be invoked with this Command passed in as an argument. × Search results Close "},"module-bajaux_commands_CommandGroup.html":{"id":"module-bajaux_commands_CommandGroup.html","title":"Module: bajaux/commands/CommandGroup","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/commands/CommandGroup new (require(\"bajaux/commands/CommandGroup\"))(params) A CommandGroup is a container for Commands and other CommandGroups. Parameters: Name Type Description params object parameters - or just pass the displayName directly Properties Name Type Argument Description displayName String A format display name that will be used when toDisplayName is called. icon String &lt;optional&gt; The Command Group Icon. commands Array.&lt;(module:bajaux/commands/CommandGroup|module:bajaux/commands/Command)&gt; &lt;optional&gt; specify the initial set of children of this command group Members off Unregister a function callback handler for the specified event. Since: Niagara 4.12 on Register a function callback handler for the specified event. Since: Niagara 4.12 Methods add(command) Add a command to this group. Triggers a bajaux:changecommandgroup event. Multiple arguments can be specified to add many commands at once. Parameters: Name Type Description command module:bajaux/commands/Command The command to add. Returns: Return the CommandGroup instance. Type module:bajaux/commands/CommandGroup doSort(kids) Does the work of sorting this command group. By default, does nothing. When overriding this method, be sure to return the sorted array as a parameter to deferred.resolve. Parameters: Name Type Description kids Array An array of the command group's children - this may include commands or sub-CommandGroups. Returns: an array containing the sorted children, or a promise to be resolved with same Type Array | Promise filter(options) Filters the commands in this command group based on the results of a test function, and returns a new, filtered CommandGroup instance. Parameters: Name Type Description options Object An options object literal Properties Name Type Argument Description include function &lt;optional&gt; A function that will receive a command object and return true or false depending on whether to include that command in the filtered results. If omitted, defaults to always return true. flags Number &lt;optional&gt; A number containing flag bits to test on each command. If omitted, defaults to Command.flags.ALL. Returns: A filtered command group. Type module:bajaux/commands/CommandGroup findCommand(id) Find a Command in this CommandGroup (or sub CommandGroup) via its id and return it. Parameters: Name Type Description id Number | function an id, filter function, or Command constructor. A filter function receives a Command instance and returns a boolean result - the first Command passing the filter will be returned. If passing a Command constructor, the first Command that is an instance of that constructor will be returned. Returns: return the found Command (or null if nothing found). Type module:bajaux/commands/Command | null Example group.findCommand(DeleteCommand); // returns the first instance of DeleteCommand in the group if present. flatten(options) Return a listing of the commands (not command groups) contained within this group. Can optionally recursively visit down through any sub-CommandGroups contained in this group (depth first) to return all commands. Can optionally filter commands by flags as well. Parameters: Name Type Description options Object An options object. Properties Name Type Argument Description flags Number &lt;optional&gt; flags Used to filter commands. If omitted, includes all commands regardless of flags. recurse Boolean &lt;optional&gt; true if should recurse down through any sub-groups. Returns: An array of Commands. Type Array Example cmdGroup.flatten(); // all child commands cmdGroup.flatten(Command.flags.MENU); // only children with MENU flag cmdGroup.flatten({ flags: Command.flags.MENU, recurse: true }); get(index) Return a Command/CommandGroup based upon the index or null if nothing can be found. Parameters: Name Type Description index Number Returns: Type module:bajaux/commands/Command | module:bajaux/commands/CommandGroup | null getChildren() Returns a defensive copy of this group's array of children. Returns: Type Array getDisplayNameFormat() Return the format display name of this command group. Returns: Type String getFlags() Returns this group's flags. Returns: Type Number getIcon() Return the CommandGroup's icon URI Since: Niagara 4.12 Returns: Type String hasFlags(flags) Check to see if this group's flags match any of the bits of the input flags. Parameters: Name Type Description flags Number The flags to check against isCommand() Always returns false. isEmpty() Return true if this CommandGroup doesn't contain any children. Returns: true if empty. isLoading() Returns: true if any of the Commands are still loading. Type boolean isToggleCommand() Always returns false. jq( [jqDom]) If a jQuery DOM argument is specified, this will set the DOM. If not specified then no DOM will be set. This method will always return the jQuery DOM associated with this CommandGroup. Parameters: Name Type Argument Description jqDom JQuery &lt;optional&gt; If specified, this will set the jQuery DOM. Returns: A jQuery DOM object for firing events on. Type JQuery loading() Return promise that will be resolved once all the Commands in this group have been loaded. Returns: A promise that will be resolved once all of the child Commands have loaded. Type Promise merge(group [, params]) Merges an input command group with this one, and returns a new command group with the merged results. Parameters: Name Type Argument Description group module:bajaux/commands/CommandGroup The group to merge with this one params object &lt;optional&gt; Properties Name Type Argument Default Description mergeCommands boolean &lt;optional&gt; true set to false to cause commands to be merged by a simple instanceof check, and all child commands of this group will be included; otherwise, only commands whose merge() function returns a new Command will be included Returns: the merged group, or null if the merge could not be completed Type module:bajaux/commands/CommandGroup | null remove(command) Remove a command from this group. Triggers a bajaux:changecommandgroup event. Multiple arguments can be specified so multiple commands can be removed at once. Parameters: Name Type Description command module:bajaux/commands/Command | Number The command or index of the Command to remove. Returns: Return the CommandGroup instance. Type module:bajaux/commands/CommandGroup Example // Passing an id/index (Number) commandGroup.remove(0); // Removes the 0 index kid from the group // Passing a command (module:bajaux/commands/Command) commandGroup.remove(command); // Removes the matching 'command' from the group // Passing multiple commands (module:bajaux/commands/Command) commandGroup.remove(command1, command2); // Removes the matching commands from the group removeAll(options) Remove all children of this command group that match the input flag, optionally emptying out child groups as well. Triggers a bajaux:changecommandgroup event. Parameters: Name Type Description options Object An options object Properties Name Type Argument Description flags Number &lt;optional&gt; Flags to use to filter commands. Only children that match one of these flags will be removed. If omitted, defaults to Command.flags.ALL meaning all children will be removed. recurse Boolean &lt;optional&gt; true if should also empty out any sub-groups. Example cmdGroup.removeAll(); cmdGroup.removeAll(Command.flags.MENU_BAR); cmdGroup.removeAll({ flags: Command.flags.MENU_BAR, recurse: true }); setChildren(children) Sets this group's array of commands/groups wholesale. Triggers a bajaux:changecommandgroup event. Parameters: Name Type Description children Array setDisplayNameFormat(displayName) Set the display name format of the command group. Triggers a bajaux:changecommandgroup event. Parameters: Name Type Description displayName String display name - supports baja Format syntax setFlags(flags) Sets this group's flags. Triggers a bajaux:changecommandgroup event. Parameters: Name Type Description flags Number setIcon(icon) Sets the icon for this CommandGroup. Triggers a bajaux:changecommandgroup event. Parameters: Name Type Description icon String The CommandButton's icon (either a URI or a module:// ORD string) Since: Niagara 4.12 size() Return the number of children the CommandGroup has (this covers both Commands and CommandGroups). Returns: Type Number sort() Sorts this command group. Typically will not be overridden; override doSort instead. Triggers a bajaux:changecommandgroup event. Returns: A promise that will be invoked once the CommandGroup has been sorted. Type Promise toDisplayName() Formats the command's display name. Returns: Promise to be resolved with the display name Type Promise.&lt;String&gt; trigger(name) Triggers an event from this CommandGroup. Parameters: Name Type Description name String visit(func) Visit through all of the Commands and CommandGroups. The function passed it will be called for every Command and CommandGroup found (including this CommandGroup). Parameters: Name Type Description func Called for every CommandGroup and Command found. Each Command/CommandGroup is passed in as the first argument to the function. If the function returns a false value then iteration stops. × Search results Close "},"module-bajaux_commands_Command-Undoable.html":{"id":"module-bajaux_commands_Command-Undoable.html","title":"Interface: Undoable","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Interface: Undoable bajaux/commands/Command~ Undoable Represents a unit of undoable/redoable work as provided by a Command. Methods canRedo() Resolves true if it is possible to do the redo work at this time. Returns: Type Promise.&lt;boolean&gt; canUndo() Resolves true if it is possible to do the undo work at this time. Returns: Type Promise.&lt;boolean&gt; redoText() Resolve some text that describes the redo work about to be done. Returns: Type Promise.&lt;string&gt; undo() Perform the undo work. Returns: Type Promise undo() Perform the redo work. Returns: Type Promise undoText() Resolve some text that describes the undo work about to be done. Returns: Type Promise.&lt;string&gt; × Search results Close "},"module-bajaux_commands_ToggleCommand.html":{"id":"module-bajaux_commands_ToggleCommand.html","title":"Module: bajaux/commands/ToggleCommand","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/commands/ToggleCommand new (require(\"bajaux/commands/ToggleCommand\"))( [params]) A ToggleCommand adds an additional state variable allowing it to be turned on or off. If no function is specified in the Constructor, the default behavior is to simply call 'toggle'. Extends: module:bajaux/commands/Command Parameters: Name Type Argument Description params object &lt;optional&gt; all parameters compatible with module:bajaux/commands/Command Properties Name Type Argument Description selected boolean &lt;optional&gt; set to true to cause this command to be pre-selected Methods defaultNotifyUser(err [, params]) Provides a default way of notifying the user about a Command invocation failure. Shows a dialog with details about the error. You might override this at runtime with your own error dialog handler. Parameters: Name Type Argument Description err Error | * params object &lt;optional&gt; Properties Name Type Argument Description messageSummary string &lt;optional&gt; any additional information you would like to include in the error dialog Since: Niagara 4.12 Inherited From: module:bajaux/commands/Command#defaultNotifyUser Returns: Type Promise getAccelerator() Return the accelerator for the Command or null if nothing is defined. Inherited From: module:bajaux/commands/Command#getAccelerator See: module:bajaux/commands/Command#setAccelerator Returns: The accelerator or null if nothing is defined. Type Object getDescriptionFormat() Get the unformatted description of the command. Inherited From: module:bajaux/commands/Command#getDescriptionFormat Returns: Type String getDisplayNameFormat() Return the format display name of the command. Inherited From: module:bajaux/commands/Command#getDisplayNameFormat Returns: Type String getFlags() Get this command's flags. Inherited From: module:bajaux/commands/Command#getFlags Returns: Type Number getFunction() Return the raw function associated with this command. Inherited From: module:bajaux/commands/Command#getFunction Returns: Type function getIcon() Return the Command's icon URI Inherited From: module:bajaux/commands/Command#getIcon Returns: Type String getId() Return a unique numerical id for the Command. This is id unique to every Command object created. Inherited From: module:bajaux/commands/Command#getId hasFlags(flags) Check to see if this command's flags match any of the bits of the input flags. Parameters: Name Type Description flags Number The flags to check against Inherited From: module:bajaux/commands/Command#hasFlags Returns: Type Boolean invoke() Invoke the Command. Triggers a bajaux:invokecommand or bajaux:failcommand event, as appropriate. Arguments can be passed into invoke() that will be passed into the function's Command Handler. Inherited From: module:bajaux/commands/Command#invoke Returns: A promise object that will be resolved (or rejected) once the Command's function handler has finished invoking. Type Promise invokeFromEvent(e) If your Command optionally implements this function, then CommandButton will call it on click instead of simply calling invoke. Use this in case your Command needs to respond differently based on where on the screen the user is pointing. Parameters: Name Type Description e JQuery.Event the DOM event triggered by the user's request to invoke this function Since: Niagara 4.11 Inherited From: module:bajaux/commands/Command#invokeFromEvent Returns: Type Promise | * isCommand() Always returns true. Inherited From: module:bajaux/commands/Command#isCommand isEnabled() Gets this command's enabled status. Inherited From: module:bajaux/commands/Command#isEnabled Returns: Type Boolean isLoading() Return true if the Command is still loading. Inherited From: module:bajaux/commands/Command#isLoading Returns: true if still loading. Type Boolean isSelected() Gets this command's selected status. Returns: Type Boolean isToggleCommand() Always returns true. Overrides: module:bajaux/commands/Command#isToggleCommand isUndoable() Since: Niagara 4.11 Inherited From: module:bajaux/commands/Command#isUndoable Returns: true if this command is undoable Type boolean jq( [jqDom]) If a jQuery DOM argument is specified, this will set the DOM. If not specified then no DOM will be set. This method will always return the jQuery DOM associated with this Command. Parameters: Name Type Argument Description jqDom JQuery &lt;optional&gt; If specified, this will set the jQuery DOM. Inherited From: module:bajaux/commands/Command#jq Returns: A jQuery DOM object for firing events on. Type JQuery loading() Return the loading promise for the Command. The returned promise will be resolved once the Command has finished loading. Inherited From: module:bajaux/commands/Command#loading Returns: The promise used for loading a Command. Type Promise merge(cmd) Attempt to merge this command with another command, and return a new Command that does both tasks. If the two commands are mutually incompatible, return a falsy value. Parameters: Name Type Description cmd module:bajaux/commands/Command Inherited From: module:bajaux/commands/Command#merge Returns: Type module:bajaux/commands/Command Example Here is an example to show the basic concept. Commands that simply add two numbers together can easily be merged together thanks to the associative property. var AddCommand = function AddCommand(inc) { this.$inc = inc; Command.call(this, { displayName: 'Add ' + inc + ' to the given number', func: function (num) { return num + inc; } }); }; AddCommand.prototype = Object.create(Command.prototype); AddCommand.prototype.merge = function (cmd) { if (cmd instanceof AddCommand) { return new AddCommand(this.$inc + cmd.$inc); } }; var addOneCommand = new AddCommand(1), addFiveCommand = new AddCommand(5), addSixCommand = addOneCommand.merge(addFiveCommand); addSixCommand.invoke(10) .then(function (result) { console.log('is 16? ', result === 16); }); off( [event] [, handler]) Unregister a function callback handler for the specified event. Parameters: Name Type Argument Description event String &lt;optional&gt; The name of the event to unregister. If name isn't specified, all events for the Command will be unregistered. handler function &lt;optional&gt; The function to unregister. If not specified, all handlers for the event will be unregistered. Inherited From: module:bajaux/commands/Command#off on(event, handler) Register a function callback handler for the specified event. Parameters: Name Type Description event String The event id to register the function for. handler function The event handler to be called when the event is fired. Inherited From: module:bajaux/commands/Command#on setAccelerator(acc) Set the accelerator information for the Command. Parameters: Name Type Description acc Object | String | Number | null | undefined The accelerator keyboard information. This can be a keyCode number, a character (i.e. 'a') or an Object that contains the accelerator information. If no accelerator should be used the null/undefined should be specified. Properties Name Type Argument Description keyCode String | Number The key code of the accelerator. This can be a character code number or a character (i.e. 'a'). ctrl Boolean &lt;optional&gt; true if the control key needs to be pressed. shift Boolean &lt;optional&gt; true if the shift key needs to be pressed. alt Boolean &lt;optional&gt; true if the alt key needs to be pressed. meta Boolean &lt;optional&gt; true if a meta key needs to be pressed. Inherited From: module:bajaux/commands/Command#setAccelerator See: module:bajaux/commands/Command#getAccelerator setDescriptionFormat(description) Set the description format of the command. Triggers a bajaux:changecommand event. Parameters: Name Type Description description String the command description - supports baja Format syntax Inherited From: module:bajaux/commands/Command#setDescriptionFormat setDisplayNameFormat(displayName) Set the display name format of the command. Triggers a bajaux:changecommand event. Parameters: Name Type Description displayName String display name - supports baja Format syntax Inherited From: module:bajaux/commands/Command#setDisplayNameFormat setEnabled(enabled) Sets this command's enabled status. Triggers a bajaux:changecommand event. Parameters: Name Type Description enabled Boolean Inherited From: module:bajaux/commands/Command#setEnabled setFlags(flags) Set this command's flags. Parameters: Name Type Description flags Number Inherited From: module:bajaux/commands/Command#setFlags setFunction(func) Set the Command's function handler. Parameters: Name Type Description func function The new function handler for the command. Inherited From: module:bajaux/commands/Command#setFunction setIcon(icon) Sets the icon for this Command. Triggers a bajaux:changecommand event. Parameters: Name Type Description icon String The Command's icon (either a URI or a module:// ORD string) Inherited From: module:bajaux/commands/Command#setIcon setSelected(selected [, params]) Sets this command's selected status. Triggers a bajaux:changecommand. Parameters: Name Type Argument Description selected Boolean params Object &lt;optional&gt; Some optional parameters to pass through to the events that are fired. toDescription() Access the Command's description. In order to access the description, a promise will be returned that will be resolved once the command has been loaded and the description has been found. Inherited From: module:bajaux/commands/Command#toDescription Returns: Promise to be resolved with the description Type Promise toDisplayName() Access the Command's display name. In order to access the display name, a promise will be returned that will be resolved once the command has been loaded and the display name has been found. Inherited From: module:bajaux/commands/Command#toDisplayName Returns: Promise to be resolved with the display name Type Promise toggle( [params]) Selects if deselected, or deselects if selected. Triggers a bajaux:changecommand. Parameters: Name Type Argument Description params Object &lt;optional&gt; Some optional parameters to pass through to setting the selection. trigger(name) Triggers an event from this Command. Parameters: Name Type Description name String Inherited From: module:bajaux/commands/Command#trigger visit(func) Visit this Command with the specified function. Parameters: Name Type Description func function Will be invoked with this Command passed in as an argument. Inherited From: module:bajaux/commands/Command#visit × Search results Close "},"module-bajaux_commands_ToggleCommandGroup.html":{"id":"module-bajaux_commands_ToggleCommandGroup.html","title":"Module: bajaux/commands/ToggleCommandGroup","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/commands/ToggleCommandGroup new (require(\"bajaux/commands/ToggleCommandGroup\"))(params) ToggleCommandGroup is a special CommandGroup that behaves like a radio button group. Extends: module:bajaux/commands/CommandGroup Parameters: Name Type Description params object Properties Name Type Description onChange function provide a callback function that can work with the selected value commands Array An array of objects, each of which will pass through to making a ToggleCommand. See module:bajaux/commands/Command for all acceptable parameters. Pass an optional \"value\" parameter to a command configuration object to associate a value with it. Examples new ToggleCommandGroup({ commands: [ { module: \"mymodule\", lex: \"mycommand1\", value: 0 }, { module: \"mymodule\", lex: \"mycommand2\", value: 1 }, { module: \"mymodule\", lex: \"mycommand3\", value: 2 } ], onChange: function (value) { // receives the values specified above. switch (value) { case 0: case 1: case 2: } } }); Use a flat style for your toggle button group. var buttonGroup = new CommandButtonGroup(); return buttonGroup.initialize(dom, { toggleGroup: true }) .then(function () { return buttonGroup.load(toggleCommandGroup); }); Members off Unregister a function callback handler for the specified event. Since: Niagara 4.12 Inherited From: module:bajaux/commands/CommandGroup#off on Register a function callback handler for the specified event. Since: Niagara 4.12 Inherited From: module:bajaux/commands/CommandGroup#on Methods add(command) Add a command to this group. Triggers a bajaux:changecommandgroup event. Multiple arguments can be specified to add many commands at once. Parameters: Name Type Description command module:bajaux/commands/Command The command to add. Inherited From: module:bajaux/commands/CommandGroup#add Returns: Return the CommandGroup instance. Type module:bajaux/commands/CommandGroup doSort(kids) Does the work of sorting this command group. By default, does nothing. When overriding this method, be sure to return the sorted array as a parameter to deferred.resolve. Parameters: Name Type Description kids Array An array of the command group's children - this may include commands or sub-CommandGroups. Inherited From: module:bajaux/commands/CommandGroup#doSort Returns: an array containing the sorted children, or a promise to be resolved with same Type Array | Promise filter(options) Filters the commands in this command group based on the results of a test function, and returns a new, filtered CommandGroup instance. Parameters: Name Type Description options Object An options object literal Properties Name Type Argument Description include function &lt;optional&gt; A function that will receive a command object and return true or false depending on whether to include that command in the filtered results. If omitted, defaults to always return true. flags Number &lt;optional&gt; A number containing flag bits to test on each command. If omitted, defaults to Command.flags.ALL. Inherited From: module:bajaux/commands/CommandGroup#filter Returns: A filtered command group. Type module:bajaux/commands/CommandGroup findCommand(id) Find a Command in this CommandGroup (or sub CommandGroup) via its id and return it. Parameters: Name Type Description id Number | function an id, filter function, or Command constructor. A filter function receives a Command instance and returns a boolean result - the first Command passing the filter will be returned. If passing a Command constructor, the first Command that is an instance of that constructor will be returned. Inherited From: module:bajaux/commands/CommandGroup#findCommand Returns: return the found Command (or null if nothing found). Type module:bajaux/commands/Command | null Example group.findCommand(DeleteCommand); // returns the first instance of DeleteCommand in the group if present. flatten(options) Return a listing of the commands (not command groups) contained within this group. Can optionally recursively visit down through any sub-CommandGroups contained in this group (depth first) to return all commands. Can optionally filter commands by flags as well. Parameters: Name Type Description options Object An options object. Properties Name Type Argument Description flags Number &lt;optional&gt; flags Used to filter commands. If omitted, includes all commands regardless of flags. recurse Boolean &lt;optional&gt; true if should recurse down through any sub-groups. Inherited From: module:bajaux/commands/CommandGroup#flatten Returns: An array of Commands. Type Array Example cmdGroup.flatten(); // all child commands cmdGroup.flatten(Command.flags.MENU); // only children with MENU flag cmdGroup.flatten({ flags: Command.flags.MENU, recurse: true }); get(index) Return a Command/CommandGroup based upon the index or null if nothing can be found. Parameters: Name Type Description index Number Inherited From: module:bajaux/commands/CommandGroup#get Returns: Type module:bajaux/commands/Command | module:bajaux/commands/CommandGroup | null getChildren() Returns a defensive copy of this group's array of children. Inherited From: module:bajaux/commands/CommandGroup#getChildren Returns: Type Array getDisplayNameFormat() Return the format display name of this command group. Inherited From: module:bajaux/commands/CommandGroup#getDisplayNameFormat Returns: Type String getFlags() Returns this group's flags. Inherited From: module:bajaux/commands/CommandGroup#getFlags Returns: Type Number getIcon() Return the CommandGroup's icon URI Since: Niagara 4.12 Inherited From: module:bajaux/commands/CommandGroup#getIcon Returns: Type String getSelected() Get the selected command Returns: The selected ToggleCommand Type module:bajaux/commands/ToggleCommand getSelectedValue() Get the selected value if available Returns: If a 'value' parameter is set it will be returned Type any hasFlags(flags) Check to see if this group's flags match any of the bits of the input flags. Parameters: Name Type Description flags Number The flags to check against Inherited From: module:bajaux/commands/CommandGroup#hasFlags isCommand() Always returns false. Inherited From: module:bajaux/commands/CommandGroup#isCommand isEmpty() Return true if this CommandGroup doesn't contain any children. Inherited From: module:bajaux/commands/CommandGroup#isEmpty Returns: true if empty. isLoading() Inherited From: module:bajaux/commands/CommandGroup#isLoading Returns: true if any of the Commands are still loading. Type boolean isToggleCommand() Always returns false. Inherited From: module:bajaux/commands/CommandGroup#isToggleCommand jq( [jqDom]) If a jQuery DOM argument is specified, this will set the DOM. If not specified then no DOM will be set. This method will always return the jQuery DOM associated with this CommandGroup. Parameters: Name Type Argument Description jqDom JQuery &lt;optional&gt; If specified, this will set the jQuery DOM. Inherited From: module:bajaux/commands/CommandGroup#jq Returns: A jQuery DOM object for firing events on. Type JQuery loading() Return promise that will be resolved once all the Commands in this group have been loaded. Inherited From: module:bajaux/commands/CommandGroup#loading Returns: A promise that will be resolved once all of the child Commands have loaded. Type Promise merge(group [, params]) Merges an input command group with this one, and returns a new command group with the merged results. Parameters: Name Type Argument Description group module:bajaux/commands/CommandGroup The group to merge with this one params object &lt;optional&gt; Properties Name Type Argument Default Description mergeCommands boolean &lt;optional&gt; true set to false to cause commands to be merged by a simple instanceof check, and all child commands of this group will be included; otherwise, only commands whose merge() function returns a new Command will be included Inherited From: module:bajaux/commands/CommandGroup#merge Returns: the merged group, or null if the merge could not be completed Type module:bajaux/commands/CommandGroup | null remove(command) Remove a command from this group. Triggers a bajaux:changecommandgroup event. Multiple arguments can be specified so multiple commands can be removed at once. Parameters: Name Type Description command module:bajaux/commands/Command | Number The command or index of the Command to remove. Inherited From: module:bajaux/commands/CommandGroup#remove Returns: Return the CommandGroup instance. Type module:bajaux/commands/CommandGroup Example // Passing an id/index (Number) commandGroup.remove(0); // Removes the 0 index kid from the group // Passing a command (module:bajaux/commands/Command) commandGroup.remove(command); // Removes the matching 'command' from the group // Passing multiple commands (module:bajaux/commands/Command) commandGroup.remove(command1, command2); // Removes the matching commands from the group removeAll(options) Remove all children of this command group that match the input flag, optionally emptying out child groups as well. Triggers a bajaux:changecommandgroup event. Parameters: Name Type Description options Object An options object Properties Name Type Argument Description flags Number &lt;optional&gt; Flags to use to filter commands. Only children that match one of these flags will be removed. If omitted, defaults to Command.flags.ALL meaning all children will be removed. recurse Boolean &lt;optional&gt; true if should also empty out any sub-groups. Inherited From: module:bajaux/commands/CommandGroup#removeAll Example cmdGroup.removeAll(); cmdGroup.removeAll(Command.flags.MENU_BAR); cmdGroup.removeAll({ flags: Command.flags.MENU_BAR, recurse: true }); setChildren(children) Sets this group's array of commands/groups wholesale. Triggers a bajaux:changecommandgroup event. Parameters: Name Type Description children Array Inherited From: module:bajaux/commands/CommandGroup#setChildren setDisplayNameFormat(displayName) Set the display name format of the command group. Triggers a bajaux:changecommandgroup event. Parameters: Name Type Description displayName String display name - supports baja Format syntax Inherited From: module:bajaux/commands/CommandGroup#setDisplayNameFormat setFlags(flags) Sets this group's flags. Triggers a bajaux:changecommandgroup event. Parameters: Name Type Description flags Number Inherited From: module:bajaux/commands/CommandGroup#setFlags setIcon(icon) Sets the icon for this CommandGroup. Triggers a bajaux:changecommandgroup event. Parameters: Name Type Description icon String The CommandButton's icon (either a URI or a module:// ORD string) Since: Niagara 4.12 Inherited From: module:bajaux/commands/CommandGroup#setIcon size() Return the number of children the CommandGroup has (this covers both Commands and CommandGroups). Inherited From: module:bajaux/commands/CommandGroup#size Returns: Type Number sort() Sorts this command group. Typically will not be overridden; override doSort instead. Triggers a bajaux:changecommandgroup event. Inherited From: module:bajaux/commands/CommandGroup#sort Returns: A promise that will be invoked once the CommandGroup has been sorted. Type Promise toDisplayName() Formats the command's display name. Inherited From: module:bajaux/commands/CommandGroup#toDisplayName Returns: Promise to be resolved with the display name Type Promise.&lt;String&gt; trigger(name) Triggers an event from this CommandGroup. Parameters: Name Type Description name String Inherited From: module:bajaux/commands/CommandGroup#trigger visit(func) Visit through all of the Commands and CommandGroups. The function passed it will be called for every Command and CommandGroup found (including this CommandGroup). Parameters: Name Type Description func Called for every CommandGroup and Command found. Each Command/CommandGroup is passed in as the first argument to the function. If the function returns a false value then iteration stops. Inherited From: module:bajaux/commands/CommandGroup#visit × Search results Close "},"module-bajaux_container_wb_Clipboard.html":{"id":"module-bajaux_container_wb_Clipboard.html","title":"Module: bajaux/container/wb/Clipboard","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/container/wb/Clipboard new (require(\"bajaux/container/wb/Clipboard\"))() A fake Clipboard that uses BajaScript to support drag and drop in Workbench. Methods &lt;static&gt; dragover(x, y, navNodesStr) The drag over function to be exported for Workbench to use. Parameters: Name Type Description x Number The x co-ordinate of the drag. y Number The y co-ordinate of the drag. navNodesStr String The Nav Node JSON encoded in a String. Returns: A promise that's resolved once the drag operation has completed. Type Promise &lt;static&gt; drop(x, y, navNodesStr) The drop function to be exported for Workbench to use. Parameters: Name Type Description x Number The x co-ordinate of the drop. y Number The y co-ordinate of the drop. navNodesStr String The Nav Node JSON encoded in a String. Returns: A promise that's resolved once the drag operation has completed. Type Promise clearData(mimeType) Clear the data from the clipboard for the specified format. Parameters: Name Type Description mimeType String The format of the data being cleared. getData(mimeType) Return data from the clipboard for the specified format. Parameters: Name Type Description mimeType String The format of the data to return. Returns: the request data (or undefined if nothing can be found). setData(mimeType, data) Adds the specified data to the clipboard. Parameters: Name Type Description mimeType String The format of the data being added. data The data to be added to the clipboard. See: http://www.w3.org/TR/html5/editing.html#dom-datatransfer-setdata setDragImage() Sets a drag image (currently is a no-op). × Search results Close "},"module-bajaux_container_wb_StringList.html":{"id":"module-bajaux_container_wb_StringList.html","title":"Module: bajaux/container/wb/StringList","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/container/wb/StringList new (require(\"bajaux/container/wb/StringList\"))() A fake DOMStringList for use with the Clipboard in the Workbench Web Browser. See: module:bajaux/container/wb/Clipboard http://www.w3.org/TR/DOM-Level-3-Core/core.html#DOMStringList Members &lt;readonly&gt; length :Number The current size of the StringList. Type: Number Methods add(str) Add a string to the list. Parameters: Name Type Description str String The string to add. contains(str) Returns true if the string is the list. Parameters: Name Type Description str String The String to query the list with. Returns: true if the string is in the list. Type Boolean item(index) Return an item from list via its index. Parameters: Name Type Description index Number Returns: a String from the list. remove(str) Remove an item for the list. Parameters: Name Type Description str String The string to remove. × Search results Close "},"module-bajaux_dragdrop_dragDropUtils.html":{"id":"module-bajaux_dragdrop_dragDropUtils.html","title":"Module: bajaux/dragdrop/dragDropUtils","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/dragdrop/dragDropUtils Utilities for interacting with the HTML5 drag/drop APIs. Methods &lt;static&gt; fromClipboard(clipboard) Read data previously written to the clipboard by a call to toClipboard. Parameters: Name Type Description clipboard DataTransfer Returns: promise to be resolved with an Envelope instance Type Promise Example $('.dropTarget').on('drop', function (e) { dragDropUtils.fromClipboard(e.originalEvent.dataTransfer) .then(function (envelope) { envelope.toValues().then(function (values) { values.forEach(handleValue); }); }); }); &lt;static&gt; toClipboard(clipboard, mimeType [, values]) Add the given data onto the clipboard. Parameters: Name Type Argument Description clipboard DataTransfer the DataTransfer on which to set the data mimeType String | module:bajaux/dragdrop/Envelope a supported Niagara mime type for the given data, in which case the given values will be converted to an Envelope instance; or an Envelope instance directly values Array &lt;optional&gt; if a mime type given, the values to convert to an Envelope instance Returns: promise to be resolved when the Envelope instance has been created or received, and has written JSON data onto the clipboard Type Promise Example $('.dragSource').on('dragstart', function (e) { var dataTransfer = e.originalEvent.dataTransfer; dragDropUtils.toClipboard(dataTransfer, 'niagara/strings', [ 'hello', 'world' ]) .then(function () { console.log('clipboard populated with: ' + dataTransfer.getData('Text')); }); }); × Search results Close "},"module-bajaux_dragdrop_Envelope.html":{"id":"module-bajaux_dragdrop_Envelope.html","title":"Module: bajaux/dragdrop/Envelope","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/dragdrop/Envelope &lt;abstract&gt; new (require(\"bajaux/dragdrop/Envelope\"))(arr) Base class for Envelope implementations. These define methods of transformation between raw JSON and data values (Baja values or otherwise), for use in drag/drop or other data-transfer situations. Parameters: Name Type Description arr Array an array of values to store in this envelope Methods &lt;abstract&gt; getMimeType() Get a mime type to identify the data type transformed by this envelope. Returns: Type String &lt;abstract&gt; toJson() Get a JSON representation of the data contained in this envelope. Returns: a promise to be resolved with an array of raw JSON objects Type Promise &lt;abstract&gt; toValues() Get the actual values represented by this envelope. Returns: a promise to be resolved with an array of data values Type Promise × Search results Close "},"module-bajaux_dragdrop_NavNodeEnvelope.html":{"id":"module-bajaux_dragdrop_NavNodeEnvelope.html","title":"Module: bajaux/dragdrop/NavNodeEnvelope","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/dragdrop/NavNodeEnvelope new (require(\"bajaux/dragdrop/NavNodeEnvelope\"))(arr) Envelope for transforming raw JSON into NavNode instances, or vice versa. Extends: module:bajaux/dragdrop/Envelope Parameters: Name Type Description arr Array.&lt;Object&gt; | Array.&lt;baja.NavNode&gt; either an array of raw JSON to be converted to NavNodes, or an array of NavNodes to be converted to JSON Throws: if a non-Array given Type Error Methods getMimeType() Overrides: module:bajaux/dragdrop/Envelope#getMimeType Returns: niagara/navnodes Type string toJson() Get the JSON representations of the envelope's NavNodes: [{ \"name\": \"navName\", \"displayName\": \"navDisplayName\", \"description\": \"navDescription\", \"icon\": \"navIcon\", \"ord\": \"navOrd\", \"typeSpec\": \"typeSpec\", \"kids\": [ \\/* child nav node JSON objects *\\/ ] }] Overrides: module:bajaux/dragdrop/Envelope#toJson Returns: promise to be resolved with an array of raw JSON objects Type Promise toValues() Get the actual NavNodes represented by this envelope. Overrides: module:bajaux/dragdrop/Envelope#toValues Returns: promise to be resolved with an array of NavNode instances Type Promise × Search results Close "},"module-bajaux_dragdrop_StringEnvelope.html":{"id":"module-bajaux_dragdrop_StringEnvelope.html","title":"Module: bajaux/dragdrop/StringEnvelope","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/dragdrop/StringEnvelope new (require(\"bajaux/dragdrop/StringEnvelope\"))(arr) Envelope for simply transferring an array of strings. Extends: module:bajaux/dragdrop/Envelope Parameters: Name Type Description arr Array.&lt;String&gt; the strings contained in this envelope. (Any non-Strings will be toString-ed.) Methods getMimeType() Overrides: module:bajaux/dragdrop/Envelope#getMimeType Returns: niagara/strings Type string toJson() The strings contained in the envelope (for simple strings, the JSON and values are the same). Overrides: module:bajaux/dragdrop/Envelope#toJson Returns: promise to be resolved with an array of strings Type Promise toValues() The strings contained in the envelope (for simple strings, the JSON and values are the same). Overrides: module:bajaux/dragdrop/Envelope#toValues Returns: promise to be resolved with an array of strings Type Promise × Search results Close "},"module-bajaux_events.html":{"id":"module-bajaux_events.html","title":"Module: bajaux/events","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/events A list of all bajaux related events. Members &lt;static&gt; DESTROY_EVENT Triggers when a Widget is destroyed. &lt;static&gt; DESTROY_FAIL_EVENT Triggers when Widget is destroyed and its callbacks fail. &lt;static&gt; DISABLE_EVENT Triggers when Widget is disabled. &lt;static&gt; DISABLE_FAIL_EVENT Triggers when Widget is disabled and its callbacks fail. &lt;static&gt; ENABLE_EVENT Triggers when Widget is enabled. &lt;static&gt; ENABLE_FAIL_EVENT Triggers when Widget is enabled and its callbacks fail. &lt;static&gt; FAILURE_EVENTS All of the failure events for bajaux. These events are fired whenever something has gone wrong. &lt;static&gt; INITIALIZE_EVENT Triggers when Widget initializes. &lt;static&gt; INITIALIZE_FAIL_EVENT Triggers when Widget tries to initialize, but fails. &lt;static&gt; INVALID_EVENT Triggers when an Widget validates and finds an invalid value. &lt;static&gt; LAYOUT_EVENT Triggers when the layout has changed. &lt;static&gt; LOAD_EVENT Triggers when an Widget loads a value. &lt;static&gt; LOAD_FAIL_EVENT Triggers when an Widget tries to load a value, but fails. &lt;static&gt; METADATA_CHANGED Triggers when a Property's metadata is changed &lt;static&gt; MODIFY_EVENT Triggers when an Widget is modified. &lt;static&gt; PROPERTY_ADDED Triggers when a Property is added &lt;static&gt; PROPERTY_CHANGED Triggers when a Property is changed &lt;static&gt; PROPERTY_REMOVED Triggers when a Property is removed &lt;static&gt; READONLY_EVENT Triggers when an Widget is made readonly. &lt;static&gt; READONLY_FAIL_EVENT Triggers when an Widget is made readonly and its callbacks fail. &lt;static&gt; SAVE_EVENT Triggers when an Widget saves. &lt;static&gt; SAVE_FAIL_EVENT Triggers when an Widget tries to save, but fails. &lt;static&gt; UNMODIFY_EVENT Triggers when an Widget is unmodified. &lt;static&gt; VALID_EVENT Triggers when an Widget validates and finds a valid value. &lt;static&gt; VALIDATORS_MODIFIED Triggers when an Widget's validators are modified. &lt;static&gt; WRITABLE_EVENT Triggers when an Widget is made writable. &lt;static&gt; WRITABLE_FAIL_EVENT Triggers when an Widget is made writable and its callbacks fail. × Search results Close "},"module-bajaux_icon_iconUtils.html":{"id":"module-bajaux_icon_iconUtils.html","title":"Module: bajaux/icon/iconUtils","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/icon/iconUtils Utility functions for working with icons and associated HTML. Methods &lt;static&gt; toCssClass(uri) Convert the URI to a usable CSS class, expected to be represented in the spritesheet CSS for that module. Parameters: Name Type Description uri String Returns: Type String &lt;static&gt; toElements(icon [, sync]) Just like toHtml, but resolves an array of raw Elements instead. Parameters: Name Type Argument Default Description icon String | baja.Ord | Array.&lt;(String|baja.Ord)&gt; | baja.Simple icon as ORD, URI, or array of the same; a baja.Icon; (as of 4.12) a gx:Image sync boolean &lt;optional&gt; false set to true to wait for the image to finish loading (making it possible to query it for width/height) before the toHtml promise resolves Returns: to be resolved with an array of span elements Type Promise.&lt;Array.&lt;HTMLSpanElement&gt;&gt; &lt;static&gt; toHtml(icon [, sync]) Given an icon value (string, baja.Icon, etc), convert it into a usable HTML snippet. The HTML will consist of a one or more span tags. Each span will have one img element. If the icon is accounted for in a spritesheet, the img tag will be hidden and the icon will be represented solely by the span using pure CSS. If the icon is not in a spritesheet, the img tag will be shown and have its src tag set to the raw icon image. Parameters: Name Type Argument Default Description icon String | baja.Ord | Array.&lt;(String|baja.Ord)&gt; | baja.Simple icon as ORD, URI, or array of the same; a baja.Icon; (as of 4.12) a gx:Image sync boolean &lt;optional&gt; false set to true to wait for the image to finish loading (making it possible to query it for width/height) before the toHtml promise resolves Returns: promise to be resolved with a raw HTML string containing one or more span tags Type Promise.&lt;string&gt; &lt;static&gt; toImageMetrics(icon) Given an icon, calculate the metrics needed to paint the icon in a painting context such as a canvas. Parameters: Name Type Description icon String | baja.Ord | Array.&lt;(String|baja.Ord)&gt; | baja.Simple icon as ORD, URI, or array of the same; a baja.Icon; (as of 4.12) a gx:Image Since: Niagara 4.11 Returns: Type Promise.&lt;module:bajaux/icon/iconUtils~ImageMetrics&gt; &lt;static&gt; toUris(icon) Convert a value to an array of image URIs. Parameters: Name Type Description icon String | baja.Ord | Array.&lt;(String|baja.Ord)&gt; | baja.Simple a string or array of strings. Each string can be a URI directly, or a module:// ORD. These will be converted to URIs to image files. If passing in arbitrary ORDs, it's recommended to relativizeToSession() first. Can also be a baja.Icon or (as of 4.12) a gx:Image Throws: if invalid input given Type Error Returns: array of image URIs Type Array.&lt;String&gt; Type Definitions ImageMetrics Metrics needed to paint an icon in a painting context such as a canvas. Properties: Name Type Description uri string URI of the image to paint x number pixels from left edge of the image y number pixels from top edge of the image width number width in pixels height number height in pixels × Search results Close "},"module-bajaux_lifecycle_WidgetManager.html":{"id":"module-bajaux_lifecycle_WidgetManager.html","title":"Module: bajaux/lifecycle/WidgetManager","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/lifecycle/WidgetManager WidgetManager's job is to manage the lifecycle of widgets, from the initial \"what kind of widget do I need?\" question through to the destruction of the unneeded widget. new (require(\"bajaux/lifecycle/WidgetManager\"))(params) Parameters: Name Type Description params object Properties Name Type Description registry module:bajaux/registry/Registry the registry responsible for looking up Widget types Since: Niagara 4.10 Methods buildContext(params) This method functions as the \"starting point\" for a Widget build. It receives the parameters as given by the user, and calculates a build context to be used during the rest of the initialize/load/destroy lifecycle. Parameters: Name Type Description params module:bajaux/lifecycle/WidgetManager~BuildParams Returns: Type Promise.&lt;module:bajaux/lifecycle/WidgetManager~BuildContext&gt; buildFor(params [, widget]) Instantiates, initializes, and loads a value into a new Widget as defined by the input parameters. Parameters: Name Type Argument Description params module:bajaux/lifecycle/WidgetManager~BuildParams widget module:bajaux/Widget &lt;optional&gt; if present, skip the instantiation and just initialize/load the given widget instance. Returns: resolves to the widget after it has been initialized and loaded. Type Promise.&lt;module:bajaux/Widget&gt; deriveConfiguredConstructor(params) Parameters: Name Type Description params module:bajaux/lifecycle/WidgetManager~BuildParams Returns: the constructor, as configured via the type parameter, if present; otherwise undefined. Override to define other methods of examining params to derive a directly-configured constructor. Type function | Promise.&lt;function()&gt; destroy(widget) Destroy the widget. Parameters: Name Type Description widget module:bajaux/Widget Returns: Type Promise error(err) This method is called when an error is encountered with a Widget. If there is an installed error hook on this manager, it will be invoked. Parameters: Name Type Description err Error the error from the Widget Returns: If an error hook is installed, this will resolve once the error hook is finished. If no error hook is installed, this will reject with the provided error. Type Promise initialize(widget, buildContext) Initialize the widget into the DOM element as specified in the build context. Parameters: Name Type Description widget module:bajaux/Widget buildContext module:bajaux/lifecycle/WidgetManager~BuildContext Returns: Type Promise installHooks(hooks) Install hooks to be invoked at various stages of a widget lifecycle. Parameters: Name Type Description hooks module:bajaux/lifecycle/WidgetManager~BuildHooks instantiate(buildContext) Create a new Widget instance from the build context. If no widget constructor could be determined, default to a ToStringWidget. Parameters: Name Type Description buildContext module:bajaux/lifecycle/WidgetManager~BuildContext Returns: Type module:bajaux/Widget | Promise.&lt;module:bajaux/Widget&gt; load(widget, buildContext) Load the value from the build context into the widget. Parameters: Name Type Description widget module:bajaux/Widget buildContext module:bajaux/lifecycle/WidgetManager~BuildContext Returns: Type Promise makeFor(params) Resolves a new Widget instance as defined by the input parameters, but does not initialize or load it anywhere. Parameters: Name Type Description params module:bajaux/lifecycle/WidgetManager~BuildParams Returns: Type Promise.&lt;module:bajaux/Widget&gt; resolveConstructor(params) Parameters: Name Type Description params module:bajaux/lifecycle/WidgetManager~BuildParams Returns: resolves the constructor to be used to instantiate the widget, either as configured via params or as looked up from the registry. Type Promise.&lt;(function()|undefined)&gt; resolveFromRegistry(params) Parameters: Name Type Description params module:bajaux/lifecycle/WidgetManager~BuildParams Returns: the constructor resolved from the registry. By default, do a simple lookup by params.value; override to define how registry lookups are performed. Type Promise.&lt;function()&gt; Type Definitions BuildContext Object describing the configuration needed to construct, initialize, and load a Widget in a DOM element. Type: Object Properties: Name Type Argument Description widgetConstructor function &lt;optional&gt; Widget constructor to instantiate. If no constructor could be found it is up to the WidgetManager to decide whether to instantiate a default Widget type or to reject. constructorParams object params object to pass to the Widget constructor initializeParams Object &lt;optional&gt; params object to pass to the initialize() method layoutParams Object &lt;optional&gt; params object to pass to the layout() method loadParams Object &lt;optional&gt; params object to pass to the load() method dom string | HTMLElement | JQuery | * DOM element in which to build a Widget. Will be translated by the WidgetManager to an appropriate type for the Widget. value * &lt;optional&gt; the value to load into the Widget. null is an acceptable loadable value. If undefined, no loading should be performed. data object any additional data passed by the caller into the WidgetManager as the data property. This will be passed through the build lifecycle untouched. Most useful when using lifecycle hooks to add functionality to the WidgetManager instance. BuildHooks} Object describing hooks to be invoked at various points in a widget's lifecycle. Each hook will be invoked with widget and buildContext arguments. Type: Object Properties: Name Type Argument Description instantiated function &lt;optional&gt; called immediately after a widget is constructed preInitialize function &lt;optional&gt; called before initialize() is called postInitialize function &lt;optional&gt; called after initialize() completes preLoad function &lt;optional&gt; called before load() is called postLoad function &lt;optional&gt; called after load() completes error module:bajaux/lifecycle/WidgetManager~error &lt;optional&gt; called when a widget encounters an error BuildParams Object describing the parameters that can be passed to WidgetManager to define a build context. Subclasses of WidgetManager may support additional parameters. Type: Object Properties: Name Type Argument Description type String | function &lt;optional&gt; a bajaux/Widget subclass constructor function - if given, an instance of that Widget will always be instantiated instead of dynamically looked up from the value. You can also use a RequireJS module ID that resolves to a Widget subclass constructor. value * &lt;optional&gt; the value to be loaded into the new widget, if applicable. dom string | HTMLElement | JQuery | * &lt;optional&gt; the DOM element in which the new widget should be initialized, if applicable. properties Object &lt;optional&gt; the bajaux Properties the new widget should have. enabled Boolean &lt;optional&gt; set to false to cause the new widget to be disabled. Not used for lookups. readonly Boolean &lt;optional&gt; set to true to cause the new widget to be readonly. Not used for lookups. formFactors String | Array.&lt;String&gt; &lt;optional&gt; the possible form factors the new widget should have. The created widget could match any of these form factors depending on what is registered in the database. If no widget is found that supports any of these form factors, then no widget will be created (even if one is present that supports a different form factor). If no form factor is given, then the widget created could be of any form factor. formFactor String &lt;optional&gt; same as a formFactors array of length 1. hooks module:bajaux/lifecycle/WidgetManager~BuildHooks &lt;optional&gt; any hooks you wish to run at various stages in this widget's lifecycle. They will run immediately after any installed hooks. × Search results Close "},"module-bajaux_mixin_batchLoadMixin.html":{"id":"module-bajaux_mixin_batchLoadMixin.html","title":"Module: bajaux/mixin/batchLoadMixin","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/mixin/batchLoadMixin new (require(\"bajaux/mixin/batchLoadMixin\"))(target) Applies the batchLoad mixin to the target Widget. The batchLoad mixin does not alter the behavior of the target Widget, but instead defines a behavioral contract. It defines the way it will handle a baja.comm.Batch passed to the load() method (thus allowing multiple Widgets to load and subscribe BajaScript values in a single network call). It states: If my load() method does receive a batch parameter, and does add a transaction to it (say, by passing it to baja.comm.Subscriber#subscribe), then I must notify the caller after I am through adding transactions to that Batch and it is safe to commit. I do this by checking for a progressCallback parameter, and passing COMMIT_READY to it. If my load() method does not make use of the batch, it must still emit COMMIT_READY, but can do so at any time. (Due to this constraint, it does not make sense to add batchLoadMixin to a widget that does not actually use a batch.) Widgets that append transactions to a batch parameter in the load() function, without marking themselves with this mixin, should be expected have those loads fail. Likewise, passing a batch to a Widget's load() function without checking whether it has the batchLoad mixin can also fail. Why is this contract necessary? When passing a batch to load(), you aren't guaranteed that load() will not perform some other asynchronous work before appending transactions to the batch. If you don't wait for the transactions to complete, you run the risk of committing the batch prematurely. Then when the widget gets around to appending transactions to the already-committed batch, it will fail. To make this easier, batchLoadMixin.loadWidgets handles a lot of this workflow for you. Parameters: Name Type Description target module:bajaux/Widget Example Example implementation of the batchLoad contract. MyWidget.prototype.doLoad = function (component, params) { var batch = params &amp;&amp; params.batch, progressCallback = params &amp;&amp; params.progressCallback, promise = this.getSubscriber().subscribe({ comps: component, batch: batch }); //I'm done with the batch - let the caller know they can commit it if (progressCallback) { progressCallback(batchLoadMixin.COMMIT_READY); } return promise; }; Members &lt;static, constant&gt; COMMIT_READY :string Value to be passed to a progressCallback parameter to indicate that a batch given to the load() function can be safely committed. Type: string Methods &lt;static&gt; loadWidgets(widgets, values [, params]) Loads values into the given widgets, passing one Batch into the load() method for each one. Widgets that make use of the Batch are expected to have batchLoadMixin. See documentation for the mixin itself for contractual details. Parameters: Name Type Argument Description widgets Array.&lt;module:bajaux/Widget&gt; the widgets to load values Array.&lt;*&gt; values to load into the widgets params Object &lt;optional&gt; Properties Name Type Argument Description batch baja.comm.Batch &lt;optional&gt; a batch to pass into each widget's load method. If none is given, a new batch will be created and committed. progressCallback function &lt;optional&gt; This callback function itself will receive COMMIT_READY when the input batch is ready to commit. The callback will not be fired if no batch is input. Returns: promise to be resolved when all widgets have completed loading Type Promise × Search results Close "},"module-bajaux_mixin_batchSaveMixin.html":{"id":"module-bajaux_mixin_batchSaveMixin.html","title":"Module: bajaux/mixin/batchSaveMixin","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/mixin/batchSaveMixin new (require(\"bajaux/mixin/batchSaveMixin\"))(target) Applies the batchSave mixin to the target Widget. The batchSave mixin does not alter the behavior of the target Widget, but instead defines a behavioral contract. It defines the way it will handle a baja.comm.Batch passed to the save() method (thus allowing multiple Widgets to save BajaScript values in a single network call). It states: If my save() method does receive a batch parameter, and does add a transaction to it (say, by passing it to Component#set), then I must notify the caller after I am through adding transactions to that Batch and it is safe to commit. I do this by checking for a progressCallback parameter, and passing COMMIT_READY to it. If my save() method does not make use of the batch, it must still emit COMMIT_READY, but can do so at any time. (Due to this constraint, it does not make sense to add batchSaveMixin to a widget that does not actually use a batch.) Widgets that append transactions to a batch parameter in the save() function, without marking themselves with this mixin, should be expected have those saves fail. Likewise, passing a batch to a Widget's save() function without checking whether it has the batchSave mixin can also fail. Why is this contract necessary? When passing a batch to save(), you aren't guaranteed that save() will not perform some other asynchronous work before appending transactions to the batch. If you don't wait for the transactions to complete, you run the risk of committing the batch prematurely. Then when the widget gets around to appending transactions to the already-committed batch, it will fail. To make this easier, batchSaveMixin.saveWidgets handles a lot of this workflow for you. Parameters: Name Type Description target module:bajaux/Widget Example Example implementation of the batchSave contract. MyWidget.prototype.doSave = function (component, params) { var batch = params &amp;&amp; params.batch, progressCallback = params &amp;&amp; params.progressCallback, promise = component.set({ slot: 'saved', value: true, batch: batch }); //I'm done with the batch - let the caller know they can commit it if (progressCallback) { progressCallback(batchSaveMixin.COMMIT_READY); } return promise; }; Members &lt;static, constant&gt; COMMIT_READY :string Value to be passed to a progressCallback parameter to indicate that a batch given to the save() function can be safely committed. Type: string Methods &lt;static&gt; saveWidgets(widgets [, params]) Saves the given widgets, passing one Batch into the save() method for each one. Widgets that make use of the Batch are expected to have batchSaveMixin. See documentation for the mixin itself for contractual details. Parameters: Name Type Argument Description widgets Array.&lt;module:bajaux/Widget&gt; the widgets to save params Object &lt;optional&gt; Properties Name Type Argument Description batch baja.comm.Batch &lt;optional&gt; a batch to pass into each widget's save method. If none is given, a new batch will be created and committed. progressCallback function &lt;optional&gt; This callback function itself will receive COMMIT_READY when the input batch is ready to commit. The callback will not be fired if no batch is input. Returns: promise to be resolved when all widgets have completed saving Type Promise × Search results Close "},"module-bajaux_mixin_responsiveMixIn.html":{"id":"module-bajaux_mixin_responsiveMixIn.html","title":"Module: bajaux/mixin/responsiveMixIn","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/mixin/responsiveMixIn new (require(\"bajaux/mixin/responsiveMixIn\"))(target, conditions) Applies the responsive mixin to the target Widget. This mixin provides responsive layout for a widget based upon its dimensions. It does this by adding or removing CSS classes to/from the widget's DOM element. The mixin injects itself by cross cutting the widget's layout behavior. As of Niagara 4.8, omit the conditions argument to use a set of default classes: phone-only: less than 600px wide tablet-portrait-up: 600px wide or greater tablet-landscape-up: 900px wide or greater desktop-up: 1200px wide or greater Extends: module:bajaux/Widget Parameters: Name Type Description target module:bajaux/Widget The widget to apply the mixin to. conditions Object.&lt;String, (module:bajaux/mixin/responsiveMixIn~ResponsiveCondition|module:bajaux/mixin/responsiveMixIn~ResponsiveCallback)&gt; An object that maps class names to set of conditions. If all conditions are met, the class name will be added to the widget's DOM element. If the conditions aren't met, the class name will be removed. Examples Apply responsive layout to a widget var ResponsiveWidget = function () { Widget.apply(this, arguments); responsiveMixIn(this, { 'my-css-class-to-use-when-small': { maxHeight: 768, maxWidth: 1024 }, 'my-css-class-to-use-when-square': function (info) { return info.width === info.height; } }); }; ResponsiveWidget.prototype = Object.create(Widget.prototype); ResponsiveWidget.prototype.doInitialize = function (dom) { dom.addClass('ResponsiveWidget'); dom.html(myHtmlTemplate()); }; // in css: // .ResponsiveWidget.my-css-class-to-use-when-small { // background-color: white; // } Use a set of default classes // ... responsiveMixIn(this); // in css: // .ResponsiveWidget.tablet-portrait-up { // display: flex; // flex-flow: row wrap; // } // .ResponsiveWidget.tablet-landscape-up { // flex-flow: row nowrap; // } Methods applyParams( [params]) Can re-apply certain params that can also be passed to the constructor. Parameters: Name Type Argument Description params Object &lt;optional&gt; Properties Name Type Argument Default Description readonly Boolean &lt;optional&gt; enabled Boolean &lt;optional&gt; true must explicitly set to false to disable properties Object &lt;optional&gt; Since: Niagara 4.10 Inherited From: module:bajaux/Widget#applyParams Returns: promise to be resolved after any setEnabled/setReadonly work is done. Note that these functions will not be called if the value of enabled/readonly is not actually changing. Type Promise canApplyResponsiveClass(className [, mediaInfo]) Returns true if the class can be added/removed to/from a widget's DOM element. Parameters: Name Type Argument Default Description className String The name of the class to test for. mediaInfo module:bajaux/mixin/responsiveMixIn~ResponsiveMediaInfo &lt;optional&gt; this.getResponsiveMediaInfo() The widget media info. Returns: Returns true if all the conditions for applying the class are met. Type Boolean changed(name, value) Called whenever a Widget's Property is changed. If this Widget is not yet initialized, this is a no-op. This function should not typically be overridden. doChanged() should be overridden instead. Parameters: Name Type Description name String The name of the Property that's changed. value * The new Property value. Inherited From: module:bajaux/Widget#changed See: module:bajaux/Widget#properties Returns: Type Promise cleanupDom() Called to clean up the DOM when the widget is being destroyed. This method can be overridden if DOM clean up needs to be handled in a different way. Inherited From: module:bajaux/Widget#cleanupDom destroy( [params]) Indicates that a widget is no longer needed and is in the process of being removed. In this function, subclasses can deallocate any resources, event handlers, etc. that they may be holding. Delegates the actual work to doDestroy. This method will not typically be overridden. doDestroy() should be overridden instead. Triggers a bajaux:destroy or bajaux:destroyfail event, as appropriate. Please note, after doDestroy has resolved, the DOM will be emptied, all event handlers will be removed and the 'widget' data stored on the DOM element will be deleted. Parameters: Name Type Argument Description params object &lt;optional&gt; optional parameters to be passed to doDestroy Inherited From: module:bajaux/Widget#destroy See: module:bajaux/Widget#doDestroy Returns: A promise to be resolved when the widget has been destroyed Type Promise doChanged(name, value) Called by changed() when a Property is changed. This method is designed to be overridden by any subclasses. Parameters: Name Type Description name String The name of the Property that's changed. value * The new Property value. Inherited From: module:bajaux/Widget#doChanged Returns: Type Promise | * doDestroy( [params]) Called by destroy so this widget has a chance to clean up after itself and release any resources it is holding. Notably, any jQuery event handlers registered on child elements of the widget's DOM element should be unregistered here. Also, you may want to remove any CSS classes you've added to the widget's DOM element. Parameters: Name Type Argument Description params Object &lt;optional&gt; Optional params object passed to destroy() Inherited From: module:bajaux/Widget#doDestroy See: module:bajaux/Widget#destroy Returns: An optional promise that's resolved once the widget has been destroyed. Type * | Promise doEnabled(enabled) Called when the widget is enabled/disabled. Parameters: Name Type Description enabled Boolean the new enabled state. Inherited From: module:bajaux/Widget#doEnabled Returns: An optional Promise that can be returned if the state change is asynchronous. Type * | Promise doInitialize(element [, params]) Performs the actual work of initializing the DOM element in which this widget will live. This function should be overridden by subclasses - the subclass function should append elements to element as necessary and then optionally return a promise. Most commonly, this will involve building up the HTML structure necessary to load in a value. If this widget will display/edit a String, for example, doInitialize might append a text input element to the target element. A DynamicEnum might include a &lt;select&gt; dropdown. In some cases, no initialization may be required at all. This might be the case if you are binding the widget to an HTML element that is already pre-populated with all the necessary structure to load a value, or maybe doLoad will empty out the element completely and rebuild it from scratch every time a new value is loaded. In this case, you do not need to override this method. (However, a widget that overrides neither doInitialize nor doLoad will not be very useful!) Tip: the promises returned by setEnabled and setReadonly can only ever resolve after initialize itself resolves. So return this.setEnabled(enabled) or return this.setReadonly(readonly) from doInitialize will result in a deadlock that will never resolve. They may be called, but not returned. Parameters: Name Type Argument Description element JQuery The element in which this Widget should build its HTML. params Object &lt;optional&gt; Optional params object passed into initialize(). Inherited From: module:bajaux/Widget#doInitialize See: module:bajaux/Widget#initialize Returns: An optional promise to be resolved once the Widget has initialized. Type * | Promise doLayout( [params]) Called when the layout of the Widget changes. This method is designed to be overridden. Parameters: Name Type Argument Description params * &lt;optional&gt; as of Niagara 4.10, any parameters passed to layout() will also be passed to doLayout(). Inherited From: module:bajaux/Widget#doLayout Returns: This method may optionally return a promise once the Widget has been laid out. Type * | Promise doLoad(value [, params]) Performs the actual work of populating the widget's HTML to reflect the input value. This function should be overridden by subclasses. The subclass function should manipulate the DOM jq() and, optionally,return a promise to indicate that the work of loading the value has completed. Parameters: Name Type Argument Description value * The value to be loaded. params Object &lt;optional&gt; Optional params object passed to load() Inherited From: module:bajaux/Widget#doLoad See: module:bajaux/Widget#load module:bajaux/Widget#value Returns: An optional promise that's resolved once the widget has loaded. Type Promise doModified(modified) The actual implementation for setModified. This function should do any work necessary when the widget is set to a modified or \"dirty\" state - typically enabling a save button, arming a window.onbeforeunload handler, etc. Likewise, it should do the opposite when setting modified to false. Note that this is synchronous, as is setModified. Async work can be performed, but setModified will not wait for it. Parameters: Name Type Description modified Boolean Inherited From: module:bajaux/Widget#doModified See: module:bajaux/Widget#setModified doRead() Does the work of reading the widget's current representation. This might mean reading a series of text inputs and assembling their values into an array. It might mean instantiating a copy of the backing baja.Component and setting slot values on the new copy. It might mean simply returning the boolean value of a checkbox. If your widget is composed of pure text/HTML and is not actually backed by an external value, it might mean returning nothing. When saving a modified widget, the output of this function will be passed directly into this widget's validation process, so all your validation steps should be expecting to receive this. It will also be passed to doSave, so your doSave implementation should also expect this value. The default behavior of doRead is simply to use the widget's current value. Inherited From: module:bajaux/Widget#doRead See: module:bajaux/Widget#read Returns: The read value, or a promise to be resolved with the read value Type * | Promise doReadonly(readonly) Called when the widget is set to readonly or made writable. Parameters: Name Type Description readonly Boolean the new readonly state. Inherited From: module:bajaux/Widget#doReadonly Returns: An optional Promise that can be returned if the state change is asynchronous. Type * | Promise doSave(validValue [, params]) Performs the actual work of saving the widget. This function should be overridden by subclasses to save the value. The subclass function should save the value and then, optionally, return a promise to indicate that the work of saving the widget has completed. Parameters: Name Type Argument Description validValue * The value to be used for saving. This value will have been read from the widget using read() and validated using validate(). params Object &lt;optional&gt; Optional params object passed to save() Inherited From: module:bajaux/Widget#doSave Returns: An optional promise that's resolved once the widget has saved. Type * | Promise generateId() Generate a unique DOM ID. The ID will include the name of this widget's constructor just for tracing/debugging purposes. Inherited From: module:bajaux/Widget#generateId Returns: Type String getChildWidgets( [params]) Returns an array of child widgets living inside this editor's DOM. This method will specifically not return child widgets of child widgets for instance, if this widget has one child editor for a baja.Facets, you will only get a single widget back - it won't recurse down and give you all the tag editors, type editors etc. This is safer and easier than using $.find(), which recurses down, or carefully managing strings of $.children() calls. Pass in a jQuery instance to limit the child editor search to a particular set of elements. Otherwise, will search all child elements of this editor's DOM. If this editor has not initialized yet, you'll just get an empty array back. The returned array will have some utility functions attached that return promises. See example for details. Parameters: Name Type Argument Description params Object | jQuery &lt;optional&gt; Properties Name Type Argument Default Description dom JQuery &lt;optional&gt; this.jq().children() the dom element to search type function &lt;optional&gt; the widget type to search for - pass in the actual constructor, for instanceof checks Since: Niagara 4.10 Inherited From: module:bajaux/Widget#getChildWidgets Returns: an array of child widgets Type Array.&lt;module:bajaux/Widget&gt; Examples var kids = ed.getChildWidgets(); kids.setAllEnabled(false).then(function () {}); kids.setAllModified(false).then(function () {}); kids.setAllReadonly(false).then(function () {}); kids.readAll().then(function (valuesArray) {}); kids.validateAll().then(function (valuesArray) {}); kids.saveAll().then(function () {}); kids.destroyAll().then(function () {}); var stringEditors = ed.getChildWidgets({ type: StringEditor }); getCommandGroup() Return the widget's command group. Inherited From: module:bajaux/Widget#getCommandGroup Returns: Type module:bajaux/commands/CommandGroup getFormFactor() Return the widget's form-factor. The form-factor is normally passed in from the Widget's constructor. However, it can be set from a 'formFactor' property if required. A widget's form-factor typically doesn't change during a widget's life-cycle. Inherited From: module:bajaux/Widget#getFormFactor See: module:bajaux/Widget.formfactor Returns: The form-factor. Type String getResponsiveMediaInfo() Return media information for the widget. This is used in deciding whether to add/remove a CSS class to/from a widget when it's laid out. The information will come from the widget's container, not the widget itself. By default, it will look up the DOM hierarchy to find an element with the class bajaux-widget-container, which will be provided to you in most profiles (the HTML5 Hx Profile's content window, the element containing a widget in a Px view, etc.). If none is found, will use the dimensions of the browser window itself. This method returns width and height but could contain more properties in future. Please note, any calculated pixel values should be floored to the nearest integer. A developer can override this method to calculate the media information differently. Returns: The media information for the widget. Type module:bajaux/mixin/responsiveMixIn~ResponsiveMediaInfo hasMixIn(mixin) Return true if the widget implements the specified MixIn. Parameters: Name Type Description mixin String the name of the mixin to test for. Inherited From: module:bajaux/Widget#hasMixIn Returns: Type Boolean initialize(dom [, params] [, layoutParams]) Initializes the DOM element to be bound to this Widget. In a nutshell, initialize defines the following contract: After initialize completes and resolves its Promise, the target element will be fully initialized, structured, and ready to load in a value. It will be accessible by calling this.jq(). If this is an editor, load may not be called until initialize's promise is resolved. Attempting to load a value prior to initialization will result in failure. This widget will be set as a jQuery data value on the initialized DOM element. It can be retrieved by calling Widget.in(element). initialize delegates the actual work of building the HTML structure (if any) to the doInitialize function. When subclassing Widget, you should not override initialize. doInitialize should be overridden. After initialize completes, an bajaux:initialize or bajaux:initializefail event will be triggered, as appropriate. initialize is a one-time operation. It will always reject if the widget has already been initialized once, or if it has been destroyed. Parameters: Name Type Argument Description dom JQuery The jQuery DOM element in which this widget should build its HTML (will be passed directly to doInitialize) params * &lt;optional&gt; optional parameters object to be passed through to doInitialize layoutParams * &lt;optional&gt; as of Niagara 4.10, optional parameters object to be passed through to layout Inherited From: module:bajaux/Widget#initialize See: module:bajaux/Widget#doInitialize Returns: A promise to be resolved once the widget has initialized Type Promise isDesignTime() Returns true if the Widget is in a graphic design editor. Inherited From: module:bajaux/Widget#isDesignTime Returns: Type Boolean isDestroyed() Return true if this Widget has already been destroyed. After destruction, initialize() will always reject: the widget cannot be reused. Inherited From: module:bajaux/Widget#isDestroyed Returns: Type Boolean isEnabled() Returns this widget's enabled state. Inherited From: module:bajaux/Widget#isEnabled See: module:bajaux/Widget#setEnabled Returns: Type Boolean isInitialized() Return true if this Widget is initialized. Inherited From: module:bajaux/Widget#isInitialized Returns: Type Boolean isLoading() Check if this widget is currently in the process of loading. This will return true immediately after load is called, and return false after the load promise resolves. Inherited From: module:bajaux/Widget#isLoading Returns: Type Boolean isModified() Returns this widget's modified state. Inherited From: module:bajaux/Widget#isModified Returns: Type Boolean isReadonly() Returns this widget's readonly state. Inherited From: module:bajaux/Widget#isReadonly See: module:bajaux/Widget#setReadonly Returns: Type Boolean jq() Returns the jQuery DOM element in which this widget has been initialized. If initialize() has not yet been called, then this will return null. Inherited From: module:bajaux/Widget#jq Returns: the DOM element in which this widget has been initialized, or null if not yet initialized. Type JQuery | null layout( [params]) Overrides the bajaux Widget's layout method. This will add/remove class names to/from a widget's DOM element depending on whether its responsive conditions are met. Parameters: Name Type Argument Description params * &lt;optional&gt; as of Niagara 4.10, any parameters passed to layout() will also be passed to doLayout(). Inherited From: module:bajaux/mixin/responsiveMixIn#layout Overrides: module:bajaux/Widget#layout See: module:bajaux/Widget#layout module:bajaux/Widget#doLayout Returns: Type Promise load(value [, params]) Updates the widget's HTML with the given value. An widget for editing a string, for example, might load the string into a text input. A view for editing a DynamicEnum might programmatically set a &lt;select&gt; dropdown's value. load() may not be called until initialize() has completed its work. If initialize() is not finished, load() will reject. After load() completes its work, the value loaded will be accessible via this.value(). load() delegates the work of loading the HTML values to doLoad(). Subclasses will typically not override load, but more commonly will override doLoad. After load() completes, a bajaux:load or bajaux:loadfail event will be triggered, as appropriate. While this method is performing its work, this.isLoading() will return true. Parameters: Name Type Argument Description value * The value to be loaded params Object &lt;optional&gt; additional parameters to be passed to doLoad() Inherited From: module:bajaux/Widget#load See: module:bajaux/Widget#doLoad module:bajaux/Widget#value module:bajaux/mixin/batchLoadMixin Returns: A promise to be resolved with the loaded value after the widget has been loaded, or rejected if the widget fails to load the value. Type Promise loadAndModify(value [, params]) Loads in a new value, and sets the widget modified as well. Use this convenience method when you wish to load in a new value as if a user had done it, thereby triggering the necessary modify event handlers. Parameters: Name Type Argument Description value * params * &lt;optional&gt; Since: Niagara 4.12 Inherited From: module:bajaux/Widget#loadAndModify Returns: Type Promise.&lt;*&gt; properties() Return the Properties for a widget. Inherited From: module:bajaux/Widget#properties Returns: The Properties for a widget. Type module:bajaux/Properties read() Read the current representation of the widget. For instance, if the widget is made up from two text input boxes, this might resolve an object with two strings from those text boxes. Note the word \"representation\" - this function does not necessarily return the widget's actual value, but might assemble a different object, or array, or number, based on current user-entered values. read will not typically be overridden. doRead() should be overridden instead. Inherited From: module:bajaux/Widget#read See: module:bajaux/Widget#doRead Returns: A promise that will be resolved with a value read from the widget as specified by doRead, or rejected if the read fails. Type Promise requestFocus() Attempts to place the cursor focus on this editor. For instance, if showing a simple string editor in a dialog, it should request focus so that the user can simply begin typing without having to move the mouse over to it and click. Override this as necessary; by default, will place focus on the first input or textarea element in this editor's element. Since: Niagara 4.10 Inherited From: module:bajaux/Widget#requestFocus resolve(data [, resolveParams]) Resolve a value from some data. Please note, this will not load the value but will resolve some data that could then be loaded by the widget. By default, this will treat the data as an ORD so it can be resolved via BajaScript. Parameters: Name Type Argument Description data * | String | baja.Ord Specifies some data used to resolve a load value so load(value) can be called on the widget. resolveParams Object &lt;optional&gt; An Object Literal used for ORD resolution. This parameter is designed to be used internally by bajaux and shouldn't be used by developers. Inherited From: module:bajaux/Widget#resolve Returns: a promise to be resolved with the value resolved from the given data object Type Promise save( [params]) Saves any outstanding user-entered changes to this widget. Triggers a bajaux:save or bajaux:savefail event, as appropriate. In order to save the widget, its current value will be validated using validate(), then the validated value will be passed to doSave(). This method will not typically be overridden. doSave() should be overridden instead. Parameters: Name Type Argument Description params Object &lt;optional&gt; Additional parameters to be passed to doSave() Inherited From: module:bajaux/Widget#save See: module:bajaux/Widget#doSave module:bajaux/mixin/batchSaveMixin Returns: A promise to be resolved once the widget has been saved, or rejected if the save fails. Type Promise setCommandGroup(commandGroup) Set this widget's command group. Triggers a bajaux:changecommandgroup event. Parameters: Name Type Description commandGroup module:bajaux/commands/CommandGroup Inherited From: module:bajaux/Widget#setCommandGroup setEnabled(enabled) Set this widget's enabled state. Setting of the internal flag will be synchronous, so isEnabled will return the expected value immediately after calling this function. However, the actual work of updating the DOM cannot be performed until after the widget has finished initializing, so this method will return a promise. This method will not typically be overridden. doEnabled() should be overridden instead. Parameters: Name Type Description enabled Boolean the new enabled state Inherited From: module:bajaux/Widget#setEnabled See: module:bajaux/Widget#isEnabled Returns: A promise to resolve immediately if initialize has not yet been called, that will resolve once the work of initialize followed by doEnabled have both been completed. It will reject if initialize or doEnabled fail. Type Promise setModified(modified) Sets this widget's modified or \"dirty\" status, to indicate that the user has made changes to this widget that may need to be saved. The modification status will only be set if the widget is initialized and the widget is not loading a new value. Triggers bajaux:modify or bajaux:unmodify depending on the input value. Any arguments passed to this function after the first will be passed through to the triggered event. This method should not typically be overridden. doModified() should be overridden instead. Parameters: Name Type Description modified Boolean | * (a non-Boolean will be checked for truthiness) Inherited From: module:bajaux/Widget#setModified See: module:bajaux/Widget#doModified Example Say I have collection of nested widgets in my DOM element. Whenever one of those widgets is modified, I want to mark myself modified but also provide the originally modified editor. For example, when a Property Sheet is modified, I want to know which row caused the modification. var that = this; dom.on(events.MODIFY_EVENT, function (e, modifiedEd) { that.setModified(true, modifiedEd); return false; }); setReadonly(readonly) Set this widget's readonly state. Setting of the internal flag will be synchronous, so isReadonly will return the expected value immediately after calling this function. However, the actual work of updating the DOM cannot be performed until after the widget has finished initializing, so this method will return a promise. This method will not typically be overridden. doReadonly() should be overridden instead. Parameters: Name Type Description readonly Boolean the new readonly state. Inherited From: module:bajaux/Widget#setReadonly See: module:bajaux/Widget#isReadonly Returns: A promise to resolve immediately if initialize has not yet been called, that will resolve once the work of initialize followed by doReadonly have both been completed. It will reject if initialize or doReadonly fail. Type Promise toDescription() Access the widget's icon asynchronously. By default this will attempt to access the widget's icon from the originating Lexicon. The Lexicon key should be in the format of keyName.description. If an entry can't be found then a blank string will be used. Inherited From: module:bajaux/Widget#toDescription Returns: A promise to be resolved with the widget's description Type Promise toDisplayName() Access the widget's display name asynchronously. By default, this will attempt to access the widget's display name from the originating Lexicon. The Lexicon key should be in the format of keyName.displayName. If an entry can't be found then the Type's name will be used. Inherited From: module:bajaux/Widget#toDisplayName Returns: A promise to be resolved with the widget's display name Type Promise toIcon() Access the widget's icon asynchronously. By default, this will attempt to access the widget's description from the originating Lexicon. The Lexicon key should be in the format of keyName.icon. If an entry can't be found then a blank String will be returned. Inherited From: module:bajaux/Widget#toIcon Returns: A promise to be resolved with the widget's icon URI. Type Promise trigger() Trigger a widget event. By default, this fires a DOM event on the associated widget's DOM element. Inherited From: module:bajaux/Widget#trigger validate() Read the current value from the widget and validate it. Inherited From: module:bajaux/Widget#validate See: module:bajaux/Validators#validate Returns: A promise to be resolved with the value read from the widget and passed through all validators, or rejected if the value could not be read or validated. Type Promise validators() Return the widget's Validators. Inherited From: module:bajaux/Widget#validators See: module:bajaux/Validators Returns: Type module:bajaux/Validators value() Returns the widget's current loaded value. This the value that was last loaded via load(). To read a widget's current representation, reflecting any user-entered changes, call read(). If no value has been loaded yet, null is returned. Inherited From: module:bajaux/Widget#value See: module:bajaux/Widget#load module:bajaux/Widget#doLoad module:bajaux/Widget#read Returns: the loaded value, or null if a value hasn't been loaded yet. Type * | null Type Definitions ResponsiveCallback(info) A callback that returns true if the condition is met. Parameters: Name Type Description info module:bajaux/mixin/responsiveMixIn~ResponsiveMediaInfo The widget's current width, height, and any other associated media information. Returns: true if the current media info satisfies the condition. Type boolean ResponsiveCondition An object that defines some conditions for the associated class to be added to the widget's DOM element. Type: Object Properties: Name Type Argument Description maxWidth Number &lt;optional&gt; The widget's width in pixels must be less than or equal to this value. maxHeight Number &lt;optional&gt; The widget's height in pixels must be less than or equal to this value. minWidth Number &lt;optional&gt; The widget's width in pixels must be greater than or equal to this value. minHeight Number &lt;optional&gt; The widget's height in pixels must be greater than or equal to this value. ResponsiveMediaInfo Type: Object Properties: Name Type Description width number The width in pixels to use in responsive layout. height number The height in pixels to use in responsive layout. × Search results Close "},"module-bajaux_mixin_subscriberMixIn.html":{"id":"module-bajaux_mixin_subscriberMixIn.html","title":"Module: bajaux/mixin/subscriberMixIn","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/mixin/subscriberMixIn A widget that implements this MixIn will be able to load and subscribe to Components. The MixIn name is 'subscriber'. Requires module:baja module:jquery module:bajaux/events module:bajaux/Widget Members &lt;static&gt; target.getSubscriber Returns this widget's subscriber. Methods &lt;static&gt; target.destroy() Overrides the default widget destroy method. This method ensures all handlers are removed by the Subscriber when the widget is destroyed. See: module:bajaux/Widget#destroy Returns: A promise resolved once everything has been destroyed. Type Promise &lt;static&gt; target.load(value [, params]) Override the default widget load method. Loads a value into the widget. If the value is a Component, then it will be subscribed (unless autoSubscribe is false). This function supports the contract defined in batchLoadMixin. Parameters: Name Type Argument Description value * The value for the Widget to load. params Object &lt;optional&gt; Properties Name Type Argument Description batch baja.comm.Batch &lt;optional&gt; component subscription will use this batch, if provided progressCallback function &lt;optional&gt; a function to be called when subscription progress occurs See: module:bajaux/Widget#load module:bajaux/mixin/batchLoadMixin Returns: A promise that's resolved once the value has been fully loaded. Type Promise &lt;static&gt; target.resolve(data [, resolveParams]) Overrides resolve and injects a Subscriber into the ORD resolution so it's subscribed. Parameters: Name Type Argument Description data Specifies some data used to resolve a load value so load(value) can be called on the widget. resolveParams Object &lt;optional&gt; An Object Literal used for ORD resolution. This parameter is designed to be used internally by bajaux and shouldn't be used by developers. Returns: Type Promise &lt;static&gt; target.setEnabled(enabled) Overrides the default widget setEnabled method. If a widget is enabled then a subscription for the value is started (unless autoSubscribe returns false). If the value is unsubscribed then an unsubscription for the value is attempted. Parameters: Name Type Description enabled Boolean See: module:bajaux/Widget#setEnabled Returns: A promise resolved once the widget is enabled and the loaded component is subscribed Type Promise × Search results Close "},"module-bajaux_Properties.html":{"id":"module-bajaux_Properties.html","title":"Module: bajaux/Properties","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/Properties Configurable Properties for Widgets. new (require(\"bajaux/Properties\"))( [obj]) The properties for a Widget. This are configurable properties that can be configured by a user. Parameters: Name Type Argument Description obj Object | Array.&lt;Object&gt; | module:bajaux/Properties &lt;optional&gt; an initial set of properties with which to initialize this Properties instance. This can be an object literal, an array of object literals, or another Properties instance. Examples Create a Properties instance with an object literal. var props = new Properties({ myProp: 'value', myHiddenProp: { value: 'hiddenValue', hidden: true } }); props.getValue('myProp'); // 'value' props.getValue('myHiddenProp'); // 'hiddenValue' props.get('myHiddenProp').hidden); // true Create a Properties instance with an array. Equivalent to the above. var props = new Properties([ { name: 'myProp', value: 'value' }, { name: 'myHiddenProp', value: 'hiddenValue', hidden: true } ]); Create a Properties instance with a defaultValue for `myProp`. var props = new Properties([ { name: 'myProp', value: 'value', defaultValue: 'this is default' }, { name: 'myHiddenProp', value: 'hiddenValue' } ]); props.getValue('myProp'); // 'value' props.getDefaultValue('myProp'); // 'this is default' props.setValue('myProp', undefined); // make the property value undefined props.getValue('myProp'); // 'this is default' props.setValue('myProp', null); props.getValue('myProp'); // null props.getValue('propThatDoesNotExist'); // null Members toObject Convert this Properties instance into a new raw object literal. The object keys will be the property names, and the values will be the property values (as returned by #getValue()). Note that any metadata about each Property will be lost; to preserve metadata, use toObject(). This function will be useful for converting Properties into a context object. Since: Niagara 4.9 (replaces toValueMap, which still works) See: module:bajaux/Properties#toObject Example Property Sheet converts slot facets into Widget properties. I need to use those facets in my field editor for number formatting purposes. MyFieldEditor.prototype.numberToString = function (number) { var cx = this.properties().toValueMap(); if (typeof cx.precision !== 'number') { cx.precision = 2; } return number.toString(cx); }; Methods &lt;static&gt; extend() Create a new Properties instance containing the merged properties of one or more other Properties instances. Properties of instances later in the argument list will override properties of earlier instances. Each argument can be of any type acceptable to the Properties constructor (Object, Array, or Properties). Returns: Type module:bajaux/Properties Example var mergedProps = Properties.extend( { myProp: 'a' }, new Properties({ myProp: { value: 'a2', hidden: true } }) ); mergedProps.getValue('myProp'); // 'a2' mergedProps.get('myProp').hidden; // true add(prop [, value]) Add a Property. Please note, if the Property isn't transient, it's value may be saved and loaded elsewhere (for example, in the case of Px, reloaded from a Px file). If the property does not already exist on this Properties instance, this will emit a PROPERTY_ADDED event, with an array (of length 1) of the property names added. Parameters: Name Type Argument Description prop String | Object The Property object to be added or the name of the Property. Properties Name Type Argument Description name String | Object The name of the Property being added or if the first argument is a String, this is the value. value * The current value of the Property. displayName String &lt;optional&gt; The display name of the Property. For translated values, this can be a format string. For example, %lexicon(moduleName:keyName)%. transient Boolean &lt;optional&gt; A hint to an external editor that it doesn't need to save the state of this Property. hidden Boolean &lt;optional&gt; A hint to an external editor to hide this Property. readonly Boolean &lt;optional&gt; A hint to an external editor to make the editor for this Property readonly. typeSpec Boolean &lt;optional&gt; A hint to Niagara on what Simple Niagara Type to use when encoding/decoding the Property. If the Type is a FrozenEnum, the tag name of the Enum should be used. value * &lt;optional&gt; if passing a string name as the first argument, pass the value here as the second. Returns: returns the Properties instance. Type module:bajaux/Properties Examples Add a Property widget.properties().add(\"foo\", true); Add a hidden Property widget.properties().add({ name: \"foo\", value: true, hidden: true }); Add a transient, readonly, hidden Property widget.properties().add({ name: \"foo\", value: true, hidden: true, transient: true, readonly: true }); Add a Property that maps to the baja:Weekday FrozenEnum in Niagara widget.properties().add({ name: \"weekday\", value: \"tuesday\", typeSpec: \"baja:Weekday\" }); addAll(arr) Add a number of properties at once. The object literal configuration for each property is the same as for add(). Parameters: Name Type Description arr Array.&lt;Object&gt; | module:bajaux/Properties an array of object literals to become properties, or a Properties instance to copy onto this one Returns: this Type module:bajaux/Properties clone() Return a clone of this Properties object that can be modified without changing the original. Returns: Type module:bajaux/Properties each(func) Iterate through each Property. Parameters: Name Type Description func function The function to be called for each Property found in the array. This function will have the index, name and value of the Property passed to it. The Context of the function callback will be the Properties instance. If iteration needs to stop prematurely then the function can return false. Returns: Returns the Properties instance. Type module:bajaux/Properties get( [name] [, attrName] [, defAttrValue]) If no arguments are specified, a copy of the internal Properties array will be returned. If only the name is specified, return a copy of the Property for the given name or index. If a name/index and an attribute name is specified, then return the attribute of a Property. If no particular value can be found then return null; Parameters: Name Type Argument Description name String | Number &lt;optional&gt; The name or index of the Property to look up. If not specified, a copy of the internal Property array will be returned. attrName String &lt;optional&gt; If specified, this will retrieve a specific attribute of the Property. For example, specifying 'value' will get the value of the Property. defAttrValue &lt;optional&gt; If specified, this value will be returned if the attribute name can't be found. Returns: A copy of the Property object or null if the Property can't be found. Type Object getDefaultValue( [name]) If a name/index is specified then return a Property's default value or null if nothing can be found. If no name is specified then return an object containing all the Property names and default values. Parameters: Name Type Argument Description name String | Number &lt;optional&gt; If specified, the name of the Property to return or the Property's index. If this parameter is not specified, an object containing all of the property default values will be returned. Returns: The Properties default value or null if nothing is found. Type * | null getIndex(name) Return a Property's index via its name or -1 if it can't be found. Parameters: Name Type Description name String The name of the Property to look up the index number for. Returns: Returns the index number of the Property. Type Number getMetadata( [name]) If no arguments are specified, a copy of the internal Properties array will be returned. If only the name is specified, return a the corresponding meta data object literal for the Property Parameters: Name Type Argument Description name String | Number &lt;optional&gt; The name or index of the Property to look up. If not specified, a copy of the internal Property array will be returned. Returns: The meta data object literal or null if the Property could not be found Type Object getValue( [name] [, defVal]) If no name is specified then return an object containing all the Property names and values. If a name/index is specified then return a Property's value or null if nothing can be found. Property values will fallback to defaultValue if value is not set. Parameters: Name Type Argument Description name String | Number &lt;optional&gt; If specified, the name of the Property to return or the Property's index. If this parameter is not specified, an object containing all of the property values will be returned. defVal &lt;optional&gt; If specified, this will return if a value can't be found providing the first argument is a String. This will override the defaultValue for the given property. Returns: The Properties value or null if nothing is found. Type * | null has(name) Return true if the Property can be found via its name of index. Parameters: Name Type Description name String | Number The name or index of the Property to look up. Returns: returns true if the Property is found. Type Boolean off(name [, func]) Called to detach any event handlers from a Property or to stop listening to all Property change events. Parameters: Name Type Argument Description name String The Property name to remove. func function &lt;optional&gt; The event handler to remove. Returns: The Properties instance. Type module:bajaux/Properties remove(name) Remove a Property. Parameters: Name Type Description name String The name of the Property to remove. Returns: returns the Properties instance. Type module:bajaux/Properties setMetadata(name, metadata [, options]) If an object is specified as the first argument, it will be iterated through with object's properties being set as metadata If a name/index is specified along with a value, the metadata for the particular name/index will be set. Parameters: Name Type Argument Description name String | Object The name of the Property we're going to set or a an object containing several metadata values to be set. The value for each property name in the object will be set as corresponding metadata for that property metadata Object The metadata to be set. options &lt;optional&gt; An optional parameter that is passed down into any changed callbacks or event handlers. Since: Niagara 4.4 Returns: Return false if at least one of the properties wasn't found. Type Boolean setValue(name, value [, options]) If an object is specified as the first argument, it will be iterated through with object's properties being set as values. If a name/index is specified along with a value, the value for the particular value will be set. A Widget can detect Property changes by implemented a method called changed. The changed call back will have the Property name and new value passed to it. A developer can then override 'doChanged' to handle any callbacks in their own widget subclasses. Parameters: Name Type Argument Description name String | Object The name of the Property we're going to set or a an object containing many values to be set. value The value to be set. options &lt;optional&gt; An optional parameter that is passed down into any changed callbacks or event handlers. Returns: Return false if at least one of the properties wasn't found. Type Boolean size() Return the total number of Properties. Returns: returns the total number of Properties. Type Number subset(keys) Build a new Properties instance consisting of a subset of the properties contained within this one. Useful for propagating a specific set of properties down to a child widget. Parameters: Name Type Description keys Array.&lt;String&gt; which keys to include in the subset. Returns: Type module:bajaux/Properties toDisplayName(name) Return a promise that will resolve once the display name of the Property has been resolved. If the Property doesn't have a display name, the Property's name will be used instead. Please note, a display name can be in the format of a Lexicon format. For instance, %lexicon(moduleName:keyName)%. Parameters: Name Type Description name String | Number The name or index of the Property. Returns: The display name of the Property. × Search results Close "},"module-bajaux_registry_Registry.html":{"id":"module-bajaux_registry_Registry.html","title":"Module: bajaux/registry/Registry","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/registry/Registry Base class for Registry implementations (local JS registration, agent-based, etc). API Status: Development new (require(\"bajaux/registry/Registry\"))( [obj]) Parameters: Name Type Argument Description obj Object &lt;optional&gt; a JSON object to use to initially build this registry (typically will be used to reconstitute a registry using the previous output of toJSON). If omitted, registry will be empty on creation. Since: Niagara 4.10 Methods getLocal() Returns: Type module:bajaux/registry/Registry queryAll(value, params) By default, just queries from the entries registered locally. Most likely, subclasses will override this with something more useful. Parameters: Name Type Description value * params module:bajaux/registry/Registry~QueryParams Returns: promise to be resolved with an array of all matching RegistryEntrys Type Promise.&lt;Array.&lt;module:bajaux/registry/RegistryEntry&gt;&gt; queryFirst(value, params) By default, just queries from the entries registered locally. Most likely, subclasses will override this with something more useful. Parameters: Name Type Description value * params module:bajaux/registry/Registry~QueryParams Returns: promise to be resolved with the first matchingRegistryEntry Type Promise.&lt;module:bajaux/registry/RegistryEntry&gt; register(key [, params]) Register a RequireJS module ID locally for the given key. Parameters: Name Type Argument Description key String params module:bajaux/registry/Registry~QueryParams &lt;optional&gt; query parameters/metadata for this RequireJS module Returns: promise to be resolved after registration is complete Type Promise resolveAll(value, params) Perform a query on the registry and resolve all RequireJS modules represented. This will resolve an array of Widget constructors, menu agent functions, etc. Parameters: Name Type Description value * params module:bajaux/registry/Registry~QueryParams Returns: promise to be resolved with an array of the exported results of all RequireJS modules represented, or empty if none found; rejected if the station could not be successfully queried for registry info, or if any of the RequireJS modules failed to resolve Type Promise.&lt;Array.&lt;*&gt;&gt; resolveFirst(value, params) Perform a query on the registry and attempt to resolve the first matching entry's RequireJS module. Note that this differs from resolveAll in that if the first entry fails to resolve (for instance, an invalid RequireJS module ID), it will move on to the next entry and keep trying to resolve all the way down until it can resolve something. Parameters: Name Type Description value * params module:bajaux/registry/Registry~QueryParams Returns: promise to be resolved with the exported results of the first matching entry that successfully resolves its RequireJS module ID, or undefined if none found Type Promise.&lt;*&gt; toJSON() Return an object suitable for serialization using JSON.stringify or similar. The returned object can be passed right back to a Registry constructor to reconstitute later. Returns: Type Object Type Definitions QueryParams Query parameters to narrow the results resolved for a given registry key. Type: Object Properties: Name Type Argument Description rjs String &lt;optional&gt; If given, only resolve entries that have this particular RequireJS ID. deps Array.&lt;Array.&lt;String&gt;&gt; &lt;optional&gt; If given, only resolve entries that have this exact set of RequireJS dependencies. hasAny Array.&lt;String&gt; &lt;optional&gt; If given, only resolve entries that have at least one of these tags. hasAll Array.&lt;String&gt; &lt;optional&gt; If given, only resolve entries that have all of these tags. × Search results Close "},"module-bajaux_registry_RegistryEntry.html":{"id":"module-bajaux_registry_RegistryEntry.html","title":"Module: bajaux/registry/RegistryEntry","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/registry/RegistryEntry An entry intended to be stored in a local Registry, representing a single RequireJS module. Additional parameters can be used to store metadata about this module, allowing it to be queried later in more detail. There is usually no reason to instantiate this class directly; a Registry will create them as needed. API Status: Development new (require(\"bajaux/registry/RegistryEntry\"))(params) Parameters: Name Type Description params module:bajaux/registry/Registry~QueryParams Since: Niagara 4.10 Methods getJsDependencies() Get the list of RequireJS dependencies (typically builtfiles) that must be loaded before requiring the main RequireJS ID. Returns: Type Array.&lt;Array.&lt;String&gt;&gt; getJsId() Get the RequireJS ID for this entry. Returns: Type string getTags() Get a list of tags that serve as metadata for this entry. Returns: Type Array.&lt;String&gt; matches( [params]) Check to see if this entry's metadata matches a registry query. Parameters: Name Type Argument Description params module:bajaux/registry/Registry~QueryParams | module:bajaux/registry/RegistryEntry &lt;optional&gt; Returns: true if this entry matches the query parameters and so should be included in the results Type boolean resolve() Resolve the RequireJS module (and any dependencies) that is represented by this module. Returns: promise to be resolved with the contents of the RequireJS module, or rejected if the module ID or any of its dependencies could not be loaded Type Promise toJSON() Return a raw object representation of this entry. As a contractual requirement, the output of this function should be able to be passed right back to the constructor to create a new instance. Return undefined to indicate that this entry should never be serialized. Returns: Type Object | undefined × Search results Close "},"module-bajaux_spandrel.html":{"id":"module-bajaux_spandrel.html","title":"Module: bajaux/spandrel","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/spandrel API Status: Development (require(\"bajaux/spandrel\"))(arg [, params]) The purpose of spandrel is to provide a reasonably pure-functional, diffable method of defining a nested structure of bajaux Widgets and supporting HTML. Rather than require Widget implementors to manually code calls to initialize() or buildFor(), spandrel allows you to provide your desired structure of HTML elements and their associated Widget instances, and handle the work of updating the document as that structure may change over time. See Building Composite Widgets With spandrel for in-depth information. Parameters: Name Type Argument Default Description arg module:bajaux/spandrel~SpandrelData | function params object &lt;optional&gt; {} params Properties Name Type Argument Description extends function &lt;optional&gt; optionally specify a Widget superclass to extend strategy string &lt;optional&gt; optionally specify a known lookup strategy for dynamically building widgets. Currently, the only accepted value is niagara, which will instruct spandrel to use the Niagara registry to perform widget lookups (introducing a dependency on the webEditors module). If included, it overrides the manager parameter. manager module:bajaux/lifecycle/WidgetManager &lt;optional&gt; optionally provide your own WidgetManager to manage Widget lifecycle Since: Niagara 4.10 Returns: a Widget constructor Type function Examples Generate a static widget const StaticWidget = spandrel([ '&lt;label&gt;Name: &lt;/label&gt;', '&lt;input type=\"text\" value=\"{{ props.name }}\"&gt;', { dom: '&lt;span&gt;&lt;/span&gt;', value: false, properties: 'inherit' } ]); return fe.buildFor({ dom: $('#myStaticWidget'), type: StaticWidget, properties: { name: 'Logan', trueText: 'Good', falseText: 'Not So Good' } }); Generate a dynamic widget with a field editor for each slot const DynamicWidget = spandrel(comp =&gt; comp.getSlots().toArray().map(slot =&gt; ({ dom: '&lt;div class=\"componentSlot\"/&gt;', kids: [ `&lt;label&gt;${ slot.getName() }: &lt;/label&gt;`, { dom: '&lt;span/&gt;', complex: comp, slot: slot } ] }))); return fe.buildFor({ dom: $('#myDynamicWidget'), type: DynamicWidget, value: myComponent }); Subclass an existing dynamic spandrel widget, making changes before rendering. // our superclass will render a &lt;label&gt; element, with a background // determined by a widget property. const LabelWidget = spandrel((value, { properties }) =&gt; { const label = document.createElement('label'); label.innerText = value; label.style.background = properties.background || ''; return label; }); const RedLabelWidget = spandrel((value, { renderSuper }) =&gt; { // renderSuper will call back to the superclass, allowing your subclass // to edit the data before spandrel renders it to the page. // // you can optionally pass a function to renderSuper that will tweak the // widget state before the superclass renders its data. if no tweaking is // desired, just renderSuper() is fine. // return renderSuper((state) =&gt; { state.properties.background = 'lightpink'; // remember to return the new state. return state; }) .then((label) =&gt; { // renderSuper will resolve the data exactly as rendered by the // superclass. label.style.color = 'red'; return label; }); }, { extends: LabelWidget }); Members &lt;static&gt; jsx Use spandrel.jsx as your JSX pragma to convert your JSX into spandrel config. See: module:bajaux/spandrel/jsx × Search results Close "},"module-bajaux_spandrel_jsx.html":{"id":"module-bajaux_spandrel_jsx.html","title":"Module: bajaux/spandrel/jsx","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/spandrel/jsx API Status: Development Methods &lt;static&gt; jsxToSpandrel(type, props, children) Parameters: Name Type Description type string | function HTML tag name, or a Widget constructor to instantiate props object | null children Array.&lt;object&gt; Returns: Type module:bajaux/spandrel~SpandrelData Example Basic JSX-&gt;spandrel example &amp;#37;** @jsx spandrel.jsx *&amp;#37; class ComponentToHTML extends spandrel((comp) =&gt; { return ( &lt;table&gt; { comp.getSlots().properties().toArray().map((prop) =&gt; { return &lt;tr&gt; &lt;td&gt;{ prop.getName() }&lt;/td&gt; &lt;td&gt;{ prop.getType() }&lt;/td&gt; &lt;/tr&gt;; }) } &lt;/table&gt; ); }) {} × Search results Close "},"module-bajaux_util_CommandButton.html":{"id":"module-bajaux_util_CommandButton.html","title":"Module: bajaux/util/CommandButton","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/util/CommandButton new (require(\"bajaux/util/CommandButton\"))() A widget for displaying and invoking a Command. Extends: module:bajaux/Widget Methods applyParams( [params]) Can re-apply certain params that can also be passed to the constructor. Parameters: Name Type Argument Description params Object &lt;optional&gt; Properties Name Type Argument Default Description readonly Boolean &lt;optional&gt; enabled Boolean &lt;optional&gt; true must explicitly set to false to disable properties Object &lt;optional&gt; Since: Niagara 4.10 Inherited From: module:bajaux/Widget#applyParams Returns: promise to be resolved after any setEnabled/setReadonly work is done. Note that these functions will not be called if the value of enabled/readonly is not actually changing. Type Promise canInvokeCommand() Since: Niagara 4.13 Returns: true if both the CommandButton and the loaded Command are enabled Type boolean changed(name, value) Called whenever a Widget's Property is changed. If this Widget is not yet initialized, this is a no-op. This function should not typically be overridden. doChanged() should be overridden instead. Parameters: Name Type Description name String The name of the Property that's changed. value * The new Property value. Inherited From: module:bajaux/Widget#changed See: module:bajaux/Widget#properties Returns: Type Promise cleanupDom() Called to clean up the DOM when the widget is being destroyed. This method can be overridden if DOM clean up needs to be handled in a different way. Inherited From: module:bajaux/Widget#cleanupDom destroy( [params]) Indicates that a widget is no longer needed and is in the process of being removed. In this function, subclasses can deallocate any resources, event handlers, etc. that they may be holding. Delegates the actual work to doDestroy. This method will not typically be overridden. doDestroy() should be overridden instead. Triggers a bajaux:destroy or bajaux:destroyfail event, as appropriate. Please note, after doDestroy has resolved, the DOM will be emptied, all event handlers will be removed and the 'widget' data stored on the DOM element will be deleted. Parameters: Name Type Argument Description params object &lt;optional&gt; optional parameters to be passed to doDestroy Inherited From: module:bajaux/Widget#destroy See: module:bajaux/Widget#doDestroy Returns: A promise to be resolved when the widget has been destroyed Type Promise doChanged(name, value) Called by changed() when a Property is changed. This method is designed to be overridden by any subclasses. Parameters: Name Type Description name String The name of the Property that's changed. value * The new Property value. Inherited From: module:bajaux/Widget#doChanged Returns: Type Promise | * doDestroy() Removes the click handler and CSS class from doInitialize. Overrides: module:bajaux/Widget#doDestroy doEnabled() Updates the DOM to look enabled/disabled depending on whether this widget is enabled and the loaded command can be invoked. Overrides: module:bajaux/Widget#doEnabled doInitialize(dom) Arms a click handler that will invoke the loaded Command. Adds a CommandButton CSS class. Technically, this widget can be initialized in any DOM element, but makes the most sense in a button element. Parameters: Name Type Description dom JQuery Overrides: module:bajaux/Widget#doInitialize doInvoke(cmd, e) Override point to allow customized command invocation from a DOM event. By default, will check that the widget and command are both enabled and then invoke it. Parameters: Name Type Description cmd module:bajaux/commands/Command e JQuery.TriggeredEvent Since: Niagara 4.11 Returns: Type Promise | undefined doLayout( [params]) Called when the layout of the Widget changes. This method is designed to be overridden. Parameters: Name Type Argument Description params * &lt;optional&gt; as of Niagara 4.10, any parameters passed to layout() will also be passed to doLayout(). Inherited From: module:bajaux/Widget#doLayout Returns: This method may optionally return a promise once the Widget has been laid out. Type * | Promise doLoad(cmd) Loads a Command. Binds an event handler to update the DOM whenever the Command's properties are changed. Parameters: Name Type Description cmd module:bajaux/commands/Command Overrides: module:bajaux/Widget#doLoad Returns: promise to be resolved when the Command is loaded, or rejected if no Command given Type Promise doModified(modified) The actual implementation for setModified. This function should do any work necessary when the widget is set to a modified or \"dirty\" state - typically enabling a save button, arming a window.onbeforeunload handler, etc. Likewise, it should do the opposite when setting modified to false. Note that this is synchronous, as is setModified. Async work can be performed, but setModified will not wait for it. Parameters: Name Type Description modified Boolean Inherited From: module:bajaux/Widget#doModified See: module:bajaux/Widget#setModified doRead() Does the work of reading the widget's current representation. This might mean reading a series of text inputs and assembling their values into an array. It might mean instantiating a copy of the backing baja.Component and setting slot values on the new copy. It might mean simply returning the boolean value of a checkbox. If your widget is composed of pure text/HTML and is not actually backed by an external value, it might mean returning nothing. When saving a modified widget, the output of this function will be passed directly into this widget's validation process, so all your validation steps should be expecting to receive this. It will also be passed to doSave, so your doSave implementation should also expect this value. The default behavior of doRead is simply to use the widget's current value. Inherited From: module:bajaux/Widget#doRead See: module:bajaux/Widget#read Returns: The read value, or a promise to be resolved with the read value Type * | Promise doReadonly(readonly) Called when the widget is set to readonly or made writable. Parameters: Name Type Description readonly Boolean the new readonly state. Inherited From: module:bajaux/Widget#doReadonly Returns: An optional Promise that can be returned if the state change is asynchronous. Type * | Promise doSave(validValue [, params]) Performs the actual work of saving the widget. This function should be overridden by subclasses to save the value. The subclass function should save the value and then, optionally, return a promise to indicate that the work of saving the widget has completed. Parameters: Name Type Argument Description validValue * The value to be used for saving. This value will have been read from the widget using read() and validated using validate(). params Object &lt;optional&gt; Optional params object passed to save() Inherited From: module:bajaux/Widget#doSave Returns: An optional promise that's resolved once the widget has saved. Type * | Promise generateId() Generate a unique DOM ID. The ID will include the name of this widget's constructor just for tracing/debugging purposes. Inherited From: module:bajaux/Widget#generateId Returns: Type String getChildWidgets( [params]) Returns an array of child widgets living inside this editor's DOM. This method will specifically not return child widgets of child widgets for instance, if this widget has one child editor for a baja.Facets, you will only get a single widget back - it won't recurse down and give you all the tag editors, type editors etc. This is safer and easier than using $.find(), which recurses down, or carefully managing strings of $.children() calls. Pass in a jQuery instance to limit the child editor search to a particular set of elements. Otherwise, will search all child elements of this editor's DOM. If this editor has not initialized yet, you'll just get an empty array back. The returned array will have some utility functions attached that return promises. See example for details. Parameters: Name Type Argument Description params Object | jQuery &lt;optional&gt; Properties Name Type Argument Default Description dom JQuery &lt;optional&gt; this.jq().children() the dom element to search type function &lt;optional&gt; the widget type to search for - pass in the actual constructor, for instanceof checks Since: Niagara 4.10 Inherited From: module:bajaux/Widget#getChildWidgets Returns: an array of child widgets Type Array.&lt;module:bajaux/Widget&gt; Examples var kids = ed.getChildWidgets(); kids.setAllEnabled(false).then(function () {}); kids.setAllModified(false).then(function () {}); kids.setAllReadonly(false).then(function () {}); kids.readAll().then(function (valuesArray) {}); kids.validateAll().then(function (valuesArray) {}); kids.saveAll().then(function () {}); kids.destroyAll().then(function () {}); var stringEditors = ed.getChildWidgets({ type: StringEditor }); getCommandGroup() Return the widget's command group. Inherited From: module:bajaux/Widget#getCommandGroup Returns: Type module:bajaux/commands/CommandGroup getFormFactor() Return the widget's form-factor. The form-factor is normally passed in from the Widget's constructor. However, it can be set from a 'formFactor' property if required. A widget's form-factor typically doesn't change during a widget's life-cycle. Inherited From: module:bajaux/Widget#getFormFactor See: module:bajaux/Widget.formfactor Returns: The form-factor. Type String hasMixIn(mixin) Return true if the widget implements the specified MixIn. Parameters: Name Type Description mixin String the name of the mixin to test for. Inherited From: module:bajaux/Widget#hasMixIn Returns: Type Boolean initialize(dom [, params] [, layoutParams]) Initializes the DOM element to be bound to this Widget. In a nutshell, initialize defines the following contract: After initialize completes and resolves its Promise, the target element will be fully initialized, structured, and ready to load in a value. It will be accessible by calling this.jq(). If this is an editor, load may not be called until initialize's promise is resolved. Attempting to load a value prior to initialization will result in failure. This widget will be set as a jQuery data value on the initialized DOM element. It can be retrieved by calling Widget.in(element). initialize delegates the actual work of building the HTML structure (if any) to the doInitialize function. When subclassing Widget, you should not override initialize. doInitialize should be overridden. After initialize completes, an bajaux:initialize or bajaux:initializefail event will be triggered, as appropriate. initialize is a one-time operation. It will always reject if the widget has already been initialized once, or if it has been destroyed. Parameters: Name Type Argument Description dom JQuery The jQuery DOM element in which this widget should build its HTML (will be passed directly to doInitialize) params * &lt;optional&gt; optional parameters object to be passed through to doInitialize layoutParams * &lt;optional&gt; as of Niagara 4.10, optional parameters object to be passed through to layout Inherited From: module:bajaux/Widget#initialize See: module:bajaux/Widget#doInitialize Returns: A promise to be resolved once the widget has initialized Type Promise isDesignTime() Returns true if the Widget is in a graphic design editor. Inherited From: module:bajaux/Widget#isDesignTime Returns: Type Boolean isDestroyed() Return true if this Widget has already been destroyed. After destruction, initialize() will always reject: the widget cannot be reused. Inherited From: module:bajaux/Widget#isDestroyed Returns: Type Boolean isEnabled() Returns this widget's enabled state. Inherited From: module:bajaux/Widget#isEnabled See: module:bajaux/Widget#setEnabled Returns: Type Boolean isInitialized() Return true if this Widget is initialized. Inherited From: module:bajaux/Widget#isInitialized Returns: Type Boolean isLoading() Check if this widget is currently in the process of loading. This will return true immediately after load is called, and return false after the load promise resolves. Inherited From: module:bajaux/Widget#isLoading Returns: Type Boolean isModified() Returns this widget's modified state. Inherited From: module:bajaux/Widget#isModified Returns: Type Boolean isReadonly() Returns this widget's readonly state. Inherited From: module:bajaux/Widget#isReadonly See: module:bajaux/Widget#setReadonly Returns: Type Boolean jq() Returns the jQuery DOM element in which this widget has been initialized. If initialize() has not yet been called, then this will return null. Inherited From: module:bajaux/Widget#jq Returns: the DOM element in which this widget has been initialized, or null if not yet initialized. Type JQuery | null layout( [params]) A widget may need to do its own layout calculation. It might need to statically position elements, or show/hide them based on the shape of its container. This function gives a widget an opportunity to do that. It's called once the Widget has been initialized and once the form factor has changed. Also it may be called when the widget's container changes shape or size, or is shown/hidden. This method should not typically be overridden. Override doLayout() instead. Parameters: Name Type Argument Description params * &lt;optional&gt; as of Niagara 4.10, any parameters passed to layout() will also be passed to doLayout(). Inherited From: module:bajaux/Widget#layout Returns: A promise that's resolved once the layout has completed. Type Promise load(value [, params]) Updates the widget's HTML with the given value. An widget for editing a string, for example, might load the string into a text input. A view for editing a DynamicEnum might programmatically set a &lt;select&gt; dropdown's value. load() may not be called until initialize() has completed its work. If initialize() is not finished, load() will reject. After load() completes its work, the value loaded will be accessible via this.value(). load() delegates the work of loading the HTML values to doLoad(). Subclasses will typically not override load, but more commonly will override doLoad. After load() completes, a bajaux:load or bajaux:loadfail event will be triggered, as appropriate. While this method is performing its work, this.isLoading() will return true. Parameters: Name Type Argument Description value * The value to be loaded params Object &lt;optional&gt; additional parameters to be passed to doLoad() Inherited From: module:bajaux/Widget#load See: module:bajaux/Widget#doLoad module:bajaux/Widget#value module:bajaux/mixin/batchLoadMixin Returns: A promise to be resolved with the loaded value after the widget has been loaded, or rejected if the widget fails to load the value. Type Promise loadAndModify(value [, params]) Loads in a new value, and sets the widget modified as well. Use this convenience method when you wish to load in a new value as if a user had done it, thereby triggering the necessary modify event handlers. Parameters: Name Type Argument Description value * params * &lt;optional&gt; Since: Niagara 4.12 Inherited From: module:bajaux/Widget#loadAndModify Returns: Type Promise.&lt;*&gt; properties() Return the Properties for a widget. Inherited From: module:bajaux/Widget#properties Returns: The Properties for a widget. Type module:bajaux/Properties read() Read the current representation of the widget. For instance, if the widget is made up from two text input boxes, this might resolve an object with two strings from those text boxes. Note the word \"representation\" - this function does not necessarily return the widget's actual value, but might assemble a different object, or array, or number, based on current user-entered values. read will not typically be overridden. doRead() should be overridden instead. Inherited From: module:bajaux/Widget#read See: module:bajaux/Widget#doRead Returns: A promise that will be resolved with a value read from the widget as specified by doRead, or rejected if the read fails. Type Promise requestFocus() Attempts to place the cursor focus on this editor. For instance, if showing a simple string editor in a dialog, it should request focus so that the user can simply begin typing without having to move the mouse over to it and click. Override this as necessary; by default, will place focus on the first input or textarea element in this editor's element. Since: Niagara 4.10 Inherited From: module:bajaux/Widget#requestFocus resolve(data [, resolveParams]) Resolve a value from some data. Please note, this will not load the value but will resolve some data that could then be loaded by the widget. By default, this will treat the data as an ORD so it can be resolved via BajaScript. Parameters: Name Type Argument Description data * | String | baja.Ord Specifies some data used to resolve a load value so load(value) can be called on the widget. resolveParams Object &lt;optional&gt; An Object Literal used for ORD resolution. This parameter is designed to be used internally by bajaux and shouldn't be used by developers. Inherited From: module:bajaux/Widget#resolve Returns: a promise to be resolved with the value resolved from the given data object Type Promise save( [params]) Saves any outstanding user-entered changes to this widget. Triggers a bajaux:save or bajaux:savefail event, as appropriate. In order to save the widget, its current value will be validated using validate(), then the validated value will be passed to doSave(). This method will not typically be overridden. doSave() should be overridden instead. Parameters: Name Type Argument Description params Object &lt;optional&gt; Additional parameters to be passed to doSave() Inherited From: module:bajaux/Widget#save See: module:bajaux/Widget#doSave module:bajaux/mixin/batchSaveMixin Returns: A promise to be resolved once the widget has been saved, or rejected if the save fails. Type Promise setCommandGroup(commandGroup) Set this widget's command group. Triggers a bajaux:changecommandgroup event. Parameters: Name Type Description commandGroup module:bajaux/commands/CommandGroup Inherited From: module:bajaux/Widget#setCommandGroup setEnabled(enabled) Set this widget's enabled state. Setting of the internal flag will be synchronous, so isEnabled will return the expected value immediately after calling this function. However, the actual work of updating the DOM cannot be performed until after the widget has finished initializing, so this method will return a promise. This method will not typically be overridden. doEnabled() should be overridden instead. Parameters: Name Type Description enabled Boolean the new enabled state Inherited From: module:bajaux/Widget#setEnabled See: module:bajaux/Widget#isEnabled Returns: A promise to resolve immediately if initialize has not yet been called, that will resolve once the work of initialize followed by doEnabled have both been completed. It will reject if initialize or doEnabled fail. Type Promise setModified(modified) Sets this widget's modified or \"dirty\" status, to indicate that the user has made changes to this widget that may need to be saved. The modification status will only be set if the widget is initialized and the widget is not loading a new value. Triggers bajaux:modify or bajaux:unmodify depending on the input value. Any arguments passed to this function after the first will be passed through to the triggered event. This method should not typically be overridden. doModified() should be overridden instead. Parameters: Name Type Description modified Boolean | * (a non-Boolean will be checked for truthiness) Inherited From: module:bajaux/Widget#setModified See: module:bajaux/Widget#doModified Example Say I have collection of nested widgets in my DOM element. Whenever one of those widgets is modified, I want to mark myself modified but also provide the originally modified editor. For example, when a Property Sheet is modified, I want to know which row caused the modification. var that = this; dom.on(events.MODIFY_EVENT, function (e, modifiedEd) { that.setModified(true, modifiedEd); return false; }); setReadonly(readonly) Set this widget's readonly state. Setting of the internal flag will be synchronous, so isReadonly will return the expected value immediately after calling this function. However, the actual work of updating the DOM cannot be performed until after the widget has finished initializing, so this method will return a promise. This method will not typically be overridden. doReadonly() should be overridden instead. Parameters: Name Type Description readonly Boolean the new readonly state. Inherited From: module:bajaux/Widget#setReadonly See: module:bajaux/Widget#isReadonly Returns: A promise to resolve immediately if initialize has not yet been called, that will resolve once the work of initialize followed by doReadonly have both been completed. It will reject if initialize or doReadonly fail. Type Promise toDescription() Access the widget's icon asynchronously. By default this will attempt to access the widget's icon from the originating Lexicon. The Lexicon key should be in the format of keyName.description. If an entry can't be found then a blank string will be used. Inherited From: module:bajaux/Widget#toDescription Returns: A promise to be resolved with the widget's description Type Promise toDisplayName() Access the widget's display name asynchronously. By default, this will attempt to access the widget's display name from the originating Lexicon. The Lexicon key should be in the format of keyName.displayName. If an entry can't be found then the Type's name will be used. Inherited From: module:bajaux/Widget#toDisplayName Returns: A promise to be resolved with the widget's display name Type Promise toIcon() Access the widget's icon asynchronously. By default, this will attempt to access the widget's description from the originating Lexicon. The Lexicon key should be in the format of keyName.icon. If an entry can't be found then a blank String will be returned. Inherited From: module:bajaux/Widget#toIcon Returns: A promise to be resolved with the widget's icon URI. Type Promise trigger() Trigger a widget event. By default, this fires a DOM event on the associated widget's DOM element. Inherited From: module:bajaux/Widget#trigger validate() Read the current value from the widget and validate it. Inherited From: module:bajaux/Widget#validate See: module:bajaux/Validators#validate Returns: A promise to be resolved with the value read from the widget and passed through all validators, or rejected if the value could not be read or validated. Type Promise validators() Return the widget's Validators. Inherited From: module:bajaux/Widget#validators See: module:bajaux/Validators Returns: Type module:bajaux/Validators value() Returns the widget's current loaded value. This the value that was last loaded via load(). To read a widget's current representation, reflecting any user-entered changes, call read(). If no value has been loaded yet, null is returned. Inherited From: module:bajaux/Widget#value See: module:bajaux/Widget#load module:bajaux/Widget#doLoad module:bajaux/Widget#read Returns: the loaded value, or null if a value hasn't been loaded yet. Type * | null × Search results Close "},"module-bajaux_util_CommandButtonGroup.html":{"id":"module-bajaux_util_CommandButtonGroup.html","title":"Module: bajaux/util/CommandButtonGroup","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/util/CommandButtonGroup new (require(\"bajaux/util/CommandButtonGroup\"))( [params]) A widget for displaying and invoking Commands in a CommandGroup. Extends: module:bajaux/Widget Parameters: Name Type Argument Description params Object &lt;optional&gt; Properties Name Type Argument Default Description properties.onDisabled String &lt;optional&gt; specify a policy for handling enabling/disabling of Commands. By default, the CommandButton itself will be responsible for updating its own DOM. A value of hide will cause the entire element to be hidden when the command is disabled. properties.toolbar Boolean &lt;optional&gt; false Set to true to cause the group to render a toolbar button group instead of a group of normal buttons. Methods applyParams( [params]) Can re-apply certain params that can also be passed to the constructor. Parameters: Name Type Argument Description params Object &lt;optional&gt; Properties Name Type Argument Default Description readonly Boolean &lt;optional&gt; enabled Boolean &lt;optional&gt; true must explicitly set to false to disable properties Object &lt;optional&gt; Since: Niagara 4.10 Inherited From: module:bajaux/Widget#applyParams Returns: promise to be resolved after any setEnabled/setReadonly work is done. Note that these functions will not be called if the value of enabled/readonly is not actually changing. Type Promise changed(name, value) Called whenever a Widget's Property is changed. If this Widget is not yet initialized, this is a no-op. This function should not typically be overridden. doChanged() should be overridden instead. Parameters: Name Type Description name String The name of the Property that's changed. value * The new Property value. Inherited From: module:bajaux/Widget#changed See: module:bajaux/Widget#properties Returns: Type Promise cleanupDom() Called to clean up the DOM when the widget is being destroyed. This method can be overridden if DOM clean up needs to be handled in a different way. Inherited From: module:bajaux/Widget#cleanupDom destroy( [params]) Indicates that a widget is no longer needed and is in the process of being removed. In this function, subclasses can deallocate any resources, event handlers, etc. that they may be holding. Delegates the actual work to doDestroy. This method will not typically be overridden. doDestroy() should be overridden instead. Triggers a bajaux:destroy or bajaux:destroyfail event, as appropriate. Please note, after doDestroy has resolved, the DOM will be emptied, all event handlers will be removed and the 'widget' data stored on the DOM element will be deleted. Parameters: Name Type Argument Description params object &lt;optional&gt; optional parameters to be passed to doDestroy Inherited From: module:bajaux/Widget#destroy See: module:bajaux/Widget#doDestroy Returns: A promise to be resolved when the widget has been destroyed Type Promise doChanged(name, value) Called by changed() when a Property is changed. This method is designed to be overridden by any subclasses. Parameters: Name Type Description name String The name of the Property that's changed. value * The new Property value. Inherited From: module:bajaux/Widget#doChanged Returns: Type Promise | * doDestroy() Removes the ux-btn-tb-group class and destroys all child buttons. Overrides: module:bajaux/Widget#doDestroy Returns: Type Promise doEnabled(enabled) Called when the widget is enabled/disabled. Parameters: Name Type Description enabled Boolean the new enabled state. Inherited From: module:bajaux/Widget#doEnabled Returns: An optional Promise that can be returned if the state change is asynchronous. Type * | Promise doInitialize(dom) Adds the ux-btn-tb-group class to the DOM to turn this into a toolbar button group. Parameters: Name Type Description dom JQuery Overrides: module:bajaux/Widget#doInitialize doLayout( [params]) Called when the layout of the Widget changes. This method is designed to be overridden. Parameters: Name Type Argument Description params * &lt;optional&gt; as of Niagara 4.10, any parameters passed to layout() will also be passed to doLayout(). Inherited From: module:bajaux/Widget#doLayout Returns: This method may optionally return a promise once the Widget has been laid out. Type * | Promise doLoad(group) Loads a CommandGroup, creating CommandButton widgets for each command in the group. Arms event handlers to hide elements for disabled buttons, as specified by the onDisabled parameter. Parameters: Name Type Description group module:bajaux/commands/CommandGroup Overrides: module:bajaux/Widget#doLoad Returns: promise to be resolved when the group is fully loaded and all elements initialized, or rejected if no CommandGroup given Type Promise doModified(modified) The actual implementation for setModified. This function should do any work necessary when the widget is set to a modified or \"dirty\" state - typically enabling a save button, arming a window.onbeforeunload handler, etc. Likewise, it should do the opposite when setting modified to false. Note that this is synchronous, as is setModified. Async work can be performed, but setModified will not wait for it. Parameters: Name Type Description modified Boolean Inherited From: module:bajaux/Widget#doModified See: module:bajaux/Widget#setModified doRead() Does the work of reading the widget's current representation. This might mean reading a series of text inputs and assembling their values into an array. It might mean instantiating a copy of the backing baja.Component and setting slot values on the new copy. It might mean simply returning the boolean value of a checkbox. If your widget is composed of pure text/HTML and is not actually backed by an external value, it might mean returning nothing. When saving a modified widget, the output of this function will be passed directly into this widget's validation process, so all your validation steps should be expecting to receive this. It will also be passed to doSave, so your doSave implementation should also expect this value. The default behavior of doRead is simply to use the widget's current value. Inherited From: module:bajaux/Widget#doRead See: module:bajaux/Widget#read Returns: The read value, or a promise to be resolved with the read value Type * | Promise doReadonly(readonly) Called when the widget is set to readonly or made writable. Parameters: Name Type Description readonly Boolean the new readonly state. Inherited From: module:bajaux/Widget#doReadonly Returns: An optional Promise that can be returned if the state change is asynchronous. Type * | Promise doSave(validValue [, params]) Performs the actual work of saving the widget. This function should be overridden by subclasses to save the value. The subclass function should save the value and then, optionally, return a promise to indicate that the work of saving the widget has completed. Parameters: Name Type Argument Description validValue * The value to be used for saving. This value will have been read from the widget using read() and validated using validate(). params Object &lt;optional&gt; Optional params object passed to save() Inherited From: module:bajaux/Widget#doSave Returns: An optional promise that's resolved once the widget has saved. Type * | Promise generateId() Generate a unique DOM ID. The ID will include the name of this widget's constructor just for tracing/debugging purposes. Inherited From: module:bajaux/Widget#generateId Returns: Type String getChildWidgets( [params]) Returns an array of child widgets living inside this editor's DOM. This method will specifically not return child widgets of child widgets for instance, if this widget has one child editor for a baja.Facets, you will only get a single widget back - it won't recurse down and give you all the tag editors, type editors etc. This is safer and easier than using $.find(), which recurses down, or carefully managing strings of $.children() calls. Pass in a jQuery instance to limit the child editor search to a particular set of elements. Otherwise, will search all child elements of this editor's DOM. If this editor has not initialized yet, you'll just get an empty array back. The returned array will have some utility functions attached that return promises. See example for details. Parameters: Name Type Argument Description params Object | jQuery &lt;optional&gt; Properties Name Type Argument Default Description dom JQuery &lt;optional&gt; this.jq().children() the dom element to search type function &lt;optional&gt; the widget type to search for - pass in the actual constructor, for instanceof checks Since: Niagara 4.10 Inherited From: module:bajaux/Widget#getChildWidgets Returns: an array of child widgets Type Array.&lt;module:bajaux/Widget&gt; Examples var kids = ed.getChildWidgets(); kids.setAllEnabled(false).then(function () {}); kids.setAllModified(false).then(function () {}); kids.setAllReadonly(false).then(function () {}); kids.readAll().then(function (valuesArray) {}); kids.validateAll().then(function (valuesArray) {}); kids.saveAll().then(function () {}); kids.destroyAll().then(function () {}); var stringEditors = ed.getChildWidgets({ type: StringEditor }); getCommandGroup() Return the widget's command group. Inherited From: module:bajaux/Widget#getCommandGroup Returns: Type module:bajaux/commands/CommandGroup getFormFactor() Return the widget's form-factor. The form-factor is normally passed in from the Widget's constructor. However, it can be set from a 'formFactor' property if required. A widget's form-factor typically doesn't change during a widget's life-cycle. Inherited From: module:bajaux/Widget#getFormFactor See: module:bajaux/Widget.formfactor Returns: The form-factor. Type String hasMixIn(mixin) Return true if the widget implements the specified MixIn. Parameters: Name Type Description mixin String the name of the mixin to test for. Inherited From: module:bajaux/Widget#hasMixIn Returns: Type Boolean initialize(dom [, params] [, layoutParams]) Initializes the DOM element to be bound to this Widget. In a nutshell, initialize defines the following contract: After initialize completes and resolves its Promise, the target element will be fully initialized, structured, and ready to load in a value. It will be accessible by calling this.jq(). If this is an editor, load may not be called until initialize's promise is resolved. Attempting to load a value prior to initialization will result in failure. This widget will be set as a jQuery data value on the initialized DOM element. It can be retrieved by calling Widget.in(element). initialize delegates the actual work of building the HTML structure (if any) to the doInitialize function. When subclassing Widget, you should not override initialize. doInitialize should be overridden. After initialize completes, an bajaux:initialize or bajaux:initializefail event will be triggered, as appropriate. initialize is a one-time operation. It will always reject if the widget has already been initialized once, or if it has been destroyed. Parameters: Name Type Argument Description dom JQuery The jQuery DOM element in which this widget should build its HTML (will be passed directly to doInitialize) params * &lt;optional&gt; optional parameters object to be passed through to doInitialize layoutParams * &lt;optional&gt; as of Niagara 4.10, optional parameters object to be passed through to layout Inherited From: module:bajaux/Widget#initialize See: module:bajaux/Widget#doInitialize Returns: A promise to be resolved once the widget has initialized Type Promise isDesignTime() Returns true if the Widget is in a graphic design editor. Inherited From: module:bajaux/Widget#isDesignTime Returns: Type Boolean isDestroyed() Return true if this Widget has already been destroyed. After destruction, initialize() will always reject: the widget cannot be reused. Inherited From: module:bajaux/Widget#isDestroyed Returns: Type Boolean isEnabled() Returns this widget's enabled state. Inherited From: module:bajaux/Widget#isEnabled See: module:bajaux/Widget#setEnabled Returns: Type Boolean isInitialized() Return true if this Widget is initialized. Inherited From: module:bajaux/Widget#isInitialized Returns: Type Boolean isLoading() Check if this widget is currently in the process of loading. This will return true immediately after load is called, and return false after the load promise resolves. Inherited From: module:bajaux/Widget#isLoading Returns: Type Boolean isModified() Returns this widget's modified state. Inherited From: module:bajaux/Widget#isModified Returns: Type Boolean isReadonly() Returns this widget's readonly state. Inherited From: module:bajaux/Widget#isReadonly See: module:bajaux/Widget#setReadonly Returns: Type Boolean jq() Returns the jQuery DOM element in which this widget has been initialized. If initialize() has not yet been called, then this will return null. Inherited From: module:bajaux/Widget#jq Returns: the DOM element in which this widget has been initialized, or null if not yet initialized. Type JQuery | null layout( [params]) A widget may need to do its own layout calculation. It might need to statically position elements, or show/hide them based on the shape of its container. This function gives a widget an opportunity to do that. It's called once the Widget has been initialized and once the form factor has changed. Also it may be called when the widget's container changes shape or size, or is shown/hidden. This method should not typically be overridden. Override doLayout() instead. Parameters: Name Type Argument Description params * &lt;optional&gt; as of Niagara 4.10, any parameters passed to layout() will also be passed to doLayout(). Inherited From: module:bajaux/Widget#layout Returns: A promise that's resolved once the layout has completed. Type Promise load(value [, params]) Updates the widget's HTML with the given value. An widget for editing a string, for example, might load the string into a text input. A view for editing a DynamicEnum might programmatically set a &lt;select&gt; dropdown's value. load() may not be called until initialize() has completed its work. If initialize() is not finished, load() will reject. After load() completes its work, the value loaded will be accessible via this.value(). load() delegates the work of loading the HTML values to doLoad(). Subclasses will typically not override load, but more commonly will override doLoad. After load() completes, a bajaux:load or bajaux:loadfail event will be triggered, as appropriate. While this method is performing its work, this.isLoading() will return true. Parameters: Name Type Argument Description value * The value to be loaded params Object &lt;optional&gt; additional parameters to be passed to doLoad() Inherited From: module:bajaux/Widget#load See: module:bajaux/Widget#doLoad module:bajaux/Widget#value module:bajaux/mixin/batchLoadMixin Returns: A promise to be resolved with the loaded value after the widget has been loaded, or rejected if the widget fails to load the value. Type Promise loadAndModify(value [, params]) Loads in a new value, and sets the widget modified as well. Use this convenience method when you wish to load in a new value as if a user had done it, thereby triggering the necessary modify event handlers. Parameters: Name Type Argument Description value * params * &lt;optional&gt; Since: Niagara 4.12 Inherited From: module:bajaux/Widget#loadAndModify Returns: Type Promise.&lt;*&gt; properties() Return the Properties for a widget. Inherited From: module:bajaux/Widget#properties Returns: The Properties for a widget. Type module:bajaux/Properties read() Read the current representation of the widget. For instance, if the widget is made up from two text input boxes, this might resolve an object with two strings from those text boxes. Note the word \"representation\" - this function does not necessarily return the widget's actual value, but might assemble a different object, or array, or number, based on current user-entered values. read will not typically be overridden. doRead() should be overridden instead. Inherited From: module:bajaux/Widget#read See: module:bajaux/Widget#doRead Returns: A promise that will be resolved with a value read from the widget as specified by doRead, or rejected if the read fails. Type Promise requestFocus() Attempts to place the cursor focus on this editor. For instance, if showing a simple string editor in a dialog, it should request focus so that the user can simply begin typing without having to move the mouse over to it and click. Override this as necessary; by default, will place focus on the first input or textarea element in this editor's element. Since: Niagara 4.10 Inherited From: module:bajaux/Widget#requestFocus resolve(data [, resolveParams]) Resolve a value from some data. Please note, this will not load the value but will resolve some data that could then be loaded by the widget. By default, this will treat the data as an ORD so it can be resolved via BajaScript. Parameters: Name Type Argument Description data * | String | baja.Ord Specifies some data used to resolve a load value so load(value) can be called on the widget. resolveParams Object &lt;optional&gt; An Object Literal used for ORD resolution. This parameter is designed to be used internally by bajaux and shouldn't be used by developers. Inherited From: module:bajaux/Widget#resolve Returns: a promise to be resolved with the value resolved from the given data object Type Promise save( [params]) Saves any outstanding user-entered changes to this widget. Triggers a bajaux:save or bajaux:savefail event, as appropriate. In order to save the widget, its current value will be validated using validate(), then the validated value will be passed to doSave(). This method will not typically be overridden. doSave() should be overridden instead. Parameters: Name Type Argument Description params Object &lt;optional&gt; Additional parameters to be passed to doSave() Inherited From: module:bajaux/Widget#save See: module:bajaux/Widget#doSave module:bajaux/mixin/batchSaveMixin Returns: A promise to be resolved once the widget has been saved, or rejected if the save fails. Type Promise setCommandGroup(commandGroup) Set this widget's command group. Triggers a bajaux:changecommandgroup event. Parameters: Name Type Description commandGroup module:bajaux/commands/CommandGroup Inherited From: module:bajaux/Widget#setCommandGroup setEnabled(enabled) Set this widget's enabled state. Setting of the internal flag will be synchronous, so isEnabled will return the expected value immediately after calling this function. However, the actual work of updating the DOM cannot be performed until after the widget has finished initializing, so this method will return a promise. This method will not typically be overridden. doEnabled() should be overridden instead. Parameters: Name Type Description enabled Boolean the new enabled state Inherited From: module:bajaux/Widget#setEnabled See: module:bajaux/Widget#isEnabled Returns: A promise to resolve immediately if initialize has not yet been called, that will resolve once the work of initialize followed by doEnabled have both been completed. It will reject if initialize or doEnabled fail. Type Promise setModified(modified) Sets this widget's modified or \"dirty\" status, to indicate that the user has made changes to this widget that may need to be saved. The modification status will only be set if the widget is initialized and the widget is not loading a new value. Triggers bajaux:modify or bajaux:unmodify depending on the input value. Any arguments passed to this function after the first will be passed through to the triggered event. This method should not typically be overridden. doModified() should be overridden instead. Parameters: Name Type Description modified Boolean | * (a non-Boolean will be checked for truthiness) Inherited From: module:bajaux/Widget#setModified See: module:bajaux/Widget#doModified Example Say I have collection of nested widgets in my DOM element. Whenever one of those widgets is modified, I want to mark myself modified but also provide the originally modified editor. For example, when a Property Sheet is modified, I want to know which row caused the modification. var that = this; dom.on(events.MODIFY_EVENT, function (e, modifiedEd) { that.setModified(true, modifiedEd); return false; }); setReadonly(readonly) Set this widget's readonly state. Setting of the internal flag will be synchronous, so isReadonly will return the expected value immediately after calling this function. However, the actual work of updating the DOM cannot be performed until after the widget has finished initializing, so this method will return a promise. This method will not typically be overridden. doReadonly() should be overridden instead. Parameters: Name Type Description readonly Boolean the new readonly state. Inherited From: module:bajaux/Widget#setReadonly See: module:bajaux/Widget#isReadonly Returns: A promise to resolve immediately if initialize has not yet been called, that will resolve once the work of initialize followed by doReadonly have both been completed. It will reject if initialize or doReadonly fail. Type Promise toDescription() Access the widget's icon asynchronously. By default this will attempt to access the widget's icon from the originating Lexicon. The Lexicon key should be in the format of keyName.description. If an entry can't be found then a blank string will be used. Inherited From: module:bajaux/Widget#toDescription Returns: A promise to be resolved with the widget's description Type Promise toDisplayName() Access the widget's display name asynchronously. By default, this will attempt to access the widget's display name from the originating Lexicon. The Lexicon key should be in the format of keyName.displayName. If an entry can't be found then the Type's name will be used. Inherited From: module:bajaux/Widget#toDisplayName Returns: A promise to be resolved with the widget's display name Type Promise toIcon() Access the widget's icon asynchronously. By default, this will attempt to access the widget's description from the originating Lexicon. The Lexicon key should be in the format of keyName.icon. If an entry can't be found then a blank String will be returned. Inherited From: module:bajaux/Widget#toIcon Returns: A promise to be resolved with the widget's icon URI. Type Promise trigger() Trigger a widget event. By default, this fires a DOM event on the associated widget's DOM element. Inherited From: module:bajaux/Widget#trigger validate() Read the current value from the widget and validate it. Inherited From: module:bajaux/Widget#validate See: module:bajaux/Validators#validate Returns: A promise to be resolved with the value read from the widget and passed through all validators, or rejected if the value could not be read or validated. Type Promise validators() Return the widget's Validators. Inherited From: module:bajaux/Widget#validators See: module:bajaux/Validators Returns: Type module:bajaux/Validators value() Returns the widget's current loaded value. This the value that was last loaded via load(). To read a widget's current representation, reflecting any user-entered changes, call read(). If no value has been loaded yet, null is returned. Inherited From: module:bajaux/Widget#value See: module:bajaux/Widget#load module:bajaux/Widget#doLoad module:bajaux/Widget#read Returns: the loaded value, or null if a value hasn't been loaded yet. Type * | null × Search results Close "},"module-bajaux_util_SaveCommand.html":{"id":"module-bajaux_util_SaveCommand.html","title":"Module: bajaux/util/SaveCommand","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/util/SaveCommand A Command for saving a Widget. new (require(\"bajaux/util/SaveCommand\"))() A Command for saving a Widget. Extends: module:bajaux/commands/Command Methods defaultNotifyUser(err [, params]) Provides a default way of notifying the user about a Command invocation failure. Shows a dialog with details about the error. You might override this at runtime with your own error dialog handler. Parameters: Name Type Argument Description err Error | * params object &lt;optional&gt; Properties Name Type Argument Description messageSummary string &lt;optional&gt; any additional information you would like to include in the error dialog Since: Niagara 4.12 Inherited From: module:bajaux/commands/Command#defaultNotifyUser Returns: Type Promise getAccelerator() Return the accelerator for the Command or null if nothing is defined. Inherited From: module:bajaux/commands/Command#getAccelerator See: module:bajaux/commands/Command#setAccelerator Returns: The accelerator or null if nothing is defined. Type Object getDescriptionFormat() Get the unformatted description of the command. Inherited From: module:bajaux/commands/Command#getDescriptionFormat Returns: Type String getDisplayNameFormat() Return the format display name of the command. Inherited From: module:bajaux/commands/Command#getDisplayNameFormat Returns: Type String getFlags() Get this command's flags. Inherited From: module:bajaux/commands/Command#getFlags Returns: Type Number getFunction() Return the raw function associated with this command. Inherited From: module:bajaux/commands/Command#getFunction Returns: Type function getIcon() Return the Command's icon URI Inherited From: module:bajaux/commands/Command#getIcon Returns: Type String getId() Return a unique numerical id for the Command. This is id unique to every Command object created. Inherited From: module:bajaux/commands/Command#getId hasFlags(flags) Check to see if this command's flags match any of the bits of the input flags. Parameters: Name Type Description flags Number The flags to check against Inherited From: module:bajaux/commands/Command#hasFlags Returns: Type Boolean invoke() Saves the Widget. Overrides: module:bajaux/commands/Command#invoke Returns: Type Promise invokeFromEvent(e) If your Command optionally implements this function, then CommandButton will call it on click instead of simply calling invoke. Use this in case your Command needs to respond differently based on where on the screen the user is pointing. Parameters: Name Type Description e JQuery.Event the DOM event triggered by the user's request to invoke this function Since: Niagara 4.11 Inherited From: module:bajaux/commands/Command#invokeFromEvent Returns: Type Promise | * isCommand() Always returns true. Inherited From: module:bajaux/commands/Command#isCommand isEnabled() Gets this command's enabled status. Inherited From: module:bajaux/commands/Command#isEnabled Returns: Type Boolean isLoading() Return true if the Command is still loading. Inherited From: module:bajaux/commands/Command#isLoading Returns: true if still loading. Type Boolean isToggleCommand() Always returns false. Inherited From: module:bajaux/commands/Command#isToggleCommand isUndoable() Since: Niagara 4.11 Inherited From: module:bajaux/commands/Command#isUndoable Returns: true if this command is undoable Type boolean jq() Overrides: module:bajaux/commands/Command#jq See: module:/bajaux/commands/Command loading() Return the loading promise for the Command. The returned promise will be resolved once the Command has finished loading. Inherited From: module:bajaux/commands/Command#loading Returns: The promise used for loading a Command. Type Promise merge(cmd) Attempt to merge this command with another command, and return a new Command that does both tasks. If the two commands are mutually incompatible, return a falsy value. Parameters: Name Type Description cmd module:bajaux/commands/Command Inherited From: module:bajaux/commands/Command#merge Returns: Type module:bajaux/commands/Command Example Here is an example to show the basic concept. Commands that simply add two numbers together can easily be merged together thanks to the associative property. var AddCommand = function AddCommand(inc) { this.$inc = inc; Command.call(this, { displayName: 'Add ' + inc + ' to the given number', func: function (num) { return num + inc; } }); }; AddCommand.prototype = Object.create(Command.prototype); AddCommand.prototype.merge = function (cmd) { if (cmd instanceof AddCommand) { return new AddCommand(this.$inc + cmd.$inc); } }; var addOneCommand = new AddCommand(1), addFiveCommand = new AddCommand(5), addSixCommand = addOneCommand.merge(addFiveCommand); addSixCommand.invoke(10) .then(function (result) { console.log('is 16? ', result === 16); }); off( [event] [, handler]) Unregister a function callback handler for the specified event. Parameters: Name Type Argument Description event String &lt;optional&gt; The name of the event to unregister. If name isn't specified, all events for the Command will be unregistered. handler function &lt;optional&gt; The function to unregister. If not specified, all handlers for the event will be unregistered. Inherited From: module:bajaux/commands/Command#off on(event, handler) Register a function callback handler for the specified event. Parameters: Name Type Description event String The event id to register the function for. handler function The event handler to be called when the event is fired. Inherited From: module:bajaux/commands/Command#on setAccelerator(acc) Set the accelerator information for the Command. Parameters: Name Type Description acc Object | String | Number | null | undefined The accelerator keyboard information. This can be a keyCode number, a character (i.e. 'a') or an Object that contains the accelerator information. If no accelerator should be used the null/undefined should be specified. Properties Name Type Argument Description keyCode String | Number The key code of the accelerator. This can be a character code number or a character (i.e. 'a'). ctrl Boolean &lt;optional&gt; true if the control key needs to be pressed. shift Boolean &lt;optional&gt; true if the shift key needs to be pressed. alt Boolean &lt;optional&gt; true if the alt key needs to be pressed. meta Boolean &lt;optional&gt; true if a meta key needs to be pressed. Inherited From: module:bajaux/commands/Command#setAccelerator See: module:bajaux/commands/Command#getAccelerator setDescriptionFormat(description) Set the description format of the command. Triggers a bajaux:changecommand event. Parameters: Name Type Description description String the command description - supports baja Format syntax Inherited From: module:bajaux/commands/Command#setDescriptionFormat setDisplayNameFormat(displayName) Set the display name format of the command. Triggers a bajaux:changecommand event. Parameters: Name Type Description displayName String display name - supports baja Format syntax Inherited From: module:bajaux/commands/Command#setDisplayNameFormat setEnabled(enabled) Sets this command's enabled status. Triggers a bajaux:changecommand event. Parameters: Name Type Description enabled Boolean Inherited From: module:bajaux/commands/Command#setEnabled setFlags(flags) Set this command's flags. Parameters: Name Type Description flags Number Inherited From: module:bajaux/commands/Command#setFlags setFunction(func) Set the Command's function handler. Parameters: Name Type Description func function The new function handler for the command. Inherited From: module:bajaux/commands/Command#setFunction setIcon(icon) Sets the icon for this Command. Triggers a bajaux:changecommand event. Parameters: Name Type Description icon String The Command's icon (either a URI or a module:// ORD string) Inherited From: module:bajaux/commands/Command#setIcon toDescription() Access the Command's description. In order to access the description, a promise will be returned that will be resolved once the command has been loaded and the description has been found. Inherited From: module:bajaux/commands/Command#toDescription Returns: Promise to be resolved with the description Type Promise toDisplayName() Access the Command's display name. In order to access the display name, a promise will be returned that will be resolved once the command has been loaded and the display name has been found. Inherited From: module:bajaux/commands/Command#toDisplayName Returns: Promise to be resolved with the display name Type Promise trigger(name) Triggers an event from this Command. Parameters: Name Type Description name String Inherited From: module:bajaux/commands/Command#trigger visit(func) Visit this Command with the specified function. Parameters: Name Type Description func function Will be invoked with this Command passed in as an argument. Inherited From: module:bajaux/commands/Command#visit × Search results Close "},"module-bajaux_Validators.html":{"id":"module-bajaux_Validators.html","title":"Module: bajaux/Validators","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/Validators bajaux Validators are used to validate data. new (require(\"bajaux/Validators\"))(eventHandler) Manages a collection of Validator functions. Parameters: Name Type Description eventHandler The associated eventHandler that has a 'trigger' method. Requires module:Promise module:bajaux/events Methods add(validator) Add a Validator function. This is used to validate a read value before it can be saved. When a Validator function is invoked, the first argument will be the value. If the function throws or returns a promise that rejects, the value is considered to be invalid. If the function returns anything else (including a promise that resolves), the value is considered to be valid. Please note, when saving a modified widget, the value will be read from the widget, then validated and finally saved. Therefore, the data passed into the validator for validation will the read data. Parameters: Name Type Description validator function See: module:bajaux/Widget#read module:bajaux/Widget#save module:bajaux/Validators#validate module:bajaux/Validators#remove module:bajaux/Validators#get Returns: Type module:bajaux/Validators Example validators.add(function (value) { if (!isAcceptable(value)) { return Promise.reject(new Error('value not acceptable')); } }); get() Return an array copy of the validators. See: module:bajaux/Validators#validate module:bajaux/Validators#add module:bajaux/Validators#remove Returns: array of Validator functions. Type Array remove(validator) Remove a Validator function. Parameters: Name Type Description validator function See: module:bajaux/Validators#validate module:bajaux/Validators#remove module:bajaux/Validators#get Returns: Type module:bajaux/Validators validate(readValue) Called to validate some data. The read value run against each registered Validator. To validate a widget, please call module:bajaux/Widget#validate instead. This method will extract the read value and then call this method. This method should not be overridden. Instead Validators functions should be added using module:bajaux/Validators#add. When saving a modified widget, the widget will be read, validated and then saved. The read data is the current representation of the widget and is passed to the validators. The read data will not be the same as the loaded current value of the widget that is used in the save process. Parameters: Name Type Description readValue The read value that needs to be validated. See: module:bajaux/Widget#read module:bajaux/Widget#save module:bajaux/Validators#add module:bajaux/Validators#remove module:bajaux/Validators#get Returns: A promise to be resolved with the validated value Type Promise × Search results Close "},"module-bajaux_Widget.html":{"id":"module-bajaux_Widget.html","title":"Module: bajaux/Widget","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Module: bajaux/Widget The base Widget class, used for loading Web based Niagara Widgets. new (require(\"bajaux/Widget\"))( [params]) A Widget contains the most basic mechanisms necessary for displaying user interface upon a DOM element. It should not be instantiated directly, but rather subclassed with the specific functionality you need. At a bare minimum, either the doInitialize or doLoad function will need to be implemented by your subclass. Parameters: Name Type Argument Description params Object &lt;optional&gt; a parameters object. Properties Name Type Argument Default Description properties Object &lt;optional&gt; properties to add to this editor's underlying bajaux/Properties instance. enabled Boolean &lt;optional&gt; false to disable this editor readonly Boolean &lt;optional&gt; true to readonly this editor formFactor String &lt;optional&gt; form factor this editor should use (c.f. Widget.formfactor) keyName String &lt;optional&gt; the key name that bajaux should use to look up lexicon entries for this editor moduleName String &lt;optional&gt; 'bajaux' the module name that bajaux should use to look up lexicon entries for this editor data Object &lt;optional&gt; optional additional configuration data that may be used on a per-widget basis. This will often be used in conjunction with fe. params Object &lt;optional&gt; in conjunction with defaults, allows a Widget subclass to accept parameters passed to the constructor as well as an initial set of default values. See example. defaults Object &lt;optional&gt; in conjunction with params, allows a Widget subclass to define an initial set of default values. See example. Examples Construct a Widget, defining its initial configuration with an object literal. const w = new Widget({ readonly: true, properties: { foo: 'bar' }, formFactor: 'compact' }); Define a Widget subclass with an initial set of default values, while still allowing parameters to be passed to the constructor. This is the preferred method of calling a Widget super-constructor. class MyButton extends Widget { constructor(params) { super({ params, defaults: { moduleName: 'myModule', keyName: 'MyButton', properties: { borderRadius: 15, padding: 10 } } }); } } const button = new MyButton({ properties: { padding: 20 } }); button.properties().getValue('borderRadius'); // 15, from defaults button.properties().getValue('padding'); // 20, from constructor params and defaults will be correctly processed even when subclassing. class AWidget extends Widget { constructor(params) { super({ params, defaults: { properties: { name: 'A' } } }); } } class BWidget extends AWidget { constructor(params) { // defaults will be merged in with superclass defaults. super({ params, defaults: { properties: { name: 'B' } } }); } } new BWidget().properties().getValue('name'); // 'B' new BWidget({ properties: { name: 'Bee' } }).properties().getValue('name'); // 'Bee' Requires module:lex module:jquery module:bajaux/events module:bajaux/Properties module:bajaux/Validators Members &lt;static&gt; css Widget CSS class names. Properties: Name Type Description initialized String bajaux-initialized: applied after the widget is initialized. disabled String bajaux-disabled: applied when the widget is disabled. designTime String bajaux-design-time: Applied to an ancestor DOM element to indicate the widget is running in a graphic design editor. You can use this to style your widget differently between design time and production. readonly String bajaux-readonly: Applied when the widget is marked readonly.b max String bajaux-max: A large Widget (e.g. a View). compact String bajaux-compact: A medium sized Widget (e.g. a dashboard Widget). mini String bajaux-mini: A small Widget (e.g. a Field Editor). &lt;static&gt; formFactor Widget form-factors. Properties: Name Type Description max String A large Widget (e.g. a View). compact String A medium sized Widget (e.g. a dashboard Widget). mini String A small Widget (e.g. a Field Editor). See: module:bajaux/Widget#getFormFactor Methods &lt;static&gt; in(el) Find the Widget instance living in this DOM element. Parameters: Name Type Description el JQuery | HTMLElement Since: Niagara 4.6 Returns: Type module:bajaux/Widget | undefined applyParams( [params]) Can re-apply certain params that can also be passed to the constructor. Parameters: Name Type Argument Description params Object &lt;optional&gt; Properties Name Type Argument Default Description readonly Boolean &lt;optional&gt; enabled Boolean &lt;optional&gt; true must explicitly set to false to disable properties Object &lt;optional&gt; Since: Niagara 4.10 Returns: promise to be resolved after any setEnabled/setReadonly work is done. Note that these functions will not be called if the value of enabled/readonly is not actually changing. Type Promise changed(name, value) Called whenever a Widget's Property is changed. If this Widget is not yet initialized, this is a no-op. This function should not typically be overridden. doChanged() should be overridden instead. Parameters: Name Type Description name String The name of the Property that's changed. value * The new Property value. See: module:bajaux/Widget#properties Returns: Type Promise cleanupDom() Called to clean up the DOM when the widget is being destroyed. This method can be overridden if DOM clean up needs to be handled in a different way. destroy( [params]) Indicates that a widget is no longer needed and is in the process of being removed. In this function, subclasses can deallocate any resources, event handlers, etc. that they may be holding. Delegates the actual work to doDestroy. This method will not typically be overridden. doDestroy() should be overridden instead. Triggers a bajaux:destroy or bajaux:destroyfail event, as appropriate. Please note, after doDestroy has resolved, the DOM will be emptied, all event handlers will be removed and the 'widget' data stored on the DOM element will be deleted. Parameters: Name Type Argument Description params object &lt;optional&gt; optional parameters to be passed to doDestroy See: module:bajaux/Widget#doDestroy Returns: A promise to be resolved when the widget has been destroyed Type Promise doChanged(name, value) Called by changed() when a Property is changed. This method is designed to be overridden by any subclasses. Parameters: Name Type Description name String The name of the Property that's changed. value * The new Property value. Returns: Type Promise | * doDestroy( [params]) Called by destroy so this widget has a chance to clean up after itself and release any resources it is holding. Notably, any jQuery event handlers registered on child elements of the widget's DOM element should be unregistered here. Also, you may want to remove any CSS classes you've added to the widget's DOM element. Parameters: Name Type Argument Description params Object &lt;optional&gt; Optional params object passed to destroy() See: module:bajaux/Widget#destroy Returns: An optional promise that's resolved once the widget has been destroyed. Type * | Promise doEnabled(enabled) Called when the widget is enabled/disabled. Parameters: Name Type Description enabled Boolean the new enabled state. Returns: An optional Promise that can be returned if the state change is asynchronous. Type * | Promise doInitialize(element [, params]) Performs the actual work of initializing the DOM element in which this widget will live. This function should be overridden by subclasses - the subclass function should append elements to element as necessary and then optionally return a promise. Most commonly, this will involve building up the HTML structure necessary to load in a value. If this widget will display/edit a String, for example, doInitialize might append a text input element to the target element. A DynamicEnum might include a &lt;select&gt; dropdown. In some cases, no initialization may be required at all. This might be the case if you are binding the widget to an HTML element that is already pre-populated with all the necessary structure to load a value, or maybe doLoad will empty out the element completely and rebuild it from scratch every time a new value is loaded. In this case, you do not need to override this method. (However, a widget that overrides neither doInitialize nor doLoad will not be very useful!) Tip: the promises returned by setEnabled and setReadonly can only ever resolve after initialize itself resolves. So return this.setEnabled(enabled) or return this.setReadonly(readonly) from doInitialize will result in a deadlock that will never resolve. They may be called, but not returned. Parameters: Name Type Argument Description element JQuery The element in which this Widget should build its HTML. params Object &lt;optional&gt; Optional params object passed into initialize(). See: module:bajaux/Widget#initialize Returns: An optional promise to be resolved once the Widget has initialized. Type * | Promise doLayout( [params]) Called when the layout of the Widget changes. This method is designed to be overridden. Parameters: Name Type Argument Description params * &lt;optional&gt; as of Niagara 4.10, any parameters passed to layout() will also be passed to doLayout(). Returns: This method may optionally return a promise once the Widget has been laid out. Type * | Promise doLoad(value [, params]) Performs the actual work of populating the widget's HTML to reflect the input value. This function should be overridden by subclasses. The subclass function should manipulate the DOM jq() and, optionally,return a promise to indicate that the work of loading the value has completed. Parameters: Name Type Argument Description value * The value to be loaded. params Object &lt;optional&gt; Optional params object passed to load() See: module:bajaux/Widget#load module:bajaux/Widget#value Returns: An optional promise that's resolved once the widget has loaded. Type Promise doModified(modified) The actual implementation for setModified. This function should do any work necessary when the widget is set to a modified or \"dirty\" state - typically enabling a save button, arming a window.onbeforeunload handler, etc. Likewise, it should do the opposite when setting modified to false. Note that this is synchronous, as is setModified. Async work can be performed, but setModified will not wait for it. Parameters: Name Type Description modified Boolean See: module:bajaux/Widget#setModified doRead() Does the work of reading the widget's current representation. This might mean reading a series of text inputs and assembling their values into an array. It might mean instantiating a copy of the backing baja.Component and setting slot values on the new copy. It might mean simply returning the boolean value of a checkbox. If your widget is composed of pure text/HTML and is not actually backed by an external value, it might mean returning nothing. When saving a modified widget, the output of this function will be passed directly into this widget's validation process, so all your validation steps should be expecting to receive this. It will also be passed to doSave, so your doSave implementation should also expect this value. The default behavior of doRead is simply to use the widget's current value. See: module:bajaux/Widget#read Returns: The read value, or a promise to be resolved with the read value Type * | Promise doReadonly(readonly) Called when the widget is set to readonly or made writable. Parameters: Name Type Description readonly Boolean the new readonly state. Returns: An optional Promise that can be returned if the state change is asynchronous. Type * | Promise doSave(validValue [, params]) Performs the actual work of saving the widget. This function should be overridden by subclasses to save the value. The subclass function should save the value and then, optionally, return a promise to indicate that the work of saving the widget has completed. Parameters: Name Type Argument Description validValue * The value to be used for saving. This value will have been read from the widget using read() and validated using validate(). params Object &lt;optional&gt; Optional params object passed to save() Returns: An optional promise that's resolved once the widget has saved. Type * | Promise generateId() Generate a unique DOM ID. The ID will include the name of this widget's constructor just for tracing/debugging purposes. Returns: Type String getChildWidgets( [params]) Returns an array of child widgets living inside this editor's DOM. This method will specifically not return child widgets of child widgets for instance, if this widget has one child editor for a baja.Facets, you will only get a single widget back - it won't recurse down and give you all the tag editors, type editors etc. This is safer and easier than using $.find(), which recurses down, or carefully managing strings of $.children() calls. Pass in a jQuery instance to limit the child editor search to a particular set of elements. Otherwise, will search all child elements of this editor's DOM. If this editor has not initialized yet, you'll just get an empty array back. The returned array will have some utility functions attached that return promises. See example for details. Parameters: Name Type Argument Description params Object | jQuery &lt;optional&gt; Properties Name Type Argument Default Description dom JQuery &lt;optional&gt; this.jq().children() the dom element to search type function &lt;optional&gt; the widget type to search for - pass in the actual constructor, for instanceof checks Since: Niagara 4.10 Returns: an array of child widgets Type Array.&lt;module:bajaux/Widget&gt; Examples var kids = ed.getChildWidgets(); kids.setAllEnabled(false).then(function () {}); kids.setAllModified(false).then(function () {}); kids.setAllReadonly(false).then(function () {}); kids.readAll().then(function (valuesArray) {}); kids.validateAll().then(function (valuesArray) {}); kids.saveAll().then(function () {}); kids.destroyAll().then(function () {}); var stringEditors = ed.getChildWidgets({ type: StringEditor }); getCommandGroup() Return the widget's command group. Returns: Type module:bajaux/commands/CommandGroup getFormFactor() Return the widget's form-factor. The form-factor is normally passed in from the Widget's constructor. However, it can be set from a 'formFactor' property if required. A widget's form-factor typically doesn't change during a widget's life-cycle. See: module:bajaux/Widget.formfactor Returns: The form-factor. Type String hasMixIn(mixin) Return true if the widget implements the specified MixIn. Parameters: Name Type Description mixin String the name of the mixin to test for. Returns: Type Boolean initialize(dom [, params] [, layoutParams]) Initializes the DOM element to be bound to this Widget. In a nutshell, initialize defines the following contract: After initialize completes and resolves its Promise, the target element will be fully initialized, structured, and ready to load in a value. It will be accessible by calling this.jq(). If this is an editor, load may not be called until initialize's promise is resolved. Attempting to load a value prior to initialization will result in failure. This widget will be set as a jQuery data value on the initialized DOM element. It can be retrieved by calling Widget.in(element). initialize delegates the actual work of building the HTML structure (if any) to the doInitialize function. When subclassing Widget, you should not override initialize. doInitialize should be overridden. After initialize completes, an bajaux:initialize or bajaux:initializefail event will be triggered, as appropriate. initialize is a one-time operation. It will always reject if the widget has already been initialized once, or if it has been destroyed. Parameters: Name Type Argument Description dom JQuery The jQuery DOM element in which this widget should build its HTML (will be passed directly to doInitialize) params * &lt;optional&gt; optional parameters object to be passed through to doInitialize layoutParams * &lt;optional&gt; as of Niagara 4.10, optional parameters object to be passed through to layout See: module:bajaux/Widget#doInitialize Returns: A promise to be resolved once the widget has initialized Type Promise isDesignTime() Returns true if the Widget is in a graphic design editor. Returns: Type Boolean isDestroyed() Return true if this Widget has already been destroyed. After destruction, initialize() will always reject: the widget cannot be reused. Returns: Type Boolean isEnabled() Returns this widget's enabled state. See: module:bajaux/Widget#setEnabled Returns: Type Boolean isInitialized() Return true if this Widget is initialized. Returns: Type Boolean isLoading() Check if this widget is currently in the process of loading. This will return true immediately after load is called, and return false after the load promise resolves. Returns: Type Boolean isModified() Returns this widget's modified state. Returns: Type Boolean isReadonly() Returns this widget's readonly state. See: module:bajaux/Widget#setReadonly Returns: Type Boolean jq() Returns the jQuery DOM element in which this widget has been initialized. If initialize() has not yet been called, then this will return null. Returns: the DOM element in which this widget has been initialized, or null if not yet initialized. Type JQuery | null layout( [params]) A widget may need to do its own layout calculation. It might need to statically position elements, or show/hide them based on the shape of its container. This function gives a widget an opportunity to do that. It's called once the Widget has been initialized and once the form factor has changed. Also it may be called when the widget's container changes shape or size, or is shown/hidden. This method should not typically be overridden. Override doLayout() instead. Parameters: Name Type Argument Description params * &lt;optional&gt; as of Niagara 4.10, any parameters passed to layout() will also be passed to doLayout(). Returns: A promise that's resolved once the layout has completed. Type Promise load(value [, params]) Updates the widget's HTML with the given value. An widget for editing a string, for example, might load the string into a text input. A view for editing a DynamicEnum might programmatically set a &lt;select&gt; dropdown's value. load() may not be called until initialize() has completed its work. If initialize() is not finished, load() will reject. After load() completes its work, the value loaded will be accessible via this.value(). load() delegates the work of loading the HTML values to doLoad(). Subclasses will typically not override load, but more commonly will override doLoad. After load() completes, a bajaux:load or bajaux:loadfail event will be triggered, as appropriate. While this method is performing its work, this.isLoading() will return true. Parameters: Name Type Argument Description value * The value to be loaded params Object &lt;optional&gt; additional parameters to be passed to doLoad() See: module:bajaux/Widget#doLoad module:bajaux/Widget#value module:bajaux/mixin/batchLoadMixin Returns: A promise to be resolved with the loaded value after the widget has been loaded, or rejected if the widget fails to load the value. Type Promise loadAndModify(value [, params]) Loads in a new value, and sets the widget modified as well. Use this convenience method when you wish to load in a new value as if a user had done it, thereby triggering the necessary modify event handlers. Parameters: Name Type Argument Description value * params * &lt;optional&gt; Since: Niagara 4.12 Returns: Type Promise.&lt;*&gt; properties() Return the Properties for a widget. Returns: The Properties for a widget. Type module:bajaux/Properties read() Read the current representation of the widget. For instance, if the widget is made up from two text input boxes, this might resolve an object with two strings from those text boxes. Note the word \"representation\" - this function does not necessarily return the widget's actual value, but might assemble a different object, or array, or number, based on current user-entered values. read will not typically be overridden. doRead() should be overridden instead. See: module:bajaux/Widget#doRead Returns: A promise that will be resolved with a value read from the widget as specified by doRead, or rejected if the read fails. Type Promise requestFocus() Attempts to place the cursor focus on this editor. For instance, if showing a simple string editor in a dialog, it should request focus so that the user can simply begin typing without having to move the mouse over to it and click. Override this as necessary; by default, will place focus on the first input or textarea element in this editor's element. Since: Niagara 4.10 resolve(data [, resolveParams]) Resolve a value from some data. Please note, this will not load the value but will resolve some data that could then be loaded by the widget. By default, this will treat the data as an ORD so it can be resolved via BajaScript. Parameters: Name Type Argument Description data * | String | baja.Ord Specifies some data used to resolve a load value so load(value) can be called on the widget. resolveParams Object &lt;optional&gt; An Object Literal used for ORD resolution. This parameter is designed to be used internally by bajaux and shouldn't be used by developers. Returns: a promise to be resolved with the value resolved from the given data object Type Promise save( [params]) Saves any outstanding user-entered changes to this widget. Triggers a bajaux:save or bajaux:savefail event, as appropriate. In order to save the widget, its current value will be validated using validate(), then the validated value will be passed to doSave(). This method will not typically be overridden. doSave() should be overridden instead. Parameters: Name Type Argument Description params Object &lt;optional&gt; Additional parameters to be passed to doSave() See: module:bajaux/Widget#doSave module:bajaux/mixin/batchSaveMixin Returns: A promise to be resolved once the widget has been saved, or rejected if the save fails. Type Promise setCommandGroup(commandGroup) Set this widget's command group. Triggers a bajaux:changecommandgroup event. Parameters: Name Type Description commandGroup module:bajaux/commands/CommandGroup setEnabled(enabled) Set this widget's enabled state. Setting of the internal flag will be synchronous, so isEnabled will return the expected value immediately after calling this function. However, the actual work of updating the DOM cannot be performed until after the widget has finished initializing, so this method will return a promise. This method will not typically be overridden. doEnabled() should be overridden instead. Parameters: Name Type Description enabled Boolean the new enabled state See: module:bajaux/Widget#isEnabled Returns: A promise to resolve immediately if initialize has not yet been called, that will resolve once the work of initialize followed by doEnabled have both been completed. It will reject if initialize or doEnabled fail. Type Promise setModified(modified) Sets this widget's modified or \"dirty\" status, to indicate that the user has made changes to this widget that may need to be saved. The modification status will only be set if the widget is initialized and the widget is not loading a new value. Triggers bajaux:modify or bajaux:unmodify depending on the input value. Any arguments passed to this function after the first will be passed through to the triggered event. This method should not typically be overridden. doModified() should be overridden instead. Parameters: Name Type Description modified Boolean | * (a non-Boolean will be checked for truthiness) See: module:bajaux/Widget#doModified Example Say I have collection of nested widgets in my DOM element. Whenever one of those widgets is modified, I want to mark myself modified but also provide the originally modified editor. For example, when a Property Sheet is modified, I want to know which row caused the modification. var that = this; dom.on(events.MODIFY_EVENT, function (e, modifiedEd) { that.setModified(true, modifiedEd); return false; }); setReadonly(readonly) Set this widget's readonly state. Setting of the internal flag will be synchronous, so isReadonly will return the expected value immediately after calling this function. However, the actual work of updating the DOM cannot be performed until after the widget has finished initializing, so this method will return a promise. This method will not typically be overridden. doReadonly() should be overridden instead. Parameters: Name Type Description readonly Boolean the new readonly state. See: module:bajaux/Widget#isReadonly Returns: A promise to resolve immediately if initialize has not yet been called, that will resolve once the work of initialize followed by doReadonly have both been completed. It will reject if initialize or doReadonly fail. Type Promise toDescription() Access the widget's icon asynchronously. By default this will attempt to access the widget's icon from the originating Lexicon. The Lexicon key should be in the format of keyName.description. If an entry can't be found then a blank string will be used. Returns: A promise to be resolved with the widget's description Type Promise toDisplayName() Access the widget's display name asynchronously. By default, this will attempt to access the widget's display name from the originating Lexicon. The Lexicon key should be in the format of keyName.displayName. If an entry can't be found then the Type's name will be used. Returns: A promise to be resolved with the widget's display name Type Promise toIcon() Access the widget's icon asynchronously. By default, this will attempt to access the widget's description from the originating Lexicon. The Lexicon key should be in the format of keyName.icon. If an entry can't be found then a blank String will be returned. Returns: A promise to be resolved with the widget's icon URI. Type Promise trigger() Trigger a widget event. By default, this fires a DOM event on the associated widget's DOM element. validate() Read the current value from the widget and validate it. See: module:bajaux/Validators#validate Returns: A promise to be resolved with the value read from the widget and passed through all validators, or rejected if the value could not be read or validated. Type Promise validators() Return the widget's Validators. See: module:bajaux/Validators Returns: Type module:bajaux/Validators value() Returns the widget's current loaded value. This the value that was last loaded via load(). To read a widget's current representation, reflecting any user-entered changes, call read(). If no value has been loaded yet, null is returned. See: module:bajaux/Widget#load module:bajaux/Widget#doLoad module:bajaux/Widget#read Returns: the loaded value, or null if a value hasn't been loaded yet. Type * | null × Search results Close "},"tutorial-10-mfw-gettingStarted.html":{"id":"tutorial-10-mfw-gettingStarted.html","title":"Tutorial: Getting Started - MyFirstWidget","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Getting Started - MyFirstWidget In this first tutorial, we're going to create a new Niagara Module with a very simple Widget that will render seamlessly in both Workbench and the browser. Outline Our new Niagara Module will have the following directory structure. (When creating a bajaux module from scratch, please consider using grunt-init-niagara - it will walk you through creating all the boilerplate files and let you get right to coding.) +- myFirstModule +- myFirstModule-ux +- src | +- com | | +- companyname | | +- myFirstModule | | +- ux | | +- BMyFirstWidget.java | +- rc | +- MyFirstWidget.js +- myFirstModule-ux.gradle.kts All the code for a bajaux Widget goes into the -ux module subdirectory. (Workbench and Hx Views belong in -wb.) BMyFirstWidget.java: a Java class to register our JavaScript with the Niagara Framework. MyFirstWidget.js: the JavaScript bajaux Widget. myFirstModule-ux.gradle.kts: used to build the JAR file. MyFirstWidget.js Let's make our first attempt at implementing a Widget! This will be a very simple Widget that displays the value of a Ramp Component from the kitControl palette. /** * A module defining `MyFirstWidget`. * @module nmodule/myFirstModule/rc/MyFirstWidget */ define([ 'bajaux/mixin/subscriberMixIn', 'bajaux/Widget', 'underscore' ], function ( subscriberMixIn, Widget, _) { 'use strict'; /** * An editor for working with `kitControl:Ramp` instances. * * @class * @extends module:bajaux/Widget * @alias module:nmodule/myFirstModule/rc/MyFirstWidget */ return class MyFirstWidget extends Widget { constructor() { super(...arguments); subscriberMixIn(this); } /** * Describe how your `Widget` does its initial setup of the DOM. * * @param {JQuery} dom - The DOM element into which to initialize this `Widget` */ doInitialize(dom) { const initialValue = 'value goes here'; // when building HTML directly, remember to perform proper escaping to prevent XSS vulnerabilities. // _.escape() is only one of many ways to do this. Calling .text() instead of .html() on a // jQuery object is another. dom.html(`&lt;input type=\"text\" value=\"${ _.escape(initialValue) }\" &gt;`); } /** * Describe how your `Widget` loads in a value. * * Thanks to `subscriberMixIn`, we can subscribe to changes to the Ramp * component to ensure that the DOM is always kept up to date. * * @param {baja.Component} ramp - an instance of `kitControl:Ramp`. */ doLoad(ramp) { const update = () =&gt; { this.jq().children('input').val(ramp.getOut().getValueDisplay()); }; // Call update whenever a Property changes this.getSubscriber().attach('changed', update); // Call update for the first time. update(); } }; }); Even if you're new to JavaScript, this code shouldn't look too strange to you. It simply loads a Niagara value into a JavaScript Widget. The JavaScript code is wrapped in a define() function. This is how we write modular JavaScript code. The JavaScript defines a new MyFirstWidget constructor that extends from Widget. doInitialize injects some HTML when the Widget is created. The dom object being passed in is a DOM element embedded in a jQuery wrapper. doLoad updates the HTML when the ramp is passed into the Widget. The value is a subscribed BajaScript proxy Component of the Ramp that sits in the Station. It's already subscribed because the Widget uses a subscriberMixIn. The value will be automatically unsubscribed when the Widget is destroyed. The subscriberMixIn automatically adds the getSubscriber method to the Widget. This is used to attach a listener for changed events. Every time a property on the Ramp Component changes, the update method will be called to update the user interface. Note that we call it the first time ourselves - otherwise the user wouldn't see any data at all until a changed event came through. This looks like a pretty solid first attempt. But there's a potential problem in this implementation of doLoad(). Can you spot it? Remember: load() can be called multiple times, with different values each time. Take a second look... ...and you may find that the subscriber does not remove any previous event handlers! If you were to unload the current Ramp and load in a new one, like this: baja.Ord.make('station:|slot:/Ramp1').get() .then((ramp1) =&gt; myWidget.load(ramp1)) .then(() =&gt; baja.Ord.make('station:|slot:/Ramp2').get()) .then((ramp2) =&gt; myWidget.load(ramp2)); then when Ramp2 fired a changed event, the old event handler would still fire once (with the previous, wrong values) before firing a second time with the new values! Due to the nature of JavaScript closures, every time we load a Ramp, we pass a function with a permanent reference to that Ramp that will be fired whenever the subscriber receives a changed event. Subscriber gets a changed event from Ramp2 but it still fires the event handler we loaded from Ramp1! What can we do? One option is to keep a reference to the event handler when we add it, and pass it to getSubscriber().detach() before attaching the new handler. This would work, but it would be a bit clunky. I recommend this approach. (I'll also replace the boilerplate JSDoc from the first example for this final version.) /** * A module defining `MyFirstWidget`. * @module nmodule/myFirstModule/rc/MyFirstWidget */ define([ 'bajaux/mixin/subscriberMixIn', 'bajaux/Widget', 'underscore' ], function ( subscriberMixIn, Widget, _) { 'use strict'; /** * An editor for working with `kitControl:Ramp` instances. * * @class * @extends module:bajaux/Widget * @alias module:nmodule/myFirstModule/rc/MyFirstWidget */ return class MyFirstWidget extends Widget { constructor() { super(...arguments); subscriberMixIn(this); } /** * Creates a text input to show the current value and subscribes for future updates. * @param {JQuery} dom */ doInitialize(dom) { const initialValue = 'value goes here'; dom.html(`&lt;input type=\"text\" value=\"${ _.escape(initialValue) }\" &gt;`); this.getSubscriber().attach('changed', () =&gt; this.$updateDom(this.value())); } /** * Shows the Ramp's current value in the DOM. * @param {baja.Component} ramp - an instance of `kitControl:Ramp`. */ doLoad(ramp) { this.$updateDom(ramp); } /** * @private * @param {baja.Component} ramp - an instance of `kitControl:Ramp`. */ $updateDom(ramp) { this.jq().children('input').val(ramp.getOut().getValueDisplay()); } } }); We've moved the attachment to the changed event from doLoad() to doInitialize(). This works better for a few reasons. doInitialize() only runs once, so it's a good place to arm event handlers, such as changed. The methods are smaller and easier to read. The code to update the DOM based on the Ramp's current values is well-defined, and has its own method. When using subscriberMixIn, there is only one Subscriber instance that lives for the life of your Widget, and it's always subscribed to the Component that is currently loaded. Therefore, it's safe to arm a changed handler, and it will get fired whenever the currently loaded Component fires a changed event. And this.value() will always return the currently loaded Component! We've also moved the actual implementation of updating the DOM into a separate, private $updateDom() method. Why didn't we just keep everything in doLoad() and then just call doLoad() directly from our changed event handler? Remember the note about do methods from the introduction: call load(), implement doLoad(); don't implement load(), don't call doLoad(). As a best practice, if you want to reuse the logic of doLoad() (or any do method), don't call it directly; refactor it out to a shared function that can be called directly. On the last point: for best testability, I recommend that you minimize the amount of code inside event handlers. In a test, it can be complicated to hit all the code branches inside an event handler because you can't call it directly. But we've refactored the actual logic out of the event handler. Now the event handler is just \"glue code\" - its only purpose is to link the component event to the $updateDom() method. Testing the glue connection is easy (trigger the event and verify $updateDom() got called); and testing the logic itself is easy - just call $updateDom() directly! BMyFirstWidget.java package com.companyname.myFirstModule.ux; import javax.baja.naming.BOrd; import javax.baja.nre.annotations.AgentOn; import javax.baja.nre.annotations.NiagaraSingleton; import javax.baja.nre.annotations.NiagaraType; import javax.baja.sys.BSingleton; import javax.baja.sys.Context; import javax.baja.sys.Sys; import javax.baja.sys.Type; import javax.baja.web.BIFormFactorMax; import javax.baja.web.js.BIJavaScript; import javax.baja.web.js.JsInfo; @NiagaraType(agent = @AgentOn(types = \"kitControl:Ramp\")) @NiagaraSingleton public final class BMyFirstWidget extends BSingleton implements BIJavaScript, BIFormFactorMax { private BMyFirstWidget() {} public JsInfo getJsInfo(Context cx) { return JS_INFO; } private static final JsInfo JS_INFO = JsInfo.make(BOrd.make(\"module://myFirstModule/rc/MyFirstWidget.js\")); } The class implements BIJavaScript. This means that the purpose of this Java class is just to register the existence of a JavaScript module with the Niagara Framework. The Java class does not have any behavior of its own. The JsInfo tells the framework that the JavaScript file that contains the actual implementation is MyFirstWidget.js. In real life, our JsInfo would also reference a BJsBuild. More information about this in the Building JavaScript Applications help document. The class implements javax.baja.web.BIFormFactorMax. This tells the framework that it represents a bajaux Widget to be rendered as a fullscreen View in Workbench and the HTML5 Profile. (If we wanted a field editor, we would use BIFormFactorMini.) The class extends javax.baja.sys.BSingleton so a new instance of it doesn't need to be created each time. Note that it is final so it cannot be extended. The class is an agent on kitControl:Ramp. This tells the framework that when we navigate to a Ramp instance, it should render a MyFirstWidget to show it to us. Note that it uses the @NiagaraType and @AgentOn annotations, so the agent registration is automatically added for us when we build the module - we no longer have to edit module-include.xml by hand. myFirstModule-ux.gradle.kts Add the following to the gradle file to ensure all relevant files are included in the built module. This snippet includes every file in the rc/ directory, which is appropriate for most use cases, but you can change it to be more selective if needed. tasks.named&lt;Jar&gt;(\"jar\") { from(\"src\") { include(\"rc/\") } } Results Building this module will result in the Ramp Component having a new pure HTML5 View available in Workbench, Hx, and the HTML5 Profile. The same code is used in both Workbench and browser environments. As it's a View, it can be accessed directly or added to a Px page. The code for the Widget is embedded in a distributable JAR file. Next See our Saving Modifications to Station tutorial. × Search results Close "},"tutorial-20-mfw-modifying.html":{"id":"tutorial-20-mfw-modifying.html","title":"Tutorial: Saving Modifications to Station","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Saving Modifications to Station This tutorial follows from Getting Started - MyFirstWidget. We're going to modify MyFirstWidget to enable a user to make changes to the Ramp Component on the Station through your Widget. We will also introduce Templates, Lexicons, and CSS to simplify your modular HTML design. Outline +- myFirstModule +- myFirstModule-ux +- src | +- com | | +- companyname | | +- myFirstModule | | +- ux | | +- BMyFirstWidget.java | +- rc | +- templates | | +- MyFirstWidget.hbs -------------------------------------- *NEW* | +- myFirstModule.css ----------------------------------------- *NEW* | +- MyFirstWidget.js --------------------------------------*MODIFIED* +- module.lexicon -------------------------------------------------- *NEW* +- myFirstModule-ux.gradle.kts MyFirstWidget.hbs: a template file holding the HTML for MyFirstWidget. myFirstModule.css: a file holding all the module's CSS. module.lexicon: a file holding the user-facing text elements of all the module's Widgets. Templates Our HTML will be getting a bit more complicated in this next example. In the first tutorial, we had a simple HTML input DOM element that was updated every time the out Property of a Ramp Component changed on the server. Now, we're going to make our Widget interactive: we're going to add two additional form elements, a text input and check box, to allow the user to view and edit the Ramp's amplitude and enabled properties. Piecing this HTML together within the JavaScript itself would make it harder to read and more difficult to edit in the future, so let's look at another way to keep the HTML looking like HTML. In this example, our Widget is going to use a client side JavaScript template library called Handlebars. This allows us to keep our HTML in a separate template file and then require it into our Widget. (The Handlebars templating method is easy, and requires no additional setup. In an upcoming tutorial, we'll look at a couple other templating methods that may fit your use case better.) MyFirstWidget.hbs &lt;div&gt; &lt;label&gt;{{valueLabel}}: &lt;label&gt; &lt;input class='MyFirstWidget-value' type='text' value='{{loadingPlaceholder}}' readonly='readonly'/&gt; &lt;/div&gt; &lt;div&gt; {{changeValuesText}} &lt;/div&gt; &lt;div&gt; &lt;label&gt;{{amplitudeLabel}}: &lt;label&gt; &lt;input class='MyFirstWidget-amplitude' type='text' value='{{loading}}' /&gt; &lt;/div&gt; &lt;div&gt; &lt;label&gt;{{enabledLabel}}: &lt;label&gt; &lt;input class='MyFirstWidget-enabled' type='checkbox' value='Enabled' /&gt; &lt;/div&gt; Here, our template will generate the contents of the DOM element in which our Widget lives. After initialization, our Widget's DOM element will contain four &lt;div&gt; child elements. The file name does not need to match, but this convention helps easily distinguish which templates belong to which Widgets. {{variables}} within double brackets will be filled in with actual values that we provide when we generate the templated HTML in the doInitialize() method below. Modifying, Reading, and Saving a Widget We can now add some additional methods to our JavaScript Widget to handle DOM modification, reading and saving the data back to the Server. We're really just implementing the behavior at the touchpoints of a Widget's lifecycle detailed in the README, so please refer back to those as needed. Please skim over the new code for MyFirstWidget.js, then we'll discuss it in detail. MyFirstWidget.js - modified /** * A module defining `MyFirstWidget`. * * @module nmodule/myFirstModule/rc/MyFirstWidget * * Note that certain module IDs include an exclamation point (!). The '!' * character indicates the use of a RequireJS plugin. The plugins used for this * module include: * * - BajaScript (baja!): ensures that BajaScript has fully started * - Handlebars (hbs!): import Handlebars templates * - CSS (css!): import a CSS stylesheet for this `Widget` */ define([ 'baja!', 'hbs!nmodule/myFirstModule/rc/templates/MyFirstWidget', 'lex!myFirstModule', 'bajaux/Widget', 'bajaux/mixin/subscriberMixIn', 'bajaux/util/SaveCommand', 'jquery', 'Promise', 'css!nmodule/myFirstModule/rc/myFirstModule' ], function ( baja, template, lexs, Widget, subscriberMixIn, SaveCommand, $, Promise) { 'use strict'; const [ myFirstModuleLex ] = lexs; const widgetDefaults = () =&gt; ({ properties: { rootCssClass: 'MyFirstWidget' } }); /** * An editor for working with `kitControl:Ramp` instances. * * @class * @extends module:bajaux/Widget * @alias module:nmodule/myFirstModule/rc/MyFirstWidget */ return class MyFirstWidget extends Widget { constructor(params) { super({ params, defaults: widgetDefaults() }); subscriberMixIn(this); // Add a Save Command to allow the user to save the `Widget`. this.getCommandGroup().add(new SaveCommand()); } /** * Initialize the `MyFirstWidget`. * * Update the contents of the DOM in which the `Widget` is initialized. This * function uses the Handlebars template we imported to generate the HTML. * * This function also sets up jQuery event handlers. By default, handlers * registered on the `dom` parameter, like then ones we arm in this function, * will be automatically cleaned up when the `Widget` is destroyed. Any * additional handlers (on child elements of the `dom` parameter, say, or on * elements outside of this `Widget`) would need to be cleaned up in * `doDestroy()` in order to prevent memory leaks. * * @param {JQuery} dom - The DOM element into which to load this `Widget` */ doInitialize(dom) { // The template function returns the contents of MyFirstWidget.hbs, but with // variables like {{valueLabel}} filled in using the properties of the object // argument. dom.html(template({ valueLabel: myFirstModuleLex.get('MyFirstWidget.value'), changeValuesText: myFirstModuleLex.get('MyFirstWidget.changeValues'), amplitudeLabel: myFirstModuleLex.get('MyFirstWidget.amplitude'), enabledLabel: myFirstModuleLex.get('MyFirstWidget.enabled'), loadingPlaceholder: myFirstModuleLex.get('MyFirstWidget.loading') })); this.getSubscriber().attach('changed', () =&gt; this.$updateDom(this.value())); // When the user makes a change, mark the `Widget` as modified. dom.on('input', '.MyFirstWidget-amplitude', () =&gt; { this.setModified(true); }); dom.on('change', '.MyFirstWidget-enabled', () =&gt; { this.setModified(true); }); } /** * Update the DOM to show the Ramp's current values. * * @param {baja.Component} ramp - an instance of `kitControl:Ramp` */ doLoad(ramp) { this.$updateDom(ramp); } /** * Update the DOM to reflect the given Ramp's current values. * @private * @param {baja.Component} ramp a `kitControl:Ramp` */ $updateDom(ramp) { this.$getValueDisplay().val(ramp.getOutDisplay()); // Only update the editable DOM if the user hasn't made unsaved changes. if (!this.isModified()) { const amplitudeInput = this.$getAmplitudeInput(); // Don't reset the user's cursor every time the value refreshes if the // input box has focus. They may be trying to select or edit the // contents. if (!amplitudeInput.is(':focus')) { amplitudeInput.val(ramp.getAmplitudeDisplay()); } this.$getEnabledCheckbox().prop('checked', ramp.getEnabled()); } } /** * Reads out the `enabled` and `amplitude` values that the user has currently entered. * * @returns {module:nmodule/myFirstModule/rc/MyFirstWidget~RampProperties} */ doRead() { return { enabled: this.$getEnabledCheckbox().is(':checked'), amplitude: parseFloat(this.$getAmplitudeInput().val()) }; } /** * Save the user-entered changes to the loaded `kitControl:Ramp`. * * Note that the parameter to this function is the same as that resolved by * doRead(). * * @param {module:nmodule/myFirstModule/rc/MyFirstWidget~RampProperties} readValue * @returns {Promise} */ doSave(readValue) { const ramp = this.value(); const { enabled, amplitude } = readValue; // Return a Promise so that the framework knows when the save has completed. return Promise.all([ ramp.set({ slot: 'enabled', value: enabled }), ramp.set({ slot: 'amplitude', value: amplitude }) ]); } /** * @private * @returns {JQuery} the text box for the `amplitude` property */ $getAmplitudeInput() { return this.jq().find('.MyFirstWidget-amplitude'); } /** * @private * @returns {JQuery} the checkbox for the `enabled` property */ $getEnabledCheckbox() { return this.jq().find('.MyFirstWidget-enabled'); } /** * @private * @returns {JQuery} the readonly text box showing the Ramp's value */ $getValueDisplay() { return this.jq().find('.MyFirstWidget-value'); } }; /** * @typedef module:nmodule/myFirstModule/rc/MyFirstWidget~RampProperties * @property {boolean} enabled - whether the Ramp is enabled * @property {number} amplitude - the Ramp's amplitude */ }); In the constructor, we're specifying a rootCssClass of MyFirstWidget. This means that whenever an instance of MyFirstWidget is initialized, its DOM element will receive the CSS class of MyFirstWidget, which we can use for styling and selection. We are also adding a SaveCommand to the Widget's CommandGroup. When you view MyFirstWidget in the browser or Workbench, this SaveCommand will appear in the toolbar, so you can click it to save the Widget. In doInitialize(), note that we are using the Handlebars template to set up the initial contents of the DOM element. We're also adding event handlers to ensure that when the user changes the entered values, we mark the Widget as modified. This is very important - without it, the SaveCommand will never enable. We apply the current properties of the Ramp to the input fields in the DOM both in doLoad() and in the changed event handler, as before. doRead() is new. When implementing a Widget that allows user edits, doRead() should resolve a value that represents what the user currently has entered. Since our Widget has a checkbox for enabled and a number input for amplitude, we resolve an object with these two properties. This object represents \"what values for enabled and amplitude the user has currently entered.\" (Note the @typedef JSDoc tag describing this data structure - this is not at all required, but can be helpful for readability and autocomplete.) doSave() is also new. When a Widget is save()d, it will read() the current value out, validate it, and pass it to doSave() for you - so there's no need to call read() again. The implementation of doSave() should mutate the currently loaded value. So we do - we use the BajaScript set() method to set the slot values of the currently loaded Ramp. Lexicons Lexicons are how Niagara allows its user interface to be localized for different languages. When a string of text needs to be shown to the user, it's best not to hardcode that string into your source code, because then it can't be localized to a different language. Instead, pull user-facing text from the Lexicon whenever possible. This makes it easy for your UI to be translated for your users in different countries and locales. The lex! plugin, used in this example, allows you to pull lexicon data from the module of your choice and easily reference it in your UI code. This works very well in conjunction with using variables in your HTML templates. module.lexicon # # Lexicon for the my first module ux. # MyFirstWidget.value=Value MyFirstWidget.changeValues=Changing these inputs modifies the Widget, not the component. You must Save to push the changes to the station. MyFirstWidget.amplitude=Amplitude MyFirstWidget.enabled=Enabled MyFirstWidget.loading=Loading... Default Values You can also set default values for lexicon entries within a JavaScript lex.get() function call. This will help if module.lexicon is ever unavailable. Even though this code allows the Widget to function without a module.lexicon file, you should still create one so that the user can easily find and customize the Widget text, all in one place. dom.html(template({ value: lex.get({ key: \"MyFirstWidget.value\", def: \"Value\" }), changeValues: lex.get({ key: \"MyFirstWidget.changeValues\", def: 'Changing these inputs modifies the Widget, not the ' + 'component. You must Save to push the changes to the station.' }), amplitude: lex.get({ key: \"MyFirstWidget.amplitude\", def: \"Amplitude\" }), enabled: lex.get({ key: \"MyFirstWidget.enabled\", def: \"Enabled\" }), loading: lex.get({ key: \"MyFirstWidget.loading\", def: \"Loading...\" }) })); CSS The CSS RequireJS plug-in is being used to import a style sheet for the Widget. Please note, since there may be other bajaux Widgets running alongside yours in the same view importing their own CSS, you must make your CSS selectors unique. An easy way to do this is to include your Widget's name in the selector, since that must also be unique already (e.g. MyFirstWidget). In this example, we just reuse the widget name as the class name, but if you are concerned about collisions with other modules that might include Widgets with the same name as yours, you can use whatever naming convention you like, such as myCompanyName-MyFirstWidget. (One note about the ordering of RequireJS imports in the example above. A css! import only ever resolves undefined, so for cleanliness, we place it at the end of the list of imports. Otherwise, the RequireJS function would require a dummy parameter like cssUnused, which would add clutter. Feel free to use whatever import ordering scheme makes sense for you.) myFirstModule.css .MyFirstWidget { background-color: #E8E8E8; padding: 5px; } .MyFirstWidget &gt; div { padding: 5px; } BCssResource As of Niagara 4.13, in your BIJavaScript class representing your Widget, you can also declare a BCssResource dependency. This will tell the framework that the CSS file is used by your Widgets, so you don't have to remember the css! import. See the UI Changelog for details. Next See our Making your Widget Dashboardable tutorial! × Search results Close "},"tutorial-30-mfw-dashboarding.html":{"id":"tutorial-30-mfw-dashboarding.html","title":"Tutorial: Making your Widget Dashboardable","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Making your Widget Dashboardable This tutorial follows from Saving Modifications to Station. We're going to modify MyFirstWidget to make it dashboardable. Outline +- myFirstModule +- myFirstModule-ux +- src | +- com | | +- companyname | | +- myFirstModule | | +- ux | | +- BMyFirstWidget.java | +- rc | +- templates | | +- MyFirstWidget.hbs ----------------------------------*MODIFIED* | +- myFirstModule.css | +- MyFirstWidget.js --------------------------------------*MODIFIED* +- module.lexicon ----------------------------------------------*MODIFIED* +- module-include.xml +- myFirstModule-ux.gradle Drag and Drop One of the most useful features of dashboards is to allow users to drag components from the Nav tree and drop them onto a Widget, instantly loading the new component so the user can interact with it without navigating away from that page. On top of that, users can save the dashboard so that they always see their preferred component in that View. In this example, we're adding drag-and-drop functionality to MyFirstWidget. When MyFirstWidget is shown in a dashboard, the user can drag a different Ramp onto it directly to bind the widget to the Ramp of their choice, instead. The ORD to that Ramp will be persisted in the user's dashboard data automatically. The added lines of code related to this functionality have been marked as *DRAG &amp; DROP* in the modified MyFirstWidget.js file. Dashboard-only Features Sometimes a user may want to show a feature of their Widget only when it is contained within a dashboard. We've added a private note box at the bottom of the Widget to demonstrate this. The added lines of code related to this functionality have been marked as *DASHBOARD-ONLY NOTE* in the modified MyFirstWidget.js file. There are a lot of changes in this example - again, just skim over the code to get a feel for what's going on, and then we'll discuss in more detail. MyFirstWidget.js - modified /** * A module defining `MyFirstWidget`. * * @module nmodule/myFirstModule/rc/MyFirstWidget */ define([ 'baja!', 'hbs!nmodule/myFirstModule/rc/templates/MyFirstWidget', 'lex!myFirstModule', 'log!nmodule.myFirstModule.rc.MyFirstWidget', 'bajaux/Widget', 'bajaux/dragdrop/dragDropUtils', //----------------------- *DRAG &amp; DROP* 'bajaux/mixin/subscriberMixIn', 'bajaux/util/SaveCommand', 'jquery', 'Promise', 'css!nmodule/myFirstModule/rc/myFirstModule' ], function ( baja, template, lexs, log, Widget, dragDropUtils, subscriberMixIn, SaveCommand, $, Promise) { 'use strict'; const [ myFirstModuleLex ] = lexs; const logSevere = log.severe.bind(log); // When the dashboard is saved, dashboardable property information will be saved away // to the user's station, and is accessible by that user alone. Also note // that these dashboardable properties are not attached to the `Widget's` value, but the // `Widget` itself! const widgetDefaults = () =&gt; ({ properties: { rootCssClass: 'MyFirstWidget', // This is where we will save the override ORD. overrideOrd: { //---------------------------------- *DRAG &amp; DROP* value: '', dashboard: true, // Use hidden and readonly to hide this property from the user in an editor. hidden: true, readonly: true }, // This is where we will save the contents of the private note unique to each user. privateNote: { //-------------------------- *DASHBOARD-ONLY NOTE* value: '', dashboard: true, hidden: true, readonly: true }, // This is a special optional property that can be added to see if a // `Widget` is currently running on a dashboard. You do not need it to make // your `Widget` dashboardable. This is how we will decide whether or not to // show the private note textbox. dashboard: { value: false, hidden: true, readonly: true, transient: true } } }); /** * An editor for working with `kitControl:Ramp` instances. * * This editor allows a user to drag and drop a new Ramp component onto it to * be viewed or modified instantly. This `Widget` is also dashboardable, which * allows a user to save the dropped override Ramp as the default one loaded. * Dashboarding also provides a private note text box, which only that user * can see and modify. * * @class * @extends module:bajaux/Widget * @alias module:nmodule/myFirstModule/rc/MyFirstWidget */ return class MyFirstWidget extends Widget { constructor(params) { super({ params, defaults: widgetDefaults() }); subscriberMixIn(this); this.getCommandGroup().add(new SaveCommand()); } doInitialize(dom) { dom.html(template({ valueLabel: myFirstModuleLex.get('MyFirstWidget.value'), changeValuesText: myFirstModuleLex.get('MyFirstWidget.changeValues'), amplitudeLabel: myFirstModuleLex.get('MyFirstWidget.amplitude'), enabledLabel: myFirstModuleLex.get('MyFirstWidget.enabled'), loadingPlaceholder: myFirstModuleLex.get('MyFirstWidget.loading'), noteLabel: myFirstModuleLex.get('MyFirstWidget.note'), //--------------------- *DASHBOARD-ONLY NOTE* // Hide private note if widget is not dashboarded. noteVisible: this.properties().getValue('dashboard'), // *DASHBOARD-ONLY NOTE* privateNote: this.properties().getValue('privateNote'), // *DASHBOARD-ONLY NOTE* dashboardPlaceholder: myFirstModuleLex.get('MyFirstWidget.dashboardPlaceholder') //-------------- *DASHBOARD-ONLY NOTE* })); dom.on('input', '.MyFirstWidget-amplitude', () =&gt; { this.setModified(true); }); dom.on('change', '.MyFirstWidget-enabled', () =&gt; { this.setModified(true); }); // Copy private note text to `Widget` when modified. - *DASHBOARD-ONLY NOTE* dom.on('input', '.MyFirstWidget-noteText', () =&gt; { this.properties().setValue('privateNote', this.$getNoteTextarea().val()); }); // Set up drag and drop. --------------------------------- *DRAG &amp; DROP ORD* dom.on('dragover', (e) =&gt; { e.preventDefault(); }); dom.on('drop', (e) =&gt; { this.$updateFromDrop(e.originalEvent.dataTransfer).catch(logSevere); e.preventDefault(); e.stopPropagation(); }); this.getSubscriber().attach('changed', () =&gt; { // Use override value if available. -------------------- *DRAG &amp; DROP ORD* const rampToUpdate = this.$overrideVal || this.value(); this.$updateDom(rampToUpdate); }); // Set up the override ORD if it exists. ----------------- *DRAG &amp; DROP ORD* return this.$resolveOverrideOrd(); } /** * Update the DOM to show the ramp's current values. * * @param {baja.Component} ramp - an instance of `kitControl:Ramp` */ doLoad(ramp) { // Use override value if available. -------------------- *DRAG &amp; DROP ORD* const rampToUpdate = this.$overrideVal || ramp; this.$updateDom(rampToUpdate); } /** * Update the DOM to reflect the given Ramp's current values. * @private * @param {baja.Component} ramp a `kitControl:Ramp` */ $updateDom(ramp) { this.$getValueDisplay().val(ramp.getOutDisplay()); // Only update the editable DOM if the user hasn't made unsaved changes. if (!this.isModified()) { const amplitudeInput = this.$getAmplitudeInput(); // Don't reset the user's cursor every time the value refreshes if the // input box has focus. They may be trying to select or edit the // contents. if (!amplitudeInput.is(':focus')) { amplitudeInput.val(ramp.getAmplitudeDisplay()); } this.$getEnabledCheckbox().prop('checked', ramp.getEnabled()); } } /** * Reads out the `enabled` and `amplitude` values that the user has currently entered. * * @returns {module:nmodule/myFirstModule/rc/MyFirstWidget~RampProperties} */ doRead() { return { enabled: this.$getEnabledCheckbox().is(':checked'), amplitude: parseFloat(this.$getAmplitudeInput().val()) }; } /** * Save the user-entered changes to the loaded `kitControl:Ramp`. * * Note that the parameter to this function is the same as that resolved by * doRead(). * * @param {module:nmodule/myFirstModule/rc/MyFirstWidget~RampProperties} readValue * @returns {Promise} */ doSave(readValue) { // Use override value if available. ------------------ *DRAG &amp; DROP ORD* const ramp = this.$overrideVal || this.value(); const { enabled, amplitude } = readValue; return Promise.all([ ramp.set({ slot: 'enabled', value: enabled }), ramp.set({ slot: 'amplitude', value: amplitude }) ]); } /** * Called when a new data value is dragged and dropped onto the `Widget`. * Resolves a promise once the drag and drop operation has completed. * * @private * @param {DataTransfer} dataTransfer - The data dropped onto the Widget. * @returns {Promise} */ $updateFromDrop(dataTransfer) { //-------------- *DRAG &amp; DROP* return dragDropUtils.fromClipboard(dataTransfer) .then((envelope) =&gt; { if (envelope.getMimeType() !== 'niagara/navnodes') { return; } return envelope.toJson() .then((json) =&gt; { // Although multiple items could have been dragged and dropped // on, we're just going to use the first item in the list. const obj = json &amp;&amp; json[0]; const ord = obj &amp;&amp; obj.ord; if (ord) { if (obj.typeSpec === 'kitControl:Ramp') { return this.$setOverrideOrd(ord); } else { throw new Error('Override component must be a `kitControl:Ramp`.'); } } }); }); } /** * Sets up a new override ORD and unsubscribes the old one. * * @private * @param {string} newOverrideOrd - The new override ORD. * @returns {Promise} */ $setOverrideOrd(newOverrideOrd) { //------------- *DRAG &amp; DROP* // Record any old ORD value so we can unsubscribe it. const oldOverrideOrd = this.properties().getValue('overrideOrd'); // There is no need to manually mark the `Widget` as modified here. When the // widget is in a dashboard, a change to widget.properties() automatically // does this. this.properties().setValue('overrideOrd', newOverrideOrd); return Promise.all([ this.$resolveOverrideOrd(), oldOverrideOrd &amp;&amp; baja.Ord.make(oldOverrideOrd).get() .then((comp) =&gt; this.getSubscriber().unsubscribe(comp)) ]); } /** * Resolves any override ORD. * * @private * @returns {Promise} */ $resolveOverrideOrd() { //------------------------ *DRAG &amp; DROP* const overrideOrd = this.properties().getValue('overrideOrd'); if (overrideOrd) { return baja.Ord.make(overrideOrd).get({ subscriber: this.getSubscriber() }) .then((value) =&gt; { this.$overrideVal = value; this.$updateDom(value); }); } else { delete this.$overrideVal; } } /** * @private * @returns {JQuery} the text box for the `amplitude` property */ $getAmplitudeInput() { return this.jq().find('.MyFirstWidget-amplitude'); } /** * @private * @returns {JQuery} the checkbox for the `enabled` property */ $getEnabledCheckbox() { return this.jq().find('.MyFirstWidget-enabled'); } /** * @private * @returns {JQuery} the readonly text box showing the Ramp's value */ $getValueDisplay() { return this.jq().find('.MyFirstWidget-value'); } /** * @private * @returns {JQuery} the label showing the Widget's override ORD */ $getOverrideOrdDisplay() { // ------------------ *DRAG &amp; DROP ORD* return this.jq().find('.MyFirstWidget-overrideOrd'); } /** * @private * @returns {JQuery} the textarea containing the private note */ $getNoteTextarea() { //----------------------- *DASHBOARD-ONLY NOTE* return this.jq().find('.MyFirstWidget-noteText'); } }; }); First off, we're using the log! plugin for browser-based logging. This provides more features than just console.log() and can log up to the Application Director. See the log JSDoc for more details. In doInitialize(), we've added some more properties relating to the override ORD and the dashboardable note to the Handlebars template. The contents of the dashboardable note are stored in the dashboardable privateNote Property. They are written to the DOM in doInitialize(), and as the user types, they are written back onto the privateNote Property. Because privateNote is a dashboardable Property, saving the Widget when it's on a dashboard will also save that Property. We also arm new handlers for drag/drop. When we drop from the nav tree onto the Widget, the profile itself (either Workbench or HTML5 profile) will provide a specific data structure. The dragDropUtils module knows how to consume this data structure into an envelope which provides more details about what you're dropping. See the JSDoc for that module for more details, and inspect the $updateFromDrop() method here for an example. When the user drops a Ramp onto the Widget, it will record that Ramp's ORD as its overrideOrd. Again, because overrideOrd is a dashboardable Property, saving the Widget when it's on a dashboard will save overrideOrd as well. $setOverrideOrd() and $resolveOverrideOrd() handle the storage and resolution of an existing override ORD. MyFirstWidget.hbs - modified &lt;div class='MyFirstWidget'&gt; &lt;div&gt; &lt;label&gt;{{overrideOrdLabel}} &lt;span class='MyFirstWidget-overrideOrd'&gt;{{overrideOrd}}&lt;/span&gt;&lt;/label&gt; &lt;/div&gt; &lt;div&gt; &lt;label&gt;{{valueLabel}}: &lt;label&gt; &lt;input class='MyFirstWidget-value' type='text' value='{{loadingPlaceholder}}' readonly='readonly'/&gt; &lt;/div&gt; &lt;div&gt; {{changeValuesText}} &lt;/div&gt; &lt;div&gt; &lt;label&gt;{{amplitudeLabel}}: &lt;label&gt; &lt;input class='MyFirstWidget-amplitude' type='text' value='{{loading}}' /&gt; &lt;/div&gt; &lt;div&gt; &lt;label&gt;{{enabledLabel}}: &lt;label&gt; &lt;input class='MyFirstWidget-enabled' type='checkbox' value='Enabled' /&gt; &lt;/div&gt; &lt;div class='MyFirstWidget-note'{{#unless noteVisible}} style='display: none;'{{/unless}}' &gt; &lt;div&gt; {{noteLabel}} &lt;/div&gt; &lt;div&gt; &lt;textarea class='MyFirstWidget-noteText' rows='4' cols='50' placeholder='{{dashboardPlaceholder}}' &gt; {{privateNote}} &lt;/textarea&gt; &lt;/div&gt; &lt;/div&gt; &lt;/div&gt; module.lexicon - modified # # Lexicon for the my first module ux. # MyFirstWidget.value=Value MyFirstWidget.changeValues=Changing these inputs modifies the widget, not the component. You must Save to push the changes to the station. MyFirstWidget.amplitude=Amplitude MyFirstWidget.enabled=Enabled MyFirstWidget.loading=Loading... MyFirstWidget.note=Private note: MyFirstWidget.overrideOrd=Override ORD: MyFirstWidget.dashboardPlaceholder=Thanks to the dashboard, no other users will see this text when looking at this View. Next There's a lot more to learn about. To explore, we're going to switch from creating a Niagara Module that embeds our JavaScript Widget to using some JavaScript Playground Components. These are not something we would distribute to customers for production, but offer another way to experiment with bajaux. In the playground examples, you can learn about... Using Commands. Using Properties. DOM event handling. Hyperlinking. Ensure you have the BoxService from the box palette added to your Station's Service Container. Open the docDeveloper palette and add the BajauxExamples folder to the root of your Station. Make sure you're logged on as user with admin read, write and invoke privileges. Using Workbench or a browser, click on the first exercise to get started. × Search results Close "},"tutorial-40-tipsAndTricks.html":{"id":"tutorial-40-tipsAndTricks.html","title":"Tutorial: Tips and Tricks","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Tips and Tricks bajaux Tips and Tricks Templating methods Over the introductory tutorials, we mainly used Handlebars for templating HTML. Handlebars is certainly a solid, well-supported choice. Here are some aspects of Handlebars that may make it a good fit, or a poor fit, for your particular use case: The HTML is kept in a separate file, apart from your JavaScript. Escaping (see below) is automatically handled for you. The logic you may embed in a template (for loops, if statements, etc.) is very limited. HTML contents of elements are parsed as a string. If you are creating a large number of elements (tens of thousands or more), this can harm performance. Here are a couple alternatives you might consider. ES6 Template Strings You can simply use template strings, which are part of JavaScript itself, to build out your HTML. class MyWidget extends Widget { doLoad(component) { this.jq().html(` &lt;table&gt; ${ component.getSlots().toArray() .map((slot) =&gt; `&lt;tr&gt;&lt;td&gt;${ _.escape(slot.getName()) }&lt;/td&gt;&lt;/tr&gt;`) .join('') } &lt;/table&gt; `); } } This way, the HTML is kept right in line with your JavaScript. You also have a lot more flexibility in terms of logic, since you are generating the HTML with JavaScript itself. The downsides are again that the HTML is parsed as a string, which may have performance implications, and escaping is not handled for you, so you must be diligent about protecting yourself from XSS. spandrel.jsx By using the Building Composite Widgets With spandrel module, you can use JSX to define your widgets. /** @jsx spandrel.jsx */ const MyWidget = spandrel((component) =&gt; { return ( &lt;table&gt;{ component.getSlots().toArray().map((slot) =&gt; { return ( &lt;tr&gt; &lt;td&gt;{slot.getName()}&lt;/td&gt; &lt;/tr&gt; ); }) }&lt;/table&gt; ); }); (Keep in mind that JSX is not React and this does not turn your bajaux Widget into a React component.) This approach, again, keeps your HTML right in line with your JavaScript and allows for any JavaScript logic you wish. The JSX compiles to JavaScript and native HTMLElements, so at scale, performance can beat parsing HTML as strings. JSX itself also handles the XSS-escaping of the text content of elements for you. Anecdotally, Tridium has been using spandrel.jsx almost exclusively for several releases now, so it may be a good fit for your use case as well. Escaping HTML for safety When implementing a bajaux Widget, you must take care to ensure that any content that could be user-generated is correctly escaped to protect your users from reflected XSS attacks. A reflected XSS attack could occur when a user of your station, who has limited write permissions, injects malicious JavaScript into the part of the station where they can write to. Another user with elevated permissions could then access that same part of the station, where a vulnerable Widget would cause that code to execute using their own permissions. In this way, the limited user could execute code using another user's elevated permissions, damaging the station or stealing information. Whenever you display user-generated text, you must ensure that it is correctly escaped to prevent it from being added to the page as code. Some examples of potentially user-generated text are: The names or display names of Components or their Slots Slot Facets The values of Component properties that are Strings, or displayed as Strings Values retrieved from the station via RPC (e.g. baja.rpc()), BQL queries, etc. Contents of files, which may be editable by users with write permissions to the file space Text retrieved from lexicons, which may be parameterized to include values from the station's Component Space Any of these can contain information that was placed into the station by a user. Some examples of methods of escaping this data for safe display are: Using jQuery.text() instead of jQuery.html() Setting an HTMLElement's textContent instead of its innerHtml Using Handlebars templating Using spandrel.jsx Manually escaping HTML using _.escape() or a similar JavaScript function × Search results Close "},"tutorial-50-spandrel.html":{"id":"tutorial-50-spandrel.html","title":"Tutorial: Building Composite Widgets With spandrel","body":" bajaux Modules bajaux/commands/Commandbajaux/commands/CommandGroupbajaux/commands/ToggleCommandbajaux/commands/ToggleCommandGroupbajaux/container/wb/Clipboardbajaux/container/wb/StringListbajaux/dragdrop/dragDropUtilsbajaux/dragdrop/Envelopebajaux/dragdrop/NavNodeEnvelopebajaux/dragdrop/StringEnvelopebajaux/eventsbajaux/icon/iconUtilsbajaux/lifecycle/WidgetManagerbajaux/mixin/batchLoadMixinbajaux/mixin/batchSaveMixinbajaux/mixin/responsiveMixInbajaux/mixin/subscriberMixInbajaux/Propertiesbajaux/registry/Registrybajaux/registry/RegistryEntrybajaux/spandrelbajaux/spandrel/jsxbajaux/util/CommandButtonbajaux/util/CommandButtonGroupbajaux/util/SaveCommandbajaux/Validatorsbajaux/Widget Interfaces bajaux/commands/Command~Undoable Tutorials Getting Started - MyFirstWidgetSaving Modifications to StationMaking your Widget DashboardableTips and TricksBuilding Composite Widgets With spandrel Building Composite Widgets With spandrel The spandrel API is new in Niagara 4.10. Its API status is Development. In many cases, Widgets will consist of several child UI controls. For instance, imagine an editor for a User object that looks like this: class User { /** * @param {string} name * @param {boolean} enabled */ constructor(name, enabled) { this.name = name; this.enabled = enabled; } } You'd most likely want a text editor for the user's name, and a checkbox for whether it's enabled or not. You could implement your Widget in pure HTML: class UserEditor extends Widget { doInitialize(dom) { dom.html('Name: &lt;input type=\"text\"&gt; Enabled: &lt;input type=\"checkbox\"&gt;'); } getTextInput() { return this.jq().find('input[type=text]'); } getCheckbox() { return this.jq().find('input[type=checkbox]'); } doLoad(user) { this.getTextInput().val(user.name); this.getCheckbox().prop('checked', user.enabled); } doRead() { return new User(this.getTextInput().val(), this.getCheckbox().prop('checked')); } } But when developing a more complex user interface, you'll need to edit strings and booleans quite often, and you probably won't want to re-implement this logic each and every time. It would be much easier to implement a fully-featured String editor, and a fully-featured Boolean editor, and then build your UserEditor by simply putting those together. As your UI grows more complex, you can easily reuse these individual Widgets and re-assemble them into a wide variety of composite Widgets. You could always do that in pure bajaux by manually instantiating and loading Widgets. In addition, the fe module in webEditors provided a way of looking up Widgets and managing the workflow of building those Widgets. In Niagara 4.10, bajaux itself now contains a number of APIs to make the process of building dynamic, composite Widgets easy. Defining a Widget Workflow The process of building a Widget to show a particular piece of data can be broken down into a series of questions: Do I need to edit a particular data value? (Is it a String? A Boolean? A Baja Component?) What kind of Widget do I need to show? (Do I know ahead of time what kind of Widget I need? Or do I need to dynamically choose the right kind of Widget for the data I'm editing?) How should the Widget be configured? (Should I assign Properties to it? Should it be readonly? What form factor should it have - large or small?) Where should I put the Widget? (Do I have a jQuery element to put it in? A raw HTMLElement?) We could actually define the answers (or lack of answers) to all of these questions in the form of a JavaScript object. const buildParams = { value: user, // I want to edit this User object, type: UserEditor, // using an instance of UserEditor, properties: { caseSensitive: true }, // with these Properties, dom: $('#userGoesHere'), // and putting it in this DOM element. }; const Ctor = buildParams.type; const widget = new Ctor({ properties: buildParams.properties }); return widget.initialize(buildParams.dom) .then(() =&gt; widget.load(buildParams.value)); This should look pretty familiar if you've previously used the fe module. This workflow and its configuration are now defined in bajaux itself using the WidgetManager API. const manager = new WidgetManager(); return manager.buildFor(buildParams) .then((widget) =&gt; { /* widget is initialized and loaded */ }); One of WidgetManager's jobs is to dynamically look up what kind of Widget to build based purely on what value is getting loaded. For instance, if value is a String, then I want a StringEditor; if it's a Boolean I want a BooleanEditor etc. In Niagara world this is handled via agent registration lookups in the station registry, and the webEditors module has its own WidgetManager implemented to perform these lookups. But bajaux itself provides all the APIs needed for you to implement your own widget lookup logic. WidgetManager itself is fairly simple, but it's what underpins the next API we'll examine: spandrel. Building Composite Widgets With spandrel spandrel allows you to define your Widgets as a structure of context objects as described above. Rather than manually implementing the how in JavaScript by managing your child widgets by hand, it allows you to declare the what and let it manage everything behind the scenes. Let's start with an example: I want a Widget that just creates a label with the text Hello. First, the existing way: class HelloWidget extends Widget { doInitialize(dom) { dom.html('&lt;label&gt;Hello&lt;/label&gt;'); } } We would have had to imperatively call the .html() method to set the HTML contents of the Widget's DOM element to our desired HTML. But with spandrel, we do it declaratively by just telling it what we want the contents of our Widget to be. const dom = document.createElement('div'); const HelloWidget = spandrel('&lt;label&gt;Hello&lt;/label&gt;'); new WidgetManager().buildFor({ type: HelloWidget, dom }) .then(() =&gt; { console.log(dom); // &lt;div&gt; // &lt;label&gt;Hello&lt;label&gt; // &lt;/div&gt; }); spandrel accepts an argument that indicates what you want the structure of your Widget to be. For this case, we give it a string defining what we want the contents of our Widget to be: a single label. spandrel can accept simple strings of HTML, which it will treat the same as a Widget config. If we want the Widget to contain more than one child element, we can return an array as well. Because the structure of this widget will be the same every time, we will refer to it as a static widget. For our next example, we'll add a child Widget, into which we load the String value 'World'. const dom = document.createElement('div'); const HelloStringWidget = spandrel([ '&lt;label&gt;Hello&lt;/label&gt;', { dom: '&lt;span&gt;&lt;/span&gt;', value: 'World' } ]); new WidgetManager().buildFor({ type: HelloStringWidget, dom }) .then((w) =&gt; { console.log(dom); // &lt;div&gt; // &lt;label&gt;Hello&lt;/label&gt; // &lt;span&gt;World&lt;/span&gt; // &lt;/div&gt; console.log(w.queryWidget(1).value()); // 'World' }); When the argument to spandrel is an array, each element indicates a child of your widget. The first argument is still simply a label, but now the second argument is a build context: I want a widget to show the value 'World', and put it in a span. By default, if you don't specify a widget type, spandrel will use a ToStringWidget, which simply shows the value as a string. Take a look at queryWidget(1): what is happening there? Well, Spandrel keeps track of all its child widgets by key. When built using an array, those keys are array indices. So queryWidget(1) gets us the Widget at index 1 in the array: our 'World' widget, which has the value World loaded into it. Speaking of widget keys: the argument to spandrel can also be an object literal, where the keys map to your widget's children. This makes it much more intuitive to query the widgets back out: const dom = document.createElement('div'); const HelloStringWidget = spandrel({ hello: '&lt;label&gt;Hello&lt;/label&gt;', world: { dom: '&lt;span&gt;&lt;/span&gt;', value: 'World' } }); new WidgetManager().buildFor({ type: HelloStringWidget, dom }) .then((w) =&gt; { console.log(w.queryWidget('world').value()); // 'World' }); The argument to spandrel can also be a function that returns your config: const HelloStringWidget = spandrel(() =&gt; { return { hello: '&lt;label&gt;Hello&lt;/label&gt;', world: { dom: '&lt;span&gt;&lt;/span&gt;', value: 'World' } }; }); This, in and of itself, is not interesting - until you consider that the argument to that function is the value being loaded. Therefore, your widget can dynamically define its own structure depending on the value! The easy example here is a Niagara Component. const dom = document.createElement('div'); const user = baja.$('baja:User'); const SlotListWidget = spandrel((component) =&gt; { const spandrelConfig = {}; component.getSlots().each((slot) =&gt; { spandrelConfig[slot] = { dom: '&lt;div&gt;&lt;/div&gt;', value: slot }; }); // our argument to spandrel is an object literal as in the previous // example, with the slot names as the spandrel keys. return spandrelConfig; }); new WidgetManager().buildFor({ type: SlotListWidget, value: user, dom }) .then((w) =&gt; { console.log(dom); // &lt;div&gt; // &lt;div&gt;fullName&lt;/div&gt; // &lt;div&gt;enabled&lt;/div&gt; // &lt;div&gt;expiration&lt;/div&gt; // ... console.log(w.queryWidget('fullName').value()); // fullName Slot }); We're already creating dynamic, composite widgets. But consider that spandrel widgets can be nested, too. This can be done using the kids property on a config object, as shown below. (We'll also split the function out to a reusable function, for clarity.) function componentToSpandrelConfig(component) { const spandrelConfig = {}; component.getSlots().properties().each((slot) =&gt; { // again, each slot name is a spandrel key. at that key we have a div, // with four child widgets underneath it. spandrelConfig[slot] = { dom: '&lt;div&gt;&lt;/div&gt;', kids: { nameLabel: '&lt;span&gt; Name: &lt;/span&gt;', nameValue: { dom: '&lt;span&gt;&lt;/span&gt;', value: slot.getName() }, typeLabel: '&lt;span&gt; Type: &lt;/span&gt;', typeValue: { dom: '&lt;span&gt;&lt;/span&gt;', value: slot.getType() } } }; }); return spandrelConfig; } const PropertyInfoWidget = spandrel(componentToSpandrelConfig); new WidgetManager().buildFor({ type: PropertyInfoWidget, value: user, dom }) .then((w) =&gt; { console.log(w.queryWidget('enabled/typeValue').value()); // baja:Boolean }); As you can see, the queryWidget function works on nested keys, separated by slashes. It and its plural counterpart, queryWidgets, also support wildcards, which makes reading out the info you want a snap: class PropertyInfoWidget extends spandrel(componentToSpandrelConfig) { /** * @returns {Array.&lt;string&gt;} an array of all the slot names */ doRead() { return this.queryWidgets('*/nameValue').map((w) =&gt; w.value()); } } In the example above, note the class extends spandrel() - this will be a common way to define spandrel-derived Widget constructors with their own read/save behavior. Performing dynamic widget lookups One of the core values of the WidgetManager API is the ability to perform a dynamic lookup of a widget that's compatible with a particular piece of data. For instance, if you were implementing a Property Sheet, you wouldn't want to manually import every possible field editor, and manually choose one for each slot based on a giant if-else. You'd just ask the framework: for each slot on my component, find me a field editor that's compatible with the value at that slot. spandrel supports this as well, because it uses the WidgetManager API to build out its widget structures. You can specify what kind of WidgetManager your spandrel widget should use by passing it as a manager parameter: class MySpecialWidgetManager extends WidgetManager {} const MySpecialWidget = spandrel((component) =&gt; { return buildMyDomStructure(component); }, { manager: new MySpecialWidgetManager() }); spandrel can also support pre-baked widget lookup strategies, so you don't have to manually inject one of the Niagara-specific widget managers. If your widget needs to use the Niagara registry to find widgets and field editors for Niagara types, you can pass strategy: 'niagara' to let spandrel inject that behavior. Note that this will introduce a dependency on the webEditors-ux module. const MySpecialWidget = spandrel((component) =&gt; { return buildMyDomStructureWithNiagaraFieldEditors(component); }, { strategy: 'niagara' }); How spandrel renders and updates your widgets When implementing a vanilla bajaux Widget, it's up to you to work directly with the DOM. When initializing or loading a value, your Widget has to update its own DOM: adding classes, creating or removing elements, or arming event handlers. Compare this with other libraries like React, where you would create a virtual DOM, and React itself would diff that virtual DOM against the real DOM, applying changes only where needed. spandrel walks a middle ground. We have a huge library of first- and third-party Widgets that are already built on the paradigm of direct DOM manipulation, so it's not feasible to completely switch over to a virtual-DOM-based approach. But direct DOM manipulation can still result in convoluted, inefficient code. spandrel's approach is to diff the configuration, not the DOM itself. You declare your DOM structure, and where you want other bajaux Widgets within that structure; spandrel will tweak the DOM in-place where possible, changing the Properties, readonly, and enabled states of Widgets, and load new values in where they are different. It may destroy or create new Widgets over time as your spandrel structure changes. spandrel minimizes the amount of DOM manipulation you, as the developer, need to worry about, even through bajaux won't ever get completely out of the DOM manipulation business itself. Although you won't need to worry about it most of the time, keep in mind the fact that the DOM you create might sometimes get tweaked in-place instead of being rebuilt. &lt;img&gt; tags, if given an onerror, might need a corresponding onsuccess - that sort of thing. But the vast majority of the time, spandrel and our existing library of bajaux Widgets should handle these sorts of details for you. Details about spandrel's diffing process When a spandrel widget updates itself, it tries to follow a unidirectional data flow. The widget will generate an intermediate representation of what it should look like - what DOM elements and Widgets should make up its structure. Then spandrel will diff that against its actual current structure, and make whatever changes are necessary to bring it up to date. This may sound similar to React's approach. But React's intermediate representation of itself takes the form of a virtual DOM, while spandrel's consists of a tree of JSON objects which define a structure of DOM elements and Widgets. Each of these JSON objects has a number of properties which may change over time. spandrel's response to changes in these properties are described below. (Note that static widgets do not change their structure, so this applies only to dynamic widgets.) dom: If the element's tag name changes (e.g. from a div to a span), the whole Widget will be destroyed and rebuilt. Otherwise, the element's classes, styles, and attributes will be updated in-place. enabled: The Widget will be enabled or disabled, and it will be re-rendered. formFactor: The Widget's form factor will be changed, and it will be re-rendered. properties: The Widget's Properties will be updated, and it will be re-rendered. readonly: The Widget will be set readonly or writable, and it will be re-rendered. type: The old Widget will be destroyed, and a new Widget instance of the new type will be constructed in its place. value: The new value will be loaded into the Widget, and it will be re-rendered. Usage of state in spandrel Widgets themselves have state: what Properties does this Widget have? Is it currently readonly or disabled? What form factor is it set to? This information can also be described in an object: WidgetState. As spandrel is dynamically constructing the configuration for a Widget, the Widget's own state is passed as the second argument to the spandrel function. This allows you to change the widget structure in response to the current state of the widget. In most cases, you'd want the enabled/readonly state of the parent widget to propagate to the children. For instance, if UserEditor is readonly, then you also want the editors for user.name and user.enabled to be readonly: const UserEditor = spandrel((user, state) =&gt; { const { enabled, readonly } = state; return { name: { dom: '&lt;span/&gt;', value: user.name, enabled, readonly }, enabled: { dom: '&lt;span/&gt;', value: user.enabled, enabled, readonly }, }; }); But because this is such a common use case, spandrel will default the child widgets to inherit the readonly/enabled state from the parent. So if you're only using enabled/readonly to propagate them down, you can leave them out! The below example is completely equivalent to the one above. const UserEditor = spandrel((user) =&gt; { return { // these will be enabled/readonly based on the parent name: { dom: '&lt;span/&gt;', value: user.name }, enabled: { dom: '&lt;span/&gt;', value: user.enabled }, }; }); You can also make explicit that readonly and enabled are inherited from their parents by setting them to the string inherit. This is only for readability purposes, and this example is functionally equivalent to the two above: const UserEditor = spandrel((user) =&gt; { return { name: { dom: '&lt;span/&gt;', value: user.name, readonly: 'inherit', enabled: 'inherit' }, enabled: { dom: '&lt;span/&gt;', value: user.enabled, readonly: 'inherit', enabled: 'inherit' }, }; }); You can also set properties to 'inherit'! This will cause the child widget to inherit the properties from its parent, with one notable exception: a uxFieldEditor property will never be inherited. If it did, then if you specified a uxFieldEditor slot facet to choose a particular editor type, that would then propagate down and cause all of your child editors to be set to the same type, and you'd get caught in an infinite loop. Please note: when setting properties, readonly, or enabled to 'inherit', their values will inherit from the owner (the widget who's rendering the spandrel data) - not necessarily the widget directly above it in the tree. In this example, they inherit from UserEditor, no matter how deep in the tree 'inherit' is declared. One notable attribute of the state object is self. This is passed so that your spandrel function can still reference your own Widget instance and call methods on it, while still being implemented as an arrow function. spandrel.jsx spandrel provides a custom JSX pragma that will let you use JSX to make it even easier to define your Widget structures. Simply insert the pragma: /** @jsx spandrel.jsx */ and you can use JSX to define your HTML and widget structures. It's important to understand that JSX is not React and the use of spandrel.jsx does not incorporate React into your application. It does not make use of a virtual DOM. It simply provides a more intuitive way of defining your HTML and Widget structure than a tree of JSON objects. Please note that JSX requires Babel transpilation to function. The easiest way to incorporate Babel into your module will be to use grunt-niagara version 2 or higher. Using spandrel.jsx to create Widgets Let's take a look at the simplest example: a Widget that consists only of HTML - no child Widgets. const HelloWorldWidget = spandrel(&lt;span&gt;Hello world!&lt;/span&gt;); The contents of the Widget will now be a span containing the string \"Hello World\". At build time, the JSX itself will be compiled out, and the spandrel.jsx function will convert it into valid spandrel data. You can consider the example above to be roughly equivalent to: const HelloWorldWidget = spandrel([ { dom: '&lt;span&gt;Hello world!&lt;/span&gt;' } ]); One difference between bajaux Widgets and React components is that React components generate their own top-level DOM element, while bajaux Widgets are mounted in an existing, empty DOM element and generate the contents of that element. As such, the spandrel render function can actually return an array of elements - the children of your Widget's own element. const HelloWorldWithLabelWidget = spandrel(() =&gt; [ &lt;label&gt;My message is...&lt;/label&gt;, &lt;span&gt;Hello world!&lt;/span&gt; ]); Defining the attributes of a DOM element works much the same as in React. Use className instead of class. style can be either a string, or an object literal. If using an object literal, then the values can be either strings or numbers. Any non-numeric falsy values will be ignored. Using JavaScript values instead of strings can be done by surrounding them with curly braces. const StyledHelloWorldWidget = spandrel(() =&gt; ( &lt;label className=\"helloWorldLabel\" style=\"padding: 5px;\"&gt; &lt;span style={{ color: 'red', backgroundColor: calculateBackground(), opacity: calculateOpacity() }}&gt; Hello world! &lt;/span&gt; &lt;/label&gt; )); function calculateBackground() { return 'yellow'; } function calculateOpacity() { return 0; } In this example, note that the child elements of a DOM element can be denoted as a JavaScript array of JSX elements, using curly braces. const SlotList = spandrel((component) =&gt; ( &lt;table className=\"slot-list-table\"&gt; &lt;tr&gt;&lt;td&gt;Slot Name&lt;/td&gt;&lt;td&gt;Slot Display&lt;/td&gt;&lt;/tr&gt; { component.getSlots().toArray().map((slot) =&gt; { return &lt;tr&gt; &lt;td&gt;{ slot.getName() }&lt;/td&gt; &lt;td&gt;{ component.getDisplay(slot) }&lt;/td&gt; &lt;/tr&gt;; }) } &lt;/table&gt; )); Each element doesn't only have to be a DOM element though - it can be a Widget! You can embed bajaux Widgets right into your JSX alongside your DOM elements. The tag name of the element just needs to correspond to a Widget constructor available in your code. The configuration of the Widget element will look very similar to what gets passed to WidgetManager or fe.buildFor - it supports properties, enabled, formFactor, etc. className and style are supported too - they will be applied to the DOM element created to house the Widget. By default, the element will be a div - but you can specify what kind of element you want with the tagName attribute. (If value is omitted and your widget is a dynamic spandrel widget, it will still render with a value of null.) const NumberInput = spandrel((number, { properties }) =&gt; { const { min = '', max = '' } = properties; return &lt;input type=\"number\" min={ min } max={ max } value={ number } /&gt;; }); const PercentageInput = spandrel((percent) =&gt; { return [ &lt;NumberInput tagName=\"span\" value={ percent } properties={{ min: 0, max: 100 }} formFactor=\"mini\" /&gt;, &lt;span&gt;%&lt;/span&gt; ]; }); When adding a Widget as an element as above, it requires you to have already imported or defined that widget's constructor. But what about one of spandrel's core functions: dynamically looking up a widget based on the value? This can be achieved with the special &lt;any&gt; tag. When spandrel encounters the &lt;any&gt; tag, it will perform a dynamic lookup based on the tag's value and other properties, find the appropriate Widget constructor, and construct an instance in its place. Be sure to specify a lookup strategy, or provide a WidgetManager instance, so that spandrel will know how to perform the dynamic lookup. const UserProperties = spandrel((user) =&gt; { return &lt;table&gt; &lt;tr&gt; &lt;td&gt;Username:&lt;/td&gt; &lt;td&gt; &lt;any value={ user.username } /&gt; &lt;/td&gt; &lt;/tr&gt; &lt;tr&gt; &lt;td&gt;Enabled:&lt;/td&gt; &lt;td&gt; &lt;any value={ user.enabled } /&gt; &lt;/td&gt; &lt;/tr&gt; &lt;/table&gt;; }, { strategy: 'niagara' }); Remember how each member of a spandrel config has a key (an array index or a property name on an object literal)? The key can be explicitly provided using the spandrelKey attribute as well. This makes the process of querying widgets quite straightforward: class UserEditor extends spandrel((user) =&gt; ( &lt;div class=\"userEditor-wrapper\" spandrelKey=\"wrapper\"&gt; &lt;StringEditor value={ user.name } spandrelKey=\"name\" /&gt;, &lt;BooleanEditor value={ user.enabled } spandrelKey=\"enabled\" /&gt; &lt;/div&gt; )) { doRead() { // without the explicit keys, we'd query \"0/0\" and \"0/1\". return Promise.all([ this.queryWidget('wrapper/name').read(), this.queryWidget('wrapper/enabled').read() ]) .then(([ name, enabled ]) =&gt; ({ name, enabled })); } } Remember - your JSX data is not an actual DOM element, but it will be used to create an actual DOM element. Sometimes you will want to make changes to the actual DOM element before it is finally rendered and inserted into the actual document. This can be done using the $init attribute, which is a function that receives a real live HTMLElement and may make changes to it before it is inserted. (Note that $init must be synchronous.) const StyledLabel = spandrel((string, { properties }) =&gt; { const { background } = properties; return ( &lt;label $init={ (el) =&gt; background.applyBackgroundToElement(el) }&gt; { string } &lt;/label&gt; ); }); return new WidgetManager().buildFor({ dom: $('#labelGoesHere'), type: StyledLabel, value: 'Hello World', properties: { background: Brush.make('yellow') } }); Incorporating event handlers (new in Niagara 4.12) Event handlers can be included directly in your JSX. There are several ways this can be done: DOM Events Standard DOM events can be listened for using onclick, onchange, etc. const ButtonClicker = spandrel(() =&gt; { return &lt;button type=\"button\" onclick={() =&gt; alert('click')}&gt;Click me&lt;/button&gt;; }); bajaux events bajaux events like LOAD_EVENT and MODIFY_EVENT can be armed using onUxLoad, onUxModify, etc. The names of these event handlers start with onUx, and the rest of the name is derived from the actual event name. See module:bajaux/events for a listing of all available bajaux events. ENABLE_EVENT can be listened for with onUxEnable; LOAD_FAIL_EVENT can be listened for with onUxLoadFail, and so on. const AlwaysValidating = spandrel((value) =&gt; { return &lt;any value={value} onUxModify={(e, ed) =&gt; ed.validate()} /&gt;; }, { strategy: 'niagara' }); The handlers for these events will receive the event as the first argument and the Widget that triggered the event as the second argument. Certain events will cause a third argument or more to be included; for instance onUxLoadFail will get the error that caused the failure as the third argument. There is one special event handler included to solve a very common use case. When a child widget is modified, often we want to do something with the newly entered value. onUxModifiedValue will receive the new value as the first argument, saving us a call to read(). const ModificationLogger = spandrel((value) =&gt; { return &lt;any value={value} onUxModifiedValue={(newValue) =&gt; log(newValue)} /&gt;; }, { strategy: 'niagara' }); Arbitrary events Certain other events may have special characters in their names that make them impossible to translate to an HTML attribute like onclick or onUxLoad. Or you may want to refer to them by variable name instead of remember their actual event name. For these, you can use the generic on attribute and pass it handlers for events with arbitrary names. One way to do this is via an object literal. The keys of the object are the event names, and the values are the event handlers. Again, the arguments to the handler are the event, the Widget that triggered the event, and any additional arguments to the event. const { CELL_ACTIVATED_EVENT } = Table; const TableContentViewer = spandrel((tableModel) =&gt; { return &lt;Table value={tableModel} on={{ [CELL_ACTIVATED_EVENT]: (e, table, row) =&gt; showDetailsDialog(row.getSubject()) }} /&gt;; }); If you need to add a selector to perform event delegation, you can set the value of on to an array with three members: the event name, the spandrel selector, and the event handler function. (If you need more than one of these delegated handlers, set on to an array of these arrays.) const DelegatedButtonListener = spandrel((component) =&gt; { const EVENT_NAME = 'click'; // note that the selector is a *spandrel* selector as used in queryWidget(), not a CSS selector. return &lt;div on={[ EVENT_NAME, '*/slotDisplay', (e, ed) =&gt; alert('slot value: ' + ed.value()) ]}&gt; { component.getSlots().properties().toArray() .map((prop, i) =&gt; ( &lt;div spandrelKey={ `row${ i }`}&gt; &lt;label spandrelKey=\"slotDisplay\" value={component.get(prop)}&gt;{prop.getName()}&lt;/label&gt; &lt;/div&gt; )) } &lt;/div&gt;; }); Notes on spandrel event handlers spandrel event handlers look similar to jQuery event handlers at first glance, but they are not the same. jQuery event handlers are synchronous, which means that they cannot respond to a returned promise. Any asynchronous error handling in a jQuery event handler must be done manually: dom.on('click', () =&gt; { doSomethingAsync() .catch((err) =&gt; logSevere(err)); }); The only thing that can be meaningfully returned from a jQuery event handler is false, which indicates that the event should be cancelled. When performing async work in a spandrel event handler, you can return a Promise. spandrel will wait for the promise to be settled, and it will log any rejections to the bajaux.spandrel log. spandrel(() =&gt; { return &lt;div onclick={() =&gt; doSomethingAsync()} /&gt;; }); A spandrel event handler will also respect a return false and cancel the event, the same way jQuery does. Inline validation (new in Niagara 4.13) A spandrel widget will often place constraints on its child widgets, like so: class PercentPicker extends spandrel((number) =&gt; { return &lt;any value={number} properties={{ min: 0, max: 100 }}/&gt;; }) {} But by default, spandrel will not just automatically validate every child widget in its whole structure. Before inline validation, you would need to manually specify a validator function: class PercentPicker extends spandrel((number) =&gt; { return &lt;any spandrelKey=\"number\" value={number} properties={{ min: 0, max: 100 }}/&gt;; }) { constructor() { super(...arguments); this.validators().add(() =&gt; this.queryWidget('number').validate()); } } As of 4.13, you can simply add the validate keyword to child widgets that you wish to validate. This can simply be a boolean validate property, which will tell spandrel that the child widget must validate using its own built-in validation behavior. For example, a numeric editor will typically validate against the min and max properties: class PercentPicker extends spandrel((number) =&gt; { return &lt;any value={number} validate properties={{ min: 0, max: 100 }}/&gt;; }) {} Or, you can set validate to a validator function. This will both indicate to spandrel that this editor must be validated, and adds additional validation. This can throw an error or return a rejected Promise to fail validation. Please note that this function will not replace any existing validation on the widget - it only adds additional validation. class EvenPercentPicker extends spandrel((number) =&gt; { return &lt;any value={number} properties={{ min: 0, max: 100 }} validate={(val) =&gt; { // the 0-100 check is already handled by the editor's built-in validation // that respects the min/max properties. if (val % 2) { return Promise.reject(new Error(val + ' must be an even number')); } }} /&gt;; }) {} Extending a spandrel superclass The general wisdom is that composition is preferable to inheritance, notably when it comes to UI elements. This also holds true for spandrel. However, you may sometimes need to inherit from a spandrel class. A couple of possible reasons why: Since the Widget-based mechanics of bajaux are different from many other front-end frameworks, it may sometimes be difficult to use composition without introducing unnecessary DOM elements. You might be implementing Widgets to mirror a Java-based class hierarchy, and inheritance makes it easier to reason about that one-to-one relationship between bajaux and bajaui Widgets. There are two reasons to extend a spandrel superclass: to render differently, or to override methods. To extend a spandrel class for the purposes of overriding methods, there's no magic here - simply extend the class the JavaScript way. class Button extends spandrel((text, { self }) =&gt; { return &lt;button type=\"button\" onClick={() =&gt; self.handleClick()}&gt;{ text }&lt;/button&gt;; }) { handleClick() { alert('you clicked it!'); } } class SuperButton extends Button { handleClick() { alert('you SUPER clicked it!'); } } But sometimes you might want to extend a spandrel class in such a way that causes it to render differently. In this case, spandrel needs to get the superclass baked into its render process, and simply extending the class isn't quite enough. As part of the second argument to spandrel itself - the same object that receives other configuration like manager and strategy described above - you'll need to add the extends property, with the superclass constructor: const Label = spandrel(myLabelRenderFunction); const SpecialLabel = spandrel(mySpecialLabelRenderFunction, { extends: Label }); By extending it in this way, you'll get the ability to actually tweak the superclass's render function. As part of the state parameter (the second parameter passed to the spandrel function), you'll receive a renderSuper function, which renders the spandrel data as implemented by the superclass. As a parameter to renderSuper, you can pass a function that will receive the widget state. You can make tweaks to this state and return a new state to cause the superclass to render differently. Here's an example: const StyledLabel = spandrel((text, { properties }) =&gt; { const { background } = properties; return &lt;label style={{ background }}&gt; { text } &lt;/label&gt;; }); const YellowLabel = spandrel((text, { renderSuper }) =&gt; { return renderSuper((state) =&gt; { state.properties.background = 'yellow'; return state; }); }, { extends: StyledLabel }); In addition, renderSuper will resolve the actual spandrel data as rendered by the superclass, so you can change it directly. In general, this is not recommended - if you need to customize the actual HTML as rendered by the superclass, it's better to add support for more properties in the superclass so that the behavior can be configured purely through widget state. Customized Widget State (New in Niagara 4.12) Dynamic spandrel widgets are designed to update themselves whenever new data is available through a re-rendering process. This process can be severely simplified and described with the following code snippet: spandrelWidget.read() .then((currentValue) =&gt; spandrelWidget.render(currentValue)); The current value is read out, then fed directly back into the widget's render process to cause it regenerate all of its spandrel data and to update itself in its entirety. But in many cases, the currently loaded value is not enough information to fully update the widget. For example, take a look at the field editor for a gx:Font. If you load in the font 12pt Arial bold, the field editor knows enough to load Arial into the name field, 12 into the size field, and to check the Bold checkbox. Now, take a look at the Null checkbox. If you check that, it indicates you've chosen the null font (that is, no font specified at all). This causes the name, size, bold, italic, and underline checkboxes to blank themselves out, and the field editor to read out the font null. So far so good. Now, uncheck the Null checkbox. What happens now? Because we blanked everything out in the previous step, if we simply read out the current font and passed it directly back into the rerender process, as described above, spandrel would have forgotten your previous entries (Arial, 12, bold). This is because during the rerender, after reading out Font.NULL, when it got passed back in, nowhere in that value is Arial, 12, or Bold. The editor would have to start over with some default settings. This would be irritating to your user if, out of curiosity, they checked and unchecked Null - they'd lose all their work. To get around this, there needs to be some other place than \"the currently loaded value\" to store the state of the widget. Niagara 4.12 introduces the concept of state to store additional, current information about the widget, on top of the currently loaded value. State binding can be done regardless of what kind of data you are returning from the spandrel function (whether array, object literal, or JSX) - but it's easiest to reason about when using JSX, so all the following examples will use JSX. Take another look at the second argument to the spandrel function - the one that includes information about the widget itself: const UserEditor = spandrel((user, { readonly, properties, formFactor }) =&gt; { const { trueText, falseText } = properties; return [ &lt;StringEditor readonly={ readonly } value={ user.value } /&gt;, &lt;BooleanEditor readonly={ readonly } value={ user.enabled } properties={{ trueText, falseText }} /&gt; ] }); As described above, this argument provides information about the current state of the widget - its readonly status, Properties, form factor, etc. But with the State API, you can define and store any kind of state data you want! The widget state is a great place to store information about what values are currently loaded into child widgets, because the state persists across new values as they are loaded, and across rerenders as well. If the user makes a change to a child widget, and you store the newly entered value in the state, and then a rerender causes that child widget to disappear or be overwritten - that value remains stored in the state for you to use later. The Font editor uses the state exactly for this purpose. If you type Arial into the name field, then name: 'Arial' goes into the widget state, and stays there even if the Null checkbox causes it to be blanked out. When Null is unchecked, name: 'Arial' comes back out of the widget state, so the user can pick up where they left off. Updating the state can be done manually, as shown below: const { MODIFY_EVENT } = events; // bajaux/events class FontEditor extends spandrel((font, { name, enabled, properties }) =&gt; { // note the \"name\" property is not part of core widget state - this is specific to FontEditor. return /* some html */; }) { doInitialize(dom) { dom.on(MODIFY_EVENT, '.name-editor', (e, ed) =&gt; { ed.read() .then((name) =&gt; { // store the name the user typed in state. // now we have it for reference even if the // \"null\" checkbox wipes out the name editor. return this.state({ name }); }) .then(() =&gt; this.rerender()) .catch(logSevere); }); } } But spandrel provides some built-in API for automatically binding child widgets to state, so you have much less code to think about. State Binding By applying state binding to a child widget, you are asking spandrel to continually keep the current value of that widget in state. As the user makes changes to that child widget, its current value will be applied to state, even as they are typing and clicking. As the widget then rerenders itself in response to changes in data, the most up-to-date changes from the user will be known (even if they are hidden or overwritten in the UI itself). Another way to think about this: if a widget will change the way it renders itself in response to new values that the user enters into child widgets, then bind those child widgets to state, and the most recently entered values will always be known (and entered into state) at render time. (Otherwise, you'd have to manually call read() on every widget whose value you care about!) For an example, let's consider a highly simplified version of the Font field editor - one that only has the font name and the Null checkbox. class FontEditor extends spandrel((font) =&gt; { const isNull = font.isNull(); return [ &lt;StringEditor value={ isNull ? '' : font.getName() } enabled={ !isNull } spandrelKey=\"name\" /&gt;, &lt;BooleanEditor className=\"nullEditor\" value={ isNull } spandrelKey=\"isNull\" /&gt; ]; }) { doInitialize(dom) { dom.on(MODIFY_EVENT, '.nullEditor', () =&gt; { this.rerender(); }); } doRead() { return Promise.all([ this.queryWidget('name').read(), this.queryWidget('isNull').read() ]) .then(([ name, isNull ]) =&gt; isNull ? Font.NULL : Font.make({ name })); } } This has the problem of immediately forgetting the user-entered font name as soon as the Null checkbox is checked. In addition, we have to manually rerender whenever the Null checkbox is checked or unchecked. By using state binding, we can solve both of these problems. With state binding, we can bind both child editors to values that live in the widget state. This means that changes to either bound editors will result in the widget state being updated, and then the widget being rerendered. class FontEditor extends spandrel((font, { name, isNull }) =&gt; { return [ &lt;StringEditor value={ isNull ? '' : font.getName() } enabled={ !isNull } bind spandrelKey=\"name\" /&gt;, &lt;BooleanEditor value={ isNull } bind spandrelKey=\"isNull\" /&gt; ]; }) { doRead() { return Promise.all([ this.queryWidget('name').read(), this.queryWidget('isNull').read() ]) .then(([ name, isNull ]) =&gt; isNull ? Font.NULL : Font.make({ name })); } } Observe the bind keyword on each child editor. By itself, bind tells spandrel to set the state value to the same key as spandrelKey. Because the StringEditor has bind spandrelKey=\"name\", the name property of the widget state will get set every time it is modified. If you wished to use a different state key than the spandrelKey, you could specify bindKey instead. However, this editor still won't work. Remember, the widget state is an entirely separate data structure than the value that gets loaded in (in this case, the Font). So as it is, the state will be unpopulated on first render, and we'll just get a blank/broken editor! How can we initialize the widget state so we can render correctly the first time? The answer is the toState function. This function receives the value being loaded in (the Font) and returns the initial state of the widget based on that value. It will automatically be called for you as part of the spandrel lifecycle - all you need to do is implement it. Similarly, we're doing a significant amount of extra work with doRead. We shouldn't need to query the widgets out and read their values - we already know those values from the state (because the widgets are bound!). Can't we just read the widget's current info out of the state? You guessed it - fromState is the callback to go in the opposite direction. It takes the widget's current state and allows you to build the value to resolve doRead with. Again, just implement it - if you do, it takes the place of doRead. All together: class FontEditor extends spandrel((font, { name, isNull }) =&gt; { return [ &lt;StringEditor value={ isNull ? '' : name } enabled={ !isNull } bind spandrelKey=\"name\" /&gt;, &lt;BooleanEditor value={ isNull } bind spandrelKey=\"isNull\" /&gt; ]; }) { toState(font) { return { name: font.getName(), isNull: font.isNull() }; } fromState({ name, isNull }) { return isNull ? Font.NULL : Font.make({ name }); } } Now you have a Font editor that is impervious to data loss as the string editor gets wiped out, and has the current data values always at hand in the widget state! Note that you may want to avoid the bind keyword on some child widgets if that widget requires a network request on load. In those cases, it is better to override doRead() and avoid those extra network requests that would come with a widget modification when the bind keyword is present. Modified widgets, dirty changes, and the lax keyword So here's a problem: spandrel widgets are very likely to continually rerender themselves as the user makes changes. As rerenders occur, new data values get loaded into your spandrel widget's children. If the user is currently typing in one of those child widgets, what happens if a new value gets loaded in? The user would lose their changes right at the moment they were typing, which would be extremely annoying! Because of this, there are two cases where spandrel will decline to load a new value into a widget. The first case: if a widget has focus - i.e., the user's cursor is currently in this widget, and they're in the process of typing something. spandrel will not overwrite the value of a modified, focused widget - no matter what. (Otherwise, a user could be actively in the process of typing and see their changes get immediately wiped out, which would be a terrible experience.) The second case: if the widget is modified. By default, spandrel will not load a new value into a widget that has user-entered changes, as in most cases, the user will not want to see those changes overwritten. However, there are many cases where you will want changes to apply even if the target widget is modified. For instance, in our FontEditor above: if the user types in a font name, but then checks the Null checkbox, we still want that font name editor to blank out, even though it's modified! Fortunately, we as developers have several ways to make that happen. Call load(). spandrel only skips loading modified widgets during a rerender. If you have a brand-new value to load into your spandrel widget, just call yourSpandrelWidget.load(newValue) and that value will be loaded in - even if the user has made changes. Make a \"dirty change.\" To update state, you don't always have to use state binding: you can also just set the state yourself by calling the state() function. this.state({ foo: 'bar' }); By manually setting the state like this, it's like you, as the developer, are saying \"I want the widget bound to this state key to receive this value - even if it's modified.\" This marks that particular state key as \"dirty.\" On the next re-render (which will be triggered automatically by the call to this.state()), the widget bound to that key is going to get that new value, modifications or no. This applies for one rerender exactly - so if you want to overwrite that modified widget a second time, you'll have to call this.state() again. (Note that this only works for bound state keys. If \"foo\" were not bound to a widget, this would still set the state, but wouldn't overwrite any widgets or trigger a rerender.) Mark it lax. This is a little more \"shotgun\" approach than the previous, but works in many circumstances. Simply add the lax attribute to your JSX widgets (or lax: true if building the spandrel data by hand). This will mark that widget as open for overwriting, regardless of whether it's modified or not: class FontEditor extends spandrel((font, { name, isNull }) =&gt; { return [ &lt;StringEditor value={ isNull ? '' : name } lax bind spandrelKey=\"name\" /&gt;, &lt;BooleanEditor value={ isNull } bind spandrelKey=\"isNull\" /&gt; ]; }) { /* ... */} Mark it unmodified. If neither of the above approaches works for you, your use case may call for child widgets to be overwritten if modified... some times, and not others. In this case, you will simply need to explicitly mark the widgets as unmodified at the appropriate time by calling setModified(false) on them. (If you have other use cases that you feel spandrel could do a better job of handling, please let us know!) Broken state: when widgets fail to read When you bind a child widget to state, it's possible for that widget to completely fail to read out a value. For instance, the user might type \"hello world\" into a numeric editor - there's no way to read a numeric value from \"hello world\", so spandrel will fail to propagate that value into state, and your state will become inconsistent. In this case, state will still work - but if you attempt to read that bound value from the state object, an error will be thrown. Just be aware of this, in case you want to completely ignore the value of certain widgets on read. If you try to pull that bound value out of state when you don't really need it, your widget might fail to read out a value when it might otherwise work. class AgePrompt extends spandrel((user, { age, sharesAge }) =&gt; { return &lt;div&gt; &lt;div&gt; &lt;span&gt;I wish to share my age:&lt;/span&gt; &lt;BooleanEditor bind spandrelKey=\"sharesAge\" value={ sharesAge } properties={{ trueText: 'Yes', falseText: 'No' }} /&gt; &lt;/div&gt; &lt;div&gt; &lt;span&gt;My age is:&lt;/span&gt; &lt;NumericEditor bind spandrelKey=\"age\" value={ age } enabled={ sharesAge } /&gt; &lt;/div&gt; &lt;/div&gt;; }) { toState(user) { // data structure of a user object: { sharesAge: [boolean], age: [number] } return user; } fromState(state) { const { sharesAge } = state; const user = { sharesAge }; // if the user typed \"hello world\" into the numeric field, // then accessing state.age here will throw an error. // if they choose not to share their age, we don't want to // force them to type a valid number for it anyway. user.age = sharesAge ? state.age : null; return user; } } Do I really need to use state? In many cases, no. If the currently loaded value is enough information to fully capture the state of your widget, then the regular rerender process will do just fine. But if the widget keeps more information than can be captured in the loaded value, then state is a great place to store it. A typical, prominent use case for when state will come in handy is: Your widget has multiple child widgets. A change to one child widget will also cause changes to other child widgets. You do not want to lose any user-entered changes to those other child widgets that would be overwritten by those changes. Those other user-entered changes can then be socked away in state to persist across rerenders. Frequently Asked Questions Is the old bajaux API going away? Not at all. spandrel is just an additional API, on top of bajaux itself, that eases the construction and updates of nested trees of Widgets. Your existing Widgets will continue to function with no changes. How can I debug what spandrel is doing under the covers? Turn up the bajaux.spandrel log to FINEST. (Set the DebugService's Remote Logging property to true to apply this setting in the browser.) You will start getting debug information in the browser. Can I use React components in conjunction with spandrel? At the moment, spandrel only supports bajaux Widgets. But it is completely possible to create a \"wrapper\" Widget that mounts your React component inside of its own DOM element, and that wrapper Widget will work with spandrel. Why is my spandrel widget not rendering anything at all? Remember a dynamic widget renders itself according to the value that is loaded. Therefore, until load() is called, it will not render anything. Ensure you are calling load(), or providing a defined value argument to buildFor(). I'm manually modifying the DOM after rendering. Why doesn't a rerender wipe my changes? spandrel's diffing process compares the DOM as returned by the first call to the render function, against the DOM as returned by the second call to the render function. It figures out what the differences are, and applies those differences to the real DOM. If you apply your own manual modifications to the DOM, independent of the render process, then spandrel won't know you've made them. Your manual modifications will persist, unless the render process's own changes overwrite them. The best way to address this is to perform your manual modifications in the doLayout callback, and to ensure that they are consistently applied regardless of whether spandrel applies any changes to the DOM or not. doLayout will be called after each render. (Note: This behavior was introduced in 4.11. In 4.10 and previous, spandrel would often completely remove the style and/or class of DOM elements, regardless of what individual changes were made, so manual modifications would often be wiped on a rerender. This caused a number of problems with child widgets, so now only the results of the render function are diffed.) I'm rerendering, but widgets won't update if they've been modified by the user. This is by design. Due to the asynchronous nature of bajaux Widgets, widgets that have been modified by the user can't be re-rendered on every modification (attempting to do so results in some very jumpy and/or broken behavior). Therefore, by default, any modified widgets (such as string editors you're currently typing in) won't respond to re-render requests. To address this, see the lax property and other approaches, as described in the State Binding section. Some of my HTML attributes aren't applying correctly! When using JSX to create DOM elements, spandrel does not apply the attributes all at once: it applies them one after another. Therefore, in some situations, the order of attributes matters, even when it would make no difference when parsing a simple HTML string. Here's an example: &lt;input type=\"range\" min=\"0\" max=\"1000\" value=\"500\"/&gt; In both regular HTML and spandrel's JSX implementation, this works fine. Now compare it with this: &lt;input type=\"range\" min=\"0\" value=\"500\" max=\"1000\"/&gt; Because spandrel sets attributes sequentially, when the value attribute is set, the min attribute has been set but max has not. An input tag with a min but no max will default the max to 100 (MDN). So the value gets set to 100, not 500! This is a case where ordering matters: the max must be set first. I'm calling load() with new data, but spandrel is not actually loading my new values. When deciding whether values have changed, spandrel will diff \"the value returned by the previous render\" against \"the value returned by this render.\" Most of the time this is straightforward: const NumberEditor = spandrel((num) =&gt; &lt;span&gt; &lt;label&gt;Enter number:&lt;/label&gt;&lt;any value={num} /&gt; &lt;/span&gt;); numberEditor.load(1) .then(() =&gt; numberEditor.load(2)); The first time it renders, the value being loaded into the child editor will be 1, and the second time it renders, it will be 2 - so spandrel knows that the new value is different and needs to be reloaded into the child editor. Where it can get dicey is when objects are instance-equal. Consider this example, in which our intention is to load up a user with a name property, change their name, and reload to reflect the new values. // the implementation of NameEditor is not interesting. const NameEditor = spandrel((name) =&gt; &lt;span&gt; &lt;label&gt;First name:&lt;/label&gt;&lt;any value={name.first} /&gt; &lt;label&gt;Last name:&lt;/label&gt;&lt;any value={name.last} /&gt; &lt;/span&gt;); // examine the behavior of UserEditor. const UserEditor = spandrel((user) =&gt; &lt;span&gt; &lt;NameEditor value={user.name} /&gt; &lt;/span&gt;); const user = { name: { first: 'Moe', last: 'Howard' } }; return userEditor.load(user) .then(() =&gt; { user.name.first = 'Larry'; user.name.last = 'Fine'; return userEditor.load(user); }); It sure looks like it should reload the editor with the new values, but it won't! When the UserEditor does the diff to see if the value to load into the NameEditor has changed, remember it is checking \"the value returned by the previous render\" against \"the value returned by the current render.\" They're the same value - in this example, user.name is always instance-equal. When we set the first and last properties of user.name, we're not just changing \"the value returned by the current render\" - we're also changing \"the value returned by the previous render\"! So when spandrel does the diff, the values are the same, and it will not see any reason to load anything into the NameEditor. Any one of the following approaches will work instead: // we could ensure that user.name refers to a brand new instance when passing it to UserEditor. return userEditor.load(user) .then(() =&gt; { user.name = { first: 'Larry', last: 'Fine' }; return userEditor.load(user); }); // even better, we could pass a fresh user altogether (think \"stateless\"): return userEditor.load({ name: { first: 'Moe', last: 'Howard' } }) .then(() =&gt; { return userEditor.load({ name: { first: 'Larry', last: 'Fine' } }); }); // we could even make it UserEditor's job to ensure that NameEditor receives fresh values // no matter what happens to the user object externally. const UserEditor = spandrel((user) =&gt; { const { first, last } = user.name; return &lt;span&gt;&lt;NameEditor value={{ first, last }} /&gt;&lt;/span&gt;; }); I'm calling value() from within doRead(), and it's breaking my rerendering! When you call load() on a bajaux Widget to load in a new value, this.value() starts to immediately return that new value - even before any of the loading work is done! If your spandrel widget is calling this.value() inside of doRead(), your diffing process may not work correctly when a new value is loaded in. Instead of referencing \"static\" parts of the widget's value by calling this.value(), consider storing that information in state instead. This way, when you load a new value, the widget knows its previous values when it rerenders, and the diffing process will work correctly. Definitions Dynamic spandrel Widget: a spandrel Widget whose structure is determined by its current properties, and what value is loaded in. Its structure will be built in doLoad because it changes based on the value. JSX: a library that converts HTML in .js files into JavaScript code. It works at compile time. spandrel uses it to convert HTML strings into spandrel data. render: describes the full cycle of a spandrel widget, from when it generates spandrel data (either statically, or dynamically, in response to a value being loaded), to when spandrel itself uses that data to update the document. re-render: when a dynamic widget changes (such as when a new value is loaded in, or its Properties change), its render function will be called again to generate an updated set of spandrel data, and spandrel will update the document itself, so the user sees the newest changes. Note that if the newly-generated spandrel data is exactly equal to the previous spandrel data, no changes will be made to the document at all. const Span = spandrel((string) =&gt; { // my render function return &lt;span&gt;{ string }&lt;/span&gt;; }); new WidgetManager.buildFor({ type: Span, value: 'hello' }) .then((ed) =&gt; { // when we load a new value, it triggers a re-render, which re-runs the // render function and updates the DOM. return ed.load('world'); }); render function: the function passed to spandrel that defines a dynamic widget. It will be called whenever a value is loaded in. It must resolve valid spandrel data, which spandrel itself will use to update the actual document. const Label = spandrel((string) =&gt; { // this is the _render function_ return &lt;label&gt;{ string }&lt;/label&gt;; }); spandrel data: describes the data returned from a render function. It defines a tree or array of Widgets and the DOM elements in which they should be initialized. This structure closely resembles the data passed to fe.buildFor, but allows for nesting. const Label = spandrel((string) =&gt; { // this is the _spandrel data_ being returned from the _render function_ const spandrelData = { dom: '&lt;label&gt;&lt;/label&gt;', kids: [ `&lt;span&gt;${ string }&lt;/span&gt;` ] }; return spandrelData; }); Static spandrel Widget: a spandrel Widget whose structure is the same in every single instance. It is not determined by a value loaded in. Its structure will be built in doInitialize because it never needs to change. × Search results Close "}}
    </script>

    <script type="text/javascript">
        $(document).ready(function() {
            Searcher.init();
        });

        $(window).on("message", function(msg) {
            var msgData = msg.originalEvent.data;

            if (msgData.msgid != "docstrap.quicksearch.start") {
                return;
            }

            var results = Searcher.search(msgData.searchTerms);

            window.parent.postMessage({"results": results, "msgid": "docstrap.quicksearch.done"}, "*");
        });
    </script>
</body>
</html>
