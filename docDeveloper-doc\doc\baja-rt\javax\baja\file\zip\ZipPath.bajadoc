<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.zip.ZipPath" name="ZipPath" packageName="javax.baja.file.zip" public="true">
<description>
Specialized FilePath which refers to a single entry in a zip file
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">29 Sep 03</tag>
<tag name="@version">$Revision: 2$ $Date: 3/28/05 9:22:57 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.file.FilePath"/>
</extends>
<!-- javax.baja.file.zip.ZipPath(java.lang.String, java.lang.String) -->
<constructor name="ZipPath" public="true">
<parameter name="scheme">
<type class="java.lang.String"/>
</parameter>
<parameter name="body">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.file.zip.ZipPath(java.lang.String) -->
<constructor name="ZipPath" public="true">
<parameter name="body">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.file.zip.ZipPath.newInstance(java.lang.String) -->
<method name="newInstance"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="body">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.file.FilePath"/>
</return>
</method>

<!-- javax.baja.file.zip.ZipPath.makePath(java.lang.String) -->
<method name="makePath"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<tag name="@since">Niagara 4.3U1</tag>
<parameter name="body">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdQuery"/>
</return>
</method>

<!-- javax.baja.file.zip.ZipPath.normalize(javax.baja.naming.OrdQueryList, int) -->
<method name="normalize"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="list">
<type class="javax.baja.naming.OrdQueryList"/>
</parameter>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
