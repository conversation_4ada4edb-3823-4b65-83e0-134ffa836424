<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetEventState" name="BBacnetEventState" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetEventState represents the Bacnet Event State&#xa; enumeration.&#xa; &lt;p&gt;&#xa; BBacnetEventState is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Craig Gemmill</tag>
<tag name="@version">$Revision: 5$ $Date: 12/19/01 4:35:58 PM$</tag>
<tag name="@creation">07 Aug 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;normal&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;fault&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;offnormal&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;highLimit&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lowLimit&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lifeSafetyAlarm&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetEventState.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetEventState"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetEventState"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.make(javax.baja.alarm.ext.BAlarmState) -->
<method name="make"  public="true" static="true">
<description>
Generate the correct ordinal mapping from BAlarmState&#xa; to BBacnetEventState.&#xa; &lt;p&gt;&#xa; The mapping is exact at this point.
</description>
<parameter name="alarmState">
<type class="javax.baja.alarm.ext.BAlarmState"/>
<description>
the BAlarmState.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetEventState"/>
<description>
the ordinal representing the corresponding BBacnetEventState.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.make(javax.baja.alarm.BSourceState) -->
<method name="make"  public="true" static="true">
<description>
Generate the correct ordinal mapping from BSourceState&#xa; to BBacnetEventState.&#xa; &lt;p&gt;&#xa; The mapping is exact at this point.
</description>
<parameter name="sourceState">
<type class="javax.baja.alarm.BSourceState"/>
<description>
the alarm source state.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetEventState"/>
<description>
the ordinal representing the corresponding BBacnetEventState.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.fromBAlarmState(javax.baja.alarm.ext.BAlarmState) -->
<method name="fromBAlarmState"  public="true" static="true">
<description>
Generate the correct ordinal mapping from BAlarmState&#xa; to BBacnetEventState.&#xa; &lt;p&gt;&#xa; The mapping is exact at this point.
</description>
<parameter name="alarmState">
<type class="javax.baja.alarm.ext.BAlarmState"/>
<description>
the BAlarmState.
</description>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal representing the corresponding BBacnetEventState.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.toSourceState() -->
<method name="toSourceState"  public="true">
<description>
Return this event state as a BSourceState.
</description>
<return>
<type class="javax.baja.alarm.BSourceState"/>
<description>
the BSourceState corresponding to this BACnet event state.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.isNormal(javax.baja.sys.BEnum) -->
<method name="isNormal"  public="true" static="true">
<description>
Is the given event state enum a &#x22;normal&#x22; event state?
</description>
<parameter name="eventState">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.isFault(javax.baja.sys.BEnum) -->
<method name="isFault"  public="true" static="true">
<description>
Is the given event state enum a &#x22;fault&#x22; event state?
</description>
<parameter name="eventState">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.isOffnormal(javax.baja.sys.BEnum) -->
<method name="isOffnormal"  public="true" static="true">
<description>
Is the given event state enum an &#x22;offnormal&#x22; event state?&#xa; Note that all non-normal and non-fault states are &#x22;offnormal&#x22;.
</description>
<parameter name="eventState">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.getEventTransitionBits(javax.baja.sys.BEnum) -->
<method name="getEventTransitionBits"  public="true" static="true">
<description/>
<parameter name="eventState">
<type class="javax.baja.sys.BEnum"/>
<description>
a BEnum using the BBacnetEventState range.
</description>
</parameter>
<return>
<type class="int"/>
<description>
the appropriate BACneEventTransitionBits field&#xa; for this event state.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.getEventTransition(javax.baja.sys.BEnum) -->
<method name="getEventTransition"  public="true" static="true">
<description/>
<parameter name="eventState">
<type class="javax.baja.sys.BEnum"/>
<description>
a BEnum using the BBacnetEventState range.
</description>
</parameter>
<return>
<type class="int"/>
<description>
the appropriate BACnetEventTransitionBits index for&#xa; referencing transition arrays, e.g. Event_Time_Stamps.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
String representation of this BEnum.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetEventState.NORMAL -->
<field name="NORMAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for normal.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventState.FAULT -->
<field name="FAULT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for fault.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventState.OFFNORMAL -->
<field name="OFFNORMAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for offnormal.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventState.HIGH_LIMIT -->
<field name="HIGH_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for highLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventState.LOW_LIMIT -->
<field name="LOW_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lowLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventState.LIFE_SAFETY_ALARM -->
<field name="LIFE_SAFETY_ALARM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lifeSafetyAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventState.normal -->
<field name="normal"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventState"/>
<description>
BBacnetEventState constant for normal.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventState.fault -->
<field name="fault"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventState"/>
<description>
BBacnetEventState constant for fault.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventState.offnormal -->
<field name="offnormal"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventState"/>
<description>
BBacnetEventState constant for offnormal.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventState.highLimit -->
<field name="highLimit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventState"/>
<description>
BBacnetEventState constant for highLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventState.lowLimit -->
<field name="lowLimit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventState"/>
<description>
BBacnetEventState constant for lowLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventState.lifeSafetyAlarm -->
<field name="lifeSafetyAlarm"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventState"/>
<description>
BBacnetEventState constant for lifeSafetyAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventState.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetEventState"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventState.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventState.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventState.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetEventState.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
