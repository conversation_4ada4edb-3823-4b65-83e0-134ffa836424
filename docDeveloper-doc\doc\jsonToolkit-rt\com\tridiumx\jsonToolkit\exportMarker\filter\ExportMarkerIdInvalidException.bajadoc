<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.exportMarker.filter.ExportMarkerIdInvalidException" name="ExportMarkerIdInvalidException" packageName="com.tridiumx.jsonToolkit.exportMarker.filter" public="true" category="exception">
<description>
Indicates an empty export marker id (which can be a valid configuration)
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="java.lang.Exception"/>
</extends>
<!-- com.tridiumx.jsonToolkit.exportMarker.filter.ExportMarkerIdInvalidException(java.lang.String) -->
<constructor name="ExportMarkerIdInvalidException" public="true">
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.ExportMarkerIdInvalidException(java.lang.String, java.lang.Exception) -->
<constructor name="ExportMarkerIdInvalidException" public="true">
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<parameter name="e">
<type class="java.lang.Exception"/>
</parameter>
<description/>
</constructor>

</class>
</bajadoc>
