<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetNotifyType" name="BBacnetNotifyType" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetNotifyType represents the Bacnet Notify Type&#xa; enumeration.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 5$ $Date: 11/28/01 6:14:21 AM$</tag>
<tag name="@creation">07 Aug 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;alarm&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;event&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ackNotification&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetNotifyType.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetNotifyType.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetNotifyType.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetNotifyType.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetNotifyType.make(javax.baja.alarm.ext.BNotifyType) -->
<method name="make"  public="true" static="true">
<description>
Generate the correct ordinal mapping from BNotifyType&#xa; to BBacnetNotifyType.&#xa; &lt;p&gt;&#xa; BNotifyType.alert is mapped to BBacnetNotifyType.event.
</description>
<parameter name="notifyType">
<type class="javax.baja.alarm.ext.BNotifyType"/>
<description>
the BNotifyType.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
<description>
the ordinal representing the corresponding BBacnetNotifyType.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetNotifyType.fromBNotifyType(javax.baja.alarm.ext.BNotifyType) -->
<method name="fromBNotifyType"  public="true" static="true">
<description>
Generate the correct ordinal mapping from BNotifyType&#xa; to BBacnetNotifyType.&#xa; &lt;p&gt;&#xa; BNotifyType.alert is mapped to BBacnetNotifyType.event.
</description>
<parameter name="notifyType">
<type class="javax.baja.alarm.ext.BNotifyType"/>
<description>
the BNotifyType.
</description>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal representing the corresponding BBacnetNotifyType.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetNotifyType.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
String representation of this BEnum.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetNotifyType.ALARM -->
<field name="ALARM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for alarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNotifyType.EVENT -->
<field name="EVENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for event.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNotifyType.ACK_NOTIFICATION -->
<field name="ACK_NOTIFICATION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ackNotification.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNotifyType.alarm -->
<field name="alarm"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
<description>
BBacnetNotifyType constant for alarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNotifyType.event -->
<field name="event"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
<description>
BBacnetNotifyType constant for event.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNotifyType.ackNotification -->
<field name="ackNotification"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
<description>
BBacnetNotifyType constant for ackNotification.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNotifyType.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetNotifyType.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
