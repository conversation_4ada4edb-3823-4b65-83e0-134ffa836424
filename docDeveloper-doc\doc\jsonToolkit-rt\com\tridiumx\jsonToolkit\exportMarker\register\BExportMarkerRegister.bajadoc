<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister" name="BExportMarkerRegister" packageName="com.tridiumx.jsonToolkit.exportMarker.register" public="true">
<description>
Allows registration of export markers in the station to facilitate setpoint routing and export filtering of&#xa; export marked control points.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<action name="resetAllMarkerIds" flags="oc">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;resetAllMarkerIds&lt;/code&gt; action.
</description>
<tag name="@see">#resetAllMarkerIds()</tag>
</action>

<action name="removeAllMarkers" flags="oc">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;removeAllMarkers&lt;/code&gt; action.
</description>
<tag name="@see">#removeAllMarkers()</tag>
</action>

<topic name="markerAdded" flags="s">
<eventType>
<type class="javax.baja.sys.BValue"/>
</eventType><description>
Slot for the &lt;code&gt;markerAdded&lt;/code&gt; topic.&#xa; Topic firing to reflect changes, allowing any unsubscribe or publishing of output by a schema
</description>
<tag name="@see">#fireMarkerAdded</tag>
</topic>

<topic name="markerRemoved" flags="s">
<eventType>
<type class="javax.baja.sys.BValue"/>
</eventType><description>
Slot for the &lt;code&gt;markerRemoved&lt;/code&gt; topic.
</description>
<tag name="@see">#fireMarkerRemoved</tag>
</topic>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister() -->
<constructor name="BExportMarkerRegister" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.resetAllMarkerIds() -->
<method name="resetAllMarkerIds"  public="true">
<description>
Invoke the &lt;code&gt;resetAllMarkerIds&lt;/code&gt; action.
</description>
<tag name="@see">#resetAllMarkerIds</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.removeAllMarkers() -->
<method name="removeAllMarkers"  public="true">
<description>
Invoke the &lt;code&gt;removeAllMarkers&lt;/code&gt; action.
</description>
<tag name="@see">#removeAllMarkers</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.fireMarkerAdded(javax.baja.sys.BValue) -->
<method name="fireMarkerAdded"  public="true">
<description>
Fire an event for the &lt;code&gt;markerAdded&lt;/code&gt; topic.&#xa; Topic firing to reflect changes, allowing any unsubscribe or publishing of output by a schema
</description>
<tag name="@see">#markerAdded</tag>
<parameter name="event">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.fireMarkerRemoved(javax.baja.sys.BValue) -->
<method name="fireMarkerRemoved"  public="true">
<description>
Fire an event for the &lt;code&gt;markerRemoved&lt;/code&gt; topic.
</description>
<tag name="@see">#markerRemoved</tag>
<parameter name="event">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.stopped() -->
<method name="stopped"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Removes all registered markers
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.clearRegister() -->
<method name="clearRegister"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.doRemoveAllMarkers(javax.baja.sys.Context) -->
<method name="doRemoveAllMarkers"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unused&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.ExportMarkerIdInvalidException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.doResetAllMarkerIds(javax.baja.sys.Context) -->
<method name="doResetAllMarkerIds"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unused&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.lookupExportMarker(java.lang.String) -->
<method name="lookupExportMarker"  public="true">
<description/>
<parameter name="id">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.ExportMarkerNotFoundException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.registerMarker(com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker) -->
<method name="registerMarker"  public="true">
<description>
Does not allow rooms with unset room number (-1) to register
</description>
<parameter name="exportMarker">
<type class="com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker"/>
<description>
The room to add to hashmap
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.ExportMarkerIdInvalidException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.unregisterMarker(com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker) -->
<method name="unregisterMarker"  public="true">
<description/>
<parameter name="marker">
<type class="com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.ExportMarkerIdInvalidException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.updateMarker(java.lang.String, com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker) -->
<method name="updateMarker"  public="true">
<description>
Remove the oldId from, and insert the new one to, markerMap
</description>
<parameter name="oldId">
<type class="java.lang.String"/>
<description>
Room to remove
</description>
</parameter>
<parameter name="marker">
<type class="com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker"/>
<description>
Room marker to add, name is extracted
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.ExportMarkerNotFoundException"/>
</throws>
<throws>
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.ExportMarkerIdInvalidException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.getMarkers() -->
<method name="getMarkers"  public="true">
<description/>
<return>
<parameterizedType class="java.util.Collection">
<args>
<type class="com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker"/>
</args>
</parameterizedType>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Dump debug information.
</description>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Add export indicator to icon
</description>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="bComponent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.resetAllMarkerIds -->
<field name="resetAllMarkerIds"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;resetAllMarkerIds&lt;/code&gt; action.
</description>
<tag name="@see">#resetAllMarkerIds()</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.removeAllMarkers -->
<field name="removeAllMarkers"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;removeAllMarkers&lt;/code&gt; action.
</description>
<tag name="@see">#removeAllMarkers()</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.markerAdded -->
<field name="markerAdded"  public="true" static="true" final="true">
<type class="javax.baja.sys.Topic"/>
<description>
Slot for the &lt;code&gt;markerAdded&lt;/code&gt; topic.&#xa; Topic firing to reflect changes, allowing any unsubscribe or publishing of output by a schema
</description>
<tag name="@see">#fireMarkerAdded</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.markerRemoved -->
<field name="markerRemoved"  public="true" static="true" final="true">
<type class="javax.baja.sys.Topic"/>
<description>
Slot for the &lt;code&gt;markerRemoved&lt;/code&gt; topic.
</description>
<tag name="@see">#fireMarkerRemoved</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.register.BExportMarkerRegister.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
