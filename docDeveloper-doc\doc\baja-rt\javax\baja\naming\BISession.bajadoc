<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.BISession" name="BISession" packageName="javax.baja.naming" public="true" interface="true" abstract="true" category="interface">
<description>
BISession is implemented by BObjects which &#xa; represent a session within a host.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">1 Apr 03</tag>
<tag name="@version">$Revision: 3$ $Date: 2/8/05 2:53:37 PM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<implements>
<type class="javax.baja.nav.BINavNode"/>
</implements>
<!-- javax.baja.naming.BISession.isConnected() -->
<method name="isConnected"  public="true" abstract="true">
<description>
Does the session have an active logical connection.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.BISession.connect() -->
<method name="connect"  public="true" abstract="true">
<description>
Open a logical connection.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.naming.BISession.disconnect() -->
<method name="disconnect"  public="true" abstract="true">
<description>
Terminate the connection, but leave the session &#xa; mounted in the navigation tree.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.BISession.close() -->
<method name="close"  public="true" abstract="true">
<description>
Close is defined as a disconnect, plus the session &#xa; should be unmounted from the navigation tree.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.BISession.getHost() -->
<method name="getHost"  public="true" abstract="true">
<description>
Get the parent host or null if unmounted.
</description>
<return>
<type class="javax.baja.naming.BHost"/>
</return>
</method>

<!-- javax.baja.naming.BISession.getAbsoluteOrd() -->
<method name="getAbsoluteOrd"  public="true" abstract="true">
<description>
Get the host absolute ord for this object.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.naming.BISession.getOrdInHost() -->
<method name="getOrdInHost"  public="true" abstract="true">
<description>
Get the ord of this session within its parent host.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.naming.BISession.getSessionContext() -->
<method name="getSessionContext"  public="true" abstract="true">
<description>
Get the Context to use for this session.
</description>
<return>
<type class="javax.baja.sys.Context"/>
</return>
</method>

<!-- javax.baja.naming.BISession.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
