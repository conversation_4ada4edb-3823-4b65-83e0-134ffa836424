<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.datatypes.BNextInstArgs" name="BNextInstArgs" packageName="com.tridium.bacnet.datatypes" public="true">
<description>
This class file specifies parameters to an invocation&#xa; of the BBacnetExportTable.getNextInst Action.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">20 Jun 2006</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="objectType" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</property>

<property name="siblings" flags="">
<type class="javax.baja.util.BEnumSet"/>
<description>
Slot for the &lt;code&gt;siblings&lt;/code&gt; property.
</description>
<tag name="@see">#getSiblings</tag>
<tag name="@see">#setSiblings</tag>
</property>

</class>
</bajadoc>
