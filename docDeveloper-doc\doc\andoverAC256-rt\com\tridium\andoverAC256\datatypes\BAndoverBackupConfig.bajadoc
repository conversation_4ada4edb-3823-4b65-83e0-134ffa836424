<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.datatypes.BAndoverBackupConfig" name="BAndoverBackupConfig" packageName="com.tridium.andoverAC256.datatypes" public="true">
<description>
BAndoverBackupConfig holds data for the BAndoverBackupJob
</description>
<tag name="@author">Cli<PERSON></tag>
<tag name="@creation">5/4/2005 8:14AM</tag>
<tag name="@version">$Revision$ $Date: 4/13/2005 4:36:34 PM$</tag>
<tag name="@since">AX 3.0.79</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="domain" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;domain&lt;/code&gt; property.&#xa; Domain to backup
</description>
<tag name="@see">#getDomain</tag>
<tag name="@see">#setDomain</tag>
</property>

<property name="fileName" flags="rh">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;fileName&lt;/code&gt; property.&#xa; Name of file where backup is stored
</description>
<tag name="@see">#getFileName</tag>
<tag name="@see">#setFileName</tag>
</property>

</class>
</bajadoc>
