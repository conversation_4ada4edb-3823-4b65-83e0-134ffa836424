<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="wb" qualifiedName="com.tridium.bacnet.ui.schedule.BBacnetScheduleImportManager" name="BBacnetScheduleImportManager" packageName="com.tridium.bacnet.ui.schedule" public="true">
<description>
BBacnetScheduleImportManager is used for configuring schedules to be imported&#xa; from a remote BACnet device into the Niagara station.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">03 Mar 04</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.driver.ui.schedule.BScheduleImportManager"/>
</extends>
</class>
</bajadoc>
