<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode" name="BAlarmFilterMode" packageName="com.tridiumx.jsonToolkit.exportMarker.filter" public="true" final="true">
<description>
Allows Alarm export marker filter behaviour selection&#xa;&#xa; Marked With Id: If the Source has an Export Marker present, _with Id set_&#xa; Marked: If the source has an Export Marker present&#xa; Pass All: All Alarms&#xa; Block All: No Alarms
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;markedWithId&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;marked&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;passAll&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;blockAll&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode.MARKED_WITH_ID -->
<field name="MARKED_WITH_ID"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for markedWithId.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode.MARKED -->
<field name="MARKED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for marked.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode.PASS_ALL -->
<field name="PASS_ALL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for passAll.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode.BLOCK_ALL -->
<field name="BLOCK_ALL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for blockAll.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode.markedWithId -->
<field name="markedWithId"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode"/>
<description>
BAlarmFilterMode constant for markedWithId.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode.marked -->
<field name="marked"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode"/>
<description>
BAlarmFilterMode constant for marked.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode.passAll -->
<field name="passAll"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode"/>
<description>
BAlarmFilterMode constant for passAll.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode.blockAll -->
<field name="blockAll"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode"/>
<description>
BAlarmFilterMode constant for blockAll.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAlarmFilterMode.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
