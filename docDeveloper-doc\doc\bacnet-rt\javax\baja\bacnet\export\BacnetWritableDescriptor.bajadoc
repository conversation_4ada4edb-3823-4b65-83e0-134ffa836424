<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BacnetWritableDescriptor" name="BacnetWritableDescriptor" packageName="javax.baja.bacnet.export" public="true" interface="true" abstract="true" category="interface">
<description>
Marker interface so the UI can tell if a descriptor can be written from BACnet.
</description>
<tag name="@author"><PERSON></tag>
</class>
</bajadoc>
