<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.device.overrides.DeviceOverrideAware" name="DeviceOverrideAware" packageName="javax.baja.bacnet.device.overrides" public="true" interface="true" abstract="true" category="interface">
<description>
Device overrides must add themselves to the override&#xa; list of each desired device.&#xa; &lt;p&gt;&#xa; This interface should allow special device overrides&#xa; to operate recursively on devices in folders,&#xa; or that match a bql query, etc.
</description>
<tag name="@author"><PERSON></tag>
<!-- javax.baja.bacnet.device.overrides.DeviceOverrideAware.addDeviceOverride(javax.baja.bacnet.device.overrides.DeviceOverride) -->
<method name="addDeviceOverride"  public="true" abstract="true">
<description/>
<parameter name="override">
<type class="javax.baja.bacnet.device.overrides.DeviceOverride"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.device.overrides.DeviceOverrideAware.removeDeviceOverride(javax.baja.bacnet.device.overrides.DeviceOverride) -->
<method name="removeDeviceOverride"  public="true" abstract="true">
<description/>
<parameter name="override">
<type class="javax.baja.bacnet.device.overrides.DeviceOverride"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.device.overrides.DeviceOverrideAware.updateServicesSupported() -->
<method name="updateServicesSupported"  public="true" abstract="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
