<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetReinitializedDeviceState" name="BBacnetReinitializedDeviceState" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetReinitializedDeviceState represents the state of a device after&#xa; receiving a ReinitializeDevice-Request.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">23 Jul 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;coldStart&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;warmStart&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;startBackup&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;endBackup&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;startRestore&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;endRestore&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;abortRestore&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetReinitializedDeviceState"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetReinitializedDeviceState"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
String representation of this BEnum.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.COLD_START -->
<field name="COLD_START"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for coldStart.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.WARM_START -->
<field name="WARM_START"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for warmStart.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.START_BACKUP -->
<field name="START_BACKUP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for startBackup.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.END_BACKUP -->
<field name="END_BACKUP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for endBackup.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.START_RESTORE -->
<field name="START_RESTORE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for startRestore.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.END_RESTORE -->
<field name="END_RESTORE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for endRestore.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.ABORT_RESTORE -->
<field name="ABORT_RESTORE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for abortRestore.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.coldStart -->
<field name="coldStart"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReinitializedDeviceState"/>
<description>
BBacnetReinitializedDeviceState constant for coldStart.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.warmStart -->
<field name="warmStart"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReinitializedDeviceState"/>
<description>
BBacnetReinitializedDeviceState constant for warmStart.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.startBackup -->
<field name="startBackup"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReinitializedDeviceState"/>
<description>
BBacnetReinitializedDeviceState constant for startBackup.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.endBackup -->
<field name="endBackup"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReinitializedDeviceState"/>
<description>
BBacnetReinitializedDeviceState constant for endBackup.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.startRestore -->
<field name="startRestore"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReinitializedDeviceState"/>
<description>
BBacnetReinitializedDeviceState constant for startRestore.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.endRestore -->
<field name="endRestore"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReinitializedDeviceState"/>
<description>
BBacnetReinitializedDeviceState constant for endRestore.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.abortRestore -->
<field name="abortRestore"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReinitializedDeviceState"/>
<description>
BBacnetReinitializedDeviceState constant for abortRestore.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReinitializedDeviceState"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReinitializedDeviceState.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
