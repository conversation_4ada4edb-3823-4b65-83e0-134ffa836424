<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.category.BCategoryMode" name="BCategoryMode" packageName="javax.baja.category" public="true" final="true">
<description>
BCategoryMode defines how a category is mapped into permissions&#xa; for an object.  Union indicates that permissions for the category&#xa; are added to the to the user&#x27;s permissions for the object.&#xa; Intersection indicates that missing permissions for the category&#xa; are removed from the user&#x27;s permissions for the object.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">11 Mar 2008</tag>
<tag name="@version">$Revision: 1$ $Date: 3/12/08 5:40:56 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;union&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;intersection&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.category.BCategoryMode.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.category.BCategoryMode"/>
</return>
</method>

<!-- javax.baja.category.BCategoryMode.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.category.BCategoryMode"/>
</return>
</method>

<!-- javax.baja.category.BCategoryMode.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.category.BCategoryMode.UNION -->
<field name="UNION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for union.
</description>
</field>

<!-- javax.baja.category.BCategoryMode.INTERSECTION -->
<field name="INTERSECTION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for intersection.
</description>
</field>

<!-- javax.baja.category.BCategoryMode.union -->
<field name="union"  public="true" static="true" final="true">
<type class="javax.baja.category.BCategoryMode"/>
<description>
BCategoryMode constant for union.
</description>
</field>

<!-- javax.baja.category.BCategoryMode.intersection -->
<field name="intersection"  public="true" static="true" final="true">
<type class="javax.baja.category.BCategoryMode"/>
<description>
BCategoryMode constant for intersection.
</description>
</field>

<!-- javax.baja.category.BCategoryMode.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.category.BCategoryMode"/>
<description/>
</field>

<!-- javax.baja.category.BCategoryMode.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
