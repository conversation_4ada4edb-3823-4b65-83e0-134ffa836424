<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetBinaryPv" name="BBacnetBinaryPv" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetBinaryPv represents the Bacnet Binary present value&#xa; enumeration.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 8$ $Date: 11/20/01 9:19:58 AM$</tag>
<tag name="@creation">31 Jan 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;inactive&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;active&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetBinaryPv.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetBinaryPv"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetBinaryPv.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetBinaryPv"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetBinaryPv.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetBinaryPv.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetBinaryPv.getTag(javax.baja.sys.Context) -->
<method name="getTag"  public="true">
<description>
Get a string tag, using the true/false text in the&#xa; given context, if possible.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetBinaryPv.make(boolean) -->
<method name="make"  public="true" static="true" final="true">
<description/>
<parameter name="value">
<type class="boolean"/>
<description>
the boolean value to be represented.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetBinaryPv"/>
<description>
a BBacnetBinaryPv object with the given code.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetBinaryPv.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
String representation of this BEnum.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetBinaryPv.isActive() -->
<method name="isActive"  public="true">
<description/>
<return>
<type class="boolean"/>
<description>
true for active, false for inactive.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetBinaryPv.INACTIVE -->
<field name="INACTIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for inactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBinaryPv.ACTIVE -->
<field name="ACTIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for active.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBinaryPv.inactive -->
<field name="inactive"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetBinaryPv"/>
<description>
BBacnetBinaryPv constant for inactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBinaryPv.active -->
<field name="active"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetBinaryPv"/>
<description>
BBacnetBinaryPv constant for active.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBinaryPv.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetBinaryPv"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBinaryPv.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
