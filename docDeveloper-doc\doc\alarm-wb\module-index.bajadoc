<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="alarm" runtimeProfile="wb" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>Niagara Alarm Module</description>
<package name="com.tridium.alarm.hx"/>
<package name="com.tridium.alarm.ui"><description>&lt;p&gt;&#xa;This package provides the enhanced functionality for user interface of alarms within the Baja framework.</description></package>
<package name="com.tridium.alarm.ui.portal"/>
<class packageName="com.tridium.alarm.ui" name="BAlarmClassDef"></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmClassFE"><description>Plugin for BString when used as an alarm class identifier.</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmClassMapping"></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmClassMappingFE"></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmClassSummary"><description>BAlarmClassManager is a plugin for managing all alarm classes&#xa; in a station.</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmConsole"><description>BAlarmConsole provides a graphical interface for displaying, auditing,&#xa; silencing and acknowledging alarms of one or more remote Baja stations.</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmConsoleOptions"><description>BAlarmConsoleOptions wraps all configuration&#xa; for an alarm console.</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmConsoleToPdf"><description>alarm:AlarmConsole -&gt; file:PdfFile</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmConsoleUIChannel"><description>BAlarmConsoleUIChannel is used by BAlarmConsole to handle messages between&#xa; the BConsoleRecipient and BAlarmConsole.</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmDataCols"></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmDbMaintenance"><description>BAlarmDbMaintenance provides a view of all AlarmRecords in the db&#xa; as well as a way to add notes to records no longer appearing in &#xa; the AlarmConsole and a way clear old records from the db.</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmDbView"></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmDetailsDialog"></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmDialog"><description>BAlarmDialog</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmExtManager"><description>BAlarmExtManager is a plugin for managing all alarm extensions&#xa; in a station.</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmInstructionsManager"></class>
<class packageName="com.tridium.alarm.ui.portal" name="BAlarmPortal"><description>BAlarmPortal is the main display view of the&#xa; alarm portal tool.</description></class>
<class packageName="com.tridium.alarm.ui.portal" name="BAlarmPortalOptions"><description>BAlarmPortalOptions wraps all configuration&#xa; for an alarm portal.</description></class>
<class packageName="com.tridium.alarm.ui.portal" name="BAlarmPortalProfile"></class>
<class packageName="com.tridium.alarm.ui.portal" name="BAlarmPortalTool"><description>BAlarmPortalTool is the component which serves as place&#xa; holder in the tools menu and declares the actual views.</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmPrioritiesFE"><description>Plugin for BAlarmPriorities.</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmReportDialog"></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmSpaceMenuAgent"><description>BAlarmSpaceMenuAgent</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmTitlePane"><description>BAlarmTilePane</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmTransitionBitsFE"><description>Plugin for BAlarmTransitionBits.</description></class>
<class packageName="com.tridium.alarm.ui.portal" name="BConsoleRecord"></class>
<class packageName="com.tridium.alarm.ui.portal" name="BConsoleRecordList"></class>
<class packageName="com.tridium.alarm.ui" name="BDeviceExtAlarmClassFE"></class>
<class packageName="com.tridium.alarm.ui" name="BEnumAlarmRangeFE"></class>
<class packageName="com.tridium.alarm.hx" name="BHxAlarmConsole"><description>BHxAlarmConsole</description></class>
<class packageName="com.tridium.alarm.ui" name="BIAlarmServiceView" category="interface"></class>
<class packageName="com.tridium.alarm.ui" name="BInstructionsFE"></class>
<class packageName="com.tridium.alarm.ui" name="BLimitEnableFE"><description>BLimitEnableFE</description></class>
<class packageName="com.tridium.alarm.ui" name="BNotesDialog"></class>
<class packageName="com.tridium.alarm.ui.portal" name="BPortalAlarmConsole"></class>
<class packageName="com.tridium.alarm.ui.portal" name="BPortalConsoleRecipient"><description>BAlarmPortalTool is the component which serves as place &#xa; holder in the tools menu and declares the actual views.</description></class>
<class packageName="com.tridium.alarm.ui" name="BRemoteStationFE"><description>Plugin for RemoteStation String Field.</description></class>
<class packageName="com.tridium.alarm.ui" name="BStringListFE"></class>
<class packageName="com.tridium.alarm.ui" name="BTextCustomizerFE"></class>
<class packageName="com.tridium.alarm.ui" name="BTimeZoneDisplay"></class>
<class packageName="com.tridium.alarm.ui" name="BTrayIcon"><description>BTrayIcon</description></class>
</module>
</bajadoc>
