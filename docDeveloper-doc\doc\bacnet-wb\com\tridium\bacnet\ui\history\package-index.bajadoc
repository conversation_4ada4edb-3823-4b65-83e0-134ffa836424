<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="wb" name="com.tridium.bacnet.ui.history">
<description/>
<class packageName="com.tridium.bacnet.ui.history" name="BBacnetHistoryImportManager"><description>BBacnetHistoryImportManager is the implementation of BHistoryImportManager&#xa; for managing history imports from a remote BACnet device into the&#xa; local station.</description></class>
<class packageName="com.tridium.bacnet.ui.history" name="BBacnetTrendMultipleView"><description>BBacnetTrendMultipleView provides a table view of all histories&#xa; associated with a single Bacnet TrendLogMultiple object.</description></class>
<class packageName="com.tridium.bacnet.ui.history" name="BMultiRecord"><description>BMultiRecord is used to build a BMultiRecordTable.</description></class>
<class packageName="com.tridium.bacnet.ui.history" name="BMultiRecordTable"><description>BMultiRecordTable is a table of BacnetTrendRecords from histories&#xa; created by a single BacnetTrendLogMultipleImport.</description></class>
</package>
</bajadoc>
