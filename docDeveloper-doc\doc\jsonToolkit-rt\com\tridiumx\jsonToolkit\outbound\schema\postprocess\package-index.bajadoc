<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.outbound.schema.postprocess">
<description/>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.postprocess" name="BSchemaHistoryDebug"><description>Util for seeing the history of output from a json schema.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.postprocess" name="BIPostProcessor" category="interface"><description>Interface for components which handle the result of a json schema generation.</description></class>
</package>
</bajadoc>
