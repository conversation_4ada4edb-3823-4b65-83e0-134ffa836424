/* @group Base */
.chzn-container {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  font-size: 13px;
  zoom: 1;
  *display: inline;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none; }

.chzn-container .chzn-drop {
  position: absolute;
  top: 100%;
  left: -9999px;
  z-index: 1010;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
  border: 1px solid #aaa;
  border-top: 0;
  background: #fff;
  box-shadow: 0 4px 5px rgba(0, 0, 0, 0.15); }

.chzn-container.chzn-with-drop .chzn-drop {
  left: 0; }

.chzn-container a {
  cursor: pointer; }

/* @end */
/* @group Single Chosen */
.chzn-container-single .chzn-single {
  position: relative;
  display: block;
  overflow: hidden;
  padding: 0 0 0 8px;
  height: 23px;
  border: 1px solid #aaa;
  border-radius: 5px;
  background-color: #fff;
  background: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(20%, #ffffff), color-stop(50%, #f6f6f6), color-stop(52%, #eeeeee), color-stop(100%, #f4f4f4));
  background: -webkit-linear-gradient(top, #ffffff 20%, #f6f6f6 50%, #eeeeee 52%, #f4f4f4 100%);
  background: -moz-linear-gradient(top, #ffffff 20%, #f6f6f6 50%, #eeeeee 52%, #f4f4f4 100%);
  background: -o-linear-gradient(top, #ffffff 20%, #f6f6f6 50%, #eeeeee 52%, #f4f4f4 100%);
  background: linear-gradient(top, #ffffff 20%, #f6f6f6 50%, #eeeeee 52%, #f4f4f4 100%);
  background-clip: padding-box;
  box-shadow: 0 0 3px white inset, 0 1px 1px rgba(0, 0, 0, 0.1);
  color: #444;
  text-decoration: none;
  white-space: nowrap;
  line-height: 24px; }

.chzn-container-single .chzn-default {
  color: #999; }

.chzn-container-single .chzn-single span {
  display: block;
  overflow: hidden;
  margin-right: 26px;
  text-overflow: ellipsis;
  white-space: nowrap; }

.chzn-container-single .chzn-single-with-deselect span {
  margin-right: 38px; }

.chzn-container-single .chzn-single abbr {
  position: absolute;
  top: 6px;
  right: 26px;
  display: block;
  width: 12px;
  height: 12px;
  background: url('/img/chosen-sprite.png') -42px 1px no-repeat;
  font-size: 1px; }

.chzn-container-single .chzn-single abbr:hover {
  background-position: -42px -10px; }

.chzn-container-single.chzn-disabled .chzn-single abbr:hover {
  background-position: -42px -10px; }

.chzn-container-single .chzn-single div {
  position: absolute;
  top: 0;
  right: 0;
  display: block;
  width: 18px;
  height: 100%; }

.chzn-container-single .chzn-single div b {
  display: block;
  width: 100%;
  height: 100%;
  background: url('/img/chosen-sprite.png') no-repeat 0px 2px; }

.chzn-container-single .chzn-search {
  position: relative;
  z-index: 1010;
  margin: 0;
  padding: 3px 4px;
  white-space: nowrap; }

.chzn-container-single .chzn-search input[type="text"] {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  margin: 1px 0;
  padding: 4px 20px 4px 5px;
  width: 100%;
  height: auto;
  outline: 0;
  border: 1px solid #aaa;
  background: white url('/img/chosen-sprite.png') no-repeat 100% -20px;
  background: url('/img/chosen-sprite.png') no-repeat 100% -20px, -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(1%, #eeeeee), color-stop(15%, #ffffff));
  background: url('/img/chosen-sprite.png') no-repeat 100% -20px, -webkit-linear-gradient(#eeeeee 1%, #ffffff 15%);
  background: url('/img/chosen-sprite.png') no-repeat 100% -20px, -moz-linear-gradient(#eeeeee 1%, #ffffff 15%);
  background: url('/img/chosen-sprite.png') no-repeat 100% -20px, -o-linear-gradient(#eeeeee 1%, #ffffff 15%);
  background: url('/img/chosen-sprite.png') no-repeat 100% -20px, linear-gradient(#eeeeee 1%, #ffffff 15%);
  font-size: 1em;
  font-family: sans-serif;
  line-height: normal;
  border-radius: 0; }

.chzn-container-single .chzn-drop {
  margin-top: -1px;
  border-radius: 0 0 4px 4px;
  background-clip: padding-box; }

.chzn-container-single.chzn-container-single-nosearch .chzn-search {
  position: absolute;
  left: -9999px; }

/* @end */
/* @group Results */
.chzn-container .chzn-results {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  margin: 0 4px 4px 0;
  padding: 0 0 0 4px;
  max-height: 240px;
  -webkit-overflow-scrolling: touch; }

.chzn-container .chzn-results li {
  display: none;
  margin: 0;
  padding: 5px 6px;
  list-style: none;
  line-height: 15px; }

.chzn-container .chzn-results li.active-result {
  display: list-item;
  cursor: pointer; }

.chzn-container .chzn-results li.disabled-result {
  display: list-item;
  color: #ccc;
  cursor: default; }

.chzn-container .chzn-results li.highlighted {
  background-color: #3875d7;
  background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(20%, #3875d7), color-stop(90%, #2a62bc));
  background-image: -webkit-linear-gradient(#3875d7 20%, #2a62bc 90%);
  background-image: -moz-linear-gradient(#3875d7 20%, #2a62bc 90%);
  background-image: -o-linear-gradient(#3875d7 20%, #2a62bc 90%);
  background-image: linear-gradient(#3875d7 20%, #2a62bc 90%);
  color: #fff; }

.chzn-container .chzn-results li.no-results {
  display: list-item;
  background: #f4f4f4; }

.chzn-container .chzn-results li.group-result {
  display: list-item;
  font-weight: bold;
  cursor: default; }

.chzn-container .chzn-results li.group-option {
  padding-left: 15px; }

.chzn-container .chzn-results li em {
  font-style: normal;
  text-decoration: underline; }

/* @end */
/* @group Multi Chosen */
.chzn-container-multi .chzn-choices {
  position: relative;
  overflow: hidden;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  width: 100%;
  height: auto !important;
  height: 1%;
  border: 1px solid #aaa;
  background-color: #fff;
  background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(1%, #eeeeee), color-stop(15%, #ffffff));
  background-image: -webkit-linear-gradient(#eeeeee 1%, #ffffff 15%);
  background-image: -moz-linear-gradient(#eeeeee 1%, #ffffff 15%);
  background-image: -o-linear-gradient(#eeeeee 1%, #ffffff 15%);
  background-image: linear-gradient(#eeeeee 1%, #ffffff 15%);
  cursor: text; }

.chzn-container-multi .chzn-choices li {
  float: left;
  list-style: none; }

.chzn-container-multi .chzn-choices li.search-field {
  margin: 0;
  padding: 0;
  white-space: nowrap; }

.chzn-container-multi .chzn-choices li.search-field input[type="text"] {
  margin: 1px 0;
  padding: 5px;
  height: 15px;
  outline: 0;
  border: 0 !important;
  background: transparent !important;
  box-shadow: none;
  color: #666;
  font-size: 100%;
  font-family: sans-serif;
  line-height: normal;
  border-radius: 0; }

.chzn-container-multi .chzn-choices li.search-field .default {
  color: #999; }

.chzn-container-multi .chzn-choices li.search-choice {
  position: relative;
  margin: 3px 0 3px 5px;
  padding: 3px 20px 3px 5px;
  border: 1px solid #aaa;
  border-radius: 3px;
  background-color: #e4e4e4;
  background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(20%, #f4f4f4), color-stop(50%, #f0f0f0), color-stop(52%, #e8e8e8), color-stop(100%, #eeeeee));
  background-image: -webkit-linear-gradient(#f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-image: -moz-linear-gradient(#f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-image: -o-linear-gradient(#f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-image: linear-gradient(#f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-clip: padding-box;
  box-shadow: 0 0 2px white inset, 0 1px 0 rgba(0, 0, 0, 0.05);
  color: #333;
  line-height: 13px;
  cursor: default; }

.chzn-container-multi .chzn-choices li.search-choice .search-choice-close {
  position: absolute;
  top: 4px;
  right: 3px;
  display: block;
  width: 12px;
  height: 12px;
  background: url('/img/chosen-sprite.png') -42px 1px no-repeat;
  font-size: 1px; }

.chzn-container-multi .chzn-choices li.search-choice .search-choice-close:hover {
  background-position: -42px -10px; }

.chzn-container-multi .chzn-choices li.search-choice-disabled {
  padding-right: 5px;
  border: 1px solid #ccc;
  background-color: #e4e4e4;
  background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(20%, #f4f4f4), color-stop(50%, #f0f0f0), color-stop(52%, #e8e8e8), color-stop(100%, #eeeeee));
  background-image: -webkit-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-image: -moz-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-image: -o-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-image: linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  color: #666; }

.chzn-container-multi .chzn-choices li.search-choice-focus {
  background: #d4d4d4; }

.chzn-container-multi .chzn-choices li.search-choice-focus .search-choice-close {
  background-position: -42px -10px; }

.chzn-container-multi .chzn-results {
  margin: 0;
  padding: 0; }

.chzn-container-multi .chzn-drop .result-selected {
  display: list-item;
  color: #ccc;
  cursor: default; }

/* @end */
/* @group Active  */
.chzn-container-active .chzn-single {
  border: 1px solid #5897fb;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3); }

.chzn-container-active.chzn-with-drop .chzn-single {
  border: 1px solid #aaa;
  -moz-border-radius-bottomright: 0;
  border-bottom-right-radius: 0;
  -moz-border-radius-bottomleft: 0;
  border-bottom-left-radius: 0;
  background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(20%, #eeeeee), color-stop(80%, #ffffff));
  background-image: -webkit-linear-gradient(#eeeeee 20%, #ffffff 80%);
  background-image: -moz-linear-gradient(#eeeeee 20%, #ffffff 80%);
  background-image: -o-linear-gradient(#eeeeee 20%, #ffffff 80%);
  background-image: linear-gradient(#eeeeee 20%, #ffffff 80%);
  box-shadow: 0 1px 0 #fff inset; }

.chzn-container-active.chzn-with-drop .chzn-single div {
  border-left: none;
  background: transparent; }

.chzn-container-active.chzn-with-drop .chzn-single div b {
  background-position: -18px 2px; }

.chzn-container-active .chzn-choices {
  border: 1px solid #5897fb;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3); }

.chzn-container-active .chzn-choices li.search-field input[type="text"] {
  color: #111 !important; }

/* @end */
/* @group Disabled Support */
.chzn-disabled {
  opacity: 0.5 !important;
  cursor: default; }

.chzn-disabled .chzn-single {
  cursor: default; }

.chzn-disabled .chzn-choices .search-choice .search-choice-close {
  cursor: default; }

/* @end */
/* @group Right to Left */
.chzn-rtl {
  text-align: right; }

.chzn-rtl .chzn-single {
  overflow: visible;
  padding: 0 8px 0 0; }

.chzn-rtl .chzn-single span {
  margin-right: 0;
  margin-left: 26px;
  direction: rtl; }

.chzn-rtl .chzn-single-with-deselect span {
  margin-left: 38px; }

.chzn-rtl .chzn-single div {
  right: auto;
  left: 3px; }

.chzn-rtl .chzn-single abbr {
  right: auto;
  left: 26px; }

.chzn-rtl .chzn-choices li {
  float: right; }

.chzn-rtl .chzn-choices li.search-field input[type="text"] {
  direction: rtl; }

.chzn-rtl .chzn-choices li.search-choice {
  margin: 3px 5px 3px 0;
  padding: 3px 5px 3px 19px; }

.chzn-rtl .chzn-choices li.search-choice .search-choice-close {
  right: auto;
  left: 4px; }

.chzn-rtl.chzn-container-single-nosearch .chzn-search, .chzn-rtl .chzn-drop {
  left: 9999px; }

.chzn-rtl.chzn-container-single .chzn-results {
  margin: 0 0 4px 4px;
  padding: 0 4px 0 0; }

.chzn-rtl .chzn-results li.group-option {
  padding-right: 15px;
  padding-left: 0; }

.chzn-rtl.chzn-container-active.chzn-with-drop .chzn-single div {
  border-right: none; }

.chzn-rtl .chzn-search input[type="text"] {
  padding: 4px 5px 4px 20px;
  background: white url('/img/chosen-sprite.png') no-repeat -30px -20px;
  background: url('/img/chosen-sprite.png') no-repeat -30px -20px, -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(1%, #eeeeee), color-stop(15%, #ffffff));
  background: url('/img/chosen-sprite.png') no-repeat -30px -20px, -webkit-linear-gradient(#eeeeee 1%, #ffffff 15%);
  background: url('/img/chosen-sprite.png') no-repeat -30px -20px, -moz-linear-gradient(#eeeeee 1%, #ffffff 15%);
  background: url('/img/chosen-sprite.png') no-repeat -30px -20px, -o-linear-gradient(#eeeeee 1%, #ffffff 15%);
  background: url('/img/chosen-sprite.png') no-repeat -30px -20px, linear-gradient(#eeeeee 1%, #ffffff 15%);
  direction: rtl; }

.chzn-rtl.chzn-container-single .chzn-single div b {
  background-position: 6px 2px; }

.chzn-rtl.chzn-container-single.chzn-with-drop .chzn-single div b {
  background-position: -12px 2px; }

/* @end */
/* @group Retina compatibility */
@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min-resolution: 144dpi) {
  .chzn-rtl .chzn-search input[type="text"], .chzn-container-single .chzn-single abbr, .chzn-container-single .chzn-single div b, .chzn-container-single .chzn-search input[type="text"], .chzn-container-multi .chzn-choices .search-choice .search-choice-close, .chzn-container .chzn-results-scroll-down span, .chzn-container .chzn-results-scroll-up span {
    background-image: url('/img/<EMAIL>') !important;
    background-size: 52px 37px !important;
    background-repeat: no-repeat !important; } }

/* @end */
/* box shadow settings */
/*
  Sub nav module for global nav drop downs
*/


/*! normalize.css v2.1.3 | MIT License | git.io/normalize */
article, aside, details, figcaption, figure, footer, header, hgroup, main, nav, section, summary {
  display: block; }

audio, canvas, video {
  display: inline-block; }

audio:not([controls]) {
  display: none;
  height: 0; }

[hidden], template {
  display: none; }

html {
  font-family: sans-serif;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%; }

body {
  margin: 0; }

a {
  background: transparent; }

a:focus {
  outline: thin dotted; }

a:active, a:hover {
  outline: 0; }

h1 {
  font-size: 2em;
  margin: 0.67em 0; }

abbr[title] {
  border-bottom: 1px dotted; }

b, .strong, strong {
  font-weight: bold; }

dfn {
  font-style: italic; }

hr {
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  height: 0; }

mark {
  background: #ff0;
  color: #000; }

code, kbd, pre, samp {
  font-family: monospace, serif;
  font-size: 1em; }

pre {
  white-space: pre-wrap; }

q {
  quotes: "\201C" "\201D" "\2018" "\2019"; }

small {
  font-size: 80%; }

sub, sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline; }

sup {
  top: -0.5em; }

sub {
  bottom: -0.25em; }

img {
  border: 0; }

svg:not(:root) {
  overflow: hidden; }

figure {
  margin: 0; }

fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em; }

legend {
  border: 0;
  padding: 0; }

button, input, select, textarea {
  font-family: inherit;
  font-size: 100%;
  margin: 0; }

button, input {
  line-height: normal; }

button, select {
  text-transform: none; }

select::-ms-expand {
  color: #363636;
  background-color: white;
  border: none; }

button, html input[type="button"], input[type="reset"], input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer; }

button[disabled], html input[disabled] {
  cursor: default; }

input[type="checkbox"], input[type="radio"] {
  box-sizing: border-box;
  padding: 0; }

input[type="search"] {
  -webkit-appearance: textfield;
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  box-sizing: content-box; }

input[type="search"]::-webkit-search-cancel-button, input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none; }

button::-moz-focus-inner, input::-moz-focus-inner {
  border: 0;
  padding: 0; }

textarea {
  overflow: auto;
  vertical-align: top; }

table {
  border-collapse: collapse;
  border-spacing: 0; }

@media print {
  @page {
    margin: 2cm 0.5cm; }

  * {
    text-shadow: none !important;
    color: #000 !important;
    background: transparent !important;
    box-shadow: none !important; }
  a, a:visited {
    text-decoration: underline; }
  a[href]:after {
    content: " (" attr(href) ")"; }
  abbr[title]:after {
    content: " (" attr(title) ")"; }
  a[href^="javascript:"]:after, a[href^="#"]:after {
    content: ""; }
  pre, blockquote {
    border: 1px solid #999;
    page-break-inside: avoid; }
  thead {
    display: table-header-group; }
  tr, img {
    page-break-inside: avoid; }
  img {
    max-width: 100% !important; }
  p, h2, h3 {
    orphans: 3;
    widows: 3; }
  h2, h3 {
    page-break-after: avoid; }
  select {
    background: #fff !important; }
  .navbar {
    display: none; }
  .table td, .table th {
    background-color: #fff !important; }
  .btn > .caret, .dropup > .btn > .caret {
    border-top-color: #000 !important; }
  .label {
    border: 1px solid #000; }
  .table {
    border-collapse: collapse !important; }
  .table-bordered th, .table-bordered td {
    border: 1px solid #ddd !important; } }

*, *:before, *:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

html {
  font-size: 62.5%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0); }

body {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.42857;
  color: #21242b;
  background-color: #fff; }

input, button, select, textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit; }

a {
  color: #6a92bb;
  text-decoration: none; }
  a:hover, a:focus {
    color: #446c95;
    text-decoration: underline;
    cursor: pointer; }
  a:focus {
    outline: thin dotted #333;
    outline: 5px auto-webkit-focus-ring-color;
    outline-offset: -2px; }
  a.disabled, a[disabled] {
    color: #8daccb;
    text-decoration: none;
    cursor: not-allowed;
    text-shadow: none;
    opacity: 0.65;
    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=65); }
    a.disabled:hover, a.disabled:focus, a[disabled]:hover, a[disabled]:focus {
      color: #8daccb;
      text-decoration: none; }

img {
  vertical-align: middle; }

.img-responsive {
  display: block;
  max-width: 100%;
  height: auto; }

.img-rounded {
  border-radius: 2px; }

.img-thumbnail, .thumbnail {
  padding: 4px;
  line-height: 1.42857;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 2px;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  display: inline-block;
  max-width: 100%;
  height: auto; }

.img-circle {
  border-radius: 50%; }

hr {
  margin-top: 15px;
  margin-bottom: 15px;
  border: 0;
  border-top: 1px solid #ddd; }

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0; }

p {
  margin: 0 0 10px; }

.lead {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 200;
  line-height: 1.4; }
  @media (min-width: 768px) {
    .lead {
      font-size: 21px; } }

small, .small {
  font-size: 12px;
  line-height: 17.14286px; }

.medium {
  font-size: 18px;
  line-height: 25.71429px; }

.large {
  font-size: 19px;
  line-height: 27.14286px; }

cite {
  font-style: normal; }

.text-muted {
  color: #999999; }

.lightly-muted {
  color: #e5e5e5; }

.text-primary {
  color: #76bb6a; }
  .text-primary:hover {
    color: #59a64c; }

.text-warning {
  color: #c09853; }
  .text-warning:hover {
    color: #a47c3c; }

.text-danger, .text-error {
  color: #b94a48; }
  .text-danger:hover, .text-error:hover {
    color: #953b39; }

.text-success {
  color: #2D8C20; }
  .text-success:hover {
    color: #216217; }

.text-info {
  color: #6a92bb; }
  .text-info:hover {
    color: #4c79a6; }

/* ADDITIONAL TEXT STYLES */
.text-aqua {
  color: #158E94; }

.text-grey {
  color: #999999; }

.text-yellow {
  color: #C09853; }

.text-red {
  color: #B94A48; }

.text-green {
  color: #B94A48; }

.text-left {
  text-align: left; }

.text-right {
  text-align: right; }

.text-center {
  text-align: center; }

.text-upper {
  text-transform: uppercase; }

.text-lower {
  text-transform: lowercase; }

.text-notrans {
  text-transform: none; }

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
  font-family: "BryantProRegular", 'Helvetica Neue', helvetica, arial, sans-serif, "Helvetica Neue";
  font-weight: normal;
  line-height: 1.1;
  color: inherit; }
  h1 small, h1 .small, h2 small, h2 .small, h3 small, h3 .small, h4 small, h4 .small, h5 small, h5 .small, h6 small, h6 .small, .h1 small, .h1 .small, .h2 small, .h2 .small, .h3 small, .h3 .small, .h4 small, .h4 .small, .h5 small, .h5 .small, .h6 small, .h6 .small {
    font-weight: normal;
    line-height: 1;
    color: #999999; }

h1, h2, h3 {
  margin-top: 20px;
  margin-bottom: 10px; }
  h1 small, h1 .small, h2 small, h2 .small, h3 small, h3 .small {
    font-size: 65%; }

h4, h5, h6 {
  margin-top: 10px;
  margin-bottom: 10px; }
  h4 small, h4 .small, h5 small, h5 .small, h6 small, h6 .small {
    font-size: 75%; }

h1, .h1 {
  font-size: 36px; }

h2, .h2 {
  font-size: 30px; }

h3, .h3 {
  font-size: 24px; }

h4, .h4 {
  font-size: 18px; }

h5, .h5 {
  font-size: 14px; }

h6, .h6 {
  font-size: 12px; }

.page-header {
  padding-bottom: 9px;
  margin: 40px 0 20px;
  border-bottom: 1px solid #ddd; }

ul, ol {
  margin-top: 0;
  margin-bottom: 10px; }
  ul ul, ul ol, ol ul, ol ol {
    margin-bottom: 0; }

.list-unstyled, .list-inline {
  padding-left: 0;
  list-style: none; }

.list-inline > li {
  display: inline-block;
  padding-left: 5px;
  padding-right: 5px; }
  .list-inline > li:first-child {
    padding-left: 0; }
  .list-inline > li:last-child {
    padding-right: 0; }

dl {
  margin-bottom: 20px; }

dt, dd {
  line-height: 1.42857; }

dt {
  font-weight: bold; }

dd {
  margin-left: 0; }

@media (min-width: 890px) {
  .dl-horizontal dt {
    float: left;
    width: 160px;
    clear: left;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; }
  .dl-horizontal dd {
    margin-left: 180px; }
    .dl-horizontal dd:before, .dl-horizontal dd:after {
      content: " ";
      /* 1 */
      display: table;
      /* 2 */ }
    .dl-horizontal dd:after {
      clear: both; } }

abbr[title], abbr[data-original-title] {
  cursor: help;
  border-bottom: 1px dotted #999999; }

abbr.initialism {
  font-size: 90%;
  text-transform: uppercase; }

blockquote {
  padding: 10px 20px;
  margin: 0 0 20px;
  border-left: 5px solid #ddd; }
  blockquote p {
    font-size: 17.5px;
    font-weight: 300;
    line-height: 1.25; }
  blockquote p:last-child {
    margin-bottom: 0; }
  blockquote small {
    display: block;
    line-height: 1.42857;
    color: #999999; }
    blockquote small:before {
      content: '\2014 \00A0'; }
  blockquote.pull-right {
    padding-right: 15px;
    padding-left: 0;
    border-right: 5px solid #ddd;
    border-left: 0; }
    blockquote.pull-right p, blockquote.pull-right small, blockquote.pull-right .small {
      text-align: right; }
    blockquote.pull-right small:before, blockquote.pull-right .small:before {
      content: ''; }
    blockquote.pull-right small:after, blockquote.pull-right .small:after {
      content: '\00A0 \2014'; }

blockquote:before, blockquote:after {
  content: ""; }

address {
  margin-bottom: 20px;
  font-style: normal;
  line-height: 1.42857; }

.neue {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; }

.bryant {
  font-family: "BryantProRegular", 'Helvetica Neue', helvetica, arial, sans-serif, "Helvetica Neue"; }

code, kbd, pre, samp {
  font-family: Monaco, Menlo, Consolas, "Courier New", monospace; }

code {
  padding: 2px 4px;
  font-size: 90%;
  color: #c7254e;
  background-color: #f9f2f4;
  white-space: nowrap;
  border-radius: 2px; }

pre {
  display: block;
  padding: 9.5px;
  margin: 0 0 10px;
  font-size: 13px;
  line-height: 1.42857;
  word-break: break-all;
  word-wrap: break-word;
  color: #333333;
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 2px; }
  pre code {
    padding: 0;
    font-size: inherit;
    color: inherit;
    white-space: pre-wrap;
    background-color: transparent;
    border-radius: 0; }

.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll; }

.container, .container-fluid {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px; }
  .container:before, .container:after, .container-fluid:before, .container-fluid:after {
    content: " ";
    /* 1 */
    display: table;
    /* 2 */ }
  .container:after, .container-fluid:after {
    clear: both; }

.row {
  margin-left: -15px;
  margin-right: -15px; }
  .row:before, .row:after {
    content: " ";
    /* 1 */
    display: table;
    /* 2 */ }
  .row:after {
    clear: both; }

.col-xs-12, .col-sm-12, .col-md-12, .col-lg-12, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-1, .col-sm-1, .col-md-1, .col-lg-1 {
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px; }

.col-xs-12, .col-xs-11, .col-xs-10, .col-xs-9, .col-xs-8, .col-xs-7, .col-xs-6, .col-xs-5, .col-xs-4, .col-xs-3, .col-xs-2, .col-xs-1 {
  float: left; }

.col-xs-1 {
  width: 8.33333%; }

.col-xs-2 {
  width: 16.66667%; }

.col-xs-3 {
  width: 25%; }

.col-xs-4 {
  width: 33.33333%; }

.col-xs-5 {
  width: 41.66667%; }

.col-xs-6 {
  width: 50%; }

.col-xs-7 {
  width: 58.33333%; }

.col-xs-8 {
  width: 66.66667%; }

.col-xs-9 {
  width: 75%; }

.col-xs-10 {
  width: 83.33333%; }

.col-xs-11 {
  width: 91.66667%; }

.col-xs-12 {
  width: 100%; }

.col-xs-pull-0 {
  right: 0%; }

.col-xs-pull-1 {
  right: 8.33333%; }

.col-xs-pull-2 {
  right: 16.66667%; }

.col-xs-pull-3 {
  right: 25%; }

.col-xs-pull-4 {
  right: 33.33333%; }

.col-xs-pull-5 {
  right: 41.66667%; }

.col-xs-pull-6 {
  right: 50%; }

.col-xs-pull-7 {
  right: 58.33333%; }

.col-xs-pull-8 {
  right: 66.66667%; }

.col-xs-pull-9 {
  right: 75%; }

.col-xs-pull-10 {
  right: 83.33333%; }

.col-xs-pull-11 {
  right: 91.66667%; }

.col-xs-pull-12 {
  right: 100%; }

.col-xs-push-0 {
  left: 0%; }

.col-xs-push-1 {
  left: 8.33333%; }

.col-xs-push-2 {
  left: 16.66667%; }

.col-xs-push-3 {
  left: 25%; }

.col-xs-push-4 {
  left: 33.33333%; }

.col-xs-push-5 {
  left: 41.66667%; }

.col-xs-push-6 {
  left: 50%; }

.col-xs-push-7 {
  left: 58.33333%; }

.col-xs-push-8 {
  left: 66.66667%; }

.col-xs-push-9 {
  left: 75%; }

.col-xs-push-10 {
  left: 83.33333%; }

.col-xs-push-11 {
  left: 91.66667%; }

.col-xs-push-12 {
  left: 100%; }

.col-xs-offset-0 {
  margin-left: 0%; }

.col-xs-offset-1 {
  margin-left: 8.33333%; }

.col-xs-offset-2 {
  margin-left: 16.66667%; }

.col-xs-offset-3 {
  margin-left: 25%; }

.col-xs-offset-4 {
  margin-left: 33.33333%; }

.col-xs-offset-5 {
  margin-left: 41.66667%; }

.col-xs-offset-6 {
  margin-left: 50%; }

.col-xs-offset-7 {
  margin-left: 58.33333%; }

.col-xs-offset-8 {
  margin-left: 66.66667%; }

.col-xs-offset-9 {
  margin-left: 75%; }

.col-xs-offset-10 {
  margin-left: 83.33333%; }

.col-xs-offset-11 {
  margin-left: 91.66667%; }

.col-xs-offset-12 {
  margin-left: 100%; }

@media (min-width: 768px) {
  .container {
    width: 750px; }
  .col-sm-12, .col-sm-11, .col-sm-10, .col-sm-9, .col-sm-8, .col-sm-7, .col-sm-6, .col-sm-5, .col-sm-4, .col-sm-3, .col-sm-2, .col-sm-1 {
    float: left; }
  .col-sm-1 {
    width: 8.33333%; }
  .col-sm-2 {
    width: 16.66667%; }
  .col-sm-3 {
    width: 25%; }
  .col-sm-4 {
    width: 33.33333%; }
  .col-sm-5 {
    width: 41.66667%; }
  .col-sm-6 {
    width: 50%; }
  .col-sm-7 {
    width: 58.33333%; }
  .col-sm-8 {
    width: 66.66667%; }
  .col-sm-9 {
    width: 75%; }
  .col-sm-10 {
    width: 83.33333%; }
  .col-sm-11 {
    width: 91.66667%; }
  .col-sm-12 {
    width: 100%; }
  .col-sm-pull-0 {
    right: 0%; }
  .col-sm-pull-1 {
    right: 8.33333%; }
  .col-sm-pull-2 {
    right: 16.66667%; }
  .col-sm-pull-3 {
    right: 25%; }
  .col-sm-pull-4 {
    right: 33.33333%; }
  .col-sm-pull-5 {
    right: 41.66667%; }
  .col-sm-pull-6 {
    right: 50%; }
  .col-sm-pull-7 {
    right: 58.33333%; }
  .col-sm-pull-8 {
    right: 66.66667%; }
  .col-sm-pull-9 {
    right: 75%; }
  .col-sm-pull-10 {
    right: 83.33333%; }
  .col-sm-pull-11 {
    right: 91.66667%; }
  .col-sm-pull-12 {
    right: 100%; }
  .col-sm-push-0 {
    left: 0%; }
  .col-sm-push-1 {
    left: 8.33333%; }
  .col-sm-push-2 {
    left: 16.66667%; }
  .col-sm-push-3 {
    left: 25%; }
  .col-sm-push-4 {
    left: 33.33333%; }
  .col-sm-push-5 {
    left: 41.66667%; }
  .col-sm-push-6 {
    left: 50%; }
  .col-sm-push-7 {
    left: 58.33333%; }
  .col-sm-push-8 {
    left: 66.66667%; }
  .col-sm-push-9 {
    left: 75%; }
  .col-sm-push-10 {
    left: 83.33333%; }
  .col-sm-push-11 {
    left: 91.66667%; }
  .col-sm-push-12 {
    left: 100%; }
  .col-sm-offset-0 {
    margin-left: 0%; }
  .col-sm-offset-1 {
    margin-left: 8.33333%; }
  .col-sm-offset-2 {
    margin-left: 16.66667%; }
  .col-sm-offset-3 {
    margin-left: 25%; }
  .col-sm-offset-4 {
    margin-left: 33.33333%; }
  .col-sm-offset-5 {
    margin-left: 41.66667%; }
  .col-sm-offset-6 {
    margin-left: 50%; }
  .col-sm-offset-7 {
    margin-left: 58.33333%; }
  .col-sm-offset-8 {
    margin-left: 66.66667%; }
  .col-sm-offset-9 {
    margin-left: 75%; }
  .col-sm-offset-10 {
    margin-left: 83.33333%; }
  .col-sm-offset-11 {
    margin-left: 91.66667%; }
  .col-sm-offset-12 {
    margin-left: 100%; } }

@media (min-width: 992px) {
  .container {
    width: 970px; }
  .col-md-12, .col-md-11, .col-md-10, .col-md-9, .col-md-8, .col-md-7, .col-md-6, .col-md-5, .col-md-4, .col-md-3, .col-md-2, .col-md-1 {
    float: left; }
  .col-md-1 {
    width: 8.33333%; }
  .col-md-2 {
    width: 16.66667%; }
  .col-md-3 {
    width: 25%; }
  .col-md-4 {
    width: 33.33333%; }
  .col-md-5 {
    width: 41.66667%; }
  .col-md-6 {
    width: 50%; }
  .col-md-7 {
    width: 58.33333%; }
  .col-md-8 {
    width: 66.66667%; }
  .col-md-9 {
    width: 75%; }
  .col-md-10 {
    width: 83.33333%; }
  .col-md-11 {
    width: 91.66667%; }
  .col-md-12 {
    width: 100%; }
  .col-md-pull-0 {
    right: 0%; }
  .col-md-pull-1 {
    right: 8.33333%; }
  .col-md-pull-2 {
    right: 16.66667%; }
  .col-md-pull-3 {
    right: 25%; }
  .col-md-pull-4 {
    right: 33.33333%; }
  .col-md-pull-5 {
    right: 41.66667%; }
  .col-md-pull-6 {
    right: 50%; }
  .col-md-pull-7 {
    right: 58.33333%; }
  .col-md-pull-8 {
    right: 66.66667%; }
  .col-md-pull-9 {
    right: 75%; }
  .col-md-pull-10 {
    right: 83.33333%; }
  .col-md-pull-11 {
    right: 91.66667%; }
  .col-md-pull-12 {
    right: 100%; }
  .col-md-push-0 {
    left: 0%; }
  .col-md-push-1 {
    left: 8.33333%; }
  .col-md-push-2 {
    left: 16.66667%; }
  .col-md-push-3 {
    left: 25%; }
  .col-md-push-4 {
    left: 33.33333%; }
  .col-md-push-5 {
    left: 41.66667%; }
  .col-md-push-6 {
    left: 50%; }
  .col-md-push-7 {
    left: 58.33333%; }
  .col-md-push-8 {
    left: 66.66667%; }
  .col-md-push-9 {
    left: 75%; }
  .col-md-push-10 {
    left: 83.33333%; }
  .col-md-push-11 {
    left: 91.66667%; }
  .col-md-push-12 {
    left: 100%; }
  .col-md-offset-0 {
    margin-left: 0%; }
  .col-md-offset-1 {
    margin-left: 8.33333%; }
  .col-md-offset-2 {
    margin-left: 16.66667%; }
  .col-md-offset-3 {
    margin-left: 25%; }
  .col-md-offset-4 {
    margin-left: 33.33333%; }
  .col-md-offset-5 {
    margin-left: 41.66667%; }
  .col-md-offset-6 {
    margin-left: 50%; }
  .col-md-offset-7 {
    margin-left: 58.33333%; }
  .col-md-offset-8 {
    margin-left: 66.66667%; }
  .col-md-offset-9 {
    margin-left: 75%; }
  .col-md-offset-10 {
    margin-left: 83.33333%; }
  .col-md-offset-11 {
    margin-left: 91.66667%; }
  .col-md-offset-12 {
    margin-left: 100%; } }

@media (min-width: 1200px) {
  .container {
    width: 1170px; }
  .col-lg-12, .col-lg-11, .col-lg-10, .col-lg-9, .col-lg-8, .col-lg-7, .col-lg-6, .col-lg-5, .col-lg-4, .col-lg-3, .col-lg-2, .col-lg-1 {
    float: left; }
  .col-lg-1 {
    width: 8.33333%; }
  .col-lg-2 {
    width: 16.66667%; }
  .col-lg-3 {
    width: 25%; }
  .col-lg-4 {
    width: 33.33333%; }
  .col-lg-5 {
    width: 41.66667%; }
  .col-lg-6 {
    width: 50%; }
  .col-lg-7 {
    width: 58.33333%; }
  .col-lg-8 {
    width: 66.66667%; }
  .col-lg-9 {
    width: 75%; }
  .col-lg-10 {
    width: 83.33333%; }
  .col-lg-11 {
    width: 91.66667%; }
  .col-lg-12 {
    width: 100%; }
  .col-lg-pull-0 {
    right: 0%; }
  .col-lg-pull-1 {
    right: 8.33333%; }
  .col-lg-pull-2 {
    right: 16.66667%; }
  .col-lg-pull-3 {
    right: 25%; }
  .col-lg-pull-4 {
    right: 33.33333%; }
  .col-lg-pull-5 {
    right: 41.66667%; }
  .col-lg-pull-6 {
    right: 50%; }
  .col-lg-pull-7 {
    right: 58.33333%; }
  .col-lg-pull-8 {
    right: 66.66667%; }
  .col-lg-pull-9 {
    right: 75%; }
  .col-lg-pull-10 {
    right: 83.33333%; }
  .col-lg-pull-11 {
    right: 91.66667%; }
  .col-lg-pull-12 {
    right: 100%; }
  .col-lg-push-0 {
    left: 0%; }
  .col-lg-push-1 {
    left: 8.33333%; }
  .col-lg-push-2 {
    left: 16.66667%; }
  .col-lg-push-3 {
    left: 25%; }
  .col-lg-push-4 {
    left: 33.33333%; }
  .col-lg-push-5 {
    left: 41.66667%; }
  .col-lg-push-6 {
    left: 50%; }
  .col-lg-push-7 {
    left: 58.33333%; }
  .col-lg-push-8 {
    left: 66.66667%; }
  .col-lg-push-9 {
    left: 75%; }
  .col-lg-push-10 {
    left: 83.33333%; }
  .col-lg-push-11 {
    left: 91.66667%; }
  .col-lg-push-12 {
    left: 100%; }
  .col-lg-offset-0 {
    margin-left: 0%; }
  .col-lg-offset-1 {
    margin-left: 8.33333%; }
  .col-lg-offset-2 {
    margin-left: 16.66667%; }
  .col-lg-offset-3 {
    margin-left: 25%; }
  .col-lg-offset-4 {
    margin-left: 33.33333%; }
  .col-lg-offset-5 {
    margin-left: 41.66667%; }
  .col-lg-offset-6 {
    margin-left: 50%; }
  .col-lg-offset-7 {
    margin-left: 58.33333%; }
  .col-lg-offset-8 {
    margin-left: 66.66667%; }
  .col-lg-offset-9 {
    margin-left: 75%; }
  .col-lg-offset-10 {
    margin-left: 83.33333%; }
  .col-lg-offset-11 {
    margin-left: 91.66667%; }
  .col-lg-offset-12 {
    margin-left: 100%; } }

table {
  max-width: 100%;
  background-color: transparent; }

th {
  text-align: left; }

.table {
  width: 100%;
  margin-bottom: 20px; }
  .table > thead > tr > th, .table > thead > tr > td, .table > tbody > tr > th, .table > tbody > tr > td, .table > tfoot > tr > th, .table > tfoot > tr > td {
    padding: 8px;
    line-height: 1.42857;
    vertical-align: top;
    border-top: 1px solid #ddd; }
  .table > thead > tr > th {
    vertical-align: bottom;
    border-bottom: 2px solid #ddd; }
  .table > caption + thead > tr:first-child > th, .table > caption + thead > tr:first-child > td, .table > colgroup + thead > tr:first-child > th, .table > colgroup + thead > tr:first-child > td, .table > thead:first-child > tr:first-child > th, .table > thead:first-child > tr:first-child > td {
    border-top: 0; }
  .table > tbody + tbody {
    border-top: 2px solid #ddd; }
  .table .table {
    background-color: #fff; }

.table-condensed > thead > tr > th, .table-condensed > thead > tr > td, .table-condensed > tbody > tr > th, .table-condensed > tbody > tr > td, .table-condensed > tfoot > tr > th, .table-condensed > tfoot > tr > td {
  padding: 5px; }

.table-bordered {
  border: 1px solid #ddd; }
  .table-bordered > thead > tr > th, .table-bordered > thead > tr > td, .table-bordered > tbody > tr > th, .table-bordered > tbody > tr > td, .table-bordered > tfoot > tr > th, .table-bordered > tfoot > tr > td {
    border: 1px solid #ddd; }
  .table-bordered > thead > tr > th, .table-bordered > thead > tr > td {
    border-bottom-width: 2px; }

.table-borderless > thead > tr > th, .table-borderless > thead > tr > td, .table-borderless > tbody > tr > th, .table-borderless > tbody > tr > td, .table-borderless > tfoot > tr > th, .table-borderless > tfoot > tr > td {
  border: 0; }

.table-no-padding {
  margin-bottom: 0 !important; }
  .table-no-padding > thead > tr > th, .table-no-padding > thead > tr > td, .table-no-padding > tbody > tr > th, .table-no-padding > tbody > tr > td, .table-no-padding > tfoot > tr > th, .table-no-padding > tfoot > tr > td {
    padding: 0 !important;
    vertical-align: bottom; }

.table-no-left-padd > thead > tr > th:first-child, .table-no-left-padd > thead > tr > td:first-child, .table-no-left-padd > tbody > tr > th:first-child, .table-no-left-padd > tbody > tr > td:first-child, .table-no-left-padd > tfoot > tr > th:first-child, .table-no-left-padd > tfoot > tr > td:first-child {
  padding-left: 0 !important; }

.table-striped > tbody > tr:nth-child(odd) > td, .table-striped > tbody > tr:nth-child(odd) > th {
  background-color: #f9f9f9; }

.table-hover > tbody > tr:hover > td, .table-hover > tbody > tr:hover > th {
  background-color: #f5f5f5; }

table col[class*="col-"] {
  float: none;
  display: table-column; }

table td[class*="col-"], table th[class*="col-"] {
  float: none;
  display: table-cell; }

.table > thead > tr > td.active, .table > thead > tr > th.active, .table > thead > tr.active > td, .table > thead > tr.active > th, .table > tbody > tr > td.active, .table > tbody > tr > th.active, .table > tbody > tr.active > td, .table > tbody > tr.active > th, .table > tfoot > tr > td.active, .table > tfoot > tr > th.active, .table > tfoot > tr.active > td, .table > tfoot > tr.active > th {
  background-color: #f5f5f5; }

.table > thead > tr > td.success, .table > thead > tr > th.success, .table > thead > tr.success > td, .table > thead > tr.success > th, .table > tbody > tr > td.success, .table > tbody > tr > th.success, .table > tbody > tr.success > td, .table > tbody > tr.success > th, .table > tfoot > tr > td.success, .table > tfoot > tr > th.success, .table > tfoot > tr.success > td, .table > tfoot > tr.success > th {
  background-color: #dff0d8; }

.table-hover > tbody > tr > td.success:hover, .table-hover > tbody > tr > th.success:hover, .table-hover > tbody > tr.success:hover > td, .table-hover > tbody > tr.success:hover > th {
  background-color: #d0e9c6; }

.table > thead > tr > td.danger, .table > thead > tr > th.danger, .table > thead > tr.danger > td, .table > thead > tr.danger > th, .table > tbody > tr > td.danger, .table > tbody > tr > th.danger, .table > tbody > tr.danger > td, .table > tbody > tr.danger > th, .table > tfoot > tr > td.danger, .table > tfoot > tr > th.danger, .table > tfoot > tr.danger > td, .table > tfoot > tr.danger > th {
  background-color: #f2dede; }

.table-hover > tbody > tr > td.danger:hover, .table-hover > tbody > tr > th.danger:hover, .table-hover > tbody > tr.danger:hover > td, .table-hover > tbody > tr.danger:hover > th {
  background-color: #ebcccc; }

.table > thead > tr > td.warning, .table > thead > tr > th.warning, .table > thead > tr.warning > td, .table > thead > tr.warning > th, .table > tbody > tr > td.warning, .table > tbody > tr > th.warning, .table > tbody > tr.warning > td, .table > tbody > tr.warning > th, .table > tfoot > tr > td.warning, .table > tfoot > tr > th.warning, .table > tfoot > tr.warning > td, .table > tfoot > tr.warning > th {
  background-color: #fcf8e3; }

.table-hover > tbody > tr > td.warning:hover, .table-hover > tbody > tr > th.warning:hover, .table-hover > tbody > tr.warning:hover > td, .table-hover > tbody > tr.warning:hover > th {
  background-color: #faf2cc; }

@media (max-width: 767px) {
  .table-responsive {
    width: 100%;
    margin-bottom: 15px;
    overflow-y: hidden;
    overflow-x: scroll;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    border: 1px solid #ddd;
    -webkit-overflow-scrolling: touch; }
    .table-responsive > .table {
      margin-bottom: 0; }
      .table-responsive > .table > thead > tr > th, .table-responsive > .table > thead > tr > td, .table-responsive > .table > tbody > tr > th, .table-responsive > .table > tbody > tr > td, .table-responsive > .table > tfoot > tr > th, .table-responsive > .table > tfoot > tr > td {
        white-space: nowrap; }
    .table-responsive > .table-bordered {
      border: 0; }
      .table-responsive > .table-bordered > thead > tr > th:first-child, .table-responsive > .table-bordered > thead > tr > td:first-child, .table-responsive > .table-bordered > tbody > tr > th:first-child, .table-responsive > .table-bordered > tbody > tr > td:first-child, .table-responsive > .table-bordered > tfoot > tr > th:first-child, .table-responsive > .table-bordered > tfoot > tr > td:first-child {
        border-left: 0; }
      .table-responsive > .table-bordered > thead > tr > th:last-child, .table-responsive > .table-bordered > thead > tr > td:last-child, .table-responsive > .table-bordered > tbody > tr > th:last-child, .table-responsive > .table-bordered > tbody > tr > td:last-child, .table-responsive > .table-bordered > tfoot > tr > th:last-child, .table-responsive > .table-bordered > tfoot > tr > td:last-child {
        border-right: 0; }
      .table-responsive > .table-bordered > tbody > tr:last-child > th, .table-responsive > .table-bordered > tbody > tr:last-child > td, .table-responsive > .table-bordered > tfoot > tr:last-child > th, .table-responsive > .table-bordered > tfoot > tr:last-child > td {
        border-bottom: 0; } }

fieldset {
  padding: 0;
  margin: 0;
  border: 0; }

legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 20px;
  font-size: 21px;
  line-height: inherit;
  color: #333333;
  border: 0;
  border-bottom: 1px solid #e5e5e5; }

label {
  display: inline-block;
  margin-bottom: 5px;
  font-weight: bold; }

input[type="search"] {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

input[type="radio"], input[type="checkbox"] {
  margin: 4px 0 0;
  margin-top: 1px \9;
  /* IE8-9 */
  line-height: normal; }

input[type="file"] {
  display: block; }

select[multiple], select[size] {
  height: auto; }

select optgroup {
  font-size: inherit;
  font-style: inherit;
  font-family: inherit; }

input[type="file"]:focus, input[type="radio"]:focus, input[type="checkbox"]:focus {
  outline: thin dotted #333;
  outline: 5px auto-webkit-focus-ring-color;
  outline-offset: -2px; }

input[type="number"]::-webkit-outer-spin-button, input[type="number"]::-webkit-inner-spin-button {
  height: auto; }

output {
  display: block;
  padding-top: 7px;
  font-size: 14px;
  line-height: 1.42857;
  color: #555555;
  vertical-align: middle; }

select, textarea, input[type="text"], input[type="password"], input[type="datetime"], input[type="datetime-local"], input[type="date"], input[type="month"], input[type="time"], input[type="week"], input[type="number"], input[type="email"], input[type="url"], input[type="search"], input[type="tel"], input[type="color"], .form-control {
  display: block;
  width: 100%;
  height: 34px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857;
  color: #555555;
  vertical-align: middle;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 2px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s; }
  select:focus, textarea:focus, input[type="text"]:focus, input[type="password"]:focus, input[type="datetime"]:focus, input[type="datetime-local"]:focus, input[type="date"]:focus, input[type="month"]:focus, input[type="time"]:focus, input[type="week"]:focus, input[type="number"]:focus, input[type="email"]:focus, input[type="url"]:focus, input[type="search"]:focus, input[type="tel"]:focus, input[type="color"]:focus, .form-control:focus {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6); }
  select:-moz-placeholder, textarea:-moz-placeholder, input[type="text"]:-moz-placeholder, input[type="password"]:-moz-placeholder, input[type="datetime"]:-moz-placeholder, input[type="datetime-local"]:-moz-placeholder, input[type="date"]:-moz-placeholder, input[type="month"]:-moz-placeholder, input[type="time"]:-moz-placeholder, input[type="week"]:-moz-placeholder, input[type="number"]:-moz-placeholder, input[type="email"]:-moz-placeholder, input[type="url"]:-moz-placeholder, input[type="search"]:-moz-placeholder, input[type="tel"]:-moz-placeholder, input[type="color"]:-moz-placeholder, .form-control:-moz-placeholder {
    color: #999999; }
  select::-moz-placeholder, textarea::-moz-placeholder, input[type="text"]::-moz-placeholder, input[type="password"]::-moz-placeholder, input[type="datetime"]::-moz-placeholder, input[type="datetime-local"]::-moz-placeholder, input[type="date"]::-moz-placeholder, input[type="month"]::-moz-placeholder, input[type="time"]::-moz-placeholder, input[type="week"]::-moz-placeholder, input[type="number"]::-moz-placeholder, input[type="email"]::-moz-placeholder, input[type="url"]::-moz-placeholder, input[type="search"]::-moz-placeholder, input[type="tel"]::-moz-placeholder, input[type="color"]::-moz-placeholder, .form-control::-moz-placeholder {
    color: #999999; }
  select:-ms-input-placeholder, textarea:-ms-input-placeholder, input[type="text"]:-ms-input-placeholder, input[type="password"]:-ms-input-placeholder, input[type="datetime"]:-ms-input-placeholder, input[type="datetime-local"]:-ms-input-placeholder, input[type="date"]:-ms-input-placeholder, input[type="month"]:-ms-input-placeholder, input[type="time"]:-ms-input-placeholder, input[type="week"]:-ms-input-placeholder, input[type="number"]:-ms-input-placeholder, input[type="email"]:-ms-input-placeholder, input[type="url"]:-ms-input-placeholder, input[type="search"]:-ms-input-placeholder, input[type="tel"]:-ms-input-placeholder, input[type="color"]:-ms-input-placeholder, .form-control:-ms-input-placeholder {
    color: #999999; }
  select::-webkit-input-placeholder, textarea::-webkit-input-placeholder, input[type="text"]::-webkit-input-placeholder, input[type="password"]::-webkit-input-placeholder, input[type="datetime"]::-webkit-input-placeholder, input[type="datetime-local"]::-webkit-input-placeholder, input[type="date"]::-webkit-input-placeholder, input[type="month"]::-webkit-input-placeholder, input[type="time"]::-webkit-input-placeholder, input[type="week"]::-webkit-input-placeholder, input[type="number"]::-webkit-input-placeholder, input[type="email"]::-webkit-input-placeholder, input[type="url"]::-webkit-input-placeholder, input[type="search"]::-webkit-input-placeholder, input[type="tel"]::-webkit-input-placeholder, input[type="color"]::-webkit-input-placeholder, .form-control::-webkit-input-placeholder {
    color: #999999; }
  select[disabled], select[readonly], fieldset[disabled] select, textarea[disabled], textarea[readonly], fieldset[disabled] textarea, input[type="text"][disabled], input[type="text"][readonly], fieldset[disabled] input[type="text"], input[type="password"][disabled], input[type="password"][readonly], fieldset[disabled] input[type="password"], input[type="datetime"][disabled], input[type="datetime"][readonly], fieldset[disabled] input[type="datetime"], input[type="datetime-local"][disabled], input[type="datetime-local"][readonly], fieldset[disabled] input[type="datetime-local"], input[type="date"][disabled], input[type="date"][readonly], fieldset[disabled] input[type="date"], input[type="month"][disabled], input[type="month"][readonly], fieldset[disabled] input[type="month"], input[type="time"][disabled], input[type="time"][readonly], fieldset[disabled] input[type="time"], input[type="week"][disabled], input[type="week"][readonly], fieldset[disabled] input[type="week"], input[type="number"][disabled], input[type="number"][readonly], fieldset[disabled] input[type="number"], input[type="email"][disabled], input[type="email"][readonly], fieldset[disabled] input[type="email"], input[type="url"][disabled], input[type="url"][readonly], fieldset[disabled] input[type="url"], input[type="search"][disabled], input[type="search"][readonly], fieldset[disabled] input[type="search"], input[type="tel"][disabled], input[type="tel"][readonly], fieldset[disabled] input[type="tel"], input[type="color"][disabled], input[type="color"][readonly], fieldset[disabled] input[type="color"], .form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
    cursor: not-allowed;
    background-color: #ddd; }
  select.invalid, textarea.invalid, input[type="text"].invalid, input[type="password"].invalid, input[type="datetime"].invalid, input[type="datetime-local"].invalid, input[type="date"].invalid, input[type="month"].invalid, input[type="time"].invalid, input[type="week"].invalid, input[type="number"].invalid, input[type="email"].invalid, input[type="url"].invalid, input[type="search"].invalid, input[type="tel"].invalid, input[type="color"].invalid, .form-control.invalid {
    border: 1px solid #B94A48;
    color: #B94A48; }
  select.valid, textarea.valid, input[type="text"].valid, input[type="password"].valid, input[type="datetime"].valid, input[type="datetime-local"].valid, input[type="date"].valid, input[type="month"].valid, input[type="time"].valid, input[type="week"].valid, input[type="number"].valid, input[type="email"].valid, input[type="url"].valid, input[type="search"].valid, input[type="tel"].valid, input[type="color"].valid, .form-control.valid {
    border: 1px solid #468847;
    color: #468847; }

label, input, button, select, textarea {
  font-size: 14px;
  font-weight: normal;
  line-height: 20px; }

.radio.inline, .checkbox.inline {
  display: inline-block;
  padding-top: 5px;
  margin-bottom: 0;
  vertical-align: middle; }

textarea {
  height: auto; }

.form-group {
  margin-bottom: 15px; }

.radio, .checkbox {
  display: block;
  min-height: 20px;
  margin-top: 10px;
  margin-bottom: 10px;
  padding-left: 20px;
  vertical-align: middle; }
  .radio label, .checkbox label {
    display: inline;
    margin-bottom: 0;
    font-weight: normal;
    cursor: pointer; }
    .radio label.disabled, .radio label[disabled], .checkbox label.disabled, .checkbox label[disabled] {
      cursor: not-allowed; }

.radio input[type="radio"], .radio-inline input[type="radio"], .checkbox input[type="checkbox"], .checkbox-inline input[type="checkbox"] {
  float: left;
  margin-left: -20px; }

.radio + .radio, .checkbox + .checkbox {
  margin-top: -5px; }

.radio-inline, .checkbox-inline {
  display: inline-block;
  padding-left: 20px;
  margin-bottom: 0;
  vertical-align: middle;
  font-weight: normal;
  cursor: pointer; }

.radio-inline + .radio-inline, .checkbox-inline + .checkbox-inline {
  margin-top: 0;
  margin-left: 10px; }

input[type="radio"][disabled], fieldset[disabled] input[type="radio"], input[type="checkbox"][disabled], fieldset[disabled] input[type="checkbox"], .radio[disabled], fieldset[disabled] .radio, .radio-inline[disabled], fieldset[disabled] .radio-inline, .checkbox[disabled], fieldset[disabled] .checkbox, .checkbox-inline[disabled], fieldset[disabled] .checkbox-inline {
  cursor: not-allowed; }

.input-sm, .input-group-sm > .form-control, .input-group-sm > .input-group-addon, .input-group-sm > .input-group-btn > .btn {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 2px; }

select.input-sm, .input-group-sm > select.form-control, .input-group-sm > select.input-group-addon, .input-group-sm > .input-group-btn > select.btn {
  height: 30px;
  line-height: 30px; }

textarea.input-sm, .input-group-sm > textarea.form-control, .input-group-sm > textarea.input-group-addon, .input-group-sm > .input-group-btn > textarea.btn {
  height: auto; }

.input-lg, .input-group-lg > .form-control, .input-group-lg > .input-group-addon, .input-group-lg > .input-group-btn > .btn {
  height: 51px;
  padding: 12px 20px;
  font-size: 19px;
  line-height: 1.33;
  border-radius: 2px; }

select.input-lg, .input-group-lg > select.form-control, .input-group-lg > select.input-group-addon, .input-group-lg > .input-group-btn > select.btn {
  height: 51px;
  line-height: 51px; }

textarea.input-lg, .input-group-lg > textarea.form-control, .input-group-lg > textarea.input-group-addon, .input-group-lg > .input-group-btn > textarea.btn {
  height: auto; }

.has-warning .help-block, .has-warning .control-label, .has-warning .radio, .has-warning .checkbox, .has-warning .radio-inline, .has-warning .checkbox-inline {
  color: #c09853; }
.has-warning .form-control {
  border-color: #c09853;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); }
  .has-warning .form-control:focus {
    border-color: #a47c3c;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #dbc49e;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #dbc49e; }
.has-warning .input-group-addon {
  color: #c09853;
  border-color: #c09853;
  background-color: #fcf8e3; }

.has-error .help-block, .has-error .control-label, .has-error .radio, .has-error .checkbox, .has-error .radio-inline, .has-error .checkbox-inline {
  color: #b94a48; }
.has-error .form-control {
  border-color: #b94a48;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); }
  .has-error .form-control:focus {
    border-color: #953b39;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #d59392;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #d59392; }
.has-error .input-group-addon {
  color: #b94a48;
  border-color: #b94a48;
  background-color: #f2dede; }

.has-success .help-block, .has-success .control-label, .has-success .radio, .has-success .checkbox, .has-success .radio-inline, .has-success .checkbox-inline {
  color: #2D8C20; }
.has-success .form-control {
  border-color: #2D8C20;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075); }
  .has-success .form-control:focus {
    border-color: #216217;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #53d33f;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #53d33f; }
.has-success .input-group-addon {
  color: #2D8C20;
  border-color: #2D8C20;
  background-color: #dff0d8; }

input[type="search"], .form-search, .search-query {
  border-radius: 15px; }

.form-control-static {
  margin-bottom: 0; }

.help-block {
  display: block;
  margin-top: 5px;
  margin-bottom: 10px;
  color: #586173; }

@media (min-width: 768px) {
  .form-inline .form-group, .form-inline .navbar-form {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle; }
  .form-inline .form-control, .form-inline .navbar-form {
    display: inline-block; }
  .form-inline .with-marg, .form-inline .navbar-form {
    margin-left: 5px; }
    .form-inline .with-marg:first-child, .form-inline .navbar-form {
      margin-left: 0; }
  .form-inline .radio, .form-inline .navbar-form, .form-inline .checkbox, .form-inline .navbar-form {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    padding-left: 0; }
  .form-inline .radio input[type="radio"], .form-inline .radio .navbar-form, .form-inline .checkbox input[type="checkbox"], .form-inline .checkbox .navbar-form {
    float: none;
    margin-left: 0; }
  .form-inline.with-marg .radio, .form-inline.with-marg .with-marg.navbar-form, .form-inline.with-marg .select, .form-inline.with-marg .with-marg.navbar-form, .form-inline.with-marg .form-group, .form-inline.with-marg .with-marg.navbar-form, .form-inline.with-marg .form-control, .form-inline.with-marg .with-marg.navbar-form {
    margin: 5px; }
    .form-inline.with-marg .radio:first-child, .form-inline.with-marg .with-marg.navbar-form, .form-inline.with-marg .select:first-child, .form-inline.with-marg .with-marg.navbar-form, .form-inline.with-marg .form-group:first-child, .form-inline.with-marg .with-marg.navbar-form, .form-inline.with-marg .form-control:first-child, .form-inline.with-marg .with-marg.navbar-form {
      margin-left: 0px; } }

@media (min-width: 480px) {
  .form-inline-phone .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle; }
  .form-inline-phone .form-control {
    display: inline-block; }
  .form-inline-phone .with-marg {
    margin-left: 5px; }
    .form-inline-phone .with-marg:first-child {
      margin-left: 0; }
  .form-inline-phone .radio, .form-inline-phone .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    padding-left: 0; }
  .form-inline-phone .radio input[type="radio"], .form-inline-phone .checkbox input[type="checkbox"] {
    float: none;
    margin-left: 0; }
  .form-inline-phone.with-marg .radio, .form-inline-phone.with-marg .select, .form-inline-phone.with-marg .form-group, .form-inline-phone.with-marg .form-control {
    margin: 5px; }
    .form-inline-phone.with-marg .radio:first-child, .form-inline-phone.with-marg .select:first-child, .form-inline-phone.with-marg .form-group:first-child, .form-inline-phone.with-marg .form-control:first-child {
      margin-left: 0px; } }

.form-inline-all .form-group {
  display: inline-block;
  margin-bottom: 0;
  vertical-align: middle; }
.form-inline-all .form-control {
  display: inline-block; }
.form-inline-all .with-marg {
  margin-left: 5px; }
  .form-inline-all .with-marg:first-child {
    margin-left: 0; }
.form-inline-all .radio, .form-inline-all .checkbox {
  display: inline-block;
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 0; }
.form-inline-all .radio input[type="radio"], .form-inline-all .checkbox input[type="checkbox"] {
  float: none;
  margin-left: 0; }
.form-inline-all.with-marg .radio, .form-inline-all.with-marg .select, .form-inline-all.with-marg .form-group, .form-inline-all.with-marg .form-control {
  margin: 5px; }
  .form-inline-all.with-marg .radio:first-child, .form-inline-all.with-marg .select:first-child, .form-inline-all.with-marg .form-group:first-child, .form-inline-all.with-marg .form-control:first-child {
    margin-left: 0px; }

.form-horizontal .control-label, .form-horizontal .radio, .form-horizontal .checkbox, .form-horizontal .radio-inline, .form-horizontal .checkbox-inline {
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 7px; }
.form-horizontal .form-group {
  margin-left: -15px;
  margin-right: -15px; }
  .form-horizontal .form-group:before, .form-horizontal .form-group:after {
    content: " ";
    /* 1 */
    display: table;
    /* 2 */ }
  .form-horizontal .form-group:after {
    clear: both; }
.form-horizontal .form-control-static {
  padding-top: 7px; }
.form-horizontal.form-horizontal-default .form-group > label:first-child {
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px; }
  @media (min-width: 768px) {
    .form-horizontal.form-horizontal-default .form-group > label:first-child {
      float: left;
      width: 33.33333%; } }
.form-horizontal.form-horizontal-default .form-group > *:nth-child(2) {
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px; }
  @media (min-width: 768px) {
    .form-horizontal.form-horizontal-default .form-group > *:nth-child(2) {
      float: left;
      width: 66.66667%; } }
@media (min-width: 768px) {
  .form-horizontal .control-label {
    text-align: right; } }

select {
  padding-right: 3px !important; }

.field-maxw-xs {
  max-width: 35px; }

.field-maxw-sm {
  max-width: 90px; }

.field-maxw-md {
  max-width: 150px; }

.field-maxw-lg {
  max-width: 210px; }

.field-maxw-xlg {
  max-width: 270px; }

.field-maxw-xxlg {
  max-width: 530px; }

.input-maxw-xs {
  padding-left: 8px !important;
  padding-right: 8px !important;
  width: 100%;
  max-width: 35px; }

.input-maxw-sm {
  width: 100%;
  max-width: 90px; }

.input-maxw-md {
  width: 100%;
  max-width: 150px; }

.input-maxw-lg {
  width: 100%;
  max-width: 210px; }

.input-maxw-xlg {
  width: 100%;
  max-width: 270px; }

.input-maxw-xxlg {
  width: 100%;
  max-width: 530px; }

.input-w-xs {
  padding-left: 8px !important;
  padding-right: 8px !important;
  width: 35px !important; }

.input-w-sm {
  width: 90px !important; }

.input-w-md {
  width: 150px !important; }

.input-w-lg {
  width: 210px !important; }

.input-w-auto {
  width: auto !important; }

.btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: normal;
  box-shadow: 0px 1px 2px #e5e5e5;
  font-family: "BryantProRegular", 'Helvetica Neue', helvetica, arial, sans-serif, "Helvetica Neue";
  text-transform: uppercase;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857;
  border-radius: 2px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none; }
  .btn:focus {
    outline: thin dotted #333;
    outline: 5px auto-webkit-focus-ring-color;
    outline-offset: -2px; }
  .btn:hover, .btn:focus {
    color: #333;
    text-decoration: none; }
  .btn:active, .btn.active {
    outline: 0;
    background-image: none;
    -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); }
  .btn.disabled, .btn[disabled], fieldset[disabled] .btn {
    cursor: not-allowed;
    text-shadow: none;
    opacity: 0.65;
    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=65);
    -webkit-box-shadow: none;
    box-shadow: none; }

.btn-default {
  color: #333;
  background-color: #f1f1f1;
  border-color: #d7d7d7; }
  .btn-default:hover, .btn-default:focus, .btn-default:active, .btn-default.active {
    color: #333;
    background-color: #dddddd;
    border-color: #b9b9b9; }
  .open .btn-default.dropdown-toggle {
    color: #333;
    background-color: #dddddd;
    border-color: #b9b9b9; }
  .btn-default:active, .btn-default.active {
    background-image: none; }
  .open .btn-default.dropdown-toggle {
    background-image: none; }
  .btn-default.disabled, .btn-default.disabled:hover, .btn-default.disabled:focus, .btn-default.disabled:active, .btn-default.disabled.active, .btn-default[disabled], .btn-default[disabled]:hover, .btn-default[disabled]:focus, .btn-default[disabled]:active, .btn-default[disabled].active, fieldset[disabled] .btn-default, fieldset[disabled] .btn-default:hover, fieldset[disabled] .btn-default:focus, fieldset[disabled] .btn-default:active, fieldset[disabled] .btn-default.active {
    background-color: #c8c6c6;
    border-color: #c8c6c6;
    color: #57585d; }

.btn-primary {
  color: #fff;
  background-color: #76bb6a;
  border-color: #59a64c; }
  .btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active {
    color: #fff;
    background-color: #5dad4f;
    border-color: #447c39; }
  .open .btn-primary.dropdown-toggle {
    color: #fff;
    background-color: #5dad4f;
    border-color: #447c39; }
  .btn-primary:active, .btn-primary.active {
    background-image: none; }
  .open .btn-primary.dropdown-toggle {
    background-image: none; }
  .btn-primary.disabled, .btn-primary.disabled:hover, .btn-primary.disabled:focus, .btn-primary.disabled:active, .btn-primary.disabled.active, .btn-primary[disabled], .btn-primary[disabled]:hover, .btn-primary[disabled]:focus, .btn-primary[disabled]:active, .btn-primary[disabled].active, fieldset[disabled] .btn-primary, fieldset[disabled] .btn-primary:hover, fieldset[disabled] .btn-primary:focus, fieldset[disabled] .btn-primary:active, fieldset[disabled] .btn-primary.active {
    background-color: #c8c6c6;
    border-color: #c8c6c6;
    color: #57585d; }

.btn-warning {
  color: #fff;
  background-color: #cf832d;
  border-color: #a56724; }
  .btn-warning:hover, .btn-warning:focus, .btn-warning:active, .btn-warning.active {
    color: #fff;
    background-color: #ad6c26;
    border-color: #734719; }
  .open .btn-warning.dropdown-toggle {
    color: #fff;
    background-color: #ad6c26;
    border-color: #734719; }
  .btn-warning:active, .btn-warning.active {
    background-image: none; }
  .open .btn-warning.dropdown-toggle {
    background-image: none; }
  .btn-warning.disabled, .btn-warning.disabled:hover, .btn-warning.disabled:focus, .btn-warning.disabled:active, .btn-warning.disabled.active, .btn-warning[disabled], .btn-warning[disabled]:hover, .btn-warning[disabled]:focus, .btn-warning[disabled]:active, .btn-warning[disabled].active, fieldset[disabled] .btn-warning, fieldset[disabled] .btn-warning:hover, fieldset[disabled] .btn-warning:focus, fieldset[disabled] .btn-warning:active, fieldset[disabled] .btn-warning.active {
    background-color: #c8c6c6;
    border-color: #c8c6c6;
    color: #57585d; }

.btn-danger {
  color: #fff;
  background-color: #da4f49;
  border-color: #c72e29; }
  .btn-danger:hover, .btn-danger:focus, .btn-danger:active, .btn-danger.active {
    color: #fff;
    background-color: #d0302a;
    border-color: #95221e; }
  .open .btn-danger.dropdown-toggle {
    color: #fff;
    background-color: #d0302a;
    border-color: #95221e; }
  .btn-danger:active, .btn-danger.active {
    background-image: none; }
  .open .btn-danger.dropdown-toggle {
    background-image: none; }
  .btn-danger.disabled, .btn-danger.disabled:hover, .btn-danger.disabled:focus, .btn-danger.disabled:active, .btn-danger.disabled.active, .btn-danger[disabled], .btn-danger[disabled]:hover, .btn-danger[disabled]:focus, .btn-danger[disabled]:active, .btn-danger[disabled].active, fieldset[disabled] .btn-danger, fieldset[disabled] .btn-danger:hover, fieldset[disabled] .btn-danger:focus, fieldset[disabled] .btn-danger:active, fieldset[disabled] .btn-danger.active {
    background-color: #c8c6c6;
    border-color: #c8c6c6;
    color: #57585d; }

.btn-success {
  color: #fff;
  background-color: #2D8C20;
  border-color: #216217; }
  .btn-success:hover, .btn-success:focus, .btn-success:active, .btn-success.active {
    color: #fff;
    background-color: #236b18;
    border-color: #11310b; }
  .open .btn-success.dropdown-toggle {
    color: #fff;
    background-color: #236b18;
    border-color: #11310b; }
  .btn-success:active, .btn-success.active {
    background-image: none; }
  .open .btn-success.dropdown-toggle {
    background-image: none; }
  .btn-success.disabled, .btn-success.disabled:hover, .btn-success.disabled:focus, .btn-success.disabled:active, .btn-success.disabled.active, .btn-success[disabled], .btn-success[disabled]:hover, .btn-success[disabled]:focus, .btn-success[disabled]:active, .btn-success[disabled].active, fieldset[disabled] .btn-success, fieldset[disabled] .btn-success:hover, fieldset[disabled] .btn-success:focus, fieldset[disabled] .btn-success:active, fieldset[disabled] .btn-success.active {
    background-color: #c8c6c6;
    border-color: #c8c6c6;
    color: #57585d; }

.btn-info {
  color: #fff;
  background-color: #3A87AD;
  border-color: #2d6a87; }
  .btn-info:hover, .btn-info:focus, .btn-info:active, .btn-info.active {
    color: #fff;
    background-color: #30708e;
    border-color: #1e4659; }
  .open .btn-info.dropdown-toggle {
    color: #fff;
    background-color: #30708e;
    border-color: #1e4659; }
  .btn-info:active, .btn-info.active {
    background-image: none; }
  .open .btn-info.dropdown-toggle {
    background-image: none; }
  .btn-info.disabled, .btn-info.disabled:hover, .btn-info.disabled:focus, .btn-info.disabled:active, .btn-info.disabled.active, .btn-info[disabled], .btn-info[disabled]:hover, .btn-info[disabled]:focus, .btn-info[disabled]:active, .btn-info[disabled].active, fieldset[disabled] .btn-info, fieldset[disabled] .btn-info:hover, fieldset[disabled] .btn-info:focus, fieldset[disabled] .btn-info:active, fieldset[disabled] .btn-info.active {
    background-color: #c8c6c6;
    border-color: #c8c6c6;
    color: #57585d; }

.btn-link, .dibs-btn-link {
  color: #6a92bb;
  font-weight: normal;
  cursor: pointer;
  border-radius: 0;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  box-shadow: none;
  text-transform: none; }
  .btn-link, .btn-link:active, .btn-link[disabled], fieldset[disabled] .btn-link, .dibs-btn-link, .dibs-btn-link:active, .dibs-btn-link[disabled], fieldset[disabled] .dibs-btn-link {
    background-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none; }
  .btn-link, .btn-link:hover, .btn-link:focus, .btn-link:active, .dibs-btn-link, .dibs-btn-link:hover, .dibs-btn-link:focus, .dibs-btn-link:active {
    border-color: transparent; }
  .btn-link:hover, .btn-link:focus, .dibs-btn-link:hover, .dibs-btn-link:focus {
    color: #446c95;
    text-decoration: underline;
    background-color: transparent; }
  .btn-link.disabled, .btn-link[disabled], fieldset[disabled] .btn-link, .dibs-btn-link.disabled, .dibs-btn-link[disabled], fieldset[disabled] .dibs-btn-link {
    color: #8daccb;
    text-decoration: none; }
    .btn-link.disabled:hover, .btn-link.disabled:focus, .btn-link[disabled]:hover, .btn-link[disabled]:focus, fieldset[disabled] .btn-link:hover, fieldset[disabled] .btn-link:focus, .dibs-btn-link.disabled:hover, .dibs-btn-link.disabled:focus, .dibs-btn-link[disabled]:hover, .dibs-btn-link[disabled]:focus, fieldset[disabled] .dibs-btn-link:hover, fieldset[disabled] .dibs-btn-link:focus {
      color: #8daccb;
      text-decoration: none; }


.btn-lg, .btn-group-lg > .btn {
  padding: 12px 20px;
  font-size: 19px;
  line-height: 1.33;
  border-radius: 2px; }

.btn-md {
  padding: 8px 17px;
  font-size: 18px;
  line-height: 1.33;
  border-radius: 2px; }

.btn-sm, .btn-group-sm > .btn, .btn-xs, .btn-group-xs > .btn {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 2px; }

.btn-xs, .btn-group-xs > .btn {
  padding: 2px 5px 0 5px;
  font-size: 11px; }

.btn-block {
  display: block;
  width: 100%;
  padding-left: 0;
  padding-right: 0; }

.btn-block + .btn-block {
  margin-top: 5px; }

input[type="submit"].btn-block, input[type="reset"].btn-block, input[type="button"].btn-block {
  width: 100%; }

.btn-wrap {
  white-space: normal; }

.fade {
  opacity: 0;
  -webkit-transition: opacity 0.15s linear;
  transition: opacity 0.15s linear; }
  .fade.in {
    opacity: 1; }

.collapse {
  display: none; }
  .collapse.in {
    display: block; }

tbody.collapse.in {
  display: table-row-group; }

.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  -webkit-transition: height 0.35s ease;
  transition: height 0.35s ease; }

.recessed {
  -webkit-animation: recess 0.7s ease-out;
  animation: recess 0.7s ease-out;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
  transform-origin: 100% 0%;
  -webkit-transform-origin: 100% 0%; }

@-webkit-keyframes recess {
  0% {
    transform: scale(1, 1);
    opacity: 1; }

  100% {
    transform: scale(0.8, 0.8);
    opacity: 0.6; }

  0% {
    -webkit-transform: scale(1, 1);
    opacity: 1; }

  100% {
    -webkit-transform: scale(0.8, 0.8);
    opacity: 0.6; } }

.full {
  animation: full 0.7s ease-out;
  animation-iteration-count: 1;
  -webkit-animation: full 0.7s ease-out;
  -webkit-animation-iteration-count: 1;
  transform-origin: 100% 0%;
  -webkit-transform-origin: 100% 0%; }

@-webkit-keyframes full {
  0% {
    transform: scale(0.8, 0.8);
    opacity: 0.6; }

  100% {
    transform: scale(1, 1);
    opacity: 1; }

  0% {
    -webkit-transform: scale(0.8, 0.8);
    opacity: 0.6; }

  100% {
    -webkit-transform: scale(1, 1);
    opacity: 1; } }

@font-face {
  font-family: 'Glyphicons Halflings';
  src: font-url('bootstrap/1stdibsIcons.eot');
  src: font-url('bootstrap/1stdibsIcons.eot?#iefix') format('embedded-opentype'), font-url('bootstrap/1stdibsIcons.woff') format('woff'), font-url('bootstrap/1stdibsIcons.ttf') format('truetype'), font-url('bootstrap/1stdibsIcons.svg#glyphicons_halflingsregular') format('svg'); }

.glyphicon {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: 'Glyphicons Halflings';
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }
  .glyphicon:empty {
    width: 1em; }

.glyphicon-asterisk:before {
  content: "\2a"; }

.glyphicon-plus:before {
  content: "\2b"; }

.glyphicon-euro:before {
  content: "\20ac"; }

.glyphicon-minus:before {
  content: "\2212"; }

.glyphicon-cloud:before {
  content: "\2601"; }

.glyphicon-envelope:before {
  content: "\2709"; }

.glyphicon-pencil:before {
  content: "\270f"; }

.glyphicon-glass:before {
  content: "\e001"; }

.glyphicon-music:before {
  content: "\e002"; }

.glyphicon-search:before {
  content: "\e003"; }

.glyphicon-heart:before {
  content: "\e005"; }

.glyphicon-star:before {
  content: "\e006"; }

.glyphicon-star-empty:before {
  content: "\e007"; }

.glyphicon-user:before {
  content: "\e008"; }

.glyphicon-film:before {
  content: "\e009"; }

.glyphicon-th-large:before {
  content: "\e010"; }

.glyphicon-th:before {
  content: "\e011"; }

.glyphicon-th-list:before {
  content: "\e012"; }

.glyphicon-ok:before {
  content: "\e013"; }

.glyphicon-remove:before {
  content: "\e014"; }

.glyphicon-zoom-in:before {
  content: "\e015"; }

.glyphicon-zoom-out:before {
  content: "\e016"; }

.glyphicon-off:before {
  content: "\e017"; }

.glyphicon-signal:before {
  content: "\e018"; }

.glyphicon-cog:before {
  content: "\e019"; }

.glyphicon-trash:before {
  content: "\e020"; }

.glyphicon-home:before {
  content: "\e021"; }

.glyphicon-file:before {
  content: "\e022"; }

.glyphicon-time:before {
  content: "\e023"; }

.glyphicon-road:before {
  content: "\e024"; }

.glyphicon-download-alt:before {
  content: "\e025"; }

.glyphicon-download:before {
  content: "\e026"; }

.glyphicon-upload:before {
  content: "\e027"; }

.glyphicon-inbox:before {
  content: "\e028"; }

.glyphicon-play-circle:before {
  content: "\e029"; }

.glyphicon-repeat:before {
  content: "\e030"; }

.glyphicon-refresh:before {
  content: "\e031"; }

.glyphicon-list-alt:before {
  content: "\e032"; }

.glyphicon-lock:before {
  content: "\e033"; }

.glyphicon-flag:before {
  content: "\e034"; }

.glyphicon-headphones:before {
  content: "\e035"; }

.glyphicon-volume-off:before {
  content: "\e036"; }

.glyphicon-volume-down:before {
  content: "\e037"; }

.glyphicon-volume-up:before {
  content: "\e038"; }

.glyphicon-qrcode:before {
  content: "\e039"; }

.glyphicon-barcode:before {
  content: "\e040"; }

.glyphicon-tag:before {
  content: "\e041"; }

.glyphicon-tags:before {
  content: "\e042"; }

.glyphicon-book:before {
  content: "\e043"; }

.glyphicon-bookmark:before {
  content: "\e044"; }

.glyphicon-print:before {
  content: "\e045"; }

.glyphicon-camera:before {
  content: "\e046"; }

.glyphicon-font:before {
  content: "\e047"; }

.glyphicon-bold:before {
  content: "\e048"; }

.glyphicon-italic:before {
  content: "\e049"; }

.glyphicon-text-height:before {
  content: "\e050"; }

.glyphicon-text-width:before {
  content: "\e051"; }

.glyphicon-align-left:before {
  content: "\e052"; }

.glyphicon-align-center:before {
  content: "\e053"; }

.glyphicon-align-right:before {
  content: "\e054"; }

.glyphicon-align-justify:before {
  content: "\e055"; }

.glyphicon-list:before {
  content: "\e056"; }

.glyphicon-indent-left:before {
  content: "\e057"; }

.glyphicon-indent-right:before {
  content: "\e058"; }

.glyphicon-facetime-video:before {
  content: "\e059"; }

.glyphicon-picture:before {
  content: "\e060"; }

.glyphicon-map-marker:before {
  content: "\e062"; }

.glyphicon-adjust:before {
  content: "\e063"; }

.glyphicon-tint:before {
  content: "\e064"; }

.glyphicon-edit:before {
  content: "\e065"; }

.glyphicon-share:before {
  content: "\e066"; }

.glyphicon-check:before {
  content: "\e067"; }

.glyphicon-move:before {
  content: "\e068"; }

.glyphicon-step-backward:before {
  content: "\e069"; }

.glyphicon-fast-backward:before {
  content: "\e070"; }

.glyphicon-backward:before {
  content: "\e071"; }

.glyphicon-play:before {
  content: "\e072"; }

.glyphicon-pause:before {
  content: "\e073"; }

.glyphicon-stop:before {
  content: "\e074"; }

.glyphicon-forward:before {
  content: "\e075"; }

.glyphicon-fast-forward:before {
  content: "\e076"; }

.glyphicon-step-forward:before {
  content: "\e077"; }

.glyphicon-eject:before {
  content: "\e078"; }

.glyphicon-chevron-left:before {
  content: "\e079"; }

.glyphicon-chevron-right:before {
  content: "\e080"; }

.glyphicon-plus-sign:before {
  content: "\e081"; }

.glyphicon-minus-sign:before {
  content: "\e082"; }

.glyphicon-remove-sign:before {
  content: "\e083"; }

.glyphicon-ok-sign:before {
  content: "\e084"; }

.glyphicon-question-sign:before {
  content: "\e085"; }

.glyphicon-info-sign:before {
  content: "\e086"; }

.glyphicon-screenshot:before {
  content: "\e087"; }

.glyphicon-remove-circle:before {
  content: "\e088"; }

.glyphicon-ok-circle:before {
  content: "\e089"; }

.glyphicon-ban-circle:before {
  content: "\e090"; }

.glyphicon-arrow-left:before {
  content: "\e091"; }

.glyphicon-arrow-right:before {
  content: "\e092"; }

.glyphicon-arrow-up:before {
  content: "\e093"; }

.glyphicon-arrow-down:before {
  content: "\e094"; }

.glyphicon-share-alt:before {
  content: "\e095"; }

.glyphicon-resize-full:before {
  content: "\e096"; }

.glyphicon-resize-small:before {
  content: "\e097"; }

.glyphicon-exclamation-sign:before {
  content: "\e101"; }

.glyphicon-gift:before {
  content: "\e102"; }

.glyphicon-leaf:before {
  content: "\e103"; }

.glyphicon-fire:before {
  content: "\e104"; }

.glyphicon-eye-open:before {
  content: "\e105"; }

.glyphicon-eye-close:before {
  content: "\e106"; }

.glyphicon-warning-sign:before {
  content: "\e107"; }

.glyphicon-plane:before {
  content: "\e108"; }

.glyphicon-calendar:before {
  content: "\e109"; }

.glyphicon-random:before {
  content: "\e110"; }

.glyphicon-comment:before {
  content: "\e111"; }

.glyphicon-magnet:before {
  content: "\e112"; }

.glyphicon-chevron-up:before {
  content: "\e113"; }

.glyphicon-chevron-down:before {
  content: "\e114"; }

.glyphicon-retweet:before {
  content: "\e115"; }

.glyphicon-shopping-cart:before {
  content: "\e116"; }

.glyphicon-folder-close:before {
  content: "\e117"; }

.glyphicon-folder-open:before {
  content: "\e118"; }

.glyphicon-resize-vertical:before {
  content: "\e119"; }

.glyphicon-resize-horizontal:before {
  content: "\e120"; }

.glyphicon-hdd:before {
  content: "\e121"; }

.glyphicon-bullhorn:before {
  content: "\e122"; }

.glyphicon-bell:before {
  content: "\e123"; }

.glyphicon-certificate:before {
  content: "\e124"; }

.glyphicon-thumbs-up:before {
  content: "\e125"; }

.glyphicon-thumbs-down:before {
  content: "\e126"; }

.glyphicon-hand-right:before {
  content: "\e127"; }

.glyphicon-hand-left:before {
  content: "\e128"; }

.glyphicon-hand-up:before {
  content: "\e129"; }

.glyphicon-hand-down:before {
  content: "\e130"; }

.glyphicon-circle-arrow-right:before {
  content: "\e131"; }

.glyphicon-circle-arrow-left:before {
  content: "\e132"; }

.glyphicon-circle-arrow-up:before {
  content: "\e133"; }

.glyphicon-circle-arrow-down:before {
  content: "\e134"; }

.glyphicon-globe:before {
  content: "\e135"; }

.glyphicon-wrench:before {
  content: "\e136"; }

.glyphicon-tasks:before {
  content: "\e137"; }

.glyphicon-filter:before {
  content: "\e138"; }

.glyphicon-briefcase:before {
  content: "\e139"; }

.glyphicon-fullscreen:before {
  content: "\e140"; }

.glyphicon-dashboard:before {
  content: "\e141"; }

.glyphicon-paperclip:before {
  content: "\e142"; }

.glyphicon-heart-empty:before {
  content: "\e143"; }

.glyphicon-link:before {
  content: "\e144"; }

.glyphicon-phone:before {
  content: "\e145"; }

.glyphicon-pushpin:before {
  content: "\e146"; }

.glyphicon-usd:before {
  content: "\e148"; }

.glyphicon-gbp:before {
  content: "\e149"; }

.glyphicon-sort:before {
  content: "\e150"; }

.glyphicon-sort-by-alphabet:before {
  content: "\e151"; }

.glyphicon-sort-by-alphabet-alt:before {
  content: "\e152"; }

.glyphicon-sort-by-order:before {
  content: "\e153"; }

.glyphicon-sort-by-order-alt:before {
  content: "\e154"; }

.glyphicon-sort-by-attributes:before {
  content: "\e155"; }

.glyphicon-sort-by-attributes-alt:before {
  content: "\e156"; }

.glyphicon-unchecked:before {
  content: "\e157"; }

.glyphicon-expand:before {
  content: "\e158"; }

.glyphicon-collapse-down:before {
  content: "\e159"; }

.glyphicon-collapse-up:before {
  content: "\e160"; }

.glyphicon-log-in:before {
  content: "\e161"; }

.glyphicon-flash:before {
  content: "\e162"; }

.glyphicon-log-out:before {
  content: "\e163"; }

.glyphicon-new-window:before {
  content: "\e164"; }

.glyphicon-record:before {
  content: "\e165"; }

.glyphicon-save:before {
  content: "\e166"; }

.glyphicon-open:before {
  content: "\e167"; }

.glyphicon-saved:before {
  content: "\e168"; }

.glyphicon-import:before {
  content: "\e169"; }

.glyphicon-export:before {
  content: "\e170"; }

.glyphicon-send:before {
  content: "\e171"; }

.glyphicon-floppy-disk:before {
  content: "\e172"; }

.glyphicon-floppy-saved:before {
  content: "\e173"; }

.glyphicon-floppy-remove:before {
  content: "\e174"; }

.glyphicon-floppy-save:before {
  content: "\e175"; }

.glyphicon-floppy-open:before {
  content: "\e176"; }

.glyphicon-credit-card:before {
  content: "\e177"; }

.glyphicon-transfer:before {
  content: "\e178"; }

.glyphicon-cutlery:before {
  content: "\e179"; }

.glyphicon-header:before {
  content: "\e180"; }

.glyphicon-compressed:before {
  content: "\e181"; }

.glyphicon-earphone:before {
  content: "\e182"; }

.glyphicon-phone-alt:before {
  content: "\e183"; }

.glyphicon-tower:before {
  content: "\e184"; }

.glyphicon-stats:before {
  content: "\e185"; }

.glyphicon-sd-video:before {
  content: "\e186"; }

.glyphicon-hd-video:before {
  content: "\e187"; }

.glyphicon-subtitles:before {
  content: "\e188"; }

.glyphicon-sound-stereo:before {
  content: "\e189"; }

.glyphicon-sound-dolby:before {
  content: "\e190"; }

.glyphicon-sound-5-1:before {
  content: "\e191"; }

.glyphicon-sound-6-1:before {
  content: "\e192"; }

.glyphicon-sound-7-1:before {
  content: "\e193"; }

.glyphicon-copyright-mark:before {
  content: "\e194"; }

.glyphicon-registration-mark:before {
  content: "\e195"; }

.glyphicon-cloud-download:before {
  content: "\e197"; }

.glyphicon-cloud-upload:before {
  content: "\e198"; }

.glyphicon-tree-conifer:before {
  content: "\e199"; }

.glyphicon-tree-deciduous:before {
  content: "\e200"; }

.caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 2px;
  vertical-align: middle;
  border-top: 4px solid #000;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
  border-bottom: 0 dotted; }

.dropdown {
  position: relative; }

.dropdown-toggle:focus {
  outline: 0; }

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1060;
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 0;
  list-style: none;
  font-size: 14px;
  background-color: #fff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 2px;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  background-clip: padding-box; }
  .dropdown-menu.pull-right {
    right: 0;
    left: auto; }
  .dropdown-menu .divider {
    height: 1px;
    margin: 4px 0;
    overflow: hidden;
    background-color: #e5e5e5; }
  .dropdown-menu > li > a {
    display: block;
    padding: 3px 20px;
    clear: both;
    font-weight: normal;
    line-height: 1.42857;
    color: #333333;
    white-space: nowrap; }

.dropdown-menu > li > a:hover, .dropdown-menu > li > a:focus {
  text-decoration: none;
  color: #76bb6a;
  background-color: transparent; }

.dropdown-menu > .active > a, .dropdown-menu > .active > a:focus {
  color: #76bb6a;
  font-weight: bold;
  text-decoration: none;
  outline: 0;
  background-color: transparent; }
.dropdown-menu > .active > a:hover {
  background-color: #76bb6a;
  color: white; }

.dropdown-menu > .disabled > a, .dropdown-menu > .disabled > a:hover, .dropdown-menu > .disabled > a:focus {
  color: #999999; }

.dropdown-menu > .disabled > a:hover, .dropdown-menu > .disabled > a:focus {
  text-decoration: none;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  cursor: not-allowed; }

.open > .dropdown-menu {
  display: block; }
.open > a {
  outline: 0; }

.dropdown-header {
  display: block;
  padding: 3px 20px;
  font-size: 12px;
  line-height: 1.42857;
  color: #999999; }

.dropdown-backdrop {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 1050; }

.pull-right > .dropdown-menu {
  right: 0;
  left: auto; }

.dropup .caret, .navbar-fixed-bottom .dropdown .caret {
  border-top: 0 dotted;
  border-bottom: 4px solid #000;
  content: ""; }
.dropup .dropdown-menu, .navbar-fixed-bottom .dropdown .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-bottom: 1px; }

@media (min-width: 890px) {
  .navbar-right .dropdown-menu {
    right: 0;
    left: auto; }
  .navbar .dropdown-menu {
    top: 85%; }
    .navbar .dropdown-menu:after {
      position: absolute;
      top: -4px;
      left: 14px;
      display: inline-block;
      border-right: 4px solid transparent;
      border-bottom: 4px solid white;
      border-left: 4px solid transparent;
      content: ''; }
  .navbar .navbar-right .dropdown-menu:after {
    left: auto;
    right: 14px; } }

.btn-default .caret {
  border-top-color: #333; }
.btn-primary .caret, .btn-success .caret, .btn-warning .caret, .btn-danger .caret, .btn-info .caret {
  border-top-color: #fff; }

.dropup .btn-default .caret {
  border-bottom-color: #333; }
.dropup .btn-primary .caret, .dropup .btn-success .caret, .dropup .btn-warning .caret, .dropup .btn-danger .caret, .dropup .btn-info .caret {
  border-bottom-color: #fff; }

.btn-group, .btn-group-vertical {
  position: relative;
  display: inline-block;
  vertical-align: middle; }
  .btn-group > .btn, .btn-group-vertical > .btn {
    position: relative;
    float: left; }
    .btn-group > .btn:hover, .btn-group > .btn:focus, .btn-group > .btn:active, .btn-group > .btn.active, .btn-group-vertical > .btn:hover, .btn-group-vertical > .btn:focus, .btn-group-vertical > .btn:active, .btn-group-vertical > .btn.active {
      z-index: 2; }
    .btn-group > .btn:focus, .btn-group-vertical > .btn:focus {
      outline: none; }

.btn-group .btn + .btn, .btn-group .btn + .btn-group, .btn-group .btn-group + .btn, .btn-group .btn-group + .btn-group {
  margin-left: -1px; }

.btn-toolbar:before, .btn-toolbar:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */ }
.btn-toolbar:after {
  clear: both; }
.btn-toolbar .btn-group {
  float: left; }
.btn-toolbar > .btn + .btn, .btn-toolbar > .btn + .btn-group, .btn-toolbar > .btn-group + .btn, .btn-toolbar > .btn-group + .btn-group {
  margin-left: 5px; }

.btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
  border-radius: 0; }

.btn-group > .btn:first-child {
  margin-left: 0; }
  .btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0; }

.btn-group > .btn:last-child:not(:first-child), .btn-group > .dropdown-toggle:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0; }

.btn-group > .btn-group {
  float: left; }

.btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0; }

.btn-group > .btn-group:first-child > .btn:last-child, .btn-group > .btn-group:first-child > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0; }

.btn-group > .btn-group:last-child > .btn:first-child {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0; }

.btn-group .dropdown-toggle:active, .btn-group.open .dropdown-toggle {
  outline: 0; }




.btn-group > .btn + .dropdown-toggle {
  padding-left: 8px;
  padding-right: 8px; }

.btn-group > .btn-lg + .dropdown-toggle, .btn-group > .btn-lg + .btn-group-lg > .btn, .btn-group-lg > .btn-group > .btn-lg + .btn {
  padding-left: 12px;
  padding-right: 12px; }

.btn-group.open .dropdown-toggle {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); }
  .btn-group.open .dropdown-toggle.btn-link {
    -webkit-box-shadow: none;
    box-shadow: none; }

.btn .caret {
  margin-left: 0; }

.btn-lg .caret, .btn-lg .btn-group-lg > .btn, .btn-group-lg > .btn-lg .btn {
  border-width: 5px 5px 0;
  border-bottom-width: 0; }

.dropup .btn-lg .caret, .dropup .btn-lg .btn-group-lg > .btn, .btn-group-lg > .dropup .btn-lg .btn {
  border-width: 0 5px 5px; }

.btn-group-vertical > .btn, .btn-group-vertical > .btn-group {
  display: block;
  float: none;
  width: 100%;
  max-width: 100%; }
.btn-group-vertical > .btn-group:before, .btn-group-vertical > .btn-group:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */ }
.btn-group-vertical > .btn-group:after {
  clear: both; }
.btn-group-vertical > .btn-group > .btn {
  float: none; }
.btn-group-vertical > .btn + .btn, .btn-group-vertical > .btn + .btn-group, .btn-group-vertical > .btn-group + .btn, .btn-group-vertical > .btn-group + .btn-group {
  margin-top: -1px;
  margin-left: 0; }

.btn-group-vertical > .btn:not(:first-child):not(:last-child) {
  border-radius: 0; }
.btn-group-vertical > .btn:first-child:not(:last-child) {
  border-top-right-radius: 2px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0; }
.btn-group-vertical > .btn:last-child:not(:first-child) {
  border-bottom-left-radius: 2px;
  border-top-right-radius: 0;
  border-top-left-radius: 0; }

.btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0; }

.btn-group-vertical > .btn-group:first-child > .btn:last-child, .btn-group-vertical > .btn-group:first-child > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0; }

.btn-group-vertical > .btn-group:last-child > .btn:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0; }

.btn-group-justified {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-collapse: separate; }
  .btn-group-justified .btn {
    float: none;
    display: table-cell;
    width: 1%; }

[data-toggle="buttons"] > .btn > input[type="radio"], [data-toggle="buttons"] > .btn > input[type="checkbox"] {
  display: none; }

.input-group {
  position: relative;
  display: table;
  border-collapse: separate; }
  .input-group.col {
    float: none;
    padding-left: 0;
    padding-right: 0; }
  .input-group .form-control {
    width: 100%;
    margin-bottom: 0; }



.input-group-addon, .input-group-btn, .input-group .form-control {
  display: table-cell; }
  .input-group-addon:not(:first-child):not(:last-child), .input-group-btn:not(:first-child):not(:last-child), .input-group .form-control:not(:first-child):not(:last-child) {
    border-radius: 0; }

.input-group-addon, .input-group-btn {
  width: 1%;
  white-space: nowrap;
  vertical-align: middle; }

.input-group-addon {
  padding: 6px 12px;
  font-size: 14px;
  font-weight: normal;
  line-height: 1;
  color: #555555;
  text-align: center;
  background-color: #ddd;
  border: 1px solid #ccc;
  border-radius: 2px; }
  .input-group-addon.input-sm, .input-group-sm > .input-group-addon.form-control, .input-group-sm > .input-group-addon, .input-group-sm > .input-group-btn > .input-group-addon.btn {
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 2px; }
  .input-group-addon.input-lg, .input-group-lg > .input-group-addon.form-control, .input-group-lg > .input-group-addon, .input-group-lg > .input-group-btn > .input-group-addon.btn {
    padding: 12px 20px;
    font-size: 19px;
    border-radius: 2px; }
  .input-group-addon input[type="radio"], .input-group-addon input[type="checkbox"] {
    margin-top: 0; }

.input-group .form-control:first-child, .input-group-addon:first-child, .input-group-btn:first-child > .btn, .input-group-btn:first-child > .dropdown-toggle, .input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0; }

.input-group-addon:first-child {
  border-right: 0; }

.input-group .form-control:last-child, .input-group-addon:last-child, .input-group-btn:last-child > .btn, .input-group-btn:last-child > .dropdown-toggle, .input-group-btn:first-child > .btn:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0; }

.input-group-addon:last-child {
  border-left: 0; }

.input-group-btn {
  position: relative;
  white-space: nowrap; }
  .input-group-btn:first-child > .btn {
    margin-right: -1px; }
  .input-group-btn:last-child > .btn {
    margin-left: -1px; }

.input-group-btn > .btn {
  position: relative; }
  .input-group-btn > .btn + .btn {
    margin-left: -4px; }
  .input-group-btn > .btn:hover, .input-group-btn > .btn:active {
    z-index: 2; }

.nav {
  margin-bottom: 0;
  padding-left: 0;
  list-style: none; }
  .nav:before, .nav:after {
    content: " ";
    /* 1 */
    display: table;
    /* 2 */ }
  .nav:after {
    clear: both; }
  .nav > li {
    position: relative;
    display: block; }
    .nav > li > a {
      position: relative;
      display: block;
      padding: 10px 15px; }
      .nav > li > a:hover, .nav > li > a:focus {
        text-decoration: none;
        background-color: #ddd; }
    .nav > li.disabled > a {
      color: #999999; }
      .nav > li.disabled > a:hover, .nav > li.disabled > a:focus {
        color: #999999;
        text-decoration: none;
        background-color: transparent;
        cursor: not-allowed; }
  .nav .open > a, .nav .open > a:hover, .nav .open > a:focus {
    background-color: #ddd;
    border-color: #6a92bb; }
    .nav .open > a .caret, .nav .open > a:hover .caret, .nav .open > a:focus .caret {
      border-top-color: #446c95;
      border-bottom-color: #446c95; }
  .nav .nav-divider {
    height: 1px;
    margin: 4px 0;
    overflow: hidden;
    background-color: #e5e5e5; }
  .nav > li > a > img {
    max-width: none; }

.nav.nav-tabs {
  border-bottom: 1px solid #d0d0d0; }
  .nav.nav-tabs > li {
    color: #6a92bb;
    float: left;
    border-radius: 1px;
    margin-right: 6px;
    margin-bottom: -1px;
    background-color: #e2e2e2;
    transition: background-color 150ms; }
    .nav.nav-tabs > li.active {
      background-color: #2D8C20; }
    .nav.nav-tabs > li.dropdown.open a.dropdown-toggle {
      background-color: #e2e2e2;
      border-color: transparent; }
    .nav.nav-tabs > li > a {
      color: #6a92bb;
      transition: background-color 150ms;
      background-color: #e2e2e2;
      font-weight: bold;
      border-radius: 0;
      border: 1px solid transparent;
      border-top: none;
      border-bottom: none;
      padding: 10px 28px;
      margin-bottom: 0px;
      margin-top: 2px; }
      .nav.nav-tabs > li > a:hover {
        background-color: #e2e2e2; }
      .nav.nav-tabs > li > a .badge, .nav.nav-tabs > li > a .badge-primary {
        color: inherit;
        background-color: transparent;
        margin-left: 11px;
        margin-right: -7px; }
    .nav.nav-tabs > li.active .badge, .nav.nav-tabs > li.active .badge-primary {
      color: #fff;
      background-color: #2D8C20;
      margin-right: 0; }
    .nav.nav-tabs > li.active > a {
      background-color: #fff;
      border-top: none;
      border-bottom: none; }
      .nav.nav-tabs > li.active > a, .nav.nav-tabs > li.active > a:hover, .nav.nav-tabs > li.active > a:focus {
        cursor: default;
        color: #2D8C20;
        background-color: #fff;
        border: 1px solid #d0d0d0;
        border-bottom: none;
        border-top: none; }
  .nav.nav-tabs.has-active-sub-tab > li.active .badge, .nav.nav-tabs.has-active-sub-tab > li.active .badge-primary {
    color: inherit;
    background-color: inherit;
    margin-right: -7px; }

.nav.nav-tabs-md > li > a {
  padding: 7px 15px; }

.nav.nav-sub-tabs {
  border-top: 1px solid #d0d0d0;
  border-bottom: 1px solid #d0d0d0;
  border-radius: 0;
  font-size: 12px;
  top: -1px;
  position: relative;
  padding-top: 4px; }
  .nav.nav-sub-tabs > li {
    float: left; }
    .nav.nav-sub-tabs > li:first-child a {
      border-left: none; }
    .nav.nav-sub-tabs > li:hover > a {
      color: #2D8C20; }
    .nav.nav-sub-tabs > li > a {
      transition: color 200ms;
      color: #6a92bb;
      position: relative;
      padding: 5px 36px;
      margin-right: 0;
      margin-bottom: 1px;
      background-color: inherit;
      font-weight: normal;
      border-radius: 0;
      border: none;
      border-left: 1px solid #d0d0d0;
      line-height: 1.5em; }
      .nav.nav-sub-tabs > li > a .badge, .nav.nav-sub-tabs > li > a .badge-primary {
        color: inherit;
        background-color: transparent;
        margin-left: 11px;
        margin-right: -7px;
        font-weight: inherit;
        color: inherit;
        background-color: transparent;
        margin-left: 11px;
        margin-right: -7px; }
      .nav.nav-sub-tabs > li > a:after {
        content: "";
        position: absolute;
        margin-left: -16px;
        display: block;
        border-top: 8px solid #2D8C20;
        border-right: 16px solid transparent;
        border-left: 16px solid transparent;
        left: 50%;
        bottom: 0px;
        opacity: 0;
        transition: opacity 20ms; }
    .nav.nav-sub-tabs > li.active {
      border-top-color: #2D8C20; }
    .nav.nav-sub-tabs > li.active .badge, .nav.nav-sub-tabs > li.active .badge-primary {
      color: white;
      background-color: #2D8C20;
      margin-right: 0; }
    .nav.nav-sub-tabs > li.active > a {
      font-weight: bold;
      background-color: #fff;
      border-top: none;
      border-bottom: none;
      color: #2D8C20; }
      .nav.nav-sub-tabs > li.active > a, .nav.nav-sub-tabs > li.active > a:hover, .nav.nav-sub-tabs > li.active > a:focus {
        border-top: none;
        border-bottom: none;
        border-right: none;
        color: #2D8C20; }
      .nav.nav-sub-tabs > li.active > a:after {
        bottom: -10px;
        opacity: 1; }

.nav-pills > li {
  float: left; }
  .nav-pills > li > a {
    border-radius: 2px; }
  .nav-pills > li + li {
    margin-left: 2px; }
  .nav-pills > li.active > a, .nav-pills > li.active > a:hover, .nav-pills > li.active > a:focus {
    color: #fff;
    background-color: #76bb6a; }
    .nav-pills > li.active > a .caret, .nav-pills > li.active > a:hover .caret, .nav-pills > li.active > a:focus .caret {
      border-top-color: #fff;
      border-bottom-color: #fff; }

.nav-stacked > li {
  float: none; }
  .nav-stacked > li + li {
    margin-top: 2px;
    margin-left: 0; }

.nav-justified, .nav.nav-tabs.nav-justified {
  width: 100%; }
  .nav-justified > li, .nav-justified > .nav.nav-tabs.nav-justified {
    float: none; }
    .nav-justified > li > a, .nav-justified > li > .nav.nav-tabs.nav-justified {
      text-align: center;
      margin-bottom: 5px; }
  .nav-justified > .dropdown .dropdown-menu, .nav-justified > .dropdown .nav.nav-tabs.nav-justified {
    top: auto;
    left: auto; }
  @media (min-width: 768px) {
    .nav-justified > li, .nav-justified > .nav.nav-tabs.nav-justified {
      display: table-cell;
      width: 1%; }
      .nav-justified > li > a, .nav-justified > li > .nav.nav-tabs.nav-justified {
        margin-bottom: 0; } }

.nav-tabs-justified, .nav.nav-tabs.nav-justified, .nav.nav-tabs.nav-justified {
  border-bottom: 0; }
  .nav-tabs-justified > li > a, .nav-tabs-justified > li > .nav.nav-tabs.nav-justified, .nav-tabs-justified > li > .nav.nav-tabs.nav-justified {
    margin-right: 0;
    border-radius: 2px; }
  .nav-tabs-justified > .active > a, .nav-tabs-justified > .active > .nav.nav-tabs.nav-justified, .nav-tabs-justified > .active > .nav.nav-tabs.nav-justified, .nav-tabs-justified > .active > a:hover, .nav-tabs-justified > .active > .nav.nav-tabs.nav-justified, .nav-tabs-justified > .active > .nav.nav-tabs.nav-justified, .nav-tabs-justified > .active > a:focus, .nav-tabs-justified > .active > .nav.nav-tabs.nav-justified, .nav-tabs-justified > .active > .nav.nav-tabs.nav-justified {
    border: 1px solid #ddd; }
  @media (min-width: 768px) {
    .nav-tabs-justified > li > a, .nav-tabs-justified > li > .nav.nav-tabs.nav-justified, .nav-tabs-justified > li > .nav.nav-tabs.nav-justified {
      border-bottom: 1px solid #ddd;
      border-radius: 2px 2px 0 0; }
    .nav-tabs-justified > .active > a, .nav-tabs-justified > .active > .nav.nav-tabs.nav-justified, .nav-tabs-justified > .active > .nav.nav-tabs.nav-justified, .nav-tabs-justified > .active > a:hover, .nav-tabs-justified > .active > .nav.nav-tabs.nav-justified, .nav-tabs-justified > .active > .nav.nav-tabs.nav-justified, .nav-tabs-justified > .active > a:focus, .nav-tabs-justified > .active > .nav.nav-tabs.nav-justified, .nav-tabs-justified > .active > .nav.nav-tabs.nav-justified {
      border-bottom-color: #fff; } }

.tab-content > .tab-pane {
  display: none; }
.tab-content > .active {
  display: block; }

.nav .caret {
  border-top-color: #6a92bb;
  border-bottom-color: #6a92bb; }

.nav a:hover .caret {
  border-top-color: #446c95;
  border-bottom-color: #446c95; }

.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-right-radius: 0;
  border-top-left-radius: 0; }

.nav-list {
  padding-right: 10px;
  padding-left: 10px;
  margin-bottom: 0; }
  .nav-list > li > a {
    padding: 5px 10px;
    margin-right: -10px;
    margin-left: -10px; }
  .nav-list > .active > a, .nav-list > .active > a:hover, .nav-list > .active > a:focus {
    color: white;
    background-color: #76bb6a; }

.nav-header {
  margin-right: -10px;
  margin-left: -10px;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  font-family: "BryantProRegular", 'Helvetica Neue', helvetica, arial, sans-serif, "Helvetica Neue";
  font-weight: normal;
  text-transform: uppercase;
  padding: 5px 10px;
  color: #999; }

.navbar {
  position: relative;
  min-height: 61px;
  margin-bottom: 20px;
  border: 1px solid transparent; }
  .navbar:before, .navbar:after {
    content: " ";
    /* 1 */
    display: table;
    /* 2 */ }
  .navbar:after {
    clear: both; }
  @media (min-width: 890px) {
    .navbar {
      border-radius: 2px; } }

.navbar-header:before, .navbar-header:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */ }
.navbar-header:after {
  clear: both; }
@media (min-width: 890px) {
  .navbar-header {
    float: left; } }

.navbar-collapse {
  max-height: 350px;
  overflow-x: visible;
  padding-right: 15px;
  padding-left: 15px;
  border-top: 1px solid transparent;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  -webkit-overflow-scrolling: touch; }
  .navbar-collapse:before, .navbar-collapse:after {
    content: " ";
    /* 1 */
    display: table;
    /* 2 */ }
  .navbar-collapse:after {
    clear: both; }
  .navbar-collapse.in {
    overflow-y: auto; }
  @media (min-width: 890px) {
    .navbar-collapse {
      width: auto;
      border-top: 0;
      box-shadow: none; }
      .navbar-collapse.collapse {
        display: block !important;
        height: auto !important;
        padding-bottom: 0;
        overflow: visible !important; }
      .navbar-collapse.in {
        overflow-y: auto; }
      .navbar-collapse .navbar-nav.navbar-left:first-child {
        margin-left: -15px; }
      .navbar-collapse .navbar-nav.navbar-right:last-child {
        margin-right: -15px; }
      .navbar-collapse .navbar-text:last-child {
        margin-right: 0; } }

.container > .navbar-header, .container > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px; }
  @media (min-width: 890px) {
    .container > .navbar-header, .container > .navbar-collapse {
      margin-right: 0;
      margin-left: 0; } }

.navbar-static-top {
  z-index: 1000;
  border-width: 0 0 1px; }
  @media (min-width: 890px) {
    .navbar-static-top {
      border-radius: 0; } }

.navbar-fixed-top, .navbar-fixed-bottom {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 1020; }
  @media (min-width: 890px) {
    .navbar-fixed-top, .navbar-fixed-bottom {
      border-radius: 0; } }

.navbar-fixed-top {
  top: 0;
  border-width: 3px 0 1px 0; }

.navbar-fixed-bottom {
  bottom: 0;
  margin-bottom: 0;
  border-width: 1px 0 0; }

.navbar-brand {
  float: left;
  padding: 20.5px 15px;
  font-size: 19px;
  line-height: 20px; }
  .navbar-brand:hover, .navbar-brand:focus {
    text-decoration: none; }
  @media (min-width: 890px) {
    .navbar > .container .navbar-brand {
      margin-left: -15px; } }

.navbar-toggle {
  position: relative;
  float: right;
  margin-right: 15px;
  padding: 9px 10px;
  margin-top: 13.5px;
  margin-bottom: 13.5px;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 2px; }
  .navbar-toggle .icon-bar {
    display: block;
    width: 22px;
    height: 2px;
    border-radius: 1px; }
  .navbar-toggle .icon-bar + .icon-bar {
    margin-top: 4px; }
  @media (min-width: 890px) {
    .navbar-toggle {
      display: none; } }

.navbar-nav {
  margin: 10.25px -15px; }
  .navbar-nav > li > a {
    padding-top: 10px;
    padding-bottom: 10px;
    line-height: 20px;
    font-family: "BryantProRegular", 'Helvetica Neue', helvetica, arial, sans-serif, "Helvetica Neue";
    text-transform: uppercase; }
  .navbar-nav .navbar-badge {
    text-transform: none;
    font-weight: normal;
    font-size: 13px; }
  @media (max-width: 890px) {
    .navbar-nav .open .dropdown-menu {
      position: static;
      float: none;
      width: auto;
      margin-top: 0;
      background-color: transparent;
      border: 0;
      box-shadow: none; }
      .navbar-nav .open .dropdown-menu > li > a, .navbar-nav .open .dropdown-menu .dropdown-header {
        padding: 5px 15px 5px 25px; }
      .navbar-nav .open .dropdown-menu > li > a {
        line-height: 20px; }
        .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-nav .open .dropdown-menu > li > a:focus {
          background-image: none; } }
  @media (min-width: 890px) {
    .navbar-nav {
      float: left;
      margin: 0; }
      .navbar-nav > li {
        float: left; }
        .navbar-nav > li > a {
          padding-top: 20.5px;
          padding-bottom: 20.5px; } }

.navbar-nav.navbar-no-collapse {
  float: left;
  margin: 0; }
  .navbar-nav.navbar-no-collapse > li {
    float: left; }
    .navbar-nav.navbar-no-collapse > li > a {
      padding-top: 20.5px;
      padding-bottom: 20.5px; }

@media (min-width: 890px) {
  .navbar-left {
    float: left !important; }
  .navbar-right {
    float: right !important; } }

.navbar-always-left {
  float: left !important; }

.navbar-always-right {
  float: right !important; }

.navbar-form {
  margin-left: -15px;
  margin-right: -15px;
  padding: 10px 15px;
  border-top: 1px solid transparent;
  border-bottom: 1px solid transparent;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  margin-top: 13.5px;
  margin-bottom: 13.5px; }
  @media (max-width: 767px) {
    .navbar-form .form-group {
      margin-bottom: 5px; } }
  @media (min-width: 890px) {
    .navbar-form {
      width: auto;
      border: 0;
      margin-left: 0;
      margin-right: 0;
      padding-top: 0;
      padding-bottom: 0;
      -webkit-box-shadow: none;
      box-shadow: none; } }

.navbar-nav > li > .dropdown-menu {
  margin-top: 0;
  border-top-right-radius: 0;
  border-top-left-radius: 0; }

.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0; }

.navbar-nav.pull-right > li > .dropdown-menu, .navbar-nav > li > .dropdown-menu.pull-right {
  left: auto;
  right: 0; }

.navbar-btn {
  margin-top: 13.5px;
  margin-bottom: 13.5px; }

.navbar-text {
  float: left;
  margin-top: 20.5px;
  margin-bottom: 20.5px; }
  @media (min-width: 890px) {
    .navbar-text {
      margin-left: 15px;
      margin-right: 15px; } }

.navbar-default {
  background-color: #f8f8f8;
  border-color: #e7e7e7; }
  .navbar-default .navbar-brand {
    color: #777; }
    .navbar-default .navbar-brand:hover, .navbar-default .navbar-brand:focus {
      color: #5e5e5e;
      background-color: transparent; }
  .navbar-default .navbar-text {
    color: #777; }
  .navbar-default .navbar-nav > li > a {
    color: #777; }
    .navbar-default .navbar-nav > li > a:hover, .navbar-default .navbar-nav > li > a:focus {
      color: #333;
      background-color: transparent; }
  .navbar-default .navbar-nav > .active > a, .navbar-default .navbar-nav > .active > a:hover, .navbar-default .navbar-nav > .active > a:focus {
    color: #555;
    background-color: #e7e7e7; }
  .navbar-default .navbar-nav > .disabled > a, .navbar-default .navbar-nav > .disabled > a:hover, .navbar-default .navbar-nav > .disabled > a:focus {
    color: #ccc;
    background-color: transparent; }
  .navbar-default .navbar-toggle {
    border-color: #ddd; }
    .navbar-default .navbar-toggle:hover, .navbar-default .navbar-toggle:focus {
      background-color: #ddd; }
    .navbar-default .navbar-toggle .icon-bar {
      background-color: #ccc; }
  .navbar-default .navbar-collapse, .navbar-default .navbar-form {
    border-color: #e7e7e7; }
  .navbar-default .navbar-nav > .dropdown > a:hover .caret, .navbar-default .navbar-nav > .dropdown > a:focus .caret {
    border-top-color: #333;
    border-bottom-color: #333; }
  .navbar-default .navbar-nav > .open > a, .navbar-default .navbar-nav > .open > a:hover, .navbar-default .navbar-nav > .open > a:focus {
    background-color: #e7e7e7;
    color: #555; }
    .navbar-default .navbar-nav > .open > a .caret, .navbar-default .navbar-nav > .open > a:hover .caret, .navbar-default .navbar-nav > .open > a:focus .caret {
      border-top-color: #555;
      border-bottom-color: #555; }
  .navbar-default .navbar-nav > .dropdown > a .caret {
    border-top-color: #777;
    border-bottom-color: #777; }
  @media (max-width: 890px) {
    .navbar-default .navbar-nav .open .dropdown-menu > li > a {
      color: #777; }
      .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
        color: #333;
        background-color: transparent; }
    .navbar-default .navbar-nav .open .dropdown-menu > .active > a, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
      color: #555;
      background-color: #e7e7e7; }
    .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a, .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus {
      color: #ccc;
      background-color: transparent; } }
  .navbar-default .navbar-link {
    color: #777; }
    .navbar-default .navbar-link:hover {
      color: #333; }

.navbar-inverse {
  background-color: #21242b;
  border-color: #76bb6a; }
  .navbar-inverse .navbar-brand {
    color: white; }
    .navbar-inverse .navbar-brand:hover, .navbar-inverse .navbar-brand:focus {
      color: #fff;
      background-color: transparent; }
  .navbar-inverse .navbar-text {
    color: #e5e5e5; }
  .navbar-inverse .navbar-nav > li > a {
    color: white; }
    .navbar-inverse .navbar-nav > li > a:hover, .navbar-inverse .navbar-nav > li > a:focus {
      color: #76bb6a;
      background-color: transparent; }
  .navbar-inverse .navbar-nav > .active > a, .navbar-inverse .navbar-nav > .active > a:hover, .navbar-inverse .navbar-nav > .active > a:focus {
    color: #76bb6a;
    background-color: transparent; }
  .navbar-inverse .navbar-nav > .disabled > a, .navbar-inverse .navbar-nav > .disabled > a:hover, .navbar-inverse .navbar-nav > .disabled > a:focus {
    color: #444;
    background-color: transparent; }
  .navbar-inverse .navbar-toggle {
    border-color: #333; }
    .navbar-inverse .navbar-toggle:hover, .navbar-inverse .navbar-toggle:focus {
      background-color: #333; }
    .navbar-inverse .navbar-toggle .icon-bar {
      background-color: #fff; }
  .navbar-inverse .navbar-collapse, .navbar-inverse .navbar-form {
    border-color: #111317; }
  .navbar-inverse .navbar-nav > .open > a, .navbar-inverse .navbar-nav > .open > a:hover, .navbar-inverse .navbar-nav > .open > a:focus {
    background-color: transparent;
    color: #76bb6a; }
  .navbar-inverse .navbar-nav > .dropdown > a:hover .caret {
    border-top-color: #76bb6a;
    border-bottom-color: #76bb6a; }
  .navbar-inverse .navbar-nav > .dropdown > a .caret {
    border-top-color: white;
    border-bottom-color: white; }
  .navbar-inverse .navbar-nav > .open > a .caret, .navbar-inverse .navbar-nav > .open > a:hover .caret, .navbar-inverse .navbar-nav > .open > a:focus .caret {
    border-top-color: #76bb6a;
    border-bottom-color: #76bb6a; }
  @media (max-width: 890px) {
    .navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header {
      border-color: #76bb6a; }
    .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
      color: white;
      font-family: "BryantProRegular", 'Helvetica Neue', helvetica, arial, sans-serif, "Helvetica Neue"; }
      .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus {
        color: #76bb6a;
        background-color: transparent; }
    .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a, .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus {
      color: #76bb6a;
      background-color: transparent; }
    .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a, .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:focus {
      color: #444;
      background-color: transparent; } }
  .navbar-inverse .navbar-link {
    color: white; }
    .navbar-inverse .navbar-link:hover {
      color: #76bb6a; }

.breadcrumb {
  padding: 8px 15px;
  margin-bottom: 20px;
  list-style: none;
  background-color: #f5f5f5;
  border-radius: 2px; }
  .breadcrumb > li {
    display: inline-block; }
    .breadcrumb > li + li:before {
      content: "/\00a0";
      padding: 0 5px;
      color: #ccc; }
  .breadcrumb > .active {
    color: #999999; }

.pagination {
  display: inline-block;
  padding-left: 0;
  margin: 20px 0;
  border-radius: 2px; }
  .pagination > li {
    display: inline; }
    .pagination > li > a, .pagination > li > span {
      position: relative;
      float: left;
      padding: 6px 12px;
      line-height: 1.42857;
      text-decoration: none;
      background-color: #fff;
      border: 1px solid #ddd;
      margin-left: -1px; }
    .pagination > li:first-child > a, .pagination > li:first-child > span {
      margin-left: 0;
      border-bottom-left-radius: 2px;
      border-top-left-radius: 2px; }
    .pagination > li:last-child > a, .pagination > li:last-child > span {
      border-bottom-right-radius: 2px;
      border-top-right-radius: 2px; }
  .pagination > li > a:hover, .pagination > li > a:focus, .pagination > li > span:hover, .pagination > li > span:focus {
    background-color: #ddd; }
  .pagination > .active > a, .pagination > .active > a:hover, .pagination > .active > a:focus, .pagination > .active > span, .pagination > .active > span:hover, .pagination > .active > span:focus {
    z-index: 2;
    color: #fff;
    background-color: #76bb6a;
    border-color: #76bb6a;
    cursor: default; }
  .pagination > .disabled > span, .pagination > .disabled > span:hover, .pagination > .disabled > span:focus, .pagination > .disabled > a, .pagination > .disabled > a:hover, .pagination > .disabled > a:focus {
    color: #999999;
    background-color: #fff;
    border-color: #ddd;
    cursor: not-allowed; }

.pagination-lg > li > a, .pagination-lg > li > span {
  padding: 12px 20px;
  font-size: 19px; }
.pagination-lg > li:first-child > a, .pagination-lg > li:first-child > span {
  border-bottom-left-radius: 2px;
  border-top-left-radius: 2px; }
.pagination-lg > li:last-child > a, .pagination-lg > li:last-child > span {
  border-bottom-right-radius: 2px;
  border-top-right-radius: 2px; }

.pagination-sm > li > a, .pagination-sm > li > span {
  padding: 5px 10px;
  font-size: 12px; }
.pagination-sm > li:first-child > a, .pagination-sm > li:first-child > span {
  border-bottom-left-radius: 2px;
  border-top-left-radius: 2px; }
.pagination-sm > li:last-child > a, .pagination-sm > li:last-child > span {
  border-bottom-right-radius: 2px;
  border-top-right-radius: 2px; }

.pager {
  padding-left: 0;
  margin: 20px 0;
  list-style: none;
  text-align: center; }
  .pager:before, .pager:after {
    content: " ";
    /* 1 */
    display: table;
    /* 2 */ }
  .pager:after {
    clear: both; }
  .pager li {
    display: inline; }
    .pager li > a, .pager li > span {
      display: inline-block;
      padding: 5px 14px;
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 15px; }
    .pager li > a:hover, .pager li > a:focus {
      text-decoration: none;
      background-color: #ddd; }
  .pager .next > a, .pager .next > span {
    float: right; }
  .pager .previous > a, .pager .previous > span {
    float: left; }
  .pager .disabled > a, .pager .disabled > a:hover, .pager .disabled > a:focus, .pager .disabled > span {
    color: #999999;
    background-color: #fff;
    cursor: not-allowed; }

.label {
  display: inline;
  padding: 0.2em 0.6em 0.3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25em; }
  .label[href]:hover, .label[href]:focus {
    color: #fff;
    text-decoration: none;
    cursor: pointer; }
  .label:empty {
    display: none; }

.label-default {
  background-color: #999999; }
  .label-default[href]:hover, .label-default[href]:focus {
    background-color: #808080; }

.label-primary {
  background-color: #76bb6a; }
  .label-primary[href]:hover, .label-primary[href]:focus {
    background-color: #59a64c; }

.label-success {
  background-color: #2D8C20; }
  .label-success[href]:hover, .label-success[href]:focus {
    background-color: #216217; }

.label-info {
  background-color: #3A87AD; }
  .label-info[href]:hover, .label-info[href]:focus {
    background-color: #2d6a87; }

.label-warning {
  background-color: #cf832d; }
  .label-warning[href]:hover, .label-warning[href]:focus {
    background-color: #a56724; }

.label-danger {
  background-color: #da4f49; }
  .label-danger[href]:hover, .label-danger[href]:focus {
    background-color: #c72e29; }

.badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: #999999;
  border-radius: 10px; }
  .badge:empty {
    display: none; }

a.badge:hover, a.badge:focus {
  color: #fff;
  text-decoration: none;
  cursor: pointer; }

.btn .badge {
  position: relative;
  top: -1px; }

a.list-group-item.active > .badge, .nav-pills > .active > a > .badge {
  color: #6a92bb;
  background-color: #fff; }

.nav-pills > li > a > .badge {
  margin-left: 3px; }

.badge-primary {
  background-color: #76bb6a; }

.badge-success {
  background-color: #2D8C20; }

.badge-info {
  background-color: #3A87AD; }

.badge-warning {
  background-color: #cf832d; }

.badge-danger {
  background-color: #da4f49; }

.badge-active {
  background-color: #fff;
  color: #6a92bb; }

.jumbotron {
  padding: 30px;
  margin-bottom: 30px;
  font-size: 21px;
  font-weight: 200;
  line-height: 2.14286;
  color: inherit;
  background-color: #ddd; }
  .jumbotron h1 {
    line-height: 1;
    color: inherit; }
  .jumbotron p {
    line-height: 1.4; }
  .container .jumbotron {
    border-radius: 2px; }
  @media screen and (min-width: 768px) {
    .jumbotron {
      padding-top: 48px;
      padding-bottom: 48px; }
      .container .jumbotron {
        padding-left: 60px;
        padding-right: 60px; }
      .jumbotron h1 {
        font-size: 63px; } }

.thumbnail {
  display: block;
  margin-bottom: 20px; }
  .thumbnail > img {
    display: block;
    max-width: 100%;
    height: auto;
    margin-left: auto;
    margin-right: auto; }
  .thumbnail .caption {
    padding: 9px;
    color: #21242b; }

a.thumbnail:hover, a.thumbnail:focus, a.thumbnail.active {
  border-color: #6a92bb; }

.alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 2px; }
  .alert h4 {
    margin-top: 0;
    color: inherit; }
  .alert .alert-link {
    font-weight: bold; }
  .alert > p, .alert > ul {
    margin-bottom: 0; }
  .alert > p + p {
    margin-top: 5px; }

.alert-dismissable {
  padding-right: 35px; }
  .alert-dismissable .close {
    position: relative;
    top: -2px;
    right: -21px;
    color: inherit; }

.alert-success {
  background-color: #dff0d8;
  border-color: #d7e9c6;
  color: #2D8C20; }
  .alert-success hr {
    border-top-color: #cae2b3; }
  .alert-success .alert-link {
    color: #216217; }

.alert-info {
  background-color: #d9edf7;
  border-color: #bce9f1;
  color: #6a92bb; }
  .alert-info hr {
    border-top-color: #a6e2ec; }
  .alert-info .alert-link {
    color: #4c79a6; }

.alert-warning {
  background-color: #fcf8e3;
  border-color: #faeacc;
  color: #c09853; }
  .alert-warning hr {
    border-top-color: #f7e0b5; }
  .alert-warning .alert-link {
    color: #a47c3c; }

.alert-todo {
  background-color: #f8f6f7;
  border-color: #ec4530;
  color: #333333; }
  .alert-todo hr {
    border-top-color: #ea2e19; }
  .alert-todo .alert-link {
    color: #1a1a1a; }

.alert-danger, .alert-error {
  background-color: #f2dede;
  border-color: #ebccd1;
  color: #b94a48; }
  .alert-danger hr, .alert-error hr {
    border-top-color: #e4b9c0; }
  .alert-danger .alert-link, .alert-error .alert-link {
    color: #953b39; }

.alert-inverse {
  background-color: #21242b;
  border-color: #848484;
  color: #e5e5e5; }
  .alert-inverse hr {
    border-top-color: #777777; }
  .alert-inverse .alert-link {
    color: #cccccc; }

#docstrap-nav {
  height: 0;
  width: 100%;
  position: fixed;
  z-index: 1020; }
  #docstrap-nav .page-message {
    position: relative;
    top: 65px;
    width: 700px;
    box-shadow: 0px 2px 3px lightgrey; }

.page-message {
  margin: 0 auto;
  max-width: 80%; }

@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 40px 0; }

  to {
    background-position: 0 0; } }

@-moz-keyframes progress-bar-stripes {
  from {
    background-position: 40px 0; }

  to {
    background-position: 0 0; } }

@-o-keyframes progress-bar-stripes {
  from {
    background-position: 0 0; }

  to {
    background-position: 40px 0; } }

@keyframes progress-bar-stripes {
  from {
    background-position: 40px 0; }

  to {
    background-position: 0 0; } }

.progress {
  overflow: hidden;
  height: 20px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border-radius: 2px;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1); }

.progress-bar {
  float: left;
  width: 0%;
  height: 100%;
  font-size: 12px;
  line-height: 20px;
  color: #fff;
  text-align: center;
  background-color: #76bb6a;
  -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  -webkit-transition: width 0.6s ease;
  transition: width 0.6s ease; }

.progress-striped .progress-bar {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear, 45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent;
  background-size: 40px 40px; }

.progress.active .progress-bar {
  -webkit-animation: progress-bar-stripes 2s linear infinite;
  animation: progress-bar-stripes 2s linear infinite; }

.progress-bar-success {
  background-color: #2D8C20; }
  .progress-striped .progress-bar-success {
    background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear, 45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent; }

.progress-bar-info {
  background-color: #3A87AD; }
  .progress-striped .progress-bar-info {
    background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear, 45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent; }

.progress-bar-warning {
  background-color: #cf832d; }
  .progress-striped .progress-bar-warning {
    background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear, 45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent; }

.progress-bar-danger {
  background-color: #da4f49; }
  .progress-striped .progress-bar-danger {
    background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear, 45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent; }

.media, .media-body {
  overflow: hidden;
  zoom: 1; }

.media, .media .media {
  margin-top: 15px; }

.media:first-child {
  margin-top: 0; }

.media-object {
  display: block; }

.media-heading {
  margin: 0 0 5px; }

.media > .pull-left {
  margin-right: 10px; }
.media > .pull-right {
  margin-left: 10px; }

.media-list {
  padding-left: 0;
  list-style: none; }

.list-group {
  margin-bottom: 20px;
  padding-left: 0; }

.list-group-item {
  position: relative;
  display: block;
  padding: 10px 15px;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid #ddd; }
  .list-group-item:first-child {
    border-top-right-radius: 2px;
    border-top-left-radius: 2px; }
  .list-group-item:last-child {
    margin-bottom: 0;
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 2px; }
  .list-group-item > .badge {
    float: right; }
  .list-group-item > .badge + .badge {
    margin-right: 5px; }

a.list-group-item {
  color: #555; }
  a.list-group-item .list-group-item-heading {
    color: #333; }
  a.list-group-item:hover, a.list-group-item:focus {
    text-decoration: none;
    background-color: #f5f5f5; }
  a.list-group-item.active, a.list-group-item.active:hover, a.list-group-item.active:focus {
    z-index: 2;
    color: #fff;
    background-color: #76bb6a;
    border-color: #76bb6a; }
    a.list-group-item.active .list-group-item-heading, a.list-group-item.active:hover .list-group-item-heading, a.list-group-item.active:focus .list-group-item-heading {
      color: inherit; }
    a.list-group-item.active .list-group-item-text, a.list-group-item.active:hover .list-group-item-text, a.list-group-item.active:focus .list-group-item-text {
      color: #f7fbf6; }

.list-group-item-heading {
  margin-top: 0;
  margin-bottom: 5px; }

.list-group-item-text {
  margin-bottom: 0;
  line-height: 1.3; }

.panel {
  margin-bottom: 20px;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 2px;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05); }

.panel-body {
  padding: 15px; }
  .panel-body:before, .panel-body:after {
    content: " ";
    /* 1 */
    display: table;
    /* 2 */ }
  .panel-body:after {
    clear: both; }

.panel > .list-group {
  margin-bottom: 0; }
  .panel > .list-group .list-group-item {
    border-width: 1px 0; }
    .panel > .list-group .list-group-item:first-child {
      border-top-right-radius: 0;
      border-top-left-radius: 0; }
    .panel > .list-group .list-group-item:last-child {
      border-bottom: 0; }

.panel-heading + .list-group .list-group-item:first-child {
  border-top-width: 0; }

.panel > .table, .panel > .table-responsive {
  margin-bottom: 0; }
.panel > .panel-body + .table, .panel > .panel-body + .table-responsive {
  border-top: 1px solid #ddd; }
.panel > .table-bordered, .panel > .table-responsive > .table-bordered {
  border: 0; }
  .panel > .table-bordered > thead > tr > th:first-child, .panel > .table-bordered > thead > tr > td:first-child, .panel > .table-bordered > tbody > tr > th:first-child, .panel > .table-bordered > tbody > tr > td:first-child, .panel > .table-bordered > tfoot > tr > th:first-child, .panel > .table-bordered > tfoot > tr > td:first-child, .panel > .table-responsive > .table-bordered > thead > tr > th:first-child, .panel > .table-responsive > .table-bordered > thead > tr > td:first-child, .panel > .table-responsive > .table-bordered > tbody > tr > th:first-child, .panel > .table-responsive > .table-bordered > tbody > tr > td:first-child, .panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child, .panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child {
    border-left: 0; }
  .panel > .table-bordered > thead > tr > th:last-child, .panel > .table-bordered > thead > tr > td:last-child, .panel > .table-bordered > tbody > tr > th:last-child, .panel > .table-bordered > tbody > tr > td:last-child, .panel > .table-bordered > tfoot > tr > th:last-child, .panel > .table-bordered > tfoot > tr > td:last-child, .panel > .table-responsive > .table-bordered > thead > tr > th:last-child, .panel > .table-responsive > .table-bordered > thead > tr > td:last-child, .panel > .table-responsive > .table-bordered > tbody > tr > th:last-child, .panel > .table-responsive > .table-bordered > tbody > tr > td:last-child, .panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child, .panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child {
    border-right: 0; }
  .panel > .table-bordered > thead > tr:last-child > th, .panel > .table-bordered > thead > tr:last-child > td, .panel > .table-bordered > tbody > tr:last-child > th, .panel > .table-bordered > tbody > tr:last-child > td, .panel > .table-bordered > tfoot > tr:last-child > th, .panel > .table-bordered > tfoot > tr:last-child > td, .panel > .table-responsive > .table-bordered > thead > tr:last-child > th, .panel > .table-responsive > .table-bordered > thead > tr:last-child > td, .panel > .table-responsive > .table-bordered > tbody > tr:last-child > th, .panel > .table-responsive > .table-bordered > tbody > tr:last-child > td, .panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th, .panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td {
    border-bottom: 0; }

.panel-heading {
  padding: 10px 15px;
  border-bottom: 1px solid transparent;
  border-top-right-radius: 1px;
  border-top-left-radius: 1px; }
  .panel-heading > .dropdown .dropdown-toggle {
    color: inherit; }

.panel-heading-sm {
  font-size: 12px; }

.panel-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 16px; }
  .panel-title > a {
    color: inherit; }

.panel-footer {
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  border-bottom-right-radius: 1px;
  border-bottom-left-radius: 1px; }

.panel-group .panel {
  margin-bottom: 0;
  border-radius: 2px;
  overflow: hidden; }
  .panel-group .panel + .panel {
    margin-top: 5px; }
.panel-group .panel-heading {
  border-bottom: 0; }
  .panel-group .panel-heading + .panel-collapse .panel-body {
    border-top: 1px solid #ddd; }
.panel-group .panel-footer {
  border-top: 0; }
  .panel-group .panel-footer + .panel-collapse .panel-body {
    border-bottom: 1px solid #ddd; }

.panel-default {
  border-color: #ddd; }
  .panel-default > .panel-heading {
    color: #333333;
    background-color: #FBFBFB;
    border-color: #ddd; }
    .panel-default > .panel-heading + .panel-collapse .panel-body {
      border-top-color: #ddd; }
    .panel-default > .panel-heading > .dropdown .caret {
      border-color: #333333 transparent; }
  .panel-default > .panel-footer + .panel-collapse .panel-body {
    border-bottom-color: #ddd; }

.panel-primary {
  border-color: #76bb6a; }
  .panel-primary > .panel-heading {
    color: #fff;
    background-color: #76bb6a;
    border-color: #76bb6a; }
    .panel-primary > .panel-heading + .panel-collapse .panel-body {
      border-top-color: #76bb6a; }
    .panel-primary > .panel-heading > .dropdown .caret {
      border-color: #fff transparent; }
  .panel-primary > .panel-footer + .panel-collapse .panel-body {
    border-bottom-color: #76bb6a; }

.panel-success {
  border-color: #d7e9c6; }
  .panel-success > .panel-heading {
    color: #2D8C20;
    background-color: #dff0d8;
    border-color: #d7e9c6; }
    .panel-success > .panel-heading + .panel-collapse .panel-body {
      border-top-color: #d7e9c6; }
    .panel-success > .panel-heading > .dropdown .caret {
      border-color: #2D8C20 transparent; }
  .panel-success > .panel-footer + .panel-collapse .panel-body {
    border-bottom-color: #d7e9c6; }

.panel-warning {
  border-color: #faeacc; }
  .panel-warning > .panel-heading {
    color: #c09853;
    background-color: #fcf8e3;
    border-color: #faeacc; }
    .panel-warning > .panel-heading + .panel-collapse .panel-body {
      border-top-color: #faeacc; }
    .panel-warning > .panel-heading > .dropdown .caret {
      border-color: #c09853 transparent; }
  .panel-warning > .panel-footer + .panel-collapse .panel-body {
    border-bottom-color: #faeacc; }

.panel-danger {
  border-color: #ebccd1; }
  .panel-danger > .panel-heading {
    color: #b94a48;
    background-color: #f2dede;
    border-color: #ebccd1; }
    .panel-danger > .panel-heading + .panel-collapse .panel-body {
      border-top-color: #ebccd1; }
    .panel-danger > .panel-heading > .dropdown .caret {
      border-color: #b94a48 transparent; }
  .panel-danger > .panel-footer + .panel-collapse .panel-body {
    border-bottom-color: #ebccd1; }

.panel-info {
  border-color: #bce9f1; }
  .panel-info > .panel-heading {
    color: #6a92bb;
    background-color: #d9edf7;
    border-color: #bce9f1; }
    .panel-info > .panel-heading + .panel-collapse .panel-body {
      border-top-color: #bce9f1; }
    .panel-info > .panel-heading > .dropdown .caret {
      border-color: #6a92bb transparent; }
  .panel-info > .panel-footer + .panel-collapse .panel-body {
    border-bottom-color: #bce9f1; }

.well {
  min-height: 20px;
  padding: 19px;
  margin-bottom: 20px;
  background-color: #FBFBFB;
  border: 1px solid #e9e9e9;
  border-radius: 2px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05); }
  .well blockquote {
    border-color: #ddd;
    border-color: rgba(0, 0, 0, 0.15); }
  .well .well-heading {
    margin: -19px -19px 20px -19px;
    border-radius: 2px 2px 0 0;
    padding: 19px; }

.well.well-white {
  background-color: white; }

.well-heading {
  border-bottom: 1px solid #e9e9e9;
  background-color: #FBFBFB; }

.well-lg {
  padding: 29px;
  border-radius: 2px; }
  .well-lg .well-heading {
    margin: -29px -29px 30px -29px;
    border-radius: 2px 2px 0 0;
    padding: 29px; }

.well-sm {
  padding: 14px;
  border-radius: 2px; }
  .well-sm .well-heading {
    margin: -14px -14px 15px -14px;
    border-radius: 2px 2px 0 0;
    padding: 14px; }

.close {
  float: right;
  font-size: 21px;
  font-weight: bold;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.2;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=20); }
  .close:hover, .close:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
    opacity: 0.5;
    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=50); }

button.close {
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none; }

.flexbox-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-direction: normal;
  -webkit-box-orient: horizontal;
  -webkit-flex-direction: row;
  -moz-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row; }

.flexbox-row-reverse {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-direction: reverse;
  -webkit-box-orient: horizontal;
  -webkit-flex-direction: row-reverse;
  -moz-flex-direction: row-reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse; }

.flexbox-column {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-direction: normal;
  -webkit-box-orient: vertical;
  -webkit-flex-direction: column;
  -moz-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column; }

.flexbox-column-reverse {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-direction: reverse;
  -webkit-box-orient: vertical;
  -webkit-flex-direction: column-reverse;
  -moz-flex-direction: column-reverse;
  -ms-flex-direction: column-reverse;
  flex-direction: column-reverse; }

.flex-1 {
  -webkit-box-flex: 1;
  -webkit-flex: 1 1 0;
  -moz-box-flex: 1;
  -moz-flex: 1 1 0;
  -ms-flex: 1 1 0;
  flex: 1 1 0; }

.flex-2 {
  -webkit-box-flex: 2;
  -webkit-flex: 2 1 0;
  -moz-box-flex: 2;
  -moz-flex: 2 1 0;
  -ms-flex: 2 1 0;
  flex: 2 1 0; }

.flex-3 {
  -webkit-box-flex: 3;
  -webkit-flex: 3 1 0;
  -moz-box-flex: 3;
  -moz-flex: 3 1 0;
  -ms-flex: 3 1 0;
  flex: 3 1 0; }

.flex-4 {
  -webkit-box-flex: 4;
  -webkit-flex: 4 1 0;
  -moz-box-flex: 4;
  -moz-flex: 4 1 0;
  -ms-flex: 4 1 0;
  flex: 4 1 0; }

.overflow-y-scroll {
  overflow-y: scroll; }

.modal-open {
  overflow: hidden; }

.modal {
  display: none;
  overflow: auto;
  overflow-y: scroll;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040; }
  .modal.fade .modal-dialog {
    -webkit-transform: translate(0, -25%);
    -ms-transform: translate(0, -25%);
    transform: translate(0, -25%);
    -webkit-transition: -webkit-transform 0.3s ease-out;
    -moz-transition: -moz-transform 0.3s ease-out;
    -o-transition: -o-transform 0.3s ease-out;
    transition: transform 0.3s ease-out; }
  .modal.in .modal-dialog {
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0); }

.modal-dialog {
  position: relative;
  margin-left: auto;
  margin-right: auto;
  width: auto;
  padding: 10px;
  z-index: 1050; }

.modal-content {
  position: relative;
  background-color: #fff;
  border: 1px solid #999;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  background-clip: padding-box;
  outline: none; }

.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
  background-color: black; }
  .modal-backdrop.fade {
    opacity: 0;
    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0); }
  .modal-backdrop.in {
    opacity: 0.5;
    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=50); }

.modal-header {
  padding: 9px;
  border-bottom: 1px solid #e5e5e5;
  min-height: 10.42857px; }

.modal-header .close {
  margin-top: 3px;
  font-size: 24.5px; }

.modal-title {
  margin: 0;
  line-height: 1.42857; }

.modal-body {
  position: relative;
  padding: 20px; }

.modal-footer {
  margin-top: 15px;
  padding: 19px 20px 20px;
  text-align: right;
  border-top: 1px solid #e5e5e5; }
  .modal-footer:before, .modal-footer:after {
    content: " ";
    /* 1 */
    display: table;
    /* 2 */ }
  .modal-footer:after {
    clear: both; }
  .modal-footer .btn + .btn {
    margin-left: 5px;
    margin-bottom: 0; }
  .modal-footer .btn-group .btn + .btn {
    margin-left: -1px; }
  .modal-footer .btn-block + .btn-block {
    margin-left: 0; }

.modal-rail {
  background-color: #FBFBFB;
  margin: -20px -20px 20px -20px;
  padding: 10px 20px;
  font-size: 12px;
  border-bottom: 1px solid #e5e5e5; }

@media screen and (min-width: 768px) {
  .modal-dialog {
    width: 600px;
    padding-top: 30px;
    padding-bottom: 30px; }
  .modal-content {
    -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5); } }

.tooltip {
  position: absolute;
  z-index: 1100;
  display: block;
  visibility: visible;
  font-size: 12px;
  line-height: 1.4;
  opacity: 0;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=0); }
  .tooltip.in {
    opacity: 0.9;
    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=90); }
  .tooltip.top {
    margin-top: -3px;
    padding: 5px 0; }
  .tooltip.right {
    margin-left: 3px;
    padding: 0 5px; }
  .tooltip.bottom {
    margin-top: 3px;
    padding: 5px 0; }
  .tooltip.left {
    margin-left: -3px;
    padding: 0 5px; }

.tooltip-inner {
  max-width: 200px;
  padding: 3px 8px;
  color: #fff;
  text-align: center;
  text-decoration: none;
  background-color: #000;
  border-radius: 2px; }

.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid; }

.tooltip.top .tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000; }
.tooltip.top-left .tooltip-arrow {
  bottom: 0;
  left: 5px;
  border-width: 5px 5px 0;
  border-top-color: #000; }
.tooltip.top-right .tooltip-arrow {
  bottom: 0;
  right: 5px;
  border-width: 5px 5px 0;
  border-top-color: #000; }
.tooltip.right .tooltip-arrow {
  top: 50%;
  left: 0;
  margin-top: -5px;
  border-width: 5px 5px 5px 0;
  border-right-color: #000; }
.tooltip.left .tooltip-arrow {
  top: 50%;
  right: 0;
  margin-top: -5px;
  border-width: 5px 0 5px 5px;
  border-left-color: #000; }
.tooltip.bottom .tooltip-arrow {
  top: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000; }
.tooltip.bottom-left .tooltip-arrow {
  top: 0;
  left: 5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000; }
.tooltip.bottom-right .tooltip-arrow {
  top: 0;
  right: 5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000; }

.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1100;
  display: none;
  max-width: 500px;
  padding: 1px;
  text-align: left;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  white-space: normal; }
  .popover.top {
    margin-top: -10px; }
  .popover.right {
    margin-left: 10px; }
  .popover.bottom {
    margin-top: 10px; }
  .popover.left {
    margin-left: -10px; }

.popover-title {
  margin: 0;
  padding: 8px 14px;
  font-size: 14px;
  font-weight: normal;
  line-height: 18px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-radius: 5px 5px 0 0; }

.popover-content {
  padding: 9px 14px; }

.popover .arrow, .popover .arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid; }

.popover .arrow {
  border-width: 11px; }

.popover .arrow:after {
  border-width: 10px;
  content: ""; }

.popover.top .arrow {
  left: 50%;
  margin-left: -11px;
  border-bottom-width: 0;
  border-top-color: #999;
  border-top-color: rgba(0, 0, 0, 0.25);
  bottom: -11px; }
  .popover.top .arrow:after {
    content: " ";
    bottom: 1px;
    margin-left: -10px;
    border-bottom-width: 0;
    border-top-color: #fff; }
.popover.right .arrow {
  top: 50%;
  left: -11px;
  margin-top: -11px;
  border-left-width: 0;
  border-right-color: #999;
  border-right-color: rgba(0, 0, 0, 0.25); }
  .popover.right .arrow:after {
    content: " ";
    left: 1px;
    bottom: -10px;
    border-left-width: 0;
    border-right-color: #fff; }
.popover.bottom .arrow {
  left: 50%;
  margin-left: -11px;
  border-top-width: 0;
  border-bottom-color: #999;
  border-bottom-color: rgba(0, 0, 0, 0.25);
  top: -11px; }
  .popover.bottom .arrow:after {
    content: " ";
    top: 1px;
    margin-left: -10px;
    border-top-width: 0;
    border-bottom-color: #fff; }
.popover.left .arrow {
  top: 50%;
  right: -11px;
  margin-top: -11px;
  border-right-width: 0;
  border-left-color: #999;
  border-left-color: rgba(0, 0, 0, 0.25); }
  .popover.left .arrow:after {
    content: " ";
    right: 1px;
    border-right-width: 0;
    border-left-color: #fff;
    bottom: -10px; }

.carousel {
  position: relative; }

.carousel-inner {
  position: relative;
  overflow: hidden;
  width: 100%; }
  .carousel-inner > .item {
    display: none;
    position: relative;
    -webkit-transition: 0.6s ease-in-out left;
    transition: 0.6s ease-in-out left; }
    .carousel-inner > .item > img, .carousel-inner > .item > a > img {
      display: block;
      max-width: 100%;
      height: auto;
      line-height: 1; }
  .carousel-inner > .active, .carousel-inner > .next, .carousel-inner > .prev {
    display: block; }
  .carousel-inner > .active {
    left: 0; }
  .carousel-inner > .next, .carousel-inner > .prev {
    position: absolute;
    top: 0;
    width: 100%; }
  .carousel-inner > .next {
    left: 100%; }
  .carousel-inner > .prev {
    left: -100%; }
  .carousel-inner > .next.left, .carousel-inner > .prev.right {
    left: 0; }
  .carousel-inner > .active.left {
    left: -100%; }
  .carousel-inner > .active.right {
    left: 100%; }

.carousel-control {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 15%;
  opacity: 0.5;
  filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=50);
  font-size: 20px;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6); }
  .carousel-control.left {
    background-image: -webkit-gradient(linear, 0% top, 100% top, from(rgba(0, 0, 0, 0.5)), to(rgba(0, 0, 0, 0.0001)));
    background-image: -webkit-linear-gradient(left, color-stop(rgba(0, 0, 0, 0.5) 0%), color-stop(rgba(0, 0, 0, 0.0001) 100%));
    background-image: -moz-linear-gradient(left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
    background-image: linear, to right, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%;
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#80000000', endColorstr='#00000000', GradientType=1); }
  .carousel-control.right {
    left: auto;
    right: 0;
    background-image: -webkit-gradient(linear, 0% top, 100% top, from(rgba(0, 0, 0, 0.0001)), to(rgba(0, 0, 0, 0.5)));
    background-image: -webkit-linear-gradient(left, color-stop(rgba(0, 0, 0, 0.0001) 0%), color-stop(rgba(0, 0, 0, 0.5) 100%));
    background-image: -moz-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
    background-image: linear, to right, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%;
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#80000000', GradientType=1); }
  .carousel-control:hover, .carousel-control:focus {
    color: #fff;
    text-decoration: none;
    opacity: 0.9;
    filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=90); }
  .carousel-control .icon-prev, .carousel-control .icon-next, .carousel-control .glyphicon-chevron-left, .carousel-control .glyphicon-chevron-right {
    position: absolute;
    top: 50%;
    z-index: 5;
    display: inline-block; }
  .carousel-control .icon-prev, .carousel-control .glyphicon-chevron-left {
    left: 50%; }
  .carousel-control .icon-next, .carousel-control .glyphicon-chevron-right {
    right: 50%; }
  .carousel-control .icon-prev, .carousel-control .icon-next {
    width: 20px;
    height: 20px;
    margin-top: -10px;
    margin-left: -10px;
    font-family: serif; }
  .carousel-control .icon-prev:before {
    content: '\2039'; }
  .carousel-control .icon-next:before {
    content: '\203a'; }

.carousel-indicators {
  position: absolute;
  bottom: 10px;
  left: 50%;
  z-index: 15;
  width: 60%;
  margin-left: -30%;
  padding-left: 0;
  list-style: none;
  text-align: center; }
  .carousel-indicators li {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin: 1px;
    text-indent: -999px;
    border: 1px solid #fff;
    border-radius: 10px;
    cursor: pointer;
    background-color: #000 \9;
    background-color: rgba(0, 0, 0, 0); }
  .carousel-indicators .active {
    margin: 0;
    width: 12px;
    height: 12px;
    background-color: #fff; }

.carousel-caption {
  position: absolute;
  left: 15%;
  right: 15%;
  bottom: 20px;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6); }
  .carousel-caption .btn {
    text-shadow: none; }

@media screen and (min-width: 768px) {
  .carousel-control .glyphicons-chevron-left, .carousel-control .glyphicons-chevron-right, .carousel-control .icon-prev, .carousel-control .icon-next {
    width: 30px;
    height: 30px;
    margin-top: -15px;
    margin-left: -15px;
    font-size: 30px; }
  .carousel-caption {
    left: 20%;
    right: 20%;
    padding-bottom: 30px; }
  .carousel-indicators {
    bottom: 20px; } }

.clearfix:before, .clearfix:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */ }
.clearfix:after {
  clear: both; }

.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto; }

.pull-right, .right, .fr {
  float: right !important; }

.pull-left, .left, .fl {
  float: left !important; }

.hide {
  display: none !important; }

.show {
  display: block !important; }

.invisible {
  visibility: hidden; }

.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0; }

.hidden {
  display: none !important;
  visibility: hidden !important; }

.affix {
  position: fixed; }

.no-spacing {
  margin: 0 !important;
  padding: 0 !important; }

.no-margin {
  margin: 0 !important; }

.no-side-margin {
  margin-left: 0 !important;
  margin-right: 0 !important; }

.no-side-margin {
  margin-left: 0 !important;
  margin-right: 0 !important; }

.no-padd {
  padding: 0 !important; }

.no-side-padd, .btn-link-no-spacing {
  padding-left: 0 !important;
  padding-right: 0 !important; }

.no-min-width, .btn-link-no-spacing {
  min-width: 0 !important; }

.force-inline {
  display: inline-block !important; }

@-ms-viewport {
  width: device-width; }

@media (max-width: 767px) {
  .fullw-below-sm {
    width: 100% !important;
    max-width: 100% !important; }
  .clear-below-sm {
    clear: both; } }

@media (max-width: 991px) {
  .fullw-below-md {
    width: 100% !important;
    max-width: 100% !important; }
  .clear-below-md {
    clear: both; } }

@media (max-width: 479px) {
  .hide-below-xs {
    display: none !important; }
  .row-below-xs {
    margin-left: -15px;
    margin-right: -15px; }
    .row-below-xs:before, .row-below-xs:after {
      content: " ";
      /* 1 */
      display: table;
      /* 2 */ }
    .row-below-xs:after {
      clear: both; } }

@media (max-width: 767px) {
  .hide-below-sm {
    display: none !important; }
  .row-below-sm {
    margin-left: -15px;
    margin-right: -15px; }
    .row-below-sm:before, .row-below-sm:after {
      content: " ";
      /* 1 */
      display: table;
      /* 2 */ }
    .row-below-sm:after {
      clear: both; } }

@media (max-width: 991px) {
  .hide-below-md {
    display: none !important; }
  .row-below-md {
    margin-left: -15px;
    margin-right: -15px; }
    .row-below-md:before, .row-below-md:after {
      content: " ";
      /* 1 */
      display: table;
      /* 2 */ }
    .row-below-md:after {
      clear: both; } }

@media (max-width: 1199px) {
  .hide-below-lg {
    display: none !important; }
  .row-below-lg {
    margin-left: -15px;
    margin-right: -15px; }
    .row-below-lg:before, .row-below-lg:after {
      content: " ";
      /* 1 */
      display: table;
      /* 2 */ }
    .row-below-lg:after {
      clear: both; } }

@media (min-width: 1200px) {
  .hide-above-md {
    display: none !important; }
  .row-above-md {
    margin-left: -15px;
    margin-right: -15px; }
    .row-above-md:before, .row-above-md:after {
      content: " ";
      /* 1 */
      display: table;
      /* 2 */ }
    .row-above-md:after {
      clear: both; } }

@media (min-width: 992px) {
  .hide-above-sm {
    display: none !important; }
  .row-above-sm {
    margin-left: -15px;
    margin-right: -15px; }
    .row-above-sm:before, .row-above-sm:after {
      content: " ";
      /* 1 */
      display: table;
      /* 2 */ }
    .row-above-sm:after {
      clear: both; } }

@media (min-width: 768px) {
  .hide-above-xs {
    display: none !important; }
  .row-above-xs {
    margin-left: -15px;
    margin-right: -15px; }
    .row-above-xs:before, .row-above-xs:after {
      content: " ";
      /* 1 */
      display: table;
      /* 2 */ }
    .row-above-xs:after {
      clear: both; } }

.visible-xs, tr.visible-xs, th.visible-xs, td.visible-xs {
  display: none !important; }

@media (max-width: 767px) {
  .visible-xs {
    display: inherit !important; }
  tr.visible-xs {
    display: table-row !important; }
  th.visible-xs, td.visible-xs {
    display: table-cell !important; }
  .no-side-padd-xs {
    padding-left: 0 !important;
    padding-right: 0 !important; }
  .no-side-marg-xs {
    margin-left: 0 !important;
    margin-right: 0 !important; } }

@media (min-width: 768px) and (max-width: 991px) {
  .visible-xs.visible-sm {
    display: inherit !important; }
  tr.visible-xs.visible-sm {
    display: table-row !important; }
  th.visible-xs.visible-sm, td.visible-xs.visible-sm {
    display: table-cell !important; }
  .no-side-padd-sm {
    padding-left: 0 !important;
    padding-right: 0 !important; }
  .no-side-marg-sm {
    margin-left: 0 !important;
    margin-right: 0 !important; } }

@media (min-width: 992px) and (max-width: 1199px) {
  .visible-xs.visible-md {
    display: inherit !important; }
  tr.visible-xs.visible-md {
    display: table-row !important; }
  th.visible-xs.visible-md, td.visible-xs.visible-md {
    display: table-cell !important; }
  .no-side-padd-md {
    padding-left: 0 !important;
    padding-right: 0 !important; }
  .no-side-marg-md {
    margin-left: 0 !important;
    margin-right: 0 !important; } }

@media (min-width: 1200px) {
  .visible-xs.visible-lg {
    display: inherit !important; }
  tr.visible-xs.visible-lg {
    display: table-row !important; }
  th.visible-xs.visible-lg, td.visible-xs.visible-lg {
    display: table-cell !important; }
  .no-side-padd-lg {
    padding-left: 0 !important;
    padding-right: 0 !important; }
  .no-side-marg-lg {
    margin-left: 0 !important;
    margin-right: 0 !important; } }

.visible-sm, tr.visible-sm, th.visible-sm, td.visible-sm {
  display: none !important; }

@media (max-width: 767px) {
  .visible-sm.visible-xs {
    display: inherit !important; }
  tr.visible-sm.visible-xs {
    display: table-row !important; }
  th.visible-sm.visible-xs, td.visible-sm.visible-xs {
    display: table-cell !important; } }

@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm {
    display: inherit !important; }
  tr.visible-sm {
    display: table-row !important; }
  th.visible-sm, td.visible-sm {
    display: table-cell !important; } }

@media (min-width: 992px) and (max-width: 1199px) {
  .visible-sm.visible-md {
    display: inherit !important; }
  tr.visible-sm.visible-md {
    display: table-row !important; }
  th.visible-sm.visible-md, td.visible-sm.visible-md {
    display: table-cell !important; } }

@media (min-width: 1200px) {
  .visible-sm.visible-lg {
    display: inherit !important; }
  tr.visible-sm.visible-lg {
    display: table-row !important; }
  th.visible-sm.visible-lg, td.visible-sm.visible-lg {
    display: table-cell !important; } }

.visible-md, tr.visible-md, th.visible-md, td.visible-md {
  display: none !important; }

@media (max-width: 767px) {
  .visible-md.visible-xs {
    display: inherit !important; }
  tr.visible-md.visible-xs {
    display: table-row !important; }
  th.visible-md.visible-xs, td.visible-md.visible-xs {
    display: table-cell !important; } }

@media (min-width: 768px) and (max-width: 991px) {
  .visible-md.visible-sm {
    display: inherit !important; }
  tr.visible-md.visible-sm {
    display: table-row !important; }
  th.visible-md.visible-sm, td.visible-md.visible-sm {
    display: table-cell !important; } }

@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md {
    display: inherit !important; }
  tr.visible-md {
    display: table-row !important; }
  th.visible-md, td.visible-md {
    display: table-cell !important; } }

@media (min-width: 1200px) {
  .visible-md.visible-lg {
    display: inherit !important; }
  tr.visible-md.visible-lg {
    display: table-row !important; }
  th.visible-md.visible-lg, td.visible-md.visible-lg {
    display: table-cell !important; } }

.visible-lg, tr.visible-lg, th.visible-lg, td.visible-lg {
  display: none !important; }

@media (max-width: 767px) {
  .visible-lg.visible-xs {
    display: inherit !important; }
  tr.visible-lg.visible-xs {
    display: table-row !important; }
  th.visible-lg.visible-xs, td.visible-lg.visible-xs {
    display: table-cell !important; } }

@media (min-width: 768px) and (max-width: 991px) {
  .visible-lg.visible-sm {
    display: inherit !important; }
  tr.visible-lg.visible-sm {
    display: table-row !important; }
  th.visible-lg.visible-sm, td.visible-lg.visible-sm {
    display: table-cell !important; } }

@media (min-width: 992px) and (max-width: 1199px) {
  .visible-lg.visible-md {
    display: inherit !important; }
  tr.visible-lg.visible-md {
    display: table-row !important; }
  th.visible-lg.visible-md, td.visible-lg.visible-md {
    display: table-cell !important; } }

@media (min-width: 1200px) {
  .visible-lg {
    display: inherit !important; }
  tr.visible-lg {
    display: table-row !important; }
  th.visible-lg, td.visible-lg {
    display: table-cell !important; } }

.hidden-xs {
  display: inherit !important; }

tr.hidden-xs {
  display: table-row !important; }

th.hidden-xs, td.hidden-xs {
  display: table-cell !important; }

@media (max-width: 767px) {
  .hidden-xs, tr.hidden-xs, th.hidden-xs, td.hidden-xs {
    display: none !important; } }

@media (min-width: 768px) and (max-width: 991px) {
  .hidden-xs.hidden-sm, tr.hidden-xs.hidden-sm, th.hidden-xs.hidden-sm, td.hidden-xs.hidden-sm {
    display: none !important; } }

@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-xs.hidden-md, tr.hidden-xs.hidden-md, th.hidden-xs.hidden-md, td.hidden-xs.hidden-md {
    display: none !important; } }

@media (min-width: 1200px) {
  .hidden-xs.hidden-lg, tr.hidden-xs.hidden-lg, th.hidden-xs.hidden-lg, td.hidden-xs.hidden-lg {
    display: none !important; } }

.hidden-sm {
  display: inherit !important; }

tr.hidden-sm {
  display: table-row !important; }

th.hidden-sm, td.hidden-sm {
  display: table-cell !important; }

@media (max-width: 767px) {
  .hidden-sm.hidden-xs, tr.hidden-sm.hidden-xs, th.hidden-sm.hidden-xs, td.hidden-sm.hidden-xs {
    display: none !important; } }

@media (min-width: 768px) and (max-width: 991px) {
  .hidden-sm, tr.hidden-sm, th.hidden-sm, td.hidden-sm {
    display: none !important; } }

@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-sm.hidden-md, tr.hidden-sm.hidden-md, th.hidden-sm.hidden-md, td.hidden-sm.hidden-md {
    display: none !important; } }

@media (min-width: 1200px) {
  .hidden-sm.hidden-lg, tr.hidden-sm.hidden-lg, th.hidden-sm.hidden-lg, td.hidden-sm.hidden-lg {
    display: none !important; } }

.hidden-md {
  display: inherit !important; }

tr.hidden-md {
  display: table-row !important; }

th.hidden-md, td.hidden-md {
  display: table-cell !important; }

@media (max-width: 767px) {
  .hidden-md.hidden-xs, tr.hidden-md.hidden-xs, th.hidden-md.hidden-xs, td.hidden-md.hidden-xs {
    display: none !important; } }

@media (min-width: 768px) and (max-width: 991px) {
  .hidden-md.hidden-sm, tr.hidden-md.hidden-sm, th.hidden-md.hidden-sm, td.hidden-md.hidden-sm {
    display: none !important; } }

@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-md, tr.hidden-md, th.hidden-md, td.hidden-md {
    display: none !important; } }

@media (min-width: 1200px) {
  .hidden-md.hidden-lg, tr.hidden-md.hidden-lg, th.hidden-md.hidden-lg, td.hidden-md.hidden-lg {
    display: none !important; } }

.hidden-lg {
  display: inherit !important; }

tr.hidden-lg {
  display: table-row !important; }

th.hidden-lg, td.hidden-lg {
  display: table-cell !important; }

@media (max-width: 767px) {
  .hidden-lg.hidden-xs, tr.hidden-lg.hidden-xs, th.hidden-lg.hidden-xs, td.hidden-lg.hidden-xs {
    display: none !important; } }

@media (min-width: 768px) and (max-width: 991px) {
  .hidden-lg.hidden-sm, tr.hidden-lg.hidden-sm, th.hidden-lg.hidden-sm, td.hidden-lg.hidden-sm {
    display: none !important; } }

@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-lg.hidden-md, tr.hidden-lg.hidden-md, th.hidden-lg.hidden-md, td.hidden-lg.hidden-md {
    display: none !important; } }

@media (min-width: 1200px) {
  .hidden-lg, tr.hidden-lg, th.hidden-lg, td.hidden-lg {
    display: none !important; } }

.visible-print, tr.visible-print, th.visible-print, td.visible-print {
  display: none !important; }

@media print {
  .visible-print {
    display: inherit !important; }
  tr.visible-print {
    display: table-row !important; }
  th.visible-print, td.visible-print {
    display: table-cell !important; }
  .hidden-print, tr.hidden-print, th.hidden-print, td.hidden-print {
    display: none !important; } }

/*!
 * Bootstrap Modal
 *
 * Copyright Jordan Schroter
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Boostrap 3 patch for for bootstrap-modal. Include BEFORE bootstrap-modal.css!
 */
body.modal-open, .modal-open .navbar-fixed-top, .modal-open .navbar-fixed-bottom {
  margin-right: 0; }

.modal {
  left: 50%;
  bottom: auto;
  right: auto;
  padding: 0;
  width: 500px;
  margin-left: -250px;
  background-color: #ffffff;
  border: 1px solid #999999;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  background-clip: padding-box; }

.modal.container {
  max-width: none; }

/*!
 * Bootstrap Modal
 *
 * Copyright Jordan Schroter
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 */
.modal-open {
  overflow: hidden; }

/* add a scroll bar to stop page from jerking around */
.modal-open.page-overflow .page-container, .modal-open.page-overflow .page-container .navbar-fixed-top, .modal-open.page-overflow .page-container .navbar-fixed-bottom, .modal-open.page-overflow .modal-scrollable {
  overflow-y: scroll; }

@media (max-width: 979px) {
  .modal-open.page-overflow .page-container .navbar-fixed-top, .modal-open.page-overflow .page-container .navbar-fixed-bottom {
    overflow-y: visible; } }

.modal-scrollable {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: auto; }

.modal {
  outline: none;
  position: absolute;
  margin-top: 0;
  top: 50%;
  overflow: visible;
  /* allow content to popup out (i.e tooltips) */ }

.modal.fade {
  top: -100%;
  -webkit-transition: opacity 0.3s linear, top 0.3s ease-out, bottom 0.3s ease-out, margin-top 0.3s ease-out;
  -moz-transition: opacity 0.3s linear, top 0.3s ease-out, bottom 0.3s ease-out, margin-top 0.3s ease-out;
  -o-transition: opacity 0.3s linear, top 0.3s ease-out, bottom 0.3s ease-out, margin-top 0.3s ease-out;
  transition: opacity 0.3s linear, top 0.3s ease-out, bottom 0.3s ease-out, margin-top 0.3s ease-out; }

.modal.fade.in {
  top: 50%; }

.modal-body {
  max-height: none;
  overflow: visible; }

.modal.modal-absolute {
  position: absolute;
  z-index: 950; }

.modal .loading-mask {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 6px; }

.modal-backdrop.modal-absolute {
  position: absolute;
  z-index: 940; }

.modal-backdrop, .modal-backdrop.fade.in {
  opacity: 0.7;
  filter: alpha(opacity=70);
  background: black; }

.modal.container {
  width: 940px;
  margin-left: -470px; }

/* Modal Overflow */
.modal-overflow.modal {
  top: 1%; }

.modal-overflow.modal.fade {
  top: -100%; }

.modal-overflow.modal.fade.in {
  top: 1%; }

.modal-overflow .modal-body {
  overflow: auto;
  -webkit-overflow-scrolling: touch; }

/* Responsive */
@media (min-width: 1200px) {
  .modal.container {
    width: 1170px;
    margin-left: -585px; } }

@media (max-width: 979px) {
  .modal, .modal.container, .modal.modal-overflow {
    top: 1%;
    right: 1%;
    left: 1%;
    bottom: auto;
    width: auto !important;
    height: auto !important;
    margin: 0 !important;
    padding: 0 !important; }
  .modal.fade.in, .modal.container.fade.in, .modal.modal-overflow.fade.in {
    top: 1%;
    bottom: auto; }
  .modal-body, .modal-overflow .modal-body {
    position: static;
    margin: 0;
    height: auto !important;
    max-height: none !important;
    overflow: visible !important; }
  .modal-footer, .modal-overflow .modal-footer {
    position: static; } }

.loading-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -12px 0 0 -12px; }

/*
Animate.css - http://daneden.me/animate
Licensed under the ☺ license (http://licence.visualidiot.com/)

Copyright (c) 2012 Dan Eden*/
.animated {
  -webkit-animation-duration: 1s;
  -moz-animation-duration: 1s;
  -o-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  -moz-animation-fill-mode: both;
  -o-animation-fill-mode: both;
  animation-fill-mode: both; }

.modal-open {
  margin-right: 15px;
  overflow: hidden; }
  .modal-open .navbar .nav.pull-right {
    margin-right: 15px; }

/* -------------------------------
 Dropdown submenu CSS
 --------------------------------- */

.dropdown-submenu {
    position:relative;
}
.dropdown-submenu>.dropdown-menu {
    top:0;
    left:100%;
    margin-top:-6px;
    margin-left:-1px;
    -webkit-border-radius:0 6px 6px 6px;
    -moz-border-radius:0 6px 6px 6px;
    border-radius:0 6px 6px 6px;
}
.dropdown-submenu:hover>.dropdown-menu {
    display:block;
}
.dropdown-submenu>a:after {
    display:block;
    content:" ";
    float:right;
    width:0;
    height:0;
    border-color:transparent;
    border-style:solid;
    border-width:5px 0 5px 5px;
    border-left-color:#cccccc;
    margin-top:5px;
    margin-right:-10px;
}
.dropdown-submenu:hover>a:after {
    border-left-color:#ffffff;
}
.dropdown-submenu.pull-left {
    float:none;
}
.dropdown-submenu.pull-left>.dropdown-menu {
    left:-100%;
    margin-left:10px;
    -webkit-border-radius:6px 0 6px 6px;
    -moz-border-radius:6px 0 6px 6px;
    border-radius:6px 0 6px 6px;
}

/* -------------------------------
 Custom CSS for DocStrap 
 --------------------------------- */

.method-doc-label {
  margin: 5px 0;
  padding: 10px 8px;
  margin-bottom: 0;
  font-weight: bold;
}

.method-params-label {
  border-bottom: 2px solid #DDD;
}
.subsection-title {
  font-size: 18px;
}
dl > dd > .description {
  margin: 20px;
  margin-bottom: 10px;
}
.method-returns {
  margin: 0 20px;
}
.method-returns dl {
  margin-top: 5px;
}
.jsdoc-message {
  color: #999;
  font-size: 10px;
}
.toc-h3-subsection-title {
  padding-left: 10px;
}
.toc-span-method-basic-name {
  padding-left: 20px;
}
.toc-shim {
  padding-top: 70px;
}
.method-wrapper {
  padding: 10px;
  border-bottom: 2px solid #eee;
}
.method-name-wrapper {
  margin: 10px 0;
}
.method-wrapper:nth-child(even) {
  background-color: #f9f9f9;
}
.method-name {
   font-size: 110%;
}
.method-body-wrapper {
   margin-left: 3%;
   margin-top: 15px;
}

@media (min-width: 992px) {

  #toc {
    position: fixed;
    top: 70px;
    right: 15px;
    height: 750px;
    overflow: auto;
  }

}
