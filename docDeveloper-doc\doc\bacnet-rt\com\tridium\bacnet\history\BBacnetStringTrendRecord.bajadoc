<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.history.BBacnetStringTrendRecord" name="BBacnetStringTrendRecord" packageName="com.tridium.bacnet.history" public="true">
<description>
&lt;code&gt;BBacnetStringTrendRecord&lt;/code&gt; is a Bacnet trend record with&#xa; a string value.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 1$ $Date: 8/11/2003 1:54:12 PM$</tag>
<tag name="@creation">11 Aug 2003</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.history.BBacnetTrendRecord"/>
</extends>
<property name="value" flags="s">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#getValue</tag>
<tag name="@see">#setValue</tag>
</property>

</class>
</bajadoc>
