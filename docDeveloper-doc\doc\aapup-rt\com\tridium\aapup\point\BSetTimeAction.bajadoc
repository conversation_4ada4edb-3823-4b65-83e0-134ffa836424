<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.point.BSetTimeAction" name="BSetTimeAction" packageName="com.tridium.aapup.point" public="true">
<description>
BSetTimeAction is used to adjust an attribute in a&#xa; PUP controller that has a &#x22;time&#x22; type.
</description>
<tag name="@author">Cli<PERSON></tag>
<tag name="@creation">9/6/2005 4:21PM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.93</tag>
<extends>
<type class="javax.baja.sys.BAction"/>
</extends>
</class>
</bajadoc>
