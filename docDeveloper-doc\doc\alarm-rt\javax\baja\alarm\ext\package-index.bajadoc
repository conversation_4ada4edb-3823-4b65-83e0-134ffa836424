<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="alarm" runtimeProfile="rt" name="javax.baja.alarm.ext">
<description>
&lt;p&gt;This package provides classes for alarm extensions to control points.&lt;/p&gt;
</description>
<class packageName="javax.baja.alarm.ext" name="BAlarmAlgorithm"><description>BAlarmAlgorithm is the base class for all alarm&#xa; algorithms designed to alarm algorithms for &#xa; BAlarmSourceExt.</description></class>
<class packageName="javax.baja.alarm.ext" name="BAlarmSourceExt"><description>BAlarmSourceExt is the abstract superclass of all&#xa; Baja control alarming algorithms.</description></class>
<class packageName="javax.baja.alarm.ext" name="BAlarmState"><description>BAlarmState is an BEnum that represents valid Baja alarm states</description></class>
<class packageName="javax.baja.alarm.ext" name="BAlarmTimestamps"><description>BAlarmTimestamps</description></class>
<class packageName="javax.baja.alarm.ext" name="BFaultAlgorithm"><description>BFaultAlgorithm is the super-class of all fault-detection&#xa; mechanisms defined by Niagara.</description></class>
<class packageName="javax.baja.alarm.ext" name="BLimitEnable"><description>BLimitEnable</description></class>
<class packageName="javax.baja.alarm.ext" name="BNotifyType"><description>BNotifyType is an BEnum that represents valid Baja notification types</description></class>
<class packageName="javax.baja.alarm.ext" name="BOffnormalAlgorithm"><description>BOffnormalAlgorithm is a super-class for all algorithms&#xa; that check for off normal (not fault) conditions.</description></class>
<class packageName="javax.baja.alarm.ext" name="BIAlarmMessages" category="interface"/>
</package>
</bajadoc>
