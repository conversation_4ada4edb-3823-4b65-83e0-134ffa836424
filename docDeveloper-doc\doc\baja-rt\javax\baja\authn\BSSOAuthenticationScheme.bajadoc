<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.authn.BSSOAuthenticationScheme" name="BSSOAuthenticationScheme" packageName="javax.baja.authn" public="true" abstract="true">
<description>
This is the base class for &lt;code&gt;<see ref="javax.baja.authn.BAuthenticationScheme">BAuthenticationScheme</see>&lt;/code&gt;s that can&#xa; perform Single Sign On.&#xa;&#xa; It is expected that users will be remote and pulled in from an external&#xa; database.&#xa;&#xa; BSSOAuthenticationSchemes are able to bypass the prelogin screens based&#xa; on configuration options.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">6/28/2016</tag>
<tag name="@since">Niagara 4.2</tag>
<extends>
<type class="javax.baja.authn.BAuthenticationScheme"/>
</extends>
<property name="loginButtonText" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;loginButtonText&lt;/code&gt; property.&#xa; Determines the text that will be displayed on the Single Sign On login button.
</description>
<tag name="@see">#getLoginButtonText</tag>
<tag name="@see">#setLoginButtonText</tag>
</property>

<!-- javax.baja.authn.BSSOAuthenticationScheme() -->
<constructor name="BSSOAuthenticationScheme" public="true">
<description/>
</constructor>

<!-- javax.baja.authn.BSSOAuthenticationScheme.getLoginButtonText() -->
<method name="getLoginButtonText"  public="true">
<description>
Get the &lt;code&gt;loginButtonText&lt;/code&gt; property.&#xa; Determines the text that will be displayed on the Single Sign On login button.
</description>
<tag name="@see">#loginButtonText</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.authn.BSSOAuthenticationScheme.setLoginButtonText(java.lang.String) -->
<method name="setLoginButtonText"  public="true">
<description>
Set the &lt;code&gt;loginButtonText&lt;/code&gt; property.&#xa; Determines the text that will be displayed on the Single Sign On login button.
</description>
<tag name="@see">#loginButtonText</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.authn.BSSOAuthenticationScheme.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.authn.BSSOAuthenticationScheme.getLoginRedirectURL() -->
<method name="getLoginRedirectURL"  public="true" abstract="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.authn.BSSOAuthenticationScheme.getHelpHTML() -->
<method name="getHelpHTML"  public="true">
<description>
Returns an &lt;code&gt;Optional&amp;lt;String&amp;gt;&lt;/code&gt; containing help HTML associated with&#xa; this authentication scheme.&#xa;&#xa; This HTML will be placed inside a &lt;code&gt;&amp;lt;div&amp;gt;&amp;lt;/div&amp;gt;&lt;/code&gt; tag that will be hidden&#xa; until the help link is pressed, there&#x27;s no need to include &lt;code&gt;&amp;lt;body&amp;gt;&amp;lt;/body&amp;gt;&lt;/code&gt;&#xa; tags, etc...
</description>
<return>
<parameterizedType class="java.util.Optional">
<args>
<type class="java.lang.String"/>
</args>
</parameterizedType>
<description>
An &lt;code&gt;Optional&amp;lt;String&amp;gt;&lt;/code&gt; containing help text in HTML format, if required.&#xa; If not, returns an empty Optional.
</description>
</return>
</method>

<!-- javax.baja.authn.BSSOAuthenticationScheme.supportsRemoteUsers() -->
<method name="supportsRemoteUsers"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.authn.BSSOAuthenticationScheme.loginButtonText -->
<field name="loginButtonText"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;loginButtonText&lt;/code&gt; property.&#xa; Determines the text that will be displayed on the Single Sign On login button.
</description>
<tag name="@see">#getLoginButtonText</tag>
<tag name="@see">#setLoginButtonText</tag>
</field>

<!-- javax.baja.authn.BSSOAuthenticationScheme.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
