<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetSilencedState" name="BBacnetSilencedState" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetSilencedState represents the Bacnet Silenced State&#xa; enumeration.&#xa; &lt;p&gt;&#xa; BBacnetSilencedState is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Craig Gemmill</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">16 May 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unsilenced&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;audibleSilenced&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;visibleSilenced&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;allSilenced&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetSilencedState.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetSilencedState"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetSilencedState"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.UNSILENCED -->
<field name="UNSILENCED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unsilenced.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.AUDIBLE_SILENCED -->
<field name="AUDIBLE_SILENCED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for audibleSilenced.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.VISIBLE_SILENCED -->
<field name="VISIBLE_SILENCED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for visibleSilenced.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.ALL_SILENCED -->
<field name="ALL_SILENCED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for allSilenced.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.unsilenced -->
<field name="unsilenced"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetSilencedState"/>
<description>
BBacnetSilencedState constant for unsilenced.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.audibleSilenced -->
<field name="audibleSilenced"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetSilencedState"/>
<description>
BBacnetSilencedState constant for audibleSilenced.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.visibleSilenced -->
<field name="visibleSilenced"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetSilencedState"/>
<description>
BBacnetSilencedState constant for visibleSilenced.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.allSilenced -->
<field name="allSilenced"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetSilencedState"/>
<description>
BBacnetSilencedState constant for allSilenced.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetSilencedState"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSilencedState.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
