<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="bacnetOws" runtimeProfile="rt" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>Niagara BACnet OWS Driver</description>
<package name="com.tridium.bacnetOws"/>
<package name="com.tridium.bacnetOws.datatypes"/>
<package name="com.tridium.bacnetOws.job"/>
<class packageName="com.tridium.bacnetOws" name="BBacnetOwsDeviceFolder"><description>BBacnetOwsDeviceFolder.</description></class>
<class packageName="com.tridium.bacnetOws" name="BBacnetOwsNetwork"><description>BBacnetOwsNetwork is the base container for the Tridium Bacnet Operator Workstation&#xa; service.</description></class>
<class packageName="com.tridium.bacnetOws" name="BBacnetThreadPoolWorker"></class>
<class packageName="com.tridium.bacnetOws.datatypes" name="BGetESummConfig"><description>BGetESummConfig represents the choices for the&#xa; user in manually issuing a GetEnrollmentSummary-Request to a device.</description></class>
<class packageName="com.tridium.bacnetOws.job" name="BGetEnrollmentSummaryJob"><description>BGetEnrollmentSummaryJob queries a device for objects that meet specific&#xa; filter criteria for event generation capability and/or status.</description></class>
<class packageName="com.tridium.bacnetOws.datatypes" name="BGetEventInfoConfig"><description>BGetEventInfoConfig configures the request for event information&#xa; from a BacnetWsDevice.</description></class>
<class packageName="com.tridium.bacnetOws.job" name="BGetEventInformationJob"><description>BGetEventInformationJob gets event information from a BacnetWsDevice.</description></class>
<class packageName="com.tridium.bacnetOws" name="BLocalBacnetOwsDevice"><description>BLocalBacnetOwsDevice is the representation of Niagara as a Bacnet&#xa; device on the Bacnet internetwork.</description></class>
</module>
</bajadoc>
