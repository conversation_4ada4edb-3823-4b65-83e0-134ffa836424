<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.enums.BAndoverDeviceTypeEnum" name="BAndoverDeviceTypeEnum" packageName="com.tridium.andoverAC256.enums" public="true" final="true">
<description>
Arbitrary enumeration of device type, necessary because&#xa; the AC256 and AC8 controllers differ(slightly) in logon&#xa; sequence, and differ in the info contained in the PRINT&#xa; STATUS response, and differ in sub-panels supported (impacts&#xa; the learn process)
</description>
<tag name="@author"><PERSON><PERSON><PERSON> (Original R2 code)</tag>
<tag name="@creation">07 Feb 2001</tag>
<tag name="@author"><PERSON><PERSON><PERSON> (Upgraded to R3)</tag>
<tag name="@creation">10/4/2004 2:14PM</tag>
<tag name="@version">$Revision: 2$ $Date: 10/4/2004 2:14PM$</tag>
<tag name="@since">Niagara 3.0 andoverAC256 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ac256&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>0</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ac8&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>1</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
</class>
</bajadoc>
