<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.collection.BInMemoryTable" name="BInMemoryTable" packageName="javax.baja.collection" public="true">
<description>
An in memory table stores all rows of the table in memory as a list.
</description>
<tag name="@author">&lt;a href=&#x22;mailto:<EMAIL>&#x22;&gt;<PERSON>&lt;/a&gt;</tag>
<extends>
<type class="javax.baja.sys.BObject"/>
</extends>
<implements>
<parameterizedType class="javax.baja.collection.BIRandomAccessTable">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</implements>
<typeParameters>
<typeVariable class="T">
<bounds>
<type class="javax.baja.sys.BIObject"/>
</bounds>
</typeVariable>
</typeParameters>
<!-- javax.baja.collection.BInMemoryTable(javax.baja.collection.BITable&lt;T&gt;) -->
<constructor name="BInMemoryTable" public="true">
<parameter name="table">
<parameterizedType class="javax.baja.collection.BITable">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</parameter>
<description/>
</constructor>

<!-- javax.baja.collection.BInMemoryTable.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.collection.BInMemoryTable.size() -->
<method name="size"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.collection.BInMemoryTable.get(int) -->
<method name="get"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="row">
<type class="int"/>
</parameter>
<return>
<parameterizedType class="javax.baja.collection.Row">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.collection.BInMemoryTable.cursor() -->
<method name="cursor"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<parameterizedType class="javax.baja.collection.TableCursor">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.collection.BInMemoryTable.getColumns() -->
<method name="getColumns"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.collection.ColumnList"/>
</return>
</method>

<!-- javax.baja.collection.BInMemoryTable.getTableFacets() -->
<method name="getTableFacets"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.collection.BInMemoryTable.sort(javax.baja.collection.Column, boolean) -->
<method name="sort"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="col">
<type class="javax.baja.collection.Column"/>
</parameter>
<parameter name="ascending">
<type class="boolean"/>
</parameter>
<return>
<parameterizedType class="javax.baja.collection.BIRandomAccessTable">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.collection.BInMemoryTable.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
