<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.InvalidOrdBaseException" name="InvalidOrdBaseException" packageName="javax.baja.naming" public="true" category="exception">
<description>
InvalidOrdBaseException is thrown when the base for&#xa; a query is not valid for the query scheme.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">16 Dec 2002</tag>
<tag name="@version">$Revision: 1$ $Date: 12/18/02 1:06:22 PM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BajaRuntimeException"/>
</extends>
<!-- javax.baja.naming.InvalidOrdBaseException(java.lang.String, java.lang.Throwable) -->
<constructor name="InvalidOrdBaseException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="cause">
<type class="java.lang.Throwable"/>
</parameter>
<description>
Constructor with specified detailed message and cause.
</description>
</constructor>

<!-- javax.baja.naming.InvalidOrdBaseException(java.lang.String) -->
<constructor name="InvalidOrdBaseException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<description>
Constructor with specified detailed message.
</description>
</constructor>

<!-- javax.baja.naming.InvalidOrdBaseException() -->
<constructor name="InvalidOrdBaseException" public="true">
<description>
Constructor with no message.
</description>
</constructor>

</class>
</bajadoc>
