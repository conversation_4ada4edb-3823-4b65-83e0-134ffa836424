<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.program.BInlineJsonWriter" name="BInlineJsonWriter" packageName="com.tridiumx.jsonToolkit.outbound.schema.program" public="true">
<description>
Allows the schema to defer control to a Program in the tree of Schema members, meaning the user can&#xa; do as they please to insert dynamic content in the Schema output.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.program.BAbstractInlineJsonWriter"/>
</extends>
<property name="program" flags="">
<type class="com.tridium.program.BProgram"/>
<description>
Slot for the &lt;code&gt;program&lt;/code&gt; property.&#xa; The example in the palette implements a method public BValue onOverride(final BInlineJsonWriter input)
</description>
<tag name="@see">#getProgram</tag>
<tag name="@see">#setProgram</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.program.BInlineJsonWriter() -->
<constructor name="BInlineJsonWriter" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.program.BInlineJsonWriter.getProgram() -->
<method name="getProgram"  public="true">
<description>
Get the &lt;code&gt;program&lt;/code&gt; property.&#xa; The example in the palette implements a method public BValue onOverride(final BInlineJsonWriter input)
</description>
<tag name="@see">#program</tag>
<return>
<type class="com.tridium.program.BProgram"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.program.BInlineJsonWriter.setProgram(com.tridium.program.BProgram) -->
<method name="setProgram"  public="true">
<description>
Set the &lt;code&gt;program&lt;/code&gt; property.&#xa; The example in the palette implements a method public BValue onOverride(final BInlineJsonWriter input)
</description>
<tag name="@see">#program</tag>
<parameter name="v">
<type class="com.tridium.program.BProgram"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.program.BInlineJsonWriter.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.program.BInlineJsonWriter.make() -->
<method name="make"  public="true" static="true">
<description>
Builder will need to replace the program code to something useful.
</description>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.program.BInlineJsonWriter"/>
<description>
new ProgramOverride
</description>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.program.BInlineJsonWriter.process(com.tridium.json.JSONWriter, boolean) -->
<method name="process"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Pass _this_ into the override program which allows it to access the writer&#xa; and base&#xa;&#xa; getJsonName is not exposed to the program as user could getParent and set&#xa; the name from within the Program&#xa;&#xa; The method signature in the program will need to look like:
</description>
<tag name="@code">public BValue onOverride(final BInlineJsonWriter in)</tag>
<parameter name="jsonWriter">
<type class="com.tridium.json.JSONWriter"/>
<description>
the existing json builder.
</description>
</parameter>
<parameter name="jsonKeysValid">
<type class="boolean"/>
<description>
true if we are currently inside an object.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.program.BInlineJsonWriter.program -->
<field name="program"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;program&lt;/code&gt; property.&#xa; The example in the palette implements a method public BValue onOverride(final BInlineJsonWriter input)
</description>
<tag name="@see">#getProgram</tag>
<tag name="@see">#setProgram</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.program.BInlineJsonWriter.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
