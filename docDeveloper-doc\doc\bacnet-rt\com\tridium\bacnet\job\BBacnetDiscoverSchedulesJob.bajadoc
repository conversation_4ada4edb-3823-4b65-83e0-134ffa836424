<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.job.BBacnetDiscoverSchedulesJob" name="BBacnetDiscoverSchedulesJob" packageName="com.tridium.bacnet.job" public="true">
<description>
BBacnetDiscoverSchedulesJob is the task that manages calendar and schedule&#xa; discovery in a remote BACnet device.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">04 Mar 04</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.job.BBacnetDiscoverJob"/>
</extends>
</class>
</bajadoc>
