<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.point.BAcePointDeviceExt" name="BAcePointDeviceExt" packageName="com.tridium.ace.point" public="true">
<description>
BAcePointDeviceExt is a container for ace proxy points.
</description>
<tag name="@author"><PERSON> on 02-Sep-16</tag>
<extends>
<type class="com.tridium.ndriver.point.BNPointDeviceExt"/>
</extends>
<action name="forceUpdate" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;forceUpdate&lt;/code&gt; action.
</description>
<tag name="@see">#forceUpdate()</tag>
</action>

<topic name="missingModules" flags="s">
<eventType>
<type class="javax.baja.sys.BString"/>
</eventType><description>
Slot for the &lt;code&gt;missingModules&lt;/code&gt; topic.
</description>
<tag name="@see">#fireMissingModules</tag>
</topic>

</class>
</bajadoc>
