<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BLocalizedFileSpace" name="BLocalizedFileSpace" packageName="javax.baja.file" public="true" abstract="true">
<description>
Abstract for a file space that can translate paths to a localized file system. Supports&#xa; target systems that may have a read-only Niagara Home directory by redirecting system files&#xa; that may be subject to modification to an alternate location.&#xa;&#xa; The following system file redirections are performed, including contained files and&#xa; subdirectories:&#xa;&#xa; &lt;pre&gt;&lt;code&gt;&amp;#xa;   !modules  -&amp;gt; ~modules&amp;#xa;   !security -&amp;gt; ~security&amp;#xa; &lt;/code&gt;&lt;/pre&gt;
</description>
<tag name="@author"><PERSON> on 4/21/2022.</tag>
<tag name="@since">Niagara 4.13</tag>
<extends>
<type class="javax.baja.file.BFileSpace"/>
</extends>
<!-- javax.baja.file.BLocalizedFileSpace(java.lang.String, javax.baja.util.LexiconText) -->
<constructor name="BLocalizedFileSpace" public="true">
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="lexText">
<type class="javax.baja.util.LexiconText"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.file.BLocalizedFileSpace(java.lang.String) -->
<constructor name="BLocalizedFileSpace" public="true">
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.file.BLocalizedFileSpace.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.file.BLocalizedFileSpace.isNiagaraHomeReadOnly() -->
<method name="isNiagaraHomeReadOnly"  public="true" abstract="true">
<description>
Determines if the target system has a read-only Niagara Home directory.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BLocalizedFileSpace.getLocalizedFilePath(javax.baja.file.FilePath) -->
<method name="getLocalizedFilePath"  protected="true">
<description>
When Niagara Home is read-only, redirect certain system file paths to an alternate writable&#xa; location. For example, !modules and !security will be redirected to ~modules and ~security&#xa; respectively.
</description>
<tag name="@since">Niagara 4.13</tag>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
<description>
the original file path
</description>
</parameter>
<return>
<type class="javax.baja.file.FilePath"/>
<description>
the redirected file path if the target system requires redirection;&#xa; otherwise the original file path
</description>
</return>
</method>

<!-- javax.baja.file.BLocalizedFileSpace.findFile(javax.baja.file.FilePath) -->
<method name="findFile"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Routes to findStore() and makeFile(), redirecting system files if needed.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
</method>

<!-- javax.baja.file.BLocalizedFileSpace.resolveFile(javax.baja.file.FilePath) -->
<method name="resolveFile"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
This method calls findFile(path), redirecting system files if needed.  If null is&#xa; returned then UnresolvedException() is thrown.
</description>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
</method>

<!-- javax.baja.file.BLocalizedFileSpace.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
