<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.agent.BAbstractPxView" name="BAbstractPxView" packageName="javax.baja.agent" public="true" abstract="true">
<description>
BAbstractPxView is a dynamic view which may be added to BComponents&#xa; as a property or by overriding getAgents().
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">18 Dec 06</tag>
<tag name="@version">$Revision: 2$ $Date: 6/11/07 12:41:23 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.agent.AgentInfo"/>
</implements>
<implements>
<type class="javax.baja.agent.BIAgent"/>
</implements>
<property name="icon" flags="">
<type class="javax.baja.sys.BIcon"/>
<description>
Slot for the &lt;code&gt;icon&lt;/code&gt; property.&#xa; Defines the icon to use for the view
</description>
<tag name="@see">#getIcon</tag>
<tag name="@see">#setIcon</tag>
</property>

<property name="requiredPermissions" flags="">
<type class="javax.baja.security.BPermissions"/>
<description>
Slot for the &lt;code&gt;requiredPermissions&lt;/code&gt; property.&#xa; Defaults the permissions needed to access the view
</description>
<tag name="@see">#getRequiredPermissions</tag>
<tag name="@see">#setRequiredPermissions</tag>
</property>

<property name="media" flags="">
<type class="javax.baja.util.BTypeSpec"/>
<description>
Slot for the &lt;code&gt;media&lt;/code&gt; property.&#xa; Specified the target media for the view.
</description>
<tag name="@see">#getMedia</tag>
<tag name="@see">#setMedia</tag>
</property>

<!-- javax.baja.agent.BAbstractPxView(javax.baja.sys.BIcon, javax.baja.security.BPermissions, javax.baja.util.BTypeSpec) -->
<constructor name="BAbstractPxView" protected="true">
<parameter name="icon">
<type class="javax.baja.sys.BIcon"/>
</parameter>
<parameter name="permissions">
<type class="javax.baja.security.BPermissions"/>
</parameter>
<parameter name="media">
<type class="javax.baja.util.BTypeSpec"/>
</parameter>
<description>
Constructor with all fields.
</description>
</constructor>

<!-- javax.baja.agent.BAbstractPxView() -->
<constructor name="BAbstractPxView" protected="true">
<description>
No argument constructor.
</description>
</constructor>

<!-- javax.baja.agent.BAbstractPxView.getIcon() -->
<method name="getIcon"  public="true">
<description>
Get the &lt;code&gt;icon&lt;/code&gt; property.&#xa; Defines the icon to use for the view
</description>
<tag name="@see">#icon</tag>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.agent.BAbstractPxView.setIcon(javax.baja.sys.BIcon) -->
<method name="setIcon"  public="true">
<description>
Set the &lt;code&gt;icon&lt;/code&gt; property.&#xa; Defines the icon to use for the view
</description>
<tag name="@see">#icon</tag>
<parameter name="v">
<type class="javax.baja.sys.BIcon"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.BAbstractPxView.getRequiredPermissions() -->
<method name="getRequiredPermissions"  public="true">
<description>
Get the &lt;code&gt;requiredPermissions&lt;/code&gt; property.&#xa; Defaults the permissions needed to access the view
</description>
<tag name="@see">#requiredPermissions</tag>
<return>
<type class="javax.baja.security.BPermissions"/>
</return>
</method>

<!-- javax.baja.agent.BAbstractPxView.setRequiredPermissions(javax.baja.security.BPermissions) -->
<method name="setRequiredPermissions"  public="true">
<description>
Set the &lt;code&gt;requiredPermissions&lt;/code&gt; property.&#xa; Defaults the permissions needed to access the view
</description>
<tag name="@see">#requiredPermissions</tag>
<parameter name="v">
<type class="javax.baja.security.BPermissions"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.BAbstractPxView.getMedia() -->
<method name="getMedia"  public="true">
<description>
Get the &lt;code&gt;media&lt;/code&gt; property.&#xa; Specified the target media for the view.
</description>
<tag name="@see">#media</tag>
<return>
<type class="javax.baja.util.BTypeSpec"/>
</return>
</method>

<!-- javax.baja.agent.BAbstractPxView.setMedia(javax.baja.util.BTypeSpec) -->
<method name="setMedia"  public="true">
<description>
Set the &lt;code&gt;media&lt;/code&gt; property.&#xa; Specified the target media for the view.
</description>
<tag name="@see">#media</tag>
<parameter name="v">
<type class="javax.baja.util.BTypeSpec"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.agent.BAbstractPxView.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.agent.BAbstractPxView.getInstance() -->
<method name="getInstance"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;this&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
</method>

<!-- javax.baja.agent.BAbstractPxView.getAgentId() -->
<method name="getAgentId"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getName()&lt;/code&gt;.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.agent.BAbstractPxView.getAgentType() -->
<method name="getAgentType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getType().getTypeInfo()&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.registry.TypeInfo"/>
</return>
</method>

<!-- javax.baja.agent.BAbstractPxView.getAppName() -->
<method name="getAppName"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return null.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.agent.BAbstractPxView.getAgentOn() -->
<method name="getAgentOn"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return an empty array.
</description>
<return>
<type class="javax.baja.registry.TypeInfo" dimension="1"/>
</return>
</method>

<!-- javax.baja.agent.BAbstractPxView.getIcon(javax.baja.sys.Context) -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return icon property.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.agent.BAbstractPxView.icon -->
<field name="icon"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;icon&lt;/code&gt; property.&#xa; Defines the icon to use for the view
</description>
<tag name="@see">#getIcon</tag>
<tag name="@see">#setIcon</tag>
</field>

<!-- javax.baja.agent.BAbstractPxView.requiredPermissions -->
<field name="requiredPermissions"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;requiredPermissions&lt;/code&gt; property.&#xa; Defaults the permissions needed to access the view
</description>
<tag name="@see">#getRequiredPermissions</tag>
<tag name="@see">#setRequiredPermissions</tag>
</field>

<!-- javax.baja.agent.BAbstractPxView.media -->
<field name="media"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;media&lt;/code&gt; property.&#xa; Specified the target media for the view.
</description>
<tag name="@see">#getMedia</tag>
<tag name="@see">#setMedia</tag>
</field>

<!-- javax.baja.agent.BAbstractPxView.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
