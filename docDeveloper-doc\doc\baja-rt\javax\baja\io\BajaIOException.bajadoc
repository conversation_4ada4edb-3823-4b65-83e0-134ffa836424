<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.io.BajaIOException" name="BajaIOException" packageName="javax.baja.io" public="true" category="exception">
<description>
BajaIOException is the root exception for checked&#xa; IOExceptions in the Baja architecture.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">27 Feb 01</tag>
<tag name="@version">$Revision: 2$ $Date: 6/13/01 9:34:08 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="java.io.IOException"/>
</extends>
<!-- javax.baja.io.BajaIOException(java.lang.String, java.lang.Throwable) -->
<constructor name="BajaIOException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="cause">
<type class="java.lang.Throwable"/>
</parameter>
<description>
Constructor with specified message and nested exception.
</description>
</constructor>

<!-- javax.baja.io.BajaIOException(java.lang.Throwable) -->
<constructor name="BajaIOException" public="true">
<parameter name="cause">
<type class="java.lang.Throwable"/>
</parameter>
<description>
Constructor with specified nested exception.
</description>
</constructor>

<!-- javax.baja.io.BajaIOException(java.lang.String) -->
<constructor name="BajaIOException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<description>
Constructor with specified message.
</description>
</constructor>

<!-- javax.baja.io.BajaIOException() -->
<constructor name="BajaIOException" public="true">
<description>
No argument constructor.
</description>
</constructor>

<!-- javax.baja.io.BajaIOException.getCause() -->
<method name="getCause"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the nested exception for this BajaIOException&#xa; or return null if no nested exception is provided.
</description>
<return>
<type class="java.lang.Throwable"/>
</return>
</method>

</class>
</bajadoc>
