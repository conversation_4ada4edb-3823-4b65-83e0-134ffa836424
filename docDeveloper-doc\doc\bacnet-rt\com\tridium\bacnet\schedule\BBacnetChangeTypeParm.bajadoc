<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.schedule.BBacnetChangeTypeParm" name="BBacnetChangeTypeParm" packageName="com.tridium.bacnet.schedule" public="true">
<description>
BBacnetChangeTypeParm is container for schedule properties that&#xa; might need to change to modify the schedule datatype.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 14$ $Date: 10/17/00 12:47:14 PM$</tag>
<tag name="@creation">5 Mar 2010</tag>
<tag name="@since">Niagara 3.6</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="dataType" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;dataType&lt;/code&gt; property.&#xa; the Asn primitive application data type.
</description>
<tag name="@see">#getDataType</tag>
<tag name="@see">#setDataType</tag>
</property>

<property name="supervisorOrd" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;supervisorOrd&lt;/code&gt; property.
</description>
<tag name="@see">#getSupervisorOrd</tag>
<tag name="@see">#setSupervisorOrd</tag>
</property>

<property name="listOfObjectPropertyRefs" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
<description>
Slot for the &lt;code&gt;listOfObjectPropertyRefs&lt;/code&gt; property.
</description>
<tag name="@see">#getListOfObjectPropertyRefs</tag>
<tag name="@see">#setListOfObjectPropertyRefs</tag>
</property>

<property name="scheduleDefault" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetAny"/>
<description>
Slot for the &lt;code&gt;scheduleDefault&lt;/code&gt; property.
</description>
<tag name="@see">#getScheduleDefault</tag>
<tag name="@see">#setScheduleDefault</tag>
</property>

<property name="hasWeeklySchedule" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;hasWeeklySchedule&lt;/code&gt; property.
</description>
<tag name="@see">#getHasWeeklySchedule</tag>
<tag name="@see">#setHasWeeklySchedule</tag>
</property>

<property name="weeklySchedule" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
<description>
Slot for the &lt;code&gt;weeklySchedule&lt;/code&gt; property.
</description>
<tag name="@see">#getWeeklySchedule</tag>
<tag name="@see">#setWeeklySchedule</tag>
</property>

<property name="hasExceptionSchedule" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;hasExceptionSchedule&lt;/code&gt; property.
</description>
<tag name="@see">#getHasExceptionSchedule</tag>
<tag name="@see">#setHasExceptionSchedule</tag>
</property>

<property name="exceptionSchedule" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
<description>
Slot for the &lt;code&gt;exceptionSchedule&lt;/code&gt; property.
</description>
<tag name="@see">#getExceptionSchedule</tag>
<tag name="@see">#setExceptionSchedule</tag>
</property>

<property name="typeChanged" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;typeChanged&lt;/code&gt; property.
</description>
<tag name="@see">#getTypeChanged</tag>
<tag name="@see">#setTypeChanged</tag>
</property>

<property name="superChanged" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;superChanged&lt;/code&gt; property.
</description>
<tag name="@see">#getSuperChanged</tag>
<tag name="@see">#setSuperChanged</tag>
</property>

<property name="oprChanged" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;oprChanged&lt;/code&gt; property.
</description>
<tag name="@see">#getOprChanged</tag>
<tag name="@see">#setOprChanged</tag>
</property>

<property name="schedDefChanged" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;schedDefChanged&lt;/code&gt; property.
</description>
<tag name="@see">#getSchedDefChanged</tag>
<tag name="@see">#setSchedDefChanged</tag>
</property>

<property name="weeklyChanged" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;weeklyChanged&lt;/code&gt; property.
</description>
<tag name="@see">#getWeeklyChanged</tag>
<tag name="@see">#setWeeklyChanged</tag>
</property>

<property name="exceptionChanged" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;exceptionChanged&lt;/code&gt; property.
</description>
<tag name="@see">#getExceptionChanged</tag>
<tag name="@see">#setExceptionChanged</tag>
</property>

</class>
</bajadoc>
