<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus" name="BBacnetAuthenticationStatus" packageName="javax.baja.bacnet.enums.access" public="true" final="true">
<description>
BBacnetAccessCredentialDisable represents the Bacnet&#xa; Access Credential Disable enumeration.&#xa; &lt;p&gt;&#xa; BBacnetAccessCredentialDisable is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Joseph Chandler</tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;notReady&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>0</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ready&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>1</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;disabled&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>2</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;waitingForAuthenticationFactor&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>3</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;waitingForAccompaniment&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>4</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;waitingForVerification&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>5</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;inProgress&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>6</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.NOT_READY -->
<field name="NOT_READY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for notReady.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.READY -->
<field name="READY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ready.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.DISABLED -->
<field name="DISABLED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for disabled.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.WAITING_FOR_AUTHENTICATION_FACTOR -->
<field name="WAITING_FOR_AUTHENTICATION_FACTOR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for waitingForAuthenticationFactor.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.WAITING_FOR_ACCOMPANIMENT -->
<field name="WAITING_FOR_ACCOMPANIMENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for waitingForAccompaniment.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.WAITING_FOR_VERIFICATION -->
<field name="WAITING_FOR_VERIFICATION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for waitingForVerification.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.IN_PROGRESS -->
<field name="IN_PROGRESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for inProgress.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.notReady -->
<field name="notReady"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus"/>
<description>
BBacnetAuthenticationStatus constant for notReady.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.ready -->
<field name="ready"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus"/>
<description>
BBacnetAuthenticationStatus constant for ready.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.disabled -->
<field name="disabled"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus"/>
<description>
BBacnetAuthenticationStatus constant for disabled.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.waitingForAuthenticationFactor -->
<field name="waitingForAuthenticationFactor"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus"/>
<description>
BBacnetAuthenticationStatus constant for waitingForAuthenticationFactor.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.waitingForAccompaniment -->
<field name="waitingForAccompaniment"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus"/>
<description>
BBacnetAuthenticationStatus constant for waitingForAccompaniment.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.waitingForVerification -->
<field name="waitingForVerification"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus"/>
<description>
BBacnetAuthenticationStatus constant for waitingForVerification.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.inProgress -->
<field name="inProgress"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus"/>
<description>
BBacnetAuthenticationStatus constant for inProgress.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationStatus.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
