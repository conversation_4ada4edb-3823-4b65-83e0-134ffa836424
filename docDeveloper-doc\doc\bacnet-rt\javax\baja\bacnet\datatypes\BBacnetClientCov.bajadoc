<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetClientCov" name="BBacnetClientCov" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BBacnetClientCov represents the choice for the COV increment to&#xa; be used in acquiring data for a trend log via COV.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">18 Nov 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="increment" flags="">
<type class="javax.baja.status.BStatusNumeric"/>
<description>
Slot for the &lt;code&gt;increment&lt;/code&gt; property.&#xa; if null, then the default-increment choice is used.&#xa; if non-null, then the real-increment choice is used.
</description>
<tag name="@see">#getIncrement</tag>
<tag name="@see">#setIncrement</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetClientCov() -->
<constructor name="BBacnetClientCov" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetClientCov(double) -->
<constructor name="BBacnetClientCov" public="true">
<parameter name="realIncrement">
<type class="double"/>
<description/>
</parameter>
<description>
Object ID constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetClientCov.getIncrement() -->
<method name="getIncrement"  public="true">
<description>
Get the &lt;code&gt;increment&lt;/code&gt; property.&#xa; if null, then the default-increment choice is used.&#xa; if non-null, then the real-increment choice is used.
</description>
<tag name="@see">#increment</tag>
<return>
<type class="javax.baja.status.BStatusNumeric"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetClientCov.setIncrement(javax.baja.status.BStatusNumeric) -->
<method name="setIncrement"  public="true">
<description>
Set the &lt;code&gt;increment&lt;/code&gt; property.&#xa; if null, then the default-increment choice is used.&#xa; if non-null, then the real-increment choice is used.
</description>
<tag name="@see">#increment</tag>
<parameter name="v">
<type class="javax.baja.status.BStatusNumeric"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetClientCov.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetClientCov.setRealIncrement(double) -->
<method name="setRealIncrement"  public="true">
<description>
Set the real-increment.
</description>
<parameter name="v">
<type class="double"/>
<description>
the new increment.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetClientCov.setDefaultIncrement() -->
<method name="setDefaultIncrement"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetClientCov.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetClientCov.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetClientCov.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetClientCov.increment -->
<field name="increment"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;increment&lt;/code&gt; property.&#xa; if null, then the default-increment choice is used.&#xa; if non-null, then the real-increment choice is used.
</description>
<tag name="@see">#getIncrement</tag>
<tag name="@see">#setIncrement</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetClientCov.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
