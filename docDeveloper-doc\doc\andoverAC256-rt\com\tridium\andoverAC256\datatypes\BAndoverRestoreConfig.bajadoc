<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.datatypes.BAndoverRestoreConfig" name="BAndoverRestoreConfig" packageName="com.tridium.andoverAC256.datatypes" public="true">
<description>
BAndoverRestoreConfig holds data for the BAndoverBackupJob
</description>
<tag name="@author">Cli<PERSON></tag>
<tag name="@creation">5/4/2005 8:14AM</tag>
<tag name="@version">$Revision$ $Date: 4/13/2005 4:36:34 PM$</tag>
<tag name="@since">AX 3.0.79</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="restoreDialog" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;restoreDialog&lt;/code&gt; property.&#xa; String description such as &#x22;Restoring Domain 0 (main controller)&#x22;
</description>
<tag name="@see">#getRestoreDialog</tag>
<tag name="@see">#setRestoreDialog</tag>
</property>

<property name="fileName" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;fileName&lt;/code&gt; property.&#xa; Name of file where backup is stored
</description>
<tag name="@see">#getFileName</tag>
<tag name="@see">#setFileName</tag>
</property>

<property name="timestamp" flags="r">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;timestamp&lt;/code&gt; property.&#xa; Timestamp of the backup file
</description>
<tag name="@see">#getTimestamp</tag>
<tag name="@see">#setTimestamp</tag>
</property>

<property name="domain" flags="h">
<type class="int"/>
<description>
Slot for the &lt;code&gt;domain&lt;/code&gt; property.&#xa; Domain to backup
</description>
<tag name="@see">#getDomain</tag>
<tag name="@see">#setDomain</tag>
</property>

<property name="ord" flags="h">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;ord&lt;/code&gt; property.&#xa; Ord of the file to use for restore
</description>
<tag name="@see">#getOrd</tag>
<tag name="@see">#setOrd</tag>
</property>

<property name="clearMemory" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;clearMemory&lt;/code&gt; property.&#xa; Clear the memory of an LCU?
</description>
<tag name="@see">#getClearMemory</tag>
<tag name="@see">#setClearMemory</tag>
</property>

</class>
</bajadoc>
