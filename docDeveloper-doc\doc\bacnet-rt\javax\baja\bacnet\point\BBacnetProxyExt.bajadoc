<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.point.BBacnetProxyExt" name="BBacnetProxyExt" packageName="javax.baja.bacnet.point" public="true" abstract="true">
<description>
BBacnetProxyExt contains all of the information necessary&#xa; to access a data value from a Bacnet device.&#xa; &lt;p&gt;&#xa; Each proxy point that represents a Bacnet data quantity will&#xa; have a BBacnetProxyExt to describe how to access the point.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">14 Dec 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.driver.point.BProxyExt"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<implements>
<type class="javax.baja.bacnet.util.BIBacnetPollable"/>
</implements>
<implements>
<type class="javax.baja.alarm.BIRemoteAlarmSource"/>
</implements>
<property name="objectId" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; the Bacnet Object_Identifier of the containing object.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="propertyId" flags="">
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
Slot for the &lt;code&gt;propertyId&lt;/code&gt; property.&#xa; the Bacnet Property_Identifier of the referenced property.
</description>
<tag name="@see">#getPropertyId</tag>
<tag name="@see">#setPropertyId</tag>
</property>

<property name="propertyArrayIndex" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.&#xa; the property array index, if used.
</description>
<tag name="@see">#getPropertyArrayIndex</tag>
<tag name="@see">#setPropertyArrayIndex</tag>
</property>

<property name="dataType" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;dataType&lt;/code&gt; property.&#xa; the Asn primitive application data type.
</description>
<tag name="@see">#getDataType</tag>
<tag name="@see">#setDataType</tag>
</property>

<property name="readStatus" flags="tr">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;readStatus&lt;/code&gt; property.
</description>
<tag name="@see">#getReadStatus</tag>
<tag name="@see">#setReadStatus</tag>
</property>

<property name="writeStatus" flags="tr">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;writeStatus&lt;/code&gt; property.
</description>
<tag name="@see">#getWriteStatus</tag>
<tag name="@see">#setWriteStatus</tag>
</property>

<action name="forceRead" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;forceRead&lt;/code&gt; action.
</description>
<tag name="@see">#forceRead()</tag>
</action>

<action name="forceWrite" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;forceWrite&lt;/code&gt; action.
</description>
<tag name="@see">#forceWrite()</tag>
</action>

<action name="subscribeCov" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;subscribeCov&lt;/code&gt; action.
</description>
<tag name="@see">#subscribeCov()</tag>
</action>

<action name="subscribeCovProperty" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;subscribeCovProperty&lt;/code&gt; action.
</description>
<tag name="@see">#subscribeCovProperty()</tag>
</action>

<action name="ackAlarm" flags="h">
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
<description>
Slot for the &lt;code&gt;ackAlarm&lt;/code&gt; action.
</description>
<tag name="@see">#ackAlarm(BAlarmRecord parameter)</tag>
</action>

<!-- javax.baja.bacnet.point.BBacnetProxyExt() -->
<constructor name="BBacnetProxyExt" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getObjectId() -->
<method name="getObjectId"  public="true">
<description>
Get the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; the Bacnet Object_Identifier of the containing object.
</description>
<tag name="@see">#objectId</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.setObjectId(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="setObjectId"  public="true">
<description>
Set the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; the Bacnet Object_Identifier of the containing object.
</description>
<tag name="@see">#objectId</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getPropertyId() -->
<method name="getPropertyId"  public="true">
<description>
Get the &lt;code&gt;propertyId&lt;/code&gt; property.&#xa; the Bacnet Property_Identifier of the referenced property.
</description>
<tag name="@see">#propertyId</tag>
<return>
<type class="javax.baja.sys.BDynamicEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.setPropertyId(javax.baja.sys.BDynamicEnum) -->
<method name="setPropertyId"  public="true">
<description>
Set the &lt;code&gt;propertyId&lt;/code&gt; property.&#xa; the Bacnet Property_Identifier of the referenced property.
</description>
<tag name="@see">#propertyId</tag>
<parameter name="v">
<type class="javax.baja.sys.BDynamicEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getPropertyArrayIndex() -->
<method name="getPropertyArrayIndex"  public="true">
<description>
Get the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.&#xa; the property array index, if used.
</description>
<tag name="@see">#propertyArrayIndex</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.setPropertyArrayIndex(int) -->
<method name="setPropertyArrayIndex"  public="true">
<description>
Set the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.&#xa; the property array index, if used.
</description>
<tag name="@see">#propertyArrayIndex</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getDataType() -->
<method name="getDataType"  public="true">
<description>
Get the &lt;code&gt;dataType&lt;/code&gt; property.&#xa; the Asn primitive application data type.
</description>
<tag name="@see">#dataType</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.setDataType(java.lang.String) -->
<method name="setDataType"  public="true">
<description>
Set the &lt;code&gt;dataType&lt;/code&gt; property.&#xa; the Asn primitive application data type.
</description>
<tag name="@see">#dataType</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getReadStatus() -->
<method name="getReadStatus"  public="true">
<description>
Get the &lt;code&gt;readStatus&lt;/code&gt; property.
</description>
<tag name="@see">#readStatus</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.setReadStatus(java.lang.String) -->
<method name="setReadStatus"  public="true">
<description>
Set the &lt;code&gt;readStatus&lt;/code&gt; property.
</description>
<tag name="@see">#readStatus</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getWriteStatus() -->
<method name="getWriteStatus"  public="true">
<description>
Get the &lt;code&gt;writeStatus&lt;/code&gt; property.
</description>
<tag name="@see">#writeStatus</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.setWriteStatus(java.lang.String) -->
<method name="setWriteStatus"  public="true">
<description>
Set the &lt;code&gt;writeStatus&lt;/code&gt; property.
</description>
<tag name="@see">#writeStatus</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.forceRead() -->
<method name="forceRead"  public="true">
<description>
Invoke the &lt;code&gt;forceRead&lt;/code&gt; action.
</description>
<tag name="@see">#forceRead</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.forceWrite() -->
<method name="forceWrite"  public="true">
<description>
Invoke the &lt;code&gt;forceWrite&lt;/code&gt; action.
</description>
<tag name="@see">#forceWrite</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.subscribeCov() -->
<method name="subscribeCov"  public="true">
<description>
Invoke the &lt;code&gt;subscribeCov&lt;/code&gt; action.
</description>
<tag name="@see">#subscribeCov</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.subscribeCovProperty() -->
<method name="subscribeCovProperty"  public="true">
<description>
Invoke the &lt;code&gt;subscribeCovProperty&lt;/code&gt; action.
</description>
<tag name="@see">#subscribeCovProperty</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.ackAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="ackAlarm"  public="true">
<description>
Invoke the &lt;code&gt;ackAlarm&lt;/code&gt; action.
</description>
<tag name="@see">#ackAlarm</tag>
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.doForceRead() -->
<method name="doForceRead"  public="true">
<description>
Read the point&#x27;s value from BACnet.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.doForceWrite() -->
<method name="doForceWrite"  public="true">
<description>
Force a write of the point&#x27;s writeValue.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.doSubscribeCov() -->
<method name="doSubscribeCov"  public="true">
<description>
Subscribe for COV updates.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.doSubscribeCovProperty() -->
<method name="doSubscribeCovProperty"  public="true">
<description>
Subscribe for COVP updates.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.doAckAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="doAckAlarm"  public="true">
<description/>
<parameter name="alarm">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.started() -->
<method name="started"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Started.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.stopped() -->
<method name="stopped"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Stopped.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.added(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="added"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Property added.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.removed(javax.baja.sys.Property, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="removed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Property removed.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="v">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Property changed.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getDeviceExtType() -->
<method name="getDeviceExtType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the device type this proxy extension belongs under.
</description>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getMode() -->
<method name="getMode"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return if this proxy point is readonly, readWrite or writeonly.
</description>
<return>
<type class="javax.baja.driver.point.BReadWriteMode"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.readFail(java.lang.String) -->
<method name="readFail"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
This method is called when a read from the device&#xa; fails due to a configuration or fault error.
</description>
<parameter name="cause">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.writeFail(java.lang.String) -->
<method name="writeFail"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
This method is called when a write to the device&#xa; fails for any reason.
</description>
<parameter name="cause">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.pointFacetsChanged() -->
<method name="pointFacetsChanged"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.isSiblingLegal(javax.baja.sys.BComponent) -->
<method name="isSiblingLegal"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Check here if the ext being added is a trend log ext, in which case we&#xa; will need to rebuild the poll list entries to make sure to include the&#xa; Status_Flags.
</description>
<parameter name="sibling">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.readSubscribed(javax.baja.sys.Context) -->
<method name="readSubscribed"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
This callback is made when the component enters a subscribed&#xa; state based on the current status and tuning.  The driver&#xa; should register for changes or begin polling.  Any IO should&#xa; be done asynchronously on another thread - never block the&#xa; calling thread.  The result of reads should be to call the&#xa; readOk() or readFail() method.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.readUnsubscribed(javax.baja.sys.Context) -->
<method name="readUnsubscribed"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
This callback is made when the component exits the subscribed&#xa; state based on the current status and tuning.  The driver&#xa; should unregister for changes of cease polling.  Any IO should&#xa; be done asynchronously on another thread - never block the&#xa; calling thread.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.write(javax.baja.sys.Context) -->
<method name="write"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
This callback is made when a write is desired based on the&#xa; current status and tuning.  Any IO should be done asynchronously&#xa; on another thread - never block the calling thread.  If the write&#xa; is enqueued then return true and call writeOk() or writeFail()&#xa; once it has been processed.  If the write is canceled immediately&#xa; for other reasons then return false.&#xa; &lt;p&gt;&#xa; There are several cases of BACnet proxy points, which require different&#xa; handling of the write mechanism:&#xa; &lt;b&gt;non-priority properties&lt;/b&gt;: properties such as High_Limit are not&#xa; prioritized.  Niagara returns without writing if the active level is&#xa; fallback, or writes the value if there is a non-fallback level.&#xa; &lt;b&gt;priority array properties&lt;/b&gt;: points mapping slots in the priority array&#xa; will write null if the active level is fallback, or the value if non-fallback.&#xa; &lt;b&gt;present value of prioritized points&lt;/b&gt;: points mapping the present value&#xa; of priority-type points (analog output, analog values with prioritization, etc.)&#xa; will write at the priority of the active level, unless it is fallback, in&#xa; which case it will write nothing.  When the active level changes, the old&#xa; level is cleared, and the new level is written.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if a write is now pending
</description>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.device() -->
<method name="device"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
<description>
the BBacnetDevice containing this BBacnetProxyExt.
</description>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getPollableType() -->
<method name="getPollableType"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the pollable type of this object.
</description>
<return>
<type class="int"/>
<description>
one of the pollable types defined in BIBacnetPollable.
</description>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.poll() -->
<method name="poll"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Poll.
</description>
<tag name="@deprecated">As of 3.2</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getPollFrequency() -->
<method name="getPollFrequency"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the poll frequency of this point.
</description>
<return>
<type class="javax.baja.driver.util.BPollFrequency"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getPollListEntries() -->
<method name="getPollListEntries"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the list of poll list entries for this pollable.&#xa; The first entry for points must be the configured property.
</description>
<return>
<type class="javax.baja.bacnet.util.PollListEntry" dimension="1"/>
<description>
the list of poll list entries.
</description>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.points() -->
<method name="points"  public="true" final="true">
<description/>
<return>
<type class="javax.baja.bacnet.point.BBacnetPointDeviceExt"/>
<description>
the BBacnetDevice containing this BBacnetDevice.
</description>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.useCov() -->
<method name="useCov"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.useConfirmedCov() -->
<method name="useConfirmedCov"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getCovSubscriptionLifetime() -->
<method name="getCovSubscriptionLifetime"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.useCovProperty() -->
<method name="useCovProperty"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.useConfirmedCovProperty() -->
<method name="useConfirmedCovProperty"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getCovPropertySubscriptionLifetime() -->
<method name="getCovPropertySubscriptionLifetime"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getCovPropertyIncrement() -->
<method name="getCovPropertyIncrement"  public="true">
<description/>
<return>
<type class="javax.baja.sys.BDouble"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getAcceptUnsolicitedCov() -->
<method name="getAcceptUnsolicitedCov"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.tuningChanged(javax.baja.bacnet.point.BBacnetTuningPolicy, javax.baja.sys.Context) -->
<method name="tuningChanged"  public="true">
<description/>
<parameter name="policy">
<type class="javax.baja.bacnet.point.BBacnetTuningPolicy"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
To String.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.toDebugString() -->
<method name="toDebugString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getSlotFacets(javax.baja.sys.Slot) -->
<method name="getSlotFacets"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
For objectId, get the facets from the device&#x27;s object type facets.
</description>
<parameter name="slot">
<type class="javax.baja.sys.Slot"/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.isCOV() -->
<method name="isCOV"  public="true" final="true">
<description>
Is this point subscribed for COV notifications?
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.isCOVPending() -->
<method name="isCOVPending"  public="true" final="true">
<description>
Is this proxyExt attempting to SubscribeCOV?
</description>
<return>
<type class="boolean"/>
<description>
true if pending first attempt or polled while waiting for retries to succeed.
</description>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.isCOVProperty() -->
<method name="isCOVProperty"  public="true" final="true">
<description>
Is this point subscribed for COV notifications?
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.isCOVPropertyPending() -->
<method name="isCOVPropertyPending"  public="true" final="true">
<description>
Is this proxyExt attempting to SubscribeCOVProperty?
</description>
<return>
<type class="boolean"/>
<description>
true if pending first attempt or polled while waiting for retries to succeed.
</description>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.isCOVPropertyFailed() -->
<method name="isCOVPropertyFailed"  public="true" final="true">
<description>
Is this proxyExt attempt to SubscribeCOVProperty failed?
</description>
<return>
<type class="boolean"/>
<description>
true if pending first attempt or polled while waiting for retries to succeed.
</description>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.isPolled() -->
<method name="isPolled"  public="true" final="true">
<description>
Is this proxyExt being polled?
</description>
<return>
<type class="boolean"/>
<description>
true if polled or polled while waiting for subscribeCov retries to succeed.
</description>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.setCOV(boolean) -->
<method name="setCOV"  public="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Set the COV flag.
</description>
<tag name="@deprecated">Use &lt;code&gt;<see ref="javax.baja.bacnet.point.BBacnetProxyExt#setSubState(int)">#setSubState(int)</see>&lt;/code&gt; instead</tag>
<parameter name="cov">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.setSubState(int) -->
<method name="setSubState"  public="true" final="true">
<description>
Set the COV state.
</description>
<parameter name="subState">
<type class="int"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getLastReadError() -->
<method name="getLastReadError"  public="true">
<description>
Get the last read error that occurred while reading this point.
</description>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.setLastReadError(javax.baja.bacnet.io.ErrorType) -->
<method name="setLastReadError"  public="true">
<description>
Set the last read error that occurred while reading this point.
</description>
<parameter name="e">
<type class="javax.baja.bacnet.io.ErrorType"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.useStatusFlags() -->
<method name="useStatusFlags"  public="true">
<description>
Should status flags be applied to this point?
</description>
<return>
<type class="boolean"/>
<description>
true if the BACnet standard specifies Status_Flags for this property.
</description>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.isPriorityArrayPoint() -->
<method name="isPriorityArrayPoint"  public="true">
<description>
Is this point referencing a priority-array entry?
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.isPrioritizedPresentValue() -->
<method name="isPrioritizedPresentValue"  public="true">
<description>
Is this point referencing the present-value of a priority-type point?
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getDataSize() -->
<method name="getDataSize"  public="true">
<description>
Get the expected size in bytes of the encoded data value for this point.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.setPrioritizedPresentValue(boolean) -->
<method name="setPrioritizedPresentValue"  public="true">
<description>
Set the prioritized present value flag.
</description>
<parameter name="prioritizedPV">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.fromEncodedValue(byte[], javax.baja.status.BStatus, javax.baja.sys.Context) -->
<method name="fromEncodedValue"  public="true" abstract="true">
<description/>
<parameter name="encodedValue">
<type class="byte" dimension="1"/>
</parameter>
<parameter name="status">
<type class="javax.baja.status.BStatus"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.toEncodedValue(javax.baja.status.BStatusValue) -->
<method name="toEncodedValue"  public="true" abstract="true">
<description/>
<parameter name="newValue">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<return>
<type class="byte" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.updateReadStatus(javax.baja.sys.Context) -->
<method name="updateReadStatus"  protected="true">
<description>
update read status on based on the context.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
PollListEntry.pointCx.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.readMetaData(byte[], javax.baja.sys.Context, javax.baja.status.BStatusValue) -->
<method name="readMetaData"  protected="true">
<description>
Read metadata from a secondary PollListEntry.
</description>
<parameter name="encodedValue">
<type class="byte" dimension="1"/>
<description/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
must be a PollListEntry.
</description>
</parameter>
<parameter name="dv">
<type class="javax.baja.status.BStatusValue"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.network() -->
<method name="network"  protected="true" final="true">
<description/>
<return>
<type class="javax.baja.bacnet.BBacnetNetwork"/>
<description>
the BBacnetNetwork containing this BBacnetDevice.
</description>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.getDeviceAddress() -->
<method name="getDeviceAddress"  protected="true">
<description>
Get the address of the containing device.
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.calculateResubscribeTime(boolean, int) -->
<method name="calculateResubscribeTime"  public="true" static="true">
<description/>
<parameter name="postFailed">
<type class="boolean"/>
</parameter>
<parameter name="subscriptionLifeTime">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.sys.BRelTime"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.discoverPrioritizedPresentValue() -->
<method name="discoverPrioritizedPresentValue"  public="true">
<description>
Set the prioritized present value flag.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.discoverPrioritizedPresentValue(boolean) -->
<method name="discoverPrioritizedPresentValue"  public="true">
<description>
Set the prioritized present value flag.
</description>
<parameter name="force">
<type class="boolean"/>
<description>
discovery of priPv if true,&#xa;              otherwise reset priPv from device facets if false
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Spy.
</description>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.deviceFacets -->
<field name="deviceFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;deviceFacets&lt;/code&gt; property.&#xa; Facets of device value being read and/or written.
</description>
<tag name="@see">#getDeviceFacets</tag>
<tag name="@see">#setDeviceFacets</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; the Bacnet Object_Identifier of the containing object.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.propertyId -->
<field name="propertyId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;propertyId&lt;/code&gt; property.&#xa; the Bacnet Property_Identifier of the referenced property.
</description>
<tag name="@see">#getPropertyId</tag>
<tag name="@see">#setPropertyId</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.propertyArrayIndex -->
<field name="propertyArrayIndex"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.&#xa; the property array index, if used.
</description>
<tag name="@see">#getPropertyArrayIndex</tag>
<tag name="@see">#setPropertyArrayIndex</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.dataType -->
<field name="dataType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;dataType&lt;/code&gt; property.&#xa; the Asn primitive application data type.
</description>
<tag name="@see">#getDataType</tag>
<tag name="@see">#setDataType</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.readStatus -->
<field name="readStatus"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;readStatus&lt;/code&gt; property.
</description>
<tag name="@see">#getReadStatus</tag>
<tag name="@see">#setReadStatus</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.writeStatus -->
<field name="writeStatus"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;writeStatus&lt;/code&gt; property.
</description>
<tag name="@see">#getWriteStatus</tag>
<tag name="@see">#setWriteStatus</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.forceRead -->
<field name="forceRead"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;forceRead&lt;/code&gt; action.
</description>
<tag name="@see">#forceRead()</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.forceWrite -->
<field name="forceWrite"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;forceWrite&lt;/code&gt; action.
</description>
<tag name="@see">#forceWrite()</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.subscribeCov -->
<field name="subscribeCov"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;subscribeCov&lt;/code&gt; action.
</description>
<tag name="@see">#subscribeCov()</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.subscribeCovProperty -->
<field name="subscribeCovProperty"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;subscribeCovProperty&lt;/code&gt; action.
</description>
<tag name="@see">#subscribeCovProperty()</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.ackAlarm -->
<field name="ackAlarm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;ackAlarm&lt;/code&gt; action.
</description>
<tag name="@see">#ackAlarm(BAlarmRecord parameter)</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.SUB_STATE_UNSUB -->
<field name="SUB_STATE_UNSUB"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.SUB_STATE_POLLED -->
<field name="SUB_STATE_POLLED"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.SUB_STATE_COV -->
<field name="SUB_STATE_COV"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.SUB_STATE_FIRST_COV_PENDING -->
<field name="SUB_STATE_FIRST_COV_PENDING"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.SUB_STATE_POLLED_PENDING -->
<field name="SUB_STATE_POLLED_PENDING"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.SUB_STATE_COV_PENDING -->
<field name="SUB_STATE_COV_PENDING"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.SUB_STATE_FIRST_COVP_PENDING -->
<field name="SUB_STATE_FIRST_COVP_PENDING"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.SUB_STATE_COVP -->
<field name="SUB_STATE_COVP"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.SUB_STATE_COVP_PENDING -->
<field name="SUB_STATE_COVP_PENDING"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.SUB_STATE_COVP_FAILED -->
<field name="SUB_STATE_COVP_FAILED"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.NO_VALUE -->
<field name="NO_VALUE"  public="true" static="true" final="true">
<type class="byte" dimension="1"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.READ_STATUS_FLAGS -->
<field name="READ_STATUS_FLAGS"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.READ_EVENT_STATE -->
<field name="READ_EVENT_STATE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.READ_PRIORITY_ARRAY -->
<field name="READ_PRIORITY_ARRAY"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.READ_RELIABILITY -->
<field name="READ_RELIABILITY"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.STATUS_FLAGS_STATUS_FACET -->
<field name="STATUS_FLAGS_STATUS_FACET"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.EVENT_STATE_STATUS_FACET -->
<field name="EVENT_STATE_STATUS_FACET"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.PRIORITY_ARRAY_STATUS_FACET -->
<field name="PRIORITY_ARRAY_STATUS_FACET"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.RELIABILITY_STATUS_FACET -->
<field name="RELIABILITY_STATUS_FACET"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.PRIORITIZED_PRESENT_VALUE -->
<field name="PRIORITIZED_PRESENT_VALUE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.covContext -->
<field name="covContext"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.dataSize -->
<field name="dataSize"  protected="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.point.BBacnetProxyExt.asnType -->
<field name="asnType"  protected="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
