<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord" name="BBacnetEventLogRecord" packageName="javax.baja.bacnetAws.datatypes" public="true" final="true">
<description>
BBacnetEventLogRecord represents the BacneteEventLogRecord sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">25 May 2010</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="timestamp" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
<description>
Slot for the &lt;code&gt;timestamp&lt;/code&gt; property.
</description>
<tag name="@see">#getTimestamp</tag>
<tag name="@see">#setTimestamp</tag>
</property>

<property name="logDatum" flags="">
<type class="javax.baja.sys.BSimple"/>
<description>
Slot for the &lt;code&gt;logDatum&lt;/code&gt; property.
</description>
<tag name="@see">#getLogDatum</tag>
<tag name="@see">#setLogDatum</tag>
</property>

<!-- javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord() -->
<constructor name="BBacnetEventLogRecord" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord.getTimestamp() -->
<method name="getTimestamp"  public="true">
<description>
Get the &lt;code&gt;timestamp&lt;/code&gt; property.
</description>
<tag name="@see">#timestamp</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord.setTimestamp(javax.baja.bacnet.datatypes.BBacnetDateTime) -->
<method name="setTimestamp"  public="true">
<description>
Set the &lt;code&gt;timestamp&lt;/code&gt; property.
</description>
<tag name="@see">#timestamp</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord.getLogDatum() -->
<method name="getLogDatum"  public="true">
<description>
Get the &lt;code&gt;logDatum&lt;/code&gt; property.
</description>
<tag name="@see">#logDatum</tag>
<return>
<type class="javax.baja.sys.BSimple"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord.setLogDatum(javax.baja.sys.BSimple) -->
<method name="setLogDatum"  public="true">
<description>
Set the &lt;code&gt;logDatum&lt;/code&gt; property.
</description>
<tag name="@see">#logDatum</tag>
<parameter name="v">
<type class="javax.baja.sys.BSimple"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord.initializeNiagaraRecord(javax.baja.history.BHistoryRecord, long) -->
<method name="initializeNiagaraRecord"  public="true">
<description/>
<parameter name="record">
<type class="javax.baja.history.BHistoryRecord"/>
</parameter>
<parameter name="seqNum">
<type class="long"/>
</parameter>
<return>
<type class="javax.baja.history.BHistoryRecord"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord.timestamp -->
<field name="timestamp"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;timestamp&lt;/code&gt; property.
</description>
<tag name="@see">#getTimestamp</tag>
<tag name="@see">#setTimestamp</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord.logDatum -->
<field name="logDatum"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;logDatum&lt;/code&gt; property.
</description>
<tag name="@see">#getLogDatum</tag>
<tag name="@see">#setLogDatum</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord.TIMESTAMP_TAG -->
<field name="TIMESTAMP_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord.LOG_DATUM_TAG -->
<field name="LOG_DATUM_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord.LOG_STATUS_TAG -->
<field name="LOG_STATUS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord.NOTIFICATION_TAG -->
<field name="NOTIFICATION_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetEventLogRecord.TIME_CHANGE_TAG -->
<field name="TIME_CHANGE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
