<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.comm.BAceIpcCommConfig" name="BAceIpcCommConfig" packageName="com.tridium.ace.comm" public="true">
<description>
BAceIpcCommConfig is commConfig implementation for ACE instance running in same platform os
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">1/15/2018</tag>
<extends>
<type class="com.tridium.ndriver.datatypes.BCommConfig"/>
</extends>
</class>
</bajadoc>
