<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarmOrion" runtimeProfile="rt" qualifiedName="javax.baja.alarmOrion.BOrionAlarmFacetValue" name="BOrionAlarmFacetValue" packageName="javax.baja.alarmOrion" public="true">
<description>
The representation of an alarm data facet value within the orion database.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">March 18, 2009</tag>
<extends>
<type class="com.tridium.orion.BOrionObject"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
<annotation><type class="com.tridium.orion.annotations.NiagaraOrionType"/>
</annotation>
<annotation><type class="com.tridium.orion.annotations.OrionProperties"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="com.tridium.orion.annotations.OrionProperty"/>
<elementValue name="name">
<annotationValue kind="expr">
<expression>&#x22;alarm&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="refType">
<annotationValue kind="expr">
<expression>&#x22;alarmOrion:OrionAlarmRecord&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="flags">
<annotationValue kind="expr">
<expression>8</expression>
</annotationValue>
</elementValue>
<elementValue name="facets">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Facet"/>
<elementValue name="name">
<annotationValue kind="expr">
<expression>&#x22;INDEXED&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;true&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Facet"/>
<elementValue name="name">
<annotationValue kind="expr">
<expression>&#x22;CLUSTERED&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;true&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Facet"/>
<elementValue name="name">
<annotationValue kind="expr">
<expression>&#x22;DESCENDING&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;true&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Facet"/>
<elementValue name="name">
<annotationValue kind="expr">
<expression>&#x22;ON_DELETE&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;BOnDelete.CASCADE&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="com.tridium.orion.annotations.OrionProperty"/>
<elementValue name="name">
<annotationValue kind="expr">
<expression>&#x22;facetName&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="refType">
<annotationValue kind="expr">
<expression>&#x22;alarmOrion:OrionAlarmFacetName&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="flags">
<annotationValue kind="expr">
<expression>8</expression>
</annotationValue>
</elementValue>
<elementValue name="facets">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Facet"/>
<elementValue name="name">
<annotationValue kind="expr">
<expression>&#x22;ON_DELETE&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;BOnDelete.CASCADE&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<property name="id" flags="rs">
<type class="int"/>
<description>
Slot for the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#getId</tag>
<tag name="@see">#setId</tag>
</property>

<property name="alarm" flags="s">
<type class="com.tridium.orion.BRef"/>
<description>
Slot for the &lt;code&gt;alarm&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarm</tag>
<tag name="@see">#setAlarm</tag>
</property>

<property name="facetName" flags="s">
<type class="com.tridium.orion.BRef"/>
<description>
Slot for the &lt;code&gt;facetName&lt;/code&gt; property.
</description>
<tag name="@see">#getFacetName</tag>
<tag name="@see">#setFacetName</tag>
</property>

<property name="value" flags="s">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#getValue</tag>
<tag name="@see">#setValue</tag>
</property>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue() -->
<constructor name="BOrionAlarmFacetValue" public="true">
<description/>
</constructor>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.getId() -->
<method name="getId"  public="true">
<description>
Get the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#id</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.setId(int) -->
<method name="setId"  public="true">
<description>
Set the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#id</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.getAlarm() -->
<method name="getAlarm"  public="true">
<description>
Get the &lt;code&gt;alarm&lt;/code&gt; property.
</description>
<tag name="@see">#alarm</tag>
<return>
<type class="com.tridium.orion.BRef"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.setAlarm(com.tridium.orion.BRef) -->
<method name="setAlarm"  public="true">
<description>
Set the &lt;code&gt;alarm&lt;/code&gt; property.
</description>
<tag name="@see">#alarm</tag>
<parameter name="v">
<type class="com.tridium.orion.BRef"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.resolveAlarm(com.tridium.orion.OrionSession) -->
<method name="resolveAlarm"  public="true">
<description>
Resolve the &lt;code&gt;alarm&lt;/code&gt; property.
</description>
<tag name="@see">#alarm</tag>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="javax.baja.alarmOrion.BOrionAlarmRecord"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.getFacetName() -->
<method name="getFacetName"  public="true">
<description>
Get the &lt;code&gt;facetName&lt;/code&gt; property.
</description>
<tag name="@see">#facetName</tag>
<return>
<type class="com.tridium.orion.BRef"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.setFacetName(com.tridium.orion.BRef) -->
<method name="setFacetName"  public="true">
<description>
Set the &lt;code&gt;facetName&lt;/code&gt; property.
</description>
<tag name="@see">#facetName</tag>
<parameter name="v">
<type class="com.tridium.orion.BRef"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.getValue() -->
<method name="getValue"  public="true">
<description>
Get the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#value</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.setValue(java.lang.String) -->
<method name="setValue"  public="true">
<description>
Set the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#value</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.resolveFacetName(com.tridium.orion.OrionSession) -->
<method name="resolveFacetName"  public="true">
<description/>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="javax.baja.alarmOrion.BOrionAlarmFacetName"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.make(java.lang.String, javax.baja.sys.BObject, javax.baja.alarmOrion.BOrionAlarmRecord, com.tridium.orion.OrionSession) -->
<method name="make"  public="true" static="true">
<description>
Create a BOrionAlarmData object from the provided parameters.
</description>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BObject"/>
</parameter>
<parameter name="alarm">
<type class="javax.baja.alarmOrion.BOrionAlarmRecord"/>
</parameter>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="javax.baja.alarmOrion.BOrionAlarmFacetValue"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.make(java.lang.String, java.lang.String, javax.baja.alarmOrion.BOrionAlarmRecord, com.tridium.orion.OrionSession) -->
<method name="make"  public="true" static="true">
<description>
Create a BOrionAlarmData object from the provided parameters.
</description>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<parameter name="value">
<type class="java.lang.String"/>
<description>
Must be encoded BObject as returned by DataUtil.marshal
</description>
</parameter>
<parameter name="alarm">
<type class="javax.baja.alarmOrion.BOrionAlarmRecord"/>
</parameter>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="javax.baja.alarmOrion.BOrionAlarmFacetValue"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.id -->
<field name="id"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#getId</tag>
<tag name="@see">#setId</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.alarm -->
<field name="alarm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarm&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarm</tag>
<tag name="@see">#setAlarm</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.facetName -->
<field name="facetName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;facetName&lt;/code&gt; property.
</description>
<tag name="@see">#getFacetName</tag>
<tag name="@see">#setFacetName</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.value -->
<field name="value"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#getValue</tag>
<tag name="@see">#setValue</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetValue.ORION_TYPE -->
<field name="ORION_TYPE"  public="true" static="true" final="true">
<type class="com.tridium.orion.OrionType"/>
<description/>
</field>

</class>
</bajadoc>
