<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.util.worker.BBacnetWorkerPool" name="BBacnetWorkerPool" packageName="javax.baja.bacnet.util.worker" public="true">
<description>
BBacnetWorkerPool is a thin wrapper around&#xa; a ThreadPoolWorker, that can be added as a child&#xa; to any IWorkerPoolAware BComponent (e.g. BBacnetWorker).&#xa; &lt;p&gt;&#xa; The WorkerPool adds new work to the queues in a&#xa; round-robin fashion.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">26 Aug 2013</tag>
<tag name="@since">Niagara 3.8 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.util.worker.IWorkerPool"/>
</implements>
<property name="maxThreads" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;maxThreads&lt;/code&gt; property.&#xa; Max number of concurrent threads for working.
</description>
<tag name="@see">#getMaxThreads</tag>
<tag name="@see">#setMaxThreads</tag>
</property>

<!-- javax.baja.bacnet.util.worker.BBacnetWorkerPool() -->
<constructor name="BBacnetWorkerPool" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.util.worker.BBacnetWorkerPool.getMaxThreads() -->
<method name="getMaxThreads"  public="true">
<description>
Get the &lt;code&gt;maxThreads&lt;/code&gt; property.&#xa; Max number of concurrent threads for working.
</description>
<tag name="@see">#maxThreads</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.util.worker.BBacnetWorkerPool.setMaxThreads(int) -->
<method name="setMaxThreads"  public="true">
<description>
Set the &lt;code&gt;maxThreads&lt;/code&gt; property.&#xa; Max number of concurrent threads for working.
</description>
<tag name="@see">#maxThreads</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.worker.BBacnetWorkerPool.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.util.worker.BBacnetWorkerPool.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<description/>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.util.worker.BBacnetWorkerPool.isLegalParent(javax.baja.sys.BComponent) -->
<method name="isLegalParent"  public="true" static="true">
<description/>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.util.worker.BBacnetWorkerPool.started() -->
<method name="started"  public="true">
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.util.worker.BBacnetWorkerPool.stopped() -->
<method name="stopped"  public="true">
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.util.worker.BBacnetWorkerPool.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.worker.BBacnetWorkerPool.post(java.lang.Runnable) -->
<method name="post"  public="true">
<description>
Post an action to be run asynchronously.
</description>
<parameter name="r">
<type class="java.lang.Runnable"/>
</parameter>
<return>
<type class="javax.baja.util.IFuture"/>
</return>
</method>

<!-- javax.baja.bacnet.util.worker.BBacnetWorkerPool.getIcon() -->
<method name="getIcon"  public="true">
<description>
Get the icon.
</description>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.bacnet.util.worker.BBacnetWorkerPool.maxThreads -->
<field name="maxThreads"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;maxThreads&lt;/code&gt; property.&#xa; Max number of concurrent threads for working.
</description>
<tag name="@see">#getMaxThreads</tag>
<tag name="@see">#setMaxThreads</tag>
</field>

<!-- javax.baja.bacnet.util.worker.BBacnetWorkerPool.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
