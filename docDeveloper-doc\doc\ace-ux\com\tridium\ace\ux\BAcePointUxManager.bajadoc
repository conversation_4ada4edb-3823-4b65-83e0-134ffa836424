<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="ux" qualifiedName="com.tridium.ace.ux.BAcePointUxManager" name="BAcePointUxManager" packageName="com.tridium.ace.ux" public="true">
<description>
The AcePointUxManager is an agent on AcePointDeviceExt and AcePointFolder.
</description>
<tag name="@since">Niagara 4.8</tag>
<extends>
<type class="com.tridium.ndriver.ux.BNPointUxManager"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraSingleton"/>
</annotation>
<!-- com.tridium.ace.ux.BAcePointUxManager() -->
<constructor name="BAcePointUxManager" protected="true">
<description/>
</constructor>

<!-- com.tridium.ace.ux.BAcePointUxManager.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.ace.ux.BAcePointUxManager.getJsInfo(javax.baja.sys.Context) -->
<method name="getJsInfo"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.web.js.JsInfo"/>
</return>
</method>

<!-- com.tridium.ace.ux.BAcePointUxManager.hashCode() -->
<method name="hashCode"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridium.ace.ux.BAcePointUxManager.INSTANCE -->
<field name="INSTANCE"  public="true" static="true" final="true">
<type class="com.tridium.ace.ux.BAcePointUxManager"/>
<description/>
</field>

<!-- com.tridium.ace.ux.BAcePointUxManager.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
