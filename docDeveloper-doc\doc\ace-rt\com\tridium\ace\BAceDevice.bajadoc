<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.BAceDevice" name="BAceDevice" packageName="com.tridium.ace" public="true">
<description>
BAceDevice models a single device
</description>
<tag name="@author"><PERSON> on 02-Sep-16</tag>
<extends>
<type class="com.tridium.ndriver.BNDevice"/>
</extends>
<implements>
<type class="com.tridium.ndriver.poll.BINPollable"/>
</implements>
<property name="pollFrequency" flags="">
<type class="javax.baja.driver.util.BPollFrequency"/>
<description>
Slot for the &lt;code&gt;pollFrequency&lt;/code&gt; property.
</description>
<tag name="@see">#getPollFrequency</tag>
<tag name="@see">#setPollFrequency</tag>
</property>

<property name="points" flags="">
<type class="com.tridium.ace.point.BAcePointDeviceExt"/>
<description>
Slot for the &lt;code&gt;points&lt;/code&gt; property.
</description>
<tag name="@see">#getPoints</tag>
<tag name="@see">#setPoints</tag>
</property>

<property name="address" flags="h">
<type class="com.tridium.ndriver.datatypes.BAddress"/>
<description>
Slot for the &lt;code&gt;address&lt;/code&gt; property.
</description>
<tag name="@see">#getAddress</tag>
<tag name="@see">#setAddress</tag>
</property>

<property name="writeWorker" flags="h">
<type class="com.tridium.ace.datatypes.BWriteWorker"/>
<description>
Slot for the &lt;code&gt;writeWorker&lt;/code&gt; property.
</description>
<tag name="@see">#getWriteWorker</tag>
<tag name="@see">#setWriteWorker</tag>
</property>

<action name="readFile" flags="h">
<parameter name="parameter">
<type class="javax.baja.sys.BString"/>
</parameter>
<return>
<type class="javax.baja.sys.BBlob"/>
</return>
<description>
Slot for the &lt;code&gt;readFile&lt;/code&gt; action.
</description>
<tag name="@see">#readFile(BString parameter)</tag>
</action>

<action name="writeFile" flags="h">
<parameter name="parameter">
<type class="com.tridium.ace.datatypes.BAceWriteFileParams"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;writeFile&lt;/code&gt; action.
</description>
<tag name="@see">#writeFile(BAceWriteFileParams parameter)</tag>
</action>

<action name="viewApp" flags="h">
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;viewApp&lt;/code&gt; action.
</description>
<tag name="@see">#viewApp()</tag>
</action>

<action name="restart" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;restart&lt;/code&gt; action.
</description>
<tag name="@see">#restart()</tag>
</action>

<action name="saveApp" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;saveApp&lt;/code&gt; action.
</description>
<tag name="@see">#saveApp()</tag>
</action>

<action name="saveAppJob" flags="h">
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;saveAppJob&lt;/code&gt; action.
</description>
<tag name="@see">#saveAppJob()</tag>
</action>

<action name="readProp" flags="h">
<parameter name="parameter">
<type class="com.tridium.ace.datatypes.BAceReadParams"/>
</parameter>
<return>
<type class="com.tridium.ace.datatypes.BAcePrimitive"/>
</return>
<description>
Slot for the &lt;code&gt;readProp&lt;/code&gt; action.
</description>
<tag name="@see">#readProp(BAceReadParams parameter)</tag>
</action>

</class>
</bajadoc>
