<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="se" qualifiedName="com.tridium.alarm.print.BLinePrinterRecipient" name="BLinePrinterRecipient" packageName="com.tridium.alarm.print" public="true">
<description>
BLinePrinterRecipient is an AlarmRecipient to allow the printer of alarms&#xa; on Line Printers attached to station running on platforms.&#xa;&#xa; The Recipient has the ability to print to local and remote Line Printers.&#xa;&#xa; Alerts may be generated if the printing of an Alarm fails, but the&#xa; Recipient will not printer alarms that it generates.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">19 Jan 05</tag>
<tag name="@version">$Revision: 21$ $Date: 7/14/11 4:28:26 PM EDT$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="javax.baja.alarm.BAlarmRecipient"/>
</extends>
<implements>
<type class="javax.baja.alarm.BIAlarmSource"/>
</implements>
<property name="printer" flags="">
<type class="javax.baja.sys.BDynamicEnum"/>
<description>
Slot for the &lt;code&gt;printer&lt;/code&gt; property.&#xa; Name of printer
</description>
<tag name="@see">#getPrinter</tag>
<tag name="@see">#setPrinter</tag>
</property>

<property name="language" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;language&lt;/code&gt; property.&#xa; ISO 639 language code as two lower-case letters.
</description>
<tag name="@see">#getLanguage</tag>
<tag name="@see">#setLanguage</tag>
</property>

<property name="printFormat" flags="">
<type class="javax.baja.util.BFormat"/>
<description>
Slot for the &lt;code&gt;printFormat&lt;/code&gt; property.
</description>
<tag name="@see">#getPrintFormat</tag>
<tag name="@see">#setPrintFormat</tag>
</property>

<property name="alertOnFailure" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;alertOnFailure&lt;/code&gt; property.&#xa; generate an alert if the printer fails to print the alarm.
</description>
<tag name="@see">#getAlertOnFailure</tag>
<tag name="@see">#setAlertOnFailure</tag>
</property>

<property name="alarmSourceInfo" flags="">
<type class="javax.baja.alarm.BAlarmSourceInfo"/>
<description>
Slot for the &lt;code&gt;alarmSourceInfo&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmSourceInfo</tag>
<tag name="@see">#setAlarmSourceInfo</tag>
</property>

<action name="ackAlarm" flags="h">
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
<description>
Slot for the &lt;code&gt;ackAlarm&lt;/code&gt; action.
</description>
<tag name="@see">#ackAlarm(BAlarmRecord parameter)</tag>
</action>

<action name="loadPrinters" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;loadPrinters&lt;/code&gt; action.&#xa; load the list of printers.
</description>
<tag name="@see">#loadPrinters()</tag>
</action>

<topic name="printersLoaded" flags="">
<eventType>
<type class="javax.baja.sys.BValue"/>
</eventType><description>
Slot for the &lt;code&gt;printersLoaded&lt;/code&gt; topic.&#xa; fire notification that the printer list has been loaded
</description>
<tag name="@see">#firePrintersLoaded</tag>
</topic>

</class>
</bajadoc>
