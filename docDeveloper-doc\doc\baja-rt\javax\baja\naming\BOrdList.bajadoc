<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.BOrdList" name="BOrdList" packageName="javax.baja.naming" public="true" final="true">
<description>
BOrdList stores zero or more BOrds.  Its serialization&#xa; format is ords separated by the newline character.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">16 Apr 03</tag>
<tag name="@version">$Revision: 9$ $Date: 1/10/11 9:43:33 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BSimple"/>
</extends>
<implements>
<parameterizedType class="java.lang.Iterable">
<args>
<type class="javax.baja.naming.BOrd"/>
</args>
</parameterizedType>
</implements>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
<!-- javax.baja.naming.BOrdList.make(javax.baja.naming.BOrd) -->
<method name="make"  public="true" static="true">
<description>
Make for a single ord.
</description>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrdList"/>
</return>
</method>

<!-- javax.baja.naming.BOrdList.make(javax.baja.naming.BOrd...) -->
<method name="make"  public="true" static="true" isVarargs="true">
<description>
Make for an array of ords.
</description>
<parameter name="ords">
<type class="javax.baja.naming.BOrd" dimension="1"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrdList"/>
</return>
</method>

<!-- javax.baja.naming.BOrdList.add(javax.baja.naming.BOrdList, javax.baja.naming.BOrd) -->
<method name="add"  public="true" static="true">
<description>
Make a new list by appending the specified &#xa; ord to the original list.
</description>
<parameter name="orig">
<type class="javax.baja.naming.BOrdList"/>
</parameter>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrdList"/>
</return>
</method>

<!-- javax.baja.naming.BOrdList.remove(javax.baja.naming.BOrdList, int) -->
<method name="remove"  public="true" static="true">
<description>
Make a new list by removing the ord at the specified index.
</description>
<parameter name="orig">
<type class="javax.baja.naming.BOrdList"/>
</parameter>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrdList"/>
</return>
</method>

<!-- javax.baja.naming.BOrdList.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Convenience for &lt;code&gt;DEFAULT.decodeFromString(string)&lt;/code&gt;
</description>
<parameter name="string">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrdList"/>
</return>
</method>

<!-- javax.baja.naming.BOrdList.get(int) -->
<method name="get"  public="true">
<description>
Get the ord at the specified index.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.naming.BOrdList.size() -->
<method name="size"  public="true">
<description>
Get the number of ords in this list.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.naming.BOrdList.isNull() -->
<method name="isNull"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is this instance null?
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.BOrdList.toArray() -->
<method name="toArray"  public="true">
<description>
Get the array of ords.
</description>
<return>
<type class="javax.baja.naming.BOrd" dimension="1"/>
</return>
</method>

<!-- javax.baja.naming.BOrdList.hashCode() -->
<method name="hashCode"  public="true">
<description>
Hash is based on hashes of individual ords.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.naming.BOrdList.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description>
Equality is based on equality of individual ords.
</description>
<parameter name="obj">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.BOrdList.encode(java.io.DataOutput) -->
<method name="encode"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
BOrd is encoded as using writeUTF().
</description>
<parameter name="out">
<type class="java.io.DataOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.naming.BOrdList.decode(java.io.DataInput) -->
<method name="decode"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
BOrd is decoded using readUTF().
</description>
<parameter name="in">
<type class="java.io.DataInput"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.naming.BOrdList.encodeToString() -->
<method name="encodeToString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Write the simple in text format.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.BOrdList.decodeFromString(java.lang.String) -->
<method name="decodeFromString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Read the simple from text format.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
</method>

<!-- javax.baja.naming.BOrdList.iterator() -->
<method name="iterator"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<parameterizedType class="java.util.Iterator">
<args>
<type class="javax.baja.naming.BOrd"/>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.naming.BOrdList.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.BOrdList.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.naming.BOrdList.NULL -->
<field name="NULL"  public="true" static="true" final="true">
<type class="javax.baja.naming.BOrdList"/>
<description>
Null is the empty list.
</description>
</field>

<!-- javax.baja.naming.BOrdList.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.naming.BOrdList"/>
<description>
The default is the empty list.
</description>
</field>

<!-- javax.baja.naming.BOrdList.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
