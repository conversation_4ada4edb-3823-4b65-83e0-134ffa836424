<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.collection.Tables" name="Tables" packageName="javax.baja.collection" public="true" final="true">
<description>
Utilities for working with a &lt;code&gt;<see ref="javax.baja.collection.BITable">BITable</see>&lt;/code&gt;. All methods will attempt to use the most efficient&#xa; interface for obtaining the result. However, users of these methods should be aware that the fallback&#xa; implementation of most methods will yield extremely poor performance. For example, unless a&#xa; table implements &lt;code&gt;<see ref="javax.baja.collection.BIRandomAccessTable">BIRandomAccessTable</see>&lt;/code&gt;, then any row-based indexing will require iterating&#xa; the cursor from the beginning.
</description>
<tag name="@author">&lt;a href=&#x22;mailto:<EMAIL>&#x22;&gt;Matthew Giannini&lt;/a&gt;</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- javax.baja.collection.Tables.hasRows(javax.baja.collection.BITable&lt;?&gt;) -->
<method name="hasRows"  public="true" static="true">
<description>
Return true if the table has at least one row.&#xa; &lt;p&gt;&#xa; If the table does not implement&#xa; &lt;code&gt;<see ref="javax.baja.collection.BIRandomAccessTable">BIRandomAccessTable</see>&lt;/code&gt;, then a cursor is used to determine if the table has rows or not.&#xa; Depending on the table implementation, some cursors are only usable once, so use this utility&#xa; with care.
</description>
<parameter name="table">
<parameterizedType class="javax.baja.collection.BITable">
<args>
<wildcardType class="?">
</wildcardType>
</args>
</parameterizedType>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.Tables.&lt;T extends javax.baja.sys.BIObject&gt;slurp(javax.baja.collection.BITable&lt;T&gt;) -->
<method name="slurp"  public="true" static="true">
<typeParameters>
<typeVariable class="T">
<bounds>
<type class="javax.baja.sys.BIObject"/>
</bounds>
</typeVariable>
</typeParameters>
<description>
Convert the given table to a &lt;code&gt;<see ref="javax.baja.collection.BIRandomAccessTable">BIRandomAccessTable</see>&lt;/code&gt; so that it can be queried by index.&#xa; This will typically result in the entire table being read into memory.
</description>
<parameter name="table">
<parameterizedType class="javax.baja.collection.BITable">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</parameter>
<return>
<parameterizedType class="javax.baja.collection.BIRandomAccessTable">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.collection.Tables.get(javax.baja.collection.BITable&lt;?&gt;, int, javax.baja.collection.Column) -->
<method name="get"  public="true" static="true">
<description>
Get the cell value at the given row for the given column.&#xa; &lt;p&gt;&#xa; In the best case this implementation takes &lt;i&gt;O(1)&lt;/i&gt; time. In the worst case it takes&#xa; &lt;i&gt;O(n)&lt;/i&gt; time, where n is the number of rows in the table.
</description>
<parameter name="table">
<parameterizedType class="javax.baja.collection.BITable">
<args>
<wildcardType class="?">
</wildcardType>
</args>
</parameterizedType>
</parameter>
<parameter name="row">
<type class="int"/>
</parameter>
<parameter name="col">
<type class="javax.baja.collection.Column"/>
</parameter>
<return>
<type class="javax.baja.sys.BIObject"/>
</return>
</method>

</class>
</bajadoc>
