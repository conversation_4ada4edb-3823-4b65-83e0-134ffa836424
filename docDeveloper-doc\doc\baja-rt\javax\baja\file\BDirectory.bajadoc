<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BDirectory" name="BDirectory" packageName="javax.baja.file" public="true">
<description>
BDirectory is the BIFile type used to represent directories &#xa; in file space implementations.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">24 Jan 03</tag>
<tag name="@version">$Revision: 11$ $Date: 3/28/05 9:22:55 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.file.BAbstractFile"/>
</extends>
<implements>
<type class="javax.baja.file.BIDirectory"/>
</implements>
<!-- javax.baja.file.BDirectory(javax.baja.file.BIFileStore, javax.baja.util.LexiconText) -->
<constructor name="BDirectory" public="true">
<parameter name="store">
<type class="javax.baja.file.BIFileStore"/>
</parameter>
<parameter name="lexText">
<type class="javax.baja.util.LexiconText"/>
</parameter>
<description>
Construct a file with the specified store and lexicon text.
</description>
</constructor>

<!-- javax.baja.file.BDirectory(javax.baja.file.BIFileStore) -->
<constructor name="BDirectory" public="true">
<parameter name="store">
<type class="javax.baja.file.BIFileStore"/>
</parameter>
<description>
Construct a file with the specified store.
</description>
</constructor>

<!-- javax.baja.file.BDirectory() -->
<constructor name="BDirectory" public="true">
<description>
Construct (must call setStore()).
</description>
</constructor>

<!-- javax.baja.file.BDirectory.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.file.BDirectory.listFiles() -->
<method name="listFiles"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getFileSpace().getChildren(this)&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.file.BIFile" dimension="1"/>
</return>
</method>

<!-- javax.baja.file.BDirectory.getMimeType() -->
<method name="getMimeType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Default returns &lt;code&gt;&#x22;application/x-baja-directory&#x22;&lt;/code&gt;
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.BDirectory.getLexiconText() -->
<method name="getLexiconText"  public="true">
<description>
Return the lexicon text used to get the display &#xa; name, null if one is not installed.
</description>
<return>
<type class="javax.baja.util.LexiconText"/>
</return>
</method>

<!-- javax.baja.file.BDirectory.setLexiconText(javax.baja.util.LexiconText) -->
<method name="setLexiconText"  public="true">
<description>
Install a lexicon text used to get the display name.
</description>
<parameter name="lexText">
<type class="javax.baja.util.LexiconText"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.file.BDirectory.getNavDisplayName(javax.baja.sys.Context) -->
<method name="getNavDisplayName"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the display text of the navigation node.  If&#xa; a LexiconText is installed then use it to get the&#xa; display name.  Otherwise default to use getNavName().
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.BDirectory.getNavDescription(javax.baja.sys.Context) -->
<method name="getNavDescription"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get a short description.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.BDirectory.getNavParent() -->
<method name="getNavParent"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get navigation parent.
</description>
<return>
<type class="javax.baja.nav.BINavNode"/>
</return>
</method>

<!-- javax.baja.file.BDirectory.hasNavChildren() -->
<method name="hasNavChildren"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return true.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BDirectory.getNavChild(java.lang.String) -->
<method name="getNavChild"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getFileSpace().getChild(this, navName)&lt;/code&gt;.
</description>
<parameter name="navName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.nav.BINavNode"/>
</return>
</method>

<!-- javax.baja.file.BDirectory.getNavChildren() -->
<method name="getNavChildren"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getFileSpace().getChildren(this)&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.nav.BINavNode" dimension="1"/>
</return>
</method>

<!-- javax.baja.file.BDirectory.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the icon ref.
</description>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.file.BDirectory.setIcon(javax.baja.sys.BIcon) -->
<method name="setIcon"  public="true">
<description>
Set the icon or pass null to use default.
</description>
<parameter name="icon">
<type class="javax.baja.sys.BIcon"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.file.BDirectory.getAgents(javax.baja.sys.Context) -->
<method name="getAgents"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.agent.AgentList"/>
</return>
</method>

<!-- javax.baja.file.BDirectory.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
