<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="javax.baja.bacnetAws.config.BBacnetStructuredView" name="BBacnetStructuredView" packageName="javax.baja.bacnetAws.config" public="true">
<description/>
<extends>
<type class="javax.baja.bacnet.BBacnetObject"/>
</extends>
<property name="nodeType" flags="">
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
<description>
Slot for the &lt;code&gt;nodeType&lt;/code&gt; property.
</description>
<tag name="@see">#getNodeType</tag>
<tag name="@see">#setNodeType</tag>
</property>

<property name="subordinateList" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
<description>
Slot for the &lt;code&gt;subordinateList&lt;/code&gt; property.
</description>
<tag name="@see">#getSubordinateList</tag>
<tag name="@see">#setSubordinateList</tag>
</property>

<!-- javax.baja.bacnetAws.config.BBacnetStructuredView() -->
<constructor name="BBacnetStructuredView" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnetAws.config.BBacnetStructuredView.getNodeType() -->
<method name="getNodeType"  public="true">
<description>
Get the &lt;code&gt;nodeType&lt;/code&gt; property.
</description>
<tag name="@see">#nodeType</tag>
<return>
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetStructuredView.setNodeType(javax.baja.bacnet.enums.BBacnetNodeType) -->
<method name="setNodeType"  public="true">
<description>
Set the &lt;code&gt;nodeType&lt;/code&gt; property.
</description>
<tag name="@see">#nodeType</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetStructuredView.getSubordinateList() -->
<method name="getSubordinateList"  public="true">
<description>
Get the &lt;code&gt;subordinateList&lt;/code&gt; property.
</description>
<tag name="@see">#subordinateList</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetStructuredView.setSubordinateList(javax.baja.bacnet.datatypes.BBacnetArray) -->
<method name="setSubordinateList"  public="true">
<description>
Set the &lt;code&gt;subordinateList&lt;/code&gt; property.
</description>
<tag name="@see">#subordinateList</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetStructuredView.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetStructuredView.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetStructuredView.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetStructuredView.nodeType -->
<field name="nodeType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;nodeType&lt;/code&gt; property.
</description>
<tag name="@see">#getNodeType</tag>
<tag name="@see">#setNodeType</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetStructuredView.subordinateList -->
<field name="subordinateList"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;subordinateList&lt;/code&gt; property.
</description>
<tag name="@see">#getSubordinateList</tag>
<tag name="@see">#setSubordinateList</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetStructuredView.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
