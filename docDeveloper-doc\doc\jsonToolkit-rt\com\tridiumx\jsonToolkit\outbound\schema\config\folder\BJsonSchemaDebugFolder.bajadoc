<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaDebugFolder" name="BJsonSchemaDebugFolder" packageName="com.tridiumx.jsonToolkit.outbound.schema.config.folder" public="true">
<description>
Contains metrics and historical debug tools
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.util.BFolder"/>
</extends>
<property name="schemaOutputHistoryDebug" flags="">
<type class="com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug"/>
<description>
Slot for the &lt;code&gt;schemaOutputHistoryDebug&lt;/code&gt; property.
</description>
<tag name="@see">#getSchemaOutputHistoryDebug</tag>
<tag name="@see">#setSchemaOutputHistoryDebug</tag>
</property>

<property name="metrics" flags="d">
<type class="com.tridiumx.jsonToolkit.outbound.schema.postprocess.BJsonSchemaMetrics"/>
<description>
Slot for the &lt;code&gt;metrics&lt;/code&gt; property.
</description>
<tag name="@see">#getMetrics</tag>
<tag name="@see">#setMetrics</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaDebugFolder() -->
<constructor name="BJsonSchemaDebugFolder" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaDebugFolder.getSchemaOutputHistoryDebug() -->
<method name="getSchemaOutputHistoryDebug"  public="true">
<description>
Get the &lt;code&gt;schemaOutputHistoryDebug&lt;/code&gt; property.
</description>
<tag name="@see">#schemaOutputHistoryDebug</tag>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaDebugFolder.setSchemaOutputHistoryDebug(com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug) -->
<method name="setSchemaOutputHistoryDebug"  public="true">
<description>
Set the &lt;code&gt;schemaOutputHistoryDebug&lt;/code&gt; property.
</description>
<tag name="@see">#schemaOutputHistoryDebug</tag>
<parameter name="v">
<type class="com.tridiumx.jsonToolkit.outbound.schema.postprocess.BSchemaHistoryDebug"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaDebugFolder.getMetrics() -->
<method name="getMetrics"  public="true">
<description>
Get the &lt;code&gt;metrics&lt;/code&gt; property.
</description>
<tag name="@see">#metrics</tag>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.postprocess.BJsonSchemaMetrics"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaDebugFolder.setMetrics(com.tridiumx.jsonToolkit.outbound.schema.postprocess.BJsonSchemaMetrics) -->
<method name="setMetrics"  public="true">
<description>
Set the &lt;code&gt;metrics&lt;/code&gt; property.
</description>
<tag name="@see">#metrics</tag>
<parameter name="v">
<type class="com.tridiumx.jsonToolkit.outbound.schema.postprocess.BJsonSchemaMetrics"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaDebugFolder.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaDebugFolder.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaDebugFolder.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaDebugFolder.schemaOutputHistoryDebug -->
<field name="schemaOutputHistoryDebug"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;schemaOutputHistoryDebug&lt;/code&gt; property.
</description>
<tag name="@see">#getSchemaOutputHistoryDebug</tag>
<tag name="@see">#setSchemaOutputHistoryDebug</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaDebugFolder.metrics -->
<field name="metrics"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;metrics&lt;/code&gt; property.
</description>
<tag name="@see">#getMetrics</tag>
<tag name="@see">#setMetrics</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaDebugFolder.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
