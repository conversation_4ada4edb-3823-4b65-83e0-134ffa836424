<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.identify.BInfinityPointId" name="BInfinityPointId" packageName="com.tridium.andoverInfinity.identify" public="true">
<description>
BInfinityPointId adds a &#x22;rawResponse&#x22; field to the point display pane.
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 22, 2007</tag>
<tag name="@version">$Revision$ $May 22, 2007 9:48:54 AM$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.identify.BDdfIdParams"/>
</extends>
<property name="rawResponse" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;rawResponse&lt;/code&gt; property.
</description>
<tag name="@see">#getRawResponse</tag>
<tag name="@see">#setRawResponse</tag>
</property>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointId() -->
<constructor name="BInfinityPointId" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointId.getRawResponse() -->
<method name="getRawResponse"  public="true">
<description>
Get the &lt;code&gt;rawResponse&lt;/code&gt; property.
</description>
<tag name="@see">#rawResponse</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointId.setRawResponse(java.lang.String) -->
<method name="setRawResponse"  public="true">
<description>
Set the &lt;code&gt;rawResponse&lt;/code&gt; property.
</description>
<tag name="@see">#rawResponse</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointId.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointId.getReadRequestType() -->
<method name="getReadRequestType"  public="true">
<description>
Infinity uses a &lt;code&gt;BInfinityReadPointRequest.TYPE&lt;/code&gt; to read points
</description>
<tag name="@see">com.tridium.devDriver.identify.BIDdfReadParams#getReadRequestType()</tag>
<return>
<type class="javax.baja.sys.Type"/>
<description>
&lt;code&gt;BInfinityReadPointRequest.TYPE&lt;/code&gt;
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointId.rawResponse -->
<field name="rawResponse"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;rawResponse&lt;/code&gt; property.
</description>
<tag name="@see">#getRawResponse</tag>
<tag name="@see">#setRawResponse</tag>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointId.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
