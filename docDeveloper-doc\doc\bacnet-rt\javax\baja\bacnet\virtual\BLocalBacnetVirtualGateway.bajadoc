<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway" name="BLocalBacnetVirtualGateway" packageName="javax.baja.bacnet.virtual" public="true">
<description/>
<extends>
<type class="javax.baja.bacnet.virtual.BBacnetVirtualGateway"/>
</extends>
<property name="pollRate" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;pollRate&lt;/code&gt; property.
</description>
<tag name="@see">#getPollRate</tag>
<tag name="@see">#setPollRate</tag>
</property>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway() -->
<constructor name="BLocalBacnetVirtualGateway" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway.getPollRate() -->
<method name="getPollRate"  public="true">
<description>
Get the &lt;code&gt;pollRate&lt;/code&gt; property.
</description>
<tag name="@see">#pollRate</tag>
<return>
<type class="javax.baja.sys.BRelTime"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway.setPollRate(javax.baja.sys.BRelTime) -->
<method name="setPollRate"  public="true">
<description>
Set the &lt;code&gt;pollRate&lt;/code&gt; property.
</description>
<tag name="@see">#pollRate</tag>
<parameter name="v">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway.started() -->
<method name="started"  public="true">
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway.makeBacnetVirtualObject(java.lang.String) -->
<method name="makeBacnetVirtualObject"  protected="true">
<description>
Used during addVirtualSlot.
</description>
<parameter name="virtualPathName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.virtual.BBacnetVirtualObject"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway.makeLocalBacnetVirtualObject(javax.baja.bacnet.export.BIBacnetExportObject) -->
<method name="makeLocalBacnetVirtualObject"  protected="true">
<description>
Used during loadObjects.
</description>
<parameter name="export">
<type class="javax.baja.bacnet.export.BIBacnetExportObject"/>
<description/>
</parameter>
<return>
<type class="javax.baja.bacnet.virtual.BLocalBacnetVirtualObject"/>
<description>
localBacnetVirtualObject
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway.makeBacnetVirtualProperty(int, javax.baja.sys.BValue, java.lang.String, boolean) -->
<method name="makeBacnetVirtualProperty"  protected="true">
<description/>
<parameter name="propertyId">
<type class="int"/>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="readFault">
<type class="java.lang.String"/>
</parameter>
<parameter name="useFacets">
<type class="boolean"/>
</parameter>
<return>
<type class="javax.baja.bacnet.virtual.BBacnetVirtualProperty"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway.addVirtualSlot(javax.baja.virtual.BVirtualComponent, java.lang.String) -->
<method name="addVirtualSlot"  protected="true">
<description>
This method is called by the default implementation of loadVirtualSlot()&#xa; and should be implemented by subclasses to add a new&#xa; instance of a virtual component (or a BValue instance to be a property&#xa; on a virtual component).  The value added should be based on the&#xa; given virtual path name and relative to the parent virtual component.&#xa; The contract is that the new slot added for the generated virtual&#xa; instance MUST be named by the escaped virtual path name&#xa; (ie. always use SlotPath.escape(virtualPathName) as the name of the slot added&#xa; to the parent).  This method should return the property for the slot added.&#xa; &lt;p&gt;&#xa; NOTE:  Due to the possibility of a partial loaded state supported by&#xa; virtuals, subclasses should also be aware of the subscription state.&#xa; This means that an addVirtualSlot() call for a new virtual slot&#xa; could occur while the parent is already subscribed.  So this could affect&#xa; how the new virtual slot should be handled (ie. added to a poll scheduler).&#xa; Subclasses should always be aware of this potential state.
</description>
<parameter name="parent">
<type class="javax.baja.virtual.BVirtualComponent"/>
</parameter>
<parameter name="virtualPathName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.Property"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway.loadObjects(javax.baja.virtual.BVirtualComponent) -->
<method name="loadObjects"  protected="true">
<description>
Load objects into the gateway by reading the device&#x27;s Object_List property.&#xa; Overrides the defined behavior in BBacnetVirtualGateway to retrieve the&#xa; BIBacnetExportObjects from the export table.
</description>
<parameter name="parent">
<type class="javax.baja.virtual.BVirtualComponent"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway.loadProperties(javax.baja.bacnet.virtual.BBacnetVirtualObject) -->
<method name="loadProperties"  protected="true">
<description>
Load properties for a BacnetVirtualComponent by reading all its properties.&#xa; This can be done with RPM(all), or by reading the list of possible properties.&#xa; Overrides the behavior defined in BBacnetVirtualGateway
</description>
<parameter name="object">
<type class="javax.baja.bacnet.virtual.BBacnetVirtualObject"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway.readArraySize(javax.baja.bacnet.virtual.BBacnetVirtualProperty) -->
<method name="readArraySize"  protected="true">
<description/>
<parameter name="vp">
<type class="javax.baja.bacnet.virtual.BBacnetVirtualProperty"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway.getLocalPoll() -->
<method name="getLocalPoll"  public="true">
<description/>
<return>
<type class="javax.baja.bacnet.virtual.LocalBacnetVirtualPoll"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway.pollRate -->
<field name="pollRate"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;pollRate&lt;/code&gt; property.
</description>
<tag name="@see">#getPollRate</tag>
<tag name="@see">#setPollRate</tag>
</field>

<!-- javax.baja.bacnet.virtual.BLocalBacnetVirtualGateway.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
