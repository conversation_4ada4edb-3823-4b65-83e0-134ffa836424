<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="wb" qualifiedName="com.tridium.bacnet.ui.fe.BBacnetBinaryPvFE" name="BBacnetBinaryPvFE" packageName="com.tridium.bacnet.ui.fe" public="true">
<description>
BBacnetBinaryPvFE allows viewing and editing of a BBacnetBinaryPv&#xa; using a text field.&#xa; It usese the default tags of active/inactive, unless the caller&#xa; provides facets containing true/false text.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">24 Jun 03</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.workbench.fieldeditor.BWbFieldEditor"/>
</extends>
</class>
</bajadoc>
