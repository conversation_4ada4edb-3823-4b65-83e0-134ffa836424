<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.CursorPosition" name="CursorPosition" packageName="com.tridium.andoverInfinity.comm" public="true">
<description>
Maintains cursor line and column position information for the BVt100Screen
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- com.tridium.andoverInfinity.comm.CursorPosition(int, int) -->
<constructor name="CursorPosition" public="true">
<parameter name="line">
<type class="int"/>
</parameter>
<parameter name="col">
<type class="int"/>
</parameter>
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.comm.CursorPosition.getCol() -->
<method name="getCol"  public="true">
<description/>
<return>
<type class="int"/>
<description>
the col
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.CursorPosition.setCol(int) -->
<method name="setCol"  public="true">
<description/>
<parameter name="col">
<type class="int"/>
<description>
the col to set
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.CursorPosition.getLine() -->
<method name="getLine"  public="true">
<description/>
<return>
<type class="int"/>
<description>
the line
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.CursorPosition.setLine(int) -->
<method name="setLine"  public="true">
<description/>
<parameter name="line">
<type class="int"/>
<description>
the line to set
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.CursorPosition.equals(java.lang.Object) -->
<method name="equals"  public="true" final="true">
<description>
Equality.
</description>
<parameter name="obj">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.CursorPosition.toString() -->
<method name="toString"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.CursorPosition.newCopy() -->
<method name="newCopy"  public="true">
<description/>
<return>
<type class="com.tridium.andoverInfinity.comm.CursorPosition"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.CursorPosition.hashCode() -->
<method name="hashCode"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

</class>
</bajadoc>
