<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.query.style.BKeyValuePairObject" name="BKeyValuePairObject" packageName="com.tridiumx.jsonToolkit.outbound.schema.query.style" public="true">
<description>
A query result writer which renders an object containing each&#xa; row as a key value pair using the first 2 columns. The first column&#xa; value is converted to a string.&#xa;&#xa; If the query returns more than 2 columns these values are discarded.&#xa;&#xa;  {&#xa;   &#x27;a1&#x27;: b1,&#xa;   &#x27;a2&#x27;: b2,&#xa;   &#x27;a3&#x27;: b3&#xa;  }
</description>
<tag name="@author"><PERSON></tag>
<tag name="@since">Niagara 4.11</tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.style.BQueryResultWriter"/>
</extends>
<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BKeyValuePairObject() -->
<constructor name="BKeyValuePairObject" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BKeyValuePairObject.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BKeyValuePairObject.minColumnsRequired() -->
<method name="minColumnsRequired"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BKeyValuePairObject.appendJson(com.tridium.json.JSONWriter, com.tridiumx.jsonToolkit.outbound.schema.query.QueryResultHolder) -->
<method name="appendJson"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="json">
<type class="com.tridium.json.JSONWriter"/>
</parameter>
<parameter name="queryResult">
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.QueryResultHolder"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BKeyValuePairObject.appendJson(com.tridium.json.JSONWriter, com.tridiumx.jsonToolkit.outbound.schema.query.QueryResultHolder, java.lang.String, java.lang.String) -->
<method name="appendJson"  protected="true">
<description/>
<parameter name="json">
<type class="com.tridium.json.JSONWriter"/>
</parameter>
<parameter name="queryResult">
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.QueryResultHolder"/>
</parameter>
<parameter name="col1Name">
<type class="java.lang.String"/>
</parameter>
<parameter name="col2Name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BKeyValuePairObject.previewText() -->
<method name="previewText"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BString"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BKeyValuePairObject.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
