<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.alarm.BBacnetAlarmDeviceExt" name="BBacnetAlarmDeviceExt" packageName="javax.baja.bacnet.alarm" public="true">
<description>
BBacnetAlarmDeviceExt.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">23 Jul 2004</tag>
<tag name="@since">Niagara 3 BACnet 1.0</tag>
<extends>
<type class="javax.baja.driver.alarm.BAlarmDeviceExt"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetAlarmConst"/>
</implements>
<property name="niagaraProcessId" flags="">
<type class="long"/>
<description>
Slot for the &lt;code&gt;niagaraProcessId&lt;/code&gt; property.
</description>
<tag name="@see">#getNiagaraProcessId</tag>
<tag name="@see">#setNiagaraProcessId</tag>
</property>

<!-- javax.baja.bacnet.alarm.BBacnetAlarmDeviceExt() -->
<constructor name="BBacnetAlarmDeviceExt" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.alarm.BBacnetAlarmDeviceExt.getNiagaraProcessId() -->
<method name="getNiagaraProcessId"  public="true">
<description>
Get the &lt;code&gt;niagaraProcessId&lt;/code&gt; property.
</description>
<tag name="@see">#niagaraProcessId</tag>
<return>
<type class="long"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetAlarmDeviceExt.setNiagaraProcessId(long) -->
<method name="setNiagaraProcessId"  public="true">
<description>
Set the &lt;code&gt;niagaraProcessId&lt;/code&gt; property.
</description>
<tag name="@see">#niagaraProcessId</tag>
<parameter name="v">
<type class="long"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetAlarmDeviceExt.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetAlarmDeviceExt.doAckAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="doAckAlarm"  public="true">
<description/>
<parameter name="ackRequest">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetAlarmDeviceExt.doRouteAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="doRouteAlarm"  public="true">
<description/>
<parameter name="record">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetAlarmDeviceExt.alarmClass -->
<field name="alarmClass"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmClass&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmClass</tag>
<tag name="@see">#setAlarmClass</tag>
</field>

<!-- javax.baja.bacnet.alarm.BBacnetAlarmDeviceExt.niagaraProcessId -->
<field name="niagaraProcessId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;niagaraProcessId&lt;/code&gt; property.
</description>
<tag name="@see">#getNiagaraProcessId</tag>
<tag name="@see">#setNiagaraProcessId</tag>
</field>

<!-- javax.baja.bacnet.alarm.BBacnetAlarmDeviceExt.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
