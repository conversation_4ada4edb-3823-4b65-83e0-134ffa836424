<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetPrescale" name="BBacnetPrescale" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BBacnetPrescale represents the Bacnet Prescale&#xa; sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">25 Jul 2006</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="multiplier" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;multiplier&lt;/code&gt; property.
</description>
<tag name="@see">#getMultiplier</tag>
<tag name="@see">#setMultiplier</tag>
</property>

<property name="moduloDivide" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;moduloDivide&lt;/code&gt; property.
</description>
<tag name="@see">#getModuloDivide</tag>
<tag name="@see">#setModuloDivide</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetPrescale() -->
<constructor name="BBacnetPrescale" public="true">
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPrescale(long, long) -->
<constructor name="BBacnetPrescale" public="true">
<parameter name="mult">
<type class="long"/>
<description/>
</parameter>
<parameter name="mod">
<type class="long"/>
<description/>
</parameter>
<description>
Fully specified constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetPrescale.getMultiplier() -->
<method name="getMultiplier"  public="true">
<description>
Get the &lt;code&gt;multiplier&lt;/code&gt; property.
</description>
<tag name="@see">#multiplier</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPrescale.setMultiplier(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setMultiplier"  public="true">
<description>
Set the &lt;code&gt;multiplier&lt;/code&gt; property.
</description>
<tag name="@see">#multiplier</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPrescale.getModuloDivide() -->
<method name="getModuloDivide"  public="true">
<description>
Get the &lt;code&gt;moduloDivide&lt;/code&gt; property.
</description>
<tag name="@see">#moduloDivide</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPrescale.setModuloDivide(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setModuloDivide"  public="true">
<description>
Set the &lt;code&gt;moduloDivide&lt;/code&gt; property.
</description>
<tag name="@see">#moduloDivide</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPrescale.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPrescale.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPrescale.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPrescale.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetPrescale.multiplier -->
<field name="multiplier"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;multiplier&lt;/code&gt; property.
</description>
<tag name="@see">#getMultiplier</tag>
<tag name="@see">#setMultiplier</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPrescale.moduloDivide -->
<field name="moduloDivide"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;moduloDivide&lt;/code&gt; property.
</description>
<tag name="@see">#getModuloDivide</tag>
<tag name="@see">#setModuloDivide</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPrescale.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPrescale.MULTIPLIER_TAG -->
<field name="MULTIPLIER_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetPrescale.MODULO_DIVIDE_TAG -->
<field name="MODULO_DIVIDE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
