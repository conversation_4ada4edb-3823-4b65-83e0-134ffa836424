<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BBacnetMultistateOutput" name="BBacnetMultistateOutput" packageName="javax.baja.bacnet.config" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">25 Jun 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.config.BBacnetMultistate"/>
</extends>
<property name="priorityArray" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
<description>
Slot for the &lt;code&gt;priorityArray&lt;/code&gt; property.
</description>
<tag name="@see">#getPriorityArray</tag>
<tag name="@see">#setPriorityArray</tag>
</property>

<property name="relinquishDefault" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;relinquishDefault&lt;/code&gt; property.
</description>
<tag name="@see">#getRelinquishDefault</tag>
<tag name="@see">#setRelinquishDefault</tag>
</property>

<!-- javax.baja.bacnet.config.BBacnetMultistateOutput() -->
<constructor name="BBacnetMultistateOutput" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetMultistateOutput.getPriorityArray() -->
<method name="getPriorityArray"  public="true">
<description>
Get the &lt;code&gt;priorityArray&lt;/code&gt; property.
</description>
<tag name="@see">#priorityArray</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetMultistateOutput.setPriorityArray(javax.baja.bacnet.datatypes.BBacnetArray) -->
<method name="setPriorityArray"  public="true">
<description>
Set the &lt;code&gt;priorityArray&lt;/code&gt; property.
</description>
<tag name="@see">#priorityArray</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetMultistateOutput.getRelinquishDefault() -->
<method name="getRelinquishDefault"  public="true">
<description>
Get the &lt;code&gt;relinquishDefault&lt;/code&gt; property.
</description>
<tag name="@see">#relinquishDefault</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetMultistateOutput.setRelinquishDefault(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setRelinquishDefault"  public="true">
<description>
Set the &lt;code&gt;relinquishDefault&lt;/code&gt; property.
</description>
<tag name="@see">#relinquishDefault</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetMultistateOutput.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetMultistateOutput.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetMultistateOutput.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetMultistateOutput.priorityArray -->
<field name="priorityArray"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;priorityArray&lt;/code&gt; property.
</description>
<tag name="@see">#getPriorityArray</tag>
<tag name="@see">#setPriorityArray</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetMultistateOutput.relinquishDefault -->
<field name="relinquishDefault"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;relinquishDefault&lt;/code&gt; property.
</description>
<tag name="@see">#getRelinquishDefault</tag>
<tag name="@see">#setRelinquishDefault</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetMultistateOutput.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
