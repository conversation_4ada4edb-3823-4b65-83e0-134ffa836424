<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BBacnetNotificationClass" name="BBacnetNotificationClass" packageName="javax.baja.bacnet.config" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">24 Jun 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.config.BBacnetCreatableObject"/>
</extends>
<property name="notificationClass" flags="r">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;notificationClass&lt;/code&gt; property.
</description>
<tag name="@see">#getNotificationClass</tag>
<tag name="@see">#setNotificationClass</tag>
</property>

<property name="priority" flags="r">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
<description>
Slot for the &lt;code&gt;priority&lt;/code&gt; property.
</description>
<tag name="@see">#getPriority</tag>
<tag name="@see">#setPriority</tag>
</property>

<property name="ackRequired" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
Slot for the &lt;code&gt;ackRequired&lt;/code&gt; property.
</description>
<tag name="@see">#getAckRequired</tag>
<tag name="@see">#setAckRequired</tag>
</property>

<property name="recipientList" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
<description>
Slot for the &lt;code&gt;recipientList&lt;/code&gt; property.
</description>
<tag name="@see">#getRecipientList</tag>
<tag name="@see">#setRecipientList</tag>
</property>

<action name="addDestination" flags="">
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetDestination"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;addDestination&lt;/code&gt; action.&#xa; add a destination to the notification class&#x27;s recipient list.
</description>
<tag name="@see">#addDestination(BBacnetDestination parameter)</tag>
</action>

<action name="removeDestination" flags="">
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetDestination"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;removeDestination&lt;/code&gt; action.&#xa; remove a destination from the notification class&#x27;s recipient list.
</description>
<tag name="@see">#removeDestination(BBacnetDestination parameter)</tag>
</action>

<action name="removeRecipient" flags="">
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetRecipient"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;removeRecipient&lt;/code&gt; action.&#xa; remove all destinations with this recipient&#xa; from the notification class&#x27;s recipient list.
</description>
<tag name="@see">#removeRecipient(BBacnetRecipient parameter)</tag>
</action>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass() -->
<constructor name="BBacnetNotificationClass" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.getNotificationClass() -->
<method name="getNotificationClass"  public="true">
<description>
Get the &lt;code&gt;notificationClass&lt;/code&gt; property.
</description>
<tag name="@see">#notificationClass</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.setNotificationClass(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setNotificationClass"  public="true">
<description>
Set the &lt;code&gt;notificationClass&lt;/code&gt; property.
</description>
<tag name="@see">#notificationClass</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.getPriority() -->
<method name="getPriority"  public="true">
<description>
Get the &lt;code&gt;priority&lt;/code&gt; property.
</description>
<tag name="@see">#priority</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.setPriority(javax.baja.bacnet.datatypes.BBacnetArray) -->
<method name="setPriority"  public="true">
<description>
Set the &lt;code&gt;priority&lt;/code&gt; property.
</description>
<tag name="@see">#priority</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.getAckRequired() -->
<method name="getAckRequired"  public="true">
<description>
Get the &lt;code&gt;ackRequired&lt;/code&gt; property.
</description>
<tag name="@see">#ackRequired</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.setAckRequired(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="setAckRequired"  public="true">
<description>
Set the &lt;code&gt;ackRequired&lt;/code&gt; property.
</description>
<tag name="@see">#ackRequired</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.getRecipientList() -->
<method name="getRecipientList"  public="true">
<description>
Get the &lt;code&gt;recipientList&lt;/code&gt; property.
</description>
<tag name="@see">#recipientList</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.setRecipientList(javax.baja.bacnet.datatypes.BBacnetListOf) -->
<method name="setRecipientList"  public="true">
<description>
Set the &lt;code&gt;recipientList&lt;/code&gt; property.
</description>
<tag name="@see">#recipientList</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.addDestination(javax.baja.bacnet.datatypes.BBacnetDestination) -->
<method name="addDestination"  public="true">
<description>
Invoke the &lt;code&gt;addDestination&lt;/code&gt; action.&#xa; add a destination to the notification class&#x27;s recipient list.
</description>
<tag name="@see">#addDestination</tag>
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetDestination"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.removeDestination(javax.baja.bacnet.datatypes.BBacnetDestination) -->
<method name="removeDestination"  public="true">
<description>
Invoke the &lt;code&gt;removeDestination&lt;/code&gt; action.&#xa; remove a destination from the notification class&#x27;s recipient list.
</description>
<tag name="@see">#removeDestination</tag>
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetDestination"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.removeRecipient(javax.baja.bacnet.datatypes.BBacnetRecipient) -->
<method name="removeRecipient"  public="true">
<description>
Invoke the &lt;code&gt;removeRecipient&lt;/code&gt; action.&#xa; remove all destinations with this recipient&#xa; from the notification class&#x27;s recipient list.
</description>
<tag name="@see">#removeRecipient</tag>
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetRecipient"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.doAddDestination(javax.baja.bacnet.datatypes.BBacnetDestination) -->
<method name="doAddDestination"  public="true">
<description>
Add a destination to the recipient list.
</description>
<parameter name="dest">
<type class="javax.baja.bacnet.datatypes.BBacnetDestination"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.doRemoveDestination(javax.baja.bacnet.datatypes.BBacnetDestination) -->
<method name="doRemoveDestination"  public="true">
<description>
Remove a destination from the recipient list.
</description>
<parameter name="dest">
<type class="javax.baja.bacnet.datatypes.BBacnetDestination"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.doRemoveRecipient(javax.baja.bacnet.datatypes.BBacnetRecipient) -->
<method name="doRemoveRecipient"  public="true">
<description>
Remove all destinations for a particular recipient from the recipient list.
</description>
<parameter name="recip">
<type class="javax.baja.bacnet.datatypes.BBacnetRecipient"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.addObjectInitialValues(javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addObjectInitialValues"  protected="true">
<description/>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.notificationClass -->
<field name="notificationClass"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;notificationClass&lt;/code&gt; property.
</description>
<tag name="@see">#getNotificationClass</tag>
<tag name="@see">#setNotificationClass</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.priority -->
<field name="priority"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;priority&lt;/code&gt; property.
</description>
<tag name="@see">#getPriority</tag>
<tag name="@see">#setPriority</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.ackRequired -->
<field name="ackRequired"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;ackRequired&lt;/code&gt; property.
</description>
<tag name="@see">#getAckRequired</tag>
<tag name="@see">#setAckRequired</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.recipientList -->
<field name="recipientList"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;recipientList&lt;/code&gt; property.
</description>
<tag name="@see">#getRecipientList</tag>
<tag name="@see">#setRecipientList</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.addDestination -->
<field name="addDestination"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;addDestination&lt;/code&gt; action.&#xa; add a destination to the notification class&#x27;s recipient list.
</description>
<tag name="@see">#addDestination(BBacnetDestination parameter)</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.removeDestination -->
<field name="removeDestination"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;removeDestination&lt;/code&gt; action.&#xa; remove a destination from the notification class&#x27;s recipient list.
</description>
<tag name="@see">#removeDestination(BBacnetDestination parameter)</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.removeRecipient -->
<field name="removeRecipient"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;removeRecipient&lt;/code&gt; action.&#xa; remove all destinations with this recipient&#xa; from the notification class&#x27;s recipient list.
</description>
<tag name="@see">#removeRecipient(BBacnetRecipient parameter)</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetNotificationClass.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
