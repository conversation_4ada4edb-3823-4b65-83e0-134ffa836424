<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.io.ChangeListError" name="ChangeListError" packageName="javax.baja.bacnet.io" public="true" interface="true" abstract="true" category="interface">
<description>
The ChangeListError sequence is returned in response to&#xa; AddListElement and RemoveListElement requests when the&#xa; request cannot be executed.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">04 Sep 03</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<!-- javax.baja.bacnet.io.ChangeListError.getFirstFailedElementNumber() -->
<method name="getFirstFailedElementNumber"  public="true" abstract="true">
<description>
Get the first element number that failed to be&#xa; added.
</description>
<return>
<type class="long"/>
<description>
the firstFailedElementNumber.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.ChangeListError.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true" abstract="true">
<description>
Encode the ChangeList-Error to Asn.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.ChangeListError.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true" abstract="true">
<description>
Decode the ChangeList-Error from Asn.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.ChangeListError.ERROR_TYPE_TAG -->
<field name="ERROR_TYPE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.io.ChangeListError.FIRST_FAILED_ELEMENT_NUMBER_TAG -->
<field name="FIRST_FAILED_ELEMENT_NUMBER_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
