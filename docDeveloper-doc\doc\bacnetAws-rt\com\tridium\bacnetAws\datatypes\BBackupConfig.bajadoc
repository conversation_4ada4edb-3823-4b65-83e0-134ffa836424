<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="com.tridium.bacnetAws.datatypes.BBackupConfig" name="BBackupConfig" packageName="com.tridium.bacnetAws.datatypes" public="true">
<description>
BBackupConfig represents the choices for the&#xa; user in the Backup procedure.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Jun 2006</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.datatypes.BRequestConfig"/>
</extends>
<property name="baseDirectory" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;baseDirectory&lt;/code&gt; property.
</description>
<tag name="@see">#getBaseDirectory</tag>
<tag name="@see">#setBaseDirectory</tag>
</property>

<property name="deviceDirectoryName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;deviceDirectoryName&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceDirectoryName</tag>
<tag name="@see">#setDeviceDirectoryName</tag>
</property>

<property name="deviceAddress" flags="h">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
Slot for the &lt;code&gt;deviceAddress&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceAddress</tag>
<tag name="@see">#setDeviceAddress</tag>
</property>

<property name="deviceId" flags="h">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;deviceId&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceId</tag>
<tag name="@see">#setDeviceId</tag>
</property>

<property name="characterSet" flags="h">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
<description>
Slot for the &lt;code&gt;characterSet&lt;/code&gt; property.
</description>
<tag name="@see">#getCharacterSet</tag>
<tag name="@see">#setCharacterSet</tag>
</property>

<property name="password" flags="">
<type class="javax.baja.security.BPassword"/>
<description>
Slot for the &lt;code&gt;password&lt;/code&gt; property.
</description>
<tag name="@see">#getPassword</tag>
<tag name="@see">#setPassword</tag>
</property>

</class>
</bajadoc>
