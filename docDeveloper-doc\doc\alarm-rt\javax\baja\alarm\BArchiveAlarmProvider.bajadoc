<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BArchiveAlarmProvider" name="BArchiveAlarmProvider" packageName="javax.baja.alarm" public="true" abstract="true">
<description>
BArchiveAlarmProvider is added to the BAlarmService and provides mechanism to&#xa; archive cleared alarms. It allows the main alarm database to store only open&#xa; alarms (that are displayed in the Alarm Consoles). Once cleared (transitioned&#xa; to normal or acknowledged), alarms are archived to a secondary database for&#xa; historical storage.&#xa;&#xa; Cleared alarms are periodically archived via the execute action which is&#xa; invoked by the executionTime trigger.&#xa;&#xa; Subclasses will need to supply a BAlarmArchive and ensure that postExecute&#xa; places the archive work on a non-control engine thread.&#xa;&#xa; The subclass needs to implement &lt;code&gt;exportClearedRecords(Cursor&amp;lt;BAlarmRecord&amp;gt; recordsToExport)&lt;/code&gt;&#xa; and &lt;code&gt;postExecute()&lt;/code&gt; methods.
</description>
<tag name="@author">Ashutosh Chaturvedi on 7/13/2020</tag>
<tag name="@since">Niagara 4.11</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.util.BIRestrictedComponent"/>
</implements>
<implements>
<type class="javax.baja.status.BIStatus"/>
</implements>
<implements>
<type class="javax.baja.alarm.BIAlarmSource"/>
</implements>
<property name="status" flags="rt">
<type class="javax.baja.status.BStatus"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.&#xa; The status of this alarm archival provider.
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</property>

<property name="enabled" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;enabled&lt;/code&gt; property.&#xa; The enabled state of this alarm archive provider.  If false, this alarm&#xa; archive provider won&#x27;t be used to store cleared alarms.  If true,&#xa; this alarm archive provider will be used to archive alarms once they&#xa; are cleared.
</description>
<tag name="@see">#getEnabled</tag>
<tag name="@see">#setEnabled</tag>
</property>

<property name="faultCause" flags="rt">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.&#xa; This is a text message describing the reason for the fault.
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</property>

<property name="state" flags="rt">
<type class="javax.baja.util.BExecutionState"/>
<description>
Slot for the &lt;code&gt;state&lt;/code&gt; property.&#xa; The current state of the work that is performed by this descriptor.
</description>
<tag name="@see">#getState</tag>
<tag name="@see">#setState</tag>
</property>

<property name="executionTime" flags="">
<type class="javax.baja.control.trigger.BTimeTrigger"/>
<description>
Slot for the &lt;code&gt;executionTime&lt;/code&gt; property.&#xa; The execution time controls when the execute action will be invoked.
</description>
<tag name="@see">#getExecutionTime</tag>
<tag name="@see">#setExecutionTime</tag>
</property>

<property name="clearedAlarmLingerTime" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;clearedAlarmLingerTime&lt;/code&gt; property.&#xa; The minimum time that an alarm should remain in the cleared state before&#xa; it can be archived.
</description>
<tag name="@see">#getClearedAlarmLingerTime</tag>
<tag name="@see">#setClearedAlarmLingerTime</tag>
</property>

<property name="lastAttempt" flags="rt">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;lastAttempt&lt;/code&gt; property.&#xa; The last time that the alarm archival&#x27;s work was started.
</description>
<tag name="@see">#getLastAttempt</tag>
<tag name="@see">#setLastAttempt</tag>
</property>

<property name="lastSuccess" flags="rt">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;lastSuccess&lt;/code&gt; property.&#xa; The last time that the alarm archival&#x27;s work completed successfully.
</description>
<tag name="@see">#getLastSuccess</tag>
<tag name="@see">#setLastSuccess</tag>
</property>

<property name="lastFailure" flags="rt">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;lastFailure&lt;/code&gt; property.&#xa; The last time that the alarm archival&#x27;s work failed to complete successfully.
</description>
<tag name="@see">#getLastFailure</tag>
<tag name="@see">#setLastFailure</tag>
</property>

<property name="retryTrigger" flags="">
<type class="javax.baja.control.trigger.BTimeTrigger"/>
<description>
Slot for the &lt;code&gt;retryTrigger&lt;/code&gt; property.&#xa; The retryTrigger controls when retries of failed alarm archival&#xa; executions will be reattempted.
</description>
<tag name="@see">#getRetryTrigger</tag>
<tag name="@see">#setRetryTrigger</tag>
</property>

<property name="alarmOnFailure" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;alarmOnFailure&lt;/code&gt; property.&#xa; Determines whether an alarm should be generated when a failure occurs for&#xa; an execution of this export.
</description>
<tag name="@see">#getAlarmOnFailure</tag>
<tag name="@see">#setAlarmOnFailure</tag>
</property>

<property name="alarmSourceInfo" flags="">
<type class="javax.baja.alarm.BAlarmSourceInfo"/>
<description>
Slot for the &lt;code&gt;alarmSourceInfo&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmSourceInfo</tag>
<tag name="@see">#setAlarmSourceInfo</tag>
</property>

<action name="execute" flags="a">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;execute&lt;/code&gt; action.&#xa; Execute the work that the alarm archival is responsible for.
</description>
<tag name="@see">#execute()</tag>
</action>

<action name="retry" flags="s">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;retry&lt;/code&gt; action.&#xa; The action invoked by the retryTrigger to retry any failed alarm archival&#xa; executions.
</description>
<tag name="@see">#retry()</tag>
</action>

<action name="ackAlarm" flags="h">
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
<description>
Slot for the &lt;code&gt;ackAlarm&lt;/code&gt; action.&#xa; Hidden action to support alarm acknowledgements
</description>
<tag name="@see">#ackAlarm(BAlarmRecord parameter)</tag>
</action>

<!-- javax.baja.alarm.BArchiveAlarmProvider() -->
<constructor name="BArchiveAlarmProvider" public="true">
<description/>
</constructor>

<!-- javax.baja.alarm.BArchiveAlarmProvider.getStatus() -->
<method name="getStatus"  public="true">
<description>
Get the &lt;code&gt;status&lt;/code&gt; property.&#xa; The status of this alarm archival provider.
</description>
<tag name="@see">#status</tag>
<return>
<type class="javax.baja.status.BStatus"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.setStatus(javax.baja.status.BStatus) -->
<method name="setStatus"  public="true">
<description>
Set the &lt;code&gt;status&lt;/code&gt; property.&#xa; The status of this alarm archival provider.
</description>
<tag name="@see">#status</tag>
<parameter name="v">
<type class="javax.baja.status.BStatus"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.getEnabled() -->
<method name="getEnabled"  public="true">
<description>
Get the &lt;code&gt;enabled&lt;/code&gt; property.&#xa; The enabled state of this alarm archive provider.  If false, this alarm&#xa; archive provider won&#x27;t be used to store cleared alarms.  If true,&#xa; this alarm archive provider will be used to archive alarms once they&#xa; are cleared.
</description>
<tag name="@see">#enabled</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.setEnabled(boolean) -->
<method name="setEnabled"  public="true">
<description>
Set the &lt;code&gt;enabled&lt;/code&gt; property.&#xa; The enabled state of this alarm archive provider.  If false, this alarm&#xa; archive provider won&#x27;t be used to store cleared alarms.  If true,&#xa; this alarm archive provider will be used to archive alarms once they&#xa; are cleared.
</description>
<tag name="@see">#enabled</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.getFaultCause() -->
<method name="getFaultCause"  public="true">
<description>
Get the &lt;code&gt;faultCause&lt;/code&gt; property.&#xa; This is a text message describing the reason for the fault.
</description>
<tag name="@see">#faultCause</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.setFaultCause(java.lang.String) -->
<method name="setFaultCause"  public="true">
<description>
Set the &lt;code&gt;faultCause&lt;/code&gt; property.&#xa; This is a text message describing the reason for the fault.
</description>
<tag name="@see">#faultCause</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.getState() -->
<method name="getState"  public="true">
<description>
Get the &lt;code&gt;state&lt;/code&gt; property.&#xa; The current state of the work that is performed by this descriptor.
</description>
<tag name="@see">#state</tag>
<return>
<type class="javax.baja.util.BExecutionState"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.setState(javax.baja.util.BExecutionState) -->
<method name="setState"  public="true">
<description>
Set the &lt;code&gt;state&lt;/code&gt; property.&#xa; The current state of the work that is performed by this descriptor.
</description>
<tag name="@see">#state</tag>
<parameter name="v">
<type class="javax.baja.util.BExecutionState"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.getExecutionTime() -->
<method name="getExecutionTime"  public="true">
<description>
Get the &lt;code&gt;executionTime&lt;/code&gt; property.&#xa; The execution time controls when the execute action will be invoked.
</description>
<tag name="@see">#executionTime</tag>
<return>
<type class="javax.baja.control.trigger.BTimeTrigger"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.setExecutionTime(javax.baja.control.trigger.BTimeTrigger) -->
<method name="setExecutionTime"  public="true">
<description>
Set the &lt;code&gt;executionTime&lt;/code&gt; property.&#xa; The execution time controls when the execute action will be invoked.
</description>
<tag name="@see">#executionTime</tag>
<parameter name="v">
<type class="javax.baja.control.trigger.BTimeTrigger"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.getClearedAlarmLingerTime() -->
<method name="getClearedAlarmLingerTime"  public="true">
<description>
Get the &lt;code&gt;clearedAlarmLingerTime&lt;/code&gt; property.&#xa; The minimum time that an alarm should remain in the cleared state before&#xa; it can be archived.
</description>
<tag name="@see">#clearedAlarmLingerTime</tag>
<return>
<type class="javax.baja.sys.BRelTime"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.setClearedAlarmLingerTime(javax.baja.sys.BRelTime) -->
<method name="setClearedAlarmLingerTime"  public="true">
<description>
Set the &lt;code&gt;clearedAlarmLingerTime&lt;/code&gt; property.&#xa; The minimum time that an alarm should remain in the cleared state before&#xa; it can be archived.
</description>
<tag name="@see">#clearedAlarmLingerTime</tag>
<parameter name="v">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.getLastAttempt() -->
<method name="getLastAttempt"  public="true">
<description>
Get the &lt;code&gt;lastAttempt&lt;/code&gt; property.&#xa; The last time that the alarm archival&#x27;s work was started.
</description>
<tag name="@see">#lastAttempt</tag>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.setLastAttempt(javax.baja.sys.BAbsTime) -->
<method name="setLastAttempt"  public="true">
<description>
Set the &lt;code&gt;lastAttempt&lt;/code&gt; property.&#xa; The last time that the alarm archival&#x27;s work was started.
</description>
<tag name="@see">#lastAttempt</tag>
<parameter name="v">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.getLastSuccess() -->
<method name="getLastSuccess"  public="true">
<description>
Get the &lt;code&gt;lastSuccess&lt;/code&gt; property.&#xa; The last time that the alarm archival&#x27;s work completed successfully.
</description>
<tag name="@see">#lastSuccess</tag>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.setLastSuccess(javax.baja.sys.BAbsTime) -->
<method name="setLastSuccess"  public="true">
<description>
Set the &lt;code&gt;lastSuccess&lt;/code&gt; property.&#xa; The last time that the alarm archival&#x27;s work completed successfully.
</description>
<tag name="@see">#lastSuccess</tag>
<parameter name="v">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.getLastFailure() -->
<method name="getLastFailure"  public="true">
<description>
Get the &lt;code&gt;lastFailure&lt;/code&gt; property.&#xa; The last time that the alarm archival&#x27;s work failed to complete successfully.
</description>
<tag name="@see">#lastFailure</tag>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.setLastFailure(javax.baja.sys.BAbsTime) -->
<method name="setLastFailure"  public="true">
<description>
Set the &lt;code&gt;lastFailure&lt;/code&gt; property.&#xa; The last time that the alarm archival&#x27;s work failed to complete successfully.
</description>
<tag name="@see">#lastFailure</tag>
<parameter name="v">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.getRetryTrigger() -->
<method name="getRetryTrigger"  public="true">
<description>
Get the &lt;code&gt;retryTrigger&lt;/code&gt; property.&#xa; The retryTrigger controls when retries of failed alarm archival&#xa; executions will be reattempted.
</description>
<tag name="@see">#retryTrigger</tag>
<return>
<type class="javax.baja.control.trigger.BTimeTrigger"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.setRetryTrigger(javax.baja.control.trigger.BTimeTrigger) -->
<method name="setRetryTrigger"  public="true">
<description>
Set the &lt;code&gt;retryTrigger&lt;/code&gt; property.&#xa; The retryTrigger controls when retries of failed alarm archival&#xa; executions will be reattempted.
</description>
<tag name="@see">#retryTrigger</tag>
<parameter name="v">
<type class="javax.baja.control.trigger.BTimeTrigger"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.getAlarmOnFailure() -->
<method name="getAlarmOnFailure"  public="true">
<description>
Get the &lt;code&gt;alarmOnFailure&lt;/code&gt; property.&#xa; Determines whether an alarm should be generated when a failure occurs for&#xa; an execution of this export.
</description>
<tag name="@see">#alarmOnFailure</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.setAlarmOnFailure(boolean) -->
<method name="setAlarmOnFailure"  public="true">
<description>
Set the &lt;code&gt;alarmOnFailure&lt;/code&gt; property.&#xa; Determines whether an alarm should be generated when a failure occurs for&#xa; an execution of this export.
</description>
<tag name="@see">#alarmOnFailure</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.getAlarmSourceInfo() -->
<method name="getAlarmSourceInfo"  public="true">
<description>
Get the &lt;code&gt;alarmSourceInfo&lt;/code&gt; property.
</description>
<tag name="@see">#alarmSourceInfo</tag>
<return>
<type class="javax.baja.alarm.BAlarmSourceInfo"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.setAlarmSourceInfo(javax.baja.alarm.BAlarmSourceInfo) -->
<method name="setAlarmSourceInfo"  public="true">
<description>
Set the &lt;code&gt;alarmSourceInfo&lt;/code&gt; property.
</description>
<tag name="@see">#alarmSourceInfo</tag>
<parameter name="v">
<type class="javax.baja.alarm.BAlarmSourceInfo"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.execute() -->
<method name="execute"  public="true">
<description>
Invoke the &lt;code&gt;execute&lt;/code&gt; action.&#xa; Execute the work that the alarm archival is responsible for.
</description>
<tag name="@see">#execute</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.retry() -->
<method name="retry"  public="true">
<description>
Invoke the &lt;code&gt;retry&lt;/code&gt; action.&#xa; The action invoked by the retryTrigger to retry any failed alarm archival&#xa; executions.
</description>
<tag name="@see">#retry</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.ackAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="ackAlarm"  public="true">
<description>
Invoke the &lt;code&gt;ackAlarm&lt;/code&gt; action.&#xa; Hidden action to support alarm acknowledgements
</description>
<tag name="@see">#ackAlarm</tag>
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.getAlarmArchive() -->
<method name="getAlarmArchive"  public="true" abstract="true">
<description>
Get the alarm archive configured.
</description>
<return>
<type class="javax.baja.alarm.BAlarmArchive"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.getArchiveStatus() -->
<method name="getArchiveStatus"  public="true" abstract="true">
<description>
Get the status of the alarm archive configured.
</description>
<return>
<type class="javax.baja.status.BStatus"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.doExecute(javax.baja.sys.Context) -->
<method name="doExecute"  public="true" final="true">
<description>
Handle an invocation of the execute action with a context.&#xa; It checks for the provider&#x27;s status and resolves the query ord to get the&#xa; alarm records to export.&#xa; This will only export the alarm records eligible for archival since&#xa; the lastUpdate time minus the clearedAlarmLingerTime.&#xa; &lt;code&gt;exportClearedRecords(Cursor&amp;lt;BAlarmRecord&amp;gt; recordsToExport)&lt;/code&gt; is called with the&#xa; query result which returns the list of the BUuid of the alarm records exported.&#xa; The list is used to remove the exported records from the alarm db space post archival.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.isProviderReady() -->
<method name="isProviderReady"  public="true" abstract="true">
<description>
Check the status of the provider&#x27;s configuration before running execute method.
</description>
<return>
<type class="boolean"/>
<description>
True if the provider is ready to run execute method. False otherwise.
</description>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.exportClearedRecords(javax.baja.sys.Cursor&lt;javax.baja.alarm.BAlarmRecord&gt;) -->
<method name="exportClearedRecords"  public="true" abstract="true">
<description>
Called as part of the execute method to export the cleared alarm/s from&#xa; the openAlarms space to the alarm archive.
</description>
<tag name="@see">#execute</tag>
<parameter name="recordsToExport">
<parameterizedType class="javax.baja.sys.Cursor">
<args>
<type class="javax.baja.alarm.BAlarmRecord"/>
</args>
</parameterizedType>
<description>
The Cursor reference containing alarm records&#xa;                        eligible for archival.
</description>
</parameter>
<return>
<parameterizedType class="java.util.List">
<args>
<type class="javax.baja.util.BUuid"/>
</args>
</parameterizedType>
<description>
List of alarm record BUuid successfully exported. The records will&#xa; be removed from the local alarm database.
</description>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.executeOk() -->
<method name="executeOk"  public="true">
<description>
Called to indicate that the execute action completed&#xa; successfully. This sets lastSuccess and clears the fault&#xa; cause. This method also updates the status by calling &lt;code&gt;updateStatus()&lt;/code&gt;
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.executeFail() -->
<method name="executeFail"  public="true">
<description>
See setExecuteFail(String)
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.executeFail(java.lang.String) -->
<method name="executeFail"  public="true">
<description>
Called to indicate that the execute action failed&#xa; to complete.  This sets the last failure time and message&#xa; and sets the fault cause. This method also&#xa; updates the status by calling &lt;code&gt;updateStatus()&lt;/code&gt;
</description>
<parameter name="reason">
<type class="java.lang.String"/>
<description>
A localized string to set as the fault cause.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.executeInProgress() -->
<method name="executeInProgress"  public="true">
<description>
Called to indicate that the execute action is in progress.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.isOperational() -->
<method name="isOperational"  public="true" final="true">
<description>
If false, execute should be short circuited.
</description>
<return>
<type class="boolean"/>
<description>
false if disabled, or fault
</description>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.isDown() -->
<method name="isDown"  public="true" final="true">
<description>
Return true if the status is down.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.isFault() -->
<method name="isFault"  public="true" final="true">
<description>
Return true if in fault.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.isDisabled() -->
<method name="isDisabled"  public="true" final="true">
<description>
Return true if disabled.&#xa; Is disabled if the user has manually&#xa; set the enabled property to false.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.canExecuteRequest() -->
<method name="canExecuteRequest"  protected="true" final="true">
<description>
Returns true if we can execute a request to the remote archive alarm system.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.updateStatus() -->
<method name="updateStatus"  public="true" final="true">
<description>
Convenience method to update the status of the instance.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.isFatalFault() -->
<method name="isFatalFault"  public="true" final="true">
<description>
Return true if the service detected a fatal fault.&#xa; Fatal faults cannot be recovered until the service&#xa; is restarted.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.configFatal(java.lang.String) -->
<method name="configFatal"  public="true" final="true">
<description>
Set the service into the fatal fault condition.  The fatal&#xa; fault condition cannot be cleared until station restart.
</description>
<parameter name="cause">
<type class="java.lang.String"/>
<description>
fault cause
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.doRetry() -->
<method name="doRetry"  public="true">
<description>
If not disabled, calls execute on alarm archive who have failed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.doAckAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="doAckAlarm"  public="true" final="true">
<description/>
<parameter name="ackRequest">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.post(javax.baja.sys.Action, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="post"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
This post method includes special handling for the execute action.&#xa; A post of the execute action results in a call to&#xa; postExecute(Action, BValue, Context).  If this method is overridden&#xa; super.post(Action, BValue, Context) must be called.
</description>
<parameter name="action">
<type class="javax.baja.sys.Action"/>
</parameter>
<parameter name="arg">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.util.IFuture"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.postExecute(javax.baja.sys.Action, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="postExecute"  protected="true" abstract="true">
<description>
This post method includes special handling for the execute action.
</description>
<parameter name="action">
<type class="javax.baja.sys.Action"/>
</parameter>
<parameter name="arg">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.util.IFuture"/>
<description>
always return null
</description>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.fw(int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object) -->
<method name="fw"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
For internal framework use only.
</description>
<parameter name="x">
<type class="int"/>
</parameter>
<parameter name="a">
<type class="java.lang.Object"/>
</parameter>
<parameter name="b">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c">
<type class="java.lang.Object"/>
</parameter>
<parameter name="d">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="java.lang.Object"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.getAgents(javax.baja.sys.Context) -->
<method name="getAgents"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.agent.AgentList"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.checkParentForRestrictedComponent(javax.baja.sys.BComponent, javax.baja.sys.Context) -->
<method name="checkParentForRestrictedComponent"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Ensures only one alarm archive provider instance is allowed to live under a&#xa; &lt;code&gt;<see ref="javax.baja.alarm.BAlarmService">BAlarmService</see>&lt;/code&gt; in a station.
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BArchiveAlarmProvider.status -->
<field name="status"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.&#xa; The status of this alarm archival provider.
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</field>

<!-- javax.baja.alarm.BArchiveAlarmProvider.enabled -->
<field name="enabled"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;enabled&lt;/code&gt; property.&#xa; The enabled state of this alarm archive provider.  If false, this alarm&#xa; archive provider won&#x27;t be used to store cleared alarms.  If true,&#xa; this alarm archive provider will be used to archive alarms once they&#xa; are cleared.
</description>
<tag name="@see">#getEnabled</tag>
<tag name="@see">#setEnabled</tag>
</field>

<!-- javax.baja.alarm.BArchiveAlarmProvider.faultCause -->
<field name="faultCause"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.&#xa; This is a text message describing the reason for the fault.
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</field>

<!-- javax.baja.alarm.BArchiveAlarmProvider.state -->
<field name="state"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;state&lt;/code&gt; property.&#xa; The current state of the work that is performed by this descriptor.
</description>
<tag name="@see">#getState</tag>
<tag name="@see">#setState</tag>
</field>

<!-- javax.baja.alarm.BArchiveAlarmProvider.executionTime -->
<field name="executionTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;executionTime&lt;/code&gt; property.&#xa; The execution time controls when the execute action will be invoked.
</description>
<tag name="@see">#getExecutionTime</tag>
<tag name="@see">#setExecutionTime</tag>
</field>

<!-- javax.baja.alarm.BArchiveAlarmProvider.clearedAlarmLingerTime -->
<field name="clearedAlarmLingerTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;clearedAlarmLingerTime&lt;/code&gt; property.&#xa; The minimum time that an alarm should remain in the cleared state before&#xa; it can be archived.
</description>
<tag name="@see">#getClearedAlarmLingerTime</tag>
<tag name="@see">#setClearedAlarmLingerTime</tag>
</field>

<!-- javax.baja.alarm.BArchiveAlarmProvider.lastAttempt -->
<field name="lastAttempt"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lastAttempt&lt;/code&gt; property.&#xa; The last time that the alarm archival&#x27;s work was started.
</description>
<tag name="@see">#getLastAttempt</tag>
<tag name="@see">#setLastAttempt</tag>
</field>

<!-- javax.baja.alarm.BArchiveAlarmProvider.lastSuccess -->
<field name="lastSuccess"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lastSuccess&lt;/code&gt; property.&#xa; The last time that the alarm archival&#x27;s work completed successfully.
</description>
<tag name="@see">#getLastSuccess</tag>
<tag name="@see">#setLastSuccess</tag>
</field>

<!-- javax.baja.alarm.BArchiveAlarmProvider.lastFailure -->
<field name="lastFailure"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lastFailure&lt;/code&gt; property.&#xa; The last time that the alarm archival&#x27;s work failed to complete successfully.
</description>
<tag name="@see">#getLastFailure</tag>
<tag name="@see">#setLastFailure</tag>
</field>

<!-- javax.baja.alarm.BArchiveAlarmProvider.retryTrigger -->
<field name="retryTrigger"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;retryTrigger&lt;/code&gt; property.&#xa; The retryTrigger controls when retries of failed alarm archival&#xa; executions will be reattempted.
</description>
<tag name="@see">#getRetryTrigger</tag>
<tag name="@see">#setRetryTrigger</tag>
</field>

<!-- javax.baja.alarm.BArchiveAlarmProvider.alarmOnFailure -->
<field name="alarmOnFailure"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmOnFailure&lt;/code&gt; property.&#xa; Determines whether an alarm should be generated when a failure occurs for&#xa; an execution of this export.
</description>
<tag name="@see">#getAlarmOnFailure</tag>
<tag name="@see">#setAlarmOnFailure</tag>
</field>

<!-- javax.baja.alarm.BArchiveAlarmProvider.alarmSourceInfo -->
<field name="alarmSourceInfo"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmSourceInfo&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmSourceInfo</tag>
<tag name="@see">#setAlarmSourceInfo</tag>
</field>

<!-- javax.baja.alarm.BArchiveAlarmProvider.execute -->
<field name="execute"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;execute&lt;/code&gt; action.&#xa; Execute the work that the alarm archival is responsible for.
</description>
<tag name="@see">#execute()</tag>
</field>

<!-- javax.baja.alarm.BArchiveAlarmProvider.retry -->
<field name="retry"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;retry&lt;/code&gt; action.&#xa; The action invoked by the retryTrigger to retry any failed alarm archival&#xa; executions.
</description>
<tag name="@see">#retry()</tag>
</field>

<!-- javax.baja.alarm.BArchiveAlarmProvider.ackAlarm -->
<field name="ackAlarm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;ackAlarm&lt;/code&gt; action.&#xa; Hidden action to support alarm acknowledgements
</description>
<tag name="@see">#ackAlarm(BAlarmRecord parameter)</tag>
</field>

<!-- javax.baja.alarm.BArchiveAlarmProvider.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.alarm.BArchiveAlarmProvider.LEX -->
<field name="LEX"  public="true" static="true" final="true">
<type class="javax.baja.util.Lexicon"/>
<description/>
</field>

</class>
</bajadoc>
