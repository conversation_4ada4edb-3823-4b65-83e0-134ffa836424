<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.job.BPupDiscoverPointsJob" name="BPupDiscoverPointsJob" packageName="com.tridium.aapup.job" public="true">
<description>
PUP Point Discovery.&#xa; This job class handles the implementation of the scan for&#xa; points in an PUP controller device.&#xa; &lt;p&gt;&#xa; Points are uniquely defined within a device by a combination of attribute and channel.
</description>
<tag name="@author">Cli<PERSON></tag>
<tag name="@creation">7/26/2005 2:13PM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.91</tag>
<extends>
<type class="javax.baja.job.BSimpleJob"/>
</extends>
<implements>
<type class="com.tridium.aapup.AaPupConst"/>
</implements>
<property name="learnedPoints" flags="">
<type class="javax.baja.util.BFolder"/>
<description>
Slot for the &lt;code&gt;learnedPoints&lt;/code&gt; property.&#xa; the folder where points discovered are plopped.
</description>
<tag name="@see">#getLearnedPoints</tag>
<tag name="@see">#setLearnedPoints</tag>
</property>

<topic name="channelLearned" flags="">
<eventType>
<type class="javax.baja.sys.BValue"/>
</eventType><description>
Slot for the &lt;code&gt;channelLearned&lt;/code&gt; topic.&#xa; Whenever a channel is completely learned (all the attributes in a single channel,&#xa; this topic if fired to pass the discovery channel object to the point manager.  If&#xa; there are n channels in a device, this will be fired n times.
</description>
<tag name="@see">#fireChannelLearned</tag>
</topic>

</class>
</bajadoc>
