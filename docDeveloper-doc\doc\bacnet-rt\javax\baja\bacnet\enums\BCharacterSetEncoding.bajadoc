<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BCharacterSetEncoding" name="BCharacterSetEncoding" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BCharacterSetEncoding represents the enumeration of character sets&#xa; defined by Bacnet for string encoding.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">22 Mar 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;iso10646_UTF8&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>0</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ibmMicrosoftDBCS&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>1</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;jisX0208&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>2</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;iso10646_UCS4&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>3</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;iso10646_UCS2&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>4</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;iso8859_1&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>5</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknown&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>255</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.getEncodingName() -->
<method name="getEncodingName"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.getCharSetName() -->
<method name="getCharSetName"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.fromCharSetName(java.lang.String) -->
<method name="fromCharSetName"  public="true" static="true">
<description/>
<parameter name="charsetName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.ISO_10646_UTF8 -->
<field name="ISO_10646_UTF8"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for iso10646_UTF8.
</description>
</field>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.IBM_MICROSOFT_DBCS -->
<field name="IBM_MICROSOFT_DBCS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ibmMicrosoftDBCS.
</description>
</field>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.JIS_X0208 -->
<field name="JIS_X0208"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for jisX0208.
</description>
</field>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.ISO_10646_UCS4 -->
<field name="ISO_10646_UCS4"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for iso10646_UCS4.
</description>
</field>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.ISO_10646_UCS2 -->
<field name="ISO_10646_UCS2"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for iso10646_UCS2.
</description>
</field>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.ISO_8859_1 -->
<field name="ISO_8859_1"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for iso8859_1.
</description>
</field>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.UNKNOWN -->
<field name="UNKNOWN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknown.
</description>
</field>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.iso10646_UTF8 -->
<field name="iso10646_UTF8"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
<description>
BCharacterSetEncoding constant for iso10646_UTF8.
</description>
</field>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.ibmMicrosoftDBCS -->
<field name="ibmMicrosoftDBCS"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
<description>
BCharacterSetEncoding constant for ibmMicrosoftDBCS.
</description>
</field>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.jisX0208 -->
<field name="jisX0208"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
<description>
BCharacterSetEncoding constant for jisX0208.
</description>
</field>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.iso10646_UCS4 -->
<field name="iso10646_UCS4"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
<description>
BCharacterSetEncoding constant for iso10646_UCS4.
</description>
</field>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.iso10646_UCS2 -->
<field name="iso10646_UCS2"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
<description>
BCharacterSetEncoding constant for iso10646_UCS2.
</description>
</field>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.iso8859_1 -->
<field name="iso8859_1"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
<description>
BCharacterSetEncoding constant for iso8859_1.
</description>
</field>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.unknown -->
<field name="unknown"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
<description>
BCharacterSetEncoding constant for unknown.
</description>
</field>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BCharacterSetEncoding.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
