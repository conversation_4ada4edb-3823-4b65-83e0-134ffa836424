<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.io.ValueDocDecoder$BogTypeResolver" name="ValueDocDecoder.BogTypeResolver" packageName="javax.baja.io" public="true" static="true" innerClass="true">
<description>
BOG Type Resolver&#xa; &lt;p&gt;&#xa; BOG has &#x27;m&#x27; and &#x27;t&#x27; attributes that are used to resolve Type Specifications&#xa; when decoding a BOG document
</description>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="javax.baja.io.ValueDocDecoder$ITypeResolver"/>
</implements>
<!-- javax.baja.io.ValueDocDecoder.BogTypeResolver() -->
<constructor name="BogTypeResolver" public="true">
<description/>
</constructor>

<!-- javax.baja.io.ValueDocDecoder.BogTypeResolver.loadModule(javax.baja.io.ValueDocDecoder, javax.baja.sys.BComplex, java.lang.String, java.lang.String, java.lang.String) -->
<method name="loadModule"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Load a module into memory for the type of a property being&#xa; decoded.  The module should be loaded into the given BogDecoder&#x27;s&#xa; module map (see getModuleMap()).&#xa; The String parameter&#xa; should be of the form &#x22;key=moduleName&#x22;.  The module&#xa; loaded should be stored in memory for subsequent lookup&#xa; by the newInstance() method.
</description>
<parameter name="decoder">
<type class="javax.baja.io.ValueDocDecoder"/>
<description>
The BogDecoder instance for which to load&#xa; the module
</description>
</parameter>
<parameter name="parent">
<type class="javax.baja.sys.BComplex"/>
<description>
The parent for the property about to be added.
</description>
</parameter>
<parameter name="propName">
<type class="java.lang.String"/>
<description>
The name of the property
</description>
</parameter>
<parameter name="moduleStr">
<type class="java.lang.String"/>
<description>
The encoded module String of the form &#x22;moduleKey=moduleName&#x22;
</description>
</parameter>
<parameter name="ignored">
<type class="java.lang.String"/>
<description>
not processed
</description>
</parameter>
<return>
<type class="javax.baja.sys.BModule"/>
<description>
BModule
</description>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogTypeResolver.newInstance(javax.baja.io.ValueDocDecoder, javax.baja.sys.BComplex, java.lang.String, javax.baja.sys.Property, java.lang.String) -->
<method name="newInstance"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Given a frozen property and/or a type attribute,&#xa; create a BValue new instance.  If there is a fatal error&#xa; then throw an XException.  If there is a recoverable&#xa; error then log a warning and return null.
</description>
<parameter name="decoder">
<type class="javax.baja.io.ValueDocDecoder"/>
<description>
The BogDecoder instance for which the property is being decoded
</description>
</parameter>
<parameter name="parent">
<type class="javax.baja.sys.BComplex"/>
<description>
The parent for the property about to be added.
</description>
</parameter>
<parameter name="propName">
<type class="java.lang.String"/>
<description>
The name of the property
</description>
</parameter>
<parameter name="prop">
<type class="javax.baja.sys.Property"/>
<description>
The frozen property
</description>
</parameter>
<parameter name="typeStr">
<type class="java.lang.String"/>
<description>
The encoded property type string of the form &#x22;moduleKey:typeName&#x22;, where&#xa; the moduleKey should be a key identifying a module loaded in memory&#xa; by a previous call to the loadModule() method.  The typeName identifies&#xa; a unique type within the module.
</description>
</parameter>
<return>
<type class="javax.baja.sys.BValue"/>
<description>
BValue is a new instance of the property for the proper type.
</description>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogTypeResolver.getModuleMap(javax.baja.io.ValueDocDecoder) -->
<method name="getModuleMap"  public="true">
<description>
Given a BogDecoder instance, return its module memory map.
</description>
<parameter name="decoder">
<type class="javax.baja.io.ValueDocDecoder"/>
</parameter>
<return>
<parameterizedType class="java.util.Map">
<args>
<type class="java.lang.String"/>
<parameterizedType class="java.util.Map">
<args>
<type class="javax.baja.nre.platform.RuntimeProfile"/>
<type class="com.tridium.sys.module.NModule"/>
</args>
</parameterizedType>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogTypeResolver.getMappedModules(javax.baja.io.ValueDocDecoder, java.lang.String) -->
<method name="getMappedModules"  public="true">
<description/>
<parameter name="decoder">
<type class="javax.baja.io.ValueDocDecoder"/>
</parameter>
<parameter name="moduleName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridium.sys.module.NModule" dimension="1"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogTypeResolver.getMappedModule(javax.baja.io.ValueDocDecoder, java.lang.String, javax.baja.nre.platform.RuntimeProfile) -->
<method name="getMappedModule"  public="true">
<description/>
<parameter name="decoder">
<type class="javax.baja.io.ValueDocDecoder"/>
</parameter>
<parameter name="moduleName">
<type class="java.lang.String"/>
</parameter>
<parameter name="profile">
<type class="javax.baja.nre.platform.RuntimeProfile"/>
</parameter>
<return>
<type class="com.tridium.sys.module.NModule"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogTypeResolver.updateModuleMap(javax.baja.io.ValueDocDecoder, com.tridium.sys.module.NModule[]) -->
<method name="updateModuleMap"  public="true">
<description/>
<parameter name="decoder">
<type class="javax.baja.io.ValueDocDecoder"/>
</parameter>
<parameter name="modules">
<type class="com.tridium.sys.module.NModule" dimension="1"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogTypeResolver.getModuleNameMap(javax.baja.io.ValueDocDecoder) -->
<method name="getModuleNameMap"  public="true">
<description>
Given a BogDecoder instance, return its module name map.
</description>
<parameter name="decoder">
<type class="javax.baja.io.ValueDocDecoder"/>
</parameter>
<return>
<parameterizedType class="java.util.Map">
<args>
<type class="java.lang.String"/>
<type class="java.lang.String"/>
</args>
</parameterizedType>
</return>
</method>

</class>
</bajadoc>
