import os
import sys
import zipfile
import shutil

def extract_all_zip_files(source_folder, destination_folder):
    """
    Extracts all ZIP files from the source folder to the destination folder.
    For each ZIP file:
    1. Creates a subdirectory in the destination folder with the ZIP file's name (without extension).
    2. Extracts the contents of the ZIP file into that subdirectory.

    Args:
        source_folder (str): The path to the folder containing the ZIP files.
        destination_folder (str): The path to the folder where extracted contents will be placed.
    """
    # Validate the source folder
    if not os.path.isdir(source_folder):
        print(f"Error: Source folder '{source_folder}' not found or is not a directory.")
        sys.exit(1)

    # Create destination folder if it doesn't exist
    try:
        os.makedirs(destination_folder, exist_ok=True)
        print(f"Destination folder: {destination_folder}")
    except OSError as e:
        print(f"Error creating destination folder '{destination_folder}': {e}")
        sys.exit(1)

    print(f"Processing ZIP files in folder: {source_folder}\n")
    processed_count = 0
    skipped_count = 0
    error_count = 0

    try:
        # List all entries in the source directory
        all_entries = os.listdir(source_folder)
    except OS<PERSON>rror as e:
        print(f"Error accessing folder '{source_folder}': {e}")
        sys.exit(1)

    for entry_name in all_entries:
        # Construct the full path to the current entry
        entry_path = os.path.join(source_folder, entry_name)

        # Check if it's a file and ends with '.zip' (case-insensitive)
        if os.path.isfile(entry_path) and entry_name.lower().endswith('.zip'):
            zip_filename = entry_name
            base_name = os.path.splitext(zip_filename)[0]  # Get filename without extension
            target_folder_path = os.path.join(destination_folder, base_name)

            print(f"--- Processing: {zip_filename} ---")

            # Create the corresponding folder in destination
            try:
                os.makedirs(target_folder_path, exist_ok=True)
                print(f"  [+] Created/verified folder: {target_folder_path}")
            except OSError as e:
                print(f"  [-] Error creating folder '{target_folder_path}': {e}")
                error_count += 1
                print("-" * (len(zip_filename) + 18))  # Separator line
                continue  # Skip to the next file

            # Extract the ZIP file
            print(f"  [*] Attempting to extract '{zip_filename}' into '{target_folder_path}'...")
            try:
                # Open the ZIP file in read mode
                with zipfile.ZipFile(entry_path, 'r') as zip_ref:
                    # Extract all the contents into the target folder
                    zip_ref.extractall(target_folder_path)
                print(f"  [+] Successfully extracted '{zip_filename}'.")
                processed_count += 1
            except zipfile.BadZipFile:
                print(f"  [-] Error: '{zip_filename}' is not a valid ZIP file or is corrupted.")
                error_count += 1
            except FileNotFoundError:
                print(f"  [-] Error: ZIP file '{entry_path}' not found.")
                error_count += 1
            except OSError as e:
                print(f"  [-] Error during extraction (e.g., disk full, permissions): {e}")
                error_count += 1
            except Exception as e:
                print(f"  [-] An unexpected error occurred during extraction: {e}")
                error_count += 1

            print("-" * (len(zip_filename) + 18))  # Separator line

    print("\n--- Processing Summary ---")
    print(f"Successfully processed: {processed_count} ZIP files")
    print(f"Skipped files: {skipped_count}")
    print(f"Errors encountered: {error_count} ZIP files")
    print("--------------------------")

# --- Main Execution ---
if __name__ == "__main__":
    # Define the source and destination paths
    source_path = r'C:\Users\<USER>\OneDrive\Desktop\work\nSFM\5.10 KC Report'
    destination_path = r'C:\Users\<USER>\OneDrive\Desktop\work\nSFM\5.10 KC Report\extracted'
    
    print(f"Source folder: {source_path}")
    print(f"Destination folder: {destination_path}")
    print("-" * 50)
    
    # Confirm before proceeding
    confirm = input("Do you want to proceed with extracting all ZIP files? (yes/no): ").lower().strip()
    if confirm != 'yes':
        print("Operation cancelled by user.")
        sys.exit(0)
    
    extract_all_zip_files(source_path, destination_path)
