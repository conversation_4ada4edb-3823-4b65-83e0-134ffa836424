<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.BAlarmConsoleChannel" name="BAlarmConsoleChannel" packageName="com.tridium.alarm" public="true">
<description>
BAlarmConsoleChannel is used by BConsoleRecipient to handle messages between&#xa; the BConsoleRecipient and BAlarmConsole.&#xa; &lt;p&gt;&#xa; Also see &#x27;com.tridium.alarm.ui.BAlarmConsoleUIChannel&#x27;&#xa; &lt;/p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">19 Jun 02</tag>
<tag name="@version">$Revision: 32$ $Date: 5/21/10 12:26:19 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="com.tridium.fox.sys.BFoxChannel"/>
</extends>
<implements>
<parameterizedType class="java.util.function.BiConsumer">
<args>
<type class="com.tridium.alarm.BConsoleRecipient"/>
<type class="javax.baja.alarm.BAlarmRecord"/>
</args>
</parameterizedType>
</implements>
<action name="routeAlarmAcks" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;routeAlarmAcks&lt;/code&gt; action.
</description>
<tag name="@see">#routeAlarmAcks()</tag>
</action>

</class>
</bajadoc>
