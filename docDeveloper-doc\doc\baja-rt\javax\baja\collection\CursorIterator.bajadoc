<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.collection.CursorIterator" name="CursorIterator" packageName="javax.baja.collection" public="true">
<description>
Wraps a Cursor in an Iterator implementation.
</description>
<tag name="@author"><PERSON>, <PERSON></tag>
<tag name="@creation">01/27/2014, 01/19/2016</tag>
<tag name="@since">Niagara 4.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<parameterizedType class="java.util.Iterator">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</implements>
<typeParameters>
<typeVariable class="T">
</typeVariable>
</typeParameters>
<!-- javax.baja.collection.CursorIterator(javax.baja.sys.Cursor&lt;T&gt;) -->
<constructor name="CursorIterator" public="true">
<parameter name="cursor">
<parameterizedType class="javax.baja.sys.Cursor">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</parameter>
<description/>
</constructor>

<!-- javax.baja.collection.CursorIterator.getContext() -->
<method name="getContext"  public="true">
<description>
Get the context from the cursor.
</description>
<return>
<type class="javax.baja.sys.Context"/>
<description>
Returns the context from the cursor.
</description>
</return>
</method>

<!-- javax.baja.collection.CursorIterator.hasNext() -->
<method name="hasNext"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="boolean"/>
<description>
Returns true if the iterator has additional elements.
</description>
</return>
</method>

<!-- javax.baja.collection.CursorIterator.next() -->
<method name="next"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the next element from the iterator.
</description>
<return>
<typeVariable class="T"/>
<description>
Returns the next element from the iterator and advances the iterator.
</description>
</return>
</method>

<!-- javax.baja.collection.CursorIterator.remove() -->
<method name="remove"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
