<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>webEditors Modules</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">webEditors</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-nmodule_webEditors_rc_fe_baja_BaseEditor.html">nmodule/webEditors/rc/fe/baja/BaseEditor</a></li><li><a href="module-nmodule_webEditors_rc_fe_BaseWidget.html">nmodule/webEditors/rc/fe/BaseWidget</a></li><li><a href="module-nmodule_webEditors_rc_fe_fe.html">nmodule/webEditors/rc/fe/fe</a></li><li><a href="module-nmodule_webEditors_rc_fe_feDialogs.html">nmodule/webEditors/rc/fe/feDialogs</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_commands_MgrCommand.html">nmodule/webEditors/rc/wb/mgr/commands/MgrCommand</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_Manager.html">nmodule/webEditors/rc/wb/mgr/Manager</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html">nmodule/webEditors/rc/wb/mgr/MgrLearn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrStateHandler.html">nmodule/webEditors/rc/wb/mgr/MgrStateHandler</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrTypeInfo.html">nmodule/webEditors/rc/wb/mgr/MgrTypeInfo</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_IconMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinPropMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_NameMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyPathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_TypeMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/MgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrModel.html">nmodule/webEditors/rc/wb/mgr/model/MgrModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">nmodule/webEditors/rc/wb/table/model/Column</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_DisplayNameColumn.html">nmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_IconColumn.html">nmodule/webEditors/rc/wb/table/model/columns/IconColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_JsonObjectPropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_PropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_ToStringColumn.html">nmodule/webEditors/rc/wb/table/model/columns/ToStringColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentSource.html">nmodule/webEditors/rc/wb/table/model/ComponentSource</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentTableModel.html">nmodule/webEditors/rc/wb/table/model/ComponentTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">nmodule/webEditors/rc/wb/table/model/Row</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html">nmodule/webEditors/rc/wb/table/model/TableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_Table.html">nmodule/webEditors/rc/wb/table/Table</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeNodeRow.html">nmodule/webEditors/rc/wb/table/tree/TreeNodeRow</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeTableModel.html">nmodule/webEditors/rc/wb/table/tree/TreeTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_tree_TreeNode.html">nmodule/webEditors/rc/wb/tree/TreeNode</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="tutorials.list.html" class="dropdown-toggle" data-toggle="dropdown">Tutorials<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="tutorial-6-managers.html">Managers</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Modules</h1>
<section>

<header>
    
        <h2>
        
        </h2>
        
    
</header>


<article>
    <div class="container-overview">
    
        

        
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


        
    
    </div>

    

    

    
        <h3 class="subsection-title">Classes</h3>

        <dl>
            <dt><a href="module-nmodule_webEditors_rc_fe_baja_BaseEditor.html">module:nmodule/webEditors/rc/fe/baja/BaseEditor</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_fe_BaseWidget.html">module:nmodule/webEditors/rc/fe/BaseWidget</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_mgr_Manager.html">module:nmodule/webEditors/rc/wb/mgr/Manager</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_mgr_MgrStateHandler.html">module:nmodule/webEditors/rc/wb/mgr/MgrStateHandler</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_mgr_MgrTypeInfo.html">module:nmodule/webEditors/rc/wb/mgr/MgrTypeInfo</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_IconMgrColumn.html">module:nmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumn</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinMgrColumn.html">module:nmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumn</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinPropMgrColumn.html">module:nmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumn</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_NameMgrColumn.html">module:nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PathMgrColumn.html">module:nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyMgrColumn.html">module:nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyPathMgrColumn.html">module:nmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumn</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_TypeMgrColumn.html">module:nmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumn</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html">module:nmodule/webEditors/rc/wb/mgr/model/MgrColumn</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrModel.html">module:nmodule/webEditors/rc/wb/mgr/model/MgrModel</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">module:nmodule/webEditors/rc/wb/table/model/Column</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_table_model_columns_DisplayNameColumn.html">module:nmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumn</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_table_model_columns_IconColumn.html">module:nmodule/webEditors/rc/wb/table/model/columns/IconColumn</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_table_model_columns_JsonObjectPropertyColumn.html">module:nmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumn</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_table_model_columns_PropertyColumn.html">module:nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_table_model_columns_ToStringColumn.html">module:nmodule/webEditors/rc/wb/table/model/columns/ToStringColumn</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentSource.html">module:nmodule/webEditors/rc/wb/table/model/ComponentSource</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentTableModel.html">module:nmodule/webEditors/rc/wb/table/model/ComponentTableModel</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html">module:nmodule/webEditors/rc/wb/table/model/TableModel</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_table_Table.html">module:nmodule/webEditors/rc/wb/table/Table</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeNodeRow.html">module:nmodule/webEditors/rc/wb/table/tree/TreeNodeRow</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeTableModel.html">module:nmodule/webEditors/rc/wb/table/tree/TreeTableModel</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_tree_TreeNode.html">module:nmodule/webEditors/rc/wb/tree/TreeNode</a></dt>
            <dd></dd>
        </dl>
    

    
        <h3 class="subsection-title">Mixins</h3>

        <dl>
            <dt><a href="module-nmodule_webEditors_rc_wb_mgr_commands_MgrCommand.html">module:nmodule/webEditors/rc/wb/mgr/commands/MgrCommand</a></dt>
            <dd></dd>
        
            <dt><a href="module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html">module:nmodule/webEditors/rc/wb/mgr/MgrLearn</a></dt>
            <dd></dd>
        </dl>
    

    

    

    

    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	webEditors Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:59+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>