<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="javax.baja.bacnet">
<description/>
<class packageName="javax.baja.bacnet" name="BBacnetDevice"><description>BBacnetDevice represents the Baja shadow object for a Bacnet device.</description></class>
<class packageName="javax.baja.bacnet" name="BBacnetDeviceFolder"><description>BBacnetDeviceFolder.</description></class>
<class packageName="javax.baja.bacnet" name="BBacnetNetwork"><description>BBacnetNetwork is the base container for Bacnet communications.</description></class>
<class packageName="javax.baja.bacnet" name="BBacnetObject"/>
<class packageName="javax.baja.bacnet" name="BBacnetObject.BacnetPropertyData"/>
<class packageName="javax.baja.bacnet" name="BacnetAlarmConst" category="interface"><description>Standard keys for the AlarmData portion of a&#xa; BAlarmRecord referencing a Bacnet alarm.</description></class>
<class packageName="javax.baja.bacnet" name="BacnetConfirmedServiceChoice" category="interface"><description>This interface contains constants which represent the values&#xa; of the BacnetConfirmedServiceChoice iteration.</description></class>
<class packageName="javax.baja.bacnet" name="BacnetConst" category="interface"><description>BacnetConst contains all constants needed for use in the&#xa; javax.baja.bacnet module.</description></class>
<class packageName="javax.baja.bacnet" name="BacnetUnconfirmedServiceChoice" category="interface"><description>This interface contains constants which represent the values&#xa; of the BacnetUnconfirmedServiceChoice enumeration.</description></class>
<class packageName="javax.baja.bacnet" name="BIBacnetObjectContainer" category="interface"><description>BIBacnetObjectContainer resolves a triplet of object identifier,&#xa; property identifier, and property array index to a Bacnet point.</description></class>
<class packageName="javax.baja.bacnet" name="BacnetException" category="exception"><description>BacnetException is the base class for exceptions that&#xa; occur while performing any Bacnet operations.</description></class>
</package>
</bajadoc>
