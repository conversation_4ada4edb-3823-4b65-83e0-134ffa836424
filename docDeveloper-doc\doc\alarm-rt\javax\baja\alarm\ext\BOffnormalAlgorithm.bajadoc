<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.ext.BOffnormalAlgorithm" name="BOffnormalAlgorithm" packageName="javax.baja.alarm.ext" public="true">
<description>
BOffnormalAlgorithm is a super-class for all algorithms&#xa; that check for off normal (not fault) conditions.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">9 Nov 00</tag>
<tag name="@version">$Revision: 33$ $Date: 6/17/10 2:27:00 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.alarm.ext.BAlarmAlgorithm"/>
</extends>
<!-- javax.baja.alarm.ext.BOffnormalAlgorithm() -->
<constructor name="BOffnormalAlgorithm" public="true">
<description/>
</constructor>

<!-- javax.baja.alarm.ext.BOffnormalAlgorithm.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BOffnormalAlgorithm.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
A BOffnormalAlgorithm&#x27;s parent must be a BAlarmSourceExt
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BOffnormalAlgorithm.isGrandparentLegal(javax.baja.sys.BComponent) -->
<method name="isGrandparentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="grandparent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BOffnormalAlgorithm.checkAlarmState(javax.baja.status.BStatusValue, long, long) -->
<method name="checkAlarmState"  public="true" final="true">
<description>
Check for a normal / offnormal alarm transition.  Return&#xa; new alarm state or null if no change.
</description>
<parameter name="out">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<parameter name="toAlarmTimeDelay">
<type class="long"/>
</parameter>
<parameter name="toNormalDelay">
<type class="long"/>
</parameter>
<return>
<type class="javax.baja.alarm.ext.BAlarmState"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BOffnormalAlgorithm.checkAlarmState(javax.baja.status.BStatusValue, long) -->
<method name="checkAlarmState"  public="true" final="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Check for a normal / offnormal alarm transition.  Return&#xa; new alarm state or null if no change.
</description>
<tag name="@deprecated">since Niagara 3.5.  Use checkAlarmState(BStatusValue, long, long) instead.</tag>
<parameter name="out">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<parameter name="toAlarmTimeDelay">
<type class="long"/>
<description>
Minimum time period that an alarm condition must exist before the object alarms.
</description>
</parameter>
<return>
<type class="javax.baja.alarm.ext.BAlarmState"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BOffnormalAlgorithm.checkAlarms(javax.baja.status.BStatusValue, long) -->
<method name="checkAlarms"  public="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Default implementation.  Always returns null indicating no change in state.
</description>
<tag name="@deprecated">since Niagara 3.5.  Use checkAlarms(BStatusValue, long, long) instead.</tag>
<parameter name="out">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<parameter name="toAlarmTimeDelay">
<type class="long"/>
<description>
Minimum time period that an alarm condition must exist before the object alarms.
</description>
</parameter>
<return>
<type class="javax.baja.alarm.ext.BAlarmState"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BOffnormalAlgorithm.checkAlarms(javax.baja.status.BStatusValue, long, long) -->
<method name="checkAlarms"  public="true">
<description>
Default implementation.  Always returns null indicating no change in state.
</description>
<parameter name="out">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<parameter name="toAlarmTimeDelay">
<type class="long"/>
<description>
Minimum time period that an alarm condition must exist before the object alarms.
</description>
</parameter>
<parameter name="toNormalTimeDelay">
<type class="long"/>
<description>
Minimum time period that a normal condition must exist before the object returns to normal.
</description>
</parameter>
<return>
<type class="javax.baja.alarm.ext.BAlarmState"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BOffnormalAlgorithm.writeAlarmData(javax.baja.status.BStatusValue, java.util.Map) -->
<method name="writeAlarmData"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Write the key-value pairs defining alarm data for the&#xa;  alarm algorithm and state to the given Facets.
</description>
<parameter name="out">
<type class="javax.baja.status.BStatusValue"/>
<description>
The relevant control point status value
</description>
</parameter>
<parameter name="map">
<parameterizedType class="java.util.Map">
<args>
</args>
</parameterizedType>
<description>
The map.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BOffnormalAlgorithm.startTimer(long) -->
<method name="startTimer"  protected="true">
<description>
Start a timer to handle alarm validation.
</description>
<parameter name="timeDelay">
<type class="long"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BOffnormalAlgorithm.cancelTimer() -->
<method name="cancelTimer"  protected="true">
<description>
Cancels all timers associated with this&#xa;  alarm support object
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BOffnormalAlgorithm.isTimerExpired() -->
<method name="isTimerExpired"  protected="true">
<description>
Timer status function
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BOffnormalAlgorithm.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.alarm.ext.BOffnormalAlgorithm.log -->
<field name="log"  protected="true" static="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

</class>
</bajadoc>
