<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.server.BBacnetExportFolder" name="BBacnetExportFolder" packageName="com.tridium.bacnet.stack.server" public="true">
<description>
BBacnetExportFolder is the standard container to use&#xa; under BBacnetExportTable to organize BIBacnetExportObjects.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">19 Nov 2004</tag>
<tag name="@since">Niagara 3 BACnet 1.0</tag>
<extends>
<type class="javax.baja.util.BFolder"/>
</extends>
<implements>
<type class="javax.baja.agent.BIAgent"/>
</implements>
<implements>
<type class="javax.baja.bacnet.export.BIBacnetExportObject"/>
</implements>
<implements>
<type class="com.tridium.bacnet.stack.server.BIBacnetExportFolder"/>
</implements>
<implements>
<type class="javax.baja.bacnet.export.BacnetPropertyListProvider"/>
</implements>
<property name="status" flags="trd">
<type class="javax.baja.status.BStatus"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.&#xa; the status for Niagara server-side behavior.
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</property>

<property name="faultCause" flags="rt">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.&#xa; Provides a description of a fault with server-side behavior.
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</property>

<property name="objectId" flags="d">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this point is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="objectName" flags="d">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;objectName&lt;/code&gt; property.&#xa; the name by which this object is known to the Bacnet world.
</description>
<tag name="@see">#getObjectName</tag>
<tag name="@see">#setObjectName</tag>
</property>

<property name="description" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#getDescription</tag>
<tag name="@see">#setDescription</tag>
</property>

<property name="nodeType" flags="">
<type class="javax.baja.bacnet.enums.BBacnetNodeType"/>
<description>
Slot for the &lt;code&gt;nodeType&lt;/code&gt; property.
</description>
<tag name="@see">#getNodeType</tag>
<tag name="@see">#setNodeType</tag>
</property>

<property name="nodeSubtype" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;nodeSubtype&lt;/code&gt; property.
</description>
<tag name="@see">#getNodeSubtype</tag>
<tag name="@see">#setNodeSubtype</tag>
</property>

<property name="subordinates" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
<description>
Slot for the &lt;code&gt;subordinates&lt;/code&gt; property.
</description>
<tag name="@see">#getSubordinates</tag>
<tag name="@see">#setSubordinates</tag>
</property>

<topic name="subordinateAnnotationChanged" flags="">
<eventType>
<type class="javax.baja.sys.BValue"/>
</eventType><description>
Slot for the &lt;code&gt;subordinateAnnotationChanged&lt;/code&gt; topic.
</description>
<tag name="@see">#fireSubordinateAnnotationChanged</tag>
</topic>

</class>
</bajadoc>
