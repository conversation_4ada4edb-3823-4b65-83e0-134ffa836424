<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.datatypes.BDownloadRegionParams" name="BDownloadRegionParams" packageName="com.tridium.aapup.datatypes" public="true">
<description/>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="splFile" flags="r">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;splFile&lt;/code&gt; property.&#xa; The name of firmware file used in the upload/download action.
</description>
<tag name="@see">#getSplFile</tag>
<tag name="@see">#setSplFile</tag>
</property>

<property name="regionName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;regionName&lt;/code&gt; property.&#xa; text description of the region, 8 chars max
</description>
<tag name="@see">#getRegionName</tag>
<tag name="@see">#setRegionName</tag>
</property>

<property name="splBlob" flags="h">
<type class="javax.baja.sys.BBlob"/>
<description>
Slot for the &lt;code&gt;splBlob&lt;/code&gt; property.&#xa; Used behind the scenes to pass a blob containing the spl file&#xa; From the client vm to station vm
</description>
<tag name="@see">#getSplBlob</tag>
<tag name="@see">#setSplBlob</tag>
</property>

<property name="regionType" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;regionType&lt;/code&gt; property.&#xa; Specifies the type
</description>
<tag name="@see">#getRegionType</tag>
<tag name="@see">#setRegionType</tag>
</property>

</class>
</bajadoc>
