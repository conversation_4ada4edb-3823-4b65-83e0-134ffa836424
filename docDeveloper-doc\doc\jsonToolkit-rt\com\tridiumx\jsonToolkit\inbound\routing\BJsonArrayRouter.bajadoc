<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter" name="BJsonArrayRouter" packageName="com.tridiumx.jsonToolkit.inbound.routing" public="true">
<description>
Json Array Router is used to redirect each incoming array index to a new slot.&#xa; &lt;p&gt;&#xa; Given an JSON array input. [&#x22;John&#x22;,55,&#x22;Doe&#x22;,true,96,false,true,58]&#xa; The router will add slots automatically or use existing slots based on the data type in the array,&#xa; and route each index value to the slots created.
</description>
<tag name="@author"><PERSON><PERSON></tag>
<tag name="@since">Niagara 4.13</tag>
<extends>
<type class="com.tridiumx.jsonToolkit.inbound.routing.BJsonRouter"/>
</extends>
<property name="maxSlots" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;maxSlots&lt;/code&gt; property.
</description>
<tag name="@see">#getMaxSlots</tag>
<tag name="@see">#setMaxSlots</tag>
</property>

<property name="createStatusValues" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;createStatusValues&lt;/code&gt; property.
</description>
<tag name="@see">#getCreateStatusValues</tag>
<tag name="@see">#setCreateStatusValues</tag>
</property>

<property name="clearOutputsOnEachMessage" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;clearOutputsOnEachMessage&lt;/code&gt; property.
</description>
<tag name="@see">#getClearOutputsOnEachMessage</tag>
<tag name="@see">#setClearOutputsOnEachMessage</tag>
</property>

<property name="path" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;path&lt;/code&gt; property.&#xa; The path is exclusive to the array.&#xa; It manipulates entries in the array to filter a specific value for a nested array.&#xa; A JSON Path Expression, for example $.data.values.[0] will only display the [0] index value for the data array.
</description>
<tag name="@see">#getPath</tag>
<tag name="@see">#setPath</tag>
</property>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter() -->
<constructor name="BJsonArrayRouter" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter.getMaxSlots() -->
<method name="getMaxSlots"  public="true">
<description>
Get the &lt;code&gt;maxSlots&lt;/code&gt; property.
</description>
<tag name="@see">#maxSlots</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter.setMaxSlots(int) -->
<method name="setMaxSlots"  public="true">
<description>
Set the &lt;code&gt;maxSlots&lt;/code&gt; property.
</description>
<tag name="@see">#maxSlots</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter.getCreateStatusValues() -->
<method name="getCreateStatusValues"  public="true">
<description>
Get the &lt;code&gt;createStatusValues&lt;/code&gt; property.
</description>
<tag name="@see">#createStatusValues</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter.setCreateStatusValues(boolean) -->
<method name="setCreateStatusValues"  public="true">
<description>
Set the &lt;code&gt;createStatusValues&lt;/code&gt; property.
</description>
<tag name="@see">#createStatusValues</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter.getClearOutputsOnEachMessage() -->
<method name="getClearOutputsOnEachMessage"  public="true">
<description>
Get the &lt;code&gt;clearOutputsOnEachMessage&lt;/code&gt; property.
</description>
<tag name="@see">#clearOutputsOnEachMessage</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter.setClearOutputsOnEachMessage(boolean) -->
<method name="setClearOutputsOnEachMessage"  public="true">
<description>
Set the &lt;code&gt;clearOutputsOnEachMessage&lt;/code&gt; property.
</description>
<tag name="@see">#clearOutputsOnEachMessage</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter.getPath() -->
<method name="getPath"  public="true">
<description>
Get the &lt;code&gt;path&lt;/code&gt; property.&#xa; The path is exclusive to the array.&#xa; It manipulates entries in the array to filter a specific value for a nested array.&#xa; A JSON Path Expression, for example $.data.values.[0] will only display the [0] index value for the data array.
</description>
<tag name="@see">#path</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter.setPath(java.lang.String) -->
<method name="setPath"  public="true">
<description>
Set the &lt;code&gt;path&lt;/code&gt; property.&#xa; The path is exclusive to the array.&#xa; It manipulates entries in the array to filter a specific value for a nested array.&#xa; A JSON Path Expression, for example $.data.values.[0] will only display the [0] index value for the data array.
</description>
<tag name="@see">#path</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter.routeValue(javax.baja.sys.BString, javax.baja.sys.Context) -->
<method name="routeValue"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="message">
<type class="javax.baja.sys.BString"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.inbound.routing.RoutingFailedException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter.maxSlots -->
<field name="maxSlots"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;maxSlots&lt;/code&gt; property.
</description>
<tag name="@see">#getMaxSlots</tag>
<tag name="@see">#setMaxSlots</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter.createStatusValues -->
<field name="createStatusValues"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;createStatusValues&lt;/code&gt; property.
</description>
<tag name="@see">#getCreateStatusValues</tag>
<tag name="@see">#setCreateStatusValues</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter.clearOutputsOnEachMessage -->
<field name="clearOutputsOnEachMessage"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;clearOutputsOnEachMessage&lt;/code&gt; property.
</description>
<tag name="@see">#getClearOutputsOnEachMessage</tag>
<tag name="@see">#setClearOutputsOnEachMessage</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter.path -->
<field name="path"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;path&lt;/code&gt; property.&#xa; The path is exclusive to the array.&#xa; It manipulates entries in the array to filter a specific value for a nested array.&#xa; A JSON Path Expression, for example $.data.values.[0] will only display the [0] index value for the data array.
</description>
<tag name="@see">#getPath</tag>
<tag name="@see">#setPath</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter.learnMode -->
<field name="learnMode"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;learnMode&lt;/code&gt; property.
</description>
<tag name="@see">#getLearnMode</tag>
<tag name="@see">#setLearnMode</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonArrayRouter.LOGGER -->
<field name="LOGGER"  public="true" static="true" final="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

</class>
</bajadoc>
