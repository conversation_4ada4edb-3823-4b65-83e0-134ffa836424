<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.datatypes.BAceDownloadParams" name="BAceDownloadParams" packageName="com.tridium.ace.datatypes" public="true">
<description>
BAceDownloadParams - is container for properties passed in download application action
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">5/9/2017</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="addressList" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;addressList&lt;/code&gt; property.
</description>
<tag name="@see">#getAddressList</tag>
<tag name="@see">#setAddressList</tag>
</property>

<property name="fileName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;fileName&lt;/code&gt; property.
</description>
<tag name="@see">#getFileName</tag>
<tag name="@see">#setFileName</tag>
</property>

<property name="data" flags="">
<type class="javax.baja.sys.BBlob"/>
<description>
Slot for the &lt;code&gt;data&lt;/code&gt; property.
</description>
<tag name="@see">#getData</tag>
<tag name="@see">#setData</tag>
</property>

<property name="checkMissingModules" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;checkMissingModules&lt;/code&gt; property.
</description>
<tag name="@see">#getCheckMissingModules</tag>
<tag name="@see">#setCheckMissingModules</tag>
</property>

</class>
</bajadoc>
