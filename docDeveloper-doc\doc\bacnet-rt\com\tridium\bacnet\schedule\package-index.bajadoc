<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="com.tridium.bacnet.schedule">
<description/>
<class packageName="com.tridium.bacnet.schedule" name="BBacnetChangeTypeParm"><description>BBacnetChangeTypeParm is container for schedule properties that&#xa; might need to change to modify the schedule datatype.</description></class>
<class packageName="com.tridium.bacnet.schedule" name="BBacnetScheduleDeviceExt"><description>BBacnetScheduleDeviceExt.</description></class>
<class packageName="com.tridium.bacnet.schedule" name="BBacnetScheduleExport"><description>BBacnetScheduleExport is a child extension of a schedule that&#xa; is being exported to a BACnet device.</description></class>
<class packageName="com.tridium.bacnet.schedule" name="BBacnetScheduleImportExt"><description>BBacnetScheduleImportExt is a child extension of a schedule that&#xa; is being imported from a BACnet device.</description></class>
</package>
</bajadoc>
