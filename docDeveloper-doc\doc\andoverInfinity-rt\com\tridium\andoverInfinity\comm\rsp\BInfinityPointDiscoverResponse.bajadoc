<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.rsp.BInfinityPointDiscoverResponse" name="BInfinityPointDiscoverResponse" packageName="com.tridium.andoverInfinity.comm.rsp" public="true">
<description>
Used to pass point discoveryObjects to the point discovery job
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.comm.rsp.BDdfResponse"/>
</extends>
<implements>
<type class="com.tridium.ddf.comm.rsp.BIDdfDiscoverResponse"/>
</implements>
<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityPointDiscoverResponse() -->
<constructor name="BInfinityPointDiscoverResponse" public="true">
<description>
Default constructor
</description>
</constructor>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityPointDiscoverResponse(com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryLeaf[]) -->
<constructor name="BInfinityPointDiscoverResponse" public="true">
<parameter name="discoveryLeaves">
<type class="com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryLeaf" dimension="1"/>
<description/>
</parameter>
<description>
Constructor with discoveryLeaves
</description>
</constructor>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityPointDiscoverResponse.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityPointDiscoverResponse.parseDiscoveryObjects(javax.baja.sys.Context) -->
<method name="parseDiscoveryObjects"  public="true">
<description>
Discovery leaves have already been parsed by the BInfinityPointDiscoverRequest&#xa; class&#x27;s processReceive() and passed into this class via the constructor.&#xa; Just return the objects.
</description>
<tag name="@see">com.tridium.ddf.comm.rsp.BIDdfDiscoverResponse#parseDiscoveryObjects(javax.baja.sys.Context)</tag>
<parameter name="c">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="com.tridium.ddf.discover.BIDdfDiscoveryObject" dimension="1"/>
<description>
the &lt;code&gt;BIDdfDiscoveryObject&lt;/code&gt;&#x27;s as an array of &lt;code&gt;BInfinityPointDiscoveryLeaf&lt;/code&gt;
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityPointDiscoverResponse.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
