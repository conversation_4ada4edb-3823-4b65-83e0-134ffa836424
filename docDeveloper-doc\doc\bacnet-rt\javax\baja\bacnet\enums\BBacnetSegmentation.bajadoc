<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetSegmentation" name="BBacnetSegmentation" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetSegmentation represents the Bacnet Segmentation&#xa; enumeration.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 8$ $Date: 12/19/01 4:36:01 PM$</tag>
<tag name="@creation">10 Aug 00</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;segmentedBoth&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;segmentedTransmit&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;segmentedReceive&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;noSegmentation&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
<elementValue name="defaultValue">
<annotationValue kind="expr">
<expression>&#x22;noSegmentation&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetSegmentation.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetSegmentation"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetSegmentation.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetSegmentation"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetSegmentation.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetSegmentation.isSegmentedTransmit() -->
<method name="isSegmentedTransmit"  public="true">
<description>
Is segmented transmit supported?
</description>
<return>
<type class="boolean"/>
<description>
true for segmentedBoth and segmentedTransmit.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetSegmentation.isSegmentedReceive() -->
<method name="isSegmentedReceive"  public="true">
<description>
Is segmented receive supported?
</description>
<return>
<type class="boolean"/>
<description>
true for segmentedBoth and segmentedReceive.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetSegmentation.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetSegmentation.SEGMENTED_BOTH -->
<field name="SEGMENTED_BOTH"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for segmentedBoth.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSegmentation.SEGMENTED_TRANSMIT -->
<field name="SEGMENTED_TRANSMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for segmentedTransmit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSegmentation.SEGMENTED_RECEIVE -->
<field name="SEGMENTED_RECEIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for segmentedReceive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSegmentation.NO_SEGMENTATION -->
<field name="NO_SEGMENTATION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for noSegmentation.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSegmentation.segmentedBoth -->
<field name="segmentedBoth"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetSegmentation"/>
<description>
BBacnetSegmentation constant for segmentedBoth.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSegmentation.segmentedTransmit -->
<field name="segmentedTransmit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetSegmentation"/>
<description>
BBacnetSegmentation constant for segmentedTransmit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSegmentation.segmentedReceive -->
<field name="segmentedReceive"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetSegmentation"/>
<description>
BBacnetSegmentation constant for segmentedReceive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSegmentation.noSegmentation -->
<field name="noSegmentation"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetSegmentation"/>
<description>
BBacnetSegmentation constant for noSegmentation.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSegmentation.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetSegmentation"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetSegmentation.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
