<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm" name="BStringChangeOfStateFaultAlgorithm" packageName="javax.baja.alarm.ext.offnormal" public="true">
<description>
BStringChangeOfStateAlgorithm implements a change of&#xa; state alarm detection algorithm for text strings.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">17 June 10</tag>
<tag name="@version">$Revision: 5$ $Date: 3/16/11 10:13:52 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.alarm.ext.fault.BTwoStateFaultAlgorithm"/>
</extends>
<property name="expression" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;expression&lt;/code&gt; property.&#xa; A regular expression
</description>
<tag name="@see">#getExpression</tag>
<tag name="@see">#setExpression</tag>
</property>

<property name="normalOnMatch" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;normalOnMatch&lt;/code&gt; property.&#xa; If true, a match to the regular expression indicates a normal condition and&#xa; a non-match is fault.  If false, a match is fault and a non-match is&#xa; normal.
</description>
<tag name="@see">#getNormalOnMatch</tag>
<tag name="@see">#setNormalOnMatch</tag>
</property>

<property name="caseSensitive" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;caseSensitive&lt;/code&gt; property.&#xa; Should text matching  be text sensitive
</description>
<tag name="@see">#getCaseSensitive</tag>
<tag name="@see">#setCaseSensitive</tag>
</property>

<!-- javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm() -->
<constructor name="BStringChangeOfStateFaultAlgorithm" public="true">
<description/>
</constructor>

<!-- javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm.getExpression() -->
<method name="getExpression"  public="true">
<description>
Get the &lt;code&gt;expression&lt;/code&gt; property.&#xa; A regular expression
</description>
<tag name="@see">#expression</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm.setExpression(java.lang.String) -->
<method name="setExpression"  public="true">
<description>
Set the &lt;code&gt;expression&lt;/code&gt; property.&#xa; A regular expression
</description>
<tag name="@see">#expression</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm.getNormalOnMatch() -->
<method name="getNormalOnMatch"  public="true">
<description>
Get the &lt;code&gt;normalOnMatch&lt;/code&gt; property.&#xa; If true, a match to the regular expression indicates a normal condition and&#xa; a non-match is fault.  If false, a match is fault and a non-match is&#xa; normal.
</description>
<tag name="@see">#normalOnMatch</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm.setNormalOnMatch(boolean) -->
<method name="setNormalOnMatch"  public="true">
<description>
Set the &lt;code&gt;normalOnMatch&lt;/code&gt; property.&#xa; If true, a match to the regular expression indicates a normal condition and&#xa; a non-match is fault.  If false, a match is fault and a non-match is&#xa; normal.
</description>
<tag name="@see">#normalOnMatch</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm.getCaseSensitive() -->
<method name="getCaseSensitive"  public="true">
<description>
Get the &lt;code&gt;caseSensitive&lt;/code&gt; property.&#xa; Should text matching  be text sensitive
</description>
<tag name="@see">#caseSensitive</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm.setCaseSensitive(boolean) -->
<method name="setCaseSensitive"  public="true">
<description>
Set the &lt;code&gt;caseSensitive&lt;/code&gt; property.&#xa; Should text matching  be text sensitive
</description>
<tag name="@see">#caseSensitive</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm.isGrandparentLegal(javax.baja.sys.BComponent) -->
<method name="isGrandparentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
A BBooleanChangeOfStateAlgorithm&#x27;s grandparent must implement&#xa; the StringPoint interface
</description>
<parameter name="grandparent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm.started() -->
<method name="started"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm.isNormal(javax.baja.status.BStatusValue) -->
<method name="isNormal"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return true if the present value
</description>
<parameter name="o">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm.writeAlarmData(javax.baja.status.BStatusValue, java.util.Map) -->
<method name="writeAlarmData"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Write the key-value pairs defining alarm data for the&#xa;  alarm algorithm and state to the given Facets.
</description>
<parameter name="out">
<type class="javax.baja.status.BStatusValue"/>
<description>
The relevant control point.
</description>
</parameter>
<parameter name="map">
<parameterizedType class="java.util.Map">
<args>
</args>
</parameterizedType>
<description>
The map.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm.expression -->
<field name="expression"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;expression&lt;/code&gt; property.&#xa; A regular expression
</description>
<tag name="@see">#getExpression</tag>
<tag name="@see">#setExpression</tag>
</field>

<!-- javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm.normalOnMatch -->
<field name="normalOnMatch"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;normalOnMatch&lt;/code&gt; property.&#xa; If true, a match to the regular expression indicates a normal condition and&#xa; a non-match is fault.  If false, a match is fault and a non-match is&#xa; normal.
</description>
<tag name="@see">#getNormalOnMatch</tag>
<tag name="@see">#setNormalOnMatch</tag>
</field>

<!-- javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm.caseSensitive -->
<field name="caseSensitive"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;caseSensitive&lt;/code&gt; property.&#xa; Should text matching  be text sensitive
</description>
<tag name="@see">#getCaseSensitive</tag>
<tag name="@see">#setCaseSensitive</tag>
</field>

<!-- javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.alarm.ext.offnormal.BStringChangeOfStateFaultAlgorithm.log -->
<field name="log"  protected="true" static="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

</class>
</bajadoc>
