<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetRecipient" name="BBacnetRecipient" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
Recipient for an alarm to be exported to Bacnet.&#xa; &lt;p&gt;&#xa; BBacnetRecipient represents the BacnetRecipient&#xa; choice.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 3$ $Date: 12/10/01 9:26:16 AM$</tag>
<tag name="@creation">26 Oct 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<implements>
<type class="javax.baja.bacnet.util.worker.IBacnetAddress"/>
</implements>
<property name="choice" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</property>

<property name="device" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;device&lt;/code&gt; property.
</description>
<tag name="@see">#getDevice</tag>
<tag name="@see">#setDevice</tag>
</property>

<property name="address" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
Slot for the &lt;code&gt;address&lt;/code&gt; property.
</description>
<tag name="@see">#getAddress</tag>
<tag name="@see">#setAddress</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient() -->
<constructor name="BBacnetRecipient" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<constructor name="BBacnetRecipient" public="true">
<parameter name="device">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<description>
Object ID constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient(javax.baja.bacnet.datatypes.BBacnetAddress) -->
<constructor name="BBacnetRecipient" public="true">
<parameter name="address">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description/>
</parameter>
<description>
Address constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.getChoice() -->
<method name="getChoice"  public="true">
<description>
Get the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.setChoice(int) -->
<method name="setChoice"  public="true">
<description>
Set the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.getDevice() -->
<method name="getDevice"  public="true">
<description>
Get the &lt;code&gt;device&lt;/code&gt; property.
</description>
<tag name="@see">#device</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.setDevice(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="setDevice"  public="true">
<description>
Set the &lt;code&gt;device&lt;/code&gt; property.
</description>
<tag name="@see">#device</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.getAddress() -->
<method name="getAddress"  public="true">
<description>
Get the &lt;code&gt;address&lt;/code&gt; property.
</description>
<tag name="@see">#address</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.setAddress(javax.baja.bacnet.datatypes.BBacnetAddress) -->
<method name="setAddress"  public="true">
<description>
Set the &lt;code&gt;address&lt;/code&gt; property.
</description>
<tag name="@see">#address</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.isDevice() -->
<method name="isDevice"  public="true">
<description>
Is this a device-type recipient?
</description>
<return>
<type class="boolean"/>
<description>
true if device, false if address.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.isAddress() -->
<method name="isAddress"  public="true">
<description>
Is this a address-type recipient?
</description>
<return>
<type class="boolean"/>
<description>
true if address, false if device.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.getRecipient() -->
<method name="getRecipient"  public="true">
<description>
Get the recipient as a BValue.
</description>
<return>
<type class="javax.baja.sys.BValue"/>
<description>
the recipient.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.setRecipient(javax.baja.sys.BValue) -->
<method name="setRecipient"  public="true">
<description>
Set the recipient.
</description>
<parameter name="v">
<type class="javax.baja.sys.BValue"/>
<description>
the new recipient.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.setRecipient(javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="setRecipient"  public="true">
<description>
Set the recipient.
</description>
<parameter name="v">
<type class="javax.baja.sys.BValue"/>
<description>
the new recipient.
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
the context for the set.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.equivalent(java.lang.Object) -->
<method name="equivalent"  public="true">
<description/>
<parameter name="o">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.choice -->
<field name="choice"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.device -->
<field name="device"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;device&lt;/code&gt; property.
</description>
<tag name="@see">#getDevice</tag>
<tag name="@see">#setDevice</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.address -->
<field name="address"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;address&lt;/code&gt; property.
</description>
<tag name="@see">#getAddress</tag>
<tag name="@see">#setAddress</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.DEVICE_TAG -->
<field name="DEVICE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipient.ADDRESS_TAG -->
<field name="ADDRESS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
