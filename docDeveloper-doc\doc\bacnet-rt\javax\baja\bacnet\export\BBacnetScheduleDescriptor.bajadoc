<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetScheduleDescriptor" name="BBacnetScheduleDescriptor" packageName="javax.baja.bacnet.export" public="true" abstract="true">
<description>
BBacnetScheduleDescriptor exposes a Niagara schedule to Bacnet.
</description>
<tag name="@author"><PERSON> on 18 Aug 03</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.export.BIBacnetExportObject"/>
</implements>
<implements>
<type class="javax.baja.bacnet.export.BacnetPropertyListProvider"/>
</implements>
<property name="status" flags="trd">
<type class="javax.baja.status.BStatus"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.&#xa; the status for Niagara server-side behavior.
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</property>

<property name="faultCause" flags="rt">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.&#xa; Provides a description of a fault with server-side behavior.
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</property>

<property name="scheduleOrd" flags="d">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;scheduleOrd&lt;/code&gt; property.&#xa; ord to the schedule being exported.
</description>
<tag name="@see">#getScheduleOrd</tag>
<tag name="@see">#setScheduleOrd</tag>
</property>

<property name="objectId" flags="d">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this point is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="objectName" flags="d">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;objectName&lt;/code&gt; property.&#xa; the name by which this object is known to the Bacnet world.
</description>
<tag name="@see">#getObjectName</tag>
<tag name="@see">#setObjectName</tag>
</property>

<property name="listOfObjectPropertyReferences" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
<description>
Slot for the &lt;code&gt;listOfObjectPropertyReferences&lt;/code&gt; property.
</description>
<tag name="@see">#getListOfObjectPropertyReferences</tag>
<tag name="@see">#setListOfObjectPropertyReferences</tag>
</property>

<property name="priorityForWriting" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;priorityForWriting&lt;/code&gt; property.
</description>
<tag name="@see">#getPriorityForWriting</tag>
<tag name="@see">#setPriorityForWriting</tag>
</property>

<property name="description" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#getDescription</tag>
<tag name="@see">#setDescription</tag>
</property>

<property name="reliability" flags="tr">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description>
Slot for the &lt;code&gt;reliability&lt;/code&gt; property.&#xa; indicates misconfiguration
</description>
<tag name="@see">#getReliability</tag>
<tag name="@see">#setReliability</tag>
</property>

<action name="writePresentValue" flags="ha">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;writePresentValue&lt;/code&gt; action.
</description>
<tag name="@see">#writePresentValue()</tag>
</action>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor() -->
<constructor name="BBacnetScheduleDescriptor" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.getStatus() -->
<method name="getStatus"  public="true">
<description>
Get the &lt;code&gt;status&lt;/code&gt; property.&#xa; the status for Niagara server-side behavior.
</description>
<tag name="@see">#status</tag>
<return>
<type class="javax.baja.status.BStatus"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.setStatus(javax.baja.status.BStatus) -->
<method name="setStatus"  public="true">
<description>
Set the &lt;code&gt;status&lt;/code&gt; property.&#xa; the status for Niagara server-side behavior.
</description>
<tag name="@see">#status</tag>
<parameter name="v">
<type class="javax.baja.status.BStatus"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.getFaultCause() -->
<method name="getFaultCause"  public="true">
<description>
Get the &lt;code&gt;faultCause&lt;/code&gt; property.&#xa; Provides a description of a fault with server-side behavior.
</description>
<tag name="@see">#faultCause</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.setFaultCause(java.lang.String) -->
<method name="setFaultCause"  public="true">
<description>
Set the &lt;code&gt;faultCause&lt;/code&gt; property.&#xa; Provides a description of a fault with server-side behavior.
</description>
<tag name="@see">#faultCause</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.getScheduleOrd() -->
<method name="getScheduleOrd"  public="true">
<description>
Get the &lt;code&gt;scheduleOrd&lt;/code&gt; property.&#xa; ord to the schedule being exported.
</description>
<tag name="@see">#scheduleOrd</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.setScheduleOrd(javax.baja.naming.BOrd) -->
<method name="setScheduleOrd"  public="true">
<description>
Set the &lt;code&gt;scheduleOrd&lt;/code&gt; property.&#xa; ord to the schedule being exported.
</description>
<tag name="@see">#scheduleOrd</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.getObjectId() -->
<method name="getObjectId"  public="true">
<description>
Get the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this point is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#objectId</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.setObjectId(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="setObjectId"  public="true">
<description>
Set the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this point is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#objectId</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.getObjectName() -->
<method name="getObjectName"  public="true">
<description>
Get the &lt;code&gt;objectName&lt;/code&gt; property.&#xa; the name by which this object is known to the Bacnet world.
</description>
<tag name="@see">#objectName</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.setObjectName(java.lang.String) -->
<method name="setObjectName"  public="true">
<description>
Set the &lt;code&gt;objectName&lt;/code&gt; property.&#xa; the name by which this object is known to the Bacnet world.
</description>
<tag name="@see">#objectName</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.getListOfObjectPropertyReferences() -->
<method name="getListOfObjectPropertyReferences"  public="true">
<description>
Get the &lt;code&gt;listOfObjectPropertyReferences&lt;/code&gt; property.
</description>
<tag name="@see">#listOfObjectPropertyReferences</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.setListOfObjectPropertyReferences(javax.baja.bacnet.datatypes.BBacnetListOf) -->
<method name="setListOfObjectPropertyReferences"  public="true">
<description>
Set the &lt;code&gt;listOfObjectPropertyReferences&lt;/code&gt; property.
</description>
<tag name="@see">#listOfObjectPropertyReferences</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.getPriorityForWriting() -->
<method name="getPriorityForWriting"  public="true">
<description>
Get the &lt;code&gt;priorityForWriting&lt;/code&gt; property.
</description>
<tag name="@see">#priorityForWriting</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.setPriorityForWriting(int) -->
<method name="setPriorityForWriting"  public="true">
<description>
Set the &lt;code&gt;priorityForWriting&lt;/code&gt; property.
</description>
<tag name="@see">#priorityForWriting</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.getDescription() -->
<method name="getDescription"  public="true">
<description>
Get the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#description</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.setDescription(java.lang.String) -->
<method name="setDescription"  public="true">
<description>
Set the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#description</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.getReliability() -->
<method name="getReliability"  public="true">
<description>
Get the &lt;code&gt;reliability&lt;/code&gt; property.&#xa; indicates misconfiguration
</description>
<tag name="@see">#reliability</tag>
<return>
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.setReliability(javax.baja.bacnet.enums.BBacnetReliability) -->
<method name="setReliability"  public="true">
<description>
Set the &lt;code&gt;reliability&lt;/code&gt; property.&#xa; indicates misconfiguration
</description>
<tag name="@see">#reliability</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.writePresentValue() -->
<method name="writePresentValue"  public="true">
<description>
Invoke the &lt;code&gt;writePresentValue&lt;/code&gt; action.
</description>
<tag name="@see">#writePresentValue</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.started() -->
<method name="started"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Register with the Bacnet service when this component is started.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.stopped() -->
<method name="stopped"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Unregister with the Bacnet service when this component is stopped.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Property Changed.&#xa; If the objectId changes, make sure the new ID is not already in use.&#xa; If it is, reset it to the current value.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.atSteadyState() -->
<method name="atSteadyState"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.getSlotFacets(javax.baja.sys.Slot) -->
<method name="getSlotFacets"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get slot facets.
</description>
<parameter name="s">
<type class="javax.baja.sys.Slot"/>
<description>
slot
</description>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
<description>
the appropriate slot facets.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.post(javax.baja.sys.Action, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="post"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="action">
<type class="javax.baja.sys.Action"/>
</parameter>
<parameter name="arg">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.util.IFuture"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.doWritePresentValue() -->
<method name="doWritePresentValue"  public="true" abstract="true">
<description>
Write the present value of the schedule to non-Present_Value&#xa; target properties, and to any external targets.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.getObject() -->
<method name="getObject"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the exported object.
</description>
<return>
<type class="javax.baja.sys.BObject"/>
<description>
the actual exported object by resolving the object ord.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.getObjectOrd() -->
<method name="getObjectOrd"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the BOrd to the exported object.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.setObjectOrd(javax.baja.naming.BOrd, javax.baja.sys.Context) -->
<method name="setObjectOrd"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the BOrd to the exported object.
</description>
<parameter name="objectOrd">
<type class="javax.baja.naming.BOrd"/>
<description/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.checkConfiguration() -->
<method name="checkConfiguration"  public="true" synchronized="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Check the configuration of this object.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.readProperty(javax.baja.bacnet.io.PropertyReference) -->
<method name="readProperty"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the value of a property.
</description>
<parameter name="ref">
<type class="javax.baja.bacnet.io.PropertyReference"/>
<description>
the PropertyReference containing id and index.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.RejectException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.readPropertyMultiple(javax.baja.bacnet.io.PropertyReference[]) -->
<method name="readPropertyMultiple"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Read the value of multiple Bacnet properties.
</description>
<parameter name="refs">
<type class="javax.baja.bacnet.io.PropertyReference" dimension="1"/>
<description>
the list of property references.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue" dimension="1"/>
<description>
an array of PropertyValues.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.RejectException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.readRange(javax.baja.bacnet.io.RangeReference) -->
<method name="readRange"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Read the specified range of values of a compound property.
</description>
<parameter name="rangeReference">
<type class="javax.baja.bacnet.io.RangeReference"/>
<description>
the range reference describing the requested range.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.RangeData"/>
<description>
a byte array containing the encoded range.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.RejectException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.writeProperty(javax.baja.bacnet.io.PropertyValue) -->
<method name="writeProperty"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="val">
<type class="javax.baja.bacnet.io.PropertyValue"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.addListElements(javax.baja.bacnet.io.PropertyValue) -->
<method name="addListElements"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Add list elements.
</description>
<parameter name="propertyValue">
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
the PropertyValue containing the propertyId,&#xa;                      propertyArrayIndex, and the encoded list elements.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ChangeListError"/>
<description>
a ChangeListError if unable to add any elements,&#xa; or null if ok.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.removeListElements(javax.baja.bacnet.io.PropertyValue) -->
<method name="removeListElements"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Remove list elements.
</description>
<parameter name="propertyValue">
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
the PropertyValue containing the propertyId,&#xa;                      propertyArrayIndex, and the encoded list elements.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ChangeListError"/>
<description>
a ChangeListError if unable to remove any elements,&#xa; or null if ok.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.readProperty(int, int) -->
<method name="readProperty"  protected="true">
<description>
Get the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.writeProperty(int, int, byte[], int) -->
<method name="writeProperty"  protected="true">
<description>
Set the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.checkScheduleConfiguration() -->
<method name="checkScheduleConfiguration"  protected="true">
<description>
Override point for subclasses to provide additional configuration&#xa; constraints to allow point export.  Default implementation returns true.
</description>
<return>
<type class="boolean"/>
<description>
true if configuration is ok, false otherwise.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.validate() -->
<method name="validate"  protected="true">
<description>
Validate the schedule&#x27;s configuration.  For schedules, an event&#xa; cannot have a value with a null status.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.writeScheduleDefaultValue(byte[]) -->
<method name="writeScheduleDefaultValue"  protected="true">
<description>
Write the default value for the schedule
</description>
<parameter name="val">
<type class="byte" dimension="1"/>
<description/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if no error, otherwise error code
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.doWriteScheduleDefaultValue(com.tridium.bacnet.asn.AsnInputStream, int) -->
<method name="doWriteScheduleDefaultValue"  protected="true" abstract="true">
<description>
Needs to be overridden by typed schedule descriptors
</description>
<parameter name="asnInputStream">
<type class="com.tridium.bacnet.asn.AsnInputStream"/>
<description/>
</parameter>
<parameter name="applicationTag">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if no error, otherwise errortype
</description>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
To String.
</description>
<parameter name="c">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.getSchedule() -->
<method name="getSchedule"  protected="true" final="true">
<description>
Get the schedule.
</description>
<return>
<type class="javax.baja.schedule.BWeeklySchedule"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.markConfigurationError(java.lang.String) -->
<method name="markConfigurationError"  public="true">
<description>
Mark the schedule descriptor to `fault` state.
</description>
<parameter name="faultCause">
<type class="java.lang.String"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.markNoFaultDetected() -->
<method name="markNoFaultDetected"  public="true">
<description>
Mark the schedule descriptor to `noFaultDetected` state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.getLastEffectiveValue() -->
<method name="getLastEffectiveValue"  public="true">
<description>
If the dynamic property LAST_EFFECTIVE_VALUE exists, its value is returned.
</description>
<return>
<type class="javax.baja.status.BStatusValue"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.setLastEffectiveValue(javax.baja.status.BStatusValue) -->
<method name="setLastEffectiveValue"  public="true">
<description>
Creates the LAST_EFFECTIVE_VALUE dynamic property.
</description>
<parameter name="o">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.replaceCurrentBacnetSchedule(javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="replaceCurrentBacnetSchedule"  protected="true">
<description>
Replace the current bacnet schedule using a new schedule that uses the passed&#xa; initial values.
</description>
<parameter name="initialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="javax.baja.bacnet.export.BBacnetScheduleDescriptor"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.isEqual(int, int) -->
<method name="isEqual"  protected="true">
<description>
Are the ASN type of Referenced object and ASN Type of Schedule Type are equal?
</description>
<parameter name="ansTypeOfRefObj">
<type class="int"/>
<description>
ASN Type of DOPR
</description>
</parameter>
<parameter name="asnTypeOfSchedule">
<type class="int"/>
<description>
ASN Type of Schedule Data Type
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if equal, otherwise false
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.isFatalFault() -->
<method name="isFatalFault"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is this component in a fatal fault condition?
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.getPropertyList() -->
<method name="getPropertyList"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.status -->
<field name="status"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.&#xa; the status for Niagara server-side behavior.
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.faultCause -->
<field name="faultCause"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.&#xa; Provides a description of a fault with server-side behavior.
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.scheduleOrd -->
<field name="scheduleOrd"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;scheduleOrd&lt;/code&gt; property.&#xa; ord to the schedule being exported.
</description>
<tag name="@see">#getScheduleOrd</tag>
<tag name="@see">#setScheduleOrd</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this point is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.objectName -->
<field name="objectName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectName&lt;/code&gt; property.&#xa; the name by which this object is known to the Bacnet world.
</description>
<tag name="@see">#getObjectName</tag>
<tag name="@see">#setObjectName</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.listOfObjectPropertyReferences -->
<field name="listOfObjectPropertyReferences"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;listOfObjectPropertyReferences&lt;/code&gt; property.
</description>
<tag name="@see">#getListOfObjectPropertyReferences</tag>
<tag name="@see">#setListOfObjectPropertyReferences</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.priorityForWriting -->
<field name="priorityForWriting"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;priorityForWriting&lt;/code&gt; property.
</description>
<tag name="@see">#getPriorityForWriting</tag>
<tag name="@see">#setPriorityForWriting</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.description -->
<field name="description"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#getDescription</tag>
<tag name="@see">#setDescription</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.reliability -->
<field name="reliability"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;reliability&lt;/code&gt; property.&#xa; indicates misconfiguration
</description>
<tag name="@see">#getReliability</tag>
<tag name="@see">#setReliability</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.writePresentValue -->
<field name="writePresentValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;writePresentValue&lt;/code&gt; action.
</description>
<tag name="@see">#writePresentValue()</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.export.BBacnetScheduleDescriptor.LAST_EFFECTIVE_VALUE -->
<field name="LAST_EFFECTIVE_VALUE"  protected="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

</class>
</bajadoc>
