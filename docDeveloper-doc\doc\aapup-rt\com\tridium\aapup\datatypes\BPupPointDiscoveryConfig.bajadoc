<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.datatypes.BPupPointDiscoveryConfig" name="BPupPointDiscoveryConfig" packageName="com.tridium.aapup.datatypes" public="true">
<description>
Pup Point Discovery Config, used to govern what&#xa; channels and attribs learned during the learn process
</description>
<tag name="@author">C<PERSON><PERSON></tag>
<tag name="@creation">7/15/2005 7:41AM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.90</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="cm" flags="hr">
<type class="int"/>
<description>
Slot for the &lt;code&gt;cm&lt;/code&gt; property.&#xa; controller manufacturer, should equal the &#x22;CM&#x22; attribute of channel FF00.&#xa; &#x27;255&#x27; is American Automatrix.&#xa; A value of -1 means manufacturer has not been read from the controller.
</description>
<tag name="@see">#getCm</tag>
<tag name="@see">#setCm</tag>
</property>

<property name="ct" flags="hr">
<type class="int"/>
<description>
Slot for the &lt;code&gt;ct&lt;/code&gt; property.&#xa; controller type, should equal the &#x22;CT&#x22; parameter of channel FF00.&#xa; value of 0 means not controller type has not been read from the controller.
</description>
<tag name="@see">#getCt</tag>
<tag name="@see">#setCt</tag>
</property>

<property name="channelList" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;channelList&lt;/code&gt; property.&#xa; an xml file which contains the mapping of cm + ct to a channel list
</description>
<tag name="@see">#getChannelList</tag>
<tag name="@see">#setChannelList</tag>
</property>

<property name="maxChannel" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;maxChannel&lt;/code&gt; property.&#xa; the highest numbered channel to learn
</description>
<tag name="@see">#getMaxChannel</tag>
<tag name="@see">#setMaxChannel</tag>
</property>

<property name="minChannel" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;minChannel&lt;/code&gt; property.&#xa; the lowest numbered channel to learn
</description>
<tag name="@see">#getMinChannel</tag>
<tag name="@see">#setMinChannel</tag>
</property>

</class>
</bajadoc>
