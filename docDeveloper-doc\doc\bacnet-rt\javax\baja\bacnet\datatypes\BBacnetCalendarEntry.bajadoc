<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetCalendarEntry" name="BBacnetCalendarEntry" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
This class represents the Bacnet CalendarEntry Choice.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">10 Jun 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="choice" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</property>

<property name="date" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
<description>
Slot for the &lt;code&gt;date&lt;/code&gt; property.
</description>
<tag name="@see">#getDate</tag>
<tag name="@see">#setDate</tag>
</property>

<property name="dateRange" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDateRange"/>
<description>
Slot for the &lt;code&gt;dateRange&lt;/code&gt; property.
</description>
<tag name="@see">#getDateRange</tag>
<tag name="@see">#setDateRange</tag>
</property>

<property name="weekNDay" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetOctetString"/>
<description>
Slot for the &lt;code&gt;weekNDay&lt;/code&gt; property.
</description>
<tag name="@see">#getWeekNDay</tag>
<tag name="@see">#setWeekNDay</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry() -->
<constructor name="BBacnetCalendarEntry" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry(javax.baja.bacnet.datatypes.BBacnetDate) -->
<constructor name="BBacnetCalendarEntry" public="true">
<parameter name="date">
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
<description/>
</parameter>
<description>
Date constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry(javax.baja.bacnet.datatypes.BBacnetDateRange) -->
<constructor name="BBacnetCalendarEntry" public="true">
<parameter name="dateRange">
<type class="javax.baja.bacnet.datatypes.BBacnetDateRange"/>
<description/>
</parameter>
<description>
DateRange constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry(javax.baja.bacnet.datatypes.BBacnetOctetString) -->
<constructor name="BBacnetCalendarEntry" public="true">
<parameter name="weekNDay">
<type class="javax.baja.bacnet.datatypes.BBacnetOctetString"/>
<description/>
</parameter>
<description>
OctetString constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.getChoice() -->
<method name="getChoice"  public="true">
<description>
Get the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.setChoice(int) -->
<method name="setChoice"  public="true">
<description>
Set the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.getDate() -->
<method name="getDate"  public="true">
<description>
Get the &lt;code&gt;date&lt;/code&gt; property.
</description>
<tag name="@see">#date</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.setDate(javax.baja.bacnet.datatypes.BBacnetDate) -->
<method name="setDate"  public="true">
<description>
Set the &lt;code&gt;date&lt;/code&gt; property.
</description>
<tag name="@see">#date</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.getDateRange() -->
<method name="getDateRange"  public="true">
<description>
Get the &lt;code&gt;dateRange&lt;/code&gt; property.
</description>
<tag name="@see">#dateRange</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDateRange"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.setDateRange(javax.baja.bacnet.datatypes.BBacnetDateRange) -->
<method name="setDateRange"  public="true">
<description>
Set the &lt;code&gt;dateRange&lt;/code&gt; property.
</description>
<tag name="@see">#dateRange</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDateRange"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.getWeekNDay() -->
<method name="getWeekNDay"  public="true">
<description>
Get the &lt;code&gt;weekNDay&lt;/code&gt; property.
</description>
<tag name="@see">#weekNDay</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetOctetString"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.setWeekNDay(javax.baja.bacnet.datatypes.BBacnetOctetString) -->
<method name="setWeekNDay"  public="true">
<description>
Set the &lt;code&gt;weekNDay&lt;/code&gt; property.
</description>
<tag name="@see">#weekNDay</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetOctetString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.getCalendarEntry() -->
<method name="getCalendarEntry"  public="true">
<description>
Get the calendar entry, returned as a BValue.
</description>
<return>
<type class="javax.baja.sys.BValue"/>
<description>
the slot value for the current value of choice.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.setCalendarEntry(javax.baja.sys.BValue) -->
<method name="setCalendarEntry"  public="true">
<description>
Set the calendar entry.
</description>
<parameter name="e">
<type class="javax.baja.sys.BValue"/>
<description>
the new calendar entry.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.setCalendarEntry(javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="setCalendarEntry"  public="true">
<description>
Set the calendar entry.
</description>
<parameter name="e">
<type class="javax.baja.sys.BValue"/>
<description>
the new calendar entry.
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
the context for the set.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.isActive(javax.baja.sys.BAbsTime) -->
<method name="isActive"  public="true">
<description>
Is this calendar entry active at the specified time?
</description>
<parameter name="at">
<type class="javax.baja.sys.BAbsTime"/>
<description>
the BAbsTime to check.
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the calendar entry is active at this time.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.nextDate(javax.baja.sys.BAbsTime) -->
<method name="nextDate"  public="true">
<description>
Get the next time after the given time at which this calendar entry&#xa; becomes active.  The returned BAbsTime will be 00:00:00.000&#xa; (12:00 midnight) on the returned date.&#xa; If the calendar entry will never become active after the given date,&#xa; this returns null.
</description>
<parameter name="time">
<type class="javax.baja.sys.BAbsTime"/>
<description>
the starting time.
</description>
</parameter>
<return>
<type class="javax.baja.sys.BAbsTime"/>
<description>
a BAbsTime at which this calendar entry will become active.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.choice -->
<field name="choice"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.date -->
<field name="date"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;date&lt;/code&gt; property.
</description>
<tag name="@see">#getDate</tag>
<tag name="@see">#setDate</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.dateRange -->
<field name="dateRange"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;dateRange&lt;/code&gt; property.
</description>
<tag name="@see">#getDateRange</tag>
<tag name="@see">#setDateRange</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.weekNDay -->
<field name="weekNDay"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;weekNDay&lt;/code&gt; property.
</description>
<tag name="@see">#getWeekNDay</tag>
<tag name="@see">#setWeekNDay</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.DATE_TAG -->
<field name="DATE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
BBacnetCalendarEntry Asn Context Tags&#xa; See Bacnet Clause 21
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.DATE_RANGE_TAG -->
<field name="DATE_RANGE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.WEEK_N_DAY_TAG -->
<field name="WEEK_N_DAY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetCalendarEntry.MAX_ENCODED_SIZE -->
<field name="MAX_ENCODED_SIZE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
