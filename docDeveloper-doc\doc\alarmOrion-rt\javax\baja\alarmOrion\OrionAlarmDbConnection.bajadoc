<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarmOrion" runtimeProfile="rt" qualifiedName="javax.baja.alarmOrion.OrionAlarmDbConnection" name="OrionAlarmDbConnection" packageName="javax.baja.alarmOrion" public="true">
<description>
Connection to a BOrionAlarmDatabase.
</description>
<tag name="@since">Niagara 4.0</tag>
<extends>
<type class="javax.baja.alarm.AlarmDbConnection"/>
</extends>
<!-- javax.baja.alarmOrion.OrionAlarmDbConnection(javax.baja.alarmOrion.BOrionAlarmDatabase, com.tridium.orion.OrionSession) -->
<constructor name="OrionAlarmDbConnection" public="true">
<parameter name="db">
<type class="javax.baja.alarmOrion.BOrionAlarmDatabase"/>
</parameter>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.getAlarmDatabase() -->
<method name="getAlarmDatabase"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
return a reference to the Alarm Database that we&#x27;re connected to.
</description>
<return>
<type class="javax.baja.alarm.BAlarmDatabase"/>
</return>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.getOrionSession() -->
<method name="getOrionSession"  public="true">
<description>
Return the underlaying OrionSession
</description>
<return>
<type class="com.tridium.orion.OrionSession"/>
</return>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.flush() -->
<method name="flush"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Commit any outstanding changes.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.close() -->
<method name="close"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Close the connection.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.append(javax.baja.alarm.BAlarmRecord) -->
<method name="append"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Archive alarm callback.&#xa; Also update the totalAlarmCount, unackedAlarmCount, openAlarmCount,&#xa; and inAlarmCount properties on the record&#x27;s BAlarmClass.
</description>
<parameter name="alarmRecord">
<type class="javax.baja.alarm.BAlarmRecord"/>
<description>
The alarm record to store.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
<throws>
<type class="javax.baja.alarm.AlarmException"/>
</throws>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.update(javax.baja.alarm.BAlarmRecord) -->
<method name="update"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Update an alarm with new information.&#xa; Also update the totalAlarmCount, unackedAlarmCount, openAlarmCount,&#xa; and inAlarmCount properties on the record&#x27;s BAlarmClass.
</description>
<parameter name="alarmRecord">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
<throws>
<type class="javax.baja.alarm.AlarmException"/>
</throws>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.getRecordCount() -->
<method name="getRecordCount"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the number of records in the database.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.getOpenRecordCount(javax.baja.naming.BOrdList) -->
<method name="getOpenRecordCount"  public="true">
<description/>
<tag name="@since">Niagara 4.4</tag>
<parameter name="alarmSource">
<type class="javax.baja.naming.BOrdList"/>
<description/>
</parameter>
<return>
<type class="int"/>
<description/>
</return>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.getOpenAckPendingRecordCount(javax.baja.naming.BOrdList) -->
<method name="getOpenAckPendingRecordCount"  public="true">
<description/>
<tag name="@since">Niagara 4.4</tag>
<parameter name="alarmSource">
<type class="javax.baja.naming.BOrdList"/>
<description/>
</parameter>
<return>
<type class="int"/>
<description/>
</return>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.getOpenUnackedRecordCount(javax.baja.naming.BOrdList) -->
<method name="getOpenUnackedRecordCount"  public="true">
<description/>
<tag name="@since">Niagara 4.4</tag>
<parameter name="alarmSource">
<type class="javax.baja.naming.BOrdList"/>
<description/>
</parameter>
<return>
<type class="int"/>
<description/>
</return>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.getOpenAckedRecordCount(javax.baja.naming.BOrdList) -->
<method name="getOpenAckedRecordCount"  public="true">
<description/>
<tag name="@since">Niagara 4.4</tag>
<parameter name="alarmSource">
<type class="javax.baja.naming.BOrdList"/>
<description/>
</parameter>
<return>
<type class="int"/>
<description/>
</return>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.getRecord(javax.baja.util.BUuid) -->
<method name="getRecord"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get a record by uuid.
</description>
<parameter name="uuid">
<type class="javax.baja.util.BUuid"/>
<description>
The uuid of the record to retrieve.
</description>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmRecord"/>
<description>
Returns the target record or null if no&#xa;   record is found with a matching uuid.
</description>
</return>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.getRecord(javax.baja.util.BUuid, com.tridium.orion.OrionSession) -->
<method name="getRecord"  public="true">
<description/>
<parameter name="uuid">
<type class="javax.baja.util.BUuid"/>
</parameter>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmRecord"/>
</return>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.getOrionRecord(javax.baja.util.BUuid) -->
<method name="getOrionRecord"  public="true">
<description/>
<parameter name="uuid">
<type class="javax.baja.util.BUuid"/>
</parameter>
<return>
<type class="javax.baja.alarmOrion.BOrionAlarmRecord"/>
</return>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.getOrionRecord(javax.baja.util.BUuid, com.tridium.orion.OrionSession) -->
<method name="getOrionRecord"  public="true">
<description/>
<parameter name="uuid">
<type class="javax.baja.util.BUuid"/>
</parameter>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="javax.baja.alarmOrion.BOrionAlarmRecord"/>
</return>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.getOpenAlarms() -->
<method name="getOpenAlarms"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the open alarms in the database.  An alarm is considered open when:&#xa; &lt;p&gt;&#xa; not (acked and normal) and not (acked and alert)&#xa; &lt;p&gt;&#xa; The AlarmRefrence may be reused so to store a copy of the BAlarmRecord, you must use newCopy().
</description>
<return>
<parameterizedType class="javax.baja.sys.Cursor">
<args>
<type class="javax.baja.alarm.BAlarmRecord"/>
</args>
</parameterizedType>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.getAckPendingAlarms() -->
<method name="getAckPendingAlarms"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the ackPending alarms in the database.&#xa; &lt;p&gt;&#xa; The AlarmRefrence may be reused so to store a copy of the BAlarmRecord, you must use newCopy().
</description>
<return>
<parameterizedType class="javax.baja.sys.Cursor">
<args>
<type class="javax.baja.alarm.BAlarmRecord"/>
</args>
</parameterizedType>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.getOpenAlarmSources() -->
<method name="getOpenAlarmSources"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<parameterizedType class="javax.baja.sys.Cursor">
<args>
<type class="javax.baja.alarm.BAlarmSource"/>
</args>
</parameterizedType>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.getAlarmsForSource(javax.baja.naming.BOrdList) -->
<method name="getAlarmsForSource"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get all alarms for the specified source.  The result&#xa; will be sorted in timestamp order with the oldest&#xa; alarm first.&#xa; &lt;p&gt;&#xa; The AlarmRefrence may be reused so to store a copy of the BAlarmRecord, you must use newCopy().
</description>
<parameter name="alarmSource">
<type class="javax.baja.naming.BOrdList"/>
</parameter>
<return>
<parameterizedType class="javax.baja.sys.Cursor">
<args>
<type class="javax.baja.alarm.BAlarmRecord"/>
</args>
</parameterizedType>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.getOpenAlarmsForSource(javax.baja.naming.BOrdList) -->
<method name="getOpenAlarmsForSource"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="alarmSource">
<type class="javax.baja.naming.BOrdList"/>
</parameter>
<return>
<parameterizedType class="javax.baja.sys.Cursor">
<args>
<type class="javax.baja.alarm.BAlarmRecord"/>
</args>
</parameterizedType>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.scan() -->
<method name="scan"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get a cursor for iterating through all record in the database.&#xa; &lt;p&gt;&#xa; The AlarmReference may be reused so to store a copy of the BAlarmRecord, you must use newCopy().
</description>
<return>
<parameterizedType class="javax.baja.sys.Cursor">
<args>
<type class="javax.baja.alarm.BAlarmRecord"/>
</args>
</parameterizedType>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
<throws>
<type class="javax.baja.alarm.AlarmException"/>
</throws>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.timeQuery(javax.baja.sys.BAbsTime, javax.baja.sys.BAbsTime) -->
<method name="timeQuery"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get a cursor for iterating through all records between&#xa; the specified start and end times inclusive.&#xa; &lt;p&gt;&#xa; The AlarmRefrence may be reused so to store a copy of the BAlarmRecord, you must use newCopy().
</description>
<parameter name="start">
<type class="javax.baja.sys.BAbsTime"/>
<description>
The earliest timestamp that will be included&#xa;   in the result.
</description>
</parameter>
<parameter name="end">
<type class="javax.baja.sys.BAbsTime"/>
<description>
The latest timestamp that will be included in&#xa;  the result.
</description>
</parameter>
<return>
<parameterizedType class="javax.baja.sys.Cursor">
<args>
<type class="javax.baja.alarm.BAlarmRecord"/>
</args>
</parameterizedType>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
<throws>
<type class="javax.baja.alarm.AlarmException"/>
</throws>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.doBqlQuery(javax.baja.bql.BqlQuery) -->
<method name="doBqlQuery"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="bql">
<type class="javax.baja.bql.BqlQuery"/>
</parameter>
<return>
<parameterizedType class="javax.baja.sys.Cursor">
<args>
<type class="javax.baja.alarm.BAlarmRecord"/>
</args>
</parameterizedType>
</return>
<throws>
<type class="javax.baja.alarm.AlarmException"/>
</throws>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.clearAllRecords(javax.baja.sys.Context) -->
<method name="clearAllRecords"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Clear all records from the database.&#xa; Also update the totalAlarmCount, unackedAlarmCount, openAlarmCount,&#xa; and inAlarmCount properties all BAlarmClasses.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.clearOldRecords(javax.baja.sys.BAbsTime, javax.baja.sys.Context) -->
<method name="clearOldRecords"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Clear all records with a timestamp before the specified time.&#xa; Also update the totalAlarmCount, unackedAlarmCount, openAlarmCount,&#xa; and inAlarmCount properties all BAlarmClasses.
</description>
<parameter name="before">
<type class="javax.baja.sys.BAbsTime"/>
<description>
The earliest time to keep in the result.  Records&#xa;   before this time will be removed.
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarmOrion.OrionAlarmDbConnection.clearRecord(javax.baja.util.BUuid, javax.baja.sys.Context) -->
<method name="clearRecord"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Clear the record with the given uuid.&#xa; Also update the totalAlarmCount, unackedAlarmCount, openAlarmCount,&#xa; and inAlarmCount properties all BAlarmClasses.
</description>
<parameter name="uuid">
<type class="javax.baja.util.BUuid"/>
<description>
the Uuid of the Alarm Record to remove from the database.
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

</class>
</bajadoc>
