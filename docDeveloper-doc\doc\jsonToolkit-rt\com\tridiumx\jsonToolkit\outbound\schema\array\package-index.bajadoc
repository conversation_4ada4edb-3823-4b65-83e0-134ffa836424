<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.outbound.schema.array">
<description/>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.array" name="BJsonSchemaArray"><description>A named array container for other &lt;code&gt;<see ref="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaMember">BJsonSchemaMember</see>&lt;/code&gt; children.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.array" name="BJsonSchemaBoundArray"><description>A json array which is bound by a ord to a target station component/slot&#xa; and whose internal values are the values of the slots inside that target.</description></class>
</package>
</bajadoc>
