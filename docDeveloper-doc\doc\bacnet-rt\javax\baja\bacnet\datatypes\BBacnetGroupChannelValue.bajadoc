<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetGroupChannelValue" name="BBacnetGroupChannelValue" packageName="javax.baja.bacnet.datatypes" public="true">
<description>
BBacnetGroupChannelValue represents the BACnetGroupChannelValue&#xa; sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="channel" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;channel&lt;/code&gt; property.
</description>
<tag name="@see">#getChannel</tag>
<tag name="@see">#setChannel</tag>
</property>

<property name="value" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetChannelValue"/>
<description>
Slot for the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#getValue</tag>
<tag name="@see">#setValue</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetGroupChannelValue() -->
<constructor name="BBacnetGroupChannelValue" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetGroupChannelValue(int, java.lang.Integer, javax.baja.bacnet.datatypes.BBacnetChannelValue) -->
<constructor name="BBacnetGroupChannelValue" public="true">
<parameter name="channel">
<type class="int"/>
</parameter>
<parameter name="overridingPriority">
<type class="java.lang.Integer"/>
</parameter>
<parameter name="value">
<type class="javax.baja.bacnet.datatypes.BBacnetChannelValue"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetGroupChannelValue.getChannel() -->
<method name="getChannel"  public="true">
<description>
Get the &lt;code&gt;channel&lt;/code&gt; property.
</description>
<tag name="@see">#channel</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetGroupChannelValue.setChannel(int) -->
<method name="setChannel"  public="true">
<description>
Set the &lt;code&gt;channel&lt;/code&gt; property.
</description>
<tag name="@see">#channel</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetGroupChannelValue.getValue() -->
<method name="getValue"  public="true">
<description>
Get the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#value</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetChannelValue"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetGroupChannelValue.setValue(javax.baja.bacnet.datatypes.BBacnetChannelValue) -->
<method name="setValue"  public="true">
<description>
Set the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#value</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetChannelValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetGroupChannelValue.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetGroupChannelValue.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetGroupChannelValue.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetGroupChannelValue.getOverridingPriority() -->
<method name="getOverridingPriority"  public="true">
<description/>
<return>
<type class="java.lang.Integer"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetGroupChannelValue.setOverridingPriority(java.lang.Integer) -->
<method name="setOverridingPriority"  public="true">
<description/>
<parameter name="overridingPriority">
<type class="java.lang.Integer"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetGroupChannelValue.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetGroupChannelValue.channel -->
<field name="channel"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;channel&lt;/code&gt; property.
</description>
<tag name="@see">#getChannel</tag>
<tag name="@see">#setChannel</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetGroupChannelValue.value -->
<field name="value"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#getValue</tag>
<tag name="@see">#setValue</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetGroupChannelValue.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetGroupChannelValue.CHANNEL_TAG -->
<field name="CHANNEL_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetGroupChannelValue.OVERRIDING_PRIORITY_TAG -->
<field name="OVERRIDING_PRIORITY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
