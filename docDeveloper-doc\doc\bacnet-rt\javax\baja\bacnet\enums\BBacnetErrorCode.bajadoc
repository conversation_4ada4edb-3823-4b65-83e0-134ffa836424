<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetErrorCode" name="BBacnetErrorCode" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetErrorCode represents the error-code portion of the&#xa; BACnet Error sequence.&#xa; &lt;p&gt;&#xa; BBacnetErrorCode is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-255 are reserved for use by ASHRAE.&#xa; Values from 256-65535 (0xFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Craig Gemmill</tag>
<tag name="@version">$Revision: 7$ $Date: 12/19/01 4:35:58 PM$</tag>
<tag name="@creation">10 Aug 00</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;other&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;authenticationFailed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;configurationInProgress&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deviceBusy&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;dynamicCreationNotSupported&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;fileAccessDenied&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;incompatibleSecurityLevels&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;inconsistentParameters&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;inconsistentSelectionCriterion&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;invalidDataType&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;invalidFileAccessMethod&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;invalidFileStartPosition&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;invalidOperatorName&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;invalidParameterDataType&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;invalidTimeStamp&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;keyGenerationError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;missingRequiredParameter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;noObjectsOfSpecifiedType&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;noSpaceForObject&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;noSpaceToAddListElement&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;noSpaceToWriteProperty&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;noVtSessionsAvailable&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;propertyIsNotA_List&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;objectDeletionNotPermitted&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;objectIdentifierAlreadyExists&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;operationalProblem&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;passwordFailure&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;readAccessDenied&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;securityNotSupported&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;serviceRequestDenied&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;timeout&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknownObject&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknownProperty&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;removed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknownVtClass&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknownVtSession&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unsupportedObjectType&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;valueOutOfRange&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;vtSessionAlreadyClosed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;vtSessionTerminationFailure&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;writeAccessDenied&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;characterSetNotSupported&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;invalidArrayIndex&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;covSubscriptionFailed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;notCovProperty&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;optionalFunctionalityNotSupported&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;invalidConfigurationData&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;datatypeNotSupported&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;duplicateName&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;duplicateObjectId&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;propertyIsNotAnArray&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;abortBufferOverflow&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;abortInvalidApduInThisState&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;abortPreemptedByHigherPriorityTask&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;abortSegmentationNotSupported&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;abortProprietary&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;abortOther&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;invalidTag&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;networkDown&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;rejectBufferOverflow&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;rejectInconsistentParameters&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;rejectInvalidParameterDataType&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;rejectInvalidTag&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;rejectMissingRequiredParameter&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;rejectParameterOutOfRange&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;rejectTooManyArguments&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;rejectUndefinedEnumeration&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;rejectUnrecognizedService&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;rejectProprietary&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;rejectOther&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknownDevice&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknownRoute&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;valueNotInitialized&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;invalidEventState&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;noAlarmConfigured&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;logBufferFull&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;loggedValuePurged&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;noPropertySpecified&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;notConfiguredForTriggeredLogging&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknownSubscription&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;parameterOutOfRange&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;listElementNotFound&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;busy&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;communicationDisabled&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;success&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accessDenied&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;badDestinationAddress&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;badDestinationDeviceId&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;badSignature&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;badSourceAddress&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;badTimestamp&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;cannotUseKey&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;cannotVerifyMessageId&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;correctKeyRevision&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;destinationDeviceIdRequired&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;duplicateMessage&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;encryptionNotConfigured&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;encryptionRequired&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;incorrectKey&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;invalidKeyData&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;keyUpdateInProgress&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;malformedMessage&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;notKeyServer&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;securityNotConfigured&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;sourceSecurityRequired&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tooManyKeys&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknownAuthenticationType&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknownKey&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknownKeyRevision&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknownSourceMessage&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;notRouterToDnet&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;routerBusy&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknownNetworkMessage&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;messageTooLong&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;securityError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;addressingError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;writeBdtFailed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;readBdtFailed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;registerForeignDeviceFailed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;readFdtFailed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deleteFdtEntryFailed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;distributeBroadcastFailed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unknownFileSize&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;abortApduTooLong&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;abortApplicationExceededReplyTime&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;abortOutOfResources&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;abortTsmTimeout&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;abortWindowSizeOutOfRange&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;fileFull&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;inconsistentConfiguration&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;inconsistentObjectType&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;internalError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;notConfigured&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;outOfMemory&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;valueTooLong&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;abortInsufficientSecurity&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;abortSecurityError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;duplicateEntry&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;invalidValueInThisState&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;invalidOperationInThisState&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;listItemNotNumbered&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;listItemNotTimestamped&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;invalidDataEncoding&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bvlcFunctionUnknown&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bvlcProprietaryFunctionUnknown&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;headerEncodingError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;headerNotUnderstood&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;messageIncomplete&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;notA_BacnetScHub&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;payloadExpected&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unexpectedData&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;nodeDuplicateVmac&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;httpUnexpectedResponseCode&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;httpNoUpgrade&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;httpResourceNotLocal&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;httpProxyAuthenticationFailed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;httpResponseTimeout&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;httpResponseSyntaxError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;httpResponseValueError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;httpResponseMissingHeader&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;httpWebsocketHeaderError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;httpUpgradeRequired&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;httpUpgradeError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;httpTemporaryUnavailable&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;httpNotA_Server&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;httpError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;websocketSchemeNotSupported&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;websocketUnknownControlMessage&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;websocketCloseError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;websocketClosedByPeer&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;websocketEndpointLeaves&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;websocketProtocolError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;websocketDataNotAccepted&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;websocketClosedAbnormally&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;websocketDataInconsistent&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;websocketDataAgainstPolicy&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;websocketFrameTooLong&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;websocketExtensionMissing&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;websocketRequestUnavailable&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;websocketError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tlsClientCertificateError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tlsServerCertificateError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tlsClientAuthenticationFailed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tlsServerAuthenticationFailed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tlsClientCertificateExpired&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tlsServerCertificateExpired&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tlsClientCertificateRevoked&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tlsServerCertificateRevoked&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tlsError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;dnsUnavailable&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;dnsNameResolutionFailed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;dnsResolverFailure&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;dnsError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tcpConnectTimeout&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tcpConnectionRefused&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tcpClosedByLocal&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tcpClosedOther&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tcpError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipAddressNotReachable&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;nullValueEvent&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>355</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetErrorCode.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.tag(int, javax.baja.bacnet.enums.BExtensibleEnumList) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<parameter name="list">
<type class="javax.baja.bacnet.enums.BExtensibleEnumList"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ordinal(java.lang.String, javax.baja.bacnet.enums.BExtensibleEnumList) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<parameter name="list">
<type class="javax.baja.bacnet.enums.BExtensibleEnumList"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.OTHER -->
<field name="OTHER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for other.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.AUTHENTICATION_FAILED -->
<field name="AUTHENTICATION_FAILED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for authenticationFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.CONFIGURATION_IN_PROGRESS -->
<field name="CONFIGURATION_IN_PROGRESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for configurationInProgress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.DEVICE_BUSY -->
<field name="DEVICE_BUSY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deviceBusy.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.DYNAMIC_CREATION_NOT_SUPPORTED -->
<field name="DYNAMIC_CREATION_NOT_SUPPORTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for dynamicCreationNotSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.FILE_ACCESS_DENIED -->
<field name="FILE_ACCESS_DENIED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for fileAccessDenied.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INCOMPATIBLE_SECURITY_LEVELS -->
<field name="INCOMPATIBLE_SECURITY_LEVELS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for incompatibleSecurityLevels.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INCONSISTENT_PARAMETERS -->
<field name="INCONSISTENT_PARAMETERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for inconsistentParameters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INCONSISTENT_SELECTION_CRITERION -->
<field name="INCONSISTENT_SELECTION_CRITERION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for inconsistentSelectionCriterion.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INVALID_DATA_TYPE -->
<field name="INVALID_DATA_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for invalidDataType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INVALID_FILE_ACCESS_METHOD -->
<field name="INVALID_FILE_ACCESS_METHOD"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for invalidFileAccessMethod.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INVALID_FILE_START_POSITION -->
<field name="INVALID_FILE_START_POSITION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for invalidFileStartPosition.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INVALID_OPERATOR_NAME -->
<field name="INVALID_OPERATOR_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for invalidOperatorName.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INVALID_PARAMETER_DATA_TYPE -->
<field name="INVALID_PARAMETER_DATA_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for invalidParameterDataType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INVALID_TIME_STAMP -->
<field name="INVALID_TIME_STAMP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for invalidTimeStamp.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.KEY_GENERATION_ERROR -->
<field name="KEY_GENERATION_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for keyGenerationError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.MISSING_REQUIRED_PARAMETER -->
<field name="MISSING_REQUIRED_PARAMETER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for missingRequiredParameter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NO_OBJECTS_OF_SPECIFIED_TYPE -->
<field name="NO_OBJECTS_OF_SPECIFIED_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for noObjectsOfSpecifiedType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NO_SPACE_FOR_OBJECT -->
<field name="NO_SPACE_FOR_OBJECT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for noSpaceForObject.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NO_SPACE_TO_ADD_LIST_ELEMENT -->
<field name="NO_SPACE_TO_ADD_LIST_ELEMENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for noSpaceToAddListElement.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NO_SPACE_TO_WRITE_PROPERTY -->
<field name="NO_SPACE_TO_WRITE_PROPERTY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for noSpaceToWriteProperty.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NO_VT_SESSIONS_AVAILABLE -->
<field name="NO_VT_SESSIONS_AVAILABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for noVtSessionsAvailable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.PROPERTY_IS_NOT_A_LIST -->
<field name="PROPERTY_IS_NOT_A_LIST"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for propertyIsNotA_List.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.OBJECT_DELETION_NOT_PERMITTED -->
<field name="OBJECT_DELETION_NOT_PERMITTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for objectDeletionNotPermitted.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.OBJECT_IDENTIFIER_ALREADY_EXISTS -->
<field name="OBJECT_IDENTIFIER_ALREADY_EXISTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for objectIdentifierAlreadyExists.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.OPERATIONAL_PROBLEM -->
<field name="OPERATIONAL_PROBLEM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for operationalProblem.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.PASSWORD_FAILURE -->
<field name="PASSWORD_FAILURE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for passwordFailure.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.READ_ACCESS_DENIED -->
<field name="READ_ACCESS_DENIED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for readAccessDenied.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.SECURITY_NOT_SUPPORTED -->
<field name="SECURITY_NOT_SUPPORTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for securityNotSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.SERVICE_REQUEST_DENIED -->
<field name="SERVICE_REQUEST_DENIED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for serviceRequestDenied.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TIMEOUT -->
<field name="TIMEOUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for timeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.UNKNOWN_OBJECT -->
<field name="UNKNOWN_OBJECT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknownObject.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.UNKNOWN_PROPERTY -->
<field name="UNKNOWN_PROPERTY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknownProperty.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.REMOVED -->
<field name="REMOVED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for removed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.UNKNOWN_VT_CLASS -->
<field name="UNKNOWN_VT_CLASS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknownVtClass.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.UNKNOWN_VT_SESSION -->
<field name="UNKNOWN_VT_SESSION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknownVtSession.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.UNSUPPORTED_OBJECT_TYPE -->
<field name="UNSUPPORTED_OBJECT_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unsupportedObjectType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.VALUE_OUT_OF_RANGE -->
<field name="VALUE_OUT_OF_RANGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for valueOutOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.VT_SESSION_ALREADY_CLOSED -->
<field name="VT_SESSION_ALREADY_CLOSED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for vtSessionAlreadyClosed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.VT_SESSION_TERMINATION_FAILURE -->
<field name="VT_SESSION_TERMINATION_FAILURE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for vtSessionTerminationFailure.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.WRITE_ACCESS_DENIED -->
<field name="WRITE_ACCESS_DENIED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for writeAccessDenied.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.CHARACTER_SET_NOT_SUPPORTED -->
<field name="CHARACTER_SET_NOT_SUPPORTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for characterSetNotSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INVALID_ARRAY_INDEX -->
<field name="INVALID_ARRAY_INDEX"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for invalidArrayIndex.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.COV_SUBSCRIPTION_FAILED -->
<field name="COV_SUBSCRIPTION_FAILED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for covSubscriptionFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NOT_COV_PROPERTY -->
<field name="NOT_COV_PROPERTY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for notCovProperty.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.OPTIONAL_FUNCTIONALITY_NOT_SUPPORTED -->
<field name="OPTIONAL_FUNCTIONALITY_NOT_SUPPORTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for optionalFunctionalityNotSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INVALID_CONFIGURATION_DATA -->
<field name="INVALID_CONFIGURATION_DATA"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for invalidConfigurationData.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.DATATYPE_NOT_SUPPORTED -->
<field name="DATATYPE_NOT_SUPPORTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for datatypeNotSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.DUPLICATE_NAME -->
<field name="DUPLICATE_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for duplicateName.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.DUPLICATE_OBJECT_ID -->
<field name="DUPLICATE_OBJECT_ID"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for duplicateObjectId.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.PROPERTY_IS_NOT_AN_ARRAY -->
<field name="PROPERTY_IS_NOT_AN_ARRAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for propertyIsNotAnArray.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ABORT_BUFFER_OVERFLOW -->
<field name="ABORT_BUFFER_OVERFLOW"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for abortBufferOverflow.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ABORT_INVALID_APDU_IN_THIS_STATE -->
<field name="ABORT_INVALID_APDU_IN_THIS_STATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for abortInvalidApduInThisState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ABORT_PREEMPTED_BY_HIGHER_PRIORITY_TASK -->
<field name="ABORT_PREEMPTED_BY_HIGHER_PRIORITY_TASK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for abortPreemptedByHigherPriorityTask.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ABORT_SEGMENTATION_NOT_SUPPORTED -->
<field name="ABORT_SEGMENTATION_NOT_SUPPORTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for abortSegmentationNotSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ABORT_PROPRIETARY -->
<field name="ABORT_PROPRIETARY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for abortProprietary.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ABORT_OTHER -->
<field name="ABORT_OTHER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for abortOther.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INVALID_TAG -->
<field name="INVALID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for invalidTag.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NETWORK_DOWN -->
<field name="NETWORK_DOWN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for networkDown.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.REJECT_BUFFER_OVERFLOW -->
<field name="REJECT_BUFFER_OVERFLOW"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for rejectBufferOverflow.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.REJECT_INCONSISTENT_PARAMETERS -->
<field name="REJECT_INCONSISTENT_PARAMETERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for rejectInconsistentParameters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.REJECT_INVALID_PARAMETER_DATA_TYPE -->
<field name="REJECT_INVALID_PARAMETER_DATA_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for rejectInvalidParameterDataType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.REJECT_INVALID_TAG -->
<field name="REJECT_INVALID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for rejectInvalidTag.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.REJECT_MISSING_REQUIRED_PARAMETER -->
<field name="REJECT_MISSING_REQUIRED_PARAMETER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for rejectMissingRequiredParameter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.REJECT_PARAMETER_OUT_OF_RANGE -->
<field name="REJECT_PARAMETER_OUT_OF_RANGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for rejectParameterOutOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.REJECT_TOO_MANY_ARGUMENTS -->
<field name="REJECT_TOO_MANY_ARGUMENTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for rejectTooManyArguments.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.REJECT_UNDEFINED_ENUMERATION -->
<field name="REJECT_UNDEFINED_ENUMERATION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for rejectUndefinedEnumeration.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.REJECT_UNRECOGNIZED_SERVICE -->
<field name="REJECT_UNRECOGNIZED_SERVICE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for rejectUnrecognizedService.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.REJECT_PROPRIETARY -->
<field name="REJECT_PROPRIETARY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for rejectProprietary.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.REJECT_OTHER -->
<field name="REJECT_OTHER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for rejectOther.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.UNKNOWN_DEVICE -->
<field name="UNKNOWN_DEVICE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknownDevice.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.UNKNOWN_ROUTE -->
<field name="UNKNOWN_ROUTE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknownRoute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.VALUE_NOT_INITIALIZED -->
<field name="VALUE_NOT_INITIALIZED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for valueNotInitialized.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INVALID_EVENT_STATE -->
<field name="INVALID_EVENT_STATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for invalidEventState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NO_ALARM_CONFIGURED -->
<field name="NO_ALARM_CONFIGURED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for noAlarmConfigured.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.LOG_BUFFER_FULL -->
<field name="LOG_BUFFER_FULL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for logBufferFull.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.LOGGED_VALUE_PURGED -->
<field name="LOGGED_VALUE_PURGED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for loggedValuePurged.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NO_PROPERTY_SPECIFIED -->
<field name="NO_PROPERTY_SPECIFIED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for noPropertySpecified.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NOT_CONFIGURED_FOR_TRIGGERED_LOGGING -->
<field name="NOT_CONFIGURED_FOR_TRIGGERED_LOGGING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for notConfiguredForTriggeredLogging.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.UNKNOWN_SUBSCRIPTION -->
<field name="UNKNOWN_SUBSCRIPTION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknownSubscription.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.PARAMETER_OUT_OF_RANGE -->
<field name="PARAMETER_OUT_OF_RANGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for parameterOutOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.LIST_ELEMENT_NOT_FOUND -->
<field name="LIST_ELEMENT_NOT_FOUND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for listElementNotFound.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.BUSY -->
<field name="BUSY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for busy.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.COMMUNICATION_DISABLED -->
<field name="COMMUNICATION_DISABLED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for communicationDisabled.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.SUCCESS -->
<field name="SUCCESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for success.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ACCESS_DENIED -->
<field name="ACCESS_DENIED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accessDenied.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.BAD_DESTINATION_ADDRESS -->
<field name="BAD_DESTINATION_ADDRESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for badDestinationAddress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.BAD_DESTINATION_DEVICE_ID -->
<field name="BAD_DESTINATION_DEVICE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for badDestinationDeviceId.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.BAD_SIGNATURE -->
<field name="BAD_SIGNATURE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for badSignature.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.BAD_SOURCE_ADDRESS -->
<field name="BAD_SOURCE_ADDRESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for badSourceAddress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.BAD_TIMESTAMP -->
<field name="BAD_TIMESTAMP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for badTimestamp.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.CANNOT_USE_KEY -->
<field name="CANNOT_USE_KEY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for cannotUseKey.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.CANNOT_VERIFY_MESSAGE_ID -->
<field name="CANNOT_VERIFY_MESSAGE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for cannotVerifyMessageId.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.CORRECT_KEY_REVISION -->
<field name="CORRECT_KEY_REVISION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for correctKeyRevision.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.DESTINATION_DEVICE_ID_REQUIRED -->
<field name="DESTINATION_DEVICE_ID_REQUIRED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for destinationDeviceIdRequired.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.DUPLICATE_MESSAGE -->
<field name="DUPLICATE_MESSAGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for duplicateMessage.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ENCRYPTION_NOT_CONFIGURED -->
<field name="ENCRYPTION_NOT_CONFIGURED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for encryptionNotConfigured.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ENCRYPTION_REQUIRED -->
<field name="ENCRYPTION_REQUIRED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for encryptionRequired.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INCORRECT_KEY -->
<field name="INCORRECT_KEY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for incorrectKey.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INVALID_KEY_DATA -->
<field name="INVALID_KEY_DATA"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for invalidKeyData.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.KEY_UPDATE_IN_PROGRESS -->
<field name="KEY_UPDATE_IN_PROGRESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for keyUpdateInProgress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.MALFORMED_MESSAGE -->
<field name="MALFORMED_MESSAGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for malformedMessage.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NOT_KEY_SERVER -->
<field name="NOT_KEY_SERVER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for notKeyServer.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.SECURITY_NOT_CONFIGURED -->
<field name="SECURITY_NOT_CONFIGURED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for securityNotConfigured.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.SOURCE_SECURITY_REQUIRED -->
<field name="SOURCE_SECURITY_REQUIRED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for sourceSecurityRequired.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TOO_MANY_KEYS -->
<field name="TOO_MANY_KEYS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tooManyKeys.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.UNKNOWN_AUTHENTICATION_TYPE -->
<field name="UNKNOWN_AUTHENTICATION_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknownAuthenticationType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.UNKNOWN_KEY -->
<field name="UNKNOWN_KEY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknownKey.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.UNKNOWN_KEY_REVISION -->
<field name="UNKNOWN_KEY_REVISION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknownKeyRevision.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.UNKNOWN_SOURCE_MESSAGE -->
<field name="UNKNOWN_SOURCE_MESSAGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknownSourceMessage.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NOT_ROUTER_TO_DNET -->
<field name="NOT_ROUTER_TO_DNET"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for notRouterToDnet.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ROUTER_BUSY -->
<field name="ROUTER_BUSY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for routerBusy.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.UNKNOWN_NETWORK_MESSAGE -->
<field name="UNKNOWN_NETWORK_MESSAGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknownNetworkMessage.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.MESSAGE_TOO_LONG -->
<field name="MESSAGE_TOO_LONG"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for messageTooLong.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.SECURITY_ERROR -->
<field name="SECURITY_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for securityError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ADDRESSING_ERROR -->
<field name="ADDRESSING_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for addressingError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.WRITE_BDT_FAILED -->
<field name="WRITE_BDT_FAILED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for writeBdtFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.READ_BDT_FAILED -->
<field name="READ_BDT_FAILED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for readBdtFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.REGISTER_FOREIGN_DEVICE_FAILED -->
<field name="REGISTER_FOREIGN_DEVICE_FAILED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for registerForeignDeviceFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.READ_FDT_FAILED -->
<field name="READ_FDT_FAILED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for readFdtFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.DELETE_FDT_ENTRY_FAILED -->
<field name="DELETE_FDT_ENTRY_FAILED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deleteFdtEntryFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.DISTRIBUTE_BROADCAST_FAILED -->
<field name="DISTRIBUTE_BROADCAST_FAILED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for distributeBroadcastFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.UNKNOWN_FILE_SIZE -->
<field name="UNKNOWN_FILE_SIZE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unknownFileSize.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ABORT_APDU_TOO_LONG -->
<field name="ABORT_APDU_TOO_LONG"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for abortApduTooLong.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ABORT_APPLICATION_EXCEEDED_REPLY_TIME -->
<field name="ABORT_APPLICATION_EXCEEDED_REPLY_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for abortApplicationExceededReplyTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ABORT_OUT_OF_RESOURCES -->
<field name="ABORT_OUT_OF_RESOURCES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for abortOutOfResources.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ABORT_TSM_TIMEOUT -->
<field name="ABORT_TSM_TIMEOUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for abortTsmTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ABORT_WINDOW_SIZE_OUT_OF_RANGE -->
<field name="ABORT_WINDOW_SIZE_OUT_OF_RANGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for abortWindowSizeOutOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.FILE_FULL -->
<field name="FILE_FULL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for fileFull.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INCONSISTENT_CONFIGURATION -->
<field name="INCONSISTENT_CONFIGURATION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for inconsistentConfiguration.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INCONSISTENT_OBJECT_TYPE -->
<field name="INCONSISTENT_OBJECT_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for inconsistentObjectType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INTERNAL_ERROR -->
<field name="INTERNAL_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for internalError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NOT_CONFIGURED -->
<field name="NOT_CONFIGURED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for notConfigured.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.OUT_OF_MEMORY -->
<field name="OUT_OF_MEMORY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for outOfMemory.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.VALUE_TOO_LONG -->
<field name="VALUE_TOO_LONG"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for valueTooLong.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ABORT_INSUFFICIENT_SECURITY -->
<field name="ABORT_INSUFFICIENT_SECURITY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for abortInsufficientSecurity.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ABORT_SECURITY_ERROR -->
<field name="ABORT_SECURITY_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for abortSecurityError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.DUPLICATE_ENTRY -->
<field name="DUPLICATE_ENTRY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for duplicateEntry.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INVALID_VALUE_IN_THIS_STATE -->
<field name="INVALID_VALUE_IN_THIS_STATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for invalidValueInThisState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INVALID_OPERATION_IN_THIS_STATE -->
<field name="INVALID_OPERATION_IN_THIS_STATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for invalidOperationInThisState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.LIST_ITEM_NOT_NUMBERED -->
<field name="LIST_ITEM_NOT_NUMBERED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for listItemNotNumbered.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.LIST_ITEM_NOT_TIMESTAMPED -->
<field name="LIST_ITEM_NOT_TIMESTAMPED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for listItemNotTimestamped.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INVALID_DATA_ENCODING -->
<field name="INVALID_DATA_ENCODING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for invalidDataEncoding.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.BVLC_FUNCTION_UNKNOWN -->
<field name="BVLC_FUNCTION_UNKNOWN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bvlcFunctionUnknown.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.BVLC_PROPRIETARY_FUNCTION_UNKNOWN -->
<field name="BVLC_PROPRIETARY_FUNCTION_UNKNOWN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bvlcProprietaryFunctionUnknown.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.HEADER_ENCODING_ERROR -->
<field name="HEADER_ENCODING_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for headerEncodingError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.HEADER_NOT_UNDERSTOOD -->
<field name="HEADER_NOT_UNDERSTOOD"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for headerNotUnderstood.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.MESSAGE_INCOMPLETE -->
<field name="MESSAGE_INCOMPLETE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for messageIncomplete.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NOT_A_BACNET_SC_HUB -->
<field name="NOT_A_BACNET_SC_HUB"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for notA_BacnetScHub.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.PAYLOAD_EXPECTED -->
<field name="PAYLOAD_EXPECTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for payloadExpected.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.UNEXPECTED_DATA -->
<field name="UNEXPECTED_DATA"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unexpectedData.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NODE_DUPLICATE_VMAC -->
<field name="NODE_DUPLICATE_VMAC"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for nodeDuplicateVmac.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.HTTP_UNEXPECTED_RESPONSE_CODE -->
<field name="HTTP_UNEXPECTED_RESPONSE_CODE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for httpUnexpectedResponseCode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.HTTP_NO_UPGRADE -->
<field name="HTTP_NO_UPGRADE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for httpNoUpgrade.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.HTTP_RESOURCE_NOT_LOCAL -->
<field name="HTTP_RESOURCE_NOT_LOCAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for httpResourceNotLocal.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.HTTP_PROXY_AUTHENTICATION_FAILED -->
<field name="HTTP_PROXY_AUTHENTICATION_FAILED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for httpProxyAuthenticationFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.HTTP_RESPONSE_TIMEOUT -->
<field name="HTTP_RESPONSE_TIMEOUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for httpResponseTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.HTTP_RESPONSE_SYNTAX_ERROR -->
<field name="HTTP_RESPONSE_SYNTAX_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for httpResponseSyntaxError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.HTTP_RESPONSE_VALUE_ERROR -->
<field name="HTTP_RESPONSE_VALUE_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for httpResponseValueError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.HTTP_RESPONSE_MISSING_HEADER -->
<field name="HTTP_RESPONSE_MISSING_HEADER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for httpResponseMissingHeader.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.HTTP_WEBSOCKET_HEADER_ERROR -->
<field name="HTTP_WEBSOCKET_HEADER_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for httpWebsocketHeaderError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.HTTP_UPGRADE_REQUIRED -->
<field name="HTTP_UPGRADE_REQUIRED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for httpUpgradeRequired.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.HTTP_UPGRADE_ERROR -->
<field name="HTTP_UPGRADE_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for httpUpgradeError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.HTTP_TEMPORARY_UNAVAILABLE -->
<field name="HTTP_TEMPORARY_UNAVAILABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for httpTemporaryUnavailable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.HTTP_NOT_A_SERVER -->
<field name="HTTP_NOT_A_SERVER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for httpNotA_Server.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.HTTP_ERROR -->
<field name="HTTP_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for httpError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.WEBSOCKET_SCHEME_NOT_SUPPORTED -->
<field name="WEBSOCKET_SCHEME_NOT_SUPPORTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for websocketSchemeNotSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.WEBSOCKET_UNKNOWN_CONTROL_MESSAGE -->
<field name="WEBSOCKET_UNKNOWN_CONTROL_MESSAGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for websocketUnknownControlMessage.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.WEBSOCKET_CLOSE_ERROR -->
<field name="WEBSOCKET_CLOSE_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for websocketCloseError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.WEBSOCKET_CLOSED_BY_PEER -->
<field name="WEBSOCKET_CLOSED_BY_PEER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for websocketClosedByPeer.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.WEBSOCKET_ENDPOINT_LEAVES -->
<field name="WEBSOCKET_ENDPOINT_LEAVES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for websocketEndpointLeaves.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.WEBSOCKET_PROTOCOL_ERROR -->
<field name="WEBSOCKET_PROTOCOL_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for websocketProtocolError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.WEBSOCKET_DATA_NOT_ACCEPTED -->
<field name="WEBSOCKET_DATA_NOT_ACCEPTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for websocketDataNotAccepted.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.WEBSOCKET_CLOSED_ABNORMALLY -->
<field name="WEBSOCKET_CLOSED_ABNORMALLY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for websocketClosedAbnormally.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.WEBSOCKET_DATA_INCONSISTENT -->
<field name="WEBSOCKET_DATA_INCONSISTENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for websocketDataInconsistent.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.WEBSOCKET_DATA_AGAINST_POLICY -->
<field name="WEBSOCKET_DATA_AGAINST_POLICY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for websocketDataAgainstPolicy.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.WEBSOCKET_FRAME_TOO_LONG -->
<field name="WEBSOCKET_FRAME_TOO_LONG"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for websocketFrameTooLong.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.WEBSOCKET_EXTENSION_MISSING -->
<field name="WEBSOCKET_EXTENSION_MISSING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for websocketExtensionMissing.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.WEBSOCKET_REQUEST_UNAVAILABLE -->
<field name="WEBSOCKET_REQUEST_UNAVAILABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for websocketRequestUnavailable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.WEBSOCKET_ERROR -->
<field name="WEBSOCKET_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for websocketError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TLS_CLIENT_CERTIFICATE_ERROR -->
<field name="TLS_CLIENT_CERTIFICATE_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tlsClientCertificateError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TLS_SERVER_CERTIFICATE_ERROR -->
<field name="TLS_SERVER_CERTIFICATE_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tlsServerCertificateError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TLS_CLIENT_AUTHENTICATION_FAILED -->
<field name="TLS_CLIENT_AUTHENTICATION_FAILED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tlsClientAuthenticationFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TLS_SERVER_AUTHENTICATION_FAILED -->
<field name="TLS_SERVER_AUTHENTICATION_FAILED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tlsServerAuthenticationFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TLS_CLIENT_CERTIFICATE_EXPIRED -->
<field name="TLS_CLIENT_CERTIFICATE_EXPIRED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tlsClientCertificateExpired.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TLS_SERVER_CERTIFICATE_EXPIRED -->
<field name="TLS_SERVER_CERTIFICATE_EXPIRED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tlsServerCertificateExpired.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TLS_CLIENT_CERTIFICATE_REVOKED -->
<field name="TLS_CLIENT_CERTIFICATE_REVOKED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tlsClientCertificateRevoked.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TLS_SERVER_CERTIFICATE_REVOKED -->
<field name="TLS_SERVER_CERTIFICATE_REVOKED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tlsServerCertificateRevoked.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TLS_ERROR -->
<field name="TLS_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tlsError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.DNS_UNAVAILABLE -->
<field name="DNS_UNAVAILABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for dnsUnavailable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.DNS_NAME_RESOLUTION_FAILED -->
<field name="DNS_NAME_RESOLUTION_FAILED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for dnsNameResolutionFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.DNS_RESOLVER_FAILURE -->
<field name="DNS_RESOLVER_FAILURE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for dnsResolverFailure.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.DNS_ERROR -->
<field name="DNS_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for dnsError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TCP_CONNECT_TIMEOUT -->
<field name="TCP_CONNECT_TIMEOUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tcpConnectTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TCP_CONNECTION_REFUSED -->
<field name="TCP_CONNECTION_REFUSED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tcpConnectionRefused.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TCP_CLOSED_BY_LOCAL -->
<field name="TCP_CLOSED_BY_LOCAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tcpClosedByLocal.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TCP_CLOSED_OTHER -->
<field name="TCP_CLOSED_OTHER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tcpClosedOther.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TCP_ERROR -->
<field name="TCP_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tcpError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.IP_ADDRESS_NOT_REACHABLE -->
<field name="IP_ADDRESS_NOT_REACHABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipAddressNotReachable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.IP_ERROR -->
<field name="IP_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NULL_VALUE_EVENT -->
<field name="NULL_VALUE_EVENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for nullValueEvent.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.other -->
<field name="other"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for other.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.authenticationFailed -->
<field name="authenticationFailed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for authenticationFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.configurationInProgress -->
<field name="configurationInProgress"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for configurationInProgress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.deviceBusy -->
<field name="deviceBusy"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for deviceBusy.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.dynamicCreationNotSupported -->
<field name="dynamicCreationNotSupported"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for dynamicCreationNotSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.fileAccessDenied -->
<field name="fileAccessDenied"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for fileAccessDenied.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.incompatibleSecurityLevels -->
<field name="incompatibleSecurityLevels"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for incompatibleSecurityLevels.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.inconsistentParameters -->
<field name="inconsistentParameters"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for inconsistentParameters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.inconsistentSelectionCriterion -->
<field name="inconsistentSelectionCriterion"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for inconsistentSelectionCriterion.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.invalidDataType -->
<field name="invalidDataType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for invalidDataType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.invalidFileAccessMethod -->
<field name="invalidFileAccessMethod"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for invalidFileAccessMethod.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.invalidFileStartPosition -->
<field name="invalidFileStartPosition"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for invalidFileStartPosition.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.invalidOperatorName -->
<field name="invalidOperatorName"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for invalidOperatorName.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.invalidParameterDataType -->
<field name="invalidParameterDataType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for invalidParameterDataType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.invalidTimeStamp -->
<field name="invalidTimeStamp"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for invalidTimeStamp.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.keyGenerationError -->
<field name="keyGenerationError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for keyGenerationError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.missingRequiredParameter -->
<field name="missingRequiredParameter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for missingRequiredParameter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.noObjectsOfSpecifiedType -->
<field name="noObjectsOfSpecifiedType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for noObjectsOfSpecifiedType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.noSpaceForObject -->
<field name="noSpaceForObject"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for noSpaceForObject.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.noSpaceToAddListElement -->
<field name="noSpaceToAddListElement"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for noSpaceToAddListElement.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.noSpaceToWriteProperty -->
<field name="noSpaceToWriteProperty"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for noSpaceToWriteProperty.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.noVtSessionsAvailable -->
<field name="noVtSessionsAvailable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for noVtSessionsAvailable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.propertyIsNotA_List -->
<field name="propertyIsNotA_List"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for propertyIsNotA_List.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.objectDeletionNotPermitted -->
<field name="objectDeletionNotPermitted"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for objectDeletionNotPermitted.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.objectIdentifierAlreadyExists -->
<field name="objectIdentifierAlreadyExists"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for objectIdentifierAlreadyExists.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.operationalProblem -->
<field name="operationalProblem"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for operationalProblem.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.passwordFailure -->
<field name="passwordFailure"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for passwordFailure.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.readAccessDenied -->
<field name="readAccessDenied"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for readAccessDenied.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.securityNotSupported -->
<field name="securityNotSupported"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for securityNotSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.serviceRequestDenied -->
<field name="serviceRequestDenied"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for serviceRequestDenied.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.timeout -->
<field name="timeout"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for timeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.unknownObject -->
<field name="unknownObject"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for unknownObject.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.unknownProperty -->
<field name="unknownProperty"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for unknownProperty.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.removed -->
<field name="removed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for removed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.unknownVtClass -->
<field name="unknownVtClass"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for unknownVtClass.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.unknownVtSession -->
<field name="unknownVtSession"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for unknownVtSession.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.unsupportedObjectType -->
<field name="unsupportedObjectType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for unsupportedObjectType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.valueOutOfRange -->
<field name="valueOutOfRange"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for valueOutOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.vtSessionAlreadyClosed -->
<field name="vtSessionAlreadyClosed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for vtSessionAlreadyClosed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.vtSessionTerminationFailure -->
<field name="vtSessionTerminationFailure"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for vtSessionTerminationFailure.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.writeAccessDenied -->
<field name="writeAccessDenied"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for writeAccessDenied.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.characterSetNotSupported -->
<field name="characterSetNotSupported"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for characterSetNotSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.invalidArrayIndex -->
<field name="invalidArrayIndex"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for invalidArrayIndex.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.covSubscriptionFailed -->
<field name="covSubscriptionFailed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for covSubscriptionFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.notCovProperty -->
<field name="notCovProperty"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for notCovProperty.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.optionalFunctionalityNotSupported -->
<field name="optionalFunctionalityNotSupported"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for optionalFunctionalityNotSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.invalidConfigurationData -->
<field name="invalidConfigurationData"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for invalidConfigurationData.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.datatypeNotSupported -->
<field name="datatypeNotSupported"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for datatypeNotSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.duplicateName -->
<field name="duplicateName"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for duplicateName.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.duplicateObjectId -->
<field name="duplicateObjectId"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for duplicateObjectId.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.propertyIsNotAnArray -->
<field name="propertyIsNotAnArray"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for propertyIsNotAnArray.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.abortBufferOverflow -->
<field name="abortBufferOverflow"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for abortBufferOverflow.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.abortInvalidApduInThisState -->
<field name="abortInvalidApduInThisState"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for abortInvalidApduInThisState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.abortPreemptedByHigherPriorityTask -->
<field name="abortPreemptedByHigherPriorityTask"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for abortPreemptedByHigherPriorityTask.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.abortSegmentationNotSupported -->
<field name="abortSegmentationNotSupported"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for abortSegmentationNotSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.abortProprietary -->
<field name="abortProprietary"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for abortProprietary.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.abortOther -->
<field name="abortOther"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for abortOther.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.invalidTag -->
<field name="invalidTag"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for invalidTag.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.networkDown -->
<field name="networkDown"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for networkDown.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.rejectBufferOverflow -->
<field name="rejectBufferOverflow"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for rejectBufferOverflow.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.rejectInconsistentParameters -->
<field name="rejectInconsistentParameters"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for rejectInconsistentParameters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.rejectInvalidParameterDataType -->
<field name="rejectInvalidParameterDataType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for rejectInvalidParameterDataType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.rejectInvalidTag -->
<field name="rejectInvalidTag"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for rejectInvalidTag.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.rejectMissingRequiredParameter -->
<field name="rejectMissingRequiredParameter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for rejectMissingRequiredParameter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.rejectParameterOutOfRange -->
<field name="rejectParameterOutOfRange"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for rejectParameterOutOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.rejectTooManyArguments -->
<field name="rejectTooManyArguments"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for rejectTooManyArguments.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.rejectUndefinedEnumeration -->
<field name="rejectUndefinedEnumeration"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for rejectUndefinedEnumeration.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.rejectUnrecognizedService -->
<field name="rejectUnrecognizedService"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for rejectUnrecognizedService.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.rejectProprietary -->
<field name="rejectProprietary"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for rejectProprietary.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.rejectOther -->
<field name="rejectOther"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for rejectOther.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.unknownDevice -->
<field name="unknownDevice"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for unknownDevice.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.unknownRoute -->
<field name="unknownRoute"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for unknownRoute.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.valueNotInitialized -->
<field name="valueNotInitialized"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for valueNotInitialized.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.invalidEventState -->
<field name="invalidEventState"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for invalidEventState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.noAlarmConfigured -->
<field name="noAlarmConfigured"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for noAlarmConfigured.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.logBufferFull -->
<field name="logBufferFull"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for logBufferFull.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.loggedValuePurged -->
<field name="loggedValuePurged"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for loggedValuePurged.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.noPropertySpecified -->
<field name="noPropertySpecified"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for noPropertySpecified.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.notConfiguredForTriggeredLogging -->
<field name="notConfiguredForTriggeredLogging"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for notConfiguredForTriggeredLogging.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.unknownSubscription -->
<field name="unknownSubscription"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for unknownSubscription.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.parameterOutOfRange -->
<field name="parameterOutOfRange"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for parameterOutOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.listElementNotFound -->
<field name="listElementNotFound"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for listElementNotFound.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.busy -->
<field name="busy"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for busy.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.communicationDisabled -->
<field name="communicationDisabled"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for communicationDisabled.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.success -->
<field name="success"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for success.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.accessDenied -->
<field name="accessDenied"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for accessDenied.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.badDestinationAddress -->
<field name="badDestinationAddress"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for badDestinationAddress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.badDestinationDeviceId -->
<field name="badDestinationDeviceId"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for badDestinationDeviceId.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.badSignature -->
<field name="badSignature"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for badSignature.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.badSourceAddress -->
<field name="badSourceAddress"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for badSourceAddress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.badTimestamp -->
<field name="badTimestamp"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for badTimestamp.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.cannotUseKey -->
<field name="cannotUseKey"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for cannotUseKey.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.cannotVerifyMessageId -->
<field name="cannotVerifyMessageId"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for cannotVerifyMessageId.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.correctKeyRevision -->
<field name="correctKeyRevision"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for correctKeyRevision.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.destinationDeviceIdRequired -->
<field name="destinationDeviceIdRequired"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for destinationDeviceIdRequired.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.duplicateMessage -->
<field name="duplicateMessage"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for duplicateMessage.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.encryptionNotConfigured -->
<field name="encryptionNotConfigured"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for encryptionNotConfigured.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.encryptionRequired -->
<field name="encryptionRequired"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for encryptionRequired.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.incorrectKey -->
<field name="incorrectKey"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for incorrectKey.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.invalidKeyData -->
<field name="invalidKeyData"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for invalidKeyData.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.keyUpdateInProgress -->
<field name="keyUpdateInProgress"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for keyUpdateInProgress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.malformedMessage -->
<field name="malformedMessage"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for malformedMessage.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.notKeyServer -->
<field name="notKeyServer"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for notKeyServer.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.securityNotConfigured -->
<field name="securityNotConfigured"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for securityNotConfigured.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.sourceSecurityRequired -->
<field name="sourceSecurityRequired"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for sourceSecurityRequired.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.tooManyKeys -->
<field name="tooManyKeys"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for tooManyKeys.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.unknownAuthenticationType -->
<field name="unknownAuthenticationType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for unknownAuthenticationType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.unknownKey -->
<field name="unknownKey"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for unknownKey.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.unknownKeyRevision -->
<field name="unknownKeyRevision"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for unknownKeyRevision.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.unknownSourceMessage -->
<field name="unknownSourceMessage"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for unknownSourceMessage.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.notRouterToDnet -->
<field name="notRouterToDnet"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for notRouterToDnet.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.routerBusy -->
<field name="routerBusy"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for routerBusy.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.unknownNetworkMessage -->
<field name="unknownNetworkMessage"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for unknownNetworkMessage.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.messageTooLong -->
<field name="messageTooLong"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for messageTooLong.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.securityError -->
<field name="securityError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for securityError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.addressingError -->
<field name="addressingError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for addressingError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.writeBdtFailed -->
<field name="writeBdtFailed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for writeBdtFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.readBdtFailed -->
<field name="readBdtFailed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for readBdtFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.registerForeignDeviceFailed -->
<field name="registerForeignDeviceFailed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for registerForeignDeviceFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.readFdtFailed -->
<field name="readFdtFailed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for readFdtFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.deleteFdtEntryFailed -->
<field name="deleteFdtEntryFailed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for deleteFdtEntryFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.distributeBroadcastFailed -->
<field name="distributeBroadcastFailed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for distributeBroadcastFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.unknownFileSize -->
<field name="unknownFileSize"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for unknownFileSize.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.abortApduTooLong -->
<field name="abortApduTooLong"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for abortApduTooLong.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.abortApplicationExceededReplyTime -->
<field name="abortApplicationExceededReplyTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for abortApplicationExceededReplyTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.abortOutOfResources -->
<field name="abortOutOfResources"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for abortOutOfResources.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.abortTsmTimeout -->
<field name="abortTsmTimeout"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for abortTsmTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.abortWindowSizeOutOfRange -->
<field name="abortWindowSizeOutOfRange"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for abortWindowSizeOutOfRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.fileFull -->
<field name="fileFull"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for fileFull.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.inconsistentConfiguration -->
<field name="inconsistentConfiguration"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for inconsistentConfiguration.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.inconsistentObjectType -->
<field name="inconsistentObjectType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for inconsistentObjectType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.internalError -->
<field name="internalError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for internalError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.notConfigured -->
<field name="notConfigured"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for notConfigured.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.outOfMemory -->
<field name="outOfMemory"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for outOfMemory.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.valueTooLong -->
<field name="valueTooLong"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for valueTooLong.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.abortInsufficientSecurity -->
<field name="abortInsufficientSecurity"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for abortInsufficientSecurity.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.abortSecurityError -->
<field name="abortSecurityError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for abortSecurityError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.duplicateEntry -->
<field name="duplicateEntry"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for duplicateEntry.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.invalidValueInThisState -->
<field name="invalidValueInThisState"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for invalidValueInThisState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.invalidOperationInThisState -->
<field name="invalidOperationInThisState"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for invalidOperationInThisState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.listItemNotNumbered -->
<field name="listItemNotNumbered"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for listItemNotNumbered.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.listItemNotTimestamped -->
<field name="listItemNotTimestamped"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for listItemNotTimestamped.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.invalidDataEncoding -->
<field name="invalidDataEncoding"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for invalidDataEncoding.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.bvlcFunctionUnknown -->
<field name="bvlcFunctionUnknown"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for bvlcFunctionUnknown.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.bvlcProprietaryFunctionUnknown -->
<field name="bvlcProprietaryFunctionUnknown"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for bvlcProprietaryFunctionUnknown.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.headerEncodingError -->
<field name="headerEncodingError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for headerEncodingError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.headerNotUnderstood -->
<field name="headerNotUnderstood"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for headerNotUnderstood.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.messageIncomplete -->
<field name="messageIncomplete"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for messageIncomplete.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.notA_BacnetScHub -->
<field name="notA_BacnetScHub"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for notA_BacnetScHub.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.payloadExpected -->
<field name="payloadExpected"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for payloadExpected.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.unexpectedData -->
<field name="unexpectedData"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for unexpectedData.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.nodeDuplicateVmac -->
<field name="nodeDuplicateVmac"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for nodeDuplicateVmac.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.httpUnexpectedResponseCode -->
<field name="httpUnexpectedResponseCode"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for httpUnexpectedResponseCode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.httpNoUpgrade -->
<field name="httpNoUpgrade"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for httpNoUpgrade.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.httpResourceNotLocal -->
<field name="httpResourceNotLocal"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for httpResourceNotLocal.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.httpProxyAuthenticationFailed -->
<field name="httpProxyAuthenticationFailed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for httpProxyAuthenticationFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.httpResponseTimeout -->
<field name="httpResponseTimeout"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for httpResponseTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.httpResponseSyntaxError -->
<field name="httpResponseSyntaxError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for httpResponseSyntaxError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.httpResponseValueError -->
<field name="httpResponseValueError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for httpResponseValueError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.httpResponseMissingHeader -->
<field name="httpResponseMissingHeader"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for httpResponseMissingHeader.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.httpWebsocketHeaderError -->
<field name="httpWebsocketHeaderError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for httpWebsocketHeaderError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.httpUpgradeRequired -->
<field name="httpUpgradeRequired"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for httpUpgradeRequired.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.httpUpgradeError -->
<field name="httpUpgradeError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for httpUpgradeError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.httpTemporaryUnavailable -->
<field name="httpTemporaryUnavailable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for httpTemporaryUnavailable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.httpNotA_Server -->
<field name="httpNotA_Server"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for httpNotA_Server.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.httpError -->
<field name="httpError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for httpError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.websocketSchemeNotSupported -->
<field name="websocketSchemeNotSupported"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for websocketSchemeNotSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.websocketUnknownControlMessage -->
<field name="websocketUnknownControlMessage"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for websocketUnknownControlMessage.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.websocketCloseError -->
<field name="websocketCloseError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for websocketCloseError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.websocketClosedByPeer -->
<field name="websocketClosedByPeer"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for websocketClosedByPeer.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.websocketEndpointLeaves -->
<field name="websocketEndpointLeaves"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for websocketEndpointLeaves.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.websocketProtocolError -->
<field name="websocketProtocolError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for websocketProtocolError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.websocketDataNotAccepted -->
<field name="websocketDataNotAccepted"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for websocketDataNotAccepted.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.websocketClosedAbnormally -->
<field name="websocketClosedAbnormally"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for websocketClosedAbnormally.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.websocketDataInconsistent -->
<field name="websocketDataInconsistent"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for websocketDataInconsistent.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.websocketDataAgainstPolicy -->
<field name="websocketDataAgainstPolicy"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for websocketDataAgainstPolicy.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.websocketFrameTooLong -->
<field name="websocketFrameTooLong"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for websocketFrameTooLong.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.websocketExtensionMissing -->
<field name="websocketExtensionMissing"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for websocketExtensionMissing.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.websocketRequestUnavailable -->
<field name="websocketRequestUnavailable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for websocketRequestUnavailable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.websocketError -->
<field name="websocketError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for websocketError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.tlsClientCertificateError -->
<field name="tlsClientCertificateError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for tlsClientCertificateError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.tlsServerCertificateError -->
<field name="tlsServerCertificateError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for tlsServerCertificateError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.tlsClientAuthenticationFailed -->
<field name="tlsClientAuthenticationFailed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for tlsClientAuthenticationFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.tlsServerAuthenticationFailed -->
<field name="tlsServerAuthenticationFailed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for tlsServerAuthenticationFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.tlsClientCertificateExpired -->
<field name="tlsClientCertificateExpired"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for tlsClientCertificateExpired.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.tlsServerCertificateExpired -->
<field name="tlsServerCertificateExpired"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for tlsServerCertificateExpired.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.tlsClientCertificateRevoked -->
<field name="tlsClientCertificateRevoked"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for tlsClientCertificateRevoked.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.tlsServerCertificateRevoked -->
<field name="tlsServerCertificateRevoked"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for tlsServerCertificateRevoked.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.tlsError -->
<field name="tlsError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for tlsError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.dnsUnavailable -->
<field name="dnsUnavailable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for dnsUnavailable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.dnsNameResolutionFailed -->
<field name="dnsNameResolutionFailed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for dnsNameResolutionFailed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.dnsResolverFailure -->
<field name="dnsResolverFailure"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for dnsResolverFailure.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.dnsError -->
<field name="dnsError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for dnsError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.tcpConnectTimeout -->
<field name="tcpConnectTimeout"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for tcpConnectTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.tcpConnectionRefused -->
<field name="tcpConnectionRefused"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for tcpConnectionRefused.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.tcpClosedByLocal -->
<field name="tcpClosedByLocal"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for tcpClosedByLocal.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.tcpClosedOther -->
<field name="tcpClosedOther"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for tcpClosedOther.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.tcpError -->
<field name="tcpError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for tcpError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ipAddressNotReachable -->
<field name="ipAddressNotReachable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for ipAddressNotReachable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.ipError -->
<field name="ipError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for ipError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.nullValueEvent -->
<field name="nullValueEvent"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description>
BBacnetErrorCode constant for nullValueEvent.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetErrorCode"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TARGET_NOT_CONFIGURED -->
<field name="TARGET_NOT_CONFIGURED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for targetNotConfigured.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INVALID_TARGET_TYPE -->
<field name="INVALID_TARGET_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for invalidTargetType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.TARGET_NOT_CONFIGURED_TAG -->
<field name="TARGET_NOT_CONFIGURED_TAG"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
BBacnetErrorCode constant for targetNotConfigured.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.INVALID_TARGET_TYPE_TAG -->
<field name="INVALID_TARGET_TYPE_TAG"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
BBacnetErrorCode constant for invalidTargetType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NIAGARA_CODES -->
<field name="NIAGARA_CODES"  public="true" static="true" final="true">
<type class="int" dimension="1"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NIAGARA_TAGS -->
<field name="NIAGARA_TAGS"  public="true" static="true" final="true">
<type class="java.lang.String" dimension="1"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetErrorCode.NIAGARA_ERROR_CODES_RANGE -->
<field name="NIAGARA_ERROR_CODES_RANGE"  public="true" static="true" final="true">
<type class="javax.baja.sys.BEnumRange"/>
<description/>
</field>

</class>
</bajadoc>
