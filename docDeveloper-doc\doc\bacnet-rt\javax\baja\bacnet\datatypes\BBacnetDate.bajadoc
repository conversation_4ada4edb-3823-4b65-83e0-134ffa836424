<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetDate" name="BBacnetDate" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BBacnetDate represents a date value in a Bacnet property.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 6$ $Date: 11/8/01 9:04:51 AM$</tag>
<tag name="@creation">24 Sep 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BSimple"/>
</extends>
<implements>
<type class="javax.baja.sys.BIComparable"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
<!-- javax.baja.bacnet.datatypes.BBacnetDate.make() -->
<method name="make"  public="true" static="true">
<description>
Factory method for all unspecified.
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.make(int, int, int, int) -->
<method name="make"  public="true" static="true">
<description>
Factory method.
</description>
<parameter name="year">
<type class="int"/>
<description>
Year minus 1900, or 255 for unspecified
</description>
</parameter>
<parameter name="month">
<type class="int"/>
<description>
1(Jan) - 12(Dec), or 255 for unspecified
</description>
</parameter>
<parameter name="dayOfMonth">
<type class="int"/>
<description>
1-31, or 255 for unspecified
</description>
</parameter>
<parameter name="dayOfWeek">
<type class="int"/>
<description>
1(Mon) - 7(Sun), or 255 for unspecified
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.make(int, int, int) -->
<method name="make"  public="true" static="true">
<description>
Factory method.&#xa; dayOfWeek is calculated from year/month/day as long as none are unspecified or special
</description>
<parameter name="year">
<type class="int"/>
<description>
Year minus 1900, or 255 for unspecified
</description>
</parameter>
<parameter name="month">
<type class="int"/>
<description>
1(Jan) - 12(Dec), or 255 for unspecified
</description>
</parameter>
<parameter name="dayOfMonth">
<type class="int"/>
<description>
1-31, or 255 for unspecified
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.make(javax.baja.sys.BAbsTime) -->
<method name="make"  public="true" static="true">
<description>
Factory method from BAbsTime.
</description>
<parameter name="bt">
<type class="javax.baja.sys.BAbsTime"/>
<description>
BAbsTime.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description>
&lt;code&gt;BBacnetDate&lt;/code&gt; equality is based on all values being equal.&#xa; &lt;p&gt;&#xa; &lt;B&gt;NOTE&lt;/B&gt;: UNSPECIFIED values match ONLY other UNSPECIFIED values.&#xa; This is &lt;B&gt;not&lt;/B&gt; the same as Bacnet equivalence.&#xa; For Bacnet date equivalence, use the dateEquals() method.
</description>
<tag name="@see">BBacnetDate#dateEquals(Object)</tag>
<parameter name="obj">
<type class="java.lang.Object"/>
<description>
the comparison object
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the object is a &lt;code&gt;BBacnetDate&lt;/code&gt; with all values equal to this one.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.hashCode() -->
<method name="hashCode"  public="true">
<description>
BBacnetDate hashcode is a concatenation of all fields.
</description>
<return>
<type class="int"/>
<description>
a hash code computed by concatenating all fields.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.toString(javax.baja.sys.Context, boolean) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<parameter name="lexiconize">
<type class="boolean"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.encode(java.io.DataOutput) -->
<method name="encode"  public="true">
<description>
BBacnetDate is serialized using calls to writeByte().
</description>
<parameter name="out">
<type class="java.io.DataOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.decode(java.io.DataInput) -->
<method name="decode"  public="true">
<description>
BBacnetDate is unserialized using calls to readByte().
</description>
<parameter name="in">
<type class="java.io.DataInput"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.encodeToString() -->
<method name="encodeToString"  public="true">
<description>
Write the primitive in text format.
</description>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.decodeFromString(java.lang.String) -->
<method name="decodeFromString"  public="true">
<description>
Read the primitive from text format.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.getYear() -->
<method name="getYear"  public="true">
<description>
Get the year.
</description>
<return>
<type class="int"/>
<description>
the actual year represented by this BBacnetDate,&#xa; or -1 if unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.getRawYear() -->
<method name="getRawYear"  public="true">
<description>
Get the uncorrected year value.
</description>
<return>
<type class="int"/>
<description>
the actual value of the year byte.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.getMonth() -->
<method name="getMonth"  public="true">
<description>
Get the month.
</description>
<return>
<type class="int"/>
<description>
the actual month represented by this BBacnetDate:&#xa; 1=January, ..., 12=December&#xa; or -1 if unspecified,&#xa; or 13 if ODD MONTHS,&#xa; or 14 if EVEN MONTHS
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.getBMonth() -->
<method name="getBMonth"  public="true">
<description>
Get the month as a BMonth.
</description>
<return>
<type class="javax.baja.sys.BMonth"/>
<description>
the month, as a BMonth.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.getDayOfMonth() -->
<method name="getDayOfMonth"  public="true">
<description>
Get the dayOfMonth.
</description>
<return>
<type class="int"/>
<description>
the actual day of the month represented by this BBacnetDate,&#xa; or -1 if unspecified.&#xa; or 32 if last day of month&#xa; or 33 if odd days of month&#xa; or 34 if even days of month
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.getDayOfWeek() -->
<method name="getDayOfWeek"  public="true">
<description>
Get the dayOfWeek.
</description>
<return>
<type class="int"/>
<description>
the actual day of the week represented by this BBacnetDate,&#xa; or -1 if unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.getBWeekday() -->
<method name="getBWeekday"  public="true">
<description>
Get the day of the week as a BWeekday.
</description>
<return>
<type class="javax.baja.sys.BWeekday"/>
<description>
the dayOfWeek, as a BWeekday.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.isYearUnspecified() -->
<method name="isYearUnspecified"  public="true">
<description>
Is the year unspecified?
</description>
<return>
<type class="boolean"/>
<description>
true if the year is unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.isMonthUnspecified() -->
<method name="isMonthUnspecified"  public="true">
<description>
Is the month unspecified?
</description>
<return>
<type class="boolean"/>
<description>
true if the month is unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.isOddMonths() -->
<method name="isOddMonths"  public="true">
<description>
Is the month unspecified?
</description>
<return>
<type class="boolean"/>
<description>
true if the month is unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.isEvenMonths() -->
<method name="isEvenMonths"  public="true">
<description>
Is the month unspecified?
</description>
<return>
<type class="boolean"/>
<description>
true if the month is unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.isDayOfMonthUnspecified() -->
<method name="isDayOfMonthUnspecified"  public="true">
<description>
Is the dayOfMonth unspecified?
</description>
<return>
<type class="boolean"/>
<description>
true if the dayOfMonth is unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.isLastDayOfMonth() -->
<method name="isLastDayOfMonth"  public="true">
<description>
Is the dayOfMonth unspecified?
</description>
<return>
<type class="boolean"/>
<description>
true if the dayOfMonth is unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.isOddDaysOfMonth() -->
<method name="isOddDaysOfMonth"  public="true">
<description>
Is the oddDaysOfMonth unspecified?
</description>
<return>
<type class="boolean"/>
<description>
true if oddDaysOfMonth is specified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.isEvenDaysOfMonth() -->
<method name="isEvenDaysOfMonth"  public="true">
<description>
Is the evenDaysOfMonth unspecified?
</description>
<return>
<type class="boolean"/>
<description>
true if evenDaysOfMonth is specified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.isDayOfWeekUnspecified() -->
<method name="isDayOfWeekUnspecified"  public="true">
<description>
Is the dayOfWeek unspecified?
</description>
<return>
<type class="boolean"/>
<description>
true if the dayOfWeek is unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.isAnyUnspecified() -->
<method name="isAnyUnspecified"  public="true">
<description>
Is any field unspecified?
</description>
<return>
<type class="boolean"/>
<description>
true if any field is unspecified.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.dateEquals(java.lang.Object) -->
<method name="dateEquals"  public="true">
<description>
BBacnetDate equivalence is based on all values being equal,&#xa; or unspecified.&#xa; &lt;B&gt;NOTE&lt;/B&gt;: This is the method to determine date equivalence according&#xa; to BACnet, &lt;B&gt;not&lt;/B&gt; the equals() method, which requires UNSPECIFIED values&#xa; to match &lt;B&gt;only&lt;/B&gt; with UNSPECIFIED values.
</description>
<tag name="@see">BBacnetDate#equals(Object)</tag>
<parameter name="obj">
<type class="java.lang.Object"/>
<description>
the comparison object.
</description>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.compareTo(java.lang.Object) -->
<method name="compareTo"  public="true">
<description>
Compare to another BBacnetDate.
</description>
<parameter name="obj">
<type class="java.lang.Object"/>
<description>
the comparison object.
</description>
</parameter>
<return>
<type class="int"/>
<description>
a negative integer, zero, or a&#xa; positive integer as this object is less&#xa; than, equal to, or greater than the&#xa; specified object.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.isBefore(java.lang.Object) -->
<method name="isBefore"  public="true">
<description/>
<parameter name="x">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the specified date is before this date.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.isAfter(java.lang.Object) -->
<method name="isAfter"  public="true">
<description/>
<parameter name="x">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the specified date is after this date.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.isNotBefore(java.lang.Object) -->
<method name="isNotBefore"  public="true">
<description/>
<parameter name="x">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the specified date is not before this date.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.isNotAfter(java.lang.Object) -->
<method name="isNotAfter"  public="true">
<description/>
<parameter name="x">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the specified date is not after this date.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.makeBAbsTime(javax.baja.sys.BAbsTime) -->
<method name="makeBAbsTime"  public="true">
<description>
Create a BAbsTime from this BBacnetDate.&#xa; Use values from the given BAbsTime for unspecified&#xa; fields and the time.
</description>
<parameter name="date">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.fromString(java.lang.String) -->
<method name="fromString"  public="true" static="true">
<description>
Read the time values from the&#xa; given String and return a new BBacnetDate.
</description>
<parameter name="s">
<type class="java.lang.String"/>
<description>
the input string.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
<description>
a BBacnetDate read from the string.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.UNSPECIFIED -->
<field name="UNSPECIFIED"  public="true" static="true" final="true">
<type class="byte"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.ODD_MONTHS -->
<field name="ODD_MONTHS"  public="true" static="true" final="true">
<type class="byte"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.EVEN_MONTHS -->
<field name="EVEN_MONTHS"  public="true" static="true" final="true">
<type class="byte"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.BAJA_ODD_MONTHS -->
<field name="BAJA_ODD_MONTHS"  public="true" static="true" final="true">
<type class="byte"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.BAJA_EVEN_MONTHS -->
<field name="BAJA_EVEN_MONTHS"  public="true" static="true" final="true">
<type class="byte"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.LAST_DAY_OF_MONTH -->
<field name="LAST_DAY_OF_MONTH"  public="true" static="true" final="true">
<type class="byte"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.ODD_DAYS_OF_MONTH -->
<field name="ODD_DAYS_OF_MONTH"  public="true" static="true" final="true">
<type class="byte"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.EVEN_DAYS_OF_MONTH -->
<field name="EVEN_DAYS_OF_MONTH"  public="true" static="true" final="true">
<type class="byte"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.BAJA_ALWAYS_EFFECTIVE -->
<field name="BAJA_ALWAYS_EFFECTIVE"  public="true" static="true" final="true">
<type class="byte"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.BAJA_LAST_7_DAYS_OF_MONTH -->
<field name="BAJA_LAST_7_DAYS_OF_MONTH"  public="true" static="true" final="true">
<type class="byte"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.BAJA_ODD_DAYS_OF_MONTH -->
<field name="BAJA_ODD_DAYS_OF_MONTH"  public="true" static="true" final="true">
<type class="byte"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.BAJA_EVEN_DAYS_OF_MONTH -->
<field name="BAJA_EVEN_DAYS_OF_MONTH"  public="true" static="true" final="true">
<type class="byte"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.datatypes.BBacnetDate"/>
<description>
The default date is all unspecified.
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetDate.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
