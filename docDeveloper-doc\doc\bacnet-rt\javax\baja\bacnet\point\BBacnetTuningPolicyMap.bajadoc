<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.point.BBacnetTuningPolicyMap" name="BBacnetTuningPolicyMap" packageName="javax.baja.bacnet.point" public="true">
<description>
BBacnetTuningPolicyMap.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">08 Jul 2004</tag>
<tag name="@since">Niagara 3 BACnet 1.0</tag>
<extends>
<type class="javax.baja.driver.point.BTuningPolicyMap"/>
</extends>
<!-- javax.baja.bacnet.point.BBacnetTuningPolicyMap() -->
<constructor name="BBacnetTuningPolicyMap" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicyMap.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicyMap.isChildLegal(javax.baja.sys.BComponent) -->
<method name="isChildLegal"  public="true">
<description>
Only BacnetTuningPolicy children are allowed.
</description>
<parameter name="child">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicyMap.defaultPolicy -->
<field name="defaultPolicy"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;defaultPolicy&lt;/code&gt; property.
</description>
<tag name="@see">#getDefaultPolicy</tag>
<tag name="@see">#setDefaultPolicy</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetTuningPolicyMap.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
