<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.ext.BIAlarmMessages" name="BIAlarmMessages" packageName="javax.baja.alarm.ext" public="true" interface="true" abstract="true" category="interface">
<description/>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<!-- javax.baja.alarm.ext.BIAlarmMessages.getToOffnormalText() -->
<method name="getToOffnormalText"  public="true" abstract="true">
<description/>
<return>
<type class="javax.baja.util.BFormat"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BIAlarmMessages.getToFaultText() -->
<method name="getToFaultText"  public="true" abstract="true">
<description/>
<return>
<type class="javax.baja.util.BFormat"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BIAlarmMessages.getToNormalText() -->
<method name="getToNormalText"  public="true" abstract="true">
<description/>
<return>
<type class="javax.baja.util.BFormat"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BIAlarmMessages.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
