<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>driver Module: nmodule/driver/rc/wb/mgr/DriverMgr</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">driver</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-nmodule_driver_rc_wb_mgr_DeviceMgr.html">nmodule/driver/rc/wb/mgr/DeviceMgr</a></li><li><a href="module-nmodule_driver_rc_wb_mgr_DeviceMgrModel.html">nmodule/driver/rc/wb/mgr/DeviceMgrModel</a></li><li><a href="module-nmodule_driver_rc_wb_mgr_DriverMgr.html">nmodule/driver/rc/wb/mgr/DriverMgr</a></li><li><a href="module-nmodule_driver_rc_wb_mgr_PointMgr.html">nmodule/driver/rc/wb/mgr/PointMgr</a></li><li><a href="module-nmodule_driver_rc_wb_mgr_PointMgrModel.html">nmodule/driver/rc/wb/mgr/PointMgrModel</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: nmodule/driver/rc/wb/mgr/DriverMgr</h1>
<section>

<header>
    
        
            
        
    
</header>


<article>
    <div class="container-overview">
    
        

        
            
<hr>
<dt>
    <h4 class="name" id="module:nmodule/driver/rc/wb/mgr/DriverMgr"><span class="type-signature"></span>new (require("nmodule/driver/rc/wb/mgr/DriverMgr"))()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>API Status: <strong>Development</strong></p>
<p>DriverMgr constructor. Contains functionality for working with components<br>
within a driver network.</p>
<p>There is usually no reason to extend this directly; extend <code>DeviceMgr</code> or<br>
<code>PointMgr</code> instead.</p>
    </div>
    

    
        <h5>Extends:</h5>
        


    <ul>
        <li>module:nmodule/webEditors/rc/wb/mgr/Manager</li>
    </ul>


    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params.keyName</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>the key name used for lexicon entries for this view.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params.moduleName</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>the module name used for lexicon entries for this view.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params.subscriptionDepth</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>the depth to subscribe the component tree.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params.subscriptionFilter</code></td>
            

            <td class="type">
            
                
<span class="param-type">module:nmodule/webEditors/rc/wb/util/subscriptionUtil~SubscriptionFilter</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>Starting in Niagara 4.13, if the optional subscriptionFilter function is provided, it will be called for each<br>
potentially subscribable BComponent with its current depth. By returning true only for the desired components,<br>
the subscription will subscribe to what is needed.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params.subscribeCallback</code></td>
            

            <td class="type">
            
                
<span class="param-type">module:nmodule/webEditors/rc/wb/util/subscriptionUtil~SubscribeCallback</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>Starting in Niagara 4.13, if the optional subscribeCallback function is provided, it will receive a callback when a component is subscribed.<br>
This can allow you to add additional subscriptions outside the normal depth and filter results.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params.folderType</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Type</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>optional parameter indicating the folder type<br>
used for the manager view. This will be used by the NewFolder command.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li><a href="module-nmodule_driver_rc_wb_mgr_DeviceMgr.html">module:nmodule/driver/rc/wb/mgr/DeviceMgr</a></li>
			
			<li><a href="module-nmodule_driver_rc_wb_mgr_PointMgr.html">module:nmodule/driver/rc/wb/mgr/PointMgr</a></li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id="buildMainTableCell"><span class="type-signature"></span>buildMainTableCell(column, row, dom)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Override of the base manager's build cell function.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>column</code></td>
            

            <td class="type">
            
                
<span class="param-type">module:nmodule/webEditors/rc/wb/table/model/Column</span>



            
            </td>

            

            

            <td class="description last"><p>The column for the cell</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>row</code></td>
            

            <td class="type">
            
                
<span class="param-type">module:nmodule/webEditors/rc/wb/table/model/Row</span>



            
            </td>

            

            

            <td class="description last"><p>The row for the cell</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dom</code></td>
            

            <td class="type">
            
                
<span class="param-type">JQuery</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="componentAdded"><span class="type-signature"></span>componentAdded(parent, child)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Function called when a new component is added. If the<br>
component is a folder and the all descendants command is selected,<br>
we want to subscribe to that folder to the correct depth.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>parent</code></td>
            

            <td class="type">
            
                
<span class="param-type">baja.Component</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>child</code></td>
            

            <td class="type">
            
                
<span class="param-type">baja.Component</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="componentChanged"><span class="type-signature"></span>componentChanged(comp)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Function called when a property of one of the row's subjects or descendants<br>
changes. This is used to update the table when, for example, a property<br>
on a point's proxy extension is changed.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>comp</code></td>
            

            <td class="type">
            
                
<span class="param-type">baja.Component</span>



            
            </td>

            

            

            <td class="description last"><p>The component notifying the changed property.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="componentRemoved"><span class="type-signature"></span>componentRemoved(value)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Function called when a subscribed component is removed. If the<br>
component was a folder and the all descendants command is selected,<br>
we want to unsubscribe to that folder.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>value</code></td>
            

            <td class="type">
            
                
<span class="param-type">baja.Component</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="componentRenamed"><span class="type-signature"></span>componentRenamed(comp)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Function called when the depth subscriber notifies a renamed component. This will<br>
try to emit a 'changed' event on the component source.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>comp</code></td>
            

            <td class="type">
            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="doDestroy"><span class="type-signature"></span>doDestroy()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Destroy the widget. This will clean up the event handler we have attached<br>
for listening to descendant changes.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">*</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="doInitialize"><span class="type-signature"></span>doInitialize(dom)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Manager initialization. In addition to the base manager initialization, this will configure<br>
double click handling on the table rows and ensure that the basic commands are in the<br>
appropriate default state.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>dom</code></td>
            

            <td class="type">
            
                
<span class="param-type">JQuery</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">*</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="doLoad"><span class="type-signature"></span>doLoad(comp)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Load the widget from the component. This will hook up the event handlers to the<br>
depth subscriber used by this type.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>comp</code></td>
            

            <td class="type">
            
                
<span class="param-type">baja.Component</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="finishMainTableRow"><span class="type-signature"></span>finishMainTableRow(row, dom)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Overrides the basic manager <code>#finishMainTableRow</code> function with some extra css information<br>
specified on the dom for the table row.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>row</code></td>
            

            <td class="type">
            
                
<span class="param-type">module:nmodule/webEditors/rc/wb/table/model/Row</span>



            
            </td>

            

            

            <td class="description last"><p>a table row instance</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dom</code></td>
            

            <td class="type">
            
                
<span class="param-type">JQuery</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getSubject"><span class="type-signature"></span>getSubject(elem)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the subject via the manager's main table.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>elem</code></td>
            

            <td class="type">
            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">*</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getSubscriptionDepth"><span class="type-signature"></span>getSubscriptionDepth()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the configured component subscription depth for the driver manager.<br>
This value is specified by the 'subscriptionDepth' parameter property<br>
in the constructor.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="makeCommands"><span class="type-signature"></span>makeCommands()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the default set of <code>Command</code> instances for a device manager. The<br>
basic set are commands for creating a new folder (if a folder type was<br>
specified in the constructor's parameters), creating a new point type<br>
and editing an existing point type.</p>
<p>Concrete point manager types may override this function to append extra<br>
commands and/or remove the default ones.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array.&lt;module:bajaux/commands/Command></span>



    </dd>
</dl>


        

    
</dd>

        </dl>
    

    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	driver Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:55+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>