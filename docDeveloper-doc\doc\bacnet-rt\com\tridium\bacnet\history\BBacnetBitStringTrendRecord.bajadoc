<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.history.BBacnetBitStringTrendRecord" name="BBacnetBitStringTrendRecord" packageName="com.tridium.bacnet.history" public="true">
<description>
&lt;code&gt;BBacnetBitStringTrendRecord&lt;/code&gt; is a Bacnet trend record&#xa; with BITSTRING value type.&#xa;&#xa; The following are the steps involved in serializing the bit string&#xa; &lt;ul&gt;&#xa;   &lt;li&gt; A total of 64 bits is reserved for the trend record BITSTRING&lt;/li&gt;&#xa;   &lt;li&gt; The least significant `MAX_BITS_SUPPORTED --&gt; 50` bits will be used to store the bit string &lt;/li&gt;&#xa;   &lt;li&gt; The most significant `(64 - MAX_BITS_SUPPORTED) --&gt; 13` bits store the length &lt;/li&gt;&#xa; &lt;/ul&gt;
</description>
<extends>
<type class="com.tridium.bacnet.history.BBacnetTrendRecord"/>
</extends>
<property name="value" flags="s">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
Slot for the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#getValue</tag>
<tag name="@see">#setValue</tag>
</property>

</class>
</bajadoc>
