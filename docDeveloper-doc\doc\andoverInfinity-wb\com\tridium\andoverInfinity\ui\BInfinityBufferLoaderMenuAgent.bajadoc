<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="wb" qualifiedName="com.tridium.andoverInfinity.ui.BInfinityBufferLoaderMenuAgent" name="BInfinityBufferLoaderMenuAgent" packageName="com.tridium.andoverInfinity.ui" public="true">
<description>
BInfinityBufferLoaderMenuAgent is an agent to load and save the BVt100&#xa; screen buffer to/from a file on the client machine.  This is used for&#xa; documentation and debug purposes.  As an example, it would be useful&#xa; if a situation popped up in the field where the driver does not correctly&#xa; recognize where it is at in the menu system, the field person could&#xa; save a copy of the screen buffer, send it to tech support or engineering,&#xa; and they could then load the buffer and re-create the problem.
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">Sep 10, 2007</tag>
<tag name="@version">$Revision$ $Sep 10, 2007 3:48:48 PM$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.workbench.nav.BComponentMenuAgent"/>
</extends>
<implements>
<type class="javax.baja.agent.BIAgent"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraSingleton"/>
</annotation>
<!-- com.tridium.andoverInfinity.ui.BInfinityBufferLoaderMenuAgent() -->
<constructor name="BInfinityBufferLoaderMenuAgent" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.ui.BInfinityBufferLoaderMenuAgent.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.ui.BInfinityBufferLoaderMenuAgent.doMakeMenu(javax.baja.ui.BWidget, javax.baja.sys.BObject, javax.baja.sys.Context) -->
<method name="doMakeMenu"  public="true">
<description/>
<parameter name="owner">
<type class="javax.baja.ui.BWidget"/>
</parameter>
<parameter name="target">
<type class="javax.baja.sys.BObject"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.ui.BMenu"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.ui.BInfinityBufferLoaderMenuAgent.INSTANCE -->
<field name="INSTANCE"  public="true" static="true" final="true">
<type class="com.tridium.andoverInfinity.ui.BInfinityBufferLoaderMenuAgent"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.ui.BInfinityBufferLoaderMenuAgent.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
