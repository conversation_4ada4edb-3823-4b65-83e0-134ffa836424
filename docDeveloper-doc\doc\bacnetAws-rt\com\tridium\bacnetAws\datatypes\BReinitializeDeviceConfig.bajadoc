<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="com.tridium.bacnetAws.datatypes.BReinitializeDeviceConfig" name="BReinitializeDeviceConfig" packageName="com.tridium.bacnetAws.datatypes" public="true">
<description>
BReinitializeDeviceConfig represents the choices for the&#xa; user in manually issuing a ReinitializeDevice-Request to a device.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">04 Aug 2004</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.datatypes.BRequestConfig"/>
</extends>
<property name="deviceAddress" flags="h">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
Slot for the &lt;code&gt;deviceAddress&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceAddress</tag>
<tag name="@see">#setDeviceAddress</tag>
</property>

<property name="characterSet" flags="h">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
<description>
Slot for the &lt;code&gt;characterSet&lt;/code&gt; property.
</description>
<tag name="@see">#getCharacterSet</tag>
<tag name="@see">#setCharacterSet</tag>
</property>

<property name="reinitializeCommand" flags="">
<type class="com.tridium.bacnetAws.enums.BReinitializeCommand"/>
<description>
Slot for the &lt;code&gt;reinitializeCommand&lt;/code&gt; property.
</description>
<tag name="@see">#getReinitializeCommand</tag>
<tag name="@see">#setReinitializeCommand</tag>
</property>

<property name="password" flags="">
<type class="javax.baja.security.BPassword"/>
<description>
Slot for the &lt;code&gt;password&lt;/code&gt; property.
</description>
<tag name="@see">#getPassword</tag>
<tag name="@see">#setPassword</tag>
</property>

</class>
</bajadoc>
