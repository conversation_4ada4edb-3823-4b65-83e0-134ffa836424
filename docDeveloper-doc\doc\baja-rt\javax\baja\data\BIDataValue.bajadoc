<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.data.BIDataValue" name="BIDataValue" packageName="javax.baja.data" public="true" interface="true" abstract="true" category="interface">
<description>
BIDataValue is the interface implemented by BSimples that&#xa; are one of the predefined data types in &lt;code&gt;<see ref="javax.baja.data.DataTypes">DataTypes</see>&lt;/code&gt;.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">21 Feb 03</tag>
<tag name="@version">$Revision: 3$ $Date: 5/6/03 4:03:47 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<implements>
<type class="javax.baja.io.BIEncodable"/>
</implements>
<!-- javax.baja.data.BIDataValue.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
