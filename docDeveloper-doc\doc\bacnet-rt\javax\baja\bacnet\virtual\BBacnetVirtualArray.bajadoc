<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.virtual.BBacnetVirtualArray" name="BBacnetVirtualArray" packageName="javax.baja.bacnet.virtual" public="true">
<description/>
<tag name="@author">cgemmill</tag>
<tag name="@deprecated"/>
<extends>
<type class="javax.baja.virtual.BVirtualComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<property name="arrayTypeSpec" flags="h">
<type class="javax.baja.util.BTypeSpec"/>
<description>
Slot for the &lt;code&gt;arrayTypeSpec&lt;/code&gt; property.
</description>
<tag name="@see">#getArrayTypeSpec</tag>
<tag name="@see">#setArrayTypeSpec</tag>
</property>

<property name="size" flags="h">
<type class="int"/>
<description>
Slot for the &lt;code&gt;size&lt;/code&gt; property.
</description>
<tag name="@see">#getSize</tag>
<tag name="@see">#setSize</tag>
</property>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray() -->
<constructor name="BBacnetVirtualArray" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray(javax.baja.sys.Type) -->
<constructor name="BBacnetVirtualArray" public="true">
<parameter name="arrayType">
<type class="javax.baja.sys.Type"/>
<description>
the type of elements to be contained by this array.
</description>
</parameter>
<description>
Constructor with type specification.
</description>
</constructor>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray(javax.baja.sys.Type, int) -->
<constructor name="BBacnetVirtualArray" public="true">
<parameter name="arrayType">
<type class="javax.baja.sys.Type"/>
<description>
the type of elements to be contained by this array.
</description>
</parameter>
<parameter name="fixedSize">
<type class="int"/>
<description>
the fixed size of this array.
</description>
</parameter>
<description>
Constructor with type specification and fixed size.
</description>
</constructor>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.getArrayTypeSpec() -->
<method name="getArrayTypeSpec"  public="true">
<description>
Get the &lt;code&gt;arrayTypeSpec&lt;/code&gt; property.
</description>
<tag name="@see">#arrayTypeSpec</tag>
<return>
<type class="javax.baja.util.BTypeSpec"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.setArrayTypeSpec(javax.baja.util.BTypeSpec) -->
<method name="setArrayTypeSpec"  public="true">
<description>
Set the &lt;code&gt;arrayTypeSpec&lt;/code&gt; property.
</description>
<tag name="@see">#arrayTypeSpec</tag>
<parameter name="v">
<type class="javax.baja.util.BTypeSpec"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.getSize() -->
<method name="getSize"  public="true">
<description>
Get the &lt;code&gt;size&lt;/code&gt; property.
</description>
<tag name="@see">#size</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.setSize(int) -->
<method name="setSize"  public="true">
<description>
Set the &lt;code&gt;size&lt;/code&gt; property.
</description>
<tag name="@see">#size</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.added(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="added"  public="true">
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.removed(javax.baja.sys.Property, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="removed"  public="true">
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="oldValue">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true" final="true">
<description>
Property changed.&#xa; We may need to write the new value to a remote device.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.subscribed() -->
<method name="subscribed"  public="true" final="true">
<description>
Callback when the component enters the subscribed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.unsubscribed() -->
<method name="unsubscribed"  public="true">
<description>
Callback when the component leaves the subscribed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<description>
VirtualArrays can only be children of BacnetVirtualComponents.
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.getElement(int) -->
<method name="getElement"  public="true" final="true">
<description>
Get the element at this index.&#xa; If index is zero, return the array size.&#xa; Note the size will, in general, not be accurate, because&#xa; all of the array elements are not usually loaded in the virtual space.
</description>
<parameter name="index">
<type class="int"/>
<description>
the (1-N) array index of the object requested.
</description>
</parameter>
<return>
<type class="javax.baja.sys.BValue"/>
<description>
the object at this index, or null.
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.setElement(int, javax.baja.sys.BValue) -->
<method name="setElement"  public="true" final="true">
<description>
Set the element at this index.&#xa; May not be possible if the virtual slot for the&#xa; array element has not yet been loaded.
</description>
<parameter name="index">
<type class="int"/>
<description>
the array element to set.
</description>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
<description>
the new array element.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.index(java.lang.String) -->
<method name="index"  public="true" static="true">
<description/>
<parameter name="propName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true" final="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true" final="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.readAsn(javax.baja.bacnet.io.AsnInput, int) -->
<method name="readAsn"  public="true">
<description/>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
</parameter>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.writeAsn(javax.baja.bacnet.io.AsnOutput, int) -->
<method name="writeAsn"  public="true" final="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.arrayTypeSpec -->
<field name="arrayTypeSpec"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;arrayTypeSpec&lt;/code&gt; property.
</description>
<tag name="@see">#getArrayTypeSpec</tag>
<tag name="@see">#setArrayTypeSpec</tag>
</field>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.size -->
<field name="size"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;size&lt;/code&gt; property.
</description>
<tag name="@see">#getSize</tag>
<tag name="@see">#setSize</tag>
</field>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualArray.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
