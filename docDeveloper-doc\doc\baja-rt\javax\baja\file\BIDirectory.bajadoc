<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BIDirectory" name="BIDirectory" packageName="javax.baja.file" public="true" interface="true" abstract="true" category="interface">
<description>
BIDirectory is a container of BIFiles
</description>
<tag name="@author"><PERSON> on 24 Jan 03</tag>
<tag name="@version">$Revision: 3$ $Date: 3/28/05 9:22:56 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<implements>
<type class="javax.baja.nav.BINavNode"/>
</implements>
<!-- javax.baja.file.BIDirectory.listFiles() -->
<method name="listFiles"  public="true" abstract="true">
<description>
Get the list of containing files.
</description>
<return>
<type class="javax.baja.file.BIFile" dimension="1"/>
</return>
</method>

<!-- javax.baja.file.BIDirectory.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
