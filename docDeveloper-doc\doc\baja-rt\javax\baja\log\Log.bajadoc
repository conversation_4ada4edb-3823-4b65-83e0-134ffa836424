<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.log.Log" name="Log" packageName="javax.baja.log" public="true" final="true">
<description>
Log is used to log system events.&#xa;&#xa; Use of this class is strongly discouraged. &lt;code&gt;java.util.logging&lt;/code&gt; should&#xa; be used instead.
</description>
<tag name="@author"><PERSON>&#xa; creation  9 Apr 00</tag>
<tag name="@version">$Revision: 14$ $Date: 4/28/11 1:56:05 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;deprecation&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.log.Log.severityToString(int) -->
<method name="severityToString"  public="true" static="true">
<description>
Get a string constant for the specified&#xa; severity level.
</description>
<parameter name="severity">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.log.Log.severityFromString(java.lang.String) -->
<method name="severityFromString"  public="true" static="true">
<description>
Convert a severity string constant back&#xa; into a severity integer constant.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.log.Log.getLogs() -->
<method name="getLogs"  public="true" static="true" synchronized="true">
<description>
Get the list of all the registered Logs.
</description>
<return>
<type class="javax.baja.log.Log" dimension="1"/>
</return>
</method>

<!-- javax.baja.log.Log.isLog(java.lang.String) -->
<method name="isLog"  public="true" static="true" synchronized="true">
<description/>
<parameter name="logName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.log.Log.getLog(java.lang.String) -->
<method name="getLog"  public="true" static="true" synchronized="true">
<description>
Get the Log with the specified name.  If the Log&#xa; doesn&#x27;t exist yet, then it is created.  By convention&#xa; this name should match the package name of the code&#xa; using this Log instance.&#xa; &lt;p&gt;&#xa; The severity level of the log is no longer initialized&#xa; from &#x22;log.properties&#x22; file as it was in r38.  It is&#xa; now derived from the Level that JUL returns when we&#xa; invoke getLevel on the Logger with the same name.
</description>
<parameter name="logName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.log.Log"/>
</return>
</method>

<!-- javax.baja.log.Log.deleteLog(java.lang.String) -->
<method name="deleteLog"  public="true" static="true" synchronized="true">
<description>
Delete the specified log.
</description>
<parameter name="logName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.log.Log.levelToSeverity(java.util.logging.Level) -->
<method name="levelToSeverity"  public="true" static="true">
<description/>
<parameter name="level">
<type class="java.util.logging.Level"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.log.Log.getLogName() -->
<method name="getLogName"  public="true">
<description>
Get the name of this log.  By convention the name&#xa; should match the Java package name of the software&#xa; utilizing this Log instance.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.log.Log.isLoggable(int) -->
<method name="isLoggable"  public="true">
<description>
Is the specified severity currently&#xa; enabled to be logged.
</description>
<parameter name="severity">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.log.Log.isTraceOn() -->
<method name="isTraceOn"  public="true">
<description>
Return true if trace is on.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.log.Log.getSeverity() -->
<method name="getSeverity"  public="true">
<description>
Get the maximum severity which is currently&#xa; enabled to be logged by this Log.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.log.Log.getSeverityString() -->
<method name="getSeverityString"  public="true">
<description>
Ge the maximum serverity enabled as a String.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.log.Log.setSeverity(int) -->
<method name="setSeverity"  public="true">
<description>
Set the maximum severity which will be&#xa; logged by this Log.
</description>
<parameter name="maxSeverity">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.log.Log.error(java.lang.String) -->
<method name="error"  public="true">
<description>
Log a message with an ERROR severity.
</description>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.log.Log.error(java.lang.String, java.lang.Throwable) -->
<method name="error"  public="true">
<description>
Log a message with an ERROR severity.
</description>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="ex">
<type class="java.lang.Throwable"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.log.Log.warning(java.lang.String) -->
<method name="warning"  public="true">
<description>
Log a message with an WARNING severity.
</description>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.log.Log.warning(java.lang.String, java.lang.Throwable) -->
<method name="warning"  public="true">
<description>
Log a message with an WARNING severity.
</description>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="ex">
<type class="java.lang.Throwable"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.log.Log.message(java.lang.String) -->
<method name="message"  public="true">
<description>
Log a message with an MESSAGE severity.
</description>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.log.Log.message(java.lang.String, java.lang.Throwable) -->
<method name="message"  public="true">
<description>
Log a message with an MESSAGE severity.
</description>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="ex">
<type class="java.lang.Throwable"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.log.Log.trace(java.lang.String) -->
<method name="trace"  public="true">
<description>
Log a message with an TRACE severity.
</description>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.log.Log.trace(java.lang.String, java.lang.Throwable) -->
<method name="trace"  public="true">
<description>
Log a message with an TRACE severity.
</description>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="ex">
<type class="java.lang.Throwable"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.log.Log.trace(byte[]) -->
<method name="trace"  public="true">
<description/>
<parameter name="buf">
<type class="byte" dimension="1"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.log.Log.trace(byte[], int, int) -->
<method name="trace"  public="true">
<description/>
<parameter name="buf">
<type class="byte" dimension="1"/>
</parameter>
<parameter name="offset">
<type class="int"/>
</parameter>
<parameter name="length">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.log.Log.trace(java.lang.String, byte[]) -->
<method name="trace"  public="true">
<description/>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="buf">
<type class="byte" dimension="1"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.log.Log.trace(java.lang.String, byte[], int, int) -->
<method name="trace"  public="true">
<description/>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="buf">
<type class="byte" dimension="1"/>
</parameter>
<parameter name="offset">
<type class="int"/>
</parameter>
<parameter name="length">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.log.Log.log(int, java.lang.String, java.lang.Throwable) -->
<method name="log"  public="true">
<description>
Log a message with the specified severity,&#xa; String message, and exception.
</description>
<parameter name="severity">
<type class="int"/>
</parameter>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="ex">
<type class="java.lang.Throwable"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.log.Log.log(int, java.lang.String, java.lang.Throwable, byte[]) -->
<method name="log"  public="true">
<description/>
<parameter name="severity">
<type class="int"/>
</parameter>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="ex">
<type class="java.lang.Throwable"/>
</parameter>
<parameter name="buf">
<type class="byte" dimension="1"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.log.Log.log(int, java.lang.String, java.lang.Throwable, byte[], int, int) -->
<method name="log"  public="true">
<description/>
<parameter name="severity">
<type class="int"/>
</parameter>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="ex">
<type class="java.lang.Throwable"/>
</parameter>
<parameter name="buf">
<type class="byte" dimension="1"/>
</parameter>
<parameter name="offset">
<type class="int"/>
</parameter>
<parameter name="length">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.log.Log.NONE -->
<field name="NONE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Severtiy constant of 4
</description>
</field>

<!-- javax.baja.log.Log.ERROR -->
<field name="ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Severtiy constant of 3
</description>
</field>

<!-- javax.baja.log.Log.WARNING -->
<field name="WARNING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Severtiy constant of 2
</description>
</field>

<!-- javax.baja.log.Log.MESSAGE -->
<field name="MESSAGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Severtiy constant of 1
</description>
</field>

<!-- javax.baja.log.Log.TRACE -->
<field name="TRACE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Severtiy constant of 0
</description>
</field>

</class>
</bajadoc>
