<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="javax.baja.bacnetAws.datatypes.BBacnetActionList" name="BBacnetActionList" packageName="javax.baja.bacnetAws.datatypes" public="true">
<description/>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<action name="addActionCommand" flags="">
<parameter name="parameter">
<type class="javax.baja.bacnetAws.datatypes.BBacnetActionCommand"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;addActionCommand&lt;/code&gt; action.
</description>
<tag name="@see">#addActionCommand(BBacnetActionCommand parameter)</tag>
</action>

<action name="removeActionCommand" flags="">
<parameter name="parameter">
<type class="javax.baja.sys.BString"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;removeActionCommand&lt;/code&gt; action.
</description>
<tag name="@see">#removeActionCommand(BString parameter)</tag>
</action>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionList() -->
<constructor name="BBacnetActionList" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionList.addActionCommand(javax.baja.bacnetAws.datatypes.BBacnetActionCommand) -->
<method name="addActionCommand"  public="true">
<description>
Invoke the &lt;code&gt;addActionCommand&lt;/code&gt; action.
</description>
<tag name="@see">#addActionCommand</tag>
<parameter name="parameter">
<type class="javax.baja.bacnetAws.datatypes.BBacnetActionCommand"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionList.removeActionCommand(javax.baja.sys.BString) -->
<method name="removeActionCommand"  public="true">
<description>
Invoke the &lt;code&gt;removeActionCommand&lt;/code&gt; action.
</description>
<tag name="@see">#removeActionCommand</tag>
<parameter name="parameter">
<type class="javax.baja.sys.BString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionList.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionList.doAddActionCommand(javax.baja.bacnetAws.datatypes.BBacnetActionCommand) -->
<method name="doAddActionCommand"  public="true">
<description/>
<parameter name="arg">
<type class="javax.baja.bacnetAws.datatypes.BBacnetActionCommand"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionList.doRemoveActionCommand(javax.baja.sys.BString) -->
<method name="doRemoveActionCommand"  public="true">
<description/>
<parameter name="arg">
<type class="javax.baja.sys.BString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionList.isChildLegal(javax.baja.sys.BComponent) -->
<method name="isChildLegal"  public="true">
<description/>
<parameter name="child">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionList.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<description>
Changed. Pass callback to parent
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionList.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionList.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description/>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionList.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description/>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionList.addActionCommand -->
<field name="addActionCommand"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;addActionCommand&lt;/code&gt; action.
</description>
<tag name="@see">#addActionCommand(BBacnetActionCommand parameter)</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionList.removeActionCommand -->
<field name="removeActionCommand"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;removeActionCommand&lt;/code&gt; action.
</description>
<tag name="@see">#removeActionCommand(BString parameter)</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionList.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionList.ACTION_TAG -->
<field name="ACTION_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
