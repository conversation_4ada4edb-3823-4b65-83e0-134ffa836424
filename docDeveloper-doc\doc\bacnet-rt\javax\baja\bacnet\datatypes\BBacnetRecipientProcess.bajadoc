<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetRecipientProcess" name="BBacnetRecipientProcess" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BBacnetRecipientProcess represents the Bacnet RecipientProcess&#xa; sequence, used in Cov notifications.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 5$ $Date: 12/10/01 9:26:07 AM$</tag>
<tag name="@creation">31 Jul 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="recipient" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetRecipient"/>
<description>
Slot for the &lt;code&gt;recipient&lt;/code&gt; property.&#xa; the recipient address information.
</description>
<tag name="@see">#getRecipient</tag>
<tag name="@see">#setRecipient</tag>
</property>

<property name="processIdentifier" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;processIdentifier&lt;/code&gt; property.&#xa; a numeric &#x22;handle&#x22; meaningful to the subscriber; used to identify&#xa; the process within the Cov client that should receive the notification.
</description>
<tag name="@see">#getProcessIdentifier</tag>
<tag name="@see">#setProcessIdentifier</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipientProcess() -->
<constructor name="BBacnetRecipientProcess" public="true">
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipientProcess(javax.baja.bacnet.datatypes.BBacnetRecipient, javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<constructor name="BBacnetRecipientProcess" public="true">
<parameter name="recipient">
<type class="javax.baja.bacnet.datatypes.BBacnetRecipient"/>
<description/>
</parameter>
<parameter name="processIdentifier">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description/>
</parameter>
<description>
Fully specified constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipientProcess.getRecipient() -->
<method name="getRecipient"  public="true">
<description>
Get the &lt;code&gt;recipient&lt;/code&gt; property.&#xa; the recipient address information.
</description>
<tag name="@see">#recipient</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetRecipient"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipientProcess.setRecipient(javax.baja.bacnet.datatypes.BBacnetRecipient) -->
<method name="setRecipient"  public="true">
<description>
Set the &lt;code&gt;recipient&lt;/code&gt; property.&#xa; the recipient address information.
</description>
<tag name="@see">#recipient</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetRecipient"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipientProcess.getProcessIdentifier() -->
<method name="getProcessIdentifier"  public="true">
<description>
Get the &lt;code&gt;processIdentifier&lt;/code&gt; property.&#xa; a numeric &#x22;handle&#x22; meaningful to the subscriber; used to identify&#xa; the process within the Cov client that should receive the notification.
</description>
<tag name="@see">#processIdentifier</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipientProcess.setProcessIdentifier(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setProcessIdentifier"  public="true">
<description>
Set the &lt;code&gt;processIdentifier&lt;/code&gt; property.&#xa; a numeric &#x22;handle&#x22; meaningful to the subscriber; used to identify&#xa; the process within the Cov client that should receive the notification.
</description>
<tag name="@see">#processIdentifier</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipientProcess.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipientProcess.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipientProcess.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipientProcess.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipientProcess.recipient -->
<field name="recipient"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;recipient&lt;/code&gt; property.&#xa; the recipient address information.
</description>
<tag name="@see">#getRecipient</tag>
<tag name="@see">#setRecipient</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipientProcess.processIdentifier -->
<field name="processIdentifier"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;processIdentifier&lt;/code&gt; property.&#xa; a numeric &#x22;handle&#x22; meaningful to the subscriber; used to identify&#xa; the process within the Cov client that should receive the notification.
</description>
<tag name="@see">#getProcessIdentifier</tag>
<tag name="@see">#setProcessIdentifier</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipientProcess.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipientProcess.RECIPIENT_TAG -->
<field name="RECIPIENT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetRecipientProcess.PROCESS_ID_TAG -->
<field name="PROCESS_ID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
