<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarmOrion" runtimeProfile="rt" qualifiedName="com.tridium.alarmOrion.conversion.BAlarmConversionJob" name="BAlarmConversionJob" packageName="com.tridium.alarmOrion.conversion" public="true" abstract="true">
<description>
This is an abstract class that creates a common interface &#xa; for alarm conversion jobs.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">December 11, 2009</tag>
<extends>
<type class="javax.baja.job.BSimpleJob"/>
</extends>
</class>
</bajadoc>
