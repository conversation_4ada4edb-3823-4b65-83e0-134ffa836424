<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.sys.BAceSpace" name="BAceSpace" packageName="com.tridium.ace.sys" public="true">
<description>
BAceSpace is the component space for a ACE application file.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">13 Feb 17</tag>
<tag name="@since">Niagara 4.4</tag>
<extends>
<type class="javax.baja.space.BComponentSpace"/>
</extends>
<implements>
<type class="com.tridium.ace.sys.BIAceSpace"/>
</implements>
<annotation><type class="javax.baja.space.AuditableSpace"/>
</annotation>
</class>
</bajadoc>
