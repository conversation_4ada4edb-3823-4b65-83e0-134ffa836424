<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.sys.BAceAppFile" name="BAceAppFile" packageName="com.tridium.ace.sys" public="true">
<description>
BAceAppFile represents a JSON file containing a ACE application definition.&#xa; BAceAppFile is a wrapper for an Ace AppFile similar to how BBogFile wraps a .bog file.
</description>
<tag name="@author"><PERSON> on 31 Jan 17</tag>
<tag name="@since">Niagara 4.4</tag>
<extends>
<type class="javax.baja.file.BSubSpaceFile"/>
</extends>
<implements>
<type class="javax.baja.file.BIComponentFile"/>
</implements>
<implements>
<type class="javax.baja.file.types.text.BIJsonFile"/>
</implements>
</class>
</bajadoc>
