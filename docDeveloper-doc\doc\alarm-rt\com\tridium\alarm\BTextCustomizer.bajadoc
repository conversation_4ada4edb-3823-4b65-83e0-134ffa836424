<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.BTextCustomizer" name="BTextCustomizer" packageName="com.tridium.alarm" public="true">
<description>
Basic rules based text customization.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">21 Feb 06</tag>
<tag name="@version">$Revision: 1$ $Date: 2/22/06 2:53:14 PM EST$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="op" flags="">
<type class="com.tridium.alarm.BTextOp"/>
<description>
Slot for the &lt;code&gt;op&lt;/code&gt; property.
</description>
<tag name="@see">#getOp</tag>
<tag name="@see">#setOp</tag>
</property>

<property name="text" flags="">
<type class="javax.baja.util.BFormat"/>
<description>
Slot for the &lt;code&gt;text&lt;/code&gt; property.
</description>
<tag name="@see">#getText</tag>
<tag name="@see">#setText</tag>
</property>

</class>
</bajadoc>
