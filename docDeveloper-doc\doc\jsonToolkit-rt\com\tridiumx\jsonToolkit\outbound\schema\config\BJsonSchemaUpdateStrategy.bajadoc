<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy" name="BJsonSchemaUpdateStrategy" packageName="com.tridiumx.jsonToolkit.outbound.schema.config" public="true" final="true">
<description>
The different stratagem for defining when a json schema should update it&#x27;s output string.&#xa;&#xa; When set to COV the Schema will subscribe to any bound BComponents returned by&#xa; BJsonSchema &lt;code&gt;<see ref="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchema">com.tridiumx.jsonToolkit.outbound.schema.BJsonSchema</see>&lt;/code&gt; or RelativeJsonSchema&#xa; &lt;code&gt;<see ref="com.tridiumx.jsonToolkit.outbound.schema.relative.BRelativeJsonSchema">com.tridiumx.jsonToolkit.outbound.schema.relative.BRelativeJsonSchema</see>&lt;/code&gt;, which means&#xa; any changes in the value of that point will trigger generation.&#xa;&#xa; In On Demand mode, generation will only occur when the generateJson action&#xa; is fired
</description>
<tag name="@author">Nick Dodd</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;cov&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;onDemandOnly&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy.COV -->
<field name="COV"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for cov.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy.ON_DEMAND_ONLY -->
<field name="ON_DEMAND_ONLY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for onDemandOnly.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy.cov -->
<field name="cov"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy"/>
<description>
BJsonSchemaUpdateStrategy constant for cov.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy.onDemandOnly -->
<field name="onDemandOnly"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy"/>
<description>
BJsonSchemaUpdateStrategy constant for onDemandOnly.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
