<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.BStationSessionScheme" name="BStationSessionScheme" packageName="javax.baja.naming" public="true" abstract="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@creation">2 March 2010</tag>
<tag name="@version">$Revision: 1$ $Date: 10/1/10 10:02:17 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.naming.BOrdScheme"/>
</extends>
<!-- javax.baja.naming.BStationSessionScheme(java.lang.String) -->
<constructor name="BStationSessionScheme" protected="true">
<parameter name="schemeId">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.naming.BStationSessionScheme.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.naming.BStationSessionScheme.getDefaultPort() -->
<method name="getDefaultPort"  public="true" abstract="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.naming.BStationSessionScheme.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
