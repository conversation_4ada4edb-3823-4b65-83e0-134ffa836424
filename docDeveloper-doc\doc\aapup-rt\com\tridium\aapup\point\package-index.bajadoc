<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="aapup" runtimeProfile="rt" name="com.tridium.aapup.point">
<description/>
<class packageName="com.tridium.aapup.point" name="BCvOverrideAction"><description>BCvOverrideAction is used to adjust the override value&#xa; of CVs for inputs.</description></class>
<class packageName="com.tridium.aapup.point" name="BCvResetOverrideAction"><description>BCvResetOverrideAction is used to adjust an attribute in a&#xa; PUP controller that has a &#x22;time&#x22; type.</description></class>
<class packageName="com.tridium.aapup.point" name="BPupBooleanProxyExt"><description>BPupBooleanProxyExt is the proxy extension for bringing&#xa; PUP point information into Niagara Boolean Points.</description></class>
<class packageName="com.tridium.aapup.point" name="BPupEnumProxyExt"><description>BPupEnumProxyExt is the proxy extension for bringing&#xa; PUP point information into Niagara Enum Points.</description></class>
<class packageName="com.tridium.aapup.point" name="BPupNumericProxyExt"><description>BPupNumericProxyExt is the proxy extension for bringing&#xa; PUP point information into Niagara Numeric Points.</description></class>
<class packageName="com.tridium.aapup.point" name="BPupPointDeviceExt"><description>aapup implementation of BPointDeviceExt</description></class>
<class packageName="com.tridium.aapup.point" name="BPupPointFolder"><description>aapup implementation of BPointFolder</description></class>
<class packageName="com.tridium.aapup.point" name="BPupProxyExt"><description>BPupProxyExt is the base proxy extension for bringing&#xa; point information from all PUP device types into Niagara.</description></class>
<class packageName="com.tridium.aapup.point" name="BPupStringProxyExt"><description>BPupStringProxyExt is the proxy extension for bringing&#xa; PUP point information into Niagara String Points.</description></class>
<class packageName="com.tridium.aapup.point" name="BSetDateAction"><description>BSetDateAction is used to adjust an attribute in a&#xa; PUP controller that has a &#x22;date&#x22; type (E4 &amp; E3)</description></class>
<class packageName="com.tridium.aapup.point" name="BSetTimeAction"><description>BSetTimeAction is used to adjust an attribute in a&#xa; PUP controller that has a &#x22;time&#x22; type.</description></class>
</package>
</bajadoc>
