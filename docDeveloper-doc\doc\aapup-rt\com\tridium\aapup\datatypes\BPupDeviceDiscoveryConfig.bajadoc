<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.datatypes.BPupDeviceDiscoveryConfig" name="BPupDeviceDiscoveryConfig" packageName="com.tridium.aapup.datatypes" public="true">
<description>
Pup Device Discovery Config, used to narrow a range of&#xa; device address to search for devices
</description>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@creation">7/21/2005 1:39PM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.91</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="startAddress" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;startAddress&lt;/code&gt; property.&#xa; the first(lowest) unit number to detect.  The device discovery will&#xa; proceed starting at startAddress, and will increment the address by&#xa; one for each sucessive potential device until the stopAddress is reached.
</description>
<tag name="@see">#getStartAddress</tag>
<tag name="@see">#setStartAddress</tag>
</property>

<property name="stopAddress" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;stopAddress&lt;/code&gt; property.&#xa; the last(highest) unit number to detect.  The device discovery will&#xa; proceed starting at startAddress, and will increment the address by&#xa; one for each sucessive potential device until the stopAddress is reached.
</description>
<tag name="@see">#getStopAddress</tag>
<tag name="@see">#setStopAddress</tag>
</property>

<property name="searchBy" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;searchBy&lt;/code&gt; property.&#xa; if the user selects to search by serial number instead of by address,&#xa; this flag should be set
</description>
<tag name="@see">#getSearchBy</tag>
<tag name="@see">#setSearchBy</tag>
</property>

<property name="controllerList" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;controllerList&lt;/code&gt; property.&#xa; an xml file which contains the mapping of cm + ct to a channel list
</description>
<tag name="@see">#getControllerList</tag>
<tag name="@see">#setControllerList</tag>
</property>

</class>
</bajadoc>
