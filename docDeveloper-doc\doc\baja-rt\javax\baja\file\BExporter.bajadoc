<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BExporter" name="BExporter" packageName="javax.baja.file" public="true" abstract="true">
<description>
BExporter is a component designed to export a BObject to&#xa; a file stream.  Exporters are registered as agents on&#xa; the source BObject type they export.  By convention you&#xa; should provide a TypeInfo icon in your lexicon using the&#xa; key &#x22;typename.icon&#x22;.  Note that if you wish to register an &#xa; exporter on a WbView then your subclass also needs to implement &#xa; workbench:BIWbViewExporter.
</description>
<tag name="@author"><PERSON> on 22 May 04</tag>
<tag name="@version">$Revision: 4$ $Date: 7/1/04 7:40:08 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.agent.BIAgent"/>
</implements>
<implements>
<parameterizedType class="javax.baja.transform.BITransformer">
<args>
<type class="javax.baja.naming.OrdTarget"/>
<type class="javax.baja.sys.Context"/>
</args>
</parameterizedType>
</implements>
<!-- javax.baja.file.BExporter() -->
<constructor name="BExporter" public="true">
<description/>
</constructor>

<!-- javax.baja.file.BExporter.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.file.BExporter.getFileType() -->
<method name="getFileType"  public="true" abstract="true">
<description>
Get the output file type this exporter generates.
</description>
<return>
<type class="javax.baja.registry.TypeInfo"/>
</return>
</method>

<!-- javax.baja.file.BExporter.getFileExtension() -->
<method name="getFileExtension"  public="true" abstract="true">
<description>
Get the default extension to use for the output file.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.BExporter.getFileMimeType() -->
<method name="getFileMimeType"  public="true" final="true">
<description>
Get the mime type of &lt;code&gt;getFileType&lt;/code&gt;.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.BExporter.export(javax.baja.file.ExportOp) -->
<method name="export"  public="true" abstract="true">
<description>
Export using the specified operation.  This is &#xa; the method subclasses must implement.&#xa; &lt;p&gt;&#xa; Note that the &lt;code&gt;OutputStream&lt;/code&gt; obtained from the &lt;code&gt;ExportOp&lt;/code&gt; may&#xa; be &lt;code&gt;flush()&lt;/code&gt;ed after writing, but must not be closed from within the&#xa; &lt;code&gt;export()&lt;/code&gt; call.
</description>
<parameter name="op">
<type class="javax.baja.file.ExportOp"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.file.BExporter.getIcon(javax.baja.sys.Context) -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.file.BExporter.getMimeType() -->
<method name="getMimeType"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.BExporter.transform(javax.baja.naming.OrdTarget, javax.baja.sys.Context) -->
<method name="transform"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Delegate to the &lt;code&gt;export()&lt;/code&gt; method, and return a stream of the&#xa; exported data.
</description>
<parameter name="target">
<type class="javax.baja.naming.OrdTarget"/>
<description>
the ORD target to be transformed
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.io.InputStream"/>
<description>
a stream that will read the transformed data
</description>
</return>
<throws>
<type class="java.io.IOException"/>
<description>
if the transform could not be initiated. If the call to&#xa; &lt;code&gt;export()&lt;/code&gt; throws, the exception will not be thrown immediately by&#xa; &lt;code&gt;transform()&lt;/code&gt;, but will be thrown on the next call to &lt;code&gt;read()&lt;/code&gt;&#xa; on the returned InputStream.
</description>
</throws>
</method>

<!-- javax.baja.file.BExporter.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
