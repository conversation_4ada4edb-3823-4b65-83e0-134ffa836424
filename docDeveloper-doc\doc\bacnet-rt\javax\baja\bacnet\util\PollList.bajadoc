<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.util.PollList" name="PollList" packageName="javax.baja.bacnet.util" public="true">
<description>
PollList.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">11 Oct 2006</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- javax.baja.bacnet.util.PollList(javax.baja.bacnet.util.PollListEntry) -->
<constructor name="PollList" public="true">
<parameter name="ple">
<type class="javax.baja.bacnet.util.PollListEntry"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.util.PollList.add(javax.baja.bacnet.util.PollListEntry) -->
<method name="add"  public="true" synchronized="true">
<description/>
<parameter name="ple">
<type class="javax.baja.bacnet.util.PollListEntry"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.remove(javax.baja.bacnet.util.PollListEntry) -->
<method name="remove"  public="true" synchronized="true">
<description/>
<parameter name="ple">
<type class="javax.baja.bacnet.util.PollListEntry"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.size() -->
<method name="size"  public="true" synchronized="true">
<description/>
<return>
<type class="int"/>
<description>
the number of entries in this list.&#xa; Is this still needed?
</description>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.getAddressHash() -->
<method name="getAddressHash"  public="true" final="true">
<description/>
<return>
<type class="int"/>
<description>
the address
</description>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.getDataSize() -->
<method name="getDataSize"  public="true" final="true">
<description/>
<return>
<type class="int"/>
<description>
the poll list&#x27;s current expected data size
</description>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.getDevice() -->
<method name="getDevice"  public="true" final="true">
<description/>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
<description>
the device
</description>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.getPollEntries() -->
<method name="getPollEntries"  public="true" synchronized="true">
<description>
Get the entries in this list as an array.
</description>
<return>
<type class="javax.baja.bacnet.util.PollListEntry" dimension="1"/>
<description>
array of pollListEntry
</description>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.contains(javax.baja.bacnet.util.PollListEntry) -->
<method name="contains"  public="true" final="true" synchronized="true">
<description/>
<parameter name="ple">
<type class="javax.baja.bacnet.util.PollListEntry"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.toString() -->
<method name="toString"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.debug() -->
<method name="debug"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.getPollFrequency() -->
<method name="getPollFrequency"  public="true" synchronized="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.setIsPolling(boolean) -->
<method name="setIsPolling"  public="true">
<description/>
<parameter name="polling">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.isPolling() -->
<method name="isPolling"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.setDone(boolean) -->
<method name="setDone"  public="true">
<description/>
<parameter name="done">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.isDone() -->
<method name="isDone"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.getSleep() -->
<method name="getSleep"  public="true">
<description/>
<return>
<type class="long"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.setSleep(long) -->
<method name="setSleep"  public="true">
<description/>
<parameter name="sleep">
<type class="long"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.getFailedCount() -->
<method name="getFailedCount"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.incrementFailedCount() -->
<method name="incrementFailedCount"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.resetFailedCount() -->
<method name="resetFailedCount"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PollList.pollCount -->
<field name="pollCount"  public="true" final="true">
<type class="java.util.concurrent.atomic.AtomicInteger"/>
<description/>
</field>

</class>
</bajadoc>
