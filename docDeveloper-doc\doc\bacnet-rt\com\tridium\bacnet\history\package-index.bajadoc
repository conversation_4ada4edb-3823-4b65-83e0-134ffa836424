<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="com.tridium.bacnet.history">
<description/>
<class packageName="com.tridium.bacnet.history" name="BAbstractBacnetHistory"><description>BAbstractBacnetHistory defines an archive action for transferring&#xa; one trend log from a remote Bacnet source to the local&#xa; destination.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetActivePeriod"><description>BBacnetActivePeriod defines when trend log&#xa; collection is active based on absolute date/time range.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetBitStringTrendLogExt"><description>&lt;code&gt; BBacnetBitStringTrendLogExt &lt;/code&gt; represents the extension for trending bacnet bit string</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetBitStringTrendLogRemoteExt"/>
<class packageName="com.tridium.bacnet.history" name="BBacnetBitStringTrendRecord"><description>&lt;code&gt;BBacnetBitStringTrendRecord&lt;/code&gt; is a Bacnet trend record&#xa; with BITSTRING value type.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetBooleanCovTrendLogExt"><description>An extension for collecting a Bacnet trend log of a boolean control value.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetBooleanIntervalTrendLogExt"><description>An extension for collecting a Bacnet trend log of a boolean control value.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetBooleanTrendLogExt"><description>Created by Sandipan Aich on 6/14/2017.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetBooleanTrendLogRemoteExt"/>
<class packageName="com.tridium.bacnet.history" name="BBacnetBooleanTrendRecord"><description>&lt;code&gt;BBacnetBooleanTrendRecord&lt;/code&gt; is a Bacnet trend record&#xa; with a boolean value.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetEnumCovTrendLogExt"><description>An extension for collecting a Bacnet trend log of a multistate control value.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetEnumIntervalTrendLogExt"><description>An extension for collecting a Bacnet trend log of a multistate control value.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetEnumTrendLogExt"><description>Generic trend log extension for Enum which takes care of both&#xa; change of value and interval based logging.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetEnumTrendLogRemoteExt"/>
<class packageName="com.tridium.bacnet.history" name="BBacnetEnumTrendRecord"><description>&lt;code&gt;BBacnetEnumTrendRecord&lt;/code&gt; is a Bacnet trend record&#xa; with an enumerated value.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetHistoryDeviceExt"><description>BHistoryDeviceExt is the base class for mapping historical&#xa; data in a device to Baja history databases.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetHistoryImport"><description>BBacnetHistoryImport defines an archive action for transferring&#xa; one trend log from a remote Bacnet source to the local&#xa; destination.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetNumericCovTrendLogExt"><description>An extension for collecting a Bacnet trend log of a float control value.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetNumericIntervalTrendLogExt"><description>An extension for collecting a Bacnet trend log of a float control value.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetNumericTrendLogExt"><description>Created by Sandipan Aich on 5/23/2017.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetNumericTrendLogRemoteExt"/>
<class packageName="com.tridium.bacnet.history" name="BBacnetNumericTrendRecord"><description>&lt;code&gt;BBacnetNumericTrendRecord&lt;/code&gt; is a Bacnet trend record&#xa; with a float value.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetStringCovTrendLogExt"><description>An extension for collecting a Bacnet trend log of a String control value.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetStringIntervalTrendLogExt"><description>An extension for collecting a Bacnet trend log of a String control value.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetStringTrendLogExt"><description>Created by Sandipan Aich on 6/6/2017.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetStringTrendLogRemoteExt"/>
<class packageName="com.tridium.bacnet.history" name="BBacnetStringTrendRecord"><description>&lt;code&gt;BBacnetStringTrendRecord&lt;/code&gt; is a Bacnet trend record with&#xa; a string value.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetTrendLogAlarmSourceExt"><description>BBacnetTrendLogAlarmSourceExt defines the intrinsic alarming/notification&#xa; for a server-side trend log exposed to Bacnet.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetTrendLogMultipleImport"><description>BBacnetTrendLogMultipleImport defines an archive action for transferring&#xa; one trend log from a remote Bacnet source to the local&#xa; destination.</description></class>
<class packageName="com.tridium.bacnet.history" name="BBacnetTrendLogRemoteExt"/>
<class packageName="com.tridium.bacnet.history" name="BBacnetTrendRecord"><description>BBacnetTrendRecord is a Bacnet history record that includes special&#xa; semantics for histories that track a single data point&#xa; like the histories generated by a history extension&#xa; on a control point.</description></class>
<class packageName="com.tridium.bacnet.history" name="BIBacnetTrendLogExt" category="interface"><description>BIBacnetTrendLogExt is the interface which all BACnet Trend Log extensions&#xa; must implement.</description></class>
</package>
</bajadoc>
