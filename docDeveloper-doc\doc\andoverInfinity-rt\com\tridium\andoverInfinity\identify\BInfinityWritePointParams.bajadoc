<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.identify.BInfinityWritePointParams" name="BInfinityWritePointParams" packageName="com.tridium.andoverInfinity.identify" public="true">
<description>
BInfinityWritePointParams
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 22, 2007</tag>
<tag name="@version">$Revision$ $May 22, 2007 9:57:38 AM$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.identify.BDdfIdParams"/>
</extends>
<implements>
<type class="com.tridium.ddf.identify.BIDdfWriteParams"/>
</implements>
<implements>
<type class="com.tridium.ddf.identify.BIDdfAutoParams"/>
</implements>
<property name="sendDisableCommand" flags="">
<type class="com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum"/>
<description>
Slot for the &lt;code&gt;sendDisableCommand&lt;/code&gt; property.&#xa; send a dis command to the point before writing
</description>
<tag name="@see">#getSendDisableCommand</tag>
<tag name="@see">#setSendDisableCommand</tag>
</property>

<!-- com.tridium.andoverInfinity.identify.BInfinityWritePointParams() -->
<constructor name="BInfinityWritePointParams" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.identify.BInfinityWritePointParams.getSendDisableCommand() -->
<method name="getSendDisableCommand"  public="true">
<description>
Get the &lt;code&gt;sendDisableCommand&lt;/code&gt; property.&#xa; send a dis command to the point before writing
</description>
<tag name="@see">#sendDisableCommand</tag>
<return>
<type class="com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityWritePointParams.setSendDisableCommand(com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum) -->
<method name="setSendDisableCommand"  public="true">
<description>
Set the &lt;code&gt;sendDisableCommand&lt;/code&gt; property.&#xa; send a dis command to the point before writing
</description>
<tag name="@see">#sendDisableCommand</tag>
<parameter name="v">
<type class="com.tridium.andoverInfinity.enums.BInfinitySendDisableEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityWritePointParams.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityWritePointParams.needToSendDisableCommand() -->
<method name="needToSendDisableCommand"  public="true">
<description>
Determine if disable command needs to be sent to the controller&#xa;  for this point.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityWritePointParams.getWriteRequestType() -->
<method name="getWriteRequestType"  public="true">
<description>
Infinity uses a &lt;code&gt;InfinityWritePointRequest.TYPE&lt;/code&gt;
</description>
<tag name="@see">com.tridium.devDriver.identify.BIDevWriteParams#getWriteRequestType()</tag>
<return>
<type class="javax.baja.sys.Type"/>
<description>
&lt;code&gt;InfinityWritePointRequest.TYPE&lt;/code&gt;
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityWritePointParams.getAutoRequestType() -->
<method name="getAutoRequestType"  public="true">
<description>
Infinity uses a &lt;code&gt;BInfinityPointAutoRequest.TYPE&lt;/code&gt; for auto requests
</description>
<tag name="@see">com.tridium.devDriver.identify.BIDevAutoParams#getAutoRequestType()</tag>
<return>
<type class="javax.baja.sys.Type"/>
<description>
&lt;code&gt;BInfinityPointAutoRequest.TYPE&lt;/code&gt;
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityWritePointParams.sendDisableCommand -->
<field name="sendDisableCommand"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;sendDisableCommand&lt;/code&gt; property.&#xa; send a dis command to the point before writing
</description>
<tag name="@see">#getSendDisableCommand</tag>
<tag name="@see">#setSendDisableCommand</tag>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityWritePointParams.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityWritePointParams.disableCommandHasBeenSent -->
<field name="disableCommandHasBeenSent"  public="true">
<type class="boolean"/>
<description/>
</field>

</class>
</bajadoc>
