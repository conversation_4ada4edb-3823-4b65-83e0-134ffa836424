<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.alarm.BBacnetEventProcessor" name="BBacnetEventProcessor" packageName="javax.baja.bacnet.alarm" public="true">
<description/>
<tag name="@author">cgemmill</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="processId" flags="">
<type class="long"/>
<description>
Slot for the &lt;code&gt;processId&lt;/code&gt; property.
</description>
<tag name="@see">#getProcessId</tag>
<tag name="@see">#setProcessId</tag>
</property>

<!-- javax.baja.bacnet.alarm.BBacnetEventProcessor() -->
<constructor name="BBacnetEventProcessor" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.alarm.BBacnetEventProcessor.getProcessId() -->
<method name="getProcessId"  public="true">
<description>
Get the &lt;code&gt;processId&lt;/code&gt; property.
</description>
<tag name="@see">#processId</tag>
<return>
<type class="long"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetEventProcessor.setProcessId(long) -->
<method name="setProcessId"  public="true">
<description>
Set the &lt;code&gt;processId&lt;/code&gt; property.
</description>
<tag name="@see">#processId</tag>
<parameter name="v">
<type class="long"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetEventProcessor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetEventProcessor.routeAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="routeAlarm"  public="true">
<description>
Route the alarm.&#xa; This is the override point for event processors.&#xa; Here a subclass may either process the alarm itself, or route it&#xa; to another class for processing.  Note that the Niagara alarm system&#xa; will already have processed the alarm IF its processId matches the&#xa; niagaraProcessId in the AlarmDeviceExt.
</description>
<parameter name="record">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.alarm.BBacnetEventProcessor.processId -->
<field name="processId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;processId&lt;/code&gt; property.
</description>
<tag name="@see">#getProcessId</tag>
<tag name="@see">#setProcessId</tag>
</field>

<!-- javax.baja.bacnet.alarm.BBacnetEventProcessor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
