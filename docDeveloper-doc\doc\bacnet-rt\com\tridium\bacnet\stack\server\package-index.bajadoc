<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="com.tridium.bacnet.stack.server">
<description/>
<class packageName="com.tridium.bacnet.stack.server" name="BBacnetExportFolder"><description>BBacnetExportFolder is the standard container to use&#xa; under BBacnetExportTable to organize BIBacnetExportObjects.</description></class>
<class packageName="com.tridium.bacnet.stack.server" name="BBacnetExportTable"><description>BBacnetExportTable manages the objects exported to Bacnet.</description></class>
<class packageName="com.tridium.bacnet.stack.server" name="BBacnetServerLayer"><description>Tridium Server Application Layer Implementation.</description></class>
<class packageName="com.tridium.bacnet.stack.server" name="BEventHandler"><description>The BEventHandler handles event notification messages,&#xa; and the acknowledgment of those messages, including&#xa; ConfirmedEventNotification, UnconfirmedEventNotification,&#xa; and AcknowledgeAlarm.</description></class>
<class packageName="com.tridium.bacnet.stack.server" name="BHashedEventBuffer"><description>BHashedEventBuffer.</description></class>
<class packageName="com.tridium.bacnet.stack.server" name="BOverrideMode"><description>BOverrideMode determines when the BACnet overridden flag.</description></class>
<class packageName="com.tridium.bacnet.stack.server" name="BIBacnetExportFolder" category="interface"><description>BIBacnetExportFolder is the common interface for&#xa; BLocalBacnetDevice and BBacnetExportFolder.</description></class>
</package>
</bajadoc>
