<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.datatypes.BChangeDeviceIdConfig" name="BChangeDeviceIdConfig" packageName="com.tridium.bacnet.datatypes" public="true">
<description/>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="value" flags="hr">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#getValue</tag>
<tag name="@see">#setValue</tag>
</property>

</class>
</bajadoc>
