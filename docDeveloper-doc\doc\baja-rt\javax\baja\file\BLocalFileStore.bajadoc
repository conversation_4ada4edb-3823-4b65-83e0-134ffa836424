<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BLocalFileStore" name="BLocalFileStore" packageName="javax.baja.file" public="true">
<description>
BLocalStore is a BIFileStore implementation for&#xa; local files using java.io.File.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">24 Jan 03</tag>
<tag name="@version">$Revision: 10$ $Date: 8/14/09 10:38:00 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.file.BAbstractFileStore"/>
</extends>
<!-- javax.baja.file.BLocalFileStore(javax.baja.file.BFileSpace, javax.baja.file.FilePath, java.io.File) -->
<constructor name="BLocalFileStore" public="true">
<parameter name="space">
<type class="javax.baja.file.BFileSpace"/>
</parameter>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="file">
<type class="java.io.File"/>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.file.BLocalFileStore.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.file.BLocalFileStore.getLocalFile() -->
<method name="getLocalFile"  public="true">
<description>
Get the java.io.File this instance wraps.
</description>
<return>
<type class="java.io.File"/>
</return>
</method>

<!-- javax.baja.file.BLocalFileStore.isDirectory() -->
<method name="isDirectory"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;file.isDirectory()&lt;/code&gt;.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BLocalFileStore.isReadonly() -->
<method name="isReadonly"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;!file.canWrite()&lt;/code&gt;.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BLocalFileStore.getSize() -->
<method name="getSize"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return -1 if a directory, otherwise file.length.
</description>
<return>
<type class="long"/>
</return>
</method>

<!-- javax.baja.file.BLocalFileStore.getLastModified() -->
<method name="getLastModified"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return BAbsTime for File.lastModified().
</description>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.file.BLocalFileStore.doSetLastModified(javax.baja.sys.BAbsTime) -->
<method name="doSetLastModified"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set File lastModified and return success
</description>
<tag name="@since">Niagara 3.5</tag>
<parameter name="absTime">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="boolean"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BLocalFileStore.getInputStream() -->
<method name="getInputStream"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;new FileInputStream(file)&lt;/code&gt;.
</description>
<return>
<type class="java.io.InputStream"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BLocalFileStore.getOutputStream() -->
<method name="getOutputStream"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;new FileOutputStream(file)&lt;/code&gt;.
</description>
<return>
<type class="java.io.OutputStream"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BLocalFileStore.delete() -->
<method name="delete"  public="true">
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BLocalFileStore.getCrc() -->
<method name="getCrc"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return calculated CRC
</description>
<tag name="@since">Niagara 3.5</tag>
<return>
<type class="long"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BLocalFileStore.hashCode() -->
<method name="hashCode"  public="true">
<description>
Return &lt;code&gt;file.hashCode()&lt;/code&gt;.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.file.BLocalFileStore.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description>
Return true if object is a BLocalFileStore and&#xa; java.io.Files are equal.
</description>
<parameter name="object">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BLocalFileStore.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
