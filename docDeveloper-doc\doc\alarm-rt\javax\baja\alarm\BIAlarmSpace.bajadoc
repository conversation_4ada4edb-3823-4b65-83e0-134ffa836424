<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BIAlarmSpace" name="BIAlarmSpace" packageName="javax.baja.alarm" public="true" interface="true" abstract="true" category="interface">
<description>
Common interface for all AlarmSpace implementations.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">11 June 2014</tag>
<tag name="@since">Niagara 4.0</tag>
<implements>
<type class="javax.baja.space.BISpace"/>
</implements>
<!-- javax.baja.alarm.BIAlarmSpace.getConnection(javax.baja.sys.Context) -->
<method name="getConnection"  public="true" abstract="true">
<description>
Open a connection to the alarm space.  All interactions with an AlarmDb&#xa; are managed through a AlarmDbConnection.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
<description>
context of the connection
</description>
</parameter>
<return>
<type class="javax.baja.alarm.AlarmSpaceConnection"/>
<description>
Returns a connection to the alarm database.
</description>
</return>
</method>

<!-- javax.baja.alarm.BIAlarmSpace.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
