<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.category.BCategory" name="BCategory" packageName="javax.baja.category" public="true">
<description>
BCategory models a category.  ICategorizable objects are mapped into &#xa; categories using BCategoryMask.  Then the BCategoryService maps those&#xa; bits into first class BCategory components.
</description>
<tag name="@author"><PERSON> Frank</tag>
<tag name="@creation">12 Feb 05</tag>
<tag name="@version">$Revision: 5$ $Date: 3/12/08 5:40:46 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.category.BAbstractCategory"/>
</extends>
<property name="index" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;index&lt;/code&gt; property.&#xa; Provides status for the category. If the disabled flag is set&#xa; the category is not yet assigned an index. The fault flag&#xa; is set if the category has an invalid index.
</description>
<tag name="@see">#getIndex</tag>
<tag name="@see">#setIndex</tag>
</property>

<property name="mode" flags="">
<type class="javax.baja.category.BCategoryMode"/>
<description>
Slot for the &lt;code&gt;mode&lt;/code&gt; property.&#xa; The category mode is used to determine a user&#x27;s permissions&#xa; for objects in the category.  The default is &#x22;union&#x22;.
</description>
<tag name="@see">#getMode</tag>
<tag name="@see">#setMode</tag>
</property>

<!-- javax.baja.category.BCategory() -->
<constructor name="BCategory" public="true">
<description/>
</constructor>

<!-- javax.baja.category.BCategory.getIndex() -->
<method name="getIndex"  public="true">
<description>
Get the &lt;code&gt;index&lt;/code&gt; property.&#xa; Provides status for the category. If the disabled flag is set&#xa; the category is not yet assigned an index. The fault flag&#xa; is set if the category has an invalid index.
</description>
<tag name="@see">#index</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.category.BCategory.setIndex(int) -->
<method name="setIndex"  public="true">
<description>
Set the &lt;code&gt;index&lt;/code&gt; property.&#xa; Provides status for the category. If the disabled flag is set&#xa; the category is not yet assigned an index. The fault flag&#xa; is set if the category has an invalid index.
</description>
<tag name="@see">#index</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.category.BCategory.getMode() -->
<method name="getMode"  public="true">
<description>
Get the &lt;code&gt;mode&lt;/code&gt; property.&#xa; The category mode is used to determine a user&#x27;s permissions&#xa; for objects in the category.  The default is &#x22;union&#x22;.
</description>
<tag name="@see">#mode</tag>
<return>
<type class="javax.baja.category.BCategoryMode"/>
</return>
</method>

<!-- javax.baja.category.BCategory.setMode(javax.baja.category.BCategoryMode) -->
<method name="setMode"  public="true">
<description>
Set the &lt;code&gt;mode&lt;/code&gt; property.&#xa; The category mode is used to determine a user&#x27;s permissions&#xa; for objects in the category.  The default is &#x22;union&#x22;.
</description>
<tag name="@see">#mode</tag>
<parameter name="v">
<type class="javax.baja.category.BCategoryMode"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.category.BCategory.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.category.BCategory.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="prop">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.category.BCategory.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
To string.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.category.BCategory.index -->
<field name="index"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;index&lt;/code&gt; property.&#xa; Provides status for the category. If the disabled flag is set&#xa; the category is not yet assigned an index. The fault flag&#xa; is set if the category has an invalid index.
</description>
<tag name="@see">#getIndex</tag>
<tag name="@see">#setIndex</tag>
</field>

<!-- javax.baja.category.BCategory.mode -->
<field name="mode"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;mode&lt;/code&gt; property.&#xa; The category mode is used to determine a user&#x27;s permissions&#xa; for objects in the category.  The default is &#x22;union&#x22;.
</description>
<tag name="@see">#getMode</tag>
<tag name="@see">#setMode</tag>
</field>

<!-- javax.baja.category.BCategory.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
