<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor" name="BBacnetEventEnrollmentDescriptor" packageName="javax.baja.bacnet.export" public="true">
<description>
BBacnetEventEnrollmentDescriptor exposes a Niagara event to Bacnet.
</description>
<tag name="@author"><PERSON><PERSON><PERSON> on 5/4/2017</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.export.BBacnetEventSource"/>
</extends>
<implements>
<type class="javax.baja.bacnet.export.BacnetPropertyListProvider"/>
</implements>
<property name="eventEnrollmentOrd" flags="d">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;eventEnrollmentOrd&lt;/code&gt; property.
</description>
<tag name="@see">#getEventEnrollmentOrd</tag>
<tag name="@see">#setEventEnrollmentOrd</tag>
</property>

<property name="objectId" flags="d">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="objectName" flags="d">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;objectName&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectName</tag>
<tag name="@see">#setObjectName</tag>
</property>

<property name="description" flags="d">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#getDescription</tag>
<tag name="@see">#setDescription</tag>
</property>

<property name="typeOfEvent" flags="dr">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
<description>
Slot for the &lt;code&gt;typeOfEvent&lt;/code&gt; property.
</description>
<tag name="@see">#getTypeOfEvent</tag>
<tag name="@see">#setTypeOfEvent</tag>
</property>

<property name="notifyTypeId" flags="dhr">
<type class="javax.baja.alarm.ext.BNotifyType"/>
<description>
Slot for the &lt;code&gt;notifyTypeId&lt;/code&gt; property.
</description>
<tag name="@see">#getNotifyTypeId</tag>
<tag name="@see">#setNotifyTypeId</tag>
</property>

<property name="objectPropertyReference" flags="drh">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
<description>
Slot for the &lt;code&gt;objectPropertyReference&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectPropertyReference</tag>
<tag name="@see">#setObjectPropertyReference</tag>
</property>

<property name="notificationClassId" flags="d">
<type class="int"/>
<description>
Slot for the &lt;code&gt;notificationClassId&lt;/code&gt; property.
</description>
<tag name="@see">#getNotificationClassId</tag>
<tag name="@see">#setNotificationClassId</tag>
</property>

<property name="reliability" flags="dr">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description>
Slot for the &lt;code&gt;reliability&lt;/code&gt; property.&#xa; Reliability of the Event Enrollment object to perform its monitoring function as described in&#xa; 135-2012 12.12.21. Does not reflect the reliability of the monitored object or the result of the&#xa; fault algorithm, if one is in use. Those other items are reflected in the BACnet Reliability&#xa; property as read through a BACnet request.
</description>
<tag name="@see">#getReliability</tag>
<tag name="@see">#setReliability</tag>
</property>

<property name="eventParameter" flags="rh">
<type class="javax.baja.bacnet.datatypes.BBacnetEventParameter"/>
<description>
Slot for the &lt;code&gt;eventParameter&lt;/code&gt; property.
</description>
<tag name="@see">#getEventParameter</tag>
<tag name="@see">#setEventParameter</tag>
</property>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor() -->
<constructor name="BBacnetEventEnrollmentDescriptor" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getEventEnrollmentOrd() -->
<method name="getEventEnrollmentOrd"  public="true">
<description>
Get the &lt;code&gt;eventEnrollmentOrd&lt;/code&gt; property.
</description>
<tag name="@see">#eventEnrollmentOrd</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.setEventEnrollmentOrd(javax.baja.naming.BOrd) -->
<method name="setEventEnrollmentOrd"  public="true">
<description>
Set the &lt;code&gt;eventEnrollmentOrd&lt;/code&gt; property.
</description>
<tag name="@see">#eventEnrollmentOrd</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getObjectId() -->
<method name="getObjectId"  public="true">
<description>
Get the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#objectId</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.setObjectId(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="setObjectId"  public="true">
<description>
Set the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#objectId</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getObjectName() -->
<method name="getObjectName"  public="true">
<description>
Get the &lt;code&gt;objectName&lt;/code&gt; property.
</description>
<tag name="@see">#objectName</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.setObjectName(java.lang.String) -->
<method name="setObjectName"  public="true">
<description>
Set the &lt;code&gt;objectName&lt;/code&gt; property.
</description>
<tag name="@see">#objectName</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getDescription() -->
<method name="getDescription"  public="true">
<description>
Get the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#description</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.setDescription(java.lang.String) -->
<method name="setDescription"  public="true">
<description>
Set the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#description</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getTypeOfEvent() -->
<method name="getTypeOfEvent"  public="true">
<description>
Get the &lt;code&gt;typeOfEvent&lt;/code&gt; property.
</description>
<tag name="@see">#typeOfEvent</tag>
<return>
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.setTypeOfEvent(javax.baja.bacnet.enums.BBacnetEventType) -->
<method name="setTypeOfEvent"  public="true">
<description>
Set the &lt;code&gt;typeOfEvent&lt;/code&gt; property.
</description>
<tag name="@see">#typeOfEvent</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.BBacnetEventType"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getNotifyTypeId() -->
<method name="getNotifyTypeId"  public="true">
<description>
Get the &lt;code&gt;notifyTypeId&lt;/code&gt; property.
</description>
<tag name="@see">#notifyTypeId</tag>
<return>
<type class="javax.baja.alarm.ext.BNotifyType"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.setNotifyTypeId(javax.baja.alarm.ext.BNotifyType) -->
<method name="setNotifyTypeId"  public="true">
<description>
Set the &lt;code&gt;notifyTypeId&lt;/code&gt; property.
</description>
<tag name="@see">#notifyTypeId</tag>
<parameter name="v">
<type class="javax.baja.alarm.ext.BNotifyType"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getObjectPropertyReference() -->
<method name="getObjectPropertyReference"  public="true">
<description>
Get the &lt;code&gt;objectPropertyReference&lt;/code&gt; property.
</description>
<tag name="@see">#objectPropertyReference</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.setObjectPropertyReference(javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference) -->
<method name="setObjectPropertyReference"  public="true">
<description>
Set the &lt;code&gt;objectPropertyReference&lt;/code&gt; property.
</description>
<tag name="@see">#objectPropertyReference</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getNotificationClassId() -->
<method name="getNotificationClassId"  public="true">
<description>
Get the &lt;code&gt;notificationClassId&lt;/code&gt; property.
</description>
<tag name="@see">#notificationClassId</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.setNotificationClassId(int) -->
<method name="setNotificationClassId"  public="true">
<description>
Set the &lt;code&gt;notificationClassId&lt;/code&gt; property.
</description>
<tag name="@see">#notificationClassId</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getReliability() -->
<method name="getReliability"  public="true">
<description>
Get the &lt;code&gt;reliability&lt;/code&gt; property.&#xa; Reliability of the Event Enrollment object to perform its monitoring function as described in&#xa; 135-2012 12.12.21. Does not reflect the reliability of the monitored object or the result of the&#xa; fault algorithm, if one is in use. Those other items are reflected in the BACnet Reliability&#xa; property as read through a BACnet request.
</description>
<tag name="@see">#reliability</tag>
<return>
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.setReliability(javax.baja.bacnet.enums.BBacnetReliability) -->
<method name="setReliability"  public="true">
<description>
Set the &lt;code&gt;reliability&lt;/code&gt; property.&#xa; Reliability of the Event Enrollment object to perform its monitoring function as described in&#xa; 135-2012 12.12.21. Does not reflect the reliability of the monitored object or the result of the&#xa; fault algorithm, if one is in use. Those other items are reflected in the BACnet Reliability&#xa; property as read through a BACnet request.
</description>
<tag name="@see">#reliability</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getEventParameter() -->
<method name="getEventParameter"  public="true">
<description>
Get the &lt;code&gt;eventParameter&lt;/code&gt; property.
</description>
<tag name="@see">#eventParameter</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetEventParameter"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.setEventParameter(javax.baja.bacnet.datatypes.BBacnetEventParameter) -->
<method name="setEventParameter"  public="true">
<description>
Set the &lt;code&gt;eventParameter&lt;/code&gt; property.
</description>
<tag name="@see">#eventParameter</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetEventParameter"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.started() -->
<method name="started"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.stationStarted() -->
<method name="stationStarted"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.stopped() -->
<method name="stopped"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getObject() -->
<method name="getObject"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the &lt;code&gt;<see ref="javax.baja.control.BPointExtension">BPointExtension</see>&lt;/code&gt; that &lt;code&gt;<see ref="javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor#eventEnrollmentOrd">#eventEnrollmentOrd</see>&lt;/code&gt; points to. The extension&#x27;s&#xa; parent should be the same as pointed to by &lt;code&gt;<see ref="javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor#objectPropertyReference">#objectPropertyReference</see>&lt;/code&gt;. Return null if the&#xa; eventEnrollmentOrd cannot be resolved to a BPointExtension, the extension has no parent, or the&#xa; extension&#x27;s parent is not consistent with the objectPropertyReference.
</description>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getObjectOrd() -->
<method name="getObjectOrd"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get &lt;code&gt;<see ref="javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor#eventEnrollmentOrd">#eventEnrollmentOrd</see>&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.setObjectOrd(javax.baja.naming.BOrd, javax.baja.sys.Context) -->
<method name="setObjectOrd"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set &lt;code&gt;<see ref="javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor#eventEnrollmentOrd">#eventEnrollmentOrd</see>&lt;/code&gt;.
</description>
<parameter name="objectOrd">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.checkConfiguration() -->
<method name="checkConfiguration"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getPropertyList() -->
<method name="getPropertyList"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getOptionalProps() -->
<method name="getOptionalProps"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;AssignmentOrReturnOfFieldWithMutableType&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<return>
<type class="int" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getRequiredProps() -->
<method name="getRequiredProps"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;AssignmentOrReturnOfFieldWithMutableType&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<return>
<type class="int" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.readProperty(javax.baja.bacnet.io.PropertyReference) -->
<method name="readProperty"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="propertyReference">
<type class="javax.baja.bacnet.io.PropertyReference"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.RejectException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.readPropertyMultiple(javax.baja.bacnet.io.PropertyReference[]) -->
<method name="readPropertyMultiple"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="propertyReferences">
<type class="javax.baja.bacnet.io.PropertyReference" dimension="1"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue" dimension="1"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.RejectException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.readRange(javax.baja.bacnet.io.RangeReference) -->
<method name="readRange"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="rangeReference">
<type class="javax.baja.bacnet.io.RangeReference"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.RangeData"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.RejectException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.writeProperty(javax.baja.bacnet.io.PropertyValue) -->
<method name="writeProperty"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="propertyValue">
<type class="javax.baja.bacnet.io.PropertyValue"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.addListElements(javax.baja.bacnet.io.PropertyValue) -->
<method name="addListElements"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="propertyValue">
<type class="javax.baja.bacnet.io.PropertyValue"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ChangeListError"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.removeListElements(javax.baja.bacnet.io.PropertyValue) -->
<method name="removeListElements"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="propertyValue">
<type class="javax.baja.bacnet.io.PropertyValue"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ChangeListError"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getEventState() -->
<method name="getEventState"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the event state associated with the alarm source extension
</description>
<return>
<type class="javax.baja.bacnet.enums.BBacnetEventState"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getPoint() -->
<method name="getPoint"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the parent point of the BPointExtension returned by &lt;code&gt;<see ref="javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor#getObject()">#getObject()</see>&lt;/code&gt; if that extension&#xa; is instanceof BAlarmSourceExt. Otherwise, return null if getObject() returns null or the&#xa; extension is not instanceof BAlarmSourceExt.
</description>
<return>
<type class="javax.baja.control.BControlPoint"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getAckedTransitions() -->
<method name="getAckedTransitions"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
This method returns three flags that separately indicate the acknowledgment state for&#xa; TO_OFFNORMAL, TO_FAULT, and TO_NORMAL events
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getEventTimeStamps() -->
<method name="getEventTimeStamps"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetTimeStamp" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getNotifyType() -->
<method name="getNotifyType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getEventEnable() -->
<method name="getEventEnable"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getEventType() -->
<method name="getEventType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.statusChanged() -->
<method name="statusChanged"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getStatusFlags() -->
<method name="getStatusFlags"  public="true">
<description>
This method represents four Boolean flags that indicate the general &#x22;health&#x22; of an object.&#xa;  These properties are :- &lt;br&gt;&#xa;  &lt;ul&gt;&#xa;    &lt;li&gt; IN_ALARM : Logical FALSE (0) if the Event_State property has a value of NORMAL, otherwise logical TRUE (1) &lt;/li&gt;&#xa;    &lt;li&gt; FAULT : Logical TRUE (1) if the Reliability property is present and does not have a value of NO_FAULT_DETECTED, otherwise logical FALSE (0) &lt;/li&gt;&#xa;    &lt;li&gt; OVERRIDDEN : Logical FALSE (0) &lt;/li&gt;&#xa;    &lt;li&gt; OUT_OF_SERVICE : Logical FALSE (0) &lt;/li&gt;&#xa;  &lt;/ul&gt;
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
status Flags&#x27; BitString
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.isValidAlarmExt(javax.baja.alarm.BIAlarmSource) -->
<method name="isValidAlarmExt"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="ext">
<type class="javax.baja.alarm.BIAlarmSource"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.isEventInitiationEnabled() -->
<method name="isEventInitiationEnabled"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getEventPriorities() -->
<method name="getEventPriorities"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.getNotificationClass() -->
<method name="getNotificationClass"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.bacnet.export.BBacnetNotificationClassDescriptor"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.updateAlarmInhibit() -->
<method name="updateAlarmInhibit"  protected="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;deprecation&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.readProperty(int, int) -->
<method name="readProperty"  protected="true">
<description>
Get the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.readOptionalProperty(int, int) -->
<method name="readOptionalProperty"  protected="true">
<description>
Read the value of an optional property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.writeProperty(int, int, byte[], int) -->
<method name="writeProperty"  protected="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unused&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<parameter name="pId">
<type class="int"/>
</parameter>
<parameter name="ndx">
<type class="int"/>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
</parameter>
<parameter name="pri">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.eventEnrollmentOrd -->
<field name="eventEnrollmentOrd"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;eventEnrollmentOrd&lt;/code&gt; property.
</description>
<tag name="@see">#getEventEnrollmentOrd</tag>
<tag name="@see">#setEventEnrollmentOrd</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.objectName -->
<field name="objectName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectName&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectName</tag>
<tag name="@see">#setObjectName</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.description -->
<field name="description"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#getDescription</tag>
<tag name="@see">#setDescription</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.typeOfEvent -->
<field name="typeOfEvent"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;typeOfEvent&lt;/code&gt; property.
</description>
<tag name="@see">#getTypeOfEvent</tag>
<tag name="@see">#setTypeOfEvent</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.notifyTypeId -->
<field name="notifyTypeId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;notifyTypeId&lt;/code&gt; property.
</description>
<tag name="@see">#getNotifyTypeId</tag>
<tag name="@see">#setNotifyTypeId</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.objectPropertyReference -->
<field name="objectPropertyReference"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectPropertyReference&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectPropertyReference</tag>
<tag name="@see">#setObjectPropertyReference</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.notificationClassId -->
<field name="notificationClassId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;notificationClassId&lt;/code&gt; property.
</description>
<tag name="@see">#getNotificationClassId</tag>
<tag name="@see">#setNotificationClassId</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.reliability -->
<field name="reliability"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;reliability&lt;/code&gt; property.&#xa; Reliability of the Event Enrollment object to perform its monitoring function as described in&#xa; 135-2012 12.12.21. Does not reflect the reliability of the monitored object or the result of the&#xa; fault algorithm, if one is in use. Those other items are reflected in the BACnet Reliability&#xa; property as read through a BACnet request.
</description>
<tag name="@see">#getReliability</tag>
<tag name="@see">#setReliability</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.eventParameter -->
<field name="eventParameter"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;eventParameter&lt;/code&gt; property.
</description>
<tag name="@see">#getEventParameter</tag>
<tag name="@see">#setEventParameter</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetEventEnrollmentDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
