<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BacnetPropertyListProvider" name="BacnetPropertyListProvider" packageName="javax.baja.bacnet.export" public="true" interface="true" abstract="true" category="interface">
<description/>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<!-- javax.baja.bacnet.export.BacnetPropertyListProvider.getPropertyList() -->
<method name="getPropertyList"  public="true" abstract="true">
<description/>
<return>
<type class="int" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BacnetPropertyListProvider.readPropertyList(int) -->
<method name="readPropertyList"  public="true" default="true">
<description/>
<parameter name="ndx">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</return>
</method>

</class>
</bajadoc>
