<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.ui.BAlarmExtManager" name="BAlarmExtManager" packageName="com.tridium.alarm.ui" public="true">
<description>
BAlarmExtManager is a plugin for managing all alarm extensions&#xa; in a station.  It is registered as a view on the BAlarmService.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">09 Dec 2003</tag>
<tag name="@version">$Revision: 23$ $Date: 9/18/08 2:29:32 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.workbench.view.BWbComponentView"/>
</extends>
<action name="updateCommands" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;updateCommands&lt;/code&gt; action.
</description>
<tag name="@see">#updateCommands()</tag>
</action>

</class>
</bajadoc>
