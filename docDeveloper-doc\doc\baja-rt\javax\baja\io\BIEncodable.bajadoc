<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.io.BIEncodable" name="BIEncodable" packageName="javax.baja.io" public="true" interface="true" abstract="true" category="interface">
<description>
BIEncodable is implemented by BObjects which can be&#xa; serialized and unserialized into both a binary and&#xa; String format.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">6 Nov 00</tag>
<tag name="@version">$Revision: 1$ $Date: 2/27/03 5:12:35 PM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<!-- javax.baja.io.BIEncodable.encode(java.io.DataOutput) -->
<method name="encode"  public="true" abstract="true">
<description/>
<parameter name="out">
<type class="java.io.DataOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.BIEncodable.decode(java.io.DataInput) -->
<method name="decode"  public="true" abstract="true">
<description/>
<parameter name="in">
<type class="java.io.DataInput"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.BIEncodable.encodeToString() -->
<method name="encodeToString"  public="true" abstract="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.BIEncodable.decodeFromString(java.lang.String) -->
<method name="decodeFromString"  public="true" abstract="true">
<description/>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.BIEncodable.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
