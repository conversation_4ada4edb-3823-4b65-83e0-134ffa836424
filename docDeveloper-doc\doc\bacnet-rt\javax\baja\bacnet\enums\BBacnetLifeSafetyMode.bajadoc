<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetLifeSafetyMode" name="BBacnetLifeSafetyMode" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetLifeSafetyMode represents the Bacnet Life Safety Mode&#xa; enumeration.&#xa; &lt;p&gt;&#xa; BBacnetLifeSafetyMode is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-255 are reserved for use by ASHRAE.&#xa; Values from 256-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Craig Gemmill</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">16 May 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;off&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;on&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;test&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;manned&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unmanned&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;armed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;disarmed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;prearmed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;slow&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;fast&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;disconnected&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;enabled&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;disabled&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;automaticReleaseDisabled&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;defaultMode&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyMode"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyMode"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.OFF -->
<field name="OFF"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for off.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.ON -->
<field name="ON"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for on.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.TEST -->
<field name="TEST"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for test.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.MANNED -->
<field name="MANNED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for manned.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.UNMANNED -->
<field name="UNMANNED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unmanned.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.ARMED -->
<field name="ARMED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for armed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.DISARMED -->
<field name="DISARMED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for disarmed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.PREARMED -->
<field name="PREARMED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for prearmed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.SLOW -->
<field name="SLOW"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for slow.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.FAST -->
<field name="FAST"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for fast.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.DISCONNECTED -->
<field name="DISCONNECTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for disconnected.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.ENABLED -->
<field name="ENABLED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for enabled.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.DISABLED -->
<field name="DISABLED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for disabled.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.AUTOMATIC_RELEASE_DISABLED -->
<field name="AUTOMATIC_RELEASE_DISABLED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for automaticReleaseDisabled.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.DEFAULT_MODE -->
<field name="DEFAULT_MODE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for defaultMode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.off -->
<field name="off"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyMode"/>
<description>
BBacnetLifeSafetyMode constant for off.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.on -->
<field name="on"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyMode"/>
<description>
BBacnetLifeSafetyMode constant for on.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.test -->
<field name="test"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyMode"/>
<description>
BBacnetLifeSafetyMode constant for test.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.manned -->
<field name="manned"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyMode"/>
<description>
BBacnetLifeSafetyMode constant for manned.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.unmanned -->
<field name="unmanned"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyMode"/>
<description>
BBacnetLifeSafetyMode constant for unmanned.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.armed -->
<field name="armed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyMode"/>
<description>
BBacnetLifeSafetyMode constant for armed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.disarmed -->
<field name="disarmed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyMode"/>
<description>
BBacnetLifeSafetyMode constant for disarmed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.prearmed -->
<field name="prearmed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyMode"/>
<description>
BBacnetLifeSafetyMode constant for prearmed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.slow -->
<field name="slow"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyMode"/>
<description>
BBacnetLifeSafetyMode constant for slow.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.fast -->
<field name="fast"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyMode"/>
<description>
BBacnetLifeSafetyMode constant for fast.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.disconnected -->
<field name="disconnected"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyMode"/>
<description>
BBacnetLifeSafetyMode constant for disconnected.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.enabled -->
<field name="enabled"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyMode"/>
<description>
BBacnetLifeSafetyMode constant for enabled.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.disabled -->
<field name="disabled"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyMode"/>
<description>
BBacnetLifeSafetyMode constant for disabled.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.automaticReleaseDisabled -->
<field name="automaticReleaseDisabled"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyMode"/>
<description>
BBacnetLifeSafetyMode constant for automaticReleaseDisabled.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.defaultMode -->
<field name="defaultMode"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyMode"/>
<description>
BBacnetLifeSafetyMode constant for defaultMode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyMode"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyMode.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
