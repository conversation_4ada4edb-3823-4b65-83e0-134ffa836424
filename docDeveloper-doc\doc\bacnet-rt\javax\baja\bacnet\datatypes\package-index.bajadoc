<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="javax.baja.bacnet.datatypes">
<description/>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetAddress"><description>This class represents the BacnetAddress data structure,&#xa; containing a fixed 16-bit network number and a variable&#xa; length MAC address.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetAddressBinding"><description>BBacnetAddressBinding represents the BacnetAddressBinding&#xa; sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetAny"><description>BBac<PERSON>Any represents the Bacnet ANY type.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetArray"><description>BBacnetArray represents a Bacnet Array, which contains an indexed&#xa; sequence of objects of a particular Bacnet data type.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetBitString"><description>BBacnetBitString represents a bit string value in a Bacnet property.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetCalendarEntry"><description>This class represents the Bacnet CalendarEntry Choice.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetChannelValue"><description>BBacnetChannelValue represents the BACnetChannelValue&#xa; choice.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetClientCov"><description>BBacnetClientCov represents the choice for the COV increment to&#xa; be used in acquiring data for a trend log via COV.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetCovSubscription"><description>BBacnetCovSubscription represents information about a client subscription&#xa; for change-of-value notification on a Bacnet server object in this device.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetDailySchedule"><description>BBacnetDailySchedule represents the BacnetDailySchedule sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetDate"><description>BBacnetDate represents a date value in a Bacnet property.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetDateRange"><description>BBacnetDateRange represents a BacnetDateRange value in a Bacnet property.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetDateTime"><description>BBacnetDateTime represents a BacnetDateTime value in a Bacnet property.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetDestination"><description>BBacnetDestination represents the Bacnet Destination&#xa; sequence, used in the Recipient_List of Notification&#xa; Class objects.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetDeviceObjectPropertyReference"><description>This class represents the BBacnetDeviceObjectPropertyReference sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetDeviceObjectReference"><description>This class represents the BBacnetDeviceObjectReference sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetEventParameter"><description>BBacnetEventParameter represents the BACnetEventParameter&#xa; choice.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetFaultParameter"><description>Created by Sandipan Aich on 3/24/2017.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetGroupChannelValue"><description>BBacnetGroupChannelValue represents the BACnetGroupChannelValue&#xa; sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetLightingCommand"><description>Recipient for an alarm to be exported to Bacnet.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetListOf"><description>BBacnetListOf represents a Bacnet ListOf sequence, which contains a non-indexed&#xa; sequence of objects of a particular Bacnet data type.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetLogMultipleRecord"><description>BBacnetLogRecord represents the BacnetLogRecord sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetLogRecord"><description>BBacnetLogRecord represents the BacnetLogRecord sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetNull"><description>BBacnetNull represents a null value in a Bacnet property.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetObjectIdentifier"><description>BBacnetObjectIdentifier represents an object-identifier value in a Bacnet property.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetObjectPropertyReference"><description>This class represents the BBacnetObjectPropertyReference sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetOctetString"><description>BBacnetOctetString represents an octet string (byte array).</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetOptionalCharacterString"><description>Recipient for an alarm to be exported to Bacnet.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetPrescale"><description>BBacnetPrescale represents the Bacnet Prescale&#xa; sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetPriorityValue"><description>BBacnetPriorityValue represents the BacnetPriorityValue&#xa; choice.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetPropertyAccessResult"><description>This class represents the ReadPropertyResult sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetPropertyReference"><description>This class represents the BacnetPropertyReference sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetPropertyStates"><description>BBacnetPropertyStates represents the BACnetPropertyStates&#xa; choice.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetPropertyValue"><description>This class represents the BacnetPropertyValue sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetRecipient"><description>Recipient for an alarm to be exported to Bacnet.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetRecipientProcess"><description>BBacnetRecipientProcess represents the Bacnet RecipientProcess&#xa; sequence, used in Cov notifications.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetScale"><description>BBacnetScale represents the BACnetScale data type.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetSetpointReference"><description>BBacnetSetpointReference represents the BacnetSetpointReference&#xa; sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetSpecialEvent"><description>BBacnetSpecialEvent represents the BacnetSpecialEvent sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetTime"><description>BBacnetTime represents a date value in a Bacnet property.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetTimeStamp"><description>This class represents the Bacnet Timestamp Choice.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetTimeValue"><description>BBacnetTimeValue represents a BacnetTimeValue pair.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BBacnetUnsigned"><description>BBacnetUnsigned represents an unsigned value in a Bacnet property.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BErrorType"><description>BErrorType represents the Error sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BReadAccessResult"><description>BReadAccessResult represents the ReadAccessResult sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BReadAccessSpecification"><description>BReadAccessSpecification represents the ReadAccessSpecification sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BReadPropertyResult"><description>This class represents the ReadPropertyResult sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes" name="BIBacnetDataType" category="interface"><description>BIBacnetDataType is the interface implemented by Bacnet constructed data types.</description></class>
</package>
</bajadoc>
