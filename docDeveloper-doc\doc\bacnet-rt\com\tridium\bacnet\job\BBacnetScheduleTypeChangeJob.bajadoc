<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.job.BBacnetScheduleTypeChangeJob" name="BBacnetScheduleTypeChangeJob" packageName="com.tridium.bacnet.job" public="true">
<description>
BBacnetScheduleTypeChangeJob makes changes to a schedule object needed to&#xa; change the type of objects scheduled or cleanup inconsistent properties
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 14$ $Date: 10/17/00 12:47:14 PM$</tag>
<tag name="@creation">24 Aug 2010</tag>
<tag name="@since">Niagara 3.6</tag>
<extends>
<type class="com.tridium.bacnet.job.BDeviceManagerJob"/>
</extends>
</class>
</bajadoc>
