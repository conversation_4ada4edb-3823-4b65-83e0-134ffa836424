<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="com.tridium.bacnetAws.history.BBacnetAwsHistoryDeviceExt" name="BBacnetAwsHistoryDeviceExt" packageName="com.tridium.bacnetAws.history" public="true">
<description>
BBacnetOwsHistoryDeviceExt adds support for TrendMultiple.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">22 Mar 2010</tag>
<tag name="@version">$Revision: 1$ $Date: 9/02/03 10:54:15 AM$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.history.BBacnetHistoryDeviceExt"/>
</extends>
</class>
</bajadoc>
