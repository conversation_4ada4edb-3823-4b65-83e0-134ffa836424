<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.routing.ext.RouterExtException" name="RouterExtException" packageName="com.tridiumx.jsonToolkit.inbound.routing.ext" public="true" abstract="true" category="exception">
<description>
Exception type to be thrown by registration exportMarker utils ... the callout/&#xa; override hook for customer specific exportMarker behaviour
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="java.lang.Exception"/>
</extends>
<!-- com.tridiumx.jsonToolkit.inbound.routing.ext.RouterExtException(java.lang.String) -->
<constructor name="RouterExtException" protected="true">
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

</class>
</bajadoc>
