<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.job.BAndoverRunScriptJob" name="BAndoverRunScriptJob" packageName="com.tridium.andoverAC256.job" public="true">
<description>
Run a script from an BAndoverScript object&#xa; &lt;p&gt;
</description>
<tag name="@author">C<PERSON><PERSON></tag>
<tag name="@creation">6/7/2005 3:32PM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.84</tag>
<extends>
<type class="javax.baja.job.BJob"/>
</extends>
<implements>
<type class="com.tridium.andoverAC256.messages.AndoverMessageConst"/>
</implements>
<implements>
<type class="java.lang.Runnable"/>
</implements>
</class>
</bajadoc>
