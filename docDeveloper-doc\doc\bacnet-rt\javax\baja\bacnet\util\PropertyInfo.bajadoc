<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.util.PropertyInfo" name="PropertyInfo" packageName="javax.baja.bacnet.util" public="true">
<description>
PropertyInfo provides information about a Bacnet property.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">30 May 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<!-- javax.baja.bacnet.util.PropertyInfo(java.lang.String, int, int) -->
<constructor name="PropertyInfo" public="true">
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="id">
<type class="int"/>
</parameter>
<parameter name="asnType">
<type class="int"/>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.util.PropertyInfo(java.lang.String, int, int, int) -->
<constructor name="PropertyInfo" public="true">
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="id">
<type class="int"/>
</parameter>
<parameter name="asnType">
<type class="int"/>
</parameter>
<parameter name="pr">
<type class="int"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.util.PropertyInfo(javax.baja.xml.XElem, int) -->
<constructor name="PropertyInfo" public="true">
<parameter name="x">
<type class="javax.baja.xml.XElem"/>
</parameter>
<parameter name="objectPR">
<type class="int"/>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.util.PropertyInfo(java.lang.String, int, int, boolean, java.lang.String) -->
<constructor name="PropertyInfo" public="true">
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="id">
<type class="int"/>
</parameter>
<parameter name="asnType">
<type class="int"/>
</parameter>
<parameter name="facet">
<type class="boolean"/>
</parameter>
<parameter name="facetControl">
<type class="java.lang.String"/>
</parameter>
<description>
Basic constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.util.PropertyInfo(java.lang.String, int, int, boolean, java.lang.String, boolean, java.lang.String) -->
<constructor name="PropertyInfo" public="true">
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="id">
<type class="int"/>
</parameter>
<parameter name="asnType">
<type class="int"/>
</parameter>
<parameter name="extensible">
<type class="boolean"/>
</parameter>
<parameter name="type">
<type class="java.lang.String"/>
</parameter>
<parameter name="facet">
<type class="boolean"/>
</parameter>
<parameter name="facetControl">
<type class="java.lang.String"/>
</parameter>
<description>
Enum constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.util.PropertyInfo(java.lang.String, int, int, java.lang.String, boolean, boolean, java.lang.String) -->
<constructor name="PropertyInfo" public="true">
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="id">
<type class="int"/>
</parameter>
<parameter name="asnType">
<type class="int"/>
</parameter>
<parameter name="s">
<type class="java.lang.String"/>
<description/>
</parameter>
<parameter name="bitString">
<type class="boolean"/>
<description>
if true, s is used for bit string name&#xa;                  if false, s is used as list/constructed data type.
</description>
</parameter>
<parameter name="facet">
<type class="boolean"/>
</parameter>
<parameter name="facetControl">
<type class="java.lang.String"/>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.util.PropertyInfo(java.lang.String, int, int, java.lang.String, int, boolean, java.lang.String) -->
<constructor name="PropertyInfo" public="true">
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="id">
<type class="int"/>
</parameter>
<parameter name="asnType">
<type class="int"/>
</parameter>
<parameter name="type">
<type class="java.lang.String"/>
</parameter>
<parameter name="size">
<type class="int"/>
</parameter>
<parameter name="facet">
<type class="boolean"/>
</parameter>
<parameter name="facetControl">
<type class="java.lang.String"/>
</parameter>
<description>
Array constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.util.PropertyInfo.getName() -->
<method name="getName"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PropertyInfo.getId() -->
<method name="getId"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PropertyInfo.getPr() -->
<method name="getPr"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PropertyInfo.getAsnType() -->
<method name="getAsnType"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PropertyInfo.getDataType() -->
<method name="getDataType"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PropertyInfo.isExtensible() -->
<method name="isExtensible"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PropertyInfo.getBitStringName() -->
<method name="getBitStringName"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PropertyInfo.getType() -->
<method name="getType"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PropertyInfo.getSize() -->
<method name="getSize"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PropertyInfo.isEnum() -->
<method name="isEnum"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PropertyInfo.isBitString() -->
<method name="isBitString"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PropertyInfo.isConstructed() -->
<method name="isConstructed"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PropertyInfo.isArray() -->
<method name="isArray"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PropertyInfo.isList() -->
<method name="isList"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PropertyInfo.isChoice() -->
<method name="isChoice"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PropertyInfo.isFacet() -->
<method name="isFacet"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PropertyInfo.getFacetControl() -->
<method name="getFacetControl"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PropertyInfo.toString() -->
<method name="toString"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.util.PropertyInfo.isAws() -->
<method name="isAws"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

</class>
</bajadoc>
