<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.routing.BJsonDemuxRouter" name="BJsonDemuxRouter" packageName="com.tridiumx.jsonToolkit.inbound.routing" public="true">
<description>
Given input { name: &#x22;Bob&#x22;, age: 60, happy: true }&#xa; &lt;p&gt;&#xa; The adding a String slot named &#x22;name&#x22; will pass the value Bob&#xa; A BDouble name age, would get 60&#xa; &lt;p&gt;&#xa; For unnecessary data, don&#x27;t add a slot
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.inbound.routing.BJsonRouter"/>
</extends>
<property name="defaultMissing" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;defaultMissing&lt;/code&gt; property.&#xa; Reset absent keys when not found
</description>
<tag name="@see">#getDefaultMissing</tag>
<tag name="@see">#setDefaultMissing</tag>
</property>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonDemuxRouter() -->
<constructor name="BJsonDemuxRouter" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonDemuxRouter.getDefaultMissing() -->
<method name="getDefaultMissing"  public="true">
<description>
Get the &lt;code&gt;defaultMissing&lt;/code&gt; property.&#xa; Reset absent keys when not found
</description>
<tag name="@see">#defaultMissing</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonDemuxRouter.setDefaultMissing(boolean) -->
<method name="setDefaultMissing"  public="true">
<description>
Set the &lt;code&gt;defaultMissing&lt;/code&gt; property.&#xa; Reset absent keys when not found
</description>
<tag name="@see">#defaultMissing</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonDemuxRouter.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonDemuxRouter.routeValue(javax.baja.sys.BString, javax.baja.sys.Context) -->
<method name="routeValue"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="message">
<type class="javax.baja.sys.BString"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.inbound.routing.RoutingFailedException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonDemuxRouter.defaultMissing -->
<field name="defaultMissing"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;defaultMissing&lt;/code&gt; property.&#xa; Reset absent keys when not found
</description>
<tag name="@see">#getDefaultMissing</tag>
<tag name="@see">#setDefaultMissing</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.BJsonDemuxRouter.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
