<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.point.BAndoverStringProxyExt" name="BAndoverStringProxyExt" packageName="com.tridium.andoverAC256.point" public="true">
<description>
BAndoverBooleanProxyExt represents a single string value&#xa; from an Andover AC256 system.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@creation">9/30/2004 10:56AM</tag>
<tag name="@version">$Revision$ $Date:9/30/2004 10:56AM$</tag>
<tag name="@since">Niagara 3.0 andoverAC256 1.0</tag>
<extends>
<type class="com.tridium.andoverAC256.point.BAndoverProxyExt"/>
</extends>
</class>
</bajadoc>
