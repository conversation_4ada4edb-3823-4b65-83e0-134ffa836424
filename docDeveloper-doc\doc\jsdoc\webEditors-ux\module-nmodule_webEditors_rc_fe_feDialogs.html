<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>webEditors Module: nmodule/webEditors/rc/fe/feDialogs</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">webEditors</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-nmodule_webEditors_rc_fe_baja_BaseEditor.html">nmodule/webEditors/rc/fe/baja/BaseEditor</a></li><li><a href="module-nmodule_webEditors_rc_fe_BaseWidget.html">nmodule/webEditors/rc/fe/BaseWidget</a></li><li><a href="module-nmodule_webEditors_rc_fe_fe.html">nmodule/webEditors/rc/fe/fe</a></li><li><a href="module-nmodule_webEditors_rc_fe_feDialogs.html">nmodule/webEditors/rc/fe/feDialogs</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_commands_MgrCommand.html">nmodule/webEditors/rc/wb/mgr/commands/MgrCommand</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_Manager.html">nmodule/webEditors/rc/wb/mgr/Manager</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html">nmodule/webEditors/rc/wb/mgr/MgrLearn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrStateHandler.html">nmodule/webEditors/rc/wb/mgr/MgrStateHandler</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrTypeInfo.html">nmodule/webEditors/rc/wb/mgr/MgrTypeInfo</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_IconMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinPropMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_NameMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyPathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_TypeMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/MgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrModel.html">nmodule/webEditors/rc/wb/mgr/model/MgrModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">nmodule/webEditors/rc/wb/table/model/Column</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_DisplayNameColumn.html">nmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_IconColumn.html">nmodule/webEditors/rc/wb/table/model/columns/IconColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_JsonObjectPropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_PropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_ToStringColumn.html">nmodule/webEditors/rc/wb/table/model/columns/ToStringColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentSource.html">nmodule/webEditors/rc/wb/table/model/ComponentSource</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentTableModel.html">nmodule/webEditors/rc/wb/table/model/ComponentTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">nmodule/webEditors/rc/wb/table/model/Row</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html">nmodule/webEditors/rc/wb/table/model/TableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_Table.html">nmodule/webEditors/rc/wb/table/Table</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeNodeRow.html">nmodule/webEditors/rc/wb/table/tree/TreeNodeRow</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeTableModel.html">nmodule/webEditors/rc/wb/table/tree/TreeTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_tree_TreeNode.html">nmodule/webEditors/rc/wb/tree/TreeNode</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="tutorials.list.html" class="dropdown-toggle" data-toggle="dropdown">Tutorials<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="tutorial-6-managers.html">Managers</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: nmodule/webEditors/rc/fe/feDialogs</h1>
<section>

<header>
    
</header>


<article>
    <div class="container-overview">
    
        
            <div class="description"><p>Functions for showing field editors in modal dialogs. Useful for prompting<br>
the user to enter values, edit individual slots, and fire actions.</p></div>
        

        
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


        
    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id=".action"><span class="type-signature">&lt;static> </span>action(params)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Invoke an action on a mounted component. If the action requires a<br>
parameter, a field editor dialog will be shown to retrieve that argument<br>
from the user.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last">
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>component</code></td>
            

            <td class="type">
            
                
<span class="param-type">baja.Component</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>the component on which to invoke<br>
the action. Must be mounted.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>slot</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">baja.Slot</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>the action slot to invoke. Must be<br>
a valid Action slot.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>actionArgument</code></td>
            

            <td class="type">
            
                
<span class="param-type">baja.Value</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>Starting in Niagara 4.10, this<br>
action argument can be used instead of showing a dialog to obtain<br>
the argument.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved with the action return<br>
value if the action was successfully invoked, resolved with <code>null</code> if<br>
the user clicked Cancel, or rejected if the parameters were invalid or the<br>
action could not be invoked.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".error"><span class="type-signature">&lt;static> </span>error(err [, params])</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Show details about an error.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>err</code></td>
            

            <td class="type">
            
                
<span class="param-type">Error</span>
|

<span class="param-type">*</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last">
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>title</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>command</code></td>
            

            <td class="type">
            
                
<span class="param-type">module:bajaux/commands/Command</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>An optional Command to help display information on which Command failed</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>messageSummary</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>An optional messageSummary to prepend to the Error.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".selfClosing"><span class="type-signature">&lt;static> </span>selfClosing(params)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Show an editor in a dialog, similar to <code>showFor</code>, but with the added<br>
expectation that the editor represents a one-time interaction, like a<br>
button click, after which the dialog can be immediately closed. In other<br>
words, the &quot;click ok to close&quot; functionality is embedded in the editor<br>
itself. Only a Cancel button will be shown in the dialog itself.</p>
<p>In order for the dialog to close, the shown editor must trigger a<br>
<code>feDialogs.VALUE_READY_EVENT</code>, optionally with a read value. When this<br>
event is triggered, the dialog will be closed and the promise resolved<br>
with the value passed to the event trigger.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>params to be passed to <code>fe.buildFor</code></p>
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>title</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>title for the dialog</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>delay</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    200
                
                </td>
            

            <td class="description last"><p>delay in ms to wait before showing a<br>
loading spinner. The spinner will disappear when the field editor has<br>
finished initializing and loading.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>progressCallback</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>pass a progress callback to<br>
receive notifications as the editor being shown goes through the stages<br>
of its life cycle (created, initialized, loaded).</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved when the editor has<br>
triggered its own value event. It will be resolved with any value passed<br>
to the event trigger, or with <code>null</code> if Cancel was clicked.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
        <p class="code-caption">Trigger a VALUE_READY_EVENT to cause the dialog to be closed.
  </p>
    
    <pre class="sunlight-highlight-javascript">// ...
MyEditor.prototype.doInitialize = function (dom) {
  dom.on(&#x27;click&#x27;, &#x27;button&#x27;, function () {
    dom.trigger(feDialogs.VALUE_READY_EVENT, [ &#x27;my value&#x27; ]);
  });
};
//...

feDialogs.selfClosing({
  type: MyEditor
}}
  .then(function (value) {
    if (value === &#x27;my value&#x27;) {
      //success!
    }
  });</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id=".showFor"><span class="type-signature">&lt;static> </span>showFor(params)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Shows a field editor in a dialog.</p>
<p>When the user clicks OK, the editor will be saved, committing any changes.<br>
The value that the user entered will be read from the editor and used to<br>
resolve the promise.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>params to be passed to <code>fe.buildFor()</code>.</p>
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        
        <th>Default</th>
        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>dom</code></td>
            

            <td class="type">
            
                
<span class="param-type">jQuery</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>if your widget type should be instantiated<br>
into a specific kind of DOM element, it can be passed in as a parameter.<br>
Note that the given element will be appended to the dialog element itself,<br>
so do not pass in an element that is already parented. If omitted, a <code>div</code><br>
will be created and used.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>title</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>title for the dialog</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>delay</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                    200
                
                </td>
            

            <td class="description last"><p>delay in ms to wait before showing a<br>
loading spinner. The spinner will disappear when the field editor has<br>
finished initializing and loading.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>save</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>set to false to specify that the dialog<br>
should <em>not</em> be saved on clicking OK - only the current value will be read<br>
from the editor and used to resolve the promise.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>onSaveError</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>when this function is set and save<br>
is set to true, the error will be handed off to this method. Otherwise,<br>
when save is true feDialogs will show whatever error caused by the save in<br>
a different dialog. This may optionally return a promise.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>progressCallback</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>pass a progress callback to<br>
receive notifications as the editor being shown goes through the stages<br>
of its life cycle (<code>created</code>, <code>initialized</code>, <code>loaded</code>), as well as whenever<br>
the editor is validated (<code>invalid</code>, <code>valid</code>).</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>buttons</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;(module:dialogs~Button|string)></span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>as of Niagara 4.12,<br>
custom buttons can be specified. See examples for details.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>on</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object.&lt;string, function()></span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            
                <td class="default">
                
                </td>
            

            <td class="description last"><p>as of Niagara 4.12, custom handlers for<br>
<code>bajaux</code> events (and only <code>bajaux</code> events) can be specified. This is an object literal<br>
where the keys are <code>bajaux</code> event names and the values are event handler functions.<br>
See examples for details.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved when the user has entered<br>
a value into the field editor and clicked OK, or rejected if the field<br>
could not be read. The promise will be resolved with the value that the<br>
user entered (or <code>null</code> if Cancel was clicked).</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
        <h5>Examples</h5>
        
    <pre class="sunlight-highlight-javascript">feDialogs.showFor({
    value: &#x27;enter a string here (max 50 chars)&#x27;,
    properties: { max: 50 },
    progressCallback: function (msg, arg) {
      switch(msg) {
      case &#x27;created&#x27;:     return console.log(&#x27;editor created&#x27;, arg);
      case &#x27;initialized&#x27;: return console.log(&#x27;editor initialized&#x27;, arg.jq());
      case &#x27;loaded&#x27;:      return console.log(&#x27;editor loaded&#x27;, arg.value());
      case &#x27;invalid&#x27;:     return console.log(&#x27;validation error&#x27;, arg);
      case &#x27;valid&#x27;:       return console.log(&#x27;value is valid&#x27;, arg);
      }
    }
  })
  .then(function (str) {
    if (str === null) {
      console.log(&#x27;you clicked cancel&#x27;);
    } else {
      console.log(&#x27;you entered: &#x27; + str);
    }
  });</pre>

        <p class="code-caption">Specify custom button handlers. If the user clicks one of these custom buttons,
the showFor promise will be resolved with the value resolved by its handler.</p>
    
    <pre class="sunlight-highlight-javascript">feDialogs.showFor({
  value: &#x27;enter a string&#x27;,
  buttons: [ {
    name: &#x27;uppercase&#x27;,
    displayName: &#x27;Uppercase It&#x27;,
    handler: (dialog, event, editor) {
      // the arguments to the button handler are: the Dialog instance, the click event,
      // and the editor being shown in the dialog.
      // call &#x60;dialog.keepOpen&#x60; if you are not ready for the dialog to close. the dialog will
      // stay open and the promise will not be resolved yet.

      dialog.keepOpen();
      return editor.read().then((string) =&gt; editor.load(string.toUpperCase());
    }
  }, {
    name: &#x27;lowercase&#x27;,
    displayName: &#x27;Lowercase It&#x27;,
    handler: (dialog, event, editor) {
      dialog.keepOpen();
      return editor.read().then((string) =&gt; editor.load(string.toLowerCase()));
    }
  }, {
    // default &#x27;ok&#x27; behavior is to read the value and resolve the promise. you don&#x27;t have to
    // specify a handler to do this.
    name: &#x27;ok&#x27;
  } ]
});</pre>

        <p class="code-caption">The strings 'ok', 'cancel', 'yes', and 'no' are special - you can include them in the buttons
parameter to get their default behavior.</p>
    
    <pre class="sunlight-highlight-javascript">// only show the OK button, and resolve the promise with the entered value. &#x27;yes&#x27; works the same.
feDialogs.showFor({ value: &#x27;enter a string&#x27;, buttons: [ &#x27;ok&#x27; ] });

// only show the Cancel button, and resolve the promise with null. &#x27;no&#x27; works the same.
feDialogs.showFor({ value: &#x27;your changes will not be used&#x27;, buttons: [ &#x27;cancel&#x27; ] });</pre>

        <p class="code-caption">The buttons parameter can be an object literal, where the values are button
definitions or handler functions.</p>
    
    <pre class="sunlight-highlight-javascript">feDialogs.showFor({
  value: &#x27;Value to Edit&#x27;,
  buttons: {
    ok: () =&gt; &#x27;my custom ok result&#x27;, // the value can be just a handler function. the default display name will be used.
    cancel: {
      displayName: &quot;Never Mind&quot;
      // omit the handler, and default handler for &quot;cancel&quot; will resolve null.
    },
    yes: {}, // just an empty object will use the default display name and default handler.
    no: {
      handler: () =&gt; &#x27;user clicked &quot;no&quot;&#x27; // include a handler to override the default handler.
    },
    delete: {
      // for anything other than &#x27;ok&#x27;, &#x27;cancel&#x27;, &#x27;yes&#x27;, or &#x27;no&#x27;, you&#x27;ll need to provide a
      // display name - or else just the button name will be used.
      displayName: &#x27;Delete Everything&#x27;,
      handler: () =&gt; deleteEverything()
    },
    retry: shouldShowRetryButton() &amp;&amp; { // falsy values will cause the button _not_ to be shown.
      displayName: &#x27;Try Again&#x27;,
      handler: () =&gt; letUserTryAgain()
    }
  }
});</pre>

        <p class="code-caption">Use the 'on' parameter to respond to any bajaux events that are triggered by the
editor.</p>
    
    <pre class="sunlight-highlight-javascript">const { MODIFY_EVENT } = events;
feDialogs.showFor({
  value: &#x27;edit me&#x27;,
  properties: { max: 10 },
  on: {
    [MODIFY_EVENT]: (dialog, event, editor) {
      return editor.validate()
        .catch(() =&gt; alert(&#x27;no more than 10 characters pls&#x27;));
    }
  }
});</pre>


    
</dd>

        </dl>
    

    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	webEditors Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:59+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>