<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetBooleanScheduleDescriptor" name="BBacnetBooleanScheduleDescriptor" packageName="javax.baja.bacnet.export" public="true">
<description>
BBacnetBooleanScheduleDescriptor exposes a Niagara schedule to Bacnet.
</description>
<tag name="@author"><PERSON> on 18 Aug 03</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.export.BBacnetScheduleDescriptor"/>
</extends>
<property name="scheduleDataType" flags="">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;scheduleDataType&lt;/code&gt; property.
</description>
<tag name="@see">#getScheduleDataType</tag>
<tag name="@see">#setScheduleDataType</tag>
</property>

<!-- javax.baja.bacnet.export.BBacnetBooleanScheduleDescriptor() -->
<constructor name="BBacnetBooleanScheduleDescriptor" public="true">
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetBooleanScheduleDescriptor.getScheduleDataType() -->
<method name="getScheduleDataType"  public="true">
<description>
Get the &lt;code&gt;scheduleDataType&lt;/code&gt; property.
</description>
<tag name="@see">#scheduleDataType</tag>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBooleanScheduleDescriptor.setScheduleDataType(javax.baja.sys.BEnum) -->
<method name="setScheduleDataType"  public="true">
<description>
Set the &lt;code&gt;scheduleDataType&lt;/code&gt; property.
</description>
<tag name="@see">#scheduleDataType</tag>
<parameter name="v">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBooleanScheduleDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBooleanScheduleDescriptor.doWritePresentValue() -->
<method name="doWritePresentValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Write the present value of the schedule to non-Present_Value&#xa; target properties, and to any external targets.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBooleanScheduleDescriptor.isEqual(int, int) -->
<method name="isEqual"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="ansTypeOfRefObj">
<type class="int"/>
</parameter>
<parameter name="asnTypeOfSchedule">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBooleanScheduleDescriptor.readProperty(int, int) -->
<method name="readProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBooleanScheduleDescriptor.writeProperty(int, int, byte[], int) -->
<method name="writeProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;fallthrough&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Set the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetBooleanScheduleDescriptor.doWriteScheduleDefaultValue(com.tridium.bacnet.asn.AsnInputStream, int) -->
<method name="doWriteScheduleDefaultValue"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;fallthrough&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Write the schedule default value for boolean type schedule
</description>
<parameter name="asnInputStream">
<type class="com.tridium.bacnet.asn.AsnInputStream"/>
<description/>
</parameter>
<parameter name="applicationTag">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if no error, otherwise errorcode
</description>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetBooleanScheduleDescriptor.BOOLEAN_IDX -->
<field name="BOOLEAN_IDX"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.export.BBacnetBooleanScheduleDescriptor.ENUMERATED_IDX -->
<field name="ENUMERATED_IDX"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.export.BBacnetBooleanScheduleDescriptor.BOOL_DATA_TYPE_RANGE -->
<field name="BOOL_DATA_TYPE_RANGE"  public="true" static="true" final="true">
<type class="javax.baja.sys.BEnumRange"/>
<description/>
</field>

<!-- javax.baja.bacnet.export.BBacnetBooleanScheduleDescriptor.scheduleDataType -->
<field name="scheduleDataType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;scheduleDataType&lt;/code&gt; property.
</description>
<tag name="@see">#getScheduleDataType</tag>
<tag name="@see">#setScheduleDataType</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetBooleanScheduleDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
