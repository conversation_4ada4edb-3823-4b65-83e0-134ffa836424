<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.rsp.BInfinitySuccessResponse" name="BInfinitySuccessResponse" packageName="com.tridium.andoverInfinity.comm.rsp" public="true">
<description>
BInfinitySuccessResponse sole purpose in life is to satisfy the completion of &#xa; requests or request-sequences.  It contains no data, and returns true always&#xa; for isComplete() method.
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.comm.rsp.BDdfResponse"/>
</extends>
<implements>
<type class="com.tridium.ddf.comm.rsp.BIDdfMultiFrameResponse"/>
</implements>
<!-- com.tridium.andoverInfinity.comm.rsp.BInfinitySuccessResponse() -->
<constructor name="BInfinitySuccessResponse" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinitySuccessResponse.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinitySuccessResponse.isComplete() -->
<method name="isComplete"  public="true">
<description>
This message always signals the completion of the transaction
</description>
<tag name="@see">com.tridium.devDriver.comm.rsp.BIDdfMultiFrameResponse#isComplete()</tag>
<return>
<type class="boolean"/>
<description>
true
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinitySuccessResponse.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
