<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.io.AsnDataTypeNotSupportedException" name="AsnDataTypeNotSupportedException" packageName="javax.baja.bacnet.io" public="true" category="exception">
<description>
A AsnDataTypeNotSupportedException is thrown whenever an&#xa; a provided data type is not in the set of supported data types&#xa; for a selected field.
</description>
<tag name="@author">Upender Paravastu</tag>
<tag name="@creation">22 Feb 19</tag>
<extends>
<type class="javax.baja.bacnet.io.DataTypeNotSupportedException"/>
</extends>
<!-- javax.baja.bacnet.io.AsnDataTypeNotSupportedException(java.lang.String) -->
<constructor name="AsnDataTypeNotSupportedException" public="true">
<parameter name="detailMessage">
<type class="java.lang.String"/>
<description>
the error message.
</description>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.io.AsnDataTypeNotSupportedException(int, java.lang.String) -->
<constructor name="AsnDataTypeNotSupportedException" public="true">
<parameter name="asnType">
<type class="int"/>
<description/>
</parameter>
<parameter name="detailMessage">
<type class="java.lang.String"/>
<description/>
</parameter>
<description>
Constructor with specified current asnType and detailed message.
</description>
</constructor>

<!-- javax.baja.bacnet.io.AsnDataTypeNotSupportedException.getAsnType() -->
<method name="getAsnType"  public="true">
<description/>
<return>
<type class="int"/>
<description>
the asn type of the current data type passed
</description>
</return>
</method>

</class>
</bajadoc>
