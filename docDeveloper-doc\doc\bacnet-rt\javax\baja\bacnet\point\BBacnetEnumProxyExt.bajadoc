<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.point.BBacnetEnumProxyExt" name="BBacnetEnumProxyExt" packageName="javax.baja.bacnet.point" public="true">
<description>
BBacnetEnumProxyExt handles the point configuration&#xa; of a point of type BOOLEAN, UNSIGNED, or ENUMERATED&#xa; in a Bacnet device.&#xa; &lt;p&gt;&#xa; Enumerated property values in Bacnet devices are&#xa; mapped to BEnumPoints in Niagara.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">04 Jan 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.point.BBacnetProxyExt"/>
</extends>
<property name="signed" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;signed&lt;/code&gt; property.&#xa; should the encoding of this point use Asn Integer (true)&#xa; or Asn Unsigned (false)?
</description>
<tag name="@see">#getSigned</tag>
<tag name="@see">#setSigned</tag>
</property>

<!-- javax.baja.bacnet.point.BBacnetEnumProxyExt() -->
<constructor name="BBacnetEnumProxyExt" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.point.BBacnetEnumProxyExt.getSigned() -->
<method name="getSigned"  public="true">
<description>
Get the &lt;code&gt;signed&lt;/code&gt; property.&#xa; should the encoding of this point use Asn Integer (true)&#xa; or Asn Unsigned (false)?
</description>
<tag name="@see">#signed</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetEnumProxyExt.setSigned(boolean) -->
<method name="setSigned"  public="true">
<description>
Set the &lt;code&gt;signed&lt;/code&gt; property.&#xa; should the encoding of this point use Asn Integer (true)&#xa; or Asn Unsigned (false)?
</description>
<tag name="@see">#signed</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetEnumProxyExt.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetEnumProxyExt.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<description>
BBacnetEnumProxyExt must be in a EnumPoint.
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetEnumProxyExt.fromEncodedValue(byte[], javax.baja.status.BStatus, javax.baja.sys.Context) -->
<method name="fromEncodedValue"  public="true">
<description/>
<parameter name="encodedValue">
<type class="byte" dimension="1"/>
</parameter>
<parameter name="bacnetStatus">
<type class="javax.baja.status.BStatus"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetEnumProxyExt.toEncodedValue(javax.baja.status.BStatusValue) -->
<method name="toEncodedValue"  public="true">
<description/>
<parameter name="newValue">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<return>
<type class="byte" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetEnumProxyExt.signed -->
<field name="signed"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;signed&lt;/code&gt; property.&#xa; should the encoding of this point use Asn Integer (true)&#xa; or Asn Unsigned (false)?
</description>
<tag name="@see">#getSigned</tag>
<tag name="@see">#setSigned</tag>
</field>

<!-- javax.baja.bacnet.point.BBacnetEnumProxyExt.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
