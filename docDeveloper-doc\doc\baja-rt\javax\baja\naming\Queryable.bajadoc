<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.Queryable" name="Queryable" packageName="javax.baja.naming" public="true" interface="true" abstract="true" category="interface">
<description>
A Queryable object knows how to provide a cursor for iterating&#xa; through objects that satisfy a query.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">18 Apr 2002</tag>
<tag name="@version">$Revision: 9$ $Date: 3/28/05 10:03:22 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<!-- javax.baja.naming.Queryable.bqlQuery(javax.baja.naming.OrdTarget, javax.baja.naming.OrdQuery) -->
<method name="bqlQuery"  public="true" abstract="true">
<description>
Get a collection of objects that satisfy the specified query.&#xa; The returned collection may include a superset of the objects&#xa; that satisfy the query.  The query processor will guarantee&#xa; that the end result only contains objects that satisfy the query.
</description>
<parameter name="base">
<type class="javax.baja.naming.OrdTarget"/>
<description/>
</parameter>
<parameter name="query">
<type class="javax.baja.naming.OrdQuery"/>
<description>
The query that qualifies the result set.
</description>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
<description>
Returns a collection for iterating through the qualified result.
</description>
</return>
</method>

</class>
</bajadoc>
