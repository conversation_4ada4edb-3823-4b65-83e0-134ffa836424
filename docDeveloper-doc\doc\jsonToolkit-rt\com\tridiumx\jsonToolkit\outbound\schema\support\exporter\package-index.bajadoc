<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.outbound.schema.support.exporter">
<description/>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.support.exporter" name="BJsonExporter"><description>Json Exporter creates a file with the current output of the schema being viewed.</description></class>
</package>
</bajadoc>
