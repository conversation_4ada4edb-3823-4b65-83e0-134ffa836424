<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetList" name="BJsonSchemaFacetList" packageName="com.tridiumx.jsonToolkit.outbound.schema.property" public="true">
<description>
A list of name/value properties based upon selected facets found upon a binding target.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaPropertyList"/>
</extends>
<property name="facetCsvList" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;facetCsvList&lt;/code&gt; property.
</description>
<tag name="@see">#getFacetCsvList</tag>
<tag name="@see">#setFacetCsvList</tag>
</property>

<property name="writeEmptyStringsForMissingFacets" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;writeEmptyStringsForMissingFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getWriteEmptyStringsForMissingFacets</tag>
<tag name="@see">#setWriteEmptyStringsForMissingFacets</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetList() -->
<constructor name="BJsonSchemaFacetList" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetList.getFacetCsvList() -->
<method name="getFacetCsvList"  public="true">
<description>
Get the &lt;code&gt;facetCsvList&lt;/code&gt; property.
</description>
<tag name="@see">#facetCsvList</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetList.setFacetCsvList(java.lang.String) -->
<method name="setFacetCsvList"  public="true">
<description>
Set the &lt;code&gt;facetCsvList&lt;/code&gt; property.
</description>
<tag name="@see">#facetCsvList</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetList.getWriteEmptyStringsForMissingFacets() -->
<method name="getWriteEmptyStringsForMissingFacets"  public="true">
<description>
Get the &lt;code&gt;writeEmptyStringsForMissingFacets&lt;/code&gt; property.
</description>
<tag name="@see">#writeEmptyStringsForMissingFacets</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetList.setWriteEmptyStringsForMissingFacets(boolean) -->
<method name="setWriteEmptyStringsForMissingFacets"  public="true">
<description>
Set the &lt;code&gt;writeEmptyStringsForMissingFacets&lt;/code&gt; property.
</description>
<tag name="@see">#writeEmptyStringsForMissingFacets</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetList.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetList.make(javax.baja.naming.BOrd, java.lang.String, boolean) -->
<method name="make"  public="true" static="true">
<description/>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<parameter name="facetCsvList">
<type class="java.lang.String"/>
</parameter>
<parameter name="writeEmptyStringsForMissingFacets">
<type class="boolean"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetList"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetList.process(com.tridium.json.JSONWriter, boolean) -->
<method name="process"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="jsonWriter">
<type class="com.tridium.json.JSONWriter"/>
</parameter>
<parameter name="jsonKeysValid">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetList.facetCsvList -->
<field name="facetCsvList"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;facetCsvList&lt;/code&gt; property.
</description>
<tag name="@see">#getFacetCsvList</tag>
<tag name="@see">#setFacetCsvList</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetList.writeEmptyStringsForMissingFacets -->
<field name="writeEmptyStringsForMissingFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;writeEmptyStringsForMissingFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getWriteEmptyStringsForMissingFacets</tag>
<tag name="@see">#setWriteEmptyStringsForMissingFacets</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaFacetList.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
