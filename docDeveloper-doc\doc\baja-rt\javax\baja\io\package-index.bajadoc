<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="baja" runtimeProfile="rt" name="javax.baja.io">
<description>
The io package contains Baja input/output support.
</description>
<class packageName="javax.baja.io" name="HtmlWriter"><description>HtmlWriter is used to generate HTML code with common tags.</description></class>
<class packageName="javax.baja.io" name="RandomAccessFileInputStream"><description>RandomAccessFileInputStream is an input stream for reading from&#xa; a RandomAccessFile.</description></class>
<class packageName="javax.baja.io" name="RandomAccessFileOutputStream"><description>RandomAccessFileOutputStream is an output stream for writing to&#xa; a RandomAccessFile.</description></class>
<class packageName="javax.baja.io" name="ValueDocDecoder"><description>ValueDocDecoder creates an in-memory graph of BObjects from&#xa; serialized document representation.</description></class>
<class packageName="javax.baja.io" name="ValueDocDecoder.BogDecoderPlugin"><description>BOG (Baja Object Graph) XML decoder plug-in</description></class>
<class packageName="javax.baja.io" name="ValueDocDecoder.BogElement"><description>A BOG document element</description></class>
<class packageName="javax.baja.io" name="ValueDocDecoder.BogTypeResolver"><description>BOG Type Resolver</description></class>
<class packageName="javax.baja.io" name="ValueDocEncoder"><description>ValueDocEncoder is used to encode a BValue tree to a document</description></class>
<class packageName="javax.baja.io" name="ValueDocEncoder.BogEncoderPlugin"/>
<class packageName="javax.baja.io" name="BIContextEncodable" category="interface"><description>BIEncodable is implemented by BObjects which can be&#xa; serialized and unserialized into both a binary and&#xa; String format, and can accept a context to their&#xa; encodeToString method</description></class>
<class packageName="javax.baja.io" name="BIEncodable" category="interface"><description>BIEncodable is implemented by BObjects which can be&#xa; serialized and unserialized into both a binary and&#xa; String format.</description></class>
<class packageName="javax.baja.io" name="ValueDocDecoder.IDecoderPlugin" category="interface"><description>A value document&#x27;s format can vary (XML or JSON).</description></class>
<class packageName="javax.baja.io" name="ValueDocDecoder.ITypeResolver" category="interface"><description>The ITypeResolver interface is used to take String attribute&#xa; elements parsed from the document and determine how to&#xa; resolve a module/type/instance.</description></class>
<class packageName="javax.baja.io" name="ValueDocEncoder.IEncoderPlugin" category="interface"><description>The document format can vary by objects implementing this interface</description></class>
<class packageName="javax.baja.io" name="BajaIOException" category="exception"><description>BajaIOException is the root exception for checked&#xa; IOExceptions in the Baja architecture.</description></class>
</package>
</bajadoc>
