<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BBacnetConfigFolder" name="BBacnetConfigFolder" packageName="javax.baja.bacnet.config" public="true">
<description>
BBacnetConfigFolder is the standard container to use&#xa; under BBacnetConfigDeviceExt to organize BBacnetObjects.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">19 Nov 2004</tag>
<tag name="@since">Niagara 3 BACnet 1.0</tag>
<extends>
<type class="javax.baja.util.BFolder"/>
</extends>
<implements>
<type class="javax.baja.bacnet.config.BIBacnetConfigFolder"/>
</implements>
<implements>
<type class="javax.baja.driver.loadable.BILoadable"/>
</implements>
<action name="upload" flags="h">
<parameter name="parameter">
<type class="javax.baja.driver.loadable.BUploadParameters"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;upload&lt;/code&gt; action.
</description>
<tag name="@see">#upload(BUploadParameters parameter)</tag>
</action>

<action name="download" flags="h">
<parameter name="parameter">
<type class="javax.baja.driver.loadable.BDownloadParameters"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;download&lt;/code&gt; action.
</description>
<tag name="@see">#download(BDownloadParameters parameter)</tag>
</action>

<!-- javax.baja.bacnet.config.BBacnetConfigFolder() -->
<constructor name="BBacnetConfigFolder" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetConfigFolder.upload(javax.baja.driver.loadable.BUploadParameters) -->
<method name="upload"  public="true">
<description>
Invoke the &lt;code&gt;upload&lt;/code&gt; action.
</description>
<tag name="@see">#upload</tag>
<parameter name="parameter">
<type class="javax.baja.driver.loadable.BUploadParameters"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigFolder.download(javax.baja.driver.loadable.BDownloadParameters) -->
<method name="download"  public="true">
<description>
Invoke the &lt;code&gt;download&lt;/code&gt; action.
</description>
<tag name="@see">#download</tag>
<parameter name="parameter">
<type class="javax.baja.driver.loadable.BDownloadParameters"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigFolder.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigFolder.getConfig() -->
<method name="getConfig"  public="true">
<description>
Get the parent network.
</description>
<return>
<type class="javax.baja.bacnet.config.BBacnetConfigDeviceExt"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigFolder.doUpload(javax.baja.driver.loadable.BUploadParameters, javax.baja.sys.Context) -->
<method name="doUpload"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="params">
<type class="javax.baja.driver.loadable.BUploadParameters"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigFolder.doDownload(javax.baja.driver.loadable.BDownloadParameters, javax.baja.sys.Context) -->
<method name="doDownload"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="params">
<type class="javax.baja.driver.loadable.BDownloadParameters"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigFolder.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigFolder.getIcon() -->
<method name="getIcon"  public="true">
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigFolder.upload -->
<field name="upload"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;upload&lt;/code&gt; action.
</description>
<tag name="@see">#upload(BUploadParameters parameter)</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetConfigFolder.download -->
<field name="download"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;download&lt;/code&gt; action.
</description>
<tag name="@see">#download(BDownloadParameters parameter)</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetConfigFolder.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
