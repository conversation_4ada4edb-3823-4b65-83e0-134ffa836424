<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.device.overrides.SegmentationOverride" name="SegmentationOverride" packageName="javax.baja.bacnet.device.overrides" public="true" interface="true" abstract="true" category="interface">
<description>
Some devices claim SegementationSupport, but really do not&#xa; segment well, or the network conditions cause continuous&#xa; out-of-order packet delivery. This override provides a way&#xa; for the user to explicitly disable segmentation support.
</description>
<tag name="@author"><PERSON></tag>
<implements>
<type class="javax.baja.bacnet.device.overrides.DeviceOverride"/>
</implements>
<!-- javax.baja.bacnet.device.overrides.SegmentationOverride.getSegmentationSupported(javax.baja.bacnet.config.BBacnetDeviceObject) -->
<method name="getSegmentationSupported"  public="true" abstract="true">
<description/>
<parameter name="device">
<type class="javax.baja.bacnet.config.BBacnetDeviceObject"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetSegmentation"/>
</return>
</method>

</class>
</bajadoc>
