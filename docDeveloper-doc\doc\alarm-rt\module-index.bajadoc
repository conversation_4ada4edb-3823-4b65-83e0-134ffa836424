<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="alarm" runtimeProfile="rt" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>Niagara Alarm Module</description>
<package name="javax.baja.alarm"><description>&lt;p&gt;&#xa;This package provides the core functionality for the lifecycle management of&#xa;alarms within the Baja framework.</description></package>
<package name="javax.baja.alarm.ext"><description>&lt;p&gt;This package provides classes for alarm extensions to control points.</description></package>
<package name="javax.baja.alarm.ext.offnormal"><description>&lt;p&gt;This package provides classes for offnormal alarm algorithms for control points.</description></package>
<package name="javax.baja.alarm.ext.fault"><description>&lt;p&gt;This package provides classes for fault alarm algorithms for control points.</description></package>
<package name="com.tridium.alarm"/>
<package name="com.tridium.alarm.db"/>
<package name="com.tridium.alarm.db.file"/>
<package name="com.tridium.alarm.ack"/>
<package name="com.tridium.alarm.fox"/>
<package name="com.tridium.alarm.user"/>
<class packageName="javax.baja.alarm" name="AlarmDbConnection"><description>AlarmDbConnection provides access to an Alarm Database.</description></class>
<class packageName="javax.baja.alarm" name="AlarmException" category="exception"><description>Base exception class for alarm.</description></class>
<class packageName="javax.baja.alarm" name="AlarmSpaceConnection" category="interface"><description>AlarmSpaceConnection provides access to a BIAlarmSpace.</description></class>
<class packageName="javax.baja.alarm" name="AlarmSupport"><description>AlarmSupport is a support class to enabled easy alarm generation and alarm&#xa; handling for BIAlarmSources.</description></class>
<class packageName="com.tridium.alarm.db" name="BAckPendingAlarmTable"><description>BAckPendingAlarmTable presents the list of ackPending alarms as a table.</description></class>
<class packageName="javax.baja.alarm" name="BAckState"><description>BAckState represents the states of acknowledgement for an alarm.</description></class>
<class packageName="com.tridium.alarm.ack" name="BAlarmAcknowledger"><description>Abstract class for a component that acknowledges alarms.</description></class>
<class packageName="javax.baja.alarm.ext" name="BAlarmAlgorithm"><description>BAlarmAlgorithm is the base class for all alarm&#xa; algorithms designed to alarm algorithms for &#xa; BAlarmSourceExt.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmArchive"><description>An Alarm Archive is a archive of cleared alarms.</description></class>
<class packageName="com.tridium.alarm.fox" name="BAlarmArchiveChannel"><description>BAlarmArchiveChannel is a BAlarmDbChannel that reads and writes data from&#xa; the Alarm Archive Database</description></class>
<class packageName="javax.baja.alarm" name="BAlarmClass"><description>A BAlarmClass object is used to group alarms that have&#xa; the same routing/handling characteristics.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmClassFolder"><description>BAlarmClassFolder is a Folder for grouping AlarmClasses under the AlarmService.</description></class>
<class packageName="com.tridium.alarm" name="BAlarmConsoleChannel"><description>BAlarmConsoleChannel is used by BConsoleRecipient to handle messages between&#xa; the BConsoleRecipient and BAlarmConsole.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmDatabase"><description>BAlarmDatabase stores both a history of alarms and a&#xa; list of unacked alarm persistently.</description></class>
<class packageName="com.tridium.alarm.fox" name="BAlarmDbChannel"><description>The AlarmDbChannel handles all messages for accessing an alarm database.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmDbConfig"><description>BAlarmDbConfig is the configuration for the alarm database.</description></class>
<class packageName="com.tridium.alarm.db" name="BAlarmDbQueryResult"><description>BAlarmDbQueryResult is the result of a bql query of the alarm database.</description></class>
<class packageName="com.tridium.alarm" name="BAlarmExtStatusJob"><description>BAlarmExtStatusJob is used to enable/disable a group of&#xa; BAlarmSourceExts.</description></class>
<class packageName="com.tridium.alarm" name="BAlarmFilterSet"><description>BAlarmFilterSet is a set of BIFilters customized to incorporate &#xa; the specific alarm filter requirements.</description></class>
<class packageName="com.tridium.alarm" name="BAlarmInputTable"><description>BAlarmTable is a table of alarm records.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmInstructions"><description>Ordered list of User Instructions for how to handle an alarm.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmPriorities"><description>BAlarmPriorities contains the priority mapping for each &#xa; Baja alarm transition type.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmRecipient"><description>BAlarmRecipient is the super-class of all alarm recipients&#xa; in the Baja framework.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmRecord"><description>Representation of a time stamped alarm record.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmSchema"><description>BAlarmSchema is the set of name-type pairs that describe a single&#xa; record in a alarm.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmScheme"><description>The alarm scheme provides access to the alarm database.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmService"><description>The BAlarmService uses BAlarmClasses to route all alarm&#xa; messages between AlarmSources and BAlarmRecipients.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmSource"><description>An AlarmSource represents a source for in the Niagara alarm database.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmSource.SourceComparator"></class>
<class packageName="javax.baja.alarm.ext" name="BAlarmSourceExt"><description>BAlarmSourceExt is the abstract superclass of all&#xa; Baja control alarming algorithms.</description></class>
<class packageName="javax.baja.alarm" name="BAlarmSourceInfo"><description>BAlarmSourceInfo is a data structure used to define common alarm data for use&#xa; with the AlarmSupport class.</description></class>
<class packageName="javax.baja.alarm.ext" name="BAlarmState"><description>BAlarmState is an BEnum that represents valid Baja alarm states</description></class>
<class packageName="com.tridium.alarm.db" name="BAlarmTable"><description>BAlarmTable is the result of a bql query of the alarm database.</description></class>
<class packageName="javax.baja.alarm.ext" name="BAlarmTimestamps"><description>BAlarmTimestamps</description></class>
<class packageName="javax.baja.alarm" name="BAlarmTransitionBits"><description>The BAlarmTransitionBits object contains a bit for each&#xa; alarm state transition type defined within Baja:&#xa;    toOffnormal&#xa;    toFault&#xa;    toNormal</description></class>
<class packageName="javax.baja.alarm" name="BArchiveAlarmProvider"><description>BArchiveAlarmProvider is added to the BAlarmService and provides mechanism to&#xa; archive cleared alarms.</description></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BBooleanChangeOfStateAlgorithm"><description>BBooleanChangeOfStateAlgorithm implements a change of&#xa; state alarm detection algorithm for boolean objects&#xa; as described in BACnet Clause 13.3.2.</description></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BBooleanCommandFailureAlgorithm"><description>BBooleanCommandFailureAlgorithm implements command&#xa; failure alarm detection algorithm for boolean&#xa; objects as described in BACnet.</description></class>
<class packageName="com.tridium.alarm" name="BConsoleRecipient"><description>Recipient for an alarm console.</description></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BEnumChangeOfStateAlgorithm"><description>BEnumChangeOfStateAlgorithm implements a change of state&#xa; alarm detection algorithm for multistate objects as described&#xa; in BACnet Clause 13.3.2.</description></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BEnumCommandFailureAlgorithm"><description>BEnumCommandFailureAlgorithm implements command failure&#xa; alarm detection algorithm for multistate objects as described&#xa; in BACnet.</description></class>
<class packageName="javax.baja.alarm.ext.fault" name="BEnumFaultAlgorithm"><description>BEnumFaultAlgorithm implements a change of state&#xa; fault detection algorithm for enum objects as described&#xa; in BACnet Clause 13.3.2.</description></class>
<class packageName="javax.baja.alarm.ext" name="BFaultAlgorithm"><description>BFaultAlgorithm is the super-class of all fault-detection&#xa; mechanisms defined by Niagara.</description></class>
<class packageName="com.tridium.alarm.db.file" name="BFileAlarmDatabase"><description>BFileAlarmDatabase is an alarm database implementation that stores&#xa; alarms in a file on the file system.</description></class>
<class packageName="com.tridium.alarm.db.file" name="BFileAlarmDbConfig"><description>BFileAlarmDbConfig is the configuration for the alarm database.</description></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BFloatingLimitAlgorithm"><description>BFloatingLimitAlgorithm implements the floating-limit event algorithm as described in BACnet.</description></class>
<class packageName="com.tridium.alarm.fox" name="BFoxAlarmArchive"><description>Fox implementation of the Alarm Archive</description></class>
<class packageName="com.tridium.alarm.fox" name="BFoxAlarmDatabase"><description>BFoxAlarmDatabase provides remote access to an alarm database via the&#xa; fox protocol.</description></class>
<class packageName="com.tridium.alarm.fox" name="BFoxAlarmResolver"><description>BFoxAlarmResolver resolves alarm queries for a fox session.</description></class>
<class packageName="javax.baja.alarm" name="BIAlarmClassFolder" category="interface"><description>BIAlarmClassFolder is a marker interface for Objects that are folders of &#xa; BAlarmClasses.</description></class>
<class packageName="javax.baja.alarm.ext" name="BIAlarmMessages" category="interface"></class>
<class packageName="com.tridium.alarm" name="BIAlarmRecordDecorator" category="interface"><description>THIS CLASS IS INTENDED FOR FRAMEWORK USE ONLY! ANY UNEXPECTED IMPLEMENTATIONS WILL BE REJECTED&#xa; WHEN ATTEMPTING TO REGISTER VIA &lt;code&gt;<see ref="com.tridium.alarm.BIAlarmRecordDecorator#registerAlarmRecordDecorator(com.tridium.alarm.BIAlarmRecordDecorator)">#registerAlarmRecordDecorator(BIAlarmRecordDecorator)</see>&lt;/code&gt;.</description></class>
<class packageName="com.tridium.alarm.db" name="BIAlarmResolver" category="interface"><description>BIAlarmResolver is an object that knows how to resolve an alarm ord.</description></class>
<class packageName="javax.baja.alarm" name="BIAlarmSource" category="interface"><description>The BIAlarmSource interface must be implemented by all&#xa; BObjects capable of generating alarms</description></class>
<class packageName="javax.baja.alarm" name="BIAlarmSpace" category="interface"><description>Common interface for all AlarmSpace implementations.</description></class>
<class packageName="com.tridium.alarm.fox" name="BIFoxAlarmDatabase" category="interface"><description>Interface for proxy BAlarmDatabases implementation over FOX</description></class>
<class packageName="javax.baja.alarm" name="BIRemoteAlarmRecipient" category="interface"><description>RemoteAlarmRecipient iis a marker interface for AlarmRecipients that are remote&#xa; to the station that generates the alarm.</description></class>
<class packageName="javax.baja.alarm" name="BIRemoteAlarmSource" category="interface"><description>The BIRemoteAlarmSource is a marker interface that is implemented by BObjects &#xa; that handle alarms sent between stations.</description></class>
<class packageName="com.tridium.alarm" name="BIUpdatableAlarmSource" category="interface"><description>The BIUpdatableAlarmSource interface must be implemented by all&#xa; BObjects capable of handling updated alarms</description></class>
<class packageName="com.tridium.alarm.user" name="BIUserAlarmRecipient" category="interface"><description>Interface for AlarmRecipients that can retransmit to a given destination from the information held in a BUser.</description></class>
<class packageName="javax.baja.alarm.ext" name="BLimitEnable"><description>BLimitEnable</description></class>
<class packageName="com.tridium.alarm.db" name="BLocalAlarmResolver"><description>Resolves alarm queries locally.</description></class>
<class packageName="javax.baja.alarm.ext" name="BNotifyType"><description>BNotifyType is an BEnum that represents valid Baja notification types</description></class>
<class packageName="javax.baja.alarm.ext" name="BOffnormalAlgorithm"><description>BOffnormalAlgorithm is a super-class for all algorithms&#xa; that check for off normal (not fault) conditions.</description></class>
<class packageName="com.tridium.alarm.db" name="BOpenAlarmTable"><description>BOpenAlarmTable presents the list of open alarms as a table.</description></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BOutOfRangeAlgorithm"><description>BOutOfRangeAlgorithm implements a standard out-of-range&#xa; alarming algorithm</description></class>
<class packageName="javax.baja.alarm.ext.fault" name="BOutOfRangeFaultAlgorithm"><description>BOutOfRangeFaultAlgorithm implements a standard out-of-range&#xa; alarming algorithm</description></class>
<class packageName="javax.baja.alarm" name="BRecoverableRecipient"><description>Recipient for sending remote alarms.</description></class>
<class packageName="javax.baja.alarm" name="BSourceState"><description>BSourceState represents the state of an alarm source.</description></class>
<class packageName="com.tridium.alarm" name="BStationRecipient"><description>Recipient for another station.</description></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BStatusAlgorithm"><description>BStatusAlgorithm allows alarming based on the ControlsPoint&#x27;s status value.</description></class>
<class packageName="javax.baja.alarm.ext.fault" name="BStatusFaultAlgorithm"><description>BStatusAlgorithm allows alarming based on the ControlsPoint&#x27;s status value.</description></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BStringChangeOfStateAlgorithm"><description>BStringChangeOfStateAlgorithm implements a change of&#xa; state alarm detection algorithm for text strings.</description></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BStringChangeOfStateFaultAlgorithm"><description>BStringChangeOfStateAlgorithm implements a change of&#xa; state alarm detection algorithm for text strings.</description></class>
<class packageName="com.tridium.alarm" name="BTextCustomizer"><description>Basic rules based text customization.</description></class>
<class packageName="com.tridium.alarm" name="BTextOp"></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BTwoStateAlgorithm"><description>BTwoStateAlgorithm implements a generic algorithm for&#xa; objects with only an normal / offnormal states (vs.</description></class>
<class packageName="javax.baja.alarm.ext.fault" name="BTwoStateFaultAlgorithm"><description>BTwoStateFaultAlgorithm implements a generic algorithm for&#xa; objects with only an normal / fault states (vs.</description></class>
</module>
</bajadoc>
