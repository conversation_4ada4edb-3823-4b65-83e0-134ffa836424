<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BAlarmArchive" name="BAlarmArchive" packageName="javax.baja.alarm" public="true" abstract="true">
<description>
An Alarm Archive is a archive of cleared alarms.  These alarms are typically removed from the&#xa; Alarm Database and stored for historical purposes.  Since they are not open, they do not appear&#xa; in the AlarmConsole and a viewable only via views on the Alarm Archive or via queries using&#xa; alarm:archive
</description>
<tag name="@since">Niagara 4.11</tag>
<extends>
<type class="javax.baja.alarm.BAlarmDatabase"/>
</extends>
<!-- javax.baja.alarm.BAlarmArchive() -->
<constructor name="BAlarmArchive" protected="true">
<description>
Default constructor
</description>
</constructor>

<!-- javax.baja.alarm.BAlarmArchive.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmArchive.getProvider() -->
<method name="getProvider"  public="true" abstract="true">
<description>
Return the associated ArchiveAlarmProvider.
</description>
<return>
<type class="javax.baja.alarm.BArchiveAlarmProvider"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmArchive.getHost() -->
<method name="getHost"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.naming.BHost"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmArchive.getSession() -->
<method name="getSession"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.naming.BISession"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmArchive.getOrdInSession() -->
<method name="getOrdInSession"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmArchive.getNavName() -->
<method name="getNavName"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmArchive.getNavIcon() -->
<method name="getNavIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmArchive.getNavChildren() -->
<method name="getNavChildren"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.nav.BINavNode" dimension="1"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmArchive.fw(int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object) -->
<method name="fw"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="x">
<type class="int"/>
</parameter>
<parameter name="a">
<type class="java.lang.Object"/>
</parameter>
<parameter name="b">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c">
<type class="java.lang.Object"/>
</parameter>
<parameter name="d">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="java.lang.Object"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmArchive.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
