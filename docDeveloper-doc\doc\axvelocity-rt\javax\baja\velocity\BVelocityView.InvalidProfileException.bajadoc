<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="axvelocity" runtimeProfile="rt" qualifiedName="javax.baja.velocity.BVelocityView$InvalidProfileException" name="BVelocityView.InvalidProfileException" packageName="javax.baja.velocity" public="true" static="true" final="true" innerClass="true" category="exception">
<description>
Exception thrown if an incorrect Web Profile is used with this view.
</description>
<extends>
<type class="java.lang.Exception"/>
</extends>
<!-- javax.baja.velocity.BVelocityView.InvalidProfileException(javax.baja.sys.Type) -->
<constructor name="InvalidProfileException" public="true">
<parameter name="type">
<type class="javax.baja.sys.Type"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.velocity.BVelocityView.InvalidProfileException(java.lang.String) -->
<constructor name="InvalidProfileException" public="true">
<parameter name="error">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

</class>
</bajadoc>
