<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBoundCsvProperty" name="BJsonSchemaBoundCsvProperty" packageName="com.tridiumx.jsonToolkit.outbound.schema.property" public="true">
<description>
A json property (key/value pair) whose value is bound to a selected station component/slot ord target.&#xa;&#xa; The json key is defined by this components name source rule (e.g display name of this / ord target display name etc)&#xa; The json value is a string consisting of comma separated list of the slot values within the target ord.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaBoundSlotsContainer"/>
</extends>
<implements>
<parameterizedType class="com.tridiumx.jsonToolkit.outbound.schema.BIJsonProperty">
<args>
<type class="javax.baja.sys.BString"/>
</args>
</parameterizedType>
</implements>
<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBoundCsvProperty() -->
<constructor name="BJsonSchemaBoundCsvProperty" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBoundCsvProperty.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBoundCsvProperty.make(javax.baja.naming.BOrd) -->
<method name="make"  public="true" static="true">
<description/>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBoundCsvProperty"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBoundCsvProperty.isChildLegal(javax.baja.sys.BComponent) -->
<method name="isChildLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="child">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBoundCsvProperty.getJsonValue() -->
<method name="getJsonValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BString"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaBoundCsvProperty.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
