<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetArray" name="BBacnetArray" packageName="javax.baja.bacnet.datatypes" public="true">
<description>
BBacnetArray represents a Bacnet Array, which contains an indexed&#xa; sequence of objects of a particular Bacnet data type.&#xa; &lt;p&gt;&#xa; The elements are named &#x22;element1&#x22; through &#x22;elementN&#x22; by default.
</description>
<tag name="@author">Craig <PERSON>emmill</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">08 May 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="arrayTypeSpec" flags="h">
<type class="javax.baja.util.BTypeSpec"/>
<description>
Slot for the &lt;code&gt;arrayTypeSpec&lt;/code&gt; property.
</description>
<tag name="@see">#getArrayTypeSpec</tag>
<tag name="@see">#setArrayTypeSpec</tag>
</property>

<property name="size" flags="h">
<type class="int"/>
<description>
Slot for the &lt;code&gt;size&lt;/code&gt; property.
</description>
<tag name="@see">#getSize</tag>
<tag name="@see">#setSize</tag>
</property>

<property name="fixedSize" flags="h">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;fixedSize&lt;/code&gt; property.&#xa; is the size fixed?
</description>
<tag name="@see">#getFixedSize</tag>
<tag name="@see">#setFixedSize</tag>
</property>

<action name="addElement" flags="h">
<parameter name="parameter">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;addElement&lt;/code&gt; action.
</description>
<tag name="@see">#addElement(BValue parameter)</tag>
</action>

<action name="removeElement" flags="h">
<parameter name="parameter">
<type class="javax.baja.sys.BInteger"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;removeElement&lt;/code&gt; action.
</description>
<tag name="@see">#removeElement(BInteger parameter)</tag>
</action>

<topic name="arrayPropertyChanged" flags="">
<eventType>
<type class="javax.baja.sys.BValue"/>
</eventType><description>
Slot for the &lt;code&gt;arrayPropertyChanged&lt;/code&gt; topic.
</description>
<tag name="@see">#fireArrayPropertyChanged</tag>
</topic>

<!-- javax.baja.bacnet.datatypes.BBacnetArray() -->
<constructor name="BBacnetArray" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetArray(javax.baja.sys.Type) -->
<constructor name="BBacnetArray" public="true">
<parameter name="arrayType">
<type class="javax.baja.sys.Type"/>
<description>
the type of elements to be contained by this array.
</description>
</parameter>
<description>
Constructor with type specification.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetArray(javax.baja.sys.Type, int) -->
<constructor name="BBacnetArray" public="true">
<parameter name="arrayType">
<type class="javax.baja.sys.Type"/>
<description>
the type of elements to be contained by this array.
</description>
</parameter>
<parameter name="fixedSize">
<type class="int"/>
<description>
the fixed size of this array.
</description>
</parameter>
<description>
Constructor with type specification and fixed size.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.getArrayTypeSpec() -->
<method name="getArrayTypeSpec"  public="true">
<description>
Get the &lt;code&gt;arrayTypeSpec&lt;/code&gt; property.
</description>
<tag name="@see">#arrayTypeSpec</tag>
<return>
<type class="javax.baja.util.BTypeSpec"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.setArrayTypeSpec(javax.baja.util.BTypeSpec) -->
<method name="setArrayTypeSpec"  public="true">
<description>
Set the &lt;code&gt;arrayTypeSpec&lt;/code&gt; property.
</description>
<tag name="@see">#arrayTypeSpec</tag>
<parameter name="v">
<type class="javax.baja.util.BTypeSpec"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.getSize() -->
<method name="getSize"  public="true">
<description>
Get the &lt;code&gt;size&lt;/code&gt; property.
</description>
<tag name="@see">#size</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.setSize(int) -->
<method name="setSize"  public="true">
<description>
Set the &lt;code&gt;size&lt;/code&gt; property.
</description>
<tag name="@see">#size</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.getFixedSize() -->
<method name="getFixedSize"  public="true">
<description>
Get the &lt;code&gt;fixedSize&lt;/code&gt; property.&#xa; is the size fixed?
</description>
<tag name="@see">#fixedSize</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.setFixedSize(boolean) -->
<method name="setFixedSize"  public="true">
<description>
Set the &lt;code&gt;fixedSize&lt;/code&gt; property.&#xa; is the size fixed?
</description>
<tag name="@see">#fixedSize</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.addElement(javax.baja.sys.BValue) -->
<method name="addElement"  public="true">
<description>
Invoke the &lt;code&gt;addElement&lt;/code&gt; action.
</description>
<tag name="@see">#addElement</tag>
<parameter name="parameter">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.removeElement(javax.baja.sys.BInteger) -->
<method name="removeElement"  public="true">
<description>
Invoke the &lt;code&gt;removeElement&lt;/code&gt; action.
</description>
<tag name="@see">#removeElement</tag>
<parameter name="parameter">
<type class="javax.baja.sys.BInteger"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.fireArrayPropertyChanged(javax.baja.sys.BValue) -->
<method name="fireArrayPropertyChanged"  public="true">
<description>
Fire an event for the &lt;code&gt;arrayPropertyChanged&lt;/code&gt; topic.
</description>
<tag name="@see">#arrayPropertyChanged</tag>
<parameter name="event">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.started() -->
<method name="started"  public="true" final="true">
<description>
Started.&#xa; Validate the size.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true" final="true">
<description>
Property changed.&#xa; We may need to write the new value to a remote device.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.subscribed() -->
<method name="subscribed"  public="true" final="true">
<description>
Callback when the component enters the subscribed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.unsubscribed() -->
<method name="unsubscribed"  public="true">
<description>
Callback when the component enters the subscribed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.isChildLegal(javax.baja.sys.BComponent) -->
<method name="isChildLegal"  public="true">
<description>
Only children of the specified array type are allowed.
</description>
<parameter name="child">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.getAppliedCategoryMask() -->
<method name="getAppliedCategoryMask"  public="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.getCategoryMask() -->
<method name="getCategoryMask"  public="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.getPermissions(javax.baja.sys.Context) -->
<method name="getPermissions"  public="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.security.BPermissions"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.doAddElement(javax.baja.sys.BValue) -->
<method name="doAddElement"  public="true" final="true">
<description>
Add an element to end of the array.
</description>
<parameter name="arrayElement">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.doRemoveElement(javax.baja.sys.BInteger) -->
<method name="doRemoveElement"  public="true" final="true">
<description>
Remove an element from the array.
</description>
<parameter name="index">
<type class="javax.baja.sys.BInteger"/>
<description>
the (zero-based) index of the element to be removed.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.getElement(int) -->
<method name="getElement"  public="true" final="true">
<description>
Get the element at this index.&#xa; If index is zero, return the array size.
</description>
<parameter name="index">
<type class="int"/>
<description>
the (1-N) array index of the object requested.
</description>
</parameter>
<return>
<type class="javax.baja.sys.BValue"/>
<description>
the object at this index, or null.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.setElement(int, javax.baja.sys.BValue) -->
<method name="setElement"  public="true" final="true">
<description>
Set the element at this index.
</description>
<parameter name="index">
<type class="int"/>
<description>
the array element to set; this starts with one, according to BACnet.
</description>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
<description>
the new array element.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.index(java.lang.String) -->
<method name="index"  public="true" static="true">
<description/>
<parameter name="propName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true" final="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true" final="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.arrayTypeSpec -->
<field name="arrayTypeSpec"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;arrayTypeSpec&lt;/code&gt; property.
</description>
<tag name="@see">#getArrayTypeSpec</tag>
<tag name="@see">#setArrayTypeSpec</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.size -->
<field name="size"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;size&lt;/code&gt; property.
</description>
<tag name="@see">#getSize</tag>
<tag name="@see">#setSize</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.fixedSize -->
<field name="fixedSize"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;fixedSize&lt;/code&gt; property.&#xa; is the size fixed?
</description>
<tag name="@see">#getFixedSize</tag>
<tag name="@see">#setFixedSize</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.addElement -->
<field name="addElement"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;addElement&lt;/code&gt; action.
</description>
<tag name="@see">#addElement(BValue parameter)</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.removeElement -->
<field name="removeElement"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;removeElement&lt;/code&gt; action.
</description>
<tag name="@see">#removeElement(BInteger parameter)</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.arrayPropertyChanged -->
<field name="arrayPropertyChanged"  public="true" static="true" final="true">
<type class="javax.baja.sys.Topic"/>
<description>
Slot for the &lt;code&gt;arrayPropertyChanged&lt;/code&gt; topic.
</description>
<tag name="@see">#fireArrayPropertyChanged</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetArray.ELEMENT_0 -->
<field name="ELEMENT_0"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

</class>
</bajadoc>
