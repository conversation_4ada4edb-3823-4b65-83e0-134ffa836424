<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.ui.BAlarmClassDef" name="BAlarmClassDef" packageName="com.tridium.alarm.ui" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@creation">26 Apr 04</tag>
<tag name="@version">$Revision: 2$ $Date: 4/4/07 4:28:53 PM EDT$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="defName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;defName&lt;/code&gt; property.
</description>
<tag name="@see">#getDefName</tag>
<tag name="@see">#setDefName</tag>
</property>

<property name="alarmIcon" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;alarmIcon&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmIcon</tag>
<tag name="@see">#setAlarmIcon</tag>
</property>

<property name="alarmUrl" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;alarmUrl&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmUrl</tag>
<tag name="@see">#setAlarmUrl</tag>
</property>

<property name="alarmSound" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;alarmSound&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmSound</tag>
<tag name="@see">#setAlarmSound</tag>
</property>

</class>
</bajadoc>
