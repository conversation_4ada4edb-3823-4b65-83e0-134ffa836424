<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="ace" runtimeProfile="rt" name="com.tridium.ace.sys">
<description/>
<class packageName="com.tridium.ace.sys" name="BAceApp"><description>BAceApp</description></class>
<class packageName="com.tridium.ace.sys" name="BAceAppFile"><description>BAceAppFile represents a JSON file containing a ACE application definition.</description></class>
<class packageName="com.tridium.ace.sys" name="BAceFolder"><description>BAceFolder</description></class>
<class packageName="com.tridium.ace.sys" name="BAceScheme"><description>BAceScheme is the portal from AceAppFile to AceSpace.</description></class>
<class packageName="com.tridium.ace.sys" name="BAceSpace"><description>BAceSpace is the component space for a ACE application file.</description></class>
<class packageName="com.tridium.ace.sys" name="BIAceSpace" category="interface"><description>BIAceSpace</description></class>
</package>
</bajadoc>
