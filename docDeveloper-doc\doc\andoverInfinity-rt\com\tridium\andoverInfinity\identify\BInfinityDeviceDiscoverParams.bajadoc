<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoverParams" name="BInfinityDeviceDiscoverParams" packageName="com.tridium.andoverInfinity.identify" public="true">
<description>
BInfinityDeviceDiscoverParams&#xa; &#xa; There are no params that a user needs to enter to do a learn, as it&#xa; is a well-defined keystroke sequence that never varies between systems.&#xa; Therefore, this class is pretty much a place holder that defines the&#xa; request class type and discovery leaf type
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 22, 2007</tag>
<tag name="@version">$Revision$ $May 22, 2007 9:28:04 AM$</tag>
<tag name="@since"/>
<extends>
<type class="com.tridium.ddf.identify.BDdfDiscoverParams"/>
</extends>
<property name="start" flags="h">
<type class="int"/>
<description>
Slot for the &lt;code&gt;start&lt;/code&gt; property.&#xa; dummy param to satisfy auto learn
</description>
<tag name="@see">#getStart</tag>
<tag name="@see">#setStart</tag>
</property>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoverParams() -->
<constructor name="BInfinityDeviceDiscoverParams" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoverParams.getStart() -->
<method name="getStart"  public="true">
<description>
Get the &lt;code&gt;start&lt;/code&gt; property.&#xa; dummy param to satisfy auto learn
</description>
<tag name="@see">#start</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoverParams.setStart(int) -->
<method name="setStart"  public="true">
<description>
Set the &lt;code&gt;start&lt;/code&gt; property.&#xa; dummy param to satisfy auto learn
</description>
<tag name="@see">#start</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoverParams.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoverParams.getDiscoverRequestType() -->
<method name="getDiscoverRequestType"  public="true">
<description>
DiscoverRequestType is &lt;code&gt;BInfinityDeviceDiscoverRequest.TYPE&lt;/code&gt;
</description>
<tag name="@see">com.tridium.ddf.identify.BIDdfDiscoverParams#getDiscoverRequestType()</tag>
<return>
<type class="javax.baja.sys.Type"/>
<description>
&lt;code&gt;BInfinityDeviceDiscoverRequest.TYPE&lt;/code&gt;
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoverParams.getDiscoveryLeafType() -->
<method name="getDiscoveryLeafType"  public="true">
<description>
Discovery leaf type is &lt;code&gt;BInfinityDeviceDiscoveryObject.TYPE&lt;/code&gt;
</description>
<tag name="@see">com.tridium.ddf.identify.BIDdfDiscoverParams#getDiscoveryLeafType()</tag>
<return>
<type class="javax.baja.sys.Type"/>
<description>
&lt;code&gt;BInfinityDeviceDiscoveryObject.TYPE&lt;/code&gt;
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoverParams.getFirst() -->
<method name="getFirst"  public="true">
<description>
We will attempt to discover all devices within the confines of&#xa;  a single request, which will most likely have multiple req/rsp&#xa;  sub parts (we need to hit F4, &#x22;V&#x22;iew , Infine&#x22;t&#x22; Controllers , and then&#xa;  possibly a PageDown.  Exact number of steps depends on how the screen&#xa;  buffer mode when we start the transcation, and how many devices there may &#xa;  be in the list.  Therefore, getFirst() really only needs to&#xa;  return a dummy instance - it is just used to kick off the discovery&#xa;  sequence.
</description>
<tag name="@see">com.tridium.ddf.identify.BIDdfDiscoverParams#getFirst()</tag>
<return>
<type class="com.tridium.ddf.identify.BIDdfDiscoverParams"/>
<description>
an instance of this class with the start property set to 1
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoverParams.getLast() -->
<method name="getLast"  public="true">
<description>
Since getFirst() does all of the work, and isAfter() returns false for&#xa; this driver, we will never have a situation where get getLast()&#xa; should be called.  Just return another new instance.
</description>
<tag name="@see">com.tridium.ddf.identify.BIDdfDiscoverParams#getLast()</tag>
<return>
<type class="com.tridium.ddf.identify.BIDdfDiscoverParams"/>
<description>
a dummy new instance
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoverParams.getNext() -->
<method name="getNext"  public="true">
<description>
Should be safe to create a new instance with the start property &#xa; incremented by 1.
</description>
<tag name="@see">com.tridium.ddf.identify.BIDdfDiscoverParams#getNext()</tag>
<return>
<type class="com.tridium.ddf.identify.BIDdfDiscoverParams"/>
<description>
a dummy new instance
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoverParams.isAfter(com.tridium.ddf.identify.BIDdfDiscoverParams) -->
<method name="isAfter"  public="true">
<description/>
<tag name="@see">com.tridium.ddf.identify.BIDdfDiscoverParams#isAfter(com.tridium.ddf.identify.BIDdfDiscoverParams)</tag>
<parameter name="anotherId">
<type class="com.tridium.ddf.identify.BIDdfDiscoverParams"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this intance&#x27;s start property is set larger than the arg&#x27;s start property
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoverParams.start -->
<field name="start"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;start&lt;/code&gt; property.&#xa; dummy param to satisfy auto learn
</description>
<tag name="@see">#getStart</tag>
<tag name="@see">#setStart</tag>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoverParams.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
