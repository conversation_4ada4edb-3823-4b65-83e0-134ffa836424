<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.license.BILicensed" name="BILicensed" packageName="javax.baja.license" public="true" interface="true" abstract="true" category="interface">
<description>
BILicensed is the interface used to represent object&#xa; that are licensed using the standard licensing&#xa; mechanism.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">11 Oct 11</tag>
<tag name="@version">$Revision: 4$ $Date: 3/28/05 9:23:10 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<!-- javax.baja.license.BILicensed.getLicenseFeature() -->
<method name="getLicenseFeature"  public="true" abstract="true">
<description>
Implement this method to return the Feature, or &#xa; return null for no license checks.  Convention is that the&#xa; vendor and feature name matches the declaring module.
</description>
<return>
<type class="javax.baja.license.Feature"/>
</return>
</method>

<!-- javax.baja.license.BILicensed.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
