<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="javax.baja.bacnet.datatypes.access">
<description/>
<class packageName="javax.baja.bacnet.datatypes.access" name="BBacnetAccessRule"><description>BBacnetAccessRule represents the BACnetAccessRule sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes.access" name="BBacnetAccessThreatLevel"><description>BBacnetAccessThreatLevel represents the BBacnetAccessThreatLevel&#xa; unsigned value where 0 is the lowest threat, and 100 represents&#xa; the highest (most dangerous) threat level.</description></class>
<class packageName="javax.baja.bacnet.datatypes.access" name="BBacnetAssignedAccessRights"><description>BBacnetAssignedAccessRights represents the BACnetAssignedAccessRights&#xa; sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes.access" name="BBacnetAuthenticationFactor"><description>BBacnetAssignedAccessRights represents the BACnetAssignedAccessRights&#xa; sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes.access" name="BBacnetAuthenticationFactorFormat"><description>BBacnetAuthenticationFactorFormat represents the BBacnetAuthenticationFactorFormat&#xa; sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes.access" name="BBacnetAuthenticationPolicy"><description>BBacnetAuthenticationPolicy represents the BBacnetAuthenticationPolicy&#xa; sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes.access" name="BBacnetAuthenticationPolicyEntry"><description>BBacnetAuthenticationPolicy represents the BBacnetAuthenticationPolicy&#xa; sequence.</description></class>
<class packageName="javax.baja.bacnet.datatypes.access" name="BBacnetCredentialAuthenticationFactor"><description>BBacnetAssignedAccessRights represents the BACnetAssignedAccessRights&#xa; sequence.</description></class>
</package>
</bajadoc>
