<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.point.BNumericActionPoint" name="BNumericActionPoint" packageName="com.tridium.ace.point" public="true">
<description/>
<tag name="@author"><PERSON> on 6/30/2017.</tag>
<extends>
<type class="com.tridium.ace.point.BActionPoint"/>
</extends>
<action name="invoke" flags="">
<parameter name="parameter">
<type class="javax.baja.sys.BDouble"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;invoke&lt;/code&gt; action.
</description>
<tag name="@see">#invoke(BDouble parameter)</tag>
</action>

</class>
</bajadoc>
