<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetMultiStateValuePrioritizedDescriptor" name="BBacnetMultiStateValuePrioritizedDescriptor" packageName="javax.baja.bacnet.export" public="true">
<description>
BBacnetMultiStateValuePrioritizedDescriptor exposes a ControlPoint as a non-commandable Bacnet&#xa; Multi State Value Object.
</description>
<tag name="@author"><PERSON> on 19 Feb 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.export.BBacnetMultiStateWritableDescriptor"/>
</extends>
<!-- javax.baja.bacnet.export.BBacnetMultiStateValuePrioritizedDescriptor() -->
<constructor name="BBacnetMultiStateValuePrioritizedDescriptor" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetMultiStateValuePrioritizedDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateValuePrioritizedDescriptor.getSlotFacets(javax.baja.sys.Slot) -->
<method name="getSlotFacets"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get slot facets.
</description>
<parameter name="s">
<type class="javax.baja.sys.Slot"/>
<description/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
<description>
the appropriate slot facets.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateValuePrioritizedDescriptor.getEventType() -->
<method name="getEventType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the BACnetEventType reported by this object.
</description>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateValuePrioritizedDescriptor.isValidAlarmExt(javax.baja.alarm.BIAlarmSource) -->
<method name="isValidAlarmExt"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is the given alarm source ext a valid extension for&#xa; exporting BACnet alarm properties?  This determines if the&#xa; given alarm source extension follows the appropriate algorithm&#xa; defined for the intrinsic alarming of a particular object&#xa; type as required by the BACnet specification.&lt;p&gt;&#xa; BACnet MultistateValue points use a ChangeOfState alarm algorithm.
</description>
<parameter name="ext">
<type class="javax.baja.alarm.BIAlarmSource"/>
<description/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if valid, otherwise false.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateValuePrioritizedDescriptor.addOptionalProps(java.util.Vector) -->
<method name="addOptionalProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Subclass override method to add optional properties.&#xa; NOTE: You MUST call super.addOptionalProps(v) first!
</description>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
Vector containing optional propertyIds.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateValuePrioritizedDescriptor.readOptionalProperty(int, int) -->
<method name="readOptionalProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Read the value of an optional property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateValuePrioritizedDescriptor.writeOptionalProperty(int, int, byte[], int) -->
<method name="writeOptionalProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the value of an optional property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateValuePrioritizedDescriptor.addListElements(javax.baja.bacnet.io.PropertyValue) -->
<method name="addListElements"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Add list elements.
</description>
<parameter name="propertyValue">
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
the PropertyValue containing the propertyId,&#xa;                      propertyArrayIndex, and the encoded list elements.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ChangeListError"/>
<description>
a ChangeListError if unable to add any elements,&#xa; or null if ok.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateValuePrioritizedDescriptor.removeListElements(javax.baja.bacnet.io.PropertyValue) -->
<method name="removeListElements"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Remove list elements.
</description>
<parameter name="propertyValue">
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
the PropertyValue containing the propertyId,&#xa;                      propertyArrayIndex, and the encoded list elements.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ChangeListError"/>
<description>
a ChangeListError if unable to remove any elements,&#xa; or null if ok.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStateValuePrioritizedDescriptor.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this point is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetMultiStateValuePrioritizedDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
