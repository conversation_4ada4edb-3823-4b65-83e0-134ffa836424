<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BAlarmPriorities" name="BAlarmPriorities" packageName="javax.baja.alarm" public="true" final="true">
<description>
BAlarmPriorities contains the priority mapping for each &#xa; Baja alarm transition type.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">23 Feb 01</tag>
<tag name="@version">$Revision: 19$ $Date: 9/30/08 5:08:59 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BSimple"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
<!-- javax.baja.alarm.BAlarmPriorities.make(int, int, int) -->
<method name="make"  public="true" static="true">
<description>
Create a new instance.
</description>
<parameter name="toOffnormal">
<type class="int"/>
<description>
The off normal priority.
</description>
</parameter>
<parameter name="toFault">
<type class="int"/>
<description>
The fault priority.
</description>
</parameter>
<parameter name="toNormal">
<type class="int"/>
<description>
The normal priority.
</description>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmPriorities"/>
<description>
The new instance.
</description>
</return>
</method>

<!-- javax.baja.alarm.BAlarmPriorities.make(int, int, int, int) -->
<method name="make"  public="true" static="true">
<description/>
<parameter name="toOffnormal">
<type class="int"/>
</parameter>
<parameter name="toFault">
<type class="int"/>
</parameter>
<parameter name="toNormal">
<type class="int"/>
</parameter>
<parameter name="toAlert">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmPriorities"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmPriorities.getPriority(javax.baja.alarm.BSourceState) -->
<method name="getPriority"  public="true">
<description>
Get the priority assigned to the given transition&#xa; type.
</description>
<parameter name="state">
<type class="javax.baja.alarm.BSourceState"/>
<description>
The alarm transition type.
</description>
</parameter>
<return>
<type class="int"/>
<description>
The priority.
</description>
</return>
</method>

<!-- javax.baja.alarm.BAlarmPriorities.getToOffnormal() -->
<method name="getToOffnormal"  public="true">
<description>
Get the priority assigned to the toOffnormal transition&#xa; type.
</description>
<return>
<type class="int"/>
<description>
The toOffnormal priority.
</description>
</return>
</method>

<!-- javax.baja.alarm.BAlarmPriorities.getToNormal() -->
<method name="getToNormal"  public="true">
<description>
Get the priority assigned to the toNormal transition&#xa; type.
</description>
<return>
<type class="int"/>
<description>
The toNormal priority.
</description>
</return>
</method>

<!-- javax.baja.alarm.BAlarmPriorities.getToFault() -->
<method name="getToFault"  public="true">
<description>
Get the priority assigned to the toFault transition&#xa; type.
</description>
<return>
<type class="int"/>
<description>
The toFault priority.
</description>
</return>
</method>

<!-- javax.baja.alarm.BAlarmPriorities.getToAlert() -->
<method name="getToAlert"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmPriorities.hashCode() -->
<method name="hashCode"  public="true">
<description>
BAlarmPriorities hash code.
</description>
<tag name="@since">Niagara 3.4</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmPriorities.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description>
Equality is based on priority equality.
</description>
<parameter name="obj">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmPriorities.encode(java.io.DataOutput) -->
<method name="encode"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Binary encoding.
</description>
<parameter name="out">
<type class="java.io.DataOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmPriorities.decode(java.io.DataInput) -->
<method name="decode"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Binary decoding.
</description>
<parameter name="in">
<type class="java.io.DataInput"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmPriorities.encodeToString() -->
<method name="encodeToString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Text format name-value; pairs
</description>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmPriorities.decodeFromString(java.lang.String) -->
<method name="decodeFromString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Parse from a string.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmPriorities.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmPriorities.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BAlarmPriorities"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmPriorities.MAX_PRIORITY -->
<field name="MAX_PRIORITY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmPriorities.MIN_PRIORITY -->
<field name="MIN_PRIORITY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmPriorities.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
