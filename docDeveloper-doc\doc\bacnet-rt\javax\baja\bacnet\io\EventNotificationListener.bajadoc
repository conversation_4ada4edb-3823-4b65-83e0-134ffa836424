<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.io.EventNotificationListener" name="EventNotificationListener" packageName="javax.baja.bacnet.io" public="true" interface="true" abstract="true" category="interface">
<description>
EventNotificationListener is the interface that classes must implement&#xa; to be notified when BACnet event notifications are received by the Niagara&#xa; BACnet comm stack.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">22 Aug 2007</tag>
<tag name="@since">Niagara 3.2</tag>
<implements>
<type class="javax.baja.bacnet.io.BacnetServiceListener"/>
</implements>
<!-- javax.baja.bacnet.io.EventNotificationListener.receiveConfirmedEventNotification(javax.baja.bacnet.datatypes.BBacnetAddress, long, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, javax.baja.bacnet.datatypes.BBacnetTimeStamp, long, int, javax.baja.sys.BEnum, java.lang.String, javax.baja.bacnet.enums.BBacnetNotifyType, boolean, javax.baja.sys.BEnum, javax.baja.sys.BEnum, byte[], javax.baja.bacnet.enums.BCharacterSetEncoding) -->
<method name="receiveConfirmedEventNotification"  public="true" abstract="true">
<description>
Receive a ConfirmedEventNotification request from the given address.&#xa; Note that the BACnet Transport layer acknowledgment of this request is&#xa; already being handled by the Niagara comm stack.  This is purely used&#xa; for notification to a subordinate application.
</description>
<parameter name="sourceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<parameter name="processId">
<type class="long"/>
</parameter>
<parameter name="initiatingDeviceId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="eventObjectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="timeStamp">
<type class="javax.baja.bacnet.datatypes.BBacnetTimeStamp"/>
</parameter>
<parameter name="notificationClass">
<type class="long"/>
</parameter>
<parameter name="priority">
<type class="int"/>
</parameter>
<parameter name="eventType">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<parameter name="messageText">
<type class="java.lang.String"/>
</parameter>
<parameter name="notifyType">
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
</parameter>
<parameter name="ackRequired">
<type class="boolean"/>
</parameter>
<parameter name="fromState">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<parameter name="toState">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<parameter name="eventValues">
<type class="byte" dimension="1"/>
</parameter>
<parameter name="encoding">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.EventNotificationListener.receiveUnconfirmedEventNotification(javax.baja.bacnet.datatypes.BBacnetAddress, long, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, javax.baja.bacnet.datatypes.BBacnetTimeStamp, long, int, javax.baja.sys.BEnum, java.lang.String, javax.baja.bacnet.enums.BBacnetNotifyType, boolean, javax.baja.sys.BEnum, javax.baja.sys.BEnum, byte[], javax.baja.bacnet.enums.BCharacterSetEncoding) -->
<method name="receiveUnconfirmedEventNotification"  public="true" abstract="true">
<description>
Receive an UnconfirmedEventNotification request from the given address.
</description>
<parameter name="sourceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<parameter name="processId">
<type class="long"/>
</parameter>
<parameter name="initiatingDeviceId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="eventObjectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="timeStamp">
<type class="javax.baja.bacnet.datatypes.BBacnetTimeStamp"/>
</parameter>
<parameter name="notificationClass">
<type class="long"/>
</parameter>
<parameter name="priority">
<type class="int"/>
</parameter>
<parameter name="eventType">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<parameter name="messageText">
<type class="java.lang.String"/>
</parameter>
<parameter name="notifyType">
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
</parameter>
<parameter name="ackRequired">
<type class="boolean"/>
</parameter>
<parameter name="fromState">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<parameter name="toState">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<parameter name="eventValues">
<type class="byte" dimension="1"/>
</parameter>
<parameter name="encoding">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
