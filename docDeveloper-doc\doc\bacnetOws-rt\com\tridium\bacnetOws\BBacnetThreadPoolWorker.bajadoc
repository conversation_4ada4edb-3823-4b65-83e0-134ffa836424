<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetOws" runtimeProfile="rt" qualifiedName="com.tridium.bacnetOws.BBacnetThreadPoolWorker" name="BBacnetThreadPoolWorker" packageName="com.tridium.bacnetOws" public="true">
<description/>
<extends>
<type class="javax.baja.bacnet.util.BBacnetWorker"/>
</extends>
<property name="maxThreads" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;maxThreads&lt;/code&gt; property.&#xa; Max number of concurrent threads for working.
</description>
<tag name="@see">#getMaxThreads</tag>
<tag name="@see">#setMaxThreads</tag>
</property>

</class>
</bajadoc>
