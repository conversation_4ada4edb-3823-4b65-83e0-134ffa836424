<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.schedule.BBacnetScheduleImportExt" name="BBacnetScheduleImportExt" packageName="com.tridium.bacnet.schedule" public="true">
<description>
BBacnetScheduleImportExt is a child extension of a schedule that&#xa; is being imported from a BACnet device.&#xa; &lt;p&gt;&#xa; The schedule in the remote BACnet device is the &#x22;master&#x22;, and Niagara&#xa; will periodically synchronize its local copy by reading the appropriate&#xa; values.
</description>
<tag name="@author"><PERSON> on 03 Mar 2004</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.driver.schedule.BScheduleImportExt"/>
</extends>
<property name="objectId" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; object ID of the schedule.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="priorityForWriting" flags="r">
<type class="int"/>
<description>
Slot for the &lt;code&gt;priorityForWriting&lt;/code&gt; property.
</description>
<tag name="@see">#getPriorityForWriting</tag>
<tag name="@see">#setPriorityForWriting</tag>
</property>

</class>
</bajadoc>
