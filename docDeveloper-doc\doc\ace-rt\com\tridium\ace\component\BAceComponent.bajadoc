<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.component.BAceComponent" name="BAceComponent" packageName="com.tridium.ace.component" public="true" abstract="true">
<description>
BAceComponent
</description>
<tag name="@author"><PERSON> on 2/25/2016</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="com.tridium.ace.message.AceConst"/>
</implements>
<implements>
<type class="javax.baja.util.ICoalesceable"/>
</implements>
<property name="objectId" flags="drs">
<type class="int"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; Unique identifier for object in instance of control engine
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="meta" flags="r">
<type class="int"/>
<description>
Slot for the &lt;code&gt;meta&lt;/code&gt; property.&#xa; Metadata common to all components is packed into 32-bits:&#xa; 00-03: nibble for security groups&#xa; 08-15: byte logical x coordinate on wiresheet&#xa; 16-23: byte logical y coordinate on wiresheet&#xa; 24-28: byte logical glyph width on wiresheet
</description>
<tag name="@see">#getMeta</tag>
<tag name="@see">#setMeta</tag>
</property>

<property name="exeParam" flags="dsr">
<type class="com.tridium.ace.datatypes.BExeParam"/>
<description>
Slot for the &lt;code&gt;exeParam&lt;/code&gt; property.
</description>
<tag name="@see">#getExeParam</tag>
<tag name="@see">#setExeParam</tag>
</property>

<property name="loaded" flags="th">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;loaded&lt;/code&gt; property.
</description>
<tag name="@see">#getLoaded</tag>
<tag name="@see">#setLoaded</tag>
</property>

<action name="forceRead" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;forceRead&lt;/code&gt; action.
</description>
<tag name="@see">#forceRead()</tag>
</action>

<action name="viewChanged" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;viewChanged&lt;/code&gt; action.
</description>
<tag name="@see">#viewChanged()</tag>
</action>

</class>
</bajadoc>
