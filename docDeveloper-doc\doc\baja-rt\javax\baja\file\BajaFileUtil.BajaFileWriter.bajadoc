<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BajaFileUtil$BajaFileWriter" name="BajaFileUtil.BajaFileWriter" packageName="javax.baja.file" public="true" interface="true" abstract="true" static="true" innerClass="true" category="interface">
<description>
Public interface for file system operations. You can use this in&#xa; production as well as mock it for testing purposes.
</description>
<tag name="@since">Niagara 4.8</tag>
<!-- javax.baja.file.BajaFileUtil.BajaFileWriter.createFile(javax.baja.naming.BOrd, java.io.InputStream, javax.baja.sys.Context) -->
<method name="createFile"  public="true" abstract="true">
<description>
Create a new file.
</description>
<parameter name="fileOrd">
<type class="javax.baja.naming.BOrd"/>
<description>
ORD to the file to be created
</description>
</parameter>
<parameter name="fileContents">
<type class="java.io.InputStream"/>
<description>
stream of the file contents. Will be consumed, but&#xa;                     up to the caller to close.
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
security context
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
<description>
if the file could not be written
</description>
</throws>
</method>

<!-- javax.baja.file.BajaFileUtil.BajaFileWriter.replaceFile(javax.baja.naming.BOrd, java.io.InputStream, javax.baja.sys.Context) -->
<method name="replaceFile"  public="true" abstract="true">
<description>
Create or replace an existing file.
</description>
<parameter name="fileOrd">
<type class="javax.baja.naming.BOrd"/>
<description>
ORD to the file to be replaced
</description>
</parameter>
<parameter name="fileContents">
<type class="java.io.InputStream"/>
<description>
stream of the file contents. Will be consumed, but&#xa;                     up to the caller to close.
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
security context
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
<description>
if the file could not be written
</description>
</throws>
</method>

<!-- javax.baja.file.BajaFileUtil.BajaFileWriter.exists(javax.baja.naming.BOrd, javax.baja.sys.Context) -->
<method name="exists"  public="true" abstract="true">
<description/>
<parameter name="fileOrd">
<type class="javax.baja.naming.BOrd"/>
<description>
ORD to the file to be checked
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
security context
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the file exists
</description>
</return>
</method>

<!-- javax.baja.file.BajaFileUtil.BajaFileWriter.isWritable(javax.baja.naming.BOrd, javax.baja.sys.Context) -->
<method name="isWritable"  public="true" abstract="true">
<description/>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
<description>
ORD to the file to be checked
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
security context
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the file can be written to
</description>
</return>
</method>

</class>
</bajadoc>
