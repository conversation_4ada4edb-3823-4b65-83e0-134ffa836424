<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnetAws" runtimeProfile="wb" name="com.tridium.bacnetAws.ui.device">
<description/>
<class packageName="com.tridium.bacnetAws.ui.device" name="BBacnetAwsDeviceManager"><description>BBacnetWsDeviceManager is the specialization of BBacnetWsDeviceManager to&#xa; handle Workstation-specific behavior.</description></class>
</package>
</bajadoc>
