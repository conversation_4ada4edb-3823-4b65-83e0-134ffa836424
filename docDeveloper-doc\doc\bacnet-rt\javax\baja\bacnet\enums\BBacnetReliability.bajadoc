<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetReliability" name="BBacnetReliability" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetReliability represents the BACnetReliability&#xa; enumeration.&#xa; &lt;p&gt;&#xa; BBacnetReliability is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0xFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Craig Gemmill</tag>
<tag name="@version">$Revision: 4$ $Date: 11/28/01 6:14:22 AM$</tag>
<tag name="@creation">07 Aug 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;noFaultDetected&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;noSensor&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;overRange&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;underRange&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;openLoop&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;shortedLoop&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;noOutput&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unreliableOther&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;processError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;multiStateFault&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;configurationError&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;communicationFailure&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>12</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;memberFault&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>13</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;monitoredObjectFault&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>14</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tripped&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>15</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetReliability.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetReliability.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetReliability.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetReliability.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetReliability.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetReliability.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetReliability.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetReliability.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetReliability.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetReliability.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
String representation of this BEnum.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetReliability.NO_FAULT_DETECTED -->
<field name="NO_FAULT_DETECTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for noFaultDetected.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.NO_SENSOR -->
<field name="NO_SENSOR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for noSensor.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.OVER_RANGE -->
<field name="OVER_RANGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for overRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.UNDER_RANGE -->
<field name="UNDER_RANGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for underRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.OPEN_LOOP -->
<field name="OPEN_LOOP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for openLoop.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.SHORTED_LOOP -->
<field name="SHORTED_LOOP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for shortedLoop.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.NO_OUTPUT -->
<field name="NO_OUTPUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for noOutput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.UNRELIABLE_OTHER -->
<field name="UNRELIABLE_OTHER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unreliableOther.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.PROCESS_ERROR -->
<field name="PROCESS_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for processError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.MULTI_STATE_FAULT -->
<field name="MULTI_STATE_FAULT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for multiStateFault.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.CONFIGURATION_ERROR -->
<field name="CONFIGURATION_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for configurationError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.COMMUNICATION_FAILURE -->
<field name="COMMUNICATION_FAILURE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for communicationFailure.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.MEMBER_FAULT -->
<field name="MEMBER_FAULT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for memberFault.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.MONITORED_OBJECT_FAULT -->
<field name="MONITORED_OBJECT_FAULT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for monitoredObjectFault.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.TRIPPED -->
<field name="TRIPPED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tripped.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.noFaultDetected -->
<field name="noFaultDetected"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description>
BBacnetReliability constant for noFaultDetected.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.noSensor -->
<field name="noSensor"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description>
BBacnetReliability constant for noSensor.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.overRange -->
<field name="overRange"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description>
BBacnetReliability constant for overRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.underRange -->
<field name="underRange"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description>
BBacnetReliability constant for underRange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.openLoop -->
<field name="openLoop"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description>
BBacnetReliability constant for openLoop.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.shortedLoop -->
<field name="shortedLoop"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description>
BBacnetReliability constant for shortedLoop.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.noOutput -->
<field name="noOutput"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description>
BBacnetReliability constant for noOutput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.unreliableOther -->
<field name="unreliableOther"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description>
BBacnetReliability constant for unreliableOther.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.processError -->
<field name="processError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description>
BBacnetReliability constant for processError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.multiStateFault -->
<field name="multiStateFault"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description>
BBacnetReliability constant for multiStateFault.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.configurationError -->
<field name="configurationError"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description>
BBacnetReliability constant for configurationError.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.communicationFailure -->
<field name="communicationFailure"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description>
BBacnetReliability constant for communicationFailure.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.memberFault -->
<field name="memberFault"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description>
BBacnetReliability constant for memberFault.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.monitoredObjectFault -->
<field name="monitoredObjectFault"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description>
BBacnetReliability constant for monitoredObjectFault.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.tripped -->
<field name="tripped"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description>
BBacnetReliability constant for tripped.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetReliability"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetReliability.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
