<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.access.BBacnetAccessThreatLevel" name="BBacnetAccessThreatLevel" packageName="javax.baja.bacnet.datatypes.access" public="true" final="true">
<description>
BBacnetAccessThreatLevel represents the BBacnetAccessThreatLevel&#xa; unsigned value where 0 is the lowest threat, and 100 represents&#xa; the highest (most dangerous) threat level.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="accessThreatLevel" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;accessThreatLevel&lt;/code&gt; property.
</description>
<tag name="@see">#getAccessThreatLevel</tag>
<tag name="@see">#setAccessThreatLevel</tag>
</property>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessThreatLevel() -->
<constructor name="BBacnetAccessThreatLevel" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessThreatLevel(int) -->
<constructor name="BBacnetAccessThreatLevel" public="true">
<parameter name="accessThreatLevel">
<type class="int"/>
</parameter>
<description>
Standard constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessThreatLevel.getAccessThreatLevel() -->
<method name="getAccessThreatLevel"  public="true">
<description>
Get the &lt;code&gt;accessThreatLevel&lt;/code&gt; property.
</description>
<tag name="@see">#accessThreatLevel</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessThreatLevel.setAccessThreatLevel(int) -->
<method name="setAccessThreatLevel"  public="true">
<description>
Set the &lt;code&gt;accessThreatLevel&lt;/code&gt; property.
</description>
<tag name="@see">#accessThreatLevel</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessThreatLevel.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessThreatLevel.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessThreatLevel.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessThreatLevel.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessThreatLevel.accessThreatLevel -->
<field name="accessThreatLevel"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;accessThreatLevel&lt;/code&gt; property.
</description>
<tag name="@see">#getAccessThreatLevel</tag>
<tag name="@see">#setAccessThreatLevel</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetAccessThreatLevel.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
