<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetAnalogValueDescriptor" name="BBacnetAnalogValueDescriptor" packageName="javax.baja.bacnet.export" public="true">
<description>
BBacnetAnalogValueDescriptor exposes a ControlPoint as a Bacnet&#xa; Analog Value Object.
</description>
<tag name="@author"><PERSON> on 11 Aug 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.export.BBacnetAnalogPointDescriptor"/>
</extends>
<!-- javax.baja.bacnet.export.BBacnetAnalogValueDescriptor() -->
<constructor name="BBacnetAnalogValueDescriptor" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetAnalogValueDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogValueDescriptor.getBacnetValue() -->
<method name="getBacnetValue"  public="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description/>
<tag name="@deprecated">BacnetValue is no longer necessary since out-of-service&#xa; changes will be written directly to the point via&#xa; the BOutOfServiceExt.</tag>
<return>
<type class="javax.baja.status.BStatusNumeric"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogValueDescriptor.setBacnetValue(javax.baja.status.BStatusNumeric) -->
<method name="setBacnetValue"  public="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description/>
<tag name="@deprecated">BacnetValue is no longer necessary since out-of-service&#xa; changes will be written directly to the point via&#xa; the BOutOfServiceExt.</tag>
<parameter name="v">
<type class="javax.baja.status.BStatusNumeric"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogValueDescriptor.getSlotFacets(javax.baja.sys.Slot) -->
<method name="getSlotFacets"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get slot facets.
</description>
<parameter name="s">
<type class="javax.baja.sys.Slot"/>
<description/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
<description>
the appropriate slot facets.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogValueDescriptor.isPointTypeLegal(javax.baja.control.BControlPoint) -->
<method name="isPointTypeLegal"  protected="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
BBacnetAnalogInputDescriptor may only expose BNumericPoint.
</description>
<parameter name="pt">
<type class="javax.baja.control.BControlPoint"/>
<description>
the exposed point
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the Niagara point type is legal for this point type.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogValueDescriptor.writeProperty(int, int, byte[], int) -->
<method name="writeProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogValueDescriptor.writeOptionalProperty(int, int, byte[], int) -->
<method name="writeOptionalProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the value of an optional property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetAnalogValueDescriptor.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this point is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetAnalogValueDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
