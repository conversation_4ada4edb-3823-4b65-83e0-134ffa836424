<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.outbound.schema.property">
<description/>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.property" name="BJsonSchemaBooleanProperty"><description>A fixed boolean key / value json pair.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.property" name="BJsonSchemaBoundCsvProperty"><description>A json property (key/value pair) whose value is bound to a selected station component/slot ord target.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.property" name="BJsonSchemaBoundProperty"><description>A json property (key/value pair) whose value is bound to a selected station component/slot ord target.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.property" name="BJsonSchemaCountProperty"><description>A json key value pair where the value is an incrementing integer.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.property" name="BJsonSchemaCurrentTimeProperty"><description>BJsonSchemaCurrentTimeProperty&#xa;&#xa; For fixed time use a bound property.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.property" name="BJsonSchemaFacetList"><description>A list of name/value properties based upon selected facets found upon a binding target.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.property" name="BJsonSchemaFacetProperty"><description>BJsonSchemaTagProperty allows a single Facet value from a bound component&#xa; to be inserted in the Schema output</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.property" name="BJsonSchemaMetadataProperty"><description>A JSON property which uses a selected metadata field as the source&#xa; for the json value (instead of a slot value as per other bindings).</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.property" name="BJsonSchemaNumericProperty"><description>A fixed numeric key / value json pair.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.property" name="BJsonSchemaProperty"><description>Base class for fixed key / value json pair.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.property" name="BJsonSchemaPropertyList"><description>A list property name value pairs.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.property" name="BJsonSchemaStringProperty"><description>A fixed string key / value json pair.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.property" name="BJsonSchemaTagList"><description>A list of name/value properties based upon selected tags found upon a binding target.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.property" name="BJsonSchemaTagProperty"><description>Allows a single tag value from the bound component to be inserted in the Schema&#xa; output.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.property" name="BJsonSchemaUnixTimeProperty"><description>Outputs the current unix timestamp as a long numeric</description></class>
</package>
</bajadoc>
