<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.job.BPupDownloadRegionJob" name="BPupDownloadRegionJob" packageName="com.tridium.aapup.job" public="true">
<description>
PUP Download SPL&#xa; This job class handles the implementation of download&#xa; of SPL programs to a given region in a controller&#xa; &lt;p&gt;
</description>
<tag name="@author">C<PERSON><PERSON></tag>
<tag name="@creation">8/15/2005 2:21PM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.93</tag>
<extends>
<type class="javax.baja.job.BSimpleJob"/>
</extends>
<implements>
<type class="com.tridium.aapup.AaPupConst"/>
</implements>
</class>
</bajadoc>
