<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.BSlotScheme" name="BSlotScheme" packageName="javax.baja.naming" public="true">
<description>
BSlotScheme is used to navigate the slot tree.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Nov 02</tag>
<tag name="@version">$Revision: 20$ $Date: 9/8/09 1:59:07 PM EDT$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="javax.baja.naming.BOrdScheme"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraSingleton"/>
</annotation>
<!-- javax.baja.naming.BSlotScheme(java.lang.String) -->
<constructor name="BSlotScheme" protected="true">
<parameter name="id">
<type class="java.lang.String"/>
</parameter>
<description>
Create a BSlotScheme with the specified ID.
</description>
<tag name="@since">Niagara 3.2</tag>
</constructor>

<!-- javax.baja.naming.BSlotScheme.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.naming.BSlotScheme.parse(java.lang.String) -->
<method name="parse"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return an instance of SlotPath.
</description>
<parameter name="queryBody">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdQuery"/>
</return>
</method>

<!-- javax.baja.naming.BSlotScheme.resolve(javax.baja.naming.OrdTarget, javax.baja.naming.OrdQuery) -->
<method name="resolve"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Resolve the SlotPath query to a OrdTarget.
</description>
<parameter name="base">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<parameter name="query">
<type class="javax.baja.naming.OrdQuery"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdTarget"/>
</return>
<throws>
<type class="javax.baja.naming.SyntaxException"/>
</throws>
<throws>
<type class="javax.baja.naming.UnresolvedException"/>
</throws>
</method>

<!-- javax.baja.naming.BSlotScheme.INSTANCE -->
<field name="INSTANCE"  public="true" static="true" final="true">
<type class="javax.baja.naming.BSlotScheme"/>
<description/>
</field>

<!-- javax.baja.naming.BSlotScheme.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
