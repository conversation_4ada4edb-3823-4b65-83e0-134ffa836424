<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetLoopDescriptor" name="BBacnetLoopDescriptor" packageName="javax.baja.bacnet.export" public="true">
<description>
BBacnetLoopDescriptor is the extension that allows a kitControl&#xa; BLoopPoint to be exposed to Bacnet.
</description>
<tag name="@author"><PERSON> on 31 Jul 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.export.BBacnetEventSource"/>
</extends>
<implements>
<type class="javax.baja.bacnet.export.BIBacnetCovSource"/>
</implements>
<implements>
<type class="javax.baja.bacnet.export.BacnetPropertyListProvider"/>
</implements>
<property name="pointOrd" flags="d">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;pointOrd&lt;/code&gt; property.&#xa; the ord to the exposed Control Point.
</description>
<tag name="@see">#getPointOrd</tag>
<tag name="@see">#setPointOrd</tag>
</property>

<property name="objectId" flags="d">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this point is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="objectName" flags="d">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;objectName&lt;/code&gt; property.&#xa; the name by which this object is known to the Bacnet world.
</description>
<tag name="@see">#getObjectName</tag>
<tag name="@see">#setObjectName</tag>
</property>

<property name="reliability" flags="tr">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;reliability&lt;/code&gt; property.&#xa; indicates misconfiguration
</description>
<tag name="@see">#getReliability</tag>
<tag name="@see">#setReliability</tag>
</property>

<property name="description" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#getDescription</tag>
<tag name="@see">#setDescription</tag>
</property>

<property name="notifyType" flags="">
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
<description>
Slot for the &lt;code&gt;notifyType&lt;/code&gt; property.
</description>
<tag name="@see">#getNotifyType</tag>
<tag name="@see">#setNotifyType</tag>
</property>

<property name="covIncrement" flags="">
<type class="float"/>
<description>
Slot for the &lt;code&gt;covIncrement&lt;/code&gt; property.
</description>
<tag name="@see">#getCovIncrement</tag>
<tag name="@see">#setCovIncrement</tag>
</property>

<property name="updateInterval" flags="r">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;updateInterval&lt;/code&gt; property.&#xa; This property indicates the maximum period of time between&#xa; updates to the Present_Value in hundredths of a second when&#xa; the input is not overridden and not out-of-service.
</description>
<tag name="@see">#getUpdateInterval</tag>
<tag name="@see">#setUpdateInterval</tag>
</property>

<action name="addCovSubscription" flags="h">
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetCovSubscription"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;addCovSubscription&lt;/code&gt; action.&#xa; add a COV subscription for a client device.
</description>
<tag name="@see">#addCovSubscription(BBacnetCovSubscription parameter)</tag>
</action>

<action name="removeCovSubscription" flags="h">
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetCovSubscription"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;removeCovSubscription&lt;/code&gt; action.&#xa; remove a COV subscription for a client device.
</description>
<tag name="@see">#removeCovSubscription(BBacnetCovSubscription parameter)</tag>
</action>

<action name="sendCovNotification" flags="h">
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetCovSubscription"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;sendCovNotification&lt;/code&gt; action.
</description>
<tag name="@see">#sendCovNotification(BBacnetCovSubscription parameter)</tag>
</action>

<action name="checkCov" flags="ha">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;checkCov&lt;/code&gt; action.
</description>
<tag name="@see">#checkCov()</tag>
</action>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor() -->
<constructor name="BBacnetLoopDescriptor" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getPointOrd() -->
<method name="getPointOrd"  public="true">
<description>
Get the &lt;code&gt;pointOrd&lt;/code&gt; property.&#xa; the ord to the exposed Control Point.
</description>
<tag name="@see">#pointOrd</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.setPointOrd(javax.baja.naming.BOrd) -->
<method name="setPointOrd"  public="true">
<description>
Set the &lt;code&gt;pointOrd&lt;/code&gt; property.&#xa; the ord to the exposed Control Point.
</description>
<tag name="@see">#pointOrd</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getObjectId() -->
<method name="getObjectId"  public="true">
<description>
Get the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this point is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#objectId</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.setObjectId(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="setObjectId"  public="true">
<description>
Set the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this point is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#objectId</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getObjectName() -->
<method name="getObjectName"  public="true">
<description>
Get the &lt;code&gt;objectName&lt;/code&gt; property.&#xa; the name by which this object is known to the Bacnet world.
</description>
<tag name="@see">#objectName</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.setObjectName(java.lang.String) -->
<method name="setObjectName"  public="true">
<description>
Set the &lt;code&gt;objectName&lt;/code&gt; property.&#xa; the name by which this object is known to the Bacnet world.
</description>
<tag name="@see">#objectName</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getReliability() -->
<method name="getReliability"  public="true">
<description>
Get the &lt;code&gt;reliability&lt;/code&gt; property.&#xa; indicates misconfiguration
</description>
<tag name="@see">#reliability</tag>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.setReliability(javax.baja.sys.BEnum) -->
<method name="setReliability"  public="true">
<description>
Set the &lt;code&gt;reliability&lt;/code&gt; property.&#xa; indicates misconfiguration
</description>
<tag name="@see">#reliability</tag>
<parameter name="v">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getDescription() -->
<method name="getDescription"  public="true">
<description>
Get the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#description</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.setDescription(java.lang.String) -->
<method name="setDescription"  public="true">
<description>
Set the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#description</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getNotifyType() -->
<method name="getNotifyType"  public="true">
<description>
Get the &lt;code&gt;notifyType&lt;/code&gt; property.
</description>
<tag name="@see">#notifyType</tag>
<return>
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.setNotifyType(javax.baja.bacnet.enums.BBacnetNotifyType) -->
<method name="setNotifyType"  public="true">
<description>
Set the &lt;code&gt;notifyType&lt;/code&gt; property.
</description>
<tag name="@see">#notifyType</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getCovIncrement() -->
<method name="getCovIncrement"  public="true">
<description>
Get the &lt;code&gt;covIncrement&lt;/code&gt; property.
</description>
<tag name="@see">#covIncrement</tag>
<return>
<type class="float"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.setCovIncrement(float) -->
<method name="setCovIncrement"  public="true">
<description>
Set the &lt;code&gt;covIncrement&lt;/code&gt; property.
</description>
<tag name="@see">#covIncrement</tag>
<parameter name="v">
<type class="float"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getUpdateInterval() -->
<method name="getUpdateInterval"  public="true">
<description>
Get the &lt;code&gt;updateInterval&lt;/code&gt; property.&#xa; This property indicates the maximum period of time between&#xa; updates to the Present_Value in hundredths of a second when&#xa; the input is not overridden and not out-of-service.
</description>
<tag name="@see">#updateInterval</tag>
<return>
<type class="javax.baja.sys.BRelTime"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.setUpdateInterval(javax.baja.sys.BRelTime) -->
<method name="setUpdateInterval"  public="true">
<description>
Set the &lt;code&gt;updateInterval&lt;/code&gt; property.&#xa; This property indicates the maximum period of time between&#xa; updates to the Present_Value in hundredths of a second when&#xa; the input is not overridden and not out-of-service.
</description>
<tag name="@see">#updateInterval</tag>
<parameter name="v">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.addCovSubscription(javax.baja.bacnet.datatypes.BBacnetCovSubscription) -->
<method name="addCovSubscription"  public="true">
<description>
Invoke the &lt;code&gt;addCovSubscription&lt;/code&gt; action.&#xa; add a COV subscription for a client device.
</description>
<tag name="@see">#addCovSubscription</tag>
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetCovSubscription"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.removeCovSubscription(javax.baja.bacnet.datatypes.BBacnetCovSubscription) -->
<method name="removeCovSubscription"  public="true">
<description>
Invoke the &lt;code&gt;removeCovSubscription&lt;/code&gt; action.&#xa; remove a COV subscription for a client device.
</description>
<tag name="@see">#removeCovSubscription</tag>
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetCovSubscription"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.sendCovNotification(javax.baja.bacnet.datatypes.BBacnetCovSubscription) -->
<method name="sendCovNotification"  public="true">
<description>
Invoke the &lt;code&gt;sendCovNotification&lt;/code&gt; action.
</description>
<tag name="@see">#sendCovNotification</tag>
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetCovSubscription"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.checkCov() -->
<method name="checkCov"  public="true">
<description>
Invoke the &lt;code&gt;checkCov&lt;/code&gt; action.
</description>
<tag name="@see">#checkCov</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.started() -->
<method name="started"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Started.&#xa; Initialize the point name subscriber and check the export configuration.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.stopped() -->
<method name="stopped"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Stopped.&#xa; Clean up the point name subscriber and null references.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.added(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="added"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Added.&#xa; Cov subscriptions will generate a new Cov notification on add.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.removed(javax.baja.sys.Property, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="removed"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Removed.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="oldValue">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Changed.&#xa; If the objectId changes, make sure the new ID is not already in use.&#xa; If it is, reset it to the current value.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.doAddCovSubscription(javax.baja.bacnet.datatypes.BBacnetCovSubscription) -->
<method name="doAddCovSubscription"  public="true" final="true">
<description/>
<parameter name="sub">
<type class="javax.baja.bacnet.datatypes.BBacnetCovSubscription"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.doRemoveCovSubscription(javax.baja.bacnet.datatypes.BBacnetCovSubscription) -->
<method name="doRemoveCovSubscription"  public="true" final="true">
<description/>
<parameter name="sub">
<type class="javax.baja.bacnet.datatypes.BBacnetCovSubscription"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.doSendCovNotification(javax.baja.bacnet.datatypes.BBacnetCovSubscription) -->
<method name="doSendCovNotification"  public="true">
<description>
Send a Cov notification.
</description>
<parameter name="covSub">
<type class="javax.baja.bacnet.datatypes.BBacnetCovSubscription"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.doCheckCov() -->
<method name="doCheckCov"  public="true" final="true">
<description>
Check Cov subscriptions to see if any require a notification.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getObject() -->
<method name="getObject"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the exported object.
</description>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getObjectOrd() -->
<method name="getObjectOrd"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the BOrd to the exported object.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.setObjectOrd(javax.baja.naming.BOrd, javax.baja.sys.Context) -->
<method name="setObjectOrd"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the BOrd to the exported object.
</description>
<parameter name="objectOrd">
<type class="javax.baja.naming.BOrd"/>
<description/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.checkConfiguration() -->
<method name="checkConfiguration"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Check the configuration of this object.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.isValidAlarmExt(javax.baja.alarm.BIAlarmSource) -->
<method name="isValidAlarmExt"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is the given alarm source ext a valid extension for&#xa; exporting BACnet alarm properties?  This determines if the&#xa; given alarm source extension follows the appropriate algorithm&#xa; defined for the intrinsic alarming of a particular object&#xa; type as required by the BACnet specification.
</description>
<parameter name="ext">
<type class="javax.baja.alarm.BIAlarmSource"/>
<description/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if valid, otherwise false.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.updateAlarmInhibit() -->
<method name="updateAlarmInhibit"  protected="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;deprecation&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.isEventInitiationEnabled() -->
<method name="isEventInitiationEnabled"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is this object currently configured to support event initiation?&#xa; This will return false if the exported object does not have an&#xa; appropriate alarm extension configured to allow Bacnet event initiation.
</description>
<return>
<type class="boolean"/>
<description>
true if this object can initiate Bacnet events.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getEventState() -->
<method name="getEventState"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the current Event_State of the object.&#xa; If the exported object also has an alarm extension, this&#xa; returns the current event state as translated from the&#xa; alarm extension&#x27;s alarm state.  Otherwise, it returns null.
</description>
<return>
<type class="javax.baja.sys.BEnum"/>
<description>
the object&#x27;s event state if configured for alarming, or null.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getAckedTransitions() -->
<method name="getAckedTransitions"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the current Acknowledged_Transitions property of the object.&#xa; If the exported object also has an alarm extension, this&#xa; returns the current acked transitions as translated from the&#xa; alarm extension&#x27;s alarm transitions.  Otherwise, it returns null.
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
the object&#x27;s acknowledged transitions if configured for alarming, or null.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getEventTimeStamps() -->
<method name="getEventTimeStamps"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the event time stamps.
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetTimeStamp" dimension="1"/>
<description>
the event time stamps, or null if event initiation is not enabled.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getEventEnable() -->
<method name="getEventEnable"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the event enable bits.
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
the event enable bits, or null if event initiation is not enabled.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getEventPriorities() -->
<method name="getEventPriorities"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the event priorities.
</description>
<return>
<type class="int" dimension="1"/>
<description>
the event priorities, or null if event initiation is not enabled.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getNotificationClass() -->
<method name="getNotificationClass"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the Notification Class object for this event source.
</description>
<return>
<type class="javax.baja.bacnet.export.BBacnetNotificationClassDescriptor"/>
<description>
the &lt;code&gt;BacnetNotificationClassDescriptor&lt;/code&gt; for this object.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getEventType() -->
<method name="getEventType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the BACnetEventType reported by this object.
</description>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getExport() -->
<method name="getExport"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the export descriptor for this cov source.  Usually this.
</description>
<return>
<type class="javax.baja.bacnet.export.BIBacnetExportObject"/>
<description>
the relevant export descriptor.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.findCovSubscription(javax.baja.bacnet.datatypes.BBacnetAddress, long, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="findCovSubscription"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Attempt to locate a COV subscription for the given subscriber information&#xa; on this object.
</description>
<parameter name="subscriberAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description/>
</parameter>
<parameter name="processId">
<type class="long"/>
<description/>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetCovSubscription"/>
<description>
the subscription if found, or null.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.findCovPropertySubscription(javax.baja.bacnet.datatypes.BBacnetAddress, long, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int) -->
<method name="findCovPropertySubscription"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Attempt to locate a COVProperty subscription for the given subscriber information&#xa; on this object.
</description>
<parameter name="subscriberAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description/>
</parameter>
<parameter name="processId">
<type class="long"/>
<description/>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<parameter name="propertyId">
<type class="int"/>
<description/>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
<description/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetCovSubscription"/>
<description>
the subscription if found, or null.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.startCovTimer(javax.baja.bacnet.datatypes.BBacnetCovSubscription, long) -->
<method name="startCovTimer"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Start or restart a timer for the given COV subscription.
</description>
<parameter name="covSub">
<type class="javax.baja.bacnet.datatypes.BBacnetCovSubscription"/>
<description>
the subscription for which to start the timer.
</description>
</parameter>
<parameter name="lifetime">
<type class="long"/>
<description>
the lifetime, in seconds, of the subscription.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getOutProperty() -->
<method name="getOutProperty"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the output property mapped as Present_Value for this export.
</description>
<return>
<type class="javax.baja.sys.Property"/>
<description>
the property used for Present_Value in COV notifications.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.supportsSubscribeCov() -->
<method name="supportsSubscribeCov"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Does this COV source support SubscribeCOV in addition to SubscribeCOVProperty?&#xa; This is true for input, output, value, and loop objects.
</description>
<return>
<type class="boolean"/>
<description>
true if Subscribe-COV can be used with this object.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getCurrentCovValue(javax.baja.bacnet.datatypes.BBacnetCovSubscription) -->
<method name="getCurrentCovValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="sub">
<type class="javax.baja.bacnet.datatypes.BBacnetCovSubscription"/>
</parameter>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.readProperty(javax.baja.bacnet.io.PropertyReference) -->
<method name="readProperty"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the value of a property.
</description>
<parameter name="ref">
<type class="javax.baja.bacnet.io.PropertyReference"/>
<description>
the PropertyReference containing id and index.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.RejectException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.readPropertyMultiple(javax.baja.bacnet.io.PropertyReference[]) -->
<method name="readPropertyMultiple"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Read the value of multiple Bacnet properties.
</description>
<parameter name="refs">
<type class="javax.baja.bacnet.io.PropertyReference" dimension="1"/>
<description>
the list of property references.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue" dimension="1"/>
<description>
an array of PropertyValues.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.RejectException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.readRange(javax.baja.bacnet.io.RangeReference) -->
<method name="readRange"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Read the specified range of values of a compound property.
</description>
<parameter name="rangeReference">
<type class="javax.baja.bacnet.io.RangeReference"/>
<description>
the range reference describing the requested range.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.RangeData"/>
<description>
a byte array containing the encoded range.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.RejectException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.writeProperty(javax.baja.bacnet.io.PropertyValue) -->
<method name="writeProperty"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the value of a property.
</description>
<parameter name="val">
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
the PropertyValue containing the write information.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.addListElements(javax.baja.bacnet.io.PropertyValue) -->
<method name="addListElements"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Add list elements.
</description>
<parameter name="propertyValue">
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
the PropertyValue containing the propertyId,&#xa;                      propertyArrayIndex, and the encoded list elements.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ChangeListError"/>
<description>
a ChangeListError if unable to add any elements,&#xa; or null if ok.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.removeListElements(javax.baja.bacnet.io.PropertyValue) -->
<method name="removeListElements"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Remove list elements.
</description>
<parameter name="propertyValue">
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
the PropertyValue containing the propertyId,&#xa;                      propertyArrayIndex, and the encoded list elements.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ChangeListError"/>
<description>
a ChangeListError if unable to remove any elements,&#xa; or null if ok.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.statusChanged() -->
<method name="statusChanged"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getPropertyList() -->
<method name="getPropertyList"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.readProperty(int, int) -->
<method name="readProperty"  protected="true">
<description>
Get the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.readOptionalProperty(int, int) -->
<method name="readOptionalProperty"  protected="true">
<description>
Read the value of an optional property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.writeProperty(int, int, byte[], int) -->
<method name="writeProperty"  protected="true">
<description>
Set the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.writeOptionalProperty(int, int, byte[], int) -->
<method name="writeOptionalProperty"  protected="true">
<description>
Set the value of an optional property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
<description/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
To String.
</description>
<parameter name="c">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getPoint() -->
<method name="getPoint"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Find the exposed control point.
</description>
<return>
<type class="javax.baja.control.BNumericPoint"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.UPDATE_INTERVAL -->
<field name="UPDATE_INTERVAL"  public="true" static="true" final="true">
<type class="long"/>
<description/>
</field>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.pointOrd -->
<field name="pointOrd"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;pointOrd&lt;/code&gt; property.&#xa; the ord to the exposed Control Point.
</description>
<tag name="@see">#getPointOrd</tag>
<tag name="@see">#setPointOrd</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this point is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.objectName -->
<field name="objectName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectName&lt;/code&gt; property.&#xa; the name by which this object is known to the Bacnet world.
</description>
<tag name="@see">#getObjectName</tag>
<tag name="@see">#setObjectName</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.reliability -->
<field name="reliability"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;reliability&lt;/code&gt; property.&#xa; indicates misconfiguration
</description>
<tag name="@see">#getReliability</tag>
<tag name="@see">#setReliability</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.description -->
<field name="description"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;description&lt;/code&gt; property.
</description>
<tag name="@see">#getDescription</tag>
<tag name="@see">#setDescription</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.notifyType -->
<field name="notifyType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;notifyType&lt;/code&gt; property.
</description>
<tag name="@see">#getNotifyType</tag>
<tag name="@see">#setNotifyType</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.covIncrement -->
<field name="covIncrement"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;covIncrement&lt;/code&gt; property.
</description>
<tag name="@see">#getCovIncrement</tag>
<tag name="@see">#setCovIncrement</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.updateInterval -->
<field name="updateInterval"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;updateInterval&lt;/code&gt; property.&#xa; This property indicates the maximum period of time between&#xa; updates to the Present_Value in hundredths of a second when&#xa; the input is not overridden and not out-of-service.
</description>
<tag name="@see">#getUpdateInterval</tag>
<tag name="@see">#setUpdateInterval</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.addCovSubscription -->
<field name="addCovSubscription"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;addCovSubscription&lt;/code&gt; action.&#xa; add a COV subscription for a client device.
</description>
<tag name="@see">#addCovSubscription(BBacnetCovSubscription parameter)</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.removeCovSubscription -->
<field name="removeCovSubscription"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;removeCovSubscription&lt;/code&gt; action.&#xa; remove a COV subscription for a client device.
</description>
<tag name="@see">#removeCovSubscription(BBacnetCovSubscription parameter)</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.sendCovNotification -->
<field name="sendCovNotification"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;sendCovNotification&lt;/code&gt; action.
</description>
<tag name="@see">#sendCovNotification(BBacnetCovSubscription parameter)</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.checkCov -->
<field name="checkCov"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;checkCov&lt;/code&gt; action.
</description>
<tag name="@see">#checkCov()</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetLoopDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
