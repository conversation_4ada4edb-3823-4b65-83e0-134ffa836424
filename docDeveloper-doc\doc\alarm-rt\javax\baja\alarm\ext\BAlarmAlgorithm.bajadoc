<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.ext.BAlarmAlgorithm" name="BAlarmAlgorithm" packageName="javax.baja.alarm.ext" public="true" abstract="true">
<description>
BAlarmAlgorithm is the base class for all alarm&#xa; algorithms designed to alarm algorithms for &#xa; BAlarmSourceExt.
</description>
<tag name="@author"><PERSON> Saunders</tag>
<tag name="@creation">13 Dec 03</tag>
<tag name="@version">$Revision: 6$ $Date: 4/23/08 11:54:50 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<!-- javax.baja.alarm.ext.BAlarmAlgorithm() -->
<constructor name="BAlarmAlgorithm" public="true">
<description/>
</constructor>

<!-- javax.baja.alarm.ext.BAlarmAlgorithm.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmAlgorithm.getParentExt() -->
<method name="getParentExt"  public="true" final="true">
<description>
Get this parent as a BAlarmSourceExt.
</description>
<return>
<type class="javax.baja.alarm.ext.BAlarmSourceExt"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmAlgorithm.getParentPoint() -->
<method name="getParentPoint"  public="true" final="true">
<description>
Get this extension&#x27;s parent point, or null if&#xa; the parent is not an instance of BControlPoint.
</description>
<return>
<type class="javax.baja.control.BControlPoint"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmAlgorithm.executePoint() -->
<method name="executePoint"  public="true">
<description>
This causes execute to be called on the parent&#xa; point if getParentPoint() is not null.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmAlgorithm.getPointFacets() -->
<method name="getPointFacets"  public="true" final="true">
<description>
Get this extension&#x27;s parent point&#x27;s facets, or BFacets.NULL&#xa; if the parent point  is not an instance of BControlPoint.
</description>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmAlgorithm.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
A BAlarmAlgorithm&#x27;s parent must be a BAlarmSourceExt
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmAlgorithm.isGrandparentLegal(javax.baja.sys.BComponent) -->
<method name="isGrandparentLegal"  public="true">
<description/>
<parameter name="grandparent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmAlgorithm.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
