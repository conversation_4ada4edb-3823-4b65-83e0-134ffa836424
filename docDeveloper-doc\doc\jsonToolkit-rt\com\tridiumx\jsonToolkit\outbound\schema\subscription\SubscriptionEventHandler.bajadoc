<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionEventHandler" name="SubscriptionEventHandler" packageName="com.tridiumx.jsonToolkit.outbound.schema.subscription" public="true" interface="true" abstract="true" category="interface">
<description>
Contract for handling subscription events in a schema.
</description>
<tag name="@author"><PERSON></tag>
<annotation><type class="java.lang.FunctionalInterface"/>
</annotation>
<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionEventHandler.handle(javax.baja.sys.BComponentEvent) -->
<method name="handle"  public="true" abstract="true">
<description/>
<parameter name="event">
<type class="javax.baja.sys.BComponentEvent"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
