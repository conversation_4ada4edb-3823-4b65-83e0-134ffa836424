<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.selector.BJsonSumSelector" name="BJsonSumSelector" packageName="com.tridiumx.jsonToolkit.inbound.selector" public="true">
<description/>
<extends>
<type class="com.tridiumx.jsonToolkit.inbound.selector.BJsonNumericSelector"/>
</extends>
<property name="key" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;key&lt;/code&gt; property.
</description>
<tag name="@see">#getKey</tag>
<tag name="@see">#setKey</tag>
</property>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonSumSelector() -->
<constructor name="BJsonSumSelector" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonSumSelector.getKey() -->
<method name="getKey"  public="true">
<description>
Get the &lt;code&gt;key&lt;/code&gt; property.
</description>
<tag name="@see">#key</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonSumSelector.setKey(java.lang.String) -->
<method name="setKey"  public="true">
<description>
Set the &lt;code&gt;key&lt;/code&gt; property.
</description>
<tag name="@see">#key</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonSumSelector.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonSumSelector.routeValue(javax.baja.sys.BString, javax.baja.sys.Context) -->
<method name="routeValue"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="message">
<type class="javax.baja.sys.BString"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.inbound.routing.RoutingFailedException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonSumSelector.getRerunTriggers() -->
<method name="getRerunTriggers"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Property" dimension="1"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonSumSelector.key -->
<field name="key"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;key&lt;/code&gt; property.
</description>
<tag name="@see">#getKey</tag>
<tag name="@see">#setKey</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonSumSelector.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
