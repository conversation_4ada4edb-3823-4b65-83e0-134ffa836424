<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.BasicQuery" name="BasicQuery" packageName="javax.baja.naming" public="true">
<description>
BasicQuery is a simple implementation of OrdQuery &#xa; that stores the scheme and body as fields.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">3 Jan 03</tag>
<tag name="@version">$Revision: 8$ $Date: 3/28/05 9:22:59 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="javax.baja.naming.OrdQuery"/>
</implements>
<!-- javax.baja.naming.BasicQuery(java.lang.String, java.lang.String) -->
<constructor name="BasicQuery" public="true">
<parameter name="scheme">
<type class="java.lang.String"/>
</parameter>
<parameter name="body">
<type class="java.lang.String"/>
</parameter>
<description>
Construct an BasicQuery with the specified scheme and body.
</description>
</constructor>

<!-- javax.baja.naming.BasicQuery.getScheme() -->
<method name="getScheme"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return the scheme field.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.BasicQuery.getBody() -->
<method name="getBody"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return the body field.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.BasicQuery.isHost() -->
<method name="isHost"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return false.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.BasicQuery.isSession() -->
<method name="isSession"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return false.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.BasicQuery.normalize(javax.baja.naming.OrdQueryList, int) -->
<method name="normalize"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Do nothing.
</description>
<parameter name="list">
<type class="javax.baja.naming.OrdQueryList"/>
</parameter>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.BasicQuery.toString() -->
<method name="toString"  public="true">
<description>
Return &lt;code&gt;scheme + &amp;#x22;:&amp;#x22; + body&lt;/code&gt;.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.BasicQuery.scheme -->
<field name="scheme"  protected="true">
<type class="java.lang.String"/>
<description>
This is the scheme id of the query
</description>
</field>

<!-- javax.baja.naming.BasicQuery.body -->
<field name="body"  protected="true">
<type class="java.lang.String"/>
<description>
This is the body of the query
</description>
</field>

</class>
</bajadoc>
