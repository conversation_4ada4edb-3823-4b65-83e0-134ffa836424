<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.history.BBacnetEnumTrendLogExt" name="BBacnetEnumTrendLogExt" packageName="com.tridium.bacnet.history" public="true">
<description>
Generic trend log extension for Enum which takes care of both&#xa; change of value and interval based logging.&#xa; Niagara version: 4.3&#xa; &lt;p&gt;&#xa; Created by <PERSON><PERSON><PERSON> on 11/06/2017.
</description>
<extends>
<type class="com.tridium.bacnet.history.BBacnetEnumIntervalTrendLogExt"/>
</extends>
</class>
</bajadoc>
