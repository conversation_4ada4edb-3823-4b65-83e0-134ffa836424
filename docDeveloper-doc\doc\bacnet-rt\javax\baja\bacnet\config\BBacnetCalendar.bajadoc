<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BBacnetCalendar" name="BBacnetCalendar" packageName="javax.baja.bacnet.config" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">15 Nov 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.config.BBacnetCreatableObject"/>
</extends>
<property name="presentValue" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</property>

<property name="datelist" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
<description>
Slot for the &lt;code&gt;datelist&lt;/code&gt; property.
</description>
<tag name="@see">#getDatelist</tag>
<tag name="@see">#setDatelist</tag>
</property>

<!-- javax.baja.bacnet.config.BBacnetCalendar() -->
<constructor name="BBacnetCalendar" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetCalendar.getPresentValue() -->
<method name="getPresentValue"  public="true">
<description>
Get the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#presentValue</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCalendar.setPresentValue(boolean) -->
<method name="setPresentValue"  public="true">
<description>
Set the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#presentValue</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCalendar.getDatelist() -->
<method name="getDatelist"  public="true">
<description>
Get the &lt;code&gt;datelist&lt;/code&gt; property.
</description>
<tag name="@see">#datelist</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCalendar.setDatelist(javax.baja.bacnet.datatypes.BBacnetListOf) -->
<method name="setDatelist"  public="true">
<description>
Set the &lt;code&gt;datelist&lt;/code&gt; property.
</description>
<tag name="@see">#datelist</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCalendar.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCalendar.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCalendar.addObjectInitialValues(javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addObjectInitialValues"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Property changed.&#xa;   public void changed(Property p, Context cx)&#xa;   {&#xa;   super.changed(p,cx);&#xa;   if (!isRunning()) return;&#xa;   }
</description>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCalendar.getPresentValueProperty() -->
<method name="getPresentValueProperty"  public="true">
<description>
Subclasses that have a present value property should&#xa; override this method and return this property.  The&#xa; default returns null.
</description>
<return>
<type class="javax.baja.sys.Property"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetCalendar.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetCalendar.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetCalendar.presentValue -->
<field name="presentValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetCalendar.datelist -->
<field name="datelist"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;datelist&lt;/code&gt; property.
</description>
<tag name="@see">#getDatelist</tag>
<tag name="@see">#setDatelist</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetCalendar.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
