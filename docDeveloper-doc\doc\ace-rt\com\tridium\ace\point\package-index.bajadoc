<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="ace" runtimeProfile="rt" name="com.tridium.ace.point">
<description/>
<class packageName="com.tridium.ace.point" name="BAcePointDeviceExt"><description>BAcePointDeviceExt is a container for ace proxy points.</description></class>
<class packageName="com.tridium.ace.point" name="BAcePointDiscoveryLeaf"><description>BAcePointDiscoveryLeaf is container class for point elements to display in&#xa; point discovery pane and pass to new point callback.</description></class>
<class packageName="com.tridium.ace.point" name="BAcePointDiscoveryPreferences"/>
<class packageName="com.tridium.ace.point" name="BAcePointFolder"><description>BAcePointFolder</description></class>
<class packageName="com.tridium.ace.point" name="BAceProxyExt"><description>BAceAbstractProxyExt</description></class>
<class packageName="com.tridium.ace.point" name="BActionPoint"><description>Implements an action point, which is an integration of a single&#xa; action from a proxy, similar to what BControlPoint does for a&#xa; single value.</description></class>
<class packageName="com.tridium.ace.point" name="BBooleanActionPoint"/>
<class packageName="com.tridium.ace.point" name="BNumericActionPoint"/>
<class packageName="com.tridium.ace.point" name="BVoidActionPoint"><description>Implementation of an action point for an action with void data type.</description></class>
</package>
</bajadoc>
