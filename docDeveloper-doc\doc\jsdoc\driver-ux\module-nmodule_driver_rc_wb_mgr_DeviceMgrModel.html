<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>driver Module: nmodule/driver/rc/wb/mgr/DeviceMgrModel</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">driver</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-nmodule_driver_rc_wb_mgr_DeviceMgr.html">nmodule/driver/rc/wb/mgr/DeviceMgr</a></li><li><a href="module-nmodule_driver_rc_wb_mgr_DeviceMgrModel.html">nmodule/driver/rc/wb/mgr/DeviceMgrModel</a></li><li><a href="module-nmodule_driver_rc_wb_mgr_DriverMgr.html">nmodule/driver/rc/wb/mgr/DriverMgr</a></li><li><a href="module-nmodule_driver_rc_wb_mgr_PointMgr.html">nmodule/driver/rc/wb/mgr/PointMgr</a></li><li><a href="module-nmodule_driver_rc_wb_mgr_PointMgrModel.html">nmodule/driver/rc/wb/mgr/PointMgrModel</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: nmodule/driver/rc/wb/mgr/DeviceMgrModel</h1>
<section>

<header>
    
        
            
        
    
</header>


<article>
    <div class="container-overview">
    
        

        
            
<hr>
<dt>
    <h4 class="name" id="module:nmodule/driver/rc/wb/mgr/DeviceMgrModel"><span class="type-signature"></span>new (require("nmodule/driver/rc/wb/mgr/DeviceMgrModel"))(params)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>API Status: <strong>Development</strong></p>
<p>A <code>MgrModel</code> type for a <code>DeviceMgr</code> derived type as an agent on a driver's<br>
BDeviceNetwork or BDeviceFolder concrete type.</p>
    </div>
    

    
        <h5>Extends:</h5>
        


    <ul>
        <li>module:nmodule/webEditors/rc/wb/mgr/model/MgrModel</li>
    </ul>


    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>object containing the constructor parameters</p>
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>component</code></td>
            

            <td class="type">
            
                
<span class="param-type">baja.Component</span>



            
            </td>

            
                <td class="attributes">
                

                

                
                </td>
            

            

            <td class="description last"><p>the component containing the devices to<br>
be shown in the manager, typically a device network or device folder.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>folderType</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>
|

<span class="param-type">Type</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>optional parameter indicating the folder<br>
type for the manager.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id="getNavDisplayName"><span class="type-signature"></span>getNavDisplayName()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the display name from the display name of the root component container.<br>
This is used for the title of the tab in the HTML5 hx profile.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="makeRow"><span class="type-signature"></span>makeRow(subject)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Make a row for the given subject with the appropriate icon for the row. Overrides<br>
TableModel.makeRow().</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>subject</code></td>
            

            <td class="type">
            
            </td>

            

            

            <td class="description last"><p>The subject of the row. Should be a device or folder instance.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">module:nmodule/webEditors/rc/wb/table/model/Row</span>



    </dd>
</dl>


        

    
</dd>

        </dl>
    

    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	driver Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:55+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>