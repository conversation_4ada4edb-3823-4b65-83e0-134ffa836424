<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.dataRecovery.DataRecoveryInvalidKeyException" name="DataRecoveryInvalidKeyException" packageName="javax.baja.dataRecovery" public="true" category="exception">
<description>
DataRecoveryInvalidKeyException is thrown whenever the key provided by&#xa; the source was invalid (NULL, error while encoding)
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">7 July 09</tag>
<tag name="@version">Original</tag>
<tag name="@since">Niagara 3.6</tag>
<extends>
<type class="javax.baja.dataRecovery.DataRecoveryException"/>
</extends>
<!-- javax.baja.dataRecovery.DataRecoveryInvalidKeyException() -->
<constructor name="DataRecoveryInvalidKeyException" public="true">
<description/>
</constructor>

<!-- javax.baja.dataRecovery.DataRecoveryInvalidKeyException(java.lang.String) -->
<constructor name="DataRecoveryInvalidKeyException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

</class>
</bajadoc>
