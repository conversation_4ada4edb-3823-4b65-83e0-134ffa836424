<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="wb" qualifiedName="com.tridium.bacnet.ui.fe.BBacnetNotifyTypeFE" name="BBacnetNotifyTypeFE" packageName="com.tridium.bacnet.ui.fe" public="true">
<description>
BBacnetNotifyTypeFE allows editing of BBacnetNotifyType.&#xa; This FE does not allow selection of the ackNotification&#xa; enumeration choice, as this is not a typically allowed choice.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">27 Apr 2006</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.1</tag>
<extends>
<type class="javax.baja.workbench.fieldeditor.BWbFieldEditor"/>
</extends>
</class>
</bajadoc>
