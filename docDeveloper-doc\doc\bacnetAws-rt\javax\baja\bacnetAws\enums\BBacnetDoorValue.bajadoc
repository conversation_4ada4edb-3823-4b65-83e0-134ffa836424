<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="javax.baja.bacnetAws.enums.BBacnetDoorValue" name="BBacnetDoorValue" packageName="javax.baja.bacnetAws.enums" public="true" final="true">
<description/>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lock&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unlock&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;pulseUnlock&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;extendedPulseUnlock&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnetAws.enums.BBacnetDoorValue.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnetAws.enums.BBacnetDoorValue"/>
</return>
</method>

<!-- javax.baja.bacnetAws.enums.BBacnetDoorValue.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnetAws.enums.BBacnetDoorValue"/>
</return>
</method>

<!-- javax.baja.bacnetAws.enums.BBacnetDoorValue.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnetAws.enums.BBacnetDoorValue.LOCK -->
<field name="LOCK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lock.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BBacnetDoorValue.UNLOCK -->
<field name="UNLOCK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unlock.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BBacnetDoorValue.PULSE_UNLOCK -->
<field name="PULSE_UNLOCK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for pulseUnlock.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BBacnetDoorValue.EXTENDED_PULSE_UNLOCK -->
<field name="EXTENDED_PULSE_UNLOCK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for extendedPulseUnlock.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BBacnetDoorValue.lock -->
<field name="lock"  public="true" static="true" final="true">
<type class="javax.baja.bacnetAws.enums.BBacnetDoorValue"/>
<description>
BBacnetDoorValue constant for lock.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BBacnetDoorValue.unlock -->
<field name="unlock"  public="true" static="true" final="true">
<type class="javax.baja.bacnetAws.enums.BBacnetDoorValue"/>
<description>
BBacnetDoorValue constant for unlock.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BBacnetDoorValue.pulseUnlock -->
<field name="pulseUnlock"  public="true" static="true" final="true">
<type class="javax.baja.bacnetAws.enums.BBacnetDoorValue"/>
<description>
BBacnetDoorValue constant for pulseUnlock.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BBacnetDoorValue.extendedPulseUnlock -->
<field name="extendedPulseUnlock"  public="true" static="true" final="true">
<type class="javax.baja.bacnetAws.enums.BBacnetDoorValue"/>
<description>
BBacnetDoorValue constant for extendedPulseUnlock.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BBacnetDoorValue.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnetAws.enums.BBacnetDoorValue"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.enums.BBacnetDoorValue.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
