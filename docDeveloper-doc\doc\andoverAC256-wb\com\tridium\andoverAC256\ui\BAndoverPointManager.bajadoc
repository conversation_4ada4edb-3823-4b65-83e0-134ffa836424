<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="wb" qualifiedName="com.tridium.andoverAC256.ui.BAndoverPointManager" name="BAndoverPointManager" packageName="com.tridium.andoverAC256.ui" public="true">
<description>
BAndoverPointManager uses the BAbstractLearn framework to&#xa; provide a way for the user to create proxy points within&#xa; a BAndoverDevice.
</description>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@creation">10/22/2004 2:58PM</tag>
<tag name="@version">$Revision$ $Date: 10/22/2004 2:58PM$</tag>
<tag name="@since">Niagara 3 AndoverAC256 1.0</tag>
<extends>
<type class="javax.baja.driver.ui.point.BPointManager"/>
</extends>
</class>
</bajadoc>
