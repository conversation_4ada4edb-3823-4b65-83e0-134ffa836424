<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.Cov" name="Cov" packageName="javax.baja.bacnet.export" public="true">
<description>
BacnetCovSubscriber handles sending a Cov notification to any&#xa; Cov subscribers when the point&#x27;s value changes.
</description>
<tag name="@author"><PERSON> on 10 Sep 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="java.lang.Runnable"/>
</implements>
<implements>
<type class="javax.baja.util.ICoalesceable"/>
</implements>
<!-- javax.baja.bacnet.export.Cov(javax.baja.bacnet.datatypes.BBacnetCovSubscription, javax.baja.bacnet.export.BIBacnetCovSource, javax.baja.control.BControlPoint) -->
<constructor name="Cov" public="true">
<parameter name="sub">
<type class="javax.baja.bacnet.datatypes.BBacnetCovSubscription"/>
</parameter>
<parameter name="object">
<type class="javax.baja.bacnet.export.BIBacnetCovSource"/>
</parameter>
<parameter name="pt">
<type class="javax.baja.control.BControlPoint"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.export.Cov.toString() -->
<method name="toString"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.Cov.getCoalesceKey() -->
<method name="getCoalesceKey"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.Object"/>
</return>
</method>

<!-- javax.baja.bacnet.export.Cov.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description/>
<parameter name="o">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.export.Cov.hashCode() -->
<method name="hashCode"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.export.Cov.coalesce(javax.baja.util.ICoalesceable) -->
<method name="coalesce"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="c">
<type class="javax.baja.util.ICoalesceable"/>
</parameter>
<return>
<type class="javax.baja.util.ICoalesceable"/>
</return>
</method>

<!-- javax.baja.bacnet.export.Cov.run() -->
<method name="run"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.Cov.getSub() -->
<method name="getSub"  public="true">
<description/>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetCovSubscription"/>
</return>
</method>

</class>
</bajadoc>
