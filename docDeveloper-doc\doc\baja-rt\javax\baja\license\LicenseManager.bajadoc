<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.license.LicenseManager" name="LicenseManager" packageName="javax.baja.license" public="true" interface="true" abstract="true" category="interface">
<description>
LicenseManager is used to access the Baja licensing framework.&#xa; The licensing framework is based on the notion of digitally signed &#xa; set of key/value pairs stored in properties file.  The license &#xa; manager is accessed via &lt;code&gt;Sys.getLicenseManager()&lt;/code&gt;.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">3 Nov 01</tag>
<tag name="@version">$Revision: 5$ $Date: 3/28/05 9:22:59 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<!-- javax.baja.license.LicenseManager.getFeature(java.lang.String, java.lang.String) -->
<method name="getFeature"  public="true" abstract="true">
<description>
Get the specified feature by vendor and feature name.  Note &#xa; this method does not automatically call Feature.check().
</description>
<parameter name="vendor">
<type class="java.lang.String"/>
</parameter>
<parameter name="feature">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.license.Feature"/>
</return>
<throws>
<type class="javax.baja.license.FeatureNotLicensedException"/>
</throws>
<throws>
<type class="javax.baja.license.LicenseDatabaseException"/>
<description>
if the license database &#xa;   is not installed or the feature is not found.
</description>
</throws>
</method>

<!-- javax.baja.license.LicenseManager.checkFeature(java.lang.String, java.lang.String) -->
<method name="checkFeature"  public="true" abstract="true">
<description>
Verify that the specified feature exists and is not expired.
</description>
<parameter name="vendor">
<type class="java.lang.String"/>
</parameter>
<parameter name="feature">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.license.Feature"/>
</return>
<throws>
<type class="javax.baja.license.FeatureNotLicensedException"/>
<description>
if the feature is not&#xa;   licensed or has expired.
</description>
</throws>
<throws>
<type class="javax.baja.license.LicenseDatabaseException"/>
<description>
if the license database &#xa;   is not installed or invalid.
</description>
</throws>
</method>

<!-- javax.baja.license.LicenseManager.getFeatures() -->
<method name="getFeatures"  public="true" abstract="true">
<description>
Get the list of features which are licensed for this &#xa; particular machine.  Each of these feature may or may &#xa; not have an expiration date.
</description>
<return>
<type class="javax.baja.license.Feature" dimension="1"/>
</return>
<throws>
<type class="javax.baja.license.LicenseDatabaseException"/>
<description>
if the license database &#xa;   is not installed or is invalid.
</description>
</throws>
</method>

</class>
</bajadoc>
