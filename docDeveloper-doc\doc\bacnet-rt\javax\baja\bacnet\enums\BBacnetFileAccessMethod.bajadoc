<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetFileAccessMethod" name="BBacnetFileAccessMethod" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetFileAccessMethod represents the BacnetFileAccessMethod&#xa; enumeration.&lt;p&gt;&#xa; NOTE: These enumeration values are the opposite of the ASN tag&#xa; values.  The enumeration value for record access is 0, while the&#xa; AtomicReadFile ASN.1 tag defining record access is 1.  The enumeration&#xa; value is only used in the File object&#x27;s File_Access_Method property.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">06 Mar 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;recordAccess&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;streamAccess&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetFileAccessMethod.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetFileAccessMethod"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetFileAccessMethod.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetFileAccessMethod"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetFileAccessMethod.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetFileAccessMethod.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetFileAccessMethod.RECORD_ACCESS -->
<field name="RECORD_ACCESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for recordAccess.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFileAccessMethod.STREAM_ACCESS -->
<field name="STREAM_ACCESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for streamAccess.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFileAccessMethod.recordAccess -->
<field name="recordAccess"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetFileAccessMethod"/>
<description>
BBacnetFileAccessMethod constant for recordAccess.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFileAccessMethod.streamAccess -->
<field name="streamAccess"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetFileAccessMethod"/>
<description>
BBacnetFileAccessMethod constant for streamAccess.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFileAccessMethod.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetFileAccessMethod"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetFileAccessMethod.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
