<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.transport.BBacnetTransportLayer" name="BBacnetTransportLayer" packageName="com.tridium.bacnet.stack.transport" public="true">
<description>
Tridium Transport Layer Implementation.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 7$ $Date: 12/19/01 4:35:33 PM$</tag>
<tag name="@creation">10 Aug 00</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="com.tridium.bacnet.stack.BacnetStackErrorCodes"/>
</implements>
<property name="clientQueueSize" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;clientQueueSize&lt;/code&gt; property.
</description>
<tag name="@see">#getClientQueueSize</tag>
<tag name="@see">#setClientQueueSize</tag>
</property>

<property name="serverQueueSize" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;serverQueueSize&lt;/code&gt; property.
</description>
<tag name="@see">#getServerQueueSize</tag>
<tag name="@see">#setServerQueueSize</tag>
</property>

<property name="lockupThreshold" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;lockupThreshold&lt;/code&gt; property.&#xa; the time that Niagara will wait for either a transaction timeout or&#xa; a response from a remote device.  After this time, Niagara will detect&#xa; a lockup of the transport state machine and restart it, abandoning the&#xa; transaction.
</description>
<tag name="@see">#getLockupThreshold</tag>
<tag name="@see">#setLockupThreshold</tag>
</property>

<action name="dumpStats" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;dumpStats&lt;/code&gt; action.
</description>
<tag name="@see">#dumpStats()</tag>
</action>

<action name="resetStats" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;resetStats&lt;/code&gt; action.
</description>
<tag name="@see">#resetStats()</tag>
</action>

</class>
</bajadoc>
