<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetBackupState" name="BBacnetBackupState" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetBackupState represents the possible states&#xa; of a BACnet device with respect to backup and restore&#xa; procedures.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">14 Jun 2006</tag>
<tag name="@since">Niagara 3.1</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;idle&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;preparingForBackup&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;preparingForRestore&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;performingABackup&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;performingARestore&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;backupFailure&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;restoreFailure&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetBackupState.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetBackupState"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetBackupState"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
String representation of this BEnum.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.IDLE -->
<field name="IDLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for idle.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.PREPARING_FOR_BACKUP -->
<field name="PREPARING_FOR_BACKUP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for preparingForBackup.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.PREPARING_FOR_RESTORE -->
<field name="PREPARING_FOR_RESTORE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for preparingForRestore.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.PERFORMING_ABACKUP -->
<field name="PERFORMING_ABACKUP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for performingABackup.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.PERFORMING_ARESTORE -->
<field name="PERFORMING_ARESTORE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for performingARestore.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.BACKUP_FAILURE -->
<field name="BACKUP_FAILURE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for backupFailure.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.RESTORE_FAILURE -->
<field name="RESTORE_FAILURE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for restoreFailure.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.idle -->
<field name="idle"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetBackupState"/>
<description>
BBacnetBackupState constant for idle.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.preparingForBackup -->
<field name="preparingForBackup"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetBackupState"/>
<description>
BBacnetBackupState constant for preparingForBackup.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.preparingForRestore -->
<field name="preparingForRestore"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetBackupState"/>
<description>
BBacnetBackupState constant for preparingForRestore.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.performingABackup -->
<field name="performingABackup"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetBackupState"/>
<description>
BBacnetBackupState constant for performingABackup.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.performingARestore -->
<field name="performingARestore"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetBackupState"/>
<description>
BBacnetBackupState constant for performingARestore.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.backupFailure -->
<field name="backupFailure"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetBackupState"/>
<description>
BBacnetBackupState constant for backupFailure.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.restoreFailure -->
<field name="restoreFailure"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetBackupState"/>
<description>
BBacnetBackupState constant for restoreFailure.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetBackupState"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetBackupState.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
