<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.datatypes.BDiscoveryNetworks" name="BDiscoveryNetworks" packageName="com.tridium.bacnet.datatypes" public="true" final="true">
<description>
Networks to be interrogated in a device discovery command.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">03 Jan 2005</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BSimple"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
</class>
</bajadoc>
