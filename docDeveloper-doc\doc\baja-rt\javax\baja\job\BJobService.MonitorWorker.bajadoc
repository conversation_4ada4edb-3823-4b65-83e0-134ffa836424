<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.job.BJobService$MonitorWorker" name="BJobService.MonitorWorker" packageName="javax.baja.job" protected="true" static="true" innerClass="true">
<description>
Monitor the thread pool
</description>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="java.lang.Runnable"/>
</implements>
<!-- javax.baja.job.BJobService.MonitorWorker(java.util.concurrent.ForkJoinPool, long) -->
<constructor name="MonitorWorker" public="true">
<parameter name="executor">
<type class="java.util.concurrent.ForkJoinPool"/>
</parameter>
<parameter name="monitorIntervalMs">
<type class="long"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.job.BJobService.MonitorWorker.shutdown() -->
<method name="shutdown"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.job.BJobService.MonitorWorker.run() -->
<method name="run"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
