<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.job.BBacnetDiscoverDevicesJob" name="BBacnetDiscoverDevicesJob" packageName="com.tridium.bacnet.job" public="true">
<description>
BBacnetDiscoverDevicesJob queries the connected network for&#xa; undiscovered devices.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">01 Mar 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.job.BDeviceManagerJob"/>
</extends>
<implements>
<type class="com.tridium.bacnet.stack.IAmListener"/>
</implements>
</class>
</bajadoc>
