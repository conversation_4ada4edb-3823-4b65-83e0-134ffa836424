<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.outbound.schema.config">
<description/>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.config" name="BJsonSchemaNameCasing"><description>Different available case options for the forming of json keys.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.config" name="BJsonSchemaNameSource"><description>Different available options for what name to use for json keys when bound to a station component.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.config" name="BJsonSchemaNameSpacing"><description>Different available options for dealing with spaces in the forming of json keys.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.config" name="BJsonSchemaPropertyNameSource"><description>Different available options for what name to use for json keys for pairs within a bound object.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.config" name="BJsonSchemaTuningPolicy"><description>Use conventional Framework construct for min/max write etc.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.config" name="BJsonSchemaUpdateStrategy"><description>The different stratagem for defining when a json schema should update it&#x27;s output string.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.config" name="BMetadataField"><description>Different metadata fields supported in json metadata bindings.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.config" name="BSlotSelectionType"><description>The different options for which slots from the ord target&#xa; to include in a bound container such as a json object or array.</description></class>
</package>
</bajadoc>
