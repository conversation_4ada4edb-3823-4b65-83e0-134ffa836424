<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.BAndoverBackupFolder" name="BAndoverBackupFolder" packageName="com.tridium.andoverAC256" public="true">
<description>
BAndoverBackupFolder is the standard container to use&#xa; under BAndoverBackupTable to organize BAndoverBackupFiles.
</description>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@creation">15/5/2005 9:45AM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.0.78</tag>
<extends>
<type class="javax.baja.util.BFolder"/>
</extends>
<topic name="tableUpdated" flags="">
<eventType>
<type class="javax.baja.sys.BValue"/>
</eventType><description>
Slot for the &lt;code&gt;tableUpdated&lt;/code&gt; topic.
</description>
<tag name="@see">#fireTableUpdated</tag>
</topic>

</class>
</bajadoc>
