<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.BBacnetOrdScheme" name="BBacnetOrdScheme" packageName="com.tridium.bacnet" public="true">
<description>
BBacnetOrdScheme is used to name and reference Bacnet objects&#xa; by BacnetObjectIdentifier.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">29 Aug 03</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.naming.BOrdScheme"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraSingleton"/>
</annotation>
</class>
</bajadoc>
