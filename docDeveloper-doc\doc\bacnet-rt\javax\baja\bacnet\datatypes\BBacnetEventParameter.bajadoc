<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetEventParameter" name="BBacnetEventParameter" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BBacnetEventParameter represents the BACnetEventParameter&#xa; choice.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">29 Jul 2005</tag>
<tag name="@since">Niagara 3.1</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="choice" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter() -->
<constructor name="BBacnetEventParameter" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.getChoice() -->
<method name="getChoice"  public="true">
<description>
Get the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.setChoice(int) -->
<method name="setChoice"  public="true">
<description>
Set the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.makeChangeOfState(javax.baja.sys.BRelTime, javax.baja.bacnet.datatypes.BBacnetListOf) -->
<method name="makeChangeOfState"  public="true" static="true">
<description/>
<parameter name="timeDelay">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<parameter name="listOfValues">
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetEventParameter"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.makeCommandFailure(javax.baja.sys.BRelTime, javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference) -->
<method name="makeCommandFailure"  public="true" static="true">
<description/>
<parameter name="timeDelay">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<parameter name="feedbackRef">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetEventParameter"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.makeFloatingLimit(javax.baja.sys.BRelTime, javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference, float, float, float) -->
<method name="makeFloatingLimit"  public="true" static="true">
<description/>
<parameter name="timeDelay">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<parameter name="setpointRef">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
</parameter>
<parameter name="lowDiffLimit">
<type class="float"/>
</parameter>
<parameter name="highDiffLimit">
<type class="float"/>
</parameter>
<parameter name="deadband">
<type class="float"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetEventParameter"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.makeOutOfRange(javax.baja.sys.BRelTime, float, float, float) -->
<method name="makeOutOfRange"  public="true" static="true">
<description/>
<parameter name="timeDelay">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<parameter name="lowLimit">
<type class="float"/>
</parameter>
<parameter name="highLimit">
<type class="float"/>
</parameter>
<parameter name="deadband">
<type class="float"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetEventParameter"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.makeBufferReady(long, long) -->
<method name="makeBufferReady"  public="true" static="true">
<description/>
<parameter name="notificationThreshold">
<type class="long"/>
</parameter>
<parameter name="previousNotificationCount">
<type class="long"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetEventParameter"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.makeSignedOutOfRange(javax.baja.sys.BRelTime, double, double, double) -->
<method name="makeSignedOutOfRange"  public="true" static="true">
<description/>
<parameter name="timeDelay">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<parameter name="lowLimit">
<type class="double"/>
</parameter>
<parameter name="highLimit">
<type class="double"/>
</parameter>
<parameter name="deadband">
<type class="double"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetEventParameter"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.makeChangeOfCharacterString(javax.baja.sys.BRelTime, javax.baja.bacnet.datatypes.BBacnetListOf) -->
<method name="makeChangeOfCharacterString"  public="true" static="true">
<description/>
<parameter name="timeDelay">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<parameter name="listOfValues">
<type class="javax.baja.bacnet.datatypes.BBacnetListOf"/>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetEventParameter"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.subscribed() -->
<method name="subscribed"  public="true" final="true">
<description>
Callback when the component enters the subscribed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.unsubscribed() -->
<method name="unsubscribed"  public="true" final="true">
<description>
Callback when the component leaves the subscribed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.getAppliedCategoryMask() -->
<method name="getAppliedCategoryMask"  public="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.getCategoryMask() -->
<method name="getCategoryMask"  public="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.getPermissions(javax.baja.sys.Context) -->
<method name="getPermissions"  public="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.security.BPermissions"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.choice -->
<field name="choice"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.TIME_DELAY_SLOT_NAME -->
<field name="TIME_DELAY_SLOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.LIST_OF_VALUES_SLOT_NAME -->
<field name="LIST_OF_VALUES_SLOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.LIST_OF_ALARM_VALUES_SLOT_NAME -->
<field name="LIST_OF_ALARM_VALUES_SLOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.FEEDBACK_PROPERTY_REFERENCE_SLOT_NAME -->
<field name="FEEDBACK_PROPERTY_REFERENCE_SLOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.SETPOINT_REFERENCE_SLOT_NAME -->
<field name="SETPOINT_REFERENCE_SLOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.LOW_DIFF_LIMIT_SLOT_NAME -->
<field name="LOW_DIFF_LIMIT_SLOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.HIGH_DIFF_LIMIT_SLOT_NAME -->
<field name="HIGH_DIFF_LIMIT_SLOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.LOW_LIMIT_SLOT_NAME -->
<field name="LOW_LIMIT_SLOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.HIGH_LIMIT_SLOT_NAME -->
<field name="HIGH_LIMIT_SLOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.DEADBAND_SLOT_NAME -->
<field name="DEADBAND_SLOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.NOTIFICATION_THRESHOLD_SLOT_NAME -->
<field name="NOTIFICATION_THRESHOLD_SLOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.PREVIOUS_NOTIFICATION_COUNT_SLOT_NAME -->
<field name="PREVIOUS_NOTIFICATION_COUNT_SLOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.STATUS_FLAGS_SLOT_NAME -->
<field name="STATUS_FLAGS_SLOT_NAME"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_BITSTRING_TAG -->
<field name="CHANGE_OF_BITSTRING_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
change-of-bitstring
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_BITSTRING_TIME_DELAY_TAG -->
<field name="CHANGE_OF_BITSTRING_TIME_DELAY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_BITSTRING_BITMASK_TAG -->
<field name="CHANGE_OF_BITSTRING_BITMASK_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_BITSTRING_LIST_OF_BITSTRING_VALUES_TAG -->
<field name="CHANGE_OF_BITSTRING_LIST_OF_BITSTRING_VALUES_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_STATE_TAG -->
<field name="CHANGE_OF_STATE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
change-of-state
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_STATE_TIME_DELAY_TAG -->
<field name="CHANGE_OF_STATE_TIME_DELAY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_STATE_LIST_OF_VALUES_TAG -->
<field name="CHANGE_OF_STATE_LIST_OF_VALUES_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_VALUE_TAG -->
<field name="CHANGE_OF_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
change-of-value
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_VALUE_TIME_DELAY_TAG -->
<field name="CHANGE_OF_VALUE_TIME_DELAY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_VALUE_COV_CRITERIA_TAG -->
<field name="CHANGE_OF_VALUE_COV_CRITERIA_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_VALUE_BITMASK_TAG -->
<field name="CHANGE_OF_VALUE_BITMASK_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_VALUE_REFERENCED_PROPERTY_INCREMENT_TAG -->
<field name="CHANGE_OF_VALUE_REFERENCED_PROPERTY_INCREMENT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.COMMAND_FAILURE_TAG -->
<field name="COMMAND_FAILURE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
command-failure
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.COMMAND_FAILURE_TIME_DELAY_TAG -->
<field name="COMMAND_FAILURE_TIME_DELAY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.COMMAND_FAILURE_FEEDBACK_PROPERTY_REFERENCE_TAG -->
<field name="COMMAND_FAILURE_FEEDBACK_PROPERTY_REFERENCE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.FLOATING_LIMIT_TAG -->
<field name="FLOATING_LIMIT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
floating-limit
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.FLOATING_LIMIT_TIME_DELAY_TAG -->
<field name="FLOATING_LIMIT_TIME_DELAY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.FLOATING_LIMIT_SETPOINT_REFERENCE_TAG -->
<field name="FLOATING_LIMIT_SETPOINT_REFERENCE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.FLOATING_LIMIT_LOW_DIFF_LIMIT_TAG -->
<field name="FLOATING_LIMIT_LOW_DIFF_LIMIT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.FLOATING_LIMIT_HIGH_DIFF_LIMIT_TAG -->
<field name="FLOATING_LIMIT_HIGH_DIFF_LIMIT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.FLOATING_LIMIT_DEADBAND_TAG -->
<field name="FLOATING_LIMIT_DEADBAND_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.OUT_OF_RANGE_TAG -->
<field name="OUT_OF_RANGE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
out-of-range
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.OUT_OF_RANGE_TIME_DELAY_TAG -->
<field name="OUT_OF_RANGE_TIME_DELAY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.OUT_OF_RANGE_LOW_LIMIT_TAG -->
<field name="OUT_OF_RANGE_LOW_LIMIT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.OUT_OF_RANGE_HIGH_LIMIT_TAG -->
<field name="OUT_OF_RANGE_HIGH_LIMIT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.OUT_OF_RANGE_DEADBAND_TAG -->
<field name="OUT_OF_RANGE_DEADBAND_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.COMPLEX_EVENT_TYPE_TAG -->
<field name="COMPLEX_EVENT_TYPE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
complex-event-type
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.BUFFER_READY_DEPRECATED_TAG -->
<field name="BUFFER_READY_DEPRECATED_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
buffer-ready (deprecated)
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.BUFFER_READY_DEPRECATED_NOTIFICATION_THRESHOLD_TAG -->
<field name="BUFFER_READY_DEPRECATED_NOTIFICATION_THRESHOLD_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.BUFFER_READY_DEPRECATED_PREVIOUS_NOTIFICATION_COUNT_TAG -->
<field name="BUFFER_READY_DEPRECATED_PREVIOUS_NOTIFICATION_COUNT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_LIFE_SAFETY_TAG -->
<field name="CHANGE_OF_LIFE_SAFETY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
change-of-life-safety
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_LIFE_SAFETY_TIME_DELAY_TAG -->
<field name="CHANGE_OF_LIFE_SAFETY_TIME_DELAY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_LIFE_SAFETY_LIST_OF_LIFE_SAFETY_ALARM_VALUES_TAG -->
<field name="CHANGE_OF_LIFE_SAFETY_LIST_OF_LIFE_SAFETY_ALARM_VALUES_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_LIFE_SAFETY_LIST_ALARM_VALUES_TAG -->
<field name="CHANGE_OF_LIFE_SAFETY_LIST_ALARM_VALUES_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_LIFE_SAFETY_MODE_PROPERTY_REFERENCES_TAG -->
<field name="CHANGE_OF_LIFE_SAFETY_MODE_PROPERTY_REFERENCES_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.EXTENDED_TAG -->
<field name="EXTENDED_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
extended
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.EXTENDED_VENDOR_ID_TAG -->
<field name="EXTENDED_VENDOR_ID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.EXTENDED_EXTENDED_EVENT_TYPE_TAG -->
<field name="EXTENDED_EXTENDED_EVENT_TYPE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.EXTENDED_PARAMETERS_TAG -->
<field name="EXTENDED_PARAMETERS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.EXTENDED_REFERENCE_TAG -->
<field name="EXTENDED_REFERENCE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.BUFFER_READY_TAG -->
<field name="BUFFER_READY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
buffer-ready
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.BUFFER_READY_NOTIFICATION_THRESHOLD_TAG -->
<field name="BUFFER_READY_NOTIFICATION_THRESHOLD_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.BUFFER_READY_PREVIOUS_NOTIFICATION_COUNT_TAG -->
<field name="BUFFER_READY_PREVIOUS_NOTIFICATION_COUNT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.UNSIGNED_RANGE_TAG -->
<field name="UNSIGNED_RANGE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
unsigned-range
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.UNSIGNED_RANGE_TIME_DELAY_TAG -->
<field name="UNSIGNED_RANGE_TIME_DELAY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.UNSIGNED_RANGE_LOW_LIMIT_TAG -->
<field name="UNSIGNED_RANGE_LOW_LIMIT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.UNSIGNED_RANGE_HIGH_LIMIT_TAG -->
<field name="UNSIGNED_RANGE_HIGH_LIMIT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.ACCESS_EVENT_PREFIX -->
<field name="ACCESS_EVENT_PREFIX"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
-- enumeration value 12 is reserved for future addenda
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.ACCESS_EVENT_TAG -->
<field name="ACCESS_EVENT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.LIST_OF_ACCESS_EVENTS_TAG -->
<field name="LIST_OF_ACCESS_EVENTS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.ACCESS_EVENT_TIME_REFERENCE_TAG -->
<field name="ACCESS_EVENT_TIME_REFERENCE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.DOUBLE_OOR_TAG -->
<field name="DOUBLE_OOR_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.SIGNED_OOR_TAG -->
<field name="SIGNED_OOR_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.UNSIGNED_OOR_TAG -->
<field name="UNSIGNED_OOR_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_CHAR_STR_PREFIX -->
<field name="CHANGE_OF_CHAR_STR_PREFIX"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_CHAR_STR_TAG -->
<field name="CHANGE_OF_CHAR_STR_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_CHAR_STR_TIME_DELAY_TAG -->
<field name="CHANGE_OF_CHAR_STR_TIME_DELAY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_CHAR_STR_ALARM_VALUES_TAG -->
<field name="CHANGE_OF_CHAR_STR_ALARM_VALUES_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.MAX_ALARM_VALUES -->
<field name="MAX_ALARM_VALUES"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_STATUS_FLAGS_TAG -->
<field name="CHANGE_OF_STATUS_FLAGS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.CHANGE_OF_RELIABILITY_FLAGS_TAG -->
<field name="CHANGE_OF_RELIABILITY_FLAGS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.COSF_TIME_DELAY_TAG -->
<field name="COSF_TIME_DELAY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.COSF_SELECTED_FLAGS_TAG -->
<field name="COSF_SELECTED_FLAGS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetEventParameter.EP_EVENT_TYPE_NONE -->
<field name="EP_EVENT_TYPE_NONE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
