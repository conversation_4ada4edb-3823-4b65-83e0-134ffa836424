<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="com.tridium.bacnetAws.job.BBacnetAwsDiscoverTrendLogsJob" name="BBacnetAwsDiscoverTrendLogsJob" packageName="com.tridium.bacnetAws.job" public="true">
<description>
BBacnetAwsDiscoverTrendLogsJob augments BBacnetDiscoverTrendLogsJob to also&#xa; handle EventLogs.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">19 March 2010</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.job.BBacnetDiscoverTrendLogsJob"/>
</extends>
</class>
</bajadoc>
