<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="com.tridium.bacnet.stack.link.ip">
<description/>
<class packageName="com.tridium.bacnet.stack.link.ip" name="BBacnetIpLinkLayer"><description>Tridium Bacnet IP Virtual Link Layer Implementation.</description></class>
<class packageName="com.tridium.bacnet.stack.link.ip" name="BBdtEntry"><description>BBdtEntry represents an entry in the&#xa; Broadcast Distribution Table (BDT) of a Bacnet Broadcast Management&#xa; Device (BBMD).</description></class>
<class packageName="com.tridium.bacnet.stack.link.ip" name="BBroadcastDistributionTable"><description>BBroadcastDistributionTable represents the Broadcast&#xa; Distribution Table (BDT) of a Bacnet Broadcast Management&#xa; Device (BBMD).</description></class>
<class packageName="com.tridium.bacnet.stack.link.ip" name="BFdtEntry"><description>BFdtEntry represents an entry in the&#xa; Foreign Device Table (FDT) of a Bacnet Broadcast Management&#xa; Device (BBMD).</description></class>
<class packageName="com.tridium.bacnet.stack.link.ip" name="BForeignDeviceRegistration"><description>The BForeignDeviceRegistration class exposes the ability to&#xa; more finely control foreign device registrations with BBMD devices.</description></class>
<class packageName="com.tridium.bacnet.stack.link.ip" name="BForeignDeviceTable"><description>BForeignDeviceTable represents the Foreign&#xa; Device Table (FDT) of a Bacnet Broadcast Management&#xa; Device (BBMD).</description></class>
</package>
</bajadoc>
