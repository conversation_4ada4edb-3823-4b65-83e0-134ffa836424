<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.dataRecovery.DataRecoveryServiceInFaultException" name="DataRecoveryServiceInFaultException" packageName="javax.baja.dataRecovery" public="true" category="exception">
<description>
DataRecoveryServiceInFaultException is thrown whenever a source&#xa; attempts to use the BIDataRecoveryService interface but the&#xa; service is in an invalid state for the request. Services should&#xa; stop using the interface upon trapping this exception.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">4 March 10</tag>
<tag name="@version">Original</tag>
<tag name="@since">Niagara 3.6</tag>
<extends>
<type class="javax.baja.dataRecovery.DataRecoveryException"/>
</extends>
<!-- javax.baja.dataRecovery.DataRecoveryServiceInFaultException() -->
<constructor name="DataRecoveryServiceInFaultException" public="true">
<description/>
</constructor>

<!-- javax.baja.dataRecovery.DataRecoveryServiceInFaultException(java.lang.String) -->
<constructor name="DataRecoveryServiceInFaultException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.dataRecovery.DataRecoveryServiceInFaultException(java.lang.String, java.lang.Throwable) -->
<constructor name="DataRecoveryServiceInFaultException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="cause">
<type class="java.lang.Throwable"/>
</parameter>
<description/>
</constructor>

</class>
</bajadoc>
