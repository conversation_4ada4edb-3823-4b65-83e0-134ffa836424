<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.job.BDiscoveryDevice" name="BDiscoveryDevice" packageName="com.tridium.bacnet.job" public="true">
<description>
BDiscoveryDevice.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">17 Aug 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="deviceName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;deviceName&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceName</tag>
<tag name="@see">#setDeviceName</tag>
</property>

<property name="objectId" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="maxApduLengthAccepted" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;maxApduLengthAccepted&lt;/code&gt; property.
</description>
<tag name="@see">#getMaxApduLengthAccepted</tag>
<tag name="@see">#setMaxApduLengthAccepted</tag>
</property>

<property name="segmentationSupported" flags="">
<type class="javax.baja.bacnet.enums.BBacnetSegmentation"/>
<description>
Slot for the &lt;code&gt;segmentationSupported&lt;/code&gt; property.
</description>
<tag name="@see">#getSegmentationSupported</tag>
<tag name="@see">#setSegmentationSupported</tag>
</property>

<property name="vendorId" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;vendorId&lt;/code&gt; property.
</description>
<tag name="@see">#getVendorId</tag>
<tag name="@see">#setVendorId</tag>
</property>

<property name="address" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
Slot for the &lt;code&gt;address&lt;/code&gt; property.
</description>
<tag name="@see">#getAddress</tag>
<tag name="@see">#setAddress</tag>
</property>

<property name="listSize" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;listSize&lt;/code&gt; property.
</description>
<tag name="@see">#getListSize</tag>
<tag name="@see">#setListSize</tag>
</property>

<property name="encoding" flags="">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
<description>
Slot for the &lt;code&gt;encoding&lt;/code&gt; property.
</description>
<tag name="@see">#getEncoding</tag>
<tag name="@see">#setEncoding</tag>
</property>

<property name="servicesSupported" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
Slot for the &lt;code&gt;servicesSupported&lt;/code&gt; property.
</description>
<tag name="@see">#getServicesSupported</tag>
<tag name="@see">#setServicesSupported</tag>
</property>

<property name="vendorName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;vendorName&lt;/code&gt; property.
</description>
<tag name="@see">#getVendorName</tag>
<tag name="@see">#setVendorName</tag>
</property>

<property name="modelName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;modelName&lt;/code&gt; property.
</description>
<tag name="@see">#getModelName</tag>
<tag name="@see">#setModelName</tag>
</property>

<property name="protocolRevision" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;protocolRevision&lt;/code&gt; property.
</description>
<tag name="@see">#getProtocolRevision</tag>
<tag name="@see">#setProtocolRevision</tag>
</property>

<property name="firmwareRevision" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;firmwareRevision&lt;/code&gt; property.
</description>
<tag name="@see">#getFirmwareRevision</tag>
<tag name="@see">#setFirmwareRevision</tag>
</property>

<property name="applicationSoftwareVersion" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;applicationSoftwareVersion&lt;/code&gt; property.
</description>
<tag name="@see">#getApplicationSoftwareVersion</tag>
<tag name="@see">#setApplicationSoftwareVersion</tag>
</property>

<property name="duplicate" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;duplicate&lt;/code&gt; property.
</description>
<tag name="@see">#getDuplicate</tag>
<tag name="@see">#setDuplicate</tag>
</property>

</class>
</bajadoc>
