<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.BModuleScheme" name="BModuleScheme" packageName="javax.baja.naming" public="true">
<description>
BModuleScheme is used to resolve modules and their contents&#xa; using a ModuleQuery.
</description>
<tag name="@author"><PERSON> on 10 Mar 03</tag>
<tag name="@version">$Revision: 6$ $Date: 5/26/11 10:32:58 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.naming.BOrdScheme"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraSingleton"/>
</annotation>
<!-- javax.baja.naming.BModuleScheme.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.naming.BModuleScheme.parse(java.lang.String) -->
<method name="parse"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return an instance of FilePath.
</description>
<parameter name="queryBody">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdQuery"/>
</return>
</method>

<!-- javax.baja.naming.BModuleScheme.resolve(javax.baja.naming.OrdTarget, javax.baja.naming.OrdQuery) -->
<method name="resolve"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Resolve the query into a BModule, palette or BIFile.
</description>
<parameter name="base">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<parameter name="query">
<type class="javax.baja.naming.OrdQuery"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdTarget"/>
</return>
<throws>
<type class="javax.baja.naming.SyntaxException"/>
</throws>
<throws>
<type class="javax.baja.naming.UnresolvedException"/>
</throws>
</method>

<!-- javax.baja.naming.BModuleScheme.isModuleDevEnabled() -->
<method name="isModuleDevEnabled"  public="true" static="true">
<description>
Return true if the module development mode is enabled. &#xa; If true then resolved module ORDs can resolve from a source&#xa; directory instead of a module zip file.
</description>
<tag name="@since">Niagara 4.0</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.BModuleScheme.INSTANCE -->
<field name="INSTANCE"  public="true" static="true" final="true">
<type class="javax.baja.naming.BModuleScheme"/>
<description/>
</field>

<!-- javax.baja.naming.BModuleScheme.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
