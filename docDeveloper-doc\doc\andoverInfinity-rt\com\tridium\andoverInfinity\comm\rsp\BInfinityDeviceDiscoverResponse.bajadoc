<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.rsp.BInfinityDeviceDiscoverResponse" name="BInfinityDeviceDiscoverResponse" packageName="com.tridium.andoverInfinity.comm.rsp" public="true">
<description>
BInfinityDeviceDiscoverResponse includes an array of BInfinityDeviceDiscoveryObject&#x27;s&#xa; that were discovered in the processing of a BInfinityDeviceDiscoverRequest
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 29, 2007</tag>
<tag name="@version">$Revision$ $May 29, 2007 11:13:23 AM$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.comm.rsp.BDdfResponse"/>
</extends>
<implements>
<type class="com.tridium.ddf.comm.rsp.BIDdfDiscoverResponse"/>
</implements>
<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityDeviceDiscoverResponse() -->
<constructor name="BInfinityDeviceDiscoverResponse" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityDeviceDiscoverResponse.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityDeviceDiscoverResponse.setDiscoveryData(com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoveryObject[]) -->
<method name="setDiscoveryData"  public="true">
<description>
Install the discovered raw data
</description>
<parameter name="data">
<type class="com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoveryObject" dimension="1"/>
<description>
is the array of BInfinityDeviceDiscoveryObject&#x27;s
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityDeviceDiscoverResponse.parseDiscoveryObjects(javax.baja.sys.Context) -->
<method name="parseDiscoveryObjects"  public="true">
<description>
Return the list of BInfinityDeviceDiscoveryObject&#x27;s set by method &lt;code&gt;setDiscoveryData&lt;/code&gt;
</description>
<tag name="@see">com.tridium.ddf.comm.rsp.BIDdfDiscoverResponse#parseDiscoveryObjects(javax.baja.sys.Context)</tag>
<parameter name="c">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="com.tridium.ddf.discover.BIDdfDiscoveryObject" dimension="1"/>
<description>
array of &lt;code&gt;BInfinityDeviceDiscoveryObject&lt;/code&gt;
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityDeviceDiscoverResponse.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
