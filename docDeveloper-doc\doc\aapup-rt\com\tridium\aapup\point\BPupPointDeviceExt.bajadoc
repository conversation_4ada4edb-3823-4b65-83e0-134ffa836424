<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.point.BPupPointDeviceExt" name="BPupPointDeviceExt" packageName="com.tridium.aapup.point" public="true">
<description>
aapup implementation of BPointDeviceExt
</description>
<tag name="@author">C<PERSON><PERSON></tag>
<tag name="@creation">7/14/2005 4:18PM</tag>
<tag name="@version">$Revision: 1$ $Date: 9/28/2004 11:35:42 AM$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="javax.baja.driver.point.BPointDeviceExt"/>
</extends>
</class>
</bajadoc>
