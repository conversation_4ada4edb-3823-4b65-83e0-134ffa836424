<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.history.BBacnetNumericTrendLogExt" name="BBacnetNumericTrendLogExt" packageName="com.tridium.bacnet.history" public="true">
<description>
Created by <PERSON><PERSON><PERSON> on 5/23/2017.
</description>
<extends>
<type class="com.tridium.bacnet.history.BBacnetNumericIntervalTrendLogExt"/>
</extends>
<property name="changeTolerance" flags="">
<type class="double"/>
<description>
Slot for the &lt;code&gt;changeTolerance&lt;/code&gt; property.
</description>
<tag name="@see">#getChangeTolerance</tag>
<tag name="@see">#setChangeTolerance</tag>
</property>

</class>
</bajadoc>
