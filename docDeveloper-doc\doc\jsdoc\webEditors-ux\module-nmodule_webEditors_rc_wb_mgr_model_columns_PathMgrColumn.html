<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>webEditors Module: nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">webEditors</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-nmodule_webEditors_rc_fe_baja_BaseEditor.html">nmodule/webEditors/rc/fe/baja/BaseEditor</a></li><li><a href="module-nmodule_webEditors_rc_fe_BaseWidget.html">nmodule/webEditors/rc/fe/BaseWidget</a></li><li><a href="module-nmodule_webEditors_rc_fe_fe.html">nmodule/webEditors/rc/fe/fe</a></li><li><a href="module-nmodule_webEditors_rc_fe_feDialogs.html">nmodule/webEditors/rc/fe/feDialogs</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_commands_MgrCommand.html">nmodule/webEditors/rc/wb/mgr/commands/MgrCommand</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_Manager.html">nmodule/webEditors/rc/wb/mgr/Manager</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html">nmodule/webEditors/rc/wb/mgr/MgrLearn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrStateHandler.html">nmodule/webEditors/rc/wb/mgr/MgrStateHandler</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrTypeInfo.html">nmodule/webEditors/rc/wb/mgr/MgrTypeInfo</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_IconMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinPropMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_NameMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyPathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_TypeMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/MgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrModel.html">nmodule/webEditors/rc/wb/mgr/model/MgrModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">nmodule/webEditors/rc/wb/table/model/Column</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_DisplayNameColumn.html">nmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_IconColumn.html">nmodule/webEditors/rc/wb/table/model/columns/IconColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_JsonObjectPropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_PropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_ToStringColumn.html">nmodule/webEditors/rc/wb/table/model/columns/ToStringColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentSource.html">nmodule/webEditors/rc/wb/table/model/ComponentSource</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentTableModel.html">nmodule/webEditors/rc/wb/table/model/ComponentTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">nmodule/webEditors/rc/wb/table/model/Row</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html">nmodule/webEditors/rc/wb/table/model/TableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_Table.html">nmodule/webEditors/rc/wb/table/Table</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeNodeRow.html">nmodule/webEditors/rc/wb/table/tree/TreeNodeRow</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeTableModel.html">nmodule/webEditors/rc/wb/table/tree/TreeTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_tree_TreeNode.html">nmodule/webEditors/rc/wb/tree/TreeNode</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="tutorials.list.html" class="dropdown-toggle" data-toggle="dropdown">Tutorials<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="tutorial-6-managers.html">Managers</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Module: nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn</h1>
<section>

<header>
    
        
            
        
    
</header>


<article>
    <div class="container-overview">
    
        

        
            
<hr>
<dt>
    <h4 class="name" id="module:nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn"><span class="type-signature"></span>new (require("nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn"))()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>API Status: <strong>Development</strong></p>
<p>Manager column used to display the slot path of a BComponent within<br>
the station. This type is equivalent to the MgrColumn.Path class<br>
in the bajaui abstract manager framework.</p>
    </div>
    

    
        <h5>Extends:</h5>
        


    <ul>
        <li><a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">module:nmodule/webEditors/rc/wb/table/model/Column</a></li>
    </ul>


    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id="buildCell"><span class="type-signature"></span>buildCell(row, dom)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Builds out the DOM element, typically a <code>td</code>, that represents the<br>
intersection of this column with a particular row. Often this will simply<br>
be a simple <code>toString()</code> or similar, but could also do more sophisticated<br>
things like build widgets.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>row</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dom</code></td>
            

            <td class="type">
            
                
<span class="param-type">JQuery</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#buildCell">module:nmodule/webEditors/rc/wb/table/model/Column#buildCell</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved when the element's contents<br>
have been fully built. It's also acceptable for overrides of this function<br>
to complete synchronously without returning a promise, so be sure to wrap<br>
calls to this function in <code>Promise.resolve()</code> when appropriate.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>
|

<span class="param-type">*</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="destroyCell"><span class="type-signature"></span>destroyCell(row, dom)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Called when the table is destroying the DOM element built for a cell in this column. This<br>
gives a <code>Column</code> implementation the chance to clean up any resources that might have been<br>
created during the earlier call to <code>#buildCell</code>, perhaps destroying a widget in the cell,<br>
for example. As with <code>#buildCell</code>, if this completes synchronously and doesn't return a<br>
Promise, the caller must wrap this in a call to <code>Promise.resolve()</code>.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>row</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a></span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dom</code></td>
            

            <td class="type">
            
                
<span class="param-type">JQuery</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#destroyCell">module:nmodule/webEditors/rc/wb/table/model/Column#destroyCell</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>
|

<span class="param-type">*</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getColumnIcon"><span class="type-signature"></span>getColumnIcon()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Returns a URI for an icon representing this column. Returns <code>null</code> by<br>
default; override as needed in subclasses.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#getColumnIcon">module:nmodule/webEditors/rc/wb/table/model/Column#getColumnIcon</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>a URI for an icon to be shown for this column.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getFlags"><span class="type-signature"></span>getFlags()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the flags set on this column.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#getFlags">module:nmodule/webEditors/rc/wb/table/model/Column#getFlags</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getName"><span class="type-signature"></span>getName()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the column name or <code>null</code> if none was given.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#getName">module:nmodule/webEditors/rc/wb/table/model/Column#getName</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getValueFor"><span class="type-signature"></span>getValueFor(row)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Gets the value of the row as an unescaped slot path string. The row's<br>
subject must be a <code>Component</code>, otherwise an Error will be thrown. In the<br>
case of an unmounted component, an empty string will be returned.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>row</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">module:nmodule/webEditors/rc/wb/table/model/Row</a></span>



            
            </td>

            

            

            <td class="description last"><p>A row<br>
representing a Component in a manager view.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#getValueFor">module:nmodule/webEditors/rc/wb/table/model/Column#getValueFor</a>
    </li></ul></dd>
    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="hasFlags"><span class="type-signature"></span>hasFlags(flags)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if the column has <em>all</em> of the given flags.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>flags</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>flags to check for</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#hasFlags">module:nmodule/webEditors/rc/wb/table/model/Column#hasFlags</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isEditable"><span class="type-signature"></span>isEditable()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if the column is editable.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#isEditable">module:nmodule/webEditors/rc/wb/table/model/Column#isEditable</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isExportable"><span class="type-signature"></span>isExportable()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if the column should show up in export operations, e.g. to CSV.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.8</li>
		</ul>
	</dd>
	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#isExportable">module:nmodule/webEditors/rc/wb/table/model/Column#isExportable</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isHidable"><span class="type-signature"></span>isHidable()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if the column should available in the table's show/hide context menu.<br>
Defaults to true.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#isHidable">module:nmodule/webEditors/rc/wb/table/model/Column#isHidable</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isReadonly"><span class="type-signature"></span>isReadonly()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if the column is readonly.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#isReadonly">module:nmodule/webEditors/rc/wb/table/model/Column#isReadonly</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isSortable"><span class="type-signature"></span>isSortable()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Returns a boolean indicating whether the column should not be sortable via the table headings.<br>
Defaults to true.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#isSortable">module:nmodule/webEditors/rc/wb/table/model/Column#isSortable</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isUnseen"><span class="type-signature"></span>isUnseen()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return true if the column is unseen.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#isUnseen">module:nmodule/webEditors/rc/wb/table/model/Column#isUnseen</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setEditable"><span class="type-signature"></span>setEditable(editable)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set or unset the column's <code>EDITABLE</code> flag. Emits a <code>flagsChanged</code> event.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>editable</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#setEditable">module:nmodule/webEditors/rc/wb/table/model/Column#setEditable</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setExportable"><span class="type-signature"></span>setExportable(exportable)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set or unset whether the column should show up in export operations.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>exportable</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.8</li>
		</ul>
	</dd>
	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#setExportable">module:nmodule/webEditors/rc/wb/table/model/Column#setExportable</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setFlags"><span class="type-signature"></span>setFlags(flags)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set the column's flags.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>flags</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#setFlags">module:nmodule/webEditors/rc/wb/table/model/Column#setFlags</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    
    <h5>Throws:</h5>
    
            

<dl>
    <dt>
        <div class="param-desc">
        <p>if a non-Number given</p>
        </div>
    </dt>
    <dt>
        <dl>
            <dt>
                Type
            </dt>
            <dd>
                
<span class="param-type">Error</span>



            </dd>
        </dl>
    </dt>
</dl>


        

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setHidable"><span class="type-signature"></span>setHidable(hidable)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set or unset whether the column should be allowed to be hidden or shown by the table's<br>
show/hide context menu.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>hidable</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#setHidable">module:nmodule/webEditors/rc/wb/table/model/Column#setHidable</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setReadonly"><span class="type-signature"></span>setReadonly(readonly)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set or unset the column's <code>READONLY</code> flag. Emits a <code>flagsChanged</code> event.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>readonly</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#setReadonly">module:nmodule/webEditors/rc/wb/table/model/Column#setReadonly</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setSortable"><span class="type-signature"></span>setSortable(sortable)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set or unset whether the column should be allowed to be sorted by the table heading.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>sortable</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#setSortable">module:nmodule/webEditors/rc/wb/table/model/Column#setSortable</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setUnseen"><span class="type-signature"></span>setUnseen(unseen)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Set or unset the column's <code>UNSEEN</code> flag. Emits a <code>flagsChanged</code> event.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>unseen</code></td>
            

            <td class="type">
            
                
<span class="param-type">boolean</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#setUnseen">module:nmodule/webEditors/rc/wb/table/model/Column#setUnseen</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="toDisplayName"><span class="type-signature"></span>toDisplayName()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Resolves a display name for this column.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	
	<dt class="inherited-from method-doc-label method-doc-details-label">Inherited From:</dt>
	<dd class="inherited-from">
		<ul class="dummy">
			<li>
				<a href="module-nmodule_webEditors_rc_wb_table_model_Column.html#toDisplayName">module:nmodule/webEditors/rc/wb/table/model/Column#toDisplayName</a>
			</li>
		</ul>
	</dd>
	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>promise to be resolved when the element's display name<br>
has been fully built. It's also acceptable for overrides of this function<br>
to complete synchronously without returning a promise, so be sure to wrap<br>
calls to this function in <code>Promise.resolve()</code> when appropriate.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>
|

<span class="param-type">*</span>



    </dd>
</dl>


        

    
</dd>

        </dl>
    

    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	webEditors Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:29:00+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>