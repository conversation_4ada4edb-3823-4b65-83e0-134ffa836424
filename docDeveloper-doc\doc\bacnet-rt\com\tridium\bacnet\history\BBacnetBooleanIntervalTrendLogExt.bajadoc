<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.history.BBacnetBooleanIntervalTrendLogExt" name="BBacnetBooleanIntervalTrendLogExt" packageName="com.tridium.bacnet.history" public="true">
<description>
An extension for collecting a Bacnet trend log of a boolean control value.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">30 Nov 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.history.ext.BBooleanIntervalHistoryExt"/>
</extends>
<implements>
<type class="com.tridium.bacnet.history.BIBacnetTrendLogExt"/>
</implements>
<property name="totalRecordCount" flags="r">
<type class="long"/>
<description>
Slot for the &lt;code&gt;totalRecordCount&lt;/code&gt; property.&#xa; represents the total number of records collected by the&#xa; Bacnet trend log since creation
</description>
<tag name="@see">#getTotalRecordCount</tag>
<tag name="@see">#setTotalRecordCount</tag>
</property>

<action name="startLogging" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;startLogging&lt;/code&gt; action.
</description>
<tag name="@see">#startLogging()</tag>
</action>

<action name="stopLogging" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;stopLogging&lt;/code&gt; action.
</description>
<tag name="@see">#stopLogging()</tag>
</action>

</class>
</bajadoc>
