<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.ext.offnormal.BEnumCommandFailureAlgorithm" name="BEnumCommandFailureAlgorithm" packageName="javax.baja.alarm.ext.offnormal" public="true">
<description>
BEnumCommandFailureAlgorithm implements command failure&#xa; alarm detection algorithm for multistate objects as described&#xa; in BACnet.  If feedback and output values of the enum point&#xa; are not equal for more than timeDelay, an offnormal alarm is&#xa; generated.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">03 May 01</tag>
<tag name="@version">$Revision: 27$ $Date: 9/8/05 11:03:53 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.alarm.ext.offnormal.BTwoStateAlgorithm"/>
</extends>
<property name="feedbackValue" flags="s">
<type class="javax.baja.status.BStatusEnum"/>
<description>
Slot for the &lt;code&gt;feedbackValue&lt;/code&gt; property.&#xa; Feedback value
</description>
<tag name="@see">#getFeedbackValue</tag>
<tag name="@see">#setFeedbackValue</tag>
</property>

<!-- javax.baja.alarm.ext.offnormal.BEnumCommandFailureAlgorithm() -->
<constructor name="BEnumCommandFailureAlgorithm" public="true">
<description/>
</constructor>

<!-- javax.baja.alarm.ext.offnormal.BEnumCommandFailureAlgorithm.getFeedbackValue() -->
<method name="getFeedbackValue"  public="true">
<description>
Get the &lt;code&gt;feedbackValue&lt;/code&gt; property.&#xa; Feedback value
</description>
<tag name="@see">#feedbackValue</tag>
<return>
<type class="javax.baja.status.BStatusEnum"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BEnumCommandFailureAlgorithm.setFeedbackValue(javax.baja.status.BStatusEnum) -->
<method name="setFeedbackValue"  public="true">
<description>
Set the &lt;code&gt;feedbackValue&lt;/code&gt; property.&#xa; Feedback value
</description>
<tag name="@see">#feedbackValue</tag>
<parameter name="v">
<type class="javax.baja.status.BStatusEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BEnumCommandFailureAlgorithm.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BEnumCommandFailureAlgorithm.isGrandparentLegal(javax.baja.sys.BComponent) -->
<method name="isGrandparentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
A BEnumCommandFailureAlgorithm&#x27;s grandparent must be an EnumPoint.
</description>
<parameter name="grandparent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BEnumCommandFailureAlgorithm.getSlotFacets(javax.baja.sys.Slot) -->
<method name="getSlotFacets"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="slot">
<type class="javax.baja.sys.Slot"/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BEnumCommandFailureAlgorithm.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BEnumCommandFailureAlgorithm.isNormal(javax.baja.status.BStatusValue) -->
<method name="isNormal"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return true if the feedback value matched output value
</description>
<parameter name="o">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BEnumCommandFailureAlgorithm.writeAlarmData(javax.baja.status.BStatusValue, java.util.Map) -->
<method name="writeAlarmData"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Write the key-value pairs defining alarm data for the&#xa;  alarm algorithm and state to the given Facets.&#xa; &lt;p&gt;&#xa;  The alarm data for a Command Failure alarm is given by&#xa;  BACnet table 13-3, Standard Object Property Values&#xa;  returned in notifications.
</description>
<parameter name="out">
<type class="javax.baja.status.BStatusValue"/>
<description>
The relevant control point status value
</description>
</parameter>
<parameter name="map">
<parameterizedType class="java.util.Map">
<args>
</args>
</parameterizedType>
<description>
The map.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BEnumCommandFailureAlgorithm.feedbackValue -->
<field name="feedbackValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;feedbackValue&lt;/code&gt; property.&#xa; Feedback value
</description>
<tag name="@see">#getFeedbackValue</tag>
<tag name="@see">#setFeedbackValue</tag>
</field>

<!-- javax.baja.alarm.ext.offnormal.BEnumCommandFailureAlgorithm.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
