<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetPropertyIdentifier" name="BBacnetPropertyIdentifier" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetPropertyIdentifier represents the BACnetPropertyIdentifier&#xa; enumeration.&#xa; &lt;p&gt;&#xa; BBacnetPropertyIdentifier is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-511 are reserved for use by ASHRAE.&#xa; Values from 512-4194303 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.&#xa; &lt;p&gt;&#xa; Maintained for backward compatibility:&#xa; &lt;ul&gt;&#xa;   &lt;li&gt;expiryTime should be expirationTime&lt;/li&gt;&#xa;   &lt;li&gt;memberStatusDlags should be memberStatusFlags&lt;/li&gt;&#xa;   &lt;li&gt;defaultFadetime should be defaultFadeTime&lt;/li&gt;&#xa;   &lt;li&gt;defaultRamprate should be defaultRampRate&lt;/li&gt;&#xa;   &lt;li&gt;egressTime should be egressTimer&lt;/li&gt;&#xa; &lt;/ul&gt;&#xa; &lt;p&gt;&#xa; Deprecated:&#xa; &lt;ul&gt;&#xa;   &lt;li&gt;removed1 (18)&lt;/li&gt;&#xa;   &lt;li&gt;issueConfirmedNotifications&lt;/li&gt;&#xa;   &lt;li&gt;listOfSessionKeys&lt;/li&gt;&#xa;   &lt;li&gt;protocolConformanceClass&lt;/li&gt;&#xa;   &lt;li&gt;recipient&lt;/li&gt;&#xa;   &lt;li&gt;currentNotifyTime&lt;/li&gt;&#xa;   &lt;li&gt;previousNotifyTime&lt;/li&gt;&#xa;   &lt;li&gt;masterExemption&lt;/li&gt;&#xa;   &lt;li&gt;occupancyExemption&lt;/li&gt;&#xa;   &lt;li&gt;passbackExemption&lt;/li&gt;&#xa; &lt;/ul&gt;
</description>
<tag name="@author">Craig Gemmill</tag>
<tag name="@version">$Revision: 7$ $Date: 12/19/01 4:36:00 PM$</tag>
<tag name="@creation">21 Jul 00</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ackedTransitions&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>0</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ackRequired&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>1</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;action&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>2</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;actionText&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>3</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;activeText&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>4</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;activeVtSessions&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>5</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;alarmValue&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>6</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;alarmValues&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>7</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;all&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>8</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;allWritesSuccessful&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>9</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;apduSegmentTimeout&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>10</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;apduTimeout&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>11</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;applicationSoftwareVersion&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>12</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;archive&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>13</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bias&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>14</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;changeOfStateCount&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>15</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;changeOfStateTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>16</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;notificationClass&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>17</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;controlledVariableReference&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>19</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;controlledVariableUnits&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>20</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;controlledVariableValue&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>21</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;covIncrement&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>22</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;dateList&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>23</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;daylightSavingsStatus&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>24</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deadband&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>25</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;derivativeConstant&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>26</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;derivativeConstantUnits&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>27</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;description&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>28</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;descriptionOfHalt&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>29</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deviceAddressBinding&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>30</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deviceType&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>31</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;effectivePeriod&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>32</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;elapsedActiveTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>33</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;errorLimit&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>34</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;eventEnable&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>35</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;eventState&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>36</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;eventType&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>37</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;exceptionSchedule&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>38</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;faultValues&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>39</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;feedbackValue&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>40</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;fileAccessMethod&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>41</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;fileSize&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>42</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;fileType&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>43</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;firmwareRevision&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>44</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;highLimit&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>45</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;inactiveText&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>46</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;inProcess&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>47</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;instanceOf&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>48</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;integralConstant&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>49</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;integralConstantUnits&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>50</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;limitEnable&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>52</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;listOfGroupMembers&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>53</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;listOfObjectPropertyReferences&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>54</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;localDate&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>56</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;localTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>57</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;location&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>58</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lowLimit&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>59</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;manipulatedVariableReference&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>60</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;maximumOutput&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>61</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;maxApduLengthAccepted&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>62</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;maxInfoFrames&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>63</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;maxMaster&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>64</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;maxPresValue&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>65</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;minimumOffTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>66</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;minimumOnTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>67</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;minimumOutput&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>68</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;minPresValue&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>69</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;modelName&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>70</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;modificationDate&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>71</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;notifyType&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>72</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;numberOfApduRetries&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>73</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;numberOfStates&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>74</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;objectIdentifier&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>75</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;objectList&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>76</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;objectName&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>77</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;objectPropertyReference&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>78</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;objectType&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>79</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;optional&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>80</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;outOfService&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>81</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;outputUnits&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>82</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;eventParameters&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>83</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;polarity&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>84</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;presentValue&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>85</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;priority&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>86</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;priorityArray&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>87</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;priorityForWriting&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>88</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;processIdentifier&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>89</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;programChange&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>90</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;programLocation&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>91</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;programState&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>92</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;proportionalConstant&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>93</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;proportionalConstantUnits&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>94</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;protocolObjectTypesSupported&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>96</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;protocolServicesSupported&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>97</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;protocolVersion&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>98</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;readOnly&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>99</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;reasonForHalt&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>100</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;recipientList&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>102</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;reliability&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>103</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;relinquishDefault&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>104</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;required&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>105</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;resolution&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>106</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;segmentationSupported&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>107</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;setpoint&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>108</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;setpointReference&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>109</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;stateText&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>110</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;statusFlags&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>111</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;systemStatus&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>112</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;timeDelay&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>113</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;timeOfActiveTimeReset&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>114</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;timeOfStateCountReset&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>115</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;timeSynchronizationRecipients&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>116</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;units&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>117</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;updateInterval&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>118</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;utcOffset&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>119</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;vendorIdentifier&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>120</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;vendorName&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>121</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;vtClassesSupported&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>122</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;weeklySchedule&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>123</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;attemptedSamples&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>124</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;averageValue&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>125</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bufferSize&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>126</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;clientCovIncrement&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>127</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;covResubscriptionInterval&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>128</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;eventTimeStamps&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>130</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;logBuffer&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>131</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;logDeviceObjectProperty&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>132</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;enable&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>133</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;logInterval&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>134</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;maximumValue&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>135</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;minimumValue&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>136</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;notificationThreshold&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>137</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;protocolRevision&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>139</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;recordsSinceNotification&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>140</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;recordCount&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>141</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;startTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>142</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;stopTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>143</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;stopWhenFull&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>144</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;totalRecordCount&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>145</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;validSamples&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>146</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;windowInterval&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>147</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;windowSamples&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>148</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;maximumValueTimestamp&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>149</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;minimumValueTimestamp&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>150</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;varianceValue&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>151</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;activeCovSubscriptions&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>152</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;backupFailureTimeout&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>153</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;configurationFiles&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>154</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;databaseRevision&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>155</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;directReading&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>156</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lastRestoreTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>157</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;maintenanceRequired&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>158</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;memberOf&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>159</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;mode&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>160</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;operationExpected&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>161</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;setting&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>162</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;silenced&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>163</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;trackingValue&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>164</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;zoneMembers&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>165</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lifeSafetyAlarmValues&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>166</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;maxSegmentsAccepted&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>167</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;profileName&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>168</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;autoSlaveDiscovery&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>169</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;manualSlaveAddressBinding&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>170</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;slaveAddressBinding&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>171</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;slaveProxyEnable&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>172</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lastNotifyRecord&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>173</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;scheduleDefault&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>174</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;acceptedModes&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>175</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;adjustValue&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>176</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;count&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>177</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;countBeforeChange&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>178</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;countChangeTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>179</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;covPeriod&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>180</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;inputReference&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>181</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;limitMonitoringInterval&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>182</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;loggingObject&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>183</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;loggingRecord&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>184</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;prescale&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>185</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;pulseRate&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>186</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;scale&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>187</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;scaleFactor&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>188</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;updateTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>189</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;valueBeforeChange&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>190</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;valueSet&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>191</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;valueChangeTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>192</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;alignIntervals&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>193</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;intervalOffset&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>195</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lastRestartReason&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>196</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;loggingType&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>197</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;restartNotificationRecipients&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>202</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;timeOfDeviceRestart&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>203</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;timeSynchronizationInterval&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>204</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;trigger&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>205</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;utcTimeSynchronizationRecipients&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>206</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;nodeSubtype&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>207</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;nodeType&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>208</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;structuredObjectList&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>209</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;subordinateAnnotations&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>210</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;subordinateList&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>211</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;actualShedLevel&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>212</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;dutyWindow&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>213</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;expectedShedLevel&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>214</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;fullDutyBaseline&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>215</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;requestedShedLevel&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>218</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;shedDuration&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>219</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;shedLevelDescriptions&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>220</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;shedLevels&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>221</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;stateDescription&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>222</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;doorAlarmState&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>226</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;doorExtendedPulseTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>227</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;doorMembers&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>228</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;doorOpenTooLongTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>229</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;doorPulseTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>230</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;doorStatus&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>231</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;doorUnlockDelayTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>232</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lockStatus&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>233</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;maskedAlarmValues&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>234</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;securedStatus&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>235</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;absenteeLimit&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>244</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accessAlarmEvents&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>245</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accessDoors&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>246</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accessEvent&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>247</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accessEventAuthenticationFactor&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>248</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accessEventCredential&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>249</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accessEventTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>250</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accessTransactionEvents&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>251</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accompaniment&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>252</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accompanimentTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>253</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;activationTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>254</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;activeAuthenticationPolicy&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>255</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;assignedAccessRights&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>256</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;authenticationFactors&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>257</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;authenticationPolicyList&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>258</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;authenticationPolicyNames&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>259</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;authenticationStatus&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>260</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;authorizationMode&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>261</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;belongsTo&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>262</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;credentialDisable&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>263</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;credentialStatus&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>264</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;credentials&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>265</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;credentialsInZone&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>266</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;daysRemaining&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>267</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;entryPoints&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>268</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;exitPoints&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>269</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;expiryTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>270</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;extendedTimeEnable&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>271</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;failedAttemptEvents&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>272</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;failedAttempts&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>273</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;failedAttemptsTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>274</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lastAccessEvent&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>275</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lastAccessPoint&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>276</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lastCredentialAdded&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>277</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lastCredentialAddedTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>278</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lastCredentialRemoved&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>279</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lastCredentialRemovedTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>280</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lastUseTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>281</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lockout&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>282</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lockoutRelinquishTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>283</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;maxFailedAttempts&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>285</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;members&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>286</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;musterPoint&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>287</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;negativeAccessRules&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>288</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;numberOfAuthenticationPolicies&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>289</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;occupancyCount&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>290</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;occupancyCountAdjust&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>291</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;occupancyCountEnable&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>292</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;occupancyLowerLimit&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>294</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;occupancyLowerLimitEnforced&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>295</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;occupancyState&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>296</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;occupancyUpperLimit&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>297</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;occupancyUpperLimitEnforced&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>298</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;passbackMode&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>300</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;passbackTimeout&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>301</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;positiveAccessRules&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>302</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;reasonForDisable&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>303</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;supportedFormats&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>304</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;supportedFormatClasses&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>305</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;threatAuthority&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>306</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;threatLevel&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>307</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;traceFlag&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>308</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;transactionNotificationClass&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>309</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;userExternalIdentifier&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>310</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;userInformationReference&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>311</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;userName&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>317</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;userType&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>318</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;usesRemaining&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>319</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;zoneFrom&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>320</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;zoneTo&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>321</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;accessEventTag&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>322</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;globalIdentifier&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>323</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;verificationTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>326</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;backupAndRestoreState&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>338</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;backupPreparationTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>339</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;restoreCompletionTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>340</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;restorePreparationTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>341</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bitMask&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>342</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bitText&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>343</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;isUtc&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>344</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;groupMembers&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>345</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;groupMemberNames&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>346</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;memberStatusDlags&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>347</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;requestedUpdateInterval&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>348</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;covuPeriod&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>349</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;covuRecipients&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>350</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;eventMessageTexts&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>351</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;eventMessageTextsConfig&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>352</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;eventDetectionEnable&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>353</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;eventAlgorithmInhibit&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>354</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;eventAlgorithmInhibitRef&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>355</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;timeDelayNormal&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>356</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;reliabilityEvaluationInhibit&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>357</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;faultParameters&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>358</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;faultType&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>359</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;localForwardingOnly&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>360</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;processIdentifierFilter&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>361</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;subscribedRecipients&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>362</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;portFilter&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>363</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;authorizationExemptions&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>364</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;allowGroupDelayInhibit&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>365</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;channelNumber&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>366</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;controlGroups&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>367</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;executionDelay&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>368</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lastPriority&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>369</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;writeStatus&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>370</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;propertyList&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>371</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;serialNumber&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>372</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;blinkWarnEnable&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>373</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;defaultFadetime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>374</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;defaultRamprate&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>375</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;defaultStepIncrement&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>376</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;egressTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>377</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;inProgress&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>378</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;instantaneousPower&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>379</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lightingCommand&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>380</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lightingCommandDefaultPriority&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>381</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;maxActualValue&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>382</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;minActualValue&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>383</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;power&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>384</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;transition&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>385</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;egressActive&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>386</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;interfaceValue&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>387</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;faultHighLimit&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>388</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;faultLowLimit&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>389</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lowDiffLimit&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>390</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;strikeCount&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>391</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;timeOfStrikeCountReset&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>392</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;defaultTimeout&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>393</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;initialTimeout&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>394</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lastStateChange&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>395</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;stateChangeValues&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>396</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;timerRunning&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>397</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;timerState&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>398</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;apduLength&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>399</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipAddress&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>400</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipDefaultGateway&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>401</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipDhcpEnable&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>402</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipDhcpLeaseTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>403</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipDhcpLeaseTimeRemaining&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>404</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipDhcpServer&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>405</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipDnsServer&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>406</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bacnetIpGlobalAddress&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>407</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bacnetIpMode&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>408</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bacnetIpMulticastAddress&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>409</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bacnetIpNatTraversal&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>410</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipSubnetMask&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>411</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bacnetIpUdpPort&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>412</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bbmdAcceptFdRegistrations&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>413</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bbmdBroadcastDistributionTable&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>414</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bbmdForeignDeviceTable&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>415</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;changesPending&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>416</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;command&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>417</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;fdBbmdAddress&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>418</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;fdSubscriptionLifetime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>419</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;linkSpeed&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>420</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;linkSpeeds&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>421</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;linkSpeedAutonegotiate&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>422</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;macAddress&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>423</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;networkInterfaceName&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>424</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;networkNumber&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>425</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;networkNumberQuality&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>426</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;networkType&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>427</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;routingTable&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>428</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;virtualMacAddressTable&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>429</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;commandTimeArray&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>430</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;currentCommandPriority&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>431</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lastCommandTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>432</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;valueSource&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>433</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;valueSourceArray&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>434</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bacnetIpv6Mode&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>435</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipv6Address&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>436</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipv6PrefixLength&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>437</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bacnetIpv6UdpPort&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>438</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipv6DefaultGateway&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>439</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bacnetIpv6MulticastAddress&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>440</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipv6DnsServer&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>441</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipv6AutoAddressingEnable&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>442</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipv6DhcpLeaseTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>443</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipv6DhcpLeaseTimeRemaining&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>444</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipv6DhcpServer&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>445</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ipv6ZoneIndex&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>446</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;assignedLandingCalls&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>447</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;carAssignedDirection&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>448</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;carDoorCommand&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>449</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;carDoorStatus&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>450</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;carDoorText&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>451</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;carDoorZone&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>452</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;carDriveStatus&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>453</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;carLoad&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>454</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;carLoadUnits&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>455</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;carMode&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>456</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;carMovingDirection&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>457</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;carPosition&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>458</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;elevatorGroup&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>459</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;energyMeter&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>460</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;energyMeterRef&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>461</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;escalatorMode&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>462</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;faultSignals&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>463</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;floorText&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>464</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;groupId&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>465</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;groupMode&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>467</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;higherDeck&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>468</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;installationId&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>469</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;landingCalls&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>470</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;landingCallControl&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>471</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;landingDoorStatus&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>472</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;lowerDeck&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>473</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;machineRoomId&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>474</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;makingCarCall&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>475</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;nextStoppingFloor&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>476</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;operationDirection&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>477</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;passengerAlarm&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>478</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;powerMode&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>479</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;registeredCarCall&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>480</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;activeCovMultipleSubscriptions&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>481</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;protocolLevel&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>482</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;referencePort&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>483</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deployedProfileLocation&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>484</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;profileLocation&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>485</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;tags&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>486</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;subordinateNodeTypes&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>487</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;subordinateTags&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>488</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;subordinateRelationships&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>489</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;defaultSubordinateRelationship&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>490</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;represents&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>491</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;defaultPresentValue&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>492</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;presentStage&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>493</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;stages&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>494</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;stageNames&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>495</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;targetReferences&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>496</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;auditSourceReporter&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>497</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;auditLevel&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>498</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;auditNotificationRecipient&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>499</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;auditPriorityFilter&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>500</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;auditableOperations&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>501</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deleteOnForward&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>502</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;maximumSendDelay&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>503</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;monitoredObjects&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>504</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;sendNow&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>505</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;floorNumber&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>506</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;deviceUuid&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>507</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;removed1&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>18</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;issueConfirmedNotifications&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>51</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;listOfSessionKeys&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>55</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;protocolConformanceClass&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>95</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;recipient&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>101</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;currentNotifyTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>129</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;previousNotifyTime&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>138</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;masterExemption&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>284</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;occupancyExemption&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>293</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;passbackExemption&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>299</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACKED_TRANSITIONS -->
<field name="ACKED_TRANSITIONS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ackedTransitions.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACK_REQUIRED -->
<field name="ACK_REQUIRED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ackRequired.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACTION -->
<field name="ACTION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for action.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACTION_TEXT -->
<field name="ACTION_TEXT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for actionText.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACTIVE_TEXT -->
<field name="ACTIVE_TEXT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for activeText.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACTIVE_VT_SESSIONS -->
<field name="ACTIVE_VT_SESSIONS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for activeVtSessions.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ALARM_VALUE -->
<field name="ALARM_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for alarmValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ALARM_VALUES -->
<field name="ALARM_VALUES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for alarmValues.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ALL -->
<field name="ALL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for all.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ALL_WRITES_SUCCESSFUL -->
<field name="ALL_WRITES_SUCCESSFUL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for allWritesSuccessful.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.APDU_SEGMENT_TIMEOUT -->
<field name="APDU_SEGMENT_TIMEOUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for apduSegmentTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.APDU_TIMEOUT -->
<field name="APDU_TIMEOUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for apduTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.APPLICATION_SOFTWARE_VERSION -->
<field name="APPLICATION_SOFTWARE_VERSION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for applicationSoftwareVersion.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ARCHIVE -->
<field name="ARCHIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for archive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BIAS -->
<field name="BIAS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bias.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CHANGE_OF_STATE_COUNT -->
<field name="CHANGE_OF_STATE_COUNT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for changeOfStateCount.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CHANGE_OF_STATE_TIME -->
<field name="CHANGE_OF_STATE_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for changeOfStateTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.NOTIFICATION_CLASS -->
<field name="NOTIFICATION_CLASS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for notificationClass.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CONTROLLED_VARIABLE_REFERENCE -->
<field name="CONTROLLED_VARIABLE_REFERENCE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for controlledVariableReference.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CONTROLLED_VARIABLE_UNITS -->
<field name="CONTROLLED_VARIABLE_UNITS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for controlledVariableUnits.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CONTROLLED_VARIABLE_VALUE -->
<field name="CONTROLLED_VARIABLE_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for controlledVariableValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.COV_INCREMENT -->
<field name="COV_INCREMENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for covIncrement.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DATE_LIST -->
<field name="DATE_LIST"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for dateList.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DAYLIGHT_SAVINGS_STATUS -->
<field name="DAYLIGHT_SAVINGS_STATUS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for daylightSavingsStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DEADBAND -->
<field name="DEADBAND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deadband.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DERIVATIVE_CONSTANT -->
<field name="DERIVATIVE_CONSTANT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for derivativeConstant.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DERIVATIVE_CONSTANT_UNITS -->
<field name="DERIVATIVE_CONSTANT_UNITS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for derivativeConstantUnits.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DESCRIPTION -->
<field name="DESCRIPTION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for description.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DESCRIPTION_OF_HALT -->
<field name="DESCRIPTION_OF_HALT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for descriptionOfHalt.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DEVICE_ADDRESS_BINDING -->
<field name="DEVICE_ADDRESS_BINDING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deviceAddressBinding.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DEVICE_TYPE -->
<field name="DEVICE_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deviceType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EFFECTIVE_PERIOD -->
<field name="EFFECTIVE_PERIOD"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for effectivePeriod.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ELAPSED_ACTIVE_TIME -->
<field name="ELAPSED_ACTIVE_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for elapsedActiveTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ERROR_LIMIT -->
<field name="ERROR_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for errorLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EVENT_ENABLE -->
<field name="EVENT_ENABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for eventEnable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EVENT_STATE -->
<field name="EVENT_STATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for eventState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EVENT_TYPE -->
<field name="EVENT_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for eventType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EXCEPTION_SCHEDULE -->
<field name="EXCEPTION_SCHEDULE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for exceptionSchedule.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FAULT_VALUES -->
<field name="FAULT_VALUES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for faultValues.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FEEDBACK_VALUE -->
<field name="FEEDBACK_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for feedbackValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FILE_ACCESS_METHOD -->
<field name="FILE_ACCESS_METHOD"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for fileAccessMethod.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FILE_SIZE -->
<field name="FILE_SIZE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for fileSize.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FILE_TYPE -->
<field name="FILE_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for fileType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FIRMWARE_REVISION -->
<field name="FIRMWARE_REVISION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for firmwareRevision.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.HIGH_LIMIT -->
<field name="HIGH_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for highLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.INACTIVE_TEXT -->
<field name="INACTIVE_TEXT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for inactiveText.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IN_PROCESS -->
<field name="IN_PROCESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for inProcess.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.INSTANCE_OF -->
<field name="INSTANCE_OF"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for instanceOf.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.INTEGRAL_CONSTANT -->
<field name="INTEGRAL_CONSTANT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for integralConstant.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.INTEGRAL_CONSTANT_UNITS -->
<field name="INTEGRAL_CONSTANT_UNITS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for integralConstantUnits.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LIMIT_ENABLE -->
<field name="LIMIT_ENABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for limitEnable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LIST_OF_GROUP_MEMBERS -->
<field name="LIST_OF_GROUP_MEMBERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for listOfGroupMembers.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LIST_OF_OBJECT_PROPERTY_REFERENCES -->
<field name="LIST_OF_OBJECT_PROPERTY_REFERENCES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for listOfObjectPropertyReferences.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LOCAL_DATE -->
<field name="LOCAL_DATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for localDate.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LOCAL_TIME -->
<field name="LOCAL_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for localTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LOCATION -->
<field name="LOCATION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for location.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LOW_LIMIT -->
<field name="LOW_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lowLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MANIPULATED_VARIABLE_REFERENCE -->
<field name="MANIPULATED_VARIABLE_REFERENCE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for manipulatedVariableReference.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MAXIMUM_OUTPUT -->
<field name="MAXIMUM_OUTPUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for maximumOutput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MAX_APDU_LENGTH_ACCEPTED -->
<field name="MAX_APDU_LENGTH_ACCEPTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for maxApduLengthAccepted.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MAX_INFO_FRAMES -->
<field name="MAX_INFO_FRAMES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for maxInfoFrames.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MAX_MASTER -->
<field name="MAX_MASTER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for maxMaster.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MAX_PRES_VALUE -->
<field name="MAX_PRES_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for maxPresValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MINIMUM_OFF_TIME -->
<field name="MINIMUM_OFF_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for minimumOffTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MINIMUM_ON_TIME -->
<field name="MINIMUM_ON_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for minimumOnTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MINIMUM_OUTPUT -->
<field name="MINIMUM_OUTPUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for minimumOutput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MIN_PRES_VALUE -->
<field name="MIN_PRES_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for minPresValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MODEL_NAME -->
<field name="MODEL_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for modelName.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MODIFICATION_DATE -->
<field name="MODIFICATION_DATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for modificationDate.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.NOTIFY_TYPE -->
<field name="NOTIFY_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for notifyType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.NUMBER_OF_APDU_RETRIES -->
<field name="NUMBER_OF_APDU_RETRIES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for numberOfApduRetries.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.NUMBER_OF_STATES -->
<field name="NUMBER_OF_STATES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for numberOfStates.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OBJECT_IDENTIFIER -->
<field name="OBJECT_IDENTIFIER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for objectIdentifier.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OBJECT_LIST -->
<field name="OBJECT_LIST"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for objectList.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OBJECT_NAME -->
<field name="OBJECT_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for objectName.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OBJECT_PROPERTY_REFERENCE -->
<field name="OBJECT_PROPERTY_REFERENCE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for objectPropertyReference.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OBJECT_TYPE -->
<field name="OBJECT_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for objectType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OPTIONAL -->
<field name="OPTIONAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for optional.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OUT_OF_SERVICE -->
<field name="OUT_OF_SERVICE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for outOfService.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OUTPUT_UNITS -->
<field name="OUTPUT_UNITS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for outputUnits.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EVENT_PARAMETERS -->
<field name="EVENT_PARAMETERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for eventParameters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.POLARITY -->
<field name="POLARITY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for polarity.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PRESENT_VALUE -->
<field name="PRESENT_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for presentValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PRIORITY -->
<field name="PRIORITY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for priority.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PRIORITY_ARRAY -->
<field name="PRIORITY_ARRAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for priorityArray.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PRIORITY_FOR_WRITING -->
<field name="PRIORITY_FOR_WRITING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for priorityForWriting.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PROCESS_IDENTIFIER -->
<field name="PROCESS_IDENTIFIER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for processIdentifier.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PROGRAM_CHANGE -->
<field name="PROGRAM_CHANGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for programChange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PROGRAM_LOCATION -->
<field name="PROGRAM_LOCATION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for programLocation.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PROGRAM_STATE -->
<field name="PROGRAM_STATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for programState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PROPORTIONAL_CONSTANT -->
<field name="PROPORTIONAL_CONSTANT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for proportionalConstant.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PROPORTIONAL_CONSTANT_UNITS -->
<field name="PROPORTIONAL_CONSTANT_UNITS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for proportionalConstantUnits.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PROTOCOL_OBJECT_TYPES_SUPPORTED -->
<field name="PROTOCOL_OBJECT_TYPES_SUPPORTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for protocolObjectTypesSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PROTOCOL_SERVICES_SUPPORTED -->
<field name="PROTOCOL_SERVICES_SUPPORTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for protocolServicesSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PROTOCOL_VERSION -->
<field name="PROTOCOL_VERSION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for protocolVersion.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.READ_ONLY -->
<field name="READ_ONLY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for readOnly.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.REASON_FOR_HALT -->
<field name="REASON_FOR_HALT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for reasonForHalt.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.RECIPIENT_LIST -->
<field name="RECIPIENT_LIST"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for recipientList.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.RELIABILITY -->
<field name="RELIABILITY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for reliability.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.RELINQUISH_DEFAULT -->
<field name="RELINQUISH_DEFAULT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for relinquishDefault.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.REQUIRED -->
<field name="REQUIRED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for required.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.RESOLUTION -->
<field name="RESOLUTION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for resolution.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SEGMENTATION_SUPPORTED -->
<field name="SEGMENTATION_SUPPORTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for segmentationSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SETPOINT -->
<field name="SETPOINT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for setpoint.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SETPOINT_REFERENCE -->
<field name="SETPOINT_REFERENCE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for setpointReference.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.STATE_TEXT -->
<field name="STATE_TEXT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for stateText.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.STATUS_FLAGS -->
<field name="STATUS_FLAGS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for statusFlags.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SYSTEM_STATUS -->
<field name="SYSTEM_STATUS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for systemStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TIME_DELAY -->
<field name="TIME_DELAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for timeDelay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TIME_OF_ACTIVE_TIME_RESET -->
<field name="TIME_OF_ACTIVE_TIME_RESET"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for timeOfActiveTimeReset.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TIME_OF_STATE_COUNT_RESET -->
<field name="TIME_OF_STATE_COUNT_RESET"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for timeOfStateCountReset.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TIME_SYNCHRONIZATION_RECIPIENTS -->
<field name="TIME_SYNCHRONIZATION_RECIPIENTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for timeSynchronizationRecipients.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.UNITS -->
<field name="UNITS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for units.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.UPDATE_INTERVAL -->
<field name="UPDATE_INTERVAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for updateInterval.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.UTC_OFFSET -->
<field name="UTC_OFFSET"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for utcOffset.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.VENDOR_IDENTIFIER -->
<field name="VENDOR_IDENTIFIER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for vendorIdentifier.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.VENDOR_NAME -->
<field name="VENDOR_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for vendorName.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.VT_CLASSES_SUPPORTED -->
<field name="VT_CLASSES_SUPPORTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for vtClassesSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.WEEKLY_SCHEDULE -->
<field name="WEEKLY_SCHEDULE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for weeklySchedule.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ATTEMPTED_SAMPLES -->
<field name="ATTEMPTED_SAMPLES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for attemptedSamples.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.AVERAGE_VALUE -->
<field name="AVERAGE_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for averageValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BUFFER_SIZE -->
<field name="BUFFER_SIZE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bufferSize.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CLIENT_COV_INCREMENT -->
<field name="CLIENT_COV_INCREMENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for clientCovIncrement.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.COV_RESUBSCRIPTION_INTERVAL -->
<field name="COV_RESUBSCRIPTION_INTERVAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for covResubscriptionInterval.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EVENT_TIME_STAMPS -->
<field name="EVENT_TIME_STAMPS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for eventTimeStamps.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LOG_BUFFER -->
<field name="LOG_BUFFER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for logBuffer.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LOG_DEVICE_OBJECT_PROPERTY -->
<field name="LOG_DEVICE_OBJECT_PROPERTY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for logDeviceObjectProperty.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ENABLE -->
<field name="ENABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for enable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LOG_INTERVAL -->
<field name="LOG_INTERVAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for logInterval.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MAXIMUM_VALUE -->
<field name="MAXIMUM_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for maximumValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MINIMUM_VALUE -->
<field name="MINIMUM_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for minimumValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.NOTIFICATION_THRESHOLD -->
<field name="NOTIFICATION_THRESHOLD"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for notificationThreshold.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PROTOCOL_REVISION -->
<field name="PROTOCOL_REVISION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for protocolRevision.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.RECORDS_SINCE_NOTIFICATION -->
<field name="RECORDS_SINCE_NOTIFICATION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for recordsSinceNotification.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.RECORD_COUNT -->
<field name="RECORD_COUNT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for recordCount.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.START_TIME -->
<field name="START_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for startTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.STOP_TIME -->
<field name="STOP_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for stopTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.STOP_WHEN_FULL -->
<field name="STOP_WHEN_FULL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for stopWhenFull.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TOTAL_RECORD_COUNT -->
<field name="TOTAL_RECORD_COUNT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for totalRecordCount.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.VALID_SAMPLES -->
<field name="VALID_SAMPLES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for validSamples.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.WINDOW_INTERVAL -->
<field name="WINDOW_INTERVAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for windowInterval.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.WINDOW_SAMPLES -->
<field name="WINDOW_SAMPLES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for windowSamples.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MAXIMUM_VALUE_TIMESTAMP -->
<field name="MAXIMUM_VALUE_TIMESTAMP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for maximumValueTimestamp.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MINIMUM_VALUE_TIMESTAMP -->
<field name="MINIMUM_VALUE_TIMESTAMP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for minimumValueTimestamp.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.VARIANCE_VALUE -->
<field name="VARIANCE_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for varianceValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACTIVE_COV_SUBSCRIPTIONS -->
<field name="ACTIVE_COV_SUBSCRIPTIONS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for activeCovSubscriptions.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BACKUP_FAILURE_TIMEOUT -->
<field name="BACKUP_FAILURE_TIMEOUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for backupFailureTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CONFIGURATION_FILES -->
<field name="CONFIGURATION_FILES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for configurationFiles.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DATABASE_REVISION -->
<field name="DATABASE_REVISION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for databaseRevision.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DIRECT_READING -->
<field name="DIRECT_READING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for directReading.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LAST_RESTORE_TIME -->
<field name="LAST_RESTORE_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lastRestoreTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MAINTENANCE_REQUIRED -->
<field name="MAINTENANCE_REQUIRED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for maintenanceRequired.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MEMBER_OF -->
<field name="MEMBER_OF"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for memberOf.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MODE -->
<field name="MODE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for mode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OPERATION_EXPECTED -->
<field name="OPERATION_EXPECTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for operationExpected.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SETTING -->
<field name="SETTING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for setting.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SILENCED -->
<field name="SILENCED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for silenced.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TRACKING_VALUE -->
<field name="TRACKING_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for trackingValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ZONE_MEMBERS -->
<field name="ZONE_MEMBERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for zoneMembers.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LIFE_SAFETY_ALARM_VALUES -->
<field name="LIFE_SAFETY_ALARM_VALUES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lifeSafetyAlarmValues.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MAX_SEGMENTS_ACCEPTED -->
<field name="MAX_SEGMENTS_ACCEPTED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for maxSegmentsAccepted.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PROFILE_NAME -->
<field name="PROFILE_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for profileName.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.AUTO_SLAVE_DISCOVERY -->
<field name="AUTO_SLAVE_DISCOVERY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for autoSlaveDiscovery.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MANUAL_SLAVE_ADDRESS_BINDING -->
<field name="MANUAL_SLAVE_ADDRESS_BINDING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for manualSlaveAddressBinding.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SLAVE_ADDRESS_BINDING -->
<field name="SLAVE_ADDRESS_BINDING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for slaveAddressBinding.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SLAVE_PROXY_ENABLE -->
<field name="SLAVE_PROXY_ENABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for slaveProxyEnable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LAST_NOTIFY_RECORD -->
<field name="LAST_NOTIFY_RECORD"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lastNotifyRecord.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SCHEDULE_DEFAULT -->
<field name="SCHEDULE_DEFAULT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for scheduleDefault.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACCEPTED_MODES -->
<field name="ACCEPTED_MODES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for acceptedModes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ADJUST_VALUE -->
<field name="ADJUST_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for adjustValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.COUNT -->
<field name="COUNT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for count.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.COUNT_BEFORE_CHANGE -->
<field name="COUNT_BEFORE_CHANGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for countBeforeChange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.COUNT_CHANGE_TIME -->
<field name="COUNT_CHANGE_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for countChangeTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.COV_PERIOD -->
<field name="COV_PERIOD"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for covPeriod.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.INPUT_REFERENCE -->
<field name="INPUT_REFERENCE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for inputReference.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LIMIT_MONITORING_INTERVAL -->
<field name="LIMIT_MONITORING_INTERVAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for limitMonitoringInterval.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LOGGING_OBJECT -->
<field name="LOGGING_OBJECT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for loggingObject.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LOGGING_RECORD -->
<field name="LOGGING_RECORD"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for loggingRecord.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PRESCALE -->
<field name="PRESCALE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for prescale.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PULSE_RATE -->
<field name="PULSE_RATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for pulseRate.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SCALE -->
<field name="SCALE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for scale.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SCALE_FACTOR -->
<field name="SCALE_FACTOR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for scaleFactor.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.UPDATE_TIME -->
<field name="UPDATE_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for updateTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.VALUE_BEFORE_CHANGE -->
<field name="VALUE_BEFORE_CHANGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for valueBeforeChange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.VALUE_SET -->
<field name="VALUE_SET"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for valueSet.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.VALUE_CHANGE_TIME -->
<field name="VALUE_CHANGE_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for valueChangeTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ALIGN_INTERVALS -->
<field name="ALIGN_INTERVALS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for alignIntervals.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.INTERVAL_OFFSET -->
<field name="INTERVAL_OFFSET"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for intervalOffset.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LAST_RESTART_REASON -->
<field name="LAST_RESTART_REASON"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lastRestartReason.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LOGGING_TYPE -->
<field name="LOGGING_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for loggingType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.RESTART_NOTIFICATION_RECIPIENTS -->
<field name="RESTART_NOTIFICATION_RECIPIENTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for restartNotificationRecipients.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TIME_OF_DEVICE_RESTART -->
<field name="TIME_OF_DEVICE_RESTART"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for timeOfDeviceRestart.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TIME_SYNCHRONIZATION_INTERVAL -->
<field name="TIME_SYNCHRONIZATION_INTERVAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for timeSynchronizationInterval.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TRIGGER -->
<field name="TRIGGER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for trigger.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.UTC_TIME_SYNCHRONIZATION_RECIPIENTS -->
<field name="UTC_TIME_SYNCHRONIZATION_RECIPIENTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for utcTimeSynchronizationRecipients.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.NODE_SUBTYPE -->
<field name="NODE_SUBTYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for nodeSubtype.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.NODE_TYPE -->
<field name="NODE_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for nodeType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.STRUCTURED_OBJECT_LIST -->
<field name="STRUCTURED_OBJECT_LIST"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for structuredObjectList.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SUBORDINATE_ANNOTATIONS -->
<field name="SUBORDINATE_ANNOTATIONS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for subordinateAnnotations.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SUBORDINATE_LIST -->
<field name="SUBORDINATE_LIST"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for subordinateList.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACTUAL_SHED_LEVEL -->
<field name="ACTUAL_SHED_LEVEL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for actualShedLevel.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DUTY_WINDOW -->
<field name="DUTY_WINDOW"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for dutyWindow.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EXPECTED_SHED_LEVEL -->
<field name="EXPECTED_SHED_LEVEL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for expectedShedLevel.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FULL_DUTY_BASELINE -->
<field name="FULL_DUTY_BASELINE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for fullDutyBaseline.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.REQUESTED_SHED_LEVEL -->
<field name="REQUESTED_SHED_LEVEL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for requestedShedLevel.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SHED_DURATION -->
<field name="SHED_DURATION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for shedDuration.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SHED_LEVEL_DESCRIPTIONS -->
<field name="SHED_LEVEL_DESCRIPTIONS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for shedLevelDescriptions.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SHED_LEVELS -->
<field name="SHED_LEVELS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for shedLevels.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.STATE_DESCRIPTION -->
<field name="STATE_DESCRIPTION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for stateDescription.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DOOR_ALARM_STATE -->
<field name="DOOR_ALARM_STATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for doorAlarmState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DOOR_EXTENDED_PULSE_TIME -->
<field name="DOOR_EXTENDED_PULSE_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for doorExtendedPulseTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DOOR_MEMBERS -->
<field name="DOOR_MEMBERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for doorMembers.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DOOR_OPEN_TOO_LONG_TIME -->
<field name="DOOR_OPEN_TOO_LONG_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for doorOpenTooLongTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DOOR_PULSE_TIME -->
<field name="DOOR_PULSE_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for doorPulseTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DOOR_STATUS -->
<field name="DOOR_STATUS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for doorStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DOOR_UNLOCK_DELAY_TIME -->
<field name="DOOR_UNLOCK_DELAY_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for doorUnlockDelayTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LOCK_STATUS -->
<field name="LOCK_STATUS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lockStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MASKED_ALARM_VALUES -->
<field name="MASKED_ALARM_VALUES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for maskedAlarmValues.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SECURED_STATUS -->
<field name="SECURED_STATUS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for securedStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ABSENTEE_LIMIT -->
<field name="ABSENTEE_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for absenteeLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACCESS_ALARM_EVENTS -->
<field name="ACCESS_ALARM_EVENTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accessAlarmEvents.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACCESS_DOORS -->
<field name="ACCESS_DOORS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accessDoors.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACCESS_EVENT -->
<field name="ACCESS_EVENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accessEvent.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACCESS_EVENT_AUTHENTICATION_FACTOR -->
<field name="ACCESS_EVENT_AUTHENTICATION_FACTOR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accessEventAuthenticationFactor.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACCESS_EVENT_CREDENTIAL -->
<field name="ACCESS_EVENT_CREDENTIAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accessEventCredential.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACCESS_EVENT_TIME -->
<field name="ACCESS_EVENT_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accessEventTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACCESS_TRANSACTION_EVENTS -->
<field name="ACCESS_TRANSACTION_EVENTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accessTransactionEvents.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACCOMPANIMENT -->
<field name="ACCOMPANIMENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accompaniment.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACCOMPANIMENT_TIME -->
<field name="ACCOMPANIMENT_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accompanimentTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACTIVATION_TIME -->
<field name="ACTIVATION_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for activationTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACTIVE_AUTHENTICATION_POLICY -->
<field name="ACTIVE_AUTHENTICATION_POLICY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for activeAuthenticationPolicy.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ASSIGNED_ACCESS_RIGHTS -->
<field name="ASSIGNED_ACCESS_RIGHTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for assignedAccessRights.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.AUTHENTICATION_FACTORS -->
<field name="AUTHENTICATION_FACTORS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for authenticationFactors.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.AUTHENTICATION_POLICY_LIST -->
<field name="AUTHENTICATION_POLICY_LIST"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for authenticationPolicyList.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.AUTHENTICATION_POLICY_NAMES -->
<field name="AUTHENTICATION_POLICY_NAMES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for authenticationPolicyNames.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.AUTHENTICATION_STATUS -->
<field name="AUTHENTICATION_STATUS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for authenticationStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.AUTHORIZATION_MODE -->
<field name="AUTHORIZATION_MODE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for authorizationMode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BELONGS_TO -->
<field name="BELONGS_TO"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for belongsTo.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CREDENTIAL_DISABLE -->
<field name="CREDENTIAL_DISABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for credentialDisable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CREDENTIAL_STATUS -->
<field name="CREDENTIAL_STATUS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for credentialStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CREDENTIALS -->
<field name="CREDENTIALS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for credentials.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CREDENTIALS_IN_ZONE -->
<field name="CREDENTIALS_IN_ZONE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for credentialsInZone.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DAYS_REMAINING -->
<field name="DAYS_REMAINING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for daysRemaining.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ENTRY_POINTS -->
<field name="ENTRY_POINTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for entryPoints.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EXIT_POINTS -->
<field name="EXIT_POINTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for exitPoints.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EXPIRY_TIME -->
<field name="EXPIRY_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for expiryTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EXTENDED_TIME_ENABLE -->
<field name="EXTENDED_TIME_ENABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for extendedTimeEnable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FAILED_ATTEMPT_EVENTS -->
<field name="FAILED_ATTEMPT_EVENTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for failedAttemptEvents.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FAILED_ATTEMPTS -->
<field name="FAILED_ATTEMPTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for failedAttempts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FAILED_ATTEMPTS_TIME -->
<field name="FAILED_ATTEMPTS_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for failedAttemptsTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LAST_ACCESS_EVENT -->
<field name="LAST_ACCESS_EVENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lastAccessEvent.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LAST_ACCESS_POINT -->
<field name="LAST_ACCESS_POINT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lastAccessPoint.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LAST_CREDENTIAL_ADDED -->
<field name="LAST_CREDENTIAL_ADDED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lastCredentialAdded.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LAST_CREDENTIAL_ADDED_TIME -->
<field name="LAST_CREDENTIAL_ADDED_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lastCredentialAddedTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LAST_CREDENTIAL_REMOVED -->
<field name="LAST_CREDENTIAL_REMOVED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lastCredentialRemoved.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LAST_CREDENTIAL_REMOVED_TIME -->
<field name="LAST_CREDENTIAL_REMOVED_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lastCredentialRemovedTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LAST_USE_TIME -->
<field name="LAST_USE_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lastUseTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LOCKOUT -->
<field name="LOCKOUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lockout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LOCKOUT_RELINQUISH_TIME -->
<field name="LOCKOUT_RELINQUISH_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lockoutRelinquishTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MAX_FAILED_ATTEMPTS -->
<field name="MAX_FAILED_ATTEMPTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for maxFailedAttempts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MEMBERS -->
<field name="MEMBERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for members.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MUSTER_POINT -->
<field name="MUSTER_POINT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for musterPoint.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.NEGATIVE_ACCESS_RULES -->
<field name="NEGATIVE_ACCESS_RULES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for negativeAccessRules.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.NUMBER_OF_AUTHENTICATION_POLICIES -->
<field name="NUMBER_OF_AUTHENTICATION_POLICIES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for numberOfAuthenticationPolicies.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OCCUPANCY_COUNT -->
<field name="OCCUPANCY_COUNT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for occupancyCount.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OCCUPANCY_COUNT_ADJUST -->
<field name="OCCUPANCY_COUNT_ADJUST"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for occupancyCountAdjust.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OCCUPANCY_COUNT_ENABLE -->
<field name="OCCUPANCY_COUNT_ENABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for occupancyCountEnable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OCCUPANCY_LOWER_LIMIT -->
<field name="OCCUPANCY_LOWER_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for occupancyLowerLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OCCUPANCY_LOWER_LIMIT_ENFORCED -->
<field name="OCCUPANCY_LOWER_LIMIT_ENFORCED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for occupancyLowerLimitEnforced.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OCCUPANCY_STATE -->
<field name="OCCUPANCY_STATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for occupancyState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OCCUPANCY_UPPER_LIMIT -->
<field name="OCCUPANCY_UPPER_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for occupancyUpperLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OCCUPANCY_UPPER_LIMIT_ENFORCED -->
<field name="OCCUPANCY_UPPER_LIMIT_ENFORCED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for occupancyUpperLimitEnforced.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PASSBACK_MODE -->
<field name="PASSBACK_MODE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for passbackMode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PASSBACK_TIMEOUT -->
<field name="PASSBACK_TIMEOUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for passbackTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.POSITIVE_ACCESS_RULES -->
<field name="POSITIVE_ACCESS_RULES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for positiveAccessRules.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.REASON_FOR_DISABLE -->
<field name="REASON_FOR_DISABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for reasonForDisable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SUPPORTED_FORMATS -->
<field name="SUPPORTED_FORMATS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for supportedFormats.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SUPPORTED_FORMAT_CLASSES -->
<field name="SUPPORTED_FORMAT_CLASSES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for supportedFormatClasses.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.THREAT_AUTHORITY -->
<field name="THREAT_AUTHORITY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for threatAuthority.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.THREAT_LEVEL -->
<field name="THREAT_LEVEL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for threatLevel.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TRACE_FLAG -->
<field name="TRACE_FLAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for traceFlag.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TRANSACTION_NOTIFICATION_CLASS -->
<field name="TRANSACTION_NOTIFICATION_CLASS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for transactionNotificationClass.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.USER_EXTERNAL_IDENTIFIER -->
<field name="USER_EXTERNAL_IDENTIFIER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for userExternalIdentifier.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.USER_INFORMATION_REFERENCE -->
<field name="USER_INFORMATION_REFERENCE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for userInformationReference.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.USER_NAME -->
<field name="USER_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for userName.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.USER_TYPE -->
<field name="USER_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for userType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.USES_REMAINING -->
<field name="USES_REMAINING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for usesRemaining.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ZONE_FROM -->
<field name="ZONE_FROM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for zoneFrom.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ZONE_TO -->
<field name="ZONE_TO"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for zoneTo.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACCESS_EVENT_TAG -->
<field name="ACCESS_EVENT_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for accessEventTag.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.GLOBAL_IDENTIFIER -->
<field name="GLOBAL_IDENTIFIER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for globalIdentifier.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.VERIFICATION_TIME -->
<field name="VERIFICATION_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for verificationTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BACKUP_AND_RESTORE_STATE -->
<field name="BACKUP_AND_RESTORE_STATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for backupAndRestoreState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BACKUP_PREPARATION_TIME -->
<field name="BACKUP_PREPARATION_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for backupPreparationTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.RESTORE_COMPLETION_TIME -->
<field name="RESTORE_COMPLETION_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for restoreCompletionTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.RESTORE_PREPARATION_TIME -->
<field name="RESTORE_PREPARATION_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for restorePreparationTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BIT_MASK -->
<field name="BIT_MASK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bitMask.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BIT_TEXT -->
<field name="BIT_TEXT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bitText.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IS_UTC -->
<field name="IS_UTC"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for isUtc.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.GROUP_MEMBERS -->
<field name="GROUP_MEMBERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for groupMembers.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.GROUP_MEMBER_NAMES -->
<field name="GROUP_MEMBER_NAMES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for groupMemberNames.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MEMBER_STATUS_DLAGS -->
<field name="MEMBER_STATUS_DLAGS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for memberStatusDlags.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.REQUESTED_UPDATE_INTERVAL -->
<field name="REQUESTED_UPDATE_INTERVAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for requestedUpdateInterval.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.COVU_PERIOD -->
<field name="COVU_PERIOD"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for covuPeriod.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.COVU_RECIPIENTS -->
<field name="COVU_RECIPIENTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for covuRecipients.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EVENT_MESSAGE_TEXTS -->
<field name="EVENT_MESSAGE_TEXTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for eventMessageTexts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EVENT_MESSAGE_TEXTS_CONFIG -->
<field name="EVENT_MESSAGE_TEXTS_CONFIG"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for eventMessageTextsConfig.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EVENT_DETECTION_ENABLE -->
<field name="EVENT_DETECTION_ENABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for eventDetectionEnable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EVENT_ALGORITHM_INHIBIT -->
<field name="EVENT_ALGORITHM_INHIBIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for eventAlgorithmInhibit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EVENT_ALGORITHM_INHIBIT_REF -->
<field name="EVENT_ALGORITHM_INHIBIT_REF"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for eventAlgorithmInhibitRef.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TIME_DELAY_NORMAL -->
<field name="TIME_DELAY_NORMAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for timeDelayNormal.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.RELIABILITY_EVALUATION_INHIBIT -->
<field name="RELIABILITY_EVALUATION_INHIBIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for reliabilityEvaluationInhibit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FAULT_PARAMETERS -->
<field name="FAULT_PARAMETERS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for faultParameters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FAULT_TYPE -->
<field name="FAULT_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for faultType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LOCAL_FORWARDING_ONLY -->
<field name="LOCAL_FORWARDING_ONLY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for localForwardingOnly.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PROCESS_IDENTIFIER_FILTER -->
<field name="PROCESS_IDENTIFIER_FILTER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for processIdentifierFilter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SUBSCRIBED_RECIPIENTS -->
<field name="SUBSCRIBED_RECIPIENTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for subscribedRecipients.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PORT_FILTER -->
<field name="PORT_FILTER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for portFilter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.AUTHORIZATION_EXEMPTIONS -->
<field name="AUTHORIZATION_EXEMPTIONS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for authorizationExemptions.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ALLOW_GROUP_DELAY_INHIBIT -->
<field name="ALLOW_GROUP_DELAY_INHIBIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for allowGroupDelayInhibit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CHANNEL_NUMBER -->
<field name="CHANNEL_NUMBER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for channelNumber.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CONTROL_GROUPS -->
<field name="CONTROL_GROUPS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for controlGroups.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EXECUTION_DELAY -->
<field name="EXECUTION_DELAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for executionDelay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LAST_PRIORITY -->
<field name="LAST_PRIORITY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lastPriority.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.WRITE_STATUS -->
<field name="WRITE_STATUS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for writeStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PROPERTY_LIST -->
<field name="PROPERTY_LIST"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for propertyList.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SERIAL_NUMBER -->
<field name="SERIAL_NUMBER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for serialNumber.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BLINK_WARN_ENABLE -->
<field name="BLINK_WARN_ENABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for blinkWarnEnable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DEFAULT_FADETIME -->
<field name="DEFAULT_FADETIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for defaultFadetime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DEFAULT_RAMPRATE -->
<field name="DEFAULT_RAMPRATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for defaultRamprate.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DEFAULT_STEP_INCREMENT -->
<field name="DEFAULT_STEP_INCREMENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for defaultStepIncrement.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EGRESS_TIME -->
<field name="EGRESS_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for egressTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IN_PROGRESS -->
<field name="IN_PROGRESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for inProgress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.INSTANTANEOUS_POWER -->
<field name="INSTANTANEOUS_POWER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for instantaneousPower.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LIGHTING_COMMAND -->
<field name="LIGHTING_COMMAND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lightingCommand.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LIGHTING_COMMAND_DEFAULT_PRIORITY -->
<field name="LIGHTING_COMMAND_DEFAULT_PRIORITY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lightingCommandDefaultPriority.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MAX_ACTUAL_VALUE -->
<field name="MAX_ACTUAL_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for maxActualValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MIN_ACTUAL_VALUE -->
<field name="MIN_ACTUAL_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for minActualValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.POWER -->
<field name="POWER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for power.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TRANSITION -->
<field name="TRANSITION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for transition.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.EGRESS_ACTIVE -->
<field name="EGRESS_ACTIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for egressActive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.INTERFACE_VALUE -->
<field name="INTERFACE_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for interfaceValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FAULT_HIGH_LIMIT -->
<field name="FAULT_HIGH_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for faultHighLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FAULT_LOW_LIMIT -->
<field name="FAULT_LOW_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for faultLowLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LOW_DIFF_LIMIT -->
<field name="LOW_DIFF_LIMIT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lowDiffLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.STRIKE_COUNT -->
<field name="STRIKE_COUNT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for strikeCount.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TIME_OF_STRIKE_COUNT_RESET -->
<field name="TIME_OF_STRIKE_COUNT_RESET"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for timeOfStrikeCountReset.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DEFAULT_TIMEOUT -->
<field name="DEFAULT_TIMEOUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for defaultTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.INITIAL_TIMEOUT -->
<field name="INITIAL_TIMEOUT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for initialTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LAST_STATE_CHANGE -->
<field name="LAST_STATE_CHANGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lastStateChange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.STATE_CHANGE_VALUES -->
<field name="STATE_CHANGE_VALUES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for stateChangeValues.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TIMER_RUNNING -->
<field name="TIMER_RUNNING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for timerRunning.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TIMER_STATE -->
<field name="TIMER_STATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for timerState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.APDU_LENGTH -->
<field name="APDU_LENGTH"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for apduLength.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IP_ADDRESS -->
<field name="IP_ADDRESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipAddress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IP_DEFAULT_GATEWAY -->
<field name="IP_DEFAULT_GATEWAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipDefaultGateway.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IP_DHCP_ENABLE -->
<field name="IP_DHCP_ENABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipDhcpEnable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IP_DHCP_LEASE_TIME -->
<field name="IP_DHCP_LEASE_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipDhcpLeaseTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IP_DHCP_LEASE_TIME_REMAINING -->
<field name="IP_DHCP_LEASE_TIME_REMAINING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipDhcpLeaseTimeRemaining.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IP_DHCP_SERVER -->
<field name="IP_DHCP_SERVER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipDhcpServer.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IP_DNS_SERVER -->
<field name="IP_DNS_SERVER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipDnsServer.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BACNET_IP_GLOBAL_ADDRESS -->
<field name="BACNET_IP_GLOBAL_ADDRESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bacnetIpGlobalAddress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BACNET_IP_MODE -->
<field name="BACNET_IP_MODE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bacnetIpMode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BACNET_IP_MULTICAST_ADDRESS -->
<field name="BACNET_IP_MULTICAST_ADDRESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bacnetIpMulticastAddress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BACNET_IP_NAT_TRAVERSAL -->
<field name="BACNET_IP_NAT_TRAVERSAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bacnetIpNatTraversal.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IP_SUBNET_MASK -->
<field name="IP_SUBNET_MASK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipSubnetMask.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BACNET_IP_UDP_PORT -->
<field name="BACNET_IP_UDP_PORT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bacnetIpUdpPort.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BBMD_ACCEPT_FD_REGISTRATIONS -->
<field name="BBMD_ACCEPT_FD_REGISTRATIONS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bbmdAcceptFdRegistrations.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BBMD_BROADCAST_DISTRIBUTION_TABLE -->
<field name="BBMD_BROADCAST_DISTRIBUTION_TABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bbmdBroadcastDistributionTable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BBMD_FOREIGN_DEVICE_TABLE -->
<field name="BBMD_FOREIGN_DEVICE_TABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bbmdForeignDeviceTable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CHANGES_PENDING -->
<field name="CHANGES_PENDING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for changesPending.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.COMMAND -->
<field name="COMMAND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for command.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FD_BBMD_ADDRESS -->
<field name="FD_BBMD_ADDRESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for fdBbmdAddress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FD_SUBSCRIPTION_LIFETIME -->
<field name="FD_SUBSCRIPTION_LIFETIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for fdSubscriptionLifetime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LINK_SPEED -->
<field name="LINK_SPEED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for linkSpeed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LINK_SPEEDS -->
<field name="LINK_SPEEDS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for linkSpeeds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LINK_SPEED_AUTONEGOTIATE -->
<field name="LINK_SPEED_AUTONEGOTIATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for linkSpeedAutonegotiate.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MAC_ADDRESS -->
<field name="MAC_ADDRESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for macAddress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.NETWORK_INTERFACE_NAME -->
<field name="NETWORK_INTERFACE_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for networkInterfaceName.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.NETWORK_NUMBER -->
<field name="NETWORK_NUMBER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for networkNumber.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.NETWORK_NUMBER_QUALITY -->
<field name="NETWORK_NUMBER_QUALITY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for networkNumberQuality.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.NETWORK_TYPE -->
<field name="NETWORK_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for networkType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ROUTING_TABLE -->
<field name="ROUTING_TABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for routingTable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.VIRTUAL_MAC_ADDRESS_TABLE -->
<field name="VIRTUAL_MAC_ADDRESS_TABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for virtualMacAddressTable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.COMMAND_TIME_ARRAY -->
<field name="COMMAND_TIME_ARRAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for commandTimeArray.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CURRENT_COMMAND_PRIORITY -->
<field name="CURRENT_COMMAND_PRIORITY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for currentCommandPriority.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LAST_COMMAND_TIME -->
<field name="LAST_COMMAND_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lastCommandTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.VALUE_SOURCE -->
<field name="VALUE_SOURCE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for valueSource.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.VALUE_SOURCE_ARRAY -->
<field name="VALUE_SOURCE_ARRAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for valueSourceArray.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BACNET_IPV_6MODE -->
<field name="BACNET_IPV_6MODE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bacnetIpv6Mode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IPV_6ADDRESS -->
<field name="IPV_6ADDRESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipv6Address.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IPV_6PREFIX_LENGTH -->
<field name="IPV_6PREFIX_LENGTH"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipv6PrefixLength.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BACNET_IPV_6UDP_PORT -->
<field name="BACNET_IPV_6UDP_PORT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bacnetIpv6UdpPort.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IPV_6DEFAULT_GATEWAY -->
<field name="IPV_6DEFAULT_GATEWAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipv6DefaultGateway.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.BACNET_IPV_6MULTICAST_ADDRESS -->
<field name="BACNET_IPV_6MULTICAST_ADDRESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bacnetIpv6MulticastAddress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IPV_6DNS_SERVER -->
<field name="IPV_6DNS_SERVER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipv6DnsServer.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IPV_6AUTO_ADDRESSING_ENABLE -->
<field name="IPV_6AUTO_ADDRESSING_ENABLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipv6AutoAddressingEnable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IPV_6DHCP_LEASE_TIME -->
<field name="IPV_6DHCP_LEASE_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipv6DhcpLeaseTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IPV_6DHCP_LEASE_TIME_REMAINING -->
<field name="IPV_6DHCP_LEASE_TIME_REMAINING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipv6DhcpLeaseTimeRemaining.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IPV_6DHCP_SERVER -->
<field name="IPV_6DHCP_SERVER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipv6DhcpServer.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.IPV_6ZONE_INDEX -->
<field name="IPV_6ZONE_INDEX"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ipv6ZoneIndex.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ASSIGNED_LANDING_CALLS -->
<field name="ASSIGNED_LANDING_CALLS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for assignedLandingCalls.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CAR_ASSIGNED_DIRECTION -->
<field name="CAR_ASSIGNED_DIRECTION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for carAssignedDirection.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CAR_DOOR_COMMAND -->
<field name="CAR_DOOR_COMMAND"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for carDoorCommand.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CAR_DOOR_STATUS -->
<field name="CAR_DOOR_STATUS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for carDoorStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CAR_DOOR_TEXT -->
<field name="CAR_DOOR_TEXT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for carDoorText.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CAR_DOOR_ZONE -->
<field name="CAR_DOOR_ZONE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for carDoorZone.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CAR_DRIVE_STATUS -->
<field name="CAR_DRIVE_STATUS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for carDriveStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CAR_LOAD -->
<field name="CAR_LOAD"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for carLoad.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CAR_LOAD_UNITS -->
<field name="CAR_LOAD_UNITS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for carLoadUnits.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CAR_MODE -->
<field name="CAR_MODE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for carMode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CAR_MOVING_DIRECTION -->
<field name="CAR_MOVING_DIRECTION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for carMovingDirection.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CAR_POSITION -->
<field name="CAR_POSITION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for carPosition.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ELEVATOR_GROUP -->
<field name="ELEVATOR_GROUP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for elevatorGroup.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ENERGY_METER -->
<field name="ENERGY_METER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for energyMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ENERGY_METER_REF -->
<field name="ENERGY_METER_REF"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for energyMeterRef.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ESCALATOR_MODE -->
<field name="ESCALATOR_MODE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for escalatorMode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FAULT_SIGNALS -->
<field name="FAULT_SIGNALS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for faultSignals.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FLOOR_TEXT -->
<field name="FLOOR_TEXT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for floorText.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.GROUP_ID -->
<field name="GROUP_ID"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for groupId.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.GROUP_MODE -->
<field name="GROUP_MODE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for groupMode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.HIGHER_DECK -->
<field name="HIGHER_DECK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for higherDeck.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.INSTALLATION_ID -->
<field name="INSTALLATION_ID"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for installationId.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LANDING_CALLS -->
<field name="LANDING_CALLS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for landingCalls.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LANDING_CALL_CONTROL -->
<field name="LANDING_CALL_CONTROL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for landingCallControl.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LANDING_DOOR_STATUS -->
<field name="LANDING_DOOR_STATUS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for landingDoorStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LOWER_DECK -->
<field name="LOWER_DECK"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for lowerDeck.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MACHINE_ROOM_ID -->
<field name="MACHINE_ROOM_ID"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for machineRoomId.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MAKING_CAR_CALL -->
<field name="MAKING_CAR_CALL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for makingCarCall.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.NEXT_STOPPING_FLOOR -->
<field name="NEXT_STOPPING_FLOOR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for nextStoppingFloor.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OPERATION_DIRECTION -->
<field name="OPERATION_DIRECTION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for operationDirection.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PASSENGER_ALARM -->
<field name="PASSENGER_ALARM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for passengerAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.POWER_MODE -->
<field name="POWER_MODE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for powerMode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.REGISTERED_CAR_CALL -->
<field name="REGISTERED_CAR_CALL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for registeredCarCall.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ACTIVE_COV_MULTIPLE_SUBSCRIPTIONS -->
<field name="ACTIVE_COV_MULTIPLE_SUBSCRIPTIONS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for activeCovMultipleSubscriptions.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PROTOCOL_LEVEL -->
<field name="PROTOCOL_LEVEL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for protocolLevel.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.REFERENCE_PORT -->
<field name="REFERENCE_PORT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for referencePort.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DEPLOYED_PROFILE_LOCATION -->
<field name="DEPLOYED_PROFILE_LOCATION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deployedProfileLocation.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PROFILE_LOCATION -->
<field name="PROFILE_LOCATION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for profileLocation.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TAGS -->
<field name="TAGS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for tags.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SUBORDINATE_NODE_TYPES -->
<field name="SUBORDINATE_NODE_TYPES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for subordinateNodeTypes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SUBORDINATE_TAGS -->
<field name="SUBORDINATE_TAGS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for subordinateTags.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SUBORDINATE_RELATIONSHIPS -->
<field name="SUBORDINATE_RELATIONSHIPS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for subordinateRelationships.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DEFAULT_SUBORDINATE_RELATIONSHIP -->
<field name="DEFAULT_SUBORDINATE_RELATIONSHIP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for defaultSubordinateRelationship.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.REPRESENTS -->
<field name="REPRESENTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for represents.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DEFAULT_PRESENT_VALUE -->
<field name="DEFAULT_PRESENT_VALUE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for defaultPresentValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PRESENT_STAGE -->
<field name="PRESENT_STAGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for presentStage.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.STAGES -->
<field name="STAGES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for stages.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.STAGE_NAMES -->
<field name="STAGE_NAMES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for stageNames.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TARGET_REFERENCES -->
<field name="TARGET_REFERENCES"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for targetReferences.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.AUDIT_SOURCE_REPORTER -->
<field name="AUDIT_SOURCE_REPORTER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for auditSourceReporter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.AUDIT_LEVEL -->
<field name="AUDIT_LEVEL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for auditLevel.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.AUDIT_NOTIFICATION_RECIPIENT -->
<field name="AUDIT_NOTIFICATION_RECIPIENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for auditNotificationRecipient.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.AUDIT_PRIORITY_FILTER -->
<field name="AUDIT_PRIORITY_FILTER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for auditPriorityFilter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.AUDITABLE_OPERATIONS -->
<field name="AUDITABLE_OPERATIONS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for auditableOperations.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DELETE_ON_FORWARD -->
<field name="DELETE_ON_FORWARD"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deleteOnForward.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MAXIMUM_SEND_DELAY -->
<field name="MAXIMUM_SEND_DELAY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for maximumSendDelay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MONITORED_OBJECTS -->
<field name="MONITORED_OBJECTS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for monitoredObjects.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.SEND_NOW -->
<field name="SEND_NOW"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for sendNow.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.FLOOR_NUMBER -->
<field name="FLOOR_NUMBER"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for floorNumber.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DEVICE_UUID -->
<field name="DEVICE_UUID"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for deviceUuid.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.REMOVED_1 -->
<field name="REMOVED_1"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for removed1.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ISSUE_CONFIRMED_NOTIFICATIONS -->
<field name="ISSUE_CONFIRMED_NOTIFICATIONS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for issueConfirmedNotifications.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LIST_OF_SESSION_KEYS -->
<field name="LIST_OF_SESSION_KEYS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for listOfSessionKeys.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PROTOCOL_CONFORMANCE_CLASS -->
<field name="PROTOCOL_CONFORMANCE_CLASS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for protocolConformanceClass.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.RECIPIENT -->
<field name="RECIPIENT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for recipient.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.CURRENT_NOTIFY_TIME -->
<field name="CURRENT_NOTIFY_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for currentNotifyTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PREVIOUS_NOTIFY_TIME -->
<field name="PREVIOUS_NOTIFY_TIME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for previousNotifyTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MASTER_EXEMPTION -->
<field name="MASTER_EXEMPTION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for masterExemption.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.OCCUPANCY_EXEMPTION -->
<field name="OCCUPANCY_EXEMPTION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for occupancyExemption.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.PASSBACK_EXEMPTION -->
<field name="PASSBACK_EXEMPTION"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for passbackExemption.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ackedTransitions -->
<field name="ackedTransitions"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ackedTransitions.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ackRequired -->
<field name="ackRequired"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ackRequired.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.action -->
<field name="action"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for action.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.actionText -->
<field name="actionText"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for actionText.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.activeText -->
<field name="activeText"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for activeText.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.activeVtSessions -->
<field name="activeVtSessions"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for activeVtSessions.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.alarmValue -->
<field name="alarmValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for alarmValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.alarmValues -->
<field name="alarmValues"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for alarmValues.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.all -->
<field name="all"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for all.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.allWritesSuccessful -->
<field name="allWritesSuccessful"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for allWritesSuccessful.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.apduSegmentTimeout -->
<field name="apduSegmentTimeout"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for apduSegmentTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.apduTimeout -->
<field name="apduTimeout"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for apduTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.applicationSoftwareVersion -->
<field name="applicationSoftwareVersion"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for applicationSoftwareVersion.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.archive -->
<field name="archive"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for archive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.bias -->
<field name="bias"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for bias.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.changeOfStateCount -->
<field name="changeOfStateCount"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for changeOfStateCount.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.changeOfStateTime -->
<field name="changeOfStateTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for changeOfStateTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.notificationClass -->
<field name="notificationClass"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for notificationClass.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.controlledVariableReference -->
<field name="controlledVariableReference"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for controlledVariableReference.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.controlledVariableUnits -->
<field name="controlledVariableUnits"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for controlledVariableUnits.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.controlledVariableValue -->
<field name="controlledVariableValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for controlledVariableValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.covIncrement -->
<field name="covIncrement"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for covIncrement.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.dateList -->
<field name="dateList"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for dateList.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.daylightSavingsStatus -->
<field name="daylightSavingsStatus"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for daylightSavingsStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.deadband -->
<field name="deadband"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for deadband.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.derivativeConstant -->
<field name="derivativeConstant"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for derivativeConstant.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.derivativeConstantUnits -->
<field name="derivativeConstantUnits"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for derivativeConstantUnits.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.description -->
<field name="description"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for description.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.descriptionOfHalt -->
<field name="descriptionOfHalt"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for descriptionOfHalt.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.deviceAddressBinding -->
<field name="deviceAddressBinding"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for deviceAddressBinding.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.deviceType -->
<field name="deviceType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for deviceType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.effectivePeriod -->
<field name="effectivePeriod"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for effectivePeriod.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.elapsedActiveTime -->
<field name="elapsedActiveTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for elapsedActiveTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.errorLimit -->
<field name="errorLimit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for errorLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.eventEnable -->
<field name="eventEnable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for eventEnable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.eventState -->
<field name="eventState"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for eventState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.eventType -->
<field name="eventType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for eventType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.exceptionSchedule -->
<field name="exceptionSchedule"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for exceptionSchedule.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.faultValues -->
<field name="faultValues"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for faultValues.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.feedbackValue -->
<field name="feedbackValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for feedbackValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.fileAccessMethod -->
<field name="fileAccessMethod"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for fileAccessMethod.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.fileSize -->
<field name="fileSize"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for fileSize.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.fileType -->
<field name="fileType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for fileType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.firmwareRevision -->
<field name="firmwareRevision"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for firmwareRevision.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.highLimit -->
<field name="highLimit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for highLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.inactiveText -->
<field name="inactiveText"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for inactiveText.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.inProcess -->
<field name="inProcess"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for inProcess.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.instanceOf -->
<field name="instanceOf"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for instanceOf.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.integralConstant -->
<field name="integralConstant"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for integralConstant.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.integralConstantUnits -->
<field name="integralConstantUnits"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for integralConstantUnits.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.limitEnable -->
<field name="limitEnable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for limitEnable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.listOfGroupMembers -->
<field name="listOfGroupMembers"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for listOfGroupMembers.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.listOfObjectPropertyReferences -->
<field name="listOfObjectPropertyReferences"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for listOfObjectPropertyReferences.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.localDate -->
<field name="localDate"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for localDate.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.localTime -->
<field name="localTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for localTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.location -->
<field name="location"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for location.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lowLimit -->
<field name="lowLimit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lowLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.manipulatedVariableReference -->
<field name="manipulatedVariableReference"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for manipulatedVariableReference.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.maximumOutput -->
<field name="maximumOutput"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for maximumOutput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.maxApduLengthAccepted -->
<field name="maxApduLengthAccepted"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for maxApduLengthAccepted.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.maxInfoFrames -->
<field name="maxInfoFrames"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for maxInfoFrames.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.maxMaster -->
<field name="maxMaster"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for maxMaster.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.maxPresValue -->
<field name="maxPresValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for maxPresValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.minimumOffTime -->
<field name="minimumOffTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for minimumOffTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.minimumOnTime -->
<field name="minimumOnTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for minimumOnTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.minimumOutput -->
<field name="minimumOutput"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for minimumOutput.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.minPresValue -->
<field name="minPresValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for minPresValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.modelName -->
<field name="modelName"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for modelName.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.modificationDate -->
<field name="modificationDate"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for modificationDate.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.notifyType -->
<field name="notifyType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for notifyType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.numberOfApduRetries -->
<field name="numberOfApduRetries"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for numberOfApduRetries.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.numberOfStates -->
<field name="numberOfStates"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for numberOfStates.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.objectIdentifier -->
<field name="objectIdentifier"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for objectIdentifier.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.objectList -->
<field name="objectList"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for objectList.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.objectName -->
<field name="objectName"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for objectName.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.objectPropertyReference -->
<field name="objectPropertyReference"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for objectPropertyReference.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for objectType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.optional -->
<field name="optional"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for optional.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.outOfService -->
<field name="outOfService"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for outOfService.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.outputUnits -->
<field name="outputUnits"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for outputUnits.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.eventParameters -->
<field name="eventParameters"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for eventParameters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.polarity -->
<field name="polarity"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for polarity.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.presentValue -->
<field name="presentValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for presentValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.priority -->
<field name="priority"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for priority.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.priorityArray -->
<field name="priorityArray"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for priorityArray.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.priorityForWriting -->
<field name="priorityForWriting"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for priorityForWriting.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.processIdentifier -->
<field name="processIdentifier"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for processIdentifier.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.programChange -->
<field name="programChange"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for programChange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.programLocation -->
<field name="programLocation"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for programLocation.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.programState -->
<field name="programState"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for programState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.proportionalConstant -->
<field name="proportionalConstant"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for proportionalConstant.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.proportionalConstantUnits -->
<field name="proportionalConstantUnits"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for proportionalConstantUnits.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.protocolObjectTypesSupported -->
<field name="protocolObjectTypesSupported"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for protocolObjectTypesSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.protocolServicesSupported -->
<field name="protocolServicesSupported"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for protocolServicesSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.protocolVersion -->
<field name="protocolVersion"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for protocolVersion.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.readOnly -->
<field name="readOnly"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for readOnly.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.reasonForHalt -->
<field name="reasonForHalt"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for reasonForHalt.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.recipientList -->
<field name="recipientList"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for recipientList.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.reliability -->
<field name="reliability"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for reliability.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.relinquishDefault -->
<field name="relinquishDefault"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for relinquishDefault.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.required -->
<field name="required"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for required.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.resolution -->
<field name="resolution"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for resolution.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.segmentationSupported -->
<field name="segmentationSupported"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for segmentationSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.setpoint -->
<field name="setpoint"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for setpoint.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.setpointReference -->
<field name="setpointReference"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for setpointReference.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.stateText -->
<field name="stateText"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for stateText.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.statusFlags -->
<field name="statusFlags"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for statusFlags.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.systemStatus -->
<field name="systemStatus"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for systemStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.timeDelay -->
<field name="timeDelay"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for timeDelay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.timeOfActiveTimeReset -->
<field name="timeOfActiveTimeReset"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for timeOfActiveTimeReset.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.timeOfStateCountReset -->
<field name="timeOfStateCountReset"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for timeOfStateCountReset.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.timeSynchronizationRecipients -->
<field name="timeSynchronizationRecipients"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for timeSynchronizationRecipients.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.units -->
<field name="units"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for units.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.updateInterval -->
<field name="updateInterval"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for updateInterval.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.utcOffset -->
<field name="utcOffset"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for utcOffset.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.vendorIdentifier -->
<field name="vendorIdentifier"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for vendorIdentifier.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.vendorName -->
<field name="vendorName"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for vendorName.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.vtClassesSupported -->
<field name="vtClassesSupported"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for vtClassesSupported.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.weeklySchedule -->
<field name="weeklySchedule"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for weeklySchedule.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.attemptedSamples -->
<field name="attemptedSamples"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for attemptedSamples.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.averageValue -->
<field name="averageValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for averageValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.bufferSize -->
<field name="bufferSize"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for bufferSize.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.clientCovIncrement -->
<field name="clientCovIncrement"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for clientCovIncrement.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.covResubscriptionInterval -->
<field name="covResubscriptionInterval"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for covResubscriptionInterval.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.eventTimeStamps -->
<field name="eventTimeStamps"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for eventTimeStamps.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.logBuffer -->
<field name="logBuffer"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for logBuffer.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.logDeviceObjectProperty -->
<field name="logDeviceObjectProperty"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for logDeviceObjectProperty.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.enable -->
<field name="enable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for enable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.logInterval -->
<field name="logInterval"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for logInterval.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.maximumValue -->
<field name="maximumValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for maximumValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.minimumValue -->
<field name="minimumValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for minimumValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.notificationThreshold -->
<field name="notificationThreshold"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for notificationThreshold.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.protocolRevision -->
<field name="protocolRevision"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for protocolRevision.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.recordsSinceNotification -->
<field name="recordsSinceNotification"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for recordsSinceNotification.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.recordCount -->
<field name="recordCount"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for recordCount.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.startTime -->
<field name="startTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for startTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.stopTime -->
<field name="stopTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for stopTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.stopWhenFull -->
<field name="stopWhenFull"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for stopWhenFull.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.totalRecordCount -->
<field name="totalRecordCount"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for totalRecordCount.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.validSamples -->
<field name="validSamples"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for validSamples.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.windowInterval -->
<field name="windowInterval"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for windowInterval.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.windowSamples -->
<field name="windowSamples"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for windowSamples.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.maximumValueTimestamp -->
<field name="maximumValueTimestamp"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for maximumValueTimestamp.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.minimumValueTimestamp -->
<field name="minimumValueTimestamp"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for minimumValueTimestamp.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.varianceValue -->
<field name="varianceValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for varianceValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.activeCovSubscriptions -->
<field name="activeCovSubscriptions"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for activeCovSubscriptions.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.backupFailureTimeout -->
<field name="backupFailureTimeout"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for backupFailureTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.configurationFiles -->
<field name="configurationFiles"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for configurationFiles.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.databaseRevision -->
<field name="databaseRevision"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for databaseRevision.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.directReading -->
<field name="directReading"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for directReading.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lastRestoreTime -->
<field name="lastRestoreTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lastRestoreTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.maintenanceRequired -->
<field name="maintenanceRequired"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for maintenanceRequired.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.memberOf -->
<field name="memberOf"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for memberOf.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.mode -->
<field name="mode"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for mode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.operationExpected -->
<field name="operationExpected"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for operationExpected.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.setting -->
<field name="setting"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for setting.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.silenced -->
<field name="silenced"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for silenced.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.trackingValue -->
<field name="trackingValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for trackingValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.zoneMembers -->
<field name="zoneMembers"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for zoneMembers.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lifeSafetyAlarmValues -->
<field name="lifeSafetyAlarmValues"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lifeSafetyAlarmValues.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.maxSegmentsAccepted -->
<field name="maxSegmentsAccepted"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for maxSegmentsAccepted.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.profileName -->
<field name="profileName"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for profileName.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.autoSlaveDiscovery -->
<field name="autoSlaveDiscovery"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for autoSlaveDiscovery.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.manualSlaveAddressBinding -->
<field name="manualSlaveAddressBinding"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for manualSlaveAddressBinding.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.slaveAddressBinding -->
<field name="slaveAddressBinding"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for slaveAddressBinding.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.slaveProxyEnable -->
<field name="slaveProxyEnable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for slaveProxyEnable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lastNotifyRecord -->
<field name="lastNotifyRecord"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lastNotifyRecord.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.scheduleDefault -->
<field name="scheduleDefault"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for scheduleDefault.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.acceptedModes -->
<field name="acceptedModes"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for acceptedModes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.adjustValue -->
<field name="adjustValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for adjustValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.count -->
<field name="count"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for count.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.countBeforeChange -->
<field name="countBeforeChange"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for countBeforeChange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.countChangeTime -->
<field name="countChangeTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for countChangeTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.covPeriod -->
<field name="covPeriod"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for covPeriod.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.inputReference -->
<field name="inputReference"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for inputReference.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.limitMonitoringInterval -->
<field name="limitMonitoringInterval"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for limitMonitoringInterval.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.loggingObject -->
<field name="loggingObject"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for loggingObject.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.loggingRecord -->
<field name="loggingRecord"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for loggingRecord.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.prescale -->
<field name="prescale"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for prescale.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.pulseRate -->
<field name="pulseRate"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for pulseRate.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.scale -->
<field name="scale"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for scale.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.scaleFactor -->
<field name="scaleFactor"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for scaleFactor.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.updateTime -->
<field name="updateTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for updateTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.valueBeforeChange -->
<field name="valueBeforeChange"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for valueBeforeChange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.valueSet -->
<field name="valueSet"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for valueSet.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.valueChangeTime -->
<field name="valueChangeTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for valueChangeTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.alignIntervals -->
<field name="alignIntervals"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for alignIntervals.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.intervalOffset -->
<field name="intervalOffset"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for intervalOffset.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lastRestartReason -->
<field name="lastRestartReason"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lastRestartReason.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.loggingType -->
<field name="loggingType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for loggingType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.restartNotificationRecipients -->
<field name="restartNotificationRecipients"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for restartNotificationRecipients.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.timeOfDeviceRestart -->
<field name="timeOfDeviceRestart"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for timeOfDeviceRestart.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.timeSynchronizationInterval -->
<field name="timeSynchronizationInterval"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for timeSynchronizationInterval.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.trigger -->
<field name="trigger"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for trigger.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.utcTimeSynchronizationRecipients -->
<field name="utcTimeSynchronizationRecipients"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for utcTimeSynchronizationRecipients.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.nodeSubtype -->
<field name="nodeSubtype"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for nodeSubtype.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.nodeType -->
<field name="nodeType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for nodeType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.structuredObjectList -->
<field name="structuredObjectList"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for structuredObjectList.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.subordinateAnnotations -->
<field name="subordinateAnnotations"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for subordinateAnnotations.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.subordinateList -->
<field name="subordinateList"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for subordinateList.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.actualShedLevel -->
<field name="actualShedLevel"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for actualShedLevel.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.dutyWindow -->
<field name="dutyWindow"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for dutyWindow.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.expectedShedLevel -->
<field name="expectedShedLevel"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for expectedShedLevel.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.fullDutyBaseline -->
<field name="fullDutyBaseline"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for fullDutyBaseline.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.requestedShedLevel -->
<field name="requestedShedLevel"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for requestedShedLevel.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.shedDuration -->
<field name="shedDuration"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for shedDuration.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.shedLevelDescriptions -->
<field name="shedLevelDescriptions"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for shedLevelDescriptions.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.shedLevels -->
<field name="shedLevels"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for shedLevels.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.stateDescription -->
<field name="stateDescription"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for stateDescription.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.doorAlarmState -->
<field name="doorAlarmState"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for doorAlarmState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.doorExtendedPulseTime -->
<field name="doorExtendedPulseTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for doorExtendedPulseTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.doorMembers -->
<field name="doorMembers"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for doorMembers.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.doorOpenTooLongTime -->
<field name="doorOpenTooLongTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for doorOpenTooLongTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.doorPulseTime -->
<field name="doorPulseTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for doorPulseTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.doorStatus -->
<field name="doorStatus"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for doorStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.doorUnlockDelayTime -->
<field name="doorUnlockDelayTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for doorUnlockDelayTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lockStatus -->
<field name="lockStatus"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lockStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.maskedAlarmValues -->
<field name="maskedAlarmValues"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for maskedAlarmValues.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.securedStatus -->
<field name="securedStatus"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for securedStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.absenteeLimit -->
<field name="absenteeLimit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for absenteeLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.accessAlarmEvents -->
<field name="accessAlarmEvents"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for accessAlarmEvents.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.accessDoors -->
<field name="accessDoors"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for accessDoors.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.accessEvent -->
<field name="accessEvent"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for accessEvent.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.accessEventAuthenticationFactor -->
<field name="accessEventAuthenticationFactor"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for accessEventAuthenticationFactor.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.accessEventCredential -->
<field name="accessEventCredential"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for accessEventCredential.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.accessEventTime -->
<field name="accessEventTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for accessEventTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.accessTransactionEvents -->
<field name="accessTransactionEvents"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for accessTransactionEvents.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.accompaniment -->
<field name="accompaniment"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for accompaniment.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.accompanimentTime -->
<field name="accompanimentTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for accompanimentTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.activationTime -->
<field name="activationTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for activationTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.activeAuthenticationPolicy -->
<field name="activeAuthenticationPolicy"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for activeAuthenticationPolicy.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.assignedAccessRights -->
<field name="assignedAccessRights"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for assignedAccessRights.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.authenticationFactors -->
<field name="authenticationFactors"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for authenticationFactors.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.authenticationPolicyList -->
<field name="authenticationPolicyList"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for authenticationPolicyList.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.authenticationPolicyNames -->
<field name="authenticationPolicyNames"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for authenticationPolicyNames.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.authenticationStatus -->
<field name="authenticationStatus"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for authenticationStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.authorizationMode -->
<field name="authorizationMode"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for authorizationMode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.belongsTo -->
<field name="belongsTo"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for belongsTo.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.credentialDisable -->
<field name="credentialDisable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for credentialDisable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.credentialStatus -->
<field name="credentialStatus"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for credentialStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.credentials -->
<field name="credentials"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for credentials.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.credentialsInZone -->
<field name="credentialsInZone"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for credentialsInZone.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.daysRemaining -->
<field name="daysRemaining"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for daysRemaining.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.entryPoints -->
<field name="entryPoints"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for entryPoints.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.exitPoints -->
<field name="exitPoints"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for exitPoints.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.expiryTime -->
<field name="expiryTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for expiryTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.extendedTimeEnable -->
<field name="extendedTimeEnable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for extendedTimeEnable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.failedAttemptEvents -->
<field name="failedAttemptEvents"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for failedAttemptEvents.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.failedAttempts -->
<field name="failedAttempts"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for failedAttempts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.failedAttemptsTime -->
<field name="failedAttemptsTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for failedAttemptsTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lastAccessEvent -->
<field name="lastAccessEvent"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lastAccessEvent.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lastAccessPoint -->
<field name="lastAccessPoint"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lastAccessPoint.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lastCredentialAdded -->
<field name="lastCredentialAdded"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lastCredentialAdded.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lastCredentialAddedTime -->
<field name="lastCredentialAddedTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lastCredentialAddedTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lastCredentialRemoved -->
<field name="lastCredentialRemoved"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lastCredentialRemoved.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lastCredentialRemovedTime -->
<field name="lastCredentialRemovedTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lastCredentialRemovedTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lastUseTime -->
<field name="lastUseTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lastUseTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lockout -->
<field name="lockout"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lockout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lockoutRelinquishTime -->
<field name="lockoutRelinquishTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lockoutRelinquishTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.maxFailedAttempts -->
<field name="maxFailedAttempts"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for maxFailedAttempts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.members -->
<field name="members"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for members.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.musterPoint -->
<field name="musterPoint"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for musterPoint.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.negativeAccessRules -->
<field name="negativeAccessRules"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for negativeAccessRules.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.numberOfAuthenticationPolicies -->
<field name="numberOfAuthenticationPolicies"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for numberOfAuthenticationPolicies.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.occupancyCount -->
<field name="occupancyCount"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for occupancyCount.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.occupancyCountAdjust -->
<field name="occupancyCountAdjust"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for occupancyCountAdjust.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.occupancyCountEnable -->
<field name="occupancyCountEnable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for occupancyCountEnable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.occupancyLowerLimit -->
<field name="occupancyLowerLimit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for occupancyLowerLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.occupancyLowerLimitEnforced -->
<field name="occupancyLowerLimitEnforced"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for occupancyLowerLimitEnforced.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.occupancyState -->
<field name="occupancyState"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for occupancyState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.occupancyUpperLimit -->
<field name="occupancyUpperLimit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for occupancyUpperLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.occupancyUpperLimitEnforced -->
<field name="occupancyUpperLimitEnforced"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for occupancyUpperLimitEnforced.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.passbackMode -->
<field name="passbackMode"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for passbackMode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.passbackTimeout -->
<field name="passbackTimeout"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for passbackTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.positiveAccessRules -->
<field name="positiveAccessRules"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for positiveAccessRules.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.reasonForDisable -->
<field name="reasonForDisable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for reasonForDisable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.supportedFormats -->
<field name="supportedFormats"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for supportedFormats.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.supportedFormatClasses -->
<field name="supportedFormatClasses"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for supportedFormatClasses.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.threatAuthority -->
<field name="threatAuthority"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for threatAuthority.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.threatLevel -->
<field name="threatLevel"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for threatLevel.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.traceFlag -->
<field name="traceFlag"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for traceFlag.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.transactionNotificationClass -->
<field name="transactionNotificationClass"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for transactionNotificationClass.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.userExternalIdentifier -->
<field name="userExternalIdentifier"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for userExternalIdentifier.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.userInformationReference -->
<field name="userInformationReference"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for userInformationReference.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.userName -->
<field name="userName"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for userName.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.userType -->
<field name="userType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for userType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.usesRemaining -->
<field name="usesRemaining"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for usesRemaining.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.zoneFrom -->
<field name="zoneFrom"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for zoneFrom.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.zoneTo -->
<field name="zoneTo"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for zoneTo.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.accessEventTag -->
<field name="accessEventTag"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for accessEventTag.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.globalIdentifier -->
<field name="globalIdentifier"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for globalIdentifier.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.verificationTime -->
<field name="verificationTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for verificationTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.backupAndRestoreState -->
<field name="backupAndRestoreState"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for backupAndRestoreState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.backupPreparationTime -->
<field name="backupPreparationTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for backupPreparationTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.restoreCompletionTime -->
<field name="restoreCompletionTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for restoreCompletionTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.restorePreparationTime -->
<field name="restorePreparationTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for restorePreparationTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.bitMask -->
<field name="bitMask"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for bitMask.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.bitText -->
<field name="bitText"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for bitText.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.isUtc -->
<field name="isUtc"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for isUtc.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.groupMembers -->
<field name="groupMembers"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for groupMembers.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.groupMemberNames -->
<field name="groupMemberNames"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for groupMemberNames.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.memberStatusDlags -->
<field name="memberStatusDlags"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for memberStatusDlags.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.requestedUpdateInterval -->
<field name="requestedUpdateInterval"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for requestedUpdateInterval.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.covuPeriod -->
<field name="covuPeriod"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for covuPeriod.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.covuRecipients -->
<field name="covuRecipients"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for covuRecipients.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.eventMessageTexts -->
<field name="eventMessageTexts"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for eventMessageTexts.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.eventMessageTextsConfig -->
<field name="eventMessageTextsConfig"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for eventMessageTextsConfig.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.eventDetectionEnable -->
<field name="eventDetectionEnable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for eventDetectionEnable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.eventAlgorithmInhibit -->
<field name="eventAlgorithmInhibit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for eventAlgorithmInhibit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.eventAlgorithmInhibitRef -->
<field name="eventAlgorithmInhibitRef"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for eventAlgorithmInhibitRef.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.timeDelayNormal -->
<field name="timeDelayNormal"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for timeDelayNormal.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.reliabilityEvaluationInhibit -->
<field name="reliabilityEvaluationInhibit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for reliabilityEvaluationInhibit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.faultParameters -->
<field name="faultParameters"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for faultParameters.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.faultType -->
<field name="faultType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for faultType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.localForwardingOnly -->
<field name="localForwardingOnly"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for localForwardingOnly.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.processIdentifierFilter -->
<field name="processIdentifierFilter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for processIdentifierFilter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.subscribedRecipients -->
<field name="subscribedRecipients"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for subscribedRecipients.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.portFilter -->
<field name="portFilter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for portFilter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.authorizationExemptions -->
<field name="authorizationExemptions"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for authorizationExemptions.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.allowGroupDelayInhibit -->
<field name="allowGroupDelayInhibit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for allowGroupDelayInhibit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.channelNumber -->
<field name="channelNumber"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for channelNumber.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.controlGroups -->
<field name="controlGroups"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for controlGroups.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.executionDelay -->
<field name="executionDelay"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for executionDelay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lastPriority -->
<field name="lastPriority"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lastPriority.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.writeStatus -->
<field name="writeStatus"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for writeStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.propertyList -->
<field name="propertyList"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for propertyList.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.serialNumber -->
<field name="serialNumber"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for serialNumber.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.blinkWarnEnable -->
<field name="blinkWarnEnable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for blinkWarnEnable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.defaultFadetime -->
<field name="defaultFadetime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for defaultFadetime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.defaultRamprate -->
<field name="defaultRamprate"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for defaultRamprate.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.defaultStepIncrement -->
<field name="defaultStepIncrement"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for defaultStepIncrement.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.egressTime -->
<field name="egressTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for egressTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.inProgress -->
<field name="inProgress"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for inProgress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.instantaneousPower -->
<field name="instantaneousPower"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for instantaneousPower.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lightingCommand -->
<field name="lightingCommand"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lightingCommand.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lightingCommandDefaultPriority -->
<field name="lightingCommandDefaultPriority"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lightingCommandDefaultPriority.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.maxActualValue -->
<field name="maxActualValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for maxActualValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.minActualValue -->
<field name="minActualValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for minActualValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.power -->
<field name="power"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for power.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.transition -->
<field name="transition"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for transition.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.egressActive -->
<field name="egressActive"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for egressActive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.interfaceValue -->
<field name="interfaceValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for interfaceValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.faultHighLimit -->
<field name="faultHighLimit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for faultHighLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.faultLowLimit -->
<field name="faultLowLimit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for faultLowLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lowDiffLimit -->
<field name="lowDiffLimit"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lowDiffLimit.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.strikeCount -->
<field name="strikeCount"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for strikeCount.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.timeOfStrikeCountReset -->
<field name="timeOfStrikeCountReset"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for timeOfStrikeCountReset.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.defaultTimeout -->
<field name="defaultTimeout"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for defaultTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.initialTimeout -->
<field name="initialTimeout"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for initialTimeout.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lastStateChange -->
<field name="lastStateChange"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lastStateChange.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.stateChangeValues -->
<field name="stateChangeValues"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for stateChangeValues.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.timerRunning -->
<field name="timerRunning"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for timerRunning.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.timerState -->
<field name="timerState"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for timerState.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.apduLength -->
<field name="apduLength"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for apduLength.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ipAddress -->
<field name="ipAddress"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ipAddress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ipDefaultGateway -->
<field name="ipDefaultGateway"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ipDefaultGateway.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ipDhcpEnable -->
<field name="ipDhcpEnable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ipDhcpEnable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ipDhcpLeaseTime -->
<field name="ipDhcpLeaseTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ipDhcpLeaseTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ipDhcpLeaseTimeRemaining -->
<field name="ipDhcpLeaseTimeRemaining"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ipDhcpLeaseTimeRemaining.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ipDhcpServer -->
<field name="ipDhcpServer"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ipDhcpServer.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ipDnsServer -->
<field name="ipDnsServer"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ipDnsServer.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.bacnetIpGlobalAddress -->
<field name="bacnetIpGlobalAddress"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for bacnetIpGlobalAddress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.bacnetIpMode -->
<field name="bacnetIpMode"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for bacnetIpMode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.bacnetIpMulticastAddress -->
<field name="bacnetIpMulticastAddress"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for bacnetIpMulticastAddress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.bacnetIpNatTraversal -->
<field name="bacnetIpNatTraversal"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for bacnetIpNatTraversal.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ipSubnetMask -->
<field name="ipSubnetMask"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ipSubnetMask.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.bacnetIpUdpPort -->
<field name="bacnetIpUdpPort"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for bacnetIpUdpPort.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.bbmdAcceptFdRegistrations -->
<field name="bbmdAcceptFdRegistrations"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for bbmdAcceptFdRegistrations.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.bbmdBroadcastDistributionTable -->
<field name="bbmdBroadcastDistributionTable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for bbmdBroadcastDistributionTable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.bbmdForeignDeviceTable -->
<field name="bbmdForeignDeviceTable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for bbmdForeignDeviceTable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.changesPending -->
<field name="changesPending"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for changesPending.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.command -->
<field name="command"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for command.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.fdBbmdAddress -->
<field name="fdBbmdAddress"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for fdBbmdAddress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.fdSubscriptionLifetime -->
<field name="fdSubscriptionLifetime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for fdSubscriptionLifetime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.linkSpeed -->
<field name="linkSpeed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for linkSpeed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.linkSpeeds -->
<field name="linkSpeeds"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for linkSpeeds.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.linkSpeedAutonegotiate -->
<field name="linkSpeedAutonegotiate"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for linkSpeedAutonegotiate.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.macAddress -->
<field name="macAddress"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for macAddress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.networkInterfaceName -->
<field name="networkInterfaceName"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for networkInterfaceName.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.networkNumber -->
<field name="networkNumber"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for networkNumber.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.networkNumberQuality -->
<field name="networkNumberQuality"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for networkNumberQuality.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.networkType -->
<field name="networkType"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for networkType.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.routingTable -->
<field name="routingTable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for routingTable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.virtualMacAddressTable -->
<field name="virtualMacAddressTable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for virtualMacAddressTable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.commandTimeArray -->
<field name="commandTimeArray"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for commandTimeArray.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.currentCommandPriority -->
<field name="currentCommandPriority"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for currentCommandPriority.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lastCommandTime -->
<field name="lastCommandTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lastCommandTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.valueSource -->
<field name="valueSource"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for valueSource.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.valueSourceArray -->
<field name="valueSourceArray"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for valueSourceArray.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.bacnetIpv6Mode -->
<field name="bacnetIpv6Mode"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for bacnetIpv6Mode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ipv6Address -->
<field name="ipv6Address"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ipv6Address.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ipv6PrefixLength -->
<field name="ipv6PrefixLength"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ipv6PrefixLength.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.bacnetIpv6UdpPort -->
<field name="bacnetIpv6UdpPort"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for bacnetIpv6UdpPort.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ipv6DefaultGateway -->
<field name="ipv6DefaultGateway"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ipv6DefaultGateway.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.bacnetIpv6MulticastAddress -->
<field name="bacnetIpv6MulticastAddress"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for bacnetIpv6MulticastAddress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ipv6DnsServer -->
<field name="ipv6DnsServer"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ipv6DnsServer.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ipv6AutoAddressingEnable -->
<field name="ipv6AutoAddressingEnable"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ipv6AutoAddressingEnable.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ipv6DhcpLeaseTime -->
<field name="ipv6DhcpLeaseTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ipv6DhcpLeaseTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ipv6DhcpLeaseTimeRemaining -->
<field name="ipv6DhcpLeaseTimeRemaining"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ipv6DhcpLeaseTimeRemaining.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ipv6DhcpServer -->
<field name="ipv6DhcpServer"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ipv6DhcpServer.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.ipv6ZoneIndex -->
<field name="ipv6ZoneIndex"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for ipv6ZoneIndex.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.assignedLandingCalls -->
<field name="assignedLandingCalls"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for assignedLandingCalls.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.carAssignedDirection -->
<field name="carAssignedDirection"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for carAssignedDirection.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.carDoorCommand -->
<field name="carDoorCommand"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for carDoorCommand.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.carDoorStatus -->
<field name="carDoorStatus"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for carDoorStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.carDoorText -->
<field name="carDoorText"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for carDoorText.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.carDoorZone -->
<field name="carDoorZone"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for carDoorZone.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.carDriveStatus -->
<field name="carDriveStatus"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for carDriveStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.carLoad -->
<field name="carLoad"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for carLoad.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.carLoadUnits -->
<field name="carLoadUnits"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for carLoadUnits.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.carMode -->
<field name="carMode"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for carMode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.carMovingDirection -->
<field name="carMovingDirection"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for carMovingDirection.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.carPosition -->
<field name="carPosition"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for carPosition.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.elevatorGroup -->
<field name="elevatorGroup"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for elevatorGroup.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.energyMeter -->
<field name="energyMeter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for energyMeter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.energyMeterRef -->
<field name="energyMeterRef"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for energyMeterRef.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.escalatorMode -->
<field name="escalatorMode"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for escalatorMode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.faultSignals -->
<field name="faultSignals"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for faultSignals.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.floorText -->
<field name="floorText"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for floorText.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.groupId -->
<field name="groupId"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for groupId.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.groupMode -->
<field name="groupMode"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for groupMode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.higherDeck -->
<field name="higherDeck"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for higherDeck.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.installationId -->
<field name="installationId"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for installationId.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.landingCalls -->
<field name="landingCalls"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for landingCalls.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.landingCallControl -->
<field name="landingCallControl"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for landingCallControl.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.landingDoorStatus -->
<field name="landingDoorStatus"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for landingDoorStatus.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.lowerDeck -->
<field name="lowerDeck"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for lowerDeck.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.machineRoomId -->
<field name="machineRoomId"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for machineRoomId.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.makingCarCall -->
<field name="makingCarCall"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for makingCarCall.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.nextStoppingFloor -->
<field name="nextStoppingFloor"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for nextStoppingFloor.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.operationDirection -->
<field name="operationDirection"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for operationDirection.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.passengerAlarm -->
<field name="passengerAlarm"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for passengerAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.powerMode -->
<field name="powerMode"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for powerMode.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.registeredCarCall -->
<field name="registeredCarCall"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for registeredCarCall.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.activeCovMultipleSubscriptions -->
<field name="activeCovMultipleSubscriptions"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for activeCovMultipleSubscriptions.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.protocolLevel -->
<field name="protocolLevel"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for protocolLevel.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.referencePort -->
<field name="referencePort"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for referencePort.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.deployedProfileLocation -->
<field name="deployedProfileLocation"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for deployedProfileLocation.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.profileLocation -->
<field name="profileLocation"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for profileLocation.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.tags -->
<field name="tags"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for tags.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.subordinateNodeTypes -->
<field name="subordinateNodeTypes"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for subordinateNodeTypes.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.subordinateTags -->
<field name="subordinateTags"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for subordinateTags.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.subordinateRelationships -->
<field name="subordinateRelationships"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for subordinateRelationships.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.defaultSubordinateRelationship -->
<field name="defaultSubordinateRelationship"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for defaultSubordinateRelationship.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.represents -->
<field name="represents"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for represents.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.defaultPresentValue -->
<field name="defaultPresentValue"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for defaultPresentValue.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.presentStage -->
<field name="presentStage"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for presentStage.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.stages -->
<field name="stages"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for stages.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.stageNames -->
<field name="stageNames"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for stageNames.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.targetReferences -->
<field name="targetReferences"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for targetReferences.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.auditSourceReporter -->
<field name="auditSourceReporter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for auditSourceReporter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.auditLevel -->
<field name="auditLevel"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for auditLevel.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.auditNotificationRecipient -->
<field name="auditNotificationRecipient"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for auditNotificationRecipient.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.auditPriorityFilter -->
<field name="auditPriorityFilter"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for auditPriorityFilter.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.auditableOperations -->
<field name="auditableOperations"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for auditableOperations.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.deleteOnForward -->
<field name="deleteOnForward"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for deleteOnForward.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.maximumSendDelay -->
<field name="maximumSendDelay"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for maximumSendDelay.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.monitoredObjects -->
<field name="monitoredObjects"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for monitoredObjects.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.sendNow -->
<field name="sendNow"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for sendNow.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.floorNumber -->
<field name="floorNumber"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for floorNumber.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.deviceUuid -->
<field name="deviceUuid"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for deviceUuid.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.removed1 -->
<field name="removed1"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for removed1.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.issueConfirmedNotifications -->
<field name="issueConfirmedNotifications"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for issueConfirmedNotifications.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.listOfSessionKeys -->
<field name="listOfSessionKeys"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for listOfSessionKeys.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.protocolConformanceClass -->
<field name="protocolConformanceClass"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for protocolConformanceClass.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.recipient -->
<field name="recipient"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for recipient.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.currentNotifyTime -->
<field name="currentNotifyTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for currentNotifyTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.previousNotifyTime -->
<field name="previousNotifyTime"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for previousNotifyTime.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.masterExemption -->
<field name="masterExemption"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for masterExemption.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.occupancyExemption -->
<field name="occupancyExemption"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for occupancyExemption.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.passbackExemption -->
<field name="passbackExemption"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description>
BBacnetPropertyIdentifier constant for passbackExemption.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetPropertyIdentifier"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.INVALID_OR_UNSPECIFIED_ID -->
<field name="INVALID_OR_UNSPECIFIED_ID"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetPropertyIdentifier.LOG_ENABLE -->
<field name="LOG_ENABLE"  public="true" static="true" final="true">
<type class="int"/>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Ordinal value for logEnable, renamed to enable in 135-2004b-4.&#xa; This is retained here only for the benefit of dependent projects.
</description>
<tag name="@deprecated">as of 3.5.</tag>
</field>

</class>
</bajadoc>
