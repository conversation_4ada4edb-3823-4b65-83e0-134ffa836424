<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.routing.ext.BRouterExt" name="BRouterExt" packageName="com.tridiumx.jsonToolkit.inbound.routing.ext" public="true" abstract="true">
<description>
Called by &lt;code&gt;<see ref="com.tridiumx.jsonToolkit.inbound.BJsonInbound">BJsonInbound</see>&lt;/code&gt; to allow  extension of fixed exportMarker functionality,&#xa; subclasses are passed the incoming message to react to as they choose.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<!-- com.tridiumx.jsonToolkit.inbound.routing.ext.BRouterExt() -->
<constructor name="BRouterExt" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.routing.ext.BRouterExt.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.ext.BRouterExt.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.ext.BRouterExt.route(javax.baja.sys.BString) -->
<method name="route"  public="true" abstract="true">
<description/>
<parameter name="messageFrag">
<type class="javax.baja.sys.BString"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.inbound.routing.ext.RouterExtException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.ext.BRouterExt.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
