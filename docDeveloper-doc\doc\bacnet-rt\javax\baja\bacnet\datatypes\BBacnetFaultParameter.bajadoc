<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetFaultParameter" name="BBacnetFaultParameter" packageName="javax.baja.bacnet.datatypes" public="true">
<description>
Created by <PERSON><PERSON><PERSON> on 3/24/2017.
</description>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="choice" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter() -->
<constructor name="BBacnetFaultParameter" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.getChoice() -->
<method name="getChoice"  public="true">
<description>
Get the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.setChoice(int) -->
<method name="setChoice"  public="true">
<description>
Set the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.choice -->
<field name="choice"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.NONE -->
<field name="NONE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.FAULT_CHARACTERSTRING -->
<field name="FAULT_CHARACTERSTRING"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.FAULT_CHARACTERSTRING_LIST_OF_FAULT_VALUES -->
<field name="FAULT_CHARACTERSTRING_LIST_OF_FAULT_VALUES"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.FAULT_EXTENDED -->
<field name="FAULT_EXTENDED"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.FAULT_EXTENDED_VENDOR_ID -->
<field name="FAULT_EXTENDED_VENDOR_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.FAULT_EXTENDED_EXTENDED_FAULT_TYPE -->
<field name="FAULT_EXTENDED_EXTENDED_FAULT_TYPE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.FAULT_EXTENDED_PARAMETERS -->
<field name="FAULT_EXTENDED_PARAMETERS"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.FAULT_LIFE_SAFETY -->
<field name="FAULT_LIFE_SAFETY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.FAULT_LIFE_SAFETY_LIST_OF_FAULT_VALUES -->
<field name="FAULT_LIFE_SAFETY_LIST_OF_FAULT_VALUES"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.FAULT_LIFE_SAFETY_MODE_PROPERTY_REFERENCE -->
<field name="FAULT_LIFE_SAFETY_MODE_PROPERTY_REFERENCE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.FAULT_STATE -->
<field name="FAULT_STATE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.FAULT_STATE_LIST_OF_FAULT_VALUES -->
<field name="FAULT_STATE_LIST_OF_FAULT_VALUES"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.FAULT_STATUS_FLAGS -->
<field name="FAULT_STATUS_FLAGS"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetFaultParameter.FAULT_STATUS_FLAGS_STATUS_FLAGS_REFERENCE -->
<field name="FAULT_STATUS_FLAGS_STATUS_FLAGS_REFERENCE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
