<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BAlarmDbConfig" name="BAlarmDbConfig" packageName="javax.baja.alarm" public="true">
<description>
BAlarmDbConfig is the configuration for the alarm database.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">09 June 2014</tag>
<tag name="@since">Niagara 4.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="recordType" flags="rh">
<type class="javax.baja.util.BTypeSpec"/>
<description>
Slot for the &lt;code&gt;recordType&lt;/code&gt; property.
</description>
<tag name="@see">#getRecordType</tag>
<tag name="@see">#setRecordType</tag>
</property>

<property name="schema" flags="hr">
<type class="javax.baja.alarm.BAlarmSchema"/>
<description>
Slot for the &lt;code&gt;schema&lt;/code&gt; property.
</description>
<tag name="@see">#getSchema</tag>
<tag name="@see">#setSchema</tag>
</property>

<!-- javax.baja.alarm.BAlarmDbConfig() -->
<constructor name="BAlarmDbConfig" public="true">
<description/>
</constructor>

<!-- javax.baja.alarm.BAlarmDbConfig.getRecordType() -->
<method name="getRecordType"  public="true">
<description>
Get the &lt;code&gt;recordType&lt;/code&gt; property.
</description>
<tag name="@see">#recordType</tag>
<return>
<type class="javax.baja.util.BTypeSpec"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmDbConfig.setRecordType(javax.baja.util.BTypeSpec) -->
<method name="setRecordType"  public="true">
<description>
Set the &lt;code&gt;recordType&lt;/code&gt; property.
</description>
<tag name="@see">#recordType</tag>
<parameter name="v">
<type class="javax.baja.util.BTypeSpec"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmDbConfig.getSchema() -->
<method name="getSchema"  public="true">
<description>
Get the &lt;code&gt;schema&lt;/code&gt; property.
</description>
<tag name="@see">#schema</tag>
<return>
<type class="javax.baja.alarm.BAlarmSchema"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmDbConfig.setSchema(javax.baja.alarm.BAlarmSchema) -->
<method name="setSchema"  public="true">
<description>
Set the &lt;code&gt;schema&lt;/code&gt; property.
</description>
<tag name="@see">#schema</tag>
<parameter name="v">
<type class="javax.baja.alarm.BAlarmSchema"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmDbConfig.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmDbConfig.isNavChild() -->
<method name="isNavChild"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Don&#x27;t display in NavTree
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmDbConfig.getColumnType(java.lang.String) -->
<method name="getColumnType"  public="true">
<description>
Get the type for the column with the specified name.
</description>
<parameter name="name">
<type class="java.lang.String"/>
<description>
The name of the column.
</description>
</parameter>
<return>
<type class="javax.baja.sys.Type"/>
<description>
Returns the type of the target column or null if the column does&#xa;   not exist.
</description>
</return>
</method>

<!-- javax.baja.alarm.BAlarmDbConfig.makeRecord() -->
<method name="makeRecord"  public="true">
<description>
Make a template record.  This creates a new instance with default values.
</description>
<return>
<type class="javax.baja.alarm.BAlarmRecord"/>
<description>
The template record.
</description>
</return>
<throws>
<type class="javax.baja.alarm.AlarmException"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmDbConfig.makePrototype() -->
<method name="makePrototype"  public="true">
<description>
Make a prototype record for the relation.
</description>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmDbConfig.getRecordSize() -->
<method name="getRecordSize"  public="true">
<description>
Get the size of the records in this history.  If the record size is not&#xa; fixed an UnsupportedOperationException is thrown.
</description>
<return>
<type class="int"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
<throws>
<type class="javax.baja.alarm.AlarmException"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmDbConfig.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Handle a property change.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="c">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmDbConfig.added(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="added"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Handle a property addition.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="c">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmDbConfig.removed(javax.baja.sys.Property, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="removed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Handle a property removal.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="c">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmDbConfig.renamed(javax.baja.sys.Property, java.lang.String, javax.baja.sys.Context) -->
<method name="renamed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Called when an existing property is renamed via one&#xa; of the &lt;code&gt;rename&lt;/code&gt; methods.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="oldName">
<type class="java.lang.String"/>
</parameter>
<parameter name="c">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmDbConfig.flagsChanged(javax.baja.sys.Slot, javax.baja.sys.Context) -->
<method name="flagsChanged"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Called when a slot&#x27;s flags are modified via one of&#xa; the &lt;code&gt;setFlags&lt;/code&gt; methods.
</description>
<parameter name="slot">
<type class="javax.baja.sys.Slot"/>
</parameter>
<parameter name="c">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmDbConfig.facetsChanged(javax.baja.sys.Slot, javax.baja.sys.Context) -->
<method name="facetsChanged"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Called when a slot&#x27;s facets are modified via one of&#xa; the &lt;code&gt;setFacets&lt;/code&gt; methods.
</description>
<parameter name="slot">
<type class="javax.baja.sys.Slot"/>
</parameter>
<parameter name="c">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmDbConfig.recordType -->
<field name="recordType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;recordType&lt;/code&gt; property.
</description>
<tag name="@see">#getRecordType</tag>
<tag name="@see">#setRecordType</tag>
</field>

<!-- javax.baja.alarm.BAlarmDbConfig.schema -->
<field name="schema"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;schema&lt;/code&gt; property.
</description>
<tag name="@see">#getSchema</tag>
<tag name="@see">#setSchema</tag>
</field>

<!-- javax.baja.alarm.BAlarmDbConfig.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
