<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.query.style.QueryStyleManager" name="QueryStyleManager" packageName="com.tridiumx.jsonToolkit.outbound.schema.query.style" public="true">
<description>
Maintains the list of registered query output style writers.&#xa; The list is created once per station lifetime using a registry agent check.&#xa; To register the writer class must be an agent upon &lt;code&gt;<see ref="com.tridiumx.jsonToolkit.outbound.schema.query.BJsonSchemaBoundQueryResult">BJsonSchemaBoundQueryResult</see>&lt;/code&gt;&#xa; and be an instance of &lt;code&gt;<see ref="com.tridiumx.jsonToolkit.outbound.schema.query.style.BQueryResultWriter">BQueryResultWriter</see>&lt;/code&gt;.
</description>
<tag name="@author">Nick Dodd</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.QueryStyleManager.getInstance() -->
<method name="getInstance"  public="true" static="true">
<description/>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.style.QueryStyleManager"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.QueryStyleManager.getQueryStyles() -->
<method name="getQueryStyles"  public="true">
<description/>
<return>
<parameterizedType class="java.util.List">
<args>
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.style.BQueryResultWriter"/>
</args>
</parameterizedType>
</return>
</method>

</class>
</bajadoc>
