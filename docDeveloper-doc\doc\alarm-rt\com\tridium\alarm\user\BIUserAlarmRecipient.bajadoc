<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.user.BIUserAlarmRecipient" name="BIUserAlarmRecipient" packageName="com.tridium.alarm.user" public="true" interface="true" abstract="true" category="interface">
<description>
Interface for AlarmRecipients that can retransmit to a given destination from the information held in a BUser.&#xa; This should only be implemented by classes that extend BAlarmRecipient.
</description>
<tag name="@author">g<PERSON><PERSON><PERSON></tag>
<tag name="@creation">7 Nov 2008</tag>
<tag name="@version">1</tag>
<tag name="@since">Niagara 3.5</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
</class>
</bajadoc>
