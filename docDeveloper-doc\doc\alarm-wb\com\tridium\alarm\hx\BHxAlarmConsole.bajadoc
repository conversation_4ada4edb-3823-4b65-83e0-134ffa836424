<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.hx.BHxAlarmConsole" name="BHxAlarmConsole" packageName="com.tridium.alarm.hx" public="true">
<description>
BHxAlarmConsole
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">23 Feb 05</tag>
<tag name="@version">$Revision: 65$ $Date: 1/10/11 11:37:51 AM EST$</tag>
<tag name="@since">Niagara 3.0</tag>
<tag name="@deprecated">since Niagara 4.4 - will be removed in a future version. Use &#x27;nmodule/alarm/rc/console/AlarmConsole&#x27; instead.</tag>
<extends>
<type class="javax.baja.hx.BHxView"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraSingleton"/>
</annotation>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
</class>
</bajadoc>
