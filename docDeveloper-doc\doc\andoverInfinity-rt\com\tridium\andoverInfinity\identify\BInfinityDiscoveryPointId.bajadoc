<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId" name="BInfinityDiscoveryPointId" packageName="com.tridium.andoverInfinity.identify" public="true">
<description>
BInfinityDiscoveryPointId extends BInfinityPointId to include properties&#xa; that are to be displayed in the learn view pane, but not in the point display&#xa; pane.
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 22, 2007</tag>
<tag name="@version">$Revision$ $May 22, 2007 9:44:02 AM$</tag>
<tag name="@since"/>
<extends>
<type class="com.tridium.andoverInfinity.identify.BInfinityPointId"/>
</extends>
<property name="rawValue" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;rawValue&lt;/code&gt; property.
</description>
<tag name="@see">#getRawValue</tag>
<tag name="@see">#setRawValue</tag>
</property>

<property name="rawPointUnits" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;rawPointUnits&lt;/code&gt; property.
</description>
<tag name="@see">#getRawPointUnits</tag>
<tag name="@see">#setRawPointUnits</tag>
</property>

<property name="rawPointType" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;rawPointType&lt;/code&gt; property.
</description>
<tag name="@see">#getRawPointType</tag>
<tag name="@see">#setRawPointType</tag>
</property>

<property name="rawPointIou" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;rawPointIou&lt;/code&gt; property.
</description>
<tag name="@see">#getRawPointIou</tag>
<tag name="@see">#setRawPointIou</tag>
</property>

<property name="rawPointChannel" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;rawPointChannel&lt;/code&gt; property.
</description>
<tag name="@see">#getRawPointChannel</tag>
<tag name="@see">#setRawPointChannel</tag>
</property>

<property name="rawPointState" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;rawPointState&lt;/code&gt; property.
</description>
<tag name="@see">#getRawPointState</tag>
<tag name="@see">#setRawPointState</tag>
</property>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId() -->
<constructor name="BInfinityDiscoveryPointId" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.getRawValue() -->
<method name="getRawValue"  public="true">
<description>
Get the &lt;code&gt;rawValue&lt;/code&gt; property.
</description>
<tag name="@see">#rawValue</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.setRawValue(java.lang.String) -->
<method name="setRawValue"  public="true">
<description>
Set the &lt;code&gt;rawValue&lt;/code&gt; property.
</description>
<tag name="@see">#rawValue</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.getRawPointUnits() -->
<method name="getRawPointUnits"  public="true">
<description>
Get the &lt;code&gt;rawPointUnits&lt;/code&gt; property.
</description>
<tag name="@see">#rawPointUnits</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.setRawPointUnits(java.lang.String) -->
<method name="setRawPointUnits"  public="true">
<description>
Set the &lt;code&gt;rawPointUnits&lt;/code&gt; property.
</description>
<tag name="@see">#rawPointUnits</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.getRawPointType() -->
<method name="getRawPointType"  public="true">
<description>
Get the &lt;code&gt;rawPointType&lt;/code&gt; property.
</description>
<tag name="@see">#rawPointType</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.setRawPointType(java.lang.String) -->
<method name="setRawPointType"  public="true">
<description>
Set the &lt;code&gt;rawPointType&lt;/code&gt; property.
</description>
<tag name="@see">#rawPointType</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.getRawPointIou() -->
<method name="getRawPointIou"  public="true">
<description>
Get the &lt;code&gt;rawPointIou&lt;/code&gt; property.
</description>
<tag name="@see">#rawPointIou</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.setRawPointIou(java.lang.String) -->
<method name="setRawPointIou"  public="true">
<description>
Set the &lt;code&gt;rawPointIou&lt;/code&gt; property.
</description>
<tag name="@see">#rawPointIou</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.getRawPointChannel() -->
<method name="getRawPointChannel"  public="true">
<description>
Get the &lt;code&gt;rawPointChannel&lt;/code&gt; property.
</description>
<tag name="@see">#rawPointChannel</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.setRawPointChannel(java.lang.String) -->
<method name="setRawPointChannel"  public="true">
<description>
Set the &lt;code&gt;rawPointChannel&lt;/code&gt; property.
</description>
<tag name="@see">#rawPointChannel</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.getRawPointState() -->
<method name="getRawPointState"  public="true">
<description>
Get the &lt;code&gt;rawPointState&lt;/code&gt; property.
</description>
<tag name="@see">#rawPointState</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.setRawPointState(java.lang.String) -->
<method name="setRawPointState"  public="true">
<description>
Set the &lt;code&gt;rawPointState&lt;/code&gt; property.
</description>
<tag name="@see">#rawPointState</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.rawResponse -->
<field name="rawResponse"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;rawResponse&lt;/code&gt; property.&#xa; override BInfinityPointId to keep this out of learn view pane
</description>
<tag name="@see">#getRawResponse</tag>
<tag name="@see">#setRawResponse</tag>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.rawValue -->
<field name="rawValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;rawValue&lt;/code&gt; property.
</description>
<tag name="@see">#getRawValue</tag>
<tag name="@see">#setRawValue</tag>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.rawPointUnits -->
<field name="rawPointUnits"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;rawPointUnits&lt;/code&gt; property.
</description>
<tag name="@see">#getRawPointUnits</tag>
<tag name="@see">#setRawPointUnits</tag>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.rawPointType -->
<field name="rawPointType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;rawPointType&lt;/code&gt; property.
</description>
<tag name="@see">#getRawPointType</tag>
<tag name="@see">#setRawPointType</tag>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.rawPointIou -->
<field name="rawPointIou"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;rawPointIou&lt;/code&gt; property.
</description>
<tag name="@see">#getRawPointIou</tag>
<tag name="@see">#setRawPointIou</tag>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.rawPointChannel -->
<field name="rawPointChannel"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;rawPointChannel&lt;/code&gt; property.
</description>
<tag name="@see">#getRawPointChannel</tag>
<tag name="@see">#setRawPointChannel</tag>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.rawPointState -->
<field name="rawPointState"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;rawPointState&lt;/code&gt; property.
</description>
<tag name="@see">#getRawPointState</tag>
<tag name="@see">#setRawPointState</tag>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityDiscoveryPointId.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
