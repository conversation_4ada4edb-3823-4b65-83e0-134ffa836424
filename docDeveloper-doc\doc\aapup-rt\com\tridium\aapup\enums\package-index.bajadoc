<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="aapup" runtimeProfile="rt" name="com.tridium.aapup.enums">
<description/>
<class packageName="com.tridium.aapup.enums" name="BPingMessageSelectionEnum"><description>The BPingMessageSelectionEnum class provides enumeration message types that can&#xa; be used for ping.</description></class>
<class packageName="com.tridium.aapup.enums" name="BPupAlarmStatusEnum"/>
<class packageName="com.tridium.aapup.enums" name="BPupCommSpeedEnum"/>
<class packageName="com.tridium.aapup.enums" name="BPupOccupancyEnum"/>
<class packageName="com.tridium.aapup.enums" name="BPupPeerTypeEnum"><description>The BPupPeerTypeEnum class provides enumeration of American&#xa; Automatrix Public Unitary Protocol (PUP) Peer Types, for selection&#xa; of how Niagara should interact with the PUP network.</description></class>
<class packageName="com.tridium.aapup.enums" name="BPupPrewriteAttributeEnum"/>
<class packageName="com.tridium.aapup.enums" name="BPupProgramStatusEnum"/>
<class packageName="com.tridium.aapup.enums" name="BPupScalingTypeEnum"/>
<class packageName="com.tridium.aapup.enums" name="BPupWeekdayEnum"/>
</package>
</bajadoc>
