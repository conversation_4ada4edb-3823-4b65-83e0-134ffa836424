<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.data.DataTypes" name="DataTypes" packageName="javax.baja.data" public="true" final="true">
<description>
DataTypes define the types used by the Baja Data API.&#xa; Each DataType is a standard Type which maps to a BSimple.&#xa; DataTypes are additionally uniquely identified using a&#xa; single ASCII character (like Flags).
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- javax.baja.data.DataTypes.otob(javax.baja.sys.BIObject) -->
<method name="otob"  public="true" static="true">
<description/>
<parameter name="o">
<type class="javax.baja.sys.BIObject"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.data.DataTypes.otoi(javax.baja.sys.BIObject) -->
<method name="otoi"  public="true" static="true">
<description/>
<parameter name="o">
<type class="javax.baja.sys.BIObject"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.data.DataTypes.otol(javax.baja.sys.BIObject) -->
<method name="otol"  public="true" static="true">
<description/>
<parameter name="o">
<type class="javax.baja.sys.BIObject"/>
</parameter>
<return>
<type class="long"/>
</return>
</method>

<!-- javax.baja.data.DataTypes.otof(javax.baja.sys.BIObject) -->
<method name="otof"  public="true" static="true">
<description/>
<parameter name="o">
<type class="javax.baja.sys.BIObject"/>
</parameter>
<return>
<type class="float"/>
</return>
</method>

<!-- javax.baja.data.DataTypes.otod(javax.baja.sys.BIObject) -->
<method name="otod"  public="true" static="true">
<description/>
<parameter name="o">
<type class="javax.baja.sys.BIObject"/>
</parameter>
<return>
<type class="double"/>
</return>
</method>

<!-- javax.baja.data.DataTypes.otos(javax.baja.sys.BIObject) -->
<method name="otos"  public="true" static="true">
<description/>
<parameter name="o">
<type class="javax.baja.sys.BIObject"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.data.DataTypes.getTypes() -->
<method name="getTypes"  public="true" static="true">
<description>
Get the predefined list of Data Types.
</description>
<return>
<type class="javax.baja.sys.Type" dimension="1"/>
</return>
</method>

<!-- javax.baja.data.DataTypes.getBySymbol(char) -->
<method name="getBySymbol"  public="true" static="true">
<description>
Get a Data Type by it symbol, or return null if not found.
</description>
<parameter name="symbol">
<type class="char"/>
</parameter>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.data.DataTypes.BOOLEAN -->
<field name="BOOLEAN"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description>
Boolean: &#x27;b&#x27;
</description>
</field>

<!-- javax.baja.data.DataTypes.INTEGER -->
<field name="INTEGER"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description>
Integer: &#x27;i&#x27;
</description>
</field>

<!-- javax.baja.data.DataTypes.LONG -->
<field name="LONG"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description>
Long: &#x27;l&#x27;
</description>
</field>

<!-- javax.baja.data.DataTypes.FLOAT -->
<field name="FLOAT"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description>
Float: &#x27;f&#x27;
</description>
</field>

<!-- javax.baja.data.DataTypes.DOUBLE -->
<field name="DOUBLE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description>
Double: &#x27;d&#x27;
</description>
</field>

<!-- javax.baja.data.DataTypes.STRING -->
<field name="STRING"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description>
String: &#x27;s&#x27;
</description>
</field>

<!-- javax.baja.data.DataTypes.ENUM -->
<field name="ENUM"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description>
Enum: &#x27;e&#x27;
</description>
</field>

<!-- javax.baja.data.DataTypes.ENUM_RANGE -->
<field name="ENUM_RANGE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description>
EnumRange: &#x27;E&#x27;
</description>
</field>

<!-- javax.baja.data.DataTypes.ABS_TIME -->
<field name="ABS_TIME"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description>
AbsTime: &#x27;a&#x27;
</description>
</field>

<!-- javax.baja.data.DataTypes.REL_TIME -->
<field name="REL_TIME"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description>
RelTime: &#x27;r&#x27;
</description>
</field>

<!-- javax.baja.data.DataTypes.UNIT -->
<field name="UNIT"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description>
Unit: &#x27;u&#x27;
</description>
</field>

<!-- javax.baja.data.DataTypes.TIME_ZONE -->
<field name="TIME_ZONE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description>
Unit: &#x27;z&#x27;
</description>
</field>

<!-- javax.baja.data.DataTypes.ORD -->
<field name="ORD"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description>
Ord: &#x27;o&#x27;
</description>
</field>

<!-- javax.baja.data.DataTypes.MARKER -->
<field name="MARKER"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description>
Marker: &#x27;m&#x27;
</description>
</field>

</class>
</bajadoc>
