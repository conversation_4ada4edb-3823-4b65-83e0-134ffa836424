<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.query.QueryResultHolder" name="QueryResultHolder" packageName="com.tridiumx.jsonToolkit.outbound.schema.query" public="true">
<description>
Cache of a bql/neql query result table.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- com.tridiumx.jsonToolkit.outbound.schema.query.QueryResultHolder(javax.baja.collection.BITable&lt;?&gt;) -->
<constructor name="QueryResultHolder" public="true">
<parameter name="queryResult">
<parameterizedType class="javax.baja.collection.BITable">
<args>
<wildcardType class="?">
</wildcardType>
</args>
</parameterizedType>
<description>
the resolved query result table
</description>
</parameter>
<description>
Drain the query result table and cache locally.
</description>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.QueryResultHolder.getResultList() -->
<method name="getResultList"  public="true">
<description/>
<return>
<parameterizedType class="java.util.List">
<args>
<parameterizedType class="java.util.Map">
<args>
<type class="java.lang.String"/>
<type class="javax.baja.sys.BIObject"/>
</args>
</parameterizedType>
</args>
</parameterizedType>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.QueryResultHolder.getColumnNames() -->
<method name="getColumnNames"  public="true">
<description/>
<return>
<parameterizedType class="java.util.List">
<args>
<type class="java.lang.String"/>
</args>
</parameterizedType>
</return>
</method>

</class>
</bajadoc>
