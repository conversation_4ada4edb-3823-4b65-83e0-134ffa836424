<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.req.RequiresNetworkAccess" name="RequiresNetworkAccess" packageName="com.tridium.andoverInfinity.comm.req" public="true" interface="true" abstract="true" category="interface">
<description>
Most Infinity requests require access to the screen buffer to get current screen&#xa; mode.
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<!-- com.tridium.andoverInfinity.comm.req.RequiresNetworkAccess.setNetwork(com.tridium.andoverInfinity.BInfinityNetwork) -->
<method name="setNetwork"  public="true" abstract="true">
<description>
For request classes for which the screen buffer cursor position, cursor mode, or&#xa; buffer contents are important, this method should be &#xa; called to set the network before the out going byte[] is generated.  &#xa; &#xa; This is called from the &lt;code&gt;forceTransmit&lt;/code&gt; method of the BInfinityTransmitter&#xa; class.
</description>
<tag name="@see">com.tridium.andoverInfinity.comm.req.RequiresNetworkAccess#setNetwork(com.tridium.andoverInfinity.BInfinityNetwork)</tag>
<parameter name="network">
<type class="com.tridium.andoverInfinity.BInfinityNetwork"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
