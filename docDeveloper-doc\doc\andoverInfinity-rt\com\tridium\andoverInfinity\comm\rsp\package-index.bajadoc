<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="andoverInfinity" runtimeProfile="rt" name="com.tridium.andoverInfinity.comm.rsp">
<description/>
<class packageName="com.tridium.andoverInfinity.comm.rsp" name="BInfinityAckWithData"><description>Used to send bytes to the controller as intermediate steps in multi-step&#xa; request/response sequences.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.rsp" name="BInfinityDeviceDiscoverResponse"><description>BInfinityDeviceDiscoverResponse includes an array of BInfinityDeviceDiscoveryObject&#x27;s&#xa; that were discovered in the processing of a BInfinityDeviceDiscoverRequest</description></class>
<class packageName="com.tridium.andoverInfinity.comm.rsp" name="BInfinityPingResponse"><description>Response class to used to complete a successful ping request</description></class>
<class packageName="com.tridium.andoverInfinity.comm.rsp" name="BInfinityPointAutoResponse"><description>The only purpose in life for this class is to satisfy the success of the &#xa; BInfinityPointAutoRequest, BInfinityPointEnableRequest, or BInfinityPointDisableRequest</description></class>
<class packageName="com.tridium.andoverInfinity.comm.rsp" name="BInfinityPointDiscoverResponse"><description>Used to pass point discoveryObjects to the point discovery job</description></class>
<class packageName="com.tridium.andoverInfinity.comm.rsp" name="BInfinityReadPointResponse"><description>Response class to parse a response string into an BStatusValue</description></class>
<class packageName="com.tridium.andoverInfinity.comm.rsp" name="BInfinityReloadStartResponse"><description>BInfinityReloadStartResponse is used to complete a reload transaction</description></class>
<class packageName="com.tridium.andoverInfinity.comm.rsp" name="BInfinitySuccessResponse"><description>BInfinitySuccessResponse sole purpose in life is to satisfy the completion of &#xa; requests or request-sequences.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.rsp" name="BInfinityWritePointResponse"><description>/**&#xa; BInfinityWritePointResponse is used to complete a write point transaction</description></class>
</package>
</bajadoc>
