<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetDynamicScheduleDescriptor" name="BBacnetDynamicScheduleDescriptor" packageName="javax.baja.bacnet.export" public="true">
<description>
BBacnetDynamicScheduleDescriptor represents an intermediate descriptor that&#xa; gets created during dynamic schedule object creation in bacnet when actual type&#xa; of the schedule to be created cannot be inferred using the initial values.&#xa; Its scheduleOrd does not point to any concrete schedule object .&#xa; &lt;p&gt;&#xa; When its write property is called for properties that can be helpful in determining&#xa; the datatype, then this descriptor gets deleted and the specific typed&#xa; Schedule descriptor is created in its place.&#xa; &lt;p&gt;
</description>
<tag name="@author">by <PERSON><PERSON><PERSON> on 7/7/2017.</tag>
<tag name="@since">Niagara 4.3</tag>
<extends>
<type class="javax.baja.bacnet.export.BBacnetScheduleDescriptor"/>
</extends>
<property name="outOfService" flags="h">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;outOfService&lt;/code&gt; property.
</description>
<tag name="@see">#getOutOfService</tag>
<tag name="@see">#setOutOfService</tag>
</property>

<property name="effective" flags="ho">
<type class="javax.baja.schedule.BDateRangeSchedule"/>
<description>
Slot for the &lt;code&gt;effective&lt;/code&gt; property.
</description>
<tag name="@see">#getEffective</tag>
<tag name="@see">#setEffective</tag>
</property>

<!-- javax.baja.bacnet.export.BBacnetDynamicScheduleDescriptor() -->
<constructor name="BBacnetDynamicScheduleDescriptor" public="true">
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetDynamicScheduleDescriptor.getOutOfService() -->
<method name="getOutOfService"  public="true">
<description>
Get the &lt;code&gt;outOfService&lt;/code&gt; property.
</description>
<tag name="@see">#outOfService</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetDynamicScheduleDescriptor.setOutOfService(boolean) -->
<method name="setOutOfService"  public="true">
<description>
Set the &lt;code&gt;outOfService&lt;/code&gt; property.
</description>
<tag name="@see">#outOfService</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetDynamicScheduleDescriptor.getEffective() -->
<method name="getEffective"  public="true">
<description>
Get the &lt;code&gt;effective&lt;/code&gt; property.
</description>
<tag name="@see">#effective</tag>
<return>
<type class="javax.baja.schedule.BDateRangeSchedule"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetDynamicScheduleDescriptor.setEffective(javax.baja.schedule.BDateRangeSchedule) -->
<method name="setEffective"  public="true">
<description>
Set the &lt;code&gt;effective&lt;/code&gt; property.
</description>
<tag name="@see">#effective</tag>
<parameter name="v">
<type class="javax.baja.schedule.BDateRangeSchedule"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetDynamicScheduleDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetDynamicScheduleDescriptor.validate() -->
<method name="validate"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Validate the schedule&#x27;s configuration.  For schedules, an event&#xa; cannot have a value with a null status.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetDynamicScheduleDescriptor.writeProperty(int, int, byte[], int) -->
<method name="writeProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetDynamicScheduleDescriptor.doWritePresentValue() -->
<method name="doWritePresentValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Write the present value of the schedule to non-Present_Value&#xa; target properties, and to any external targets.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetDynamicScheduleDescriptor.doWriteScheduleDefaultValue(com.tridium.bacnet.asn.AsnInputStream, int) -->
<method name="doWriteScheduleDefaultValue"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
WRite the schedule default values
</description>
<parameter name="asnInputStream">
<type class="com.tridium.bacnet.asn.AsnInputStream"/>
<description/>
</parameter>
<parameter name="applicationTag">
<type class="int"/>
<description/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
error
</description>
</return>
<throws>
<type class="java.lang.Exception"/>
<description/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetDynamicScheduleDescriptor.readProperty(int, int) -->
<method name="readProperty"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the value of a property.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the int containing property id
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
index.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetDynamicScheduleDescriptor.outOfService -->
<field name="outOfService"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;outOfService&lt;/code&gt; property.
</description>
<tag name="@see">#getOutOfService</tag>
<tag name="@see">#setOutOfService</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetDynamicScheduleDescriptor.effective -->
<field name="effective"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;effective&lt;/code&gt; property.
</description>
<tag name="@see">#getEffective</tag>
<tag name="@see">#setEffective</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetDynamicScheduleDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
