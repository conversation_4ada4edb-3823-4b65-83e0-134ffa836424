<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="abstractMqttDriver" runtimeProfile="rt" qualifiedName="javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator" name="BAbstractMqttAuthenticator" packageName="javax.baja.mqttClientDriver.authenticator" public="true" abstract="true">
<description>
BAbstractMqttAuthenticator, the base of the authentication feature in any device.
</description>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@creation">05-Oct-16</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="brokerEndpoint" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;brokerEndpoint&lt;/code&gt; property.
</description>
<tag name="@see">#getBrokerEndpoint</tag>
<tag name="@see">#setBrokerEndpoint</tag>
</property>

<property name="clientID" flags="d">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;clientID&lt;/code&gt; property.
</description>
<tag name="@see">#getClientID</tag>
<tag name="@see">#setClientID</tag>
</property>

<property name="brokerPort" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;brokerPort&lt;/code&gt; property.
</description>
<tag name="@see">#getBrokerPort</tag>
<tag name="@see">#setBrokerPort</tag>
</property>

<property name="callbackRouter" flags="">
<type class="com.tridium.mqttClientDriver.authenticator.gcp.BMqttCallbackRouter"/>
<description>
Slot for the &lt;code&gt;callbackRouter&lt;/code&gt; property.
</description>
<tag name="@see">#getCallbackRouter</tag>
<tag name="@see">#setCallbackRouter</tag>
</property>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator() -->
<constructor name="BAbstractMqttAuthenticator" public="true">
<description/>
</constructor>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.getBrokerEndpoint() -->
<method name="getBrokerEndpoint"  public="true">
<description>
Get the &lt;code&gt;brokerEndpoint&lt;/code&gt; property.
</description>
<tag name="@see">#brokerEndpoint</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.setBrokerEndpoint(java.lang.String) -->
<method name="setBrokerEndpoint"  public="true">
<description>
Set the &lt;code&gt;brokerEndpoint&lt;/code&gt; property.
</description>
<tag name="@see">#brokerEndpoint</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.getClientID() -->
<method name="getClientID"  public="true">
<description>
Get the &lt;code&gt;clientID&lt;/code&gt; property.
</description>
<tag name="@see">#clientID</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.setClientID(java.lang.String) -->
<method name="setClientID"  public="true">
<description>
Set the &lt;code&gt;clientID&lt;/code&gt; property.
</description>
<tag name="@see">#clientID</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.getBrokerPort() -->
<method name="getBrokerPort"  public="true">
<description>
Get the &lt;code&gt;brokerPort&lt;/code&gt; property.
</description>
<tag name="@see">#brokerPort</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.setBrokerPort(int) -->
<method name="setBrokerPort"  public="true">
<description>
Set the &lt;code&gt;brokerPort&lt;/code&gt; property.
</description>
<tag name="@see">#brokerPort</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.getCallbackRouter() -->
<method name="getCallbackRouter"  public="true">
<description>
Get the &lt;code&gt;callbackRouter&lt;/code&gt; property.
</description>
<tag name="@see">#callbackRouter</tag>
<return>
<type class="com.tridium.mqttClientDriver.authenticator.gcp.BMqttCallbackRouter"/>
</return>
</method>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.setCallbackRouter(com.tridium.mqttClientDriver.authenticator.gcp.BMqttCallbackRouter) -->
<method name="setCallbackRouter"  public="true">
<description>
Set the &lt;code&gt;callbackRouter&lt;/code&gt; property.
</description>
<tag name="@see">#callbackRouter</tag>
<parameter name="v">
<type class="com.tridium.mqttClientDriver.authenticator.gcp.BMqttCallbackRouter"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.setCurrentMqttAction(com.tridium.mqttClientDriver.util.MqttActions) -->
<method name="setCurrentMqttAction"  public="true">
<description/>
<parameter name="action">
<type class="com.tridium.mqttClientDriver.util.MqttActions"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.getCurrentMqttAction() -->
<method name="getCurrentMqttAction"  public="true">
<description/>
<return>
<type class="com.tridium.mqttClientDriver.util.MqttActions"/>
</return>
</method>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.getSubscriberMap() -->
<method name="getSubscriberMap"  public="true">
<description/>
<return>
<type class="com.tridium.mqttClientDriver.util.MqttSubscriberTopics"/>
</return>
</method>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.preRemove() -->
<method name="preRemove"  public="true">
<description>
Callback which is called just prior to the removal (deletion) of this authenticator&#xa; or parent device, to allow any cleanup of resources.
</description>
<tag name="@since">Niagara 4.12</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.connect(com.tridium.mqttClientDriver.BAbstractMqttDevice) -->
<method name="connect"  public="true">
<description/>
<parameter name="device">
<type class="com.tridium.mqttClientDriver.BAbstractMqttDevice"/>
</parameter>
<return>
<type class="com.tridium.mqttClientDriver.clients.INiagaraMqttClient"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.disconnect() -->
<method name="disconnect"  public="true">
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.enableAndDisableFields(int) -->
<method name="enableAndDisableFields"  public="true" abstract="true">
<description/>
<parameter name="flag">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.brokerEndpoint -->
<field name="brokerEndpoint"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;brokerEndpoint&lt;/code&gt; property.
</description>
<tag name="@see">#getBrokerEndpoint</tag>
<tag name="@see">#setBrokerEndpoint</tag>
</field>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.clientID -->
<field name="clientID"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;clientID&lt;/code&gt; property.
</description>
<tag name="@see">#getClientID</tag>
<tag name="@see">#setClientID</tag>
</field>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.brokerPort -->
<field name="brokerPort"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;brokerPort&lt;/code&gt; property.
</description>
<tag name="@see">#getBrokerPort</tag>
<tag name="@see">#setBrokerPort</tag>
</field>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.callbackRouter -->
<field name="callbackRouter"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;callbackRouter&lt;/code&gt; property.
</description>
<tag name="@see">#getCallbackRouter</tag>
<tag name="@see">#setCallbackRouter</tag>
</field>

<!-- javax.baja.mqttClientDriver.authenticator.BAbstractMqttAuthenticator.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
