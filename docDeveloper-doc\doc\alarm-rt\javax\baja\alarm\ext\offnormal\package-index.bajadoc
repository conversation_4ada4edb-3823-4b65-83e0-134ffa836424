<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="alarm" runtimeProfile="rt" name="javax.baja.alarm.ext.offnormal">
<description>
&lt;p&gt;This package provides classes for offnormal alarm algorithms for control points.&lt;/p&gt;
</description>
<class packageName="javax.baja.alarm.ext.offnormal" name="BBooleanChangeOfStateAlgorithm"><description>BBooleanChangeOfStateAlgorithm implements a change of&#xa; state alarm detection algorithm for boolean objects&#xa; as described in BACnet Clause 13.3.2.</description></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BBooleanCommandFailureAlgorithm"><description>BBooleanCommandFailureAlgorithm implements command&#xa; failure alarm detection algorithm for boolean&#xa; objects as described in BACnet.</description></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BEnumChangeOfStateAlgorithm"><description>BEnumChangeOfStateAlgorithm implements a change of state&#xa; alarm detection algorithm for multistate objects as described&#xa; in BACnet Clause 13.3.2.</description></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BEnumCommandFailureAlgorithm"><description>BEnumCommandFailureAlgorithm implements command failure&#xa; alarm detection algorithm for multistate objects as described&#xa; in BACnet.</description></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BFloatingLimitAlgorithm"><description>BFloatingLimitAlgorithm implements the floating-limit event algorithm as described in BACnet.</description></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BOutOfRangeAlgorithm"><description>BOutOfRangeAlgorithm implements a standard out-of-range&#xa; alarming algorithm</description></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BStatusAlgorithm"><description>BStatusAlgorithm allows alarming based on the ControlsPoint&#x27;s status value.</description></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BStringChangeOfStateAlgorithm"><description>BStringChangeOfStateAlgorithm implements a change of&#xa; state alarm detection algorithm for text strings.</description></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BStringChangeOfStateFaultAlgorithm"><description>BStringChangeOfStateAlgorithm implements a change of&#xa; state alarm detection algorithm for text strings.</description></class>
<class packageName="javax.baja.alarm.ext.offnormal" name="BTwoStateAlgorithm"><description>BTwoStateAlgorithm implements a generic algorithm for&#xa; objects with only an normal / offnormal states (vs.</description></class>
</package>
</bajadoc>
