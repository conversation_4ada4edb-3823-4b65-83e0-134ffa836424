<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.io.AsnException" name="AsnException" packageName="javax.baja.bacnet.io" public="true" category="exception">
<description>
An AsnException is thrown whenever a error is&#xa; detected in encoding or decoding an Asn production.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 2$ $Date: 12/19/01 4:35:43 PM$</tag>
<tag name="@creation">28 Jul 00</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.BacnetException"/>
</extends>
<!-- javax.baja.bacnet.io.AsnException(java.lang.String) -->
<constructor name="AsnException" public="true">
<parameter name="detailMessage">
<type class="java.lang.String"/>
<description>
the error message.
</description>
</parameter>
<description>
Constructor with specified detailed message.
</description>
</constructor>

<!-- javax.baja.bacnet.io.AsnException.toString() -->
<method name="toString"  public="true">
<description>
Constructor with error code and detail message.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

</class>
</bajadoc>
