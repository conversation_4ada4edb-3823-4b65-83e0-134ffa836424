<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="javax.baja.bacnet.device.overrides">
<description/>
<class packageName="javax.baja.bacnet.device.overrides" name="ApduSizeOverride" category="interface"><description>Allow manual and persistent definition&#xa; APDU sizes that will be observed while&#xa; communicating with a device.</description></class>
<class packageName="javax.baja.bacnet.device.overrides" name="DeviceOverride" category="interface"><description>Marker interface to support DeviceOverrideAware&#xa; components.</description></class>
<class packageName="javax.baja.bacnet.device.overrides" name="DeviceOverrideAware" category="interface"><description>Device overrides must add themselves to the override&#xa; list of each desired device.</description></class>
<class packageName="javax.baja.bacnet.device.overrides" name="SegmentationOverride" category="interface"><description>Some devices claim SegementationSupport, but really do not&#xa; segment well, or the network conditions cause continuous&#xa; out-of-order packet delivery.</description></class>
<class packageName="javax.baja.bacnet.device.overrides" name="ServiceOverride" category="interface"><description>Allow the creation of components to&#xa; prevent the use of certain services&#xa; to certain devices.</description></class>
</package>
</bajadoc>
