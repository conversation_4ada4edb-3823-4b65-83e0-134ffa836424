<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.point.BPupProxyExt" name="BPupProxyExt" packageName="com.tridium.aapup.point" public="true" abstract="true">
<description>
BPupProxyExt is the base proxy extension for bringing&#xa; point information from all PUP device types into Niagara.
</description>
<tag name="@author">C<PERSON><PERSON></tag>
<tag name="@creation">7/28/2005 1:08PM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.91</tag>
<extends>
<type class="com.tridium.basicdriver.point.BBasicProxyExt"/>
</extends>
<implements>
<type class="com.tridium.basicdriver.util.BIBasicPollable"/>
</implements>
<implements>
<type class="com.tridium.aapup.AaPupConst"/>
</implements>
<property name="channel" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;channel&lt;/code&gt; property.&#xa; the channel number
</description>
<tag name="@see">#getChannel</tag>
<tag name="@see">#setChannel</tag>
</property>

<property name="channelDescription" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;channelDescription&lt;/code&gt; property.&#xa; A text string describing the channel contents
</description>
<tag name="@see">#getChannelDescription</tag>
<tag name="@see">#setChannelDescription</tag>
</property>

<property name="attribute" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;attribute&lt;/code&gt; property.&#xa; the attribute specifier, only 2 characters&#xa; 2 spaces specifies the default attribute (&#x22;  &#x22;)&#xa; &#x22;??&#x22; means unconfigured
</description>
<tag name="@see">#getAttribute</tag>
<tag name="@see">#setAttribute</tag>
</property>

<property name="attributeType" flags="r">
<type class="int"/>
<description>
Slot for the &lt;code&gt;attributeType&lt;/code&gt; property.&#xa; the data type of the attribute
</description>
<tag name="@see">#getAttributeType</tag>
<tag name="@see">#setAttributeType</tag>
</property>

<property name="attributeValue" flags="tr">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;attributeValue&lt;/code&gt; property.&#xa; string representation of the converted data
</description>
<tag name="@see">#getAttributeValue</tag>
<tag name="@see">#setAttributeValue</tag>
</property>

<property name="pollFrequency" flags="">
<type class="javax.baja.driver.util.BPollFrequency"/>
<description>
Slot for the &lt;code&gt;pollFrequency&lt;/code&gt; property.&#xa; the poll frequency bucket this extension is assigned to
</description>
<tag name="@see">#getPollFrequency</tag>
<tag name="@see">#setPollFrequency</tag>
</property>

<property name="prewriteAttribute" flags="h">
<type class="com.tridium.aapup.enums.BPupPrewriteAttributeEnum"/>
<description>
Slot for the &lt;code&gt;prewriteAttribute&lt;/code&gt; property.&#xa; determines if a writable attribute needs to have the &#x22;OI&#x22; or&#xa; &#x22;AM&#x22; attribute set before a write.  Defaults to &#x22;none&#x22; meaning&#xa; no pre-write is required.
</description>
<tag name="@see">#getPrewriteAttribute</tag>
<tag name="@see">#setPrewriteAttribute</tag>
</property>

<action name="forceRead" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;forceRead&lt;/code&gt; action.
</description>
<tag name="@see">#forceRead()</tag>
</action>

<action name="forceWrite" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;forceWrite&lt;/code&gt; action.
</description>
<tag name="@see">#forceWrite()</tag>
</action>

</class>
</bajadoc>
