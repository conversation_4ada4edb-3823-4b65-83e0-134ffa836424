<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="app" runtimeProfile="rt" qualifiedName="javax.baja.app.BAppFolder" name="BAppFolder" packageName="javax.baja.app" public="true">
<description>
Niagara Application Folder
</description>
<tag name="@author">g<PERSON><PERSON><PERSON></tag>
<tag name="@creation">27 Jul 2011</tag>
<tag name="@version">1</tag>
<tag name="@since">Niagara 3.7</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.app.BIAppFolder"/>
</implements>
<!-- javax.baja.app.BAppFolder() -->
<constructor name="BAppFolder" public="true">
<description/>
</constructor>

<!-- javax.baja.app.BAppFolder.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.app.BAppFolder.fw(int, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object) -->
<method name="fw"  public="true">
<description/>
<parameter name="x">
<type class="int"/>
</parameter>
<parameter name="a">
<type class="java.lang.Object"/>
</parameter>
<parameter name="b">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c">
<type class="java.lang.Object"/>
</parameter>
<parameter name="d">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="java.lang.Object"/>
</return>
</method>

<!-- javax.baja.app.BAppFolder.getIcon() -->
<method name="getIcon"  public="true">
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.app.BAppFolder.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true" final="true">
<description/>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.app.BAppFolder.getAppDisplayName(javax.baja.sys.Context) -->
<method name="getAppDisplayName"  public="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.app.BAppFolder.getAppDisplayIcon() -->
<method name="getAppDisplayIcon"  public="true">
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.app.BAppFolder.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
