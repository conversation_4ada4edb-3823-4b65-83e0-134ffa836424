<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.virtual.BBacnetVirtualProperty" name="BBacnetVirtualProperty" packageName="javax.baja.bacnet.virtual" public="true">
<description>
BBacnetVirtualProperty is the virtual representation of one&#xa; property of a BBacnetVirtualObject.  It is responsible for&#xa; managing its own poll subscription.  The actual property&#xa; value is added as a dynamic slot called &#x27;value&#x27;.  The semantics&#xa; of the proxy point framework, e.g., readOk(), readFail(), etc.&#xa; are implemented here as appropriate.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">05 Dec 2007</tag>
<tag name="@since">NiagaraAX 3.3</tag>
<extends>
<type class="javax.baja.virtual.BVirtualComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<implements>
<type class="javax.baja.bacnet.util.BIBacnetPollable"/>
</implements>
<implements>
<type class="javax.baja.status.BIStatus"/>
</implements>
<property name="propertyId" flags="htr">
<type class="int"/>
<description>
Slot for the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyId</tag>
<tag name="@see">#setPropertyId</tag>
</property>

<property name="status" flags="tr">
<type class="javax.baja.status.BStatus"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</property>

<property name="useFacets" flags="htr">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;useFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getUseFacets</tag>
<tag name="@see">#setUseFacets</tag>
</property>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty() -->
<constructor name="BBacnetVirtualProperty" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty(int, javax.baja.sys.BValue, java.lang.String, boolean) -->
<constructor name="BBacnetVirtualProperty" public="true">
<parameter name="propertyId">
<type class="int"/>
<description/>
</parameter>
<parameter name="v">
<type class="javax.baja.sys.BValue"/>
<description>
initial value
</description>
</parameter>
<parameter name="readFault">
<type class="java.lang.String"/>
<description>
null if ok, or a string describing the initial read failure
</description>
</parameter>
<parameter name="useFacets">
<type class="boolean"/>
<description>
whether the virtual object&#x27;s facets apply for this property
</description>
</parameter>
<description>
Constructor used by BBacnetVirtualGateway.
</description>
</constructor>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.getPropertyId() -->
<method name="getPropertyId"  public="true">
<description>
Get the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#propertyId</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.setPropertyId(int) -->
<method name="setPropertyId"  public="true">
<description>
Set the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#propertyId</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.getStatus() -->
<method name="getStatus"  public="true">
<description>
Get the &lt;code&gt;status&lt;/code&gt; property.
</description>
<tag name="@see">#status</tag>
<return>
<type class="javax.baja.status.BStatus"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.setStatus(javax.baja.status.BStatus) -->
<method name="setStatus"  public="true">
<description>
Set the &lt;code&gt;status&lt;/code&gt; property.
</description>
<tag name="@see">#status</tag>
<parameter name="v">
<type class="javax.baja.status.BStatus"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.getUseFacets() -->
<method name="getUseFacets"  public="true">
<description>
Get the &lt;code&gt;useFacets&lt;/code&gt; property.
</description>
<tag name="@see">#useFacets</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.setUseFacets(boolean) -->
<method name="setUseFacets"  public="true">
<description>
Set the &lt;code&gt;useFacets&lt;/code&gt; property.
</description>
<tag name="@see">#useFacets</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.started() -->
<method name="started"  public="true">
<description>
Started.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<description>
Changed.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.subscribed() -->
<method name="subscribed"  public="true">
<description>
Subscribed.&#xa; Subscribe to the poll scheduler for regular updates.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.unsubscribed() -->
<method name="unsubscribed"  public="true">
<description>
Unsubscribed.&#xa; Clear the poll scheduler subscription and set the stale flag.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<description>
BBacnetVirtualProperty must reside under a BBacnetVirtualObject.
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.isChildLegal(javax.baja.sys.BComponent) -->
<method name="isChildLegal"  public="true">
<description>
Overrides the superclass method to remove the restriction&#xa; to virtual components to allow special BACnet datatypes.
</description>
<parameter name="child">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.debugString(javax.baja.sys.Context) -->
<method name="debugString"  public="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.getSlotFacets(javax.baja.sys.Slot) -->
<method name="getSlotFacets"  public="true">
<description/>
<parameter name="s">
<type class="javax.baja.sys.Slot"/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.getValue() -->
<method name="getValue"  public="true">
<description>
Get the virtual property&#x27;s value.&lt;p&gt;&#xa; NOTE: This may be null, if the value property has not been added yet.
</description>
<return>
<type class="javax.baja.sys.BValue"/>
<description>
the property value, in its native datatype.
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.setValue(javax.baja.sys.BValue) -->
<method name="setValue"  public="true">
<description/>
<parameter name="v">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.setValue(javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="setValue"  public="true">
<description/>
<parameter name="v">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.valueToString(javax.baja.sys.Context) -->
<method name="valueToString"  protected="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.getArrayIndex() -->
<method name="getArrayIndex"  protected="true">
<description>
Get the propertyArrayIndex that shall be used when writing this property&#xa; through the VirtualPropertyWrite action.
</description>
<return>
<type class="int"/>
<description>
the value of the &#x27;propertyArrayIndex&#x27; slot if it exists, or the&#xa; default array index.
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.object() -->
<method name="object"  public="true">
<description>
Access to the parent virtual object.
</description>
<return>
<type class="javax.baja.bacnet.virtual.BBacnetVirtualObject"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.readOk(javax.baja.sys.BValue) -->
<method name="readOk"  public="true">
<description>
Called on successful read.  This clears the stale flag,&#xa; sets the value, clears read and write faults, and&#xa; updates the status.
</description>
<parameter name="v">
<type class="javax.baja.sys.BValue"/>
<description>
the value read from the remote device.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.writeOk() -->
<method name="writeOk"  public="true">
<description>
Called on successful write.  This clears the write&#xa; fault and updates the status.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.writeFail(java.lang.String) -->
<method name="writeFail"  public="true">
<description>
Called on write failure.  This sets the write fault&#xa; and updates the status.
</description>
<parameter name="failureMsg">
<type class="java.lang.String"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.updateStatus() -->
<method name="updateStatus"  public="true">
<description>
Update the property status with no flags or facets.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.updateStatus(int, javax.baja.sys.BFacets) -->
<method name="updateStatus"  public="true">
<description>
Update the virtual property&#x27;s status.  This merges the status&#xa; from the parent device with any local status sources, such as&#xa; readFault or stale().  In addition, metadata status information&#xa; is included.  The metadata information is OR&#x27;d with the other&#xa; status information, so if any of the various status sources&#xa; indicate a condition, then it is set for the property&#x27;s status.
</description>
<parameter name="metaBits">
<type class="int"/>
<description/>
</parameter>
<parameter name="metaFacets">
<type class="javax.baja.sys.BFacets"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.childSubscribed(javax.baja.sys.BComponent) -->
<method name="childSubscribed"  public="true">
<description>
Called when a component type value property is subscribed,&#xa; to alert this property to register for polling.
</description>
<parameter name="kid">
<type class="javax.baja.sys.BComponent"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.childUnsubscribed(javax.baja.sys.BComponent) -->
<method name="childUnsubscribed"  public="true">
<description>
Called when a component type value property is unsubscribed,&#xa; to alert this property to unregister from the poll scheduler.
</description>
<parameter name="kid">
<type class="javax.baja.sys.BComponent"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.usePriority() -->
<method name="usePriority"  protected="true">
<description>
Should the write priority be included in a write?&#xa; Currently only true if the propertyId is Present_Value.
</description>
<return>
<type class="boolean"/>
<description>
true if priority is needed.
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.auditWrites() -->
<method name="auditWrites"  protected="true">
<description>
Should writes performed through the BVirtualPropertyWrite dynamic action&#xa; generate separate audit log events?
</description>
<return>
<type class="boolean"/>
<description>
false by default, subclasses may override.
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.getAuditName() -->
<method name="getAuditName"  protected="true">
<description>
Get the name to be used for this virtual property in the AuditEvent&#xa; for writes to the remote device.
</description>
<return>
<type class="java.lang.String"/>
<description>
auditName
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.getReadFault() -->
<method name="getReadFault"  public="true">
<description>
Get the most recent read fault or null if last read was successful.
</description>
<return>
<type class="java.lang.String"/>
<description>
readFault
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.getWriteFault() -->
<method name="getWriteFault"  public="true">
<description>
Get the most recent write fault or null if last write was successful.
</description>
<return>
<type class="java.lang.String"/>
<description>
writeFault
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.getPollFrequency() -->
<method name="getPollFrequency"  public="true">
<description>
Get the component&#x27;s configured poll frequency.
</description>
<return>
<type class="javax.baja.driver.util.BPollFrequency"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.device() -->
<method name="device"  public="true">
<description>
Get the containing device object which will poll this object.
</description>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
<description>
the containing BBacnetDevice
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.getPollableType() -->
<method name="getPollableType"  public="true">
<description>
Get the pollable type of this object.
</description>
<return>
<type class="int"/>
<description>
one of the pollable types defined in BIBacnetPollable.
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.poll() -->
<method name="poll"  public="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Poll the node.
</description>
<tag name="@deprecated">As of 3.2</tag>
<return>
<type class="boolean"/>
<description>
true if a poll was attempted to this node, or&#xa; false if the poll was skipped due to device down, out of service, etc.
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.readFail(java.lang.String) -->
<method name="readFail"  public="true">
<description>
Indicate a failure polling this object.
</description>
<parameter name="failureMsg">
<type class="java.lang.String"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.fromEncodedValue(byte[], javax.baja.status.BStatus, javax.baja.sys.Context) -->
<method name="fromEncodedValue"  public="true">
<description>
Normalize the encoded data into the pollable&#x27;s data structure.
</description>
<parameter name="encodedValue">
<type class="byte" dimension="1"/>
<description/>
</parameter>
<parameter name="status">
<type class="javax.baja.status.BStatus"/>
<description/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description>
must be a PollListEntry.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.getPollListEntries() -->
<method name="getPollListEntries"  public="true">
<description>
Get the list of poll list entries for this pollable.&#xa; The first entry for points must be the configured property.&#xa; If any other properties are called for by the ord syntax,&#xa; they must be added here as well, and they will be included in&#xa; the status of the property.  The other properties are&#xa; specified in the form of a BACnetDeviceObjectPropertyReference.
</description>
<return>
<type class="javax.baja.bacnet.util.PollListEntry" dimension="1"/>
<description>
the list of poll list entries.
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.getDeviceStatus() -->
<method name="getDeviceStatus"  protected="true">
<description>
Get the status of the containing device.
</description>
<return>
<type class="javax.baja.status.BStatus"/>
<description>
the status of the device
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.network() -->
<method name="network"  protected="true">
<description/>
<return>
<type class="javax.baja.bacnet.BBacnetNetwork"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.pollSubscribe() -->
<method name="pollSubscribe"  protected="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.pollUnsubscribe() -->
<method name="pollUnsubscribe"  protected="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.checkSubscription() -->
<method name="checkSubscription"  protected="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.propertyId -->
<field name="propertyId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyId</tag>
<tag name="@see">#setPropertyId</tag>
</field>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.status -->
<field name="status"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</field>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.useFacets -->
<field name="useFacets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;useFacets&lt;/code&gt; property.
</description>
<tag name="@see">#getUseFacets</tag>
<tag name="@see">#setUseFacets</tag>
</field>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.lex -->
<field name="lex"  protected="true" static="true" final="true">
<type class="javax.baja.util.Lexicon"/>
<description/>
</field>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.VALUE -->
<field name="VALUE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.WRITE_ACTION_NAME -->
<field name="WRITE_ACTION_NAME"  protected="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualProperty.DEFAULT_ARRAY_INDEX -->
<field name="DEFAULT_ARRAY_INDEX"  protected="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
