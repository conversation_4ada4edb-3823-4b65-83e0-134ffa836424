<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.outbound.schema.subscription">
<description/>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.subscription" name="BindingSlotFilter"><description>A subscription filter which only matches if the event slot is the first slot in a component when walking up the&#xa; target ord of the bound member.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.subscription" name="BSubscriptionSlotBlacklist"><description>Global slot filter for subscription events.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.subscription" name="ChildSlotRenamedFilter"><description>Subscription filter for property rename events.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.subscription" name="SchemaBoundMemberSubscriber"><description>A subscriber for a specific bound member.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.subscription" name="SlotWhiteListFilter"><description>A subscription filter for when bound objects which specify a set of list of properties for json output.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.subscription" name="Subscription"><description>Immutable properties of a subscription to a binding target, along with event filtering logic.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.subscription" name="SubscriptionFactory"><description>A factory for constructing subscriptions based upon the binding type and target type.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.subscription" name="SubscriptionEventFilter" category="interface"><description>A filter for subscription events.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.subscription" name="SubscriptionEventHandler" category="interface"><description>Contract for handling subscription events in a schema.</description></class>
</package>
</bajadoc>
