<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.history.BBacnetHistoryDeviceExt" name="BBacnetHistoryDeviceExt" packageName="com.tridium.bacnet.history" public="true">
<description>
BHistoryDeviceExt is the base class for mapping historical&#xa; data in a device to Baja history databases.&#xa; /**&#xa; BBacnetHistoryDeviceExt is the class for mapping trend log data&#xa; in a Bacnet device to Baja history databases.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 1$ $Date: 9/02/03 10:54:15 AM$</tag>
<tag name="@creation">02 Sep 03</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.driver.history.BHistoryDeviceExt"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<action name="submitTrendLogDiscoveryJob" flags="h">
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;submitTrendLogDiscoveryJob&lt;/code&gt; action.
</description>
<tag name="@see">#submitTrendLogDiscoveryJob()</tag>
</action>

<action name="getImportTypes" flags="h">
<return>
<type class="javax.baja.sys.BString"/>
</return>
<description>
Slot for the &lt;code&gt;getImportTypes&lt;/code&gt; action.
</description>
<tag name="@see">#getImportTypes()</tag>
</action>

</class>
</bajadoc>
