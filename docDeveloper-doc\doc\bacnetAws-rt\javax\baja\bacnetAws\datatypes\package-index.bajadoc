<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnetAws" runtimeProfile="rt" name="javax.baja.bacnetAws.datatypes">
<description/>
<class packageName="javax.baja.bacnetAws.datatypes" name="BBacnetAccumulatorRecord"><description>BBacnetAccumulatorRecord represents the BACnetAccumulatorRecord sequence.</description></class>
<class packageName="javax.baja.bacnetAws.datatypes" name="BBacnetActionCommand"/>
<class packageName="javax.baja.bacnetAws.datatypes" name="BBacnetActionList"/>
<class packageName="javax.baja.bacnetAws.datatypes" name="BBacnetEventLogRecord"><description>BBacnetEventLogRecord represents the BacneteEventLogRecord sequence.</description></class>
<class packageName="javax.baja.bacnetAws.datatypes" name="BBacnetSessionKey"><description>BBacnetSessionKey represents the BacnetSessionKey data type.</description></class>
<class packageName="javax.baja.bacnetAws.datatypes" name="BBacnetShedLevel"><description>BBacnetScale represents the BACnetScale data type.</description></class>
<class packageName="javax.baja.bacnetAws.datatypes" name="BBacnetVtSession"><description>BBacnetVtSession represents the BACnetVtSession data type.</description></class>
</package>
</bajadoc>
