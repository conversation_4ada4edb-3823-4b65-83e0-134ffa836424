<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.datatypes.BAceReadFileParams" name="BAceReadFileParams" packageName="com.tridium.ace.datatypes" public="true">
<description>
BAceReadFileParams - is container for properties passed in read file action
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">8/12/2017</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="dev" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;dev&lt;/code&gt; property.
</description>
<tag name="@see">#getDev</tag>
<tag name="@see">#setDev</tag>
</property>

<property name="appFileName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;appFileName&lt;/code&gt; property.
</description>
<tag name="@see">#getAppFileName</tag>
<tag name="@see">#setAppFileName</tag>
</property>

</class>
</bajadoc>
