<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.history.BBacnetTrendLogAlarmSourceExt" name="BBacnetTrendLogAlarmSourceExt" packageName="com.tridium.bacnet.history" public="true">
<description>
BBacnetTrendLogAlarmSourceExt defines the intrinsic alarming/notification&#xa; for a server-side trend log exposed to Bacnet.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 1$ $Date: 8/22/03 2:40:15 PM$</tag>
<tag name="@creation">22 Aug 03</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.control.BPointExtension"/>
</extends>
<implements>
<type class="javax.baja.alarm.BIAlarmSource"/>
</implements>
<implements>
<type class="javax.baja.alarm.ext.BIAlarmMessages"/>
</implements>
<implements>
<type class="javax.baja.bacnet.BacnetAlarmConst"/>
</implements>
<property name="notificationThreshold" flags="">
<type class="long"/>
<description>
Slot for the &lt;code&gt;notificationThreshold&lt;/code&gt; property.&#xa; Specifies the value of Records Since Notification&#xa; at which notification occurs
</description>
<tag name="@see">#getNotificationThreshold</tag>
<tag name="@see">#setNotificationThreshold</tag>
</property>

<property name="recordsSinceNotification" flags="tr">
<type class="long"/>
<description>
Slot for the &lt;code&gt;recordsSinceNotification&lt;/code&gt; property.&#xa; The number of records collected since the previous notification
</description>
<tag name="@see">#getRecordsSinceNotification</tag>
<tag name="@see">#setRecordsSinceNotification</tag>
</property>

<property name="lastNotifyRecord" flags="rt">
<type class="long"/>
<description>
Slot for the &lt;code&gt;lastNotifyRecord&lt;/code&gt; property.&#xa; The sequence number associated with the most recently collected&#xa; record whose collection triggered a notification.
</description>
<tag name="@see">#getLastNotifyRecord</tag>
<tag name="@see">#setLastNotifyRecord</tag>
</property>

<property name="alarmInhibit" flags="">
<type class="javax.baja.status.BStatusBoolean"/>
<description>
Slot for the &lt;code&gt;alarmInhibit&lt;/code&gt; property.&#xa; Inhibits alarm generation.
</description>
<tag name="@see">#getAlarmInhibit</tag>
<tag name="@see">#setAlarmInhibit</tag>
</property>

<property name="alarmState" flags="tr">
<type class="javax.baja.alarm.ext.BAlarmState"/>
<description>
Slot for the &lt;code&gt;alarmState&lt;/code&gt; property.&#xa; Shows the object&#x27;s current alarm state.
</description>
<tag name="@see">#getAlarmState</tag>
<tag name="@see">#setAlarmState</tag>
</property>

<property name="alarmEnable" flags="">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
<description>
Slot for the &lt;code&gt;alarmEnable&lt;/code&gt; property.&#xa; Flags that define the types of alarm transitions for this object that will generate alarm.
</description>
<tag name="@see">#getAlarmEnable</tag>
<tag name="@see">#setAlarmEnable</tag>
</property>

<property name="ackedTransitions" flags="tr">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
<description>
Slot for the &lt;code&gt;ackedTransitions&lt;/code&gt; property.&#xa; Flags, that when cleared, indicate that an unacknowledged alarm transition has occurred.
</description>
<tag name="@see">#getAckedTransitions</tag>
<tag name="@see">#setAckedTransitions</tag>
</property>

<property name="notifyType" flags="">
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
<description>
Slot for the &lt;code&gt;notifyType&lt;/code&gt; property.&#xa; If set to event (not alarm), an active unacknowledged alarm is not reported by the station&#x27;s Bacnet service.
</description>
<tag name="@see">#getNotifyType</tag>
<tag name="@see">#setNotifyType</tag>
</property>

<property name="toOffnormalTimes" flags="tr">
<type class="javax.baja.alarm.ext.BAlarmTimestamps"/>
<description>
Slot for the &lt;code&gt;toOffnormalTimes&lt;/code&gt; property.&#xa; eventTime, ackTime and count for last to offnormal event.
</description>
<tag name="@see">#getToOffnormalTimes</tag>
<tag name="@see">#setToOffnormalTimes</tag>
</property>

<property name="toFaultTimes" flags="tr">
<type class="javax.baja.alarm.ext.BAlarmTimestamps"/>
<description>
Slot for the &lt;code&gt;toFaultTimes&lt;/code&gt; property.&#xa; eventTime, ackTime and count for last to fault event.
</description>
<tag name="@see">#getToFaultTimes</tag>
<tag name="@see">#setToFaultTimes</tag>
</property>

<property name="toNormalTimes" flags="tr">
<type class="javax.baja.alarm.ext.BAlarmTimestamps"/>
<description>
Slot for the &lt;code&gt;toNormalTimes&lt;/code&gt; property.&#xa; eventTime, ackTime and count for last to normal event.
</description>
<tag name="@see">#getToNormalTimes</tag>
<tag name="@see">#setToNormalTimes</tag>
</property>

<property name="toNormalText" flags="">
<type class="javax.baja.util.BFormat"/>
<description>
Slot for the &lt;code&gt;toNormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-normal alarm for this object.
</description>
<tag name="@see">#getToNormalText</tag>
<tag name="@see">#setToNormalText</tag>
</property>

<property name="hyperlinkOrd" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;hyperlinkOrd&lt;/code&gt; property.&#xa; Ord to link to when we have a to-normal alarm for this object.
</description>
<tag name="@see">#getHyperlinkOrd</tag>
<tag name="@see">#setHyperlinkOrd</tag>
</property>

<property name="alarmClass" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;alarmClass&lt;/code&gt; property.&#xa; This is the alarm class used for this object.
</description>
<tag name="@see">#getAlarmClass</tag>
<tag name="@see">#setAlarmClass</tag>
</property>

<action name="ackAlarm" flags="h">
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
<description>
Slot for the &lt;code&gt;ackAlarm&lt;/code&gt; action.&#xa; Acknowledge the alarm matching this ack request
</description>
<tag name="@see">#ackAlarm(BAlarmRecord parameter)</tag>
</action>

</class>
</bajadoc>
