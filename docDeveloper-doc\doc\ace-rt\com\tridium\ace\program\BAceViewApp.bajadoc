<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.program.BAceViewApp" name="BAceViewApp" packageName="com.tridium.ace.program" public="true">
<description>
<PERSON>ceViewApp uploads the current contents of running app and creates database version.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">7/18/2018</tag>
<extends>
<type class="javax.baja.job.BSimpleJob"/>
</extends>
<action name="getMissingModules" flags="h">
<return>
<type class="javax.baja.sys.BString"/>
</return>
<description>
Slot for the &lt;code&gt;getMissingModules&lt;/code&gt; action.
</description>
<tag name="@see">#getMissingModules()</tag>
</action>

</class>
</bajadoc>
