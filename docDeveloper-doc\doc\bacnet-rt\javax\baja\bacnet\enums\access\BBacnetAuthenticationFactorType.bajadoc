<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType" name="BBacnetAuthenticationFactorType" packageName="javax.baja.bacnet.enums.access" public="true" final="true">
<description>
BBacnetAccessCredentialDisable represents the Bacnet&#xa; Access Credential Disable enumeration.&#xa; &lt;p&gt;&#xa; BBacnetAccessCredentialDisable is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Joseph Chandler</tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;undefined&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>0</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;error&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>1</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;custom&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>2</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;simpleNumber16&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>3</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;simpleNumber32&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>4</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;simpleNumber56&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>5</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;simpleAlphaNumeric&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>6</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;abaTrack2&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>7</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;wiegand26&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>8</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;wiegand37&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>9</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;wiegand37Facility&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>10</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;facility16Card32&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>11</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;facility32Card32&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>12</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;fascN&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>13</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;fascNBcd&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>14</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;fascNLarge&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>15</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;fascNLargeBcd&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>16</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;gsa75&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>17</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;chuid&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>18</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;chuidFull&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>19</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;guid&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>20</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;cbeffA&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>21</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;cbeffB&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>22</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;cbeffC&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>23</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;userPassword&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>24</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.UNDEFINED -->
<field name="UNDEFINED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for undefined.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.ERROR -->
<field name="ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for error.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.CUSTOM -->
<field name="CUSTOM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for custom.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.SIMPLE_NUMBER_16 -->
<field name="SIMPLE_NUMBER_16"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for simpleNumber16.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.SIMPLE_NUMBER_32 -->
<field name="SIMPLE_NUMBER_32"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for simpleNumber32.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.SIMPLE_NUMBER_56 -->
<field name="SIMPLE_NUMBER_56"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for simpleNumber56.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.SIMPLE_ALPHA_NUMERIC -->
<field name="SIMPLE_ALPHA_NUMERIC"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for simpleAlphaNumeric.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.ABA_TRACK_2 -->
<field name="ABA_TRACK_2"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for abaTrack2.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.WIEGAND_26 -->
<field name="WIEGAND_26"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for wiegand26.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.WIEGAND_37 -->
<field name="WIEGAND_37"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for wiegand37.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.WIEGAND_37FACILITY -->
<field name="WIEGAND_37FACILITY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for wiegand37Facility.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.FACILITY_16CARD_32 -->
<field name="FACILITY_16CARD_32"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for facility16Card32.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.FACILITY_32CARD_32 -->
<field name="FACILITY_32CARD_32"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for facility32Card32.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.FASC_N -->
<field name="FASC_N"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for fascN.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.FASC_NBCD -->
<field name="FASC_NBCD"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for fascNBcd.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.FASC_NLARGE -->
<field name="FASC_NLARGE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for fascNLarge.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.FASC_NLARGE_BCD -->
<field name="FASC_NLARGE_BCD"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for fascNLargeBcd.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.GSA_75 -->
<field name="GSA_75"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for gsa75.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.CHUID -->
<field name="CHUID"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for chuid.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.CHUID_FULL -->
<field name="CHUID_FULL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for chuidFull.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.GUID -->
<field name="GUID"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for guid.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.CBEFF_A -->
<field name="CBEFF_A"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for cbeffA.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.CBEFF_B -->
<field name="CBEFF_B"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for cbeffB.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.CBEFF_C -->
<field name="CBEFF_C"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for cbeffC.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.USER_PASSWORD -->
<field name="USER_PASSWORD"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for userPassword.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.undefined -->
<field name="undefined"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for undefined.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.error -->
<field name="error"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for error.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.custom -->
<field name="custom"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for custom.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.simpleNumber16 -->
<field name="simpleNumber16"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for simpleNumber16.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.simpleNumber32 -->
<field name="simpleNumber32"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for simpleNumber32.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.simpleNumber56 -->
<field name="simpleNumber56"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for simpleNumber56.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.simpleAlphaNumeric -->
<field name="simpleAlphaNumeric"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for simpleAlphaNumeric.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.abaTrack2 -->
<field name="abaTrack2"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for abaTrack2.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.wiegand26 -->
<field name="wiegand26"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for wiegand26.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.wiegand37 -->
<field name="wiegand37"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for wiegand37.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.wiegand37Facility -->
<field name="wiegand37Facility"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for wiegand37Facility.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.facility16Card32 -->
<field name="facility16Card32"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for facility16Card32.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.facility32Card32 -->
<field name="facility32Card32"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for facility32Card32.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.fascN -->
<field name="fascN"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for fascN.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.fascNBcd -->
<field name="fascNBcd"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for fascNBcd.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.fascNLarge -->
<field name="fascNLarge"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for fascNLarge.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.fascNLargeBcd -->
<field name="fascNLargeBcd"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for fascNLargeBcd.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.gsa75 -->
<field name="gsa75"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for gsa75.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.chuid -->
<field name="chuid"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for chuid.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.chuidFull -->
<field name="chuidFull"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for chuidFull.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.guid -->
<field name="guid"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for guid.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.cbeffA -->
<field name="cbeffA"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for cbeffA.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.cbeffB -->
<field name="cbeffB"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for cbeffB.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.cbeffC -->
<field name="cbeffC"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for cbeffC.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.userPassword -->
<field name="userPassword"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description>
BBacnetAuthenticationFactorType constant for userPassword.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthenticationFactorType.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
