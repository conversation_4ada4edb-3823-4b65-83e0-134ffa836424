<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.ViewQuery" name="ViewQuery" packageName="javax.baja.naming" public="true">
<description>
ViewQuery defines user agent information.&#xa; &lt;pre&gt;&#xa;   view       := [viewId] [&#x22;?&#x22; params] &#xa;   params     := param ( &#x22;;&#x22; param )*&#xa;   param      := name &#x22;=&#x22; value&#xa;   name       := spaces paramToken spaces&#xa;   value      := paramToken&#xa;   paramToken := paramChar (paramChar)*&#xa;   paramChar  := (0x32 - 0x127) except | ; = % ?&#xa; &lt;/pre&gt;
</description>
<tag name="@author"><PERSON> on 6 Jan 03</tag>
<tag name="@version">$Revision: 10$ $Date: 1/18/06 10:29:27 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="javax.baja.naming.OrdQuery"/>
</implements>
<!-- javax.baja.naming.ViewQuery(java.lang.String, java.lang.String) -->
<constructor name="ViewQuery" public="true">
<parameter name="scheme">
<type class="java.lang.String"/>
</parameter>
<parameter name="body">
<type class="java.lang.String"/>
</parameter>
<throws>
<type class="javax.baja.naming.SyntaxException"/>
<description>
if the body isn&#x27;t a valid view body.
</description>
</throws>
<description>
Construct an ViewQuery with the specified scheme and body.
</description>
</constructor>

<!-- javax.baja.naming.ViewQuery(java.lang.String) -->
<constructor name="ViewQuery" public="true">
<parameter name="body">
<type class="java.lang.String"/>
</parameter>
<throws>
<type class="javax.baja.naming.SyntaxException"/>
</throws>
<description>
Convenience with &#x22;view&#x22; scheme.
</description>
</constructor>

<!-- javax.baja.naming.ViewQuery.getViewId() -->
<method name="getViewId"  public="true">
<description>
Get the view id string or return null if not specified.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.ViewQuery.getParameterNames() -->
<method name="getParameterNames"  public="true">
<description>
Get the array of parameter names.
</description>
<return>
<type class="java.lang.String" dimension="1"/>
</return>
</method>

<!-- javax.baja.naming.ViewQuery.getParameter(java.lang.String) -->
<method name="getParameter"  public="true">
<description>
Get the parameter with the specified key name.
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.ViewQuery.getParameter(java.lang.String, java.lang.String) -->
<method name="getParameter"  public="true">
<description>
Get the parameter with the specified key name.  If&#xa; not found then return the default value.
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="defaultValue">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.ViewQuery.getParameters() -->
<method name="getParameters"  public="true">
<description>
Return the parameters for the view query.
</description>
<tag name="@since">Niagara 4.8</tag>
<return>
<parameterizedType class="java.util.Map">
<args>
<type class="java.lang.String"/>
<type class="java.lang.String"/>
</args>
</parameterizedType>
<description>
An unmodifiable map of parameter names to values.
</description>
</return>
</method>

<!-- javax.baja.naming.ViewQuery.isHost() -->
<method name="isHost"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return false.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.ViewQuery.isSession() -->
<method name="isSession"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return false.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.ViewQuery.normalize(javax.baja.naming.OrdQueryList, int) -->
<method name="normalize"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
If the query at index+1 is also a ViewQuery, then perform&#xa; a merge using the &lt;code&gt;merge()&lt;/code&gt; method.  Otherwise&#xa; if this query is not the last query in the list, strip it.
</description>
<parameter name="list">
<type class="javax.baja.naming.OrdQueryList"/>
</parameter>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.ViewQuery.merge(javax.baja.naming.ViewQuery) -->
<method name="merge"  public="true">
<description>
Merge this view query with the specified view query.&#xa; If both queries specify a view id, or a parameter with&#xa; the same name, then the specified query trumps this one.
</description>
<parameter name="a">
<type class="javax.baja.naming.ViewQuery"/>
</parameter>
<return>
<type class="javax.baja.naming.ViewQuery"/>
</return>
</method>

<!-- javax.baja.naming.ViewQuery.getScheme() -->
<method name="getScheme"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return the scheme field.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.ViewQuery.getBody() -->
<method name="getBody"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return the body field.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.ViewQuery.toString() -->
<method name="toString"  public="true">
<description>
Return &lt;code&gt;scheme + &amp;#x22;:&amp;#x22; + body&lt;/code&gt;.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

</class>
</bajadoc>
