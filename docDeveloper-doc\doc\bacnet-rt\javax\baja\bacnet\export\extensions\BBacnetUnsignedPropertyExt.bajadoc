<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt" name="BBacnetUnsignedPropertyExt" packageName="javax.baja.bacnet.export.extensions" public="true">
<description>
Point extension for BACnet Unsigned Properties.
</description>
<tag name="@author"><PERSON><PERSON><PERSON> on 05/03/2019</tag>
<extends>
<type class="javax.baja.control.BPointExtension"/>
</extends>
<property name="status" flags="r">
<type class="javax.baja.status.BStatus"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</property>

<property name="faultCause" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</property>

<property name="objectId" flags="r">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="propertyId" flags="r">
<type class="int"/>
<description>
Slot for the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyId</tag>
<tag name="@see">#setPropertyId</tag>
</property>

<!-- javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt() -->
<constructor name="BBacnetUnsignedPropertyExt" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int) -->
<constructor name="BBacnetUnsignedPropertyExt" public="true">
<parameter name="oid">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="propertyId">
<type class="int"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt.getStatus() -->
<method name="getStatus"  public="true">
<description>
Get the &lt;code&gt;status&lt;/code&gt; property.
</description>
<tag name="@see">#status</tag>
<return>
<type class="javax.baja.status.BStatus"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt.setStatus(javax.baja.status.BStatus) -->
<method name="setStatus"  public="true">
<description>
Set the &lt;code&gt;status&lt;/code&gt; property.
</description>
<tag name="@see">#status</tag>
<parameter name="v">
<type class="javax.baja.status.BStatus"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt.getFaultCause() -->
<method name="getFaultCause"  public="true">
<description>
Get the &lt;code&gt;faultCause&lt;/code&gt; property.
</description>
<tag name="@see">#faultCause</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt.setFaultCause(java.lang.String) -->
<method name="setFaultCause"  public="true">
<description>
Set the &lt;code&gt;faultCause&lt;/code&gt; property.
</description>
<tag name="@see">#faultCause</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt.getObjectId() -->
<method name="getObjectId"  public="true">
<description>
Get the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#objectId</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt.setObjectId(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="setObjectId"  public="true">
<description>
Set the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#objectId</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt.getPropertyId() -->
<method name="getPropertyId"  public="true">
<description>
Get the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#propertyId</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt.setPropertyId(int) -->
<method name="setPropertyId"  public="true">
<description>
Set the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#propertyId</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt.onExecute(javax.baja.status.BStatusValue, javax.baja.sys.Context) -->
<method name="onExecute"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="bStatusValue">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt.status -->
<field name="status"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</field>

<!-- javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt.faultCause -->
<field name="faultCause"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</field>

<!-- javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt.propertyId -->
<field name="propertyId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyId</tag>
<tag name="@see">#setPropertyId</tag>
</field>

<!-- javax.baja.bacnet.export.extensions.BBacnetUnsignedPropertyExt.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
