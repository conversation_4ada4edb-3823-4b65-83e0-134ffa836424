<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet" name="BBacnetNetworkSecurityKeySet" packageName="javax.baja.bacnet.datatypes.security" public="true" final="true">
<description>
BBacnetNetworkSecurityKeySet represents the BACnetNetworkSecurityKeySet&#xa; sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="keyRevision" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;keyRevision&lt;/code&gt; property.
</description>
<tag name="@see">#getKeyRevision</tag>
<tag name="@see">#setKeyRevision</tag>
</property>

<property name="activationTime" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
<description>
Slot for the &lt;code&gt;activationTime&lt;/code&gt; property.
</description>
<tag name="@see">#getActivationTime</tag>
<tag name="@see">#setActivationTime</tag>
</property>

<property name="expirationTime" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
<description>
Slot for the &lt;code&gt;expirationTime&lt;/code&gt; property.
</description>
<tag name="@see">#getExpirationTime</tag>
<tag name="@see">#setExpirationTime</tag>
</property>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet() -->
<constructor name="BBacnetNetworkSecurityKeySet" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet(javax.baja.bacnet.datatypes.BBacnetDateTime, javax.baja.bacnet.datatypes.BBacnetDateTime, int, java.util.Collection&lt;javax.baja.bacnet.datatypes.security.BBacnetKeyIdentifier&gt;) -->
<constructor name="BBacnetNetworkSecurityKeySet" public="true">
<parameter name="activationTime">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</parameter>
<parameter name="expirationTime">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</parameter>
<parameter name="keyRevision">
<type class="int"/>
</parameter>
<parameter name="keyIds">
<parameterizedType class="java.util.Collection">
<args>
<type class="javax.baja.bacnet.datatypes.security.BBacnetKeyIdentifier"/>
</args>
</parameterizedType>
</parameter>
<description>
Standard constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet.getKeyRevision() -->
<method name="getKeyRevision"  public="true">
<description>
Get the &lt;code&gt;keyRevision&lt;/code&gt; property.
</description>
<tag name="@see">#keyRevision</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet.setKeyRevision(int) -->
<method name="setKeyRevision"  public="true">
<description>
Set the &lt;code&gt;keyRevision&lt;/code&gt; property.
</description>
<tag name="@see">#keyRevision</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet.getActivationTime() -->
<method name="getActivationTime"  public="true">
<description>
Get the &lt;code&gt;activationTime&lt;/code&gt; property.
</description>
<tag name="@see">#activationTime</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet.setActivationTime(javax.baja.bacnet.datatypes.BBacnetDateTime) -->
<method name="setActivationTime"  public="true">
<description>
Set the &lt;code&gt;activationTime&lt;/code&gt; property.
</description>
<tag name="@see">#activationTime</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet.getExpirationTime() -->
<method name="getExpirationTime"  public="true">
<description>
Get the &lt;code&gt;expirationTime&lt;/code&gt; property.
</description>
<tag name="@see">#expirationTime</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet.setExpirationTime(javax.baja.bacnet.datatypes.BBacnetDateTime) -->
<method name="setExpirationTime"  public="true">
<description>
Set the &lt;code&gt;expirationTime&lt;/code&gt; property.
</description>
<tag name="@see">#expirationTime</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description/>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet.keyRevision -->
<field name="keyRevision"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;keyRevision&lt;/code&gt; property.
</description>
<tag name="@see">#getKeyRevision</tag>
<tag name="@see">#setKeyRevision</tag>
</field>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet.activationTime -->
<field name="activationTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;activationTime&lt;/code&gt; property.
</description>
<tag name="@see">#getActivationTime</tag>
<tag name="@see">#setActivationTime</tag>
</field>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet.expirationTime -->
<field name="expirationTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;expirationTime&lt;/code&gt; property.
</description>
<tag name="@see">#getExpirationTime</tag>
<tag name="@see">#setExpirationTime</tag>
</field>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet.KEY_REVISION_TAG -->
<field name="KEY_REVISION_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet.ACTIVATION_TAG -->
<field name="ACTIVATION_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet.EXPIRATION_TAG -->
<field name="EXPIRATION_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.security.BBacnetNetworkSecurityKeySet.KEY_IDS_TAG -->
<field name="KEY_IDS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
