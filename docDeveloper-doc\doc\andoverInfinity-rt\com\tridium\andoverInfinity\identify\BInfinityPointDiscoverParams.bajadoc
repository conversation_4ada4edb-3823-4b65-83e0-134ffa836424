<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.identify.BInfinityPointDiscoverParams" name="BInfinityPointDiscoverParams" packageName="com.tridium.andoverInfinity.identify" public="true">
<description>
BInfinityPointDiscoverParams
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 22, 2007</tag>
<tag name="@version">$Revision$ $May 22, 2007 9:47:21 AM$</tag>
<tag name="@since"/>
<extends>
<type class="com.tridium.ddf.identify.BDdfDiscoverParams"/>
</extends>
<property name="step" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;step&lt;/code&gt; property.&#xa; indicates a step in the learn process
</description>
<tag name="@see">#getStep</tag>
<tag name="@see">#setStep</tag>
</property>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointDiscoverParams() -->
<constructor name="BInfinityPointDiscoverParams" public="true">
<description>
Default constructor
</description>
</constructor>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointDiscoverParams(int) -->
<constructor name="BInfinityPointDiscoverParams" public="true">
<parameter name="step">
<type class="int"/>
</parameter>
<description>
Constructor with a step
</description>
</constructor>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointDiscoverParams.getStep() -->
<method name="getStep"  public="true">
<description>
Get the &lt;code&gt;step&lt;/code&gt; property.&#xa; indicates a step in the learn process
</description>
<tag name="@see">#step</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointDiscoverParams.setStep(int) -->
<method name="setStep"  public="true">
<description>
Set the &lt;code&gt;step&lt;/code&gt; property.&#xa; indicates a step in the learn process
</description>
<tag name="@see">#step</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointDiscoverParams.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointDiscoverParams.getDiscoverRequestType() -->
<method name="getDiscoverRequestType"  public="true">
<description>
Get the discover request type
</description>
<tag name="@see">com.tridium.ddf.identify.BIDdfDiscoverParams#getDiscoverRequestType()</tag>
<return>
<type class="javax.baja.sys.Type"/>
<description>
&lt;code&gt;BInfinityPointDiscoverRequest.TYPE&lt;/code&gt;
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointDiscoverParams.getDiscoveryLeafType() -->
<method name="getDiscoveryLeafType"  public="true">
<description>
Get the discovery leaf type
</description>
<tag name="@see">com.tridium.ddf.identify.BIDdfDiscoverParams#getDiscoveryLeafType()</tag>
<return>
<type class="javax.baja.sys.Type"/>
<description>
&lt;code&gt;BInfinityPointDiscoveryLeaf.TYPE&lt;/code&gt;
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointDiscoverParams.getFirst() -->
<method name="getFirst"  public="true">
<description>
We will attempt to discover all points within the confines of&#xa;  a single request, which will most likely have multiple req/rsp&#xa;  sub parts (we need to hit F4, &#x22;V&#x22;iew , Points , and then&#xa;  possibly a PageDown.  May even need to clean up by F4/F4 to get back&#xa;  to the command console.  Therefore, getFirst() really only needs to&#xa;  return a dummy instance with the &#x27;step&#x27; set to 0
</description>
<return>
<type class="com.tridium.ddf.identify.BIDdfDiscoverParams"/>
<description>
a new &lt;code&gt;BInfinityPointDiscoverParams&lt;/code&gt; with step set to 0
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointDiscoverParams.getLast() -->
<method name="getLast"  public="true">
<description>
Return a &lt;code&gt;BInfinityPointDiscoverParams&lt;/code&gt; with step set to 1 to&#xa; terminate the learn sequence after one step
</description>
<tag name="@see">com.tridium.ddf.identify.BIDdfDiscoverParams#getLast()</tag>
<return>
<type class="com.tridium.ddf.identify.BIDdfDiscoverParams"/>
<description>
a new &lt;code&gt;BInfinityPointDiscoverParams&lt;/code&gt; with step set to 1
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointDiscoverParams.getNext() -->
<method name="getNext"  public="true">
<description>
getNext() should return an BInfinityPointDiscoverParams&lt;/code&gt; identical&#xa; to one created by &lt;code&gt;getLast()&lt;/code&gt; to ensure only one iteration.
</description>
<tag name="@see">com.tridium.ddf.identify.BIDdfDiscoverParams#getNext()</tag>
<return>
<type class="com.tridium.ddf.identify.BIDdfDiscoverParams"/>
<description>
a new &lt;code&gt;BInfinityPointDiscoverParams&lt;/code&gt; with step set to 1
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointDiscoverParams.isAfter(com.tridium.ddf.identify.BIDdfDiscoverParams) -->
<method name="isAfter"  public="true">
<description>
isAfter is based on the step
</description>
<tag name="@see">com.tridium.ddf.identify.BIDdfDiscoverParams#isAfter(com.tridium.ddf.identify.BIDdfDiscoverParams)</tag>
<parameter name="anotherId">
<type class="com.tridium.ddf.identify.BIDdfDiscoverParams"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this object&#x27;s step is larger than the arg&#x27;s step
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointDiscoverParams.step -->
<field name="step"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;step&lt;/code&gt; property.&#xa; indicates a step in the learn process
</description>
<tag name="@see">#getStep</tag>
<tag name="@see">#setStep</tag>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityPointDiscoverParams.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
