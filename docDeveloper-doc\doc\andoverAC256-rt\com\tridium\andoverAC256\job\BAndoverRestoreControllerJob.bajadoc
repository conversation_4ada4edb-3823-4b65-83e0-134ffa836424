<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.job.BAndoverRestoreControllerJob" name="BAndoverRestoreControllerJob" packageName="com.tridium.andoverAC256.job" public="true">
<description>
Andover AC256 Restore a controller&#x27;s program.&#xa; This job class handles performing restores of the program&#xa; in an Andover controller, getting the program from a file&#xa; stored locally on the jace in the backups folder&#xa; &lt;p&gt;
</description>
<tag name="@author">Clif <PERSON></tag>
<tag name="@creation">5/6/2005 9:56AM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.78</tag>
<extends>
<type class="javax.baja.job.BJob"/>
</extends>
<implements>
<type class="com.tridium.andoverAC256.messages.AndoverMessageConst"/>
</implements>
<implements>
<type class="java.lang.Runnable"/>
</implements>
<action name="abortReload" flags="a">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;abortReload&lt;/code&gt; action.
</description>
<tag name="@see">#abortReload()</tag>
</action>

<topic name="jobComplete" flags="">
<eventType>
<type class="javax.baja.sys.BValue"/>
</eventType><description>
Slot for the &lt;code&gt;jobComplete&lt;/code&gt; topic.
</description>
<tag name="@see">#fireJobComplete</tag>
</topic>

</class>
</bajadoc>
