<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.comm.BAndoverUnsolicitedReceive" name="BAndoverUnsolicitedReceive" packageName="com.tridium.andoverAC256.comm" public="true">
<description>
This class customizes unsolicited receive handling for the&#xa; Andover driver. Most functionality is inherited from BBasicUnsolicitedReceive.&#xa;&#xa; This component is a member of BAndoverDevice.
</description>
<extends>
<type class="com.tridium.basicdriver.util.BBasicUnsolicitedReceive"/>
</extends>
</class>
</bajadoc>
