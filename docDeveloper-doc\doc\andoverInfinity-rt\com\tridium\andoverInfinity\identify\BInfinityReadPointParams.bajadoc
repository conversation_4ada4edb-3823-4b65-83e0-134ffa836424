<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.identify.BInfinityReadPointParams" name="BInfinityReadPointParams" packageName="com.tridium.andoverInfinity.identify" public="true">
<description>
BInfinityReadPointParams&#xa; &#xa; All we need to poll a point is the device name and the point name.&#xa; Device name is handled at the device level, so all we need here is&#xa; a point name.
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 22, 2007</tag>
<tag name="@version">$Revision$ $May 22, 2007 9:49:55 AM$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.identify.BDdfReadParams"/>
</extends>
<property name="pointName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;pointName&lt;/code&gt; property.
</description>
<tag name="@see">#getPointName</tag>
<tag name="@see">#setPointName</tag>
</property>

<!-- com.tridium.andoverInfinity.identify.BInfinityReadPointParams() -->
<constructor name="BInfinityReadPointParams" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.identify.BInfinityReadPointParams.getPointName() -->
<method name="getPointName"  public="true">
<description>
Get the &lt;code&gt;pointName&lt;/code&gt; property.
</description>
<tag name="@see">#pointName</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityReadPointParams.setPointName(java.lang.String) -->
<method name="setPointName"  public="true">
<description>
Set the &lt;code&gt;pointName&lt;/code&gt; property.
</description>
<tag name="@see">#pointName</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityReadPointParams.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityReadPointParams.getReadRequestType() -->
<method name="getReadRequestType"  public="true">
<description>
Infinity uses a &lt;code&gt;BInfinityReadPointRequest.TYPE&lt;/code&gt; to read points
</description>
<tag name="@see">com.tridium.devDriver.identify.BIDevReadParams#getReadRequestType()</tag>
<return>
<type class="javax.baja.sys.Type"/>
<description>
&lt;code&gt;BInfinityReadPointRequest.TYPE&lt;/code&gt;
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityReadPointParams.pointName -->
<field name="pointName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;pointName&lt;/code&gt; property.
</description>
<tag name="@see">#getPointName</tag>
<tag name="@see">#setPointName</tag>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityReadPointParams.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
