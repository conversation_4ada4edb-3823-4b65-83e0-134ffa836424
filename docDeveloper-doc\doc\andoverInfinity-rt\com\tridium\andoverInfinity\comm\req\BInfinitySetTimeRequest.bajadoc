<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.req.BInfinitySetTimeRequest" name="BInfinitySetTimeRequest" packageName="com.tridium.andoverInfinity.comm.req" public="true">
<description>
Used to navigate the Edit/System Date and Time menus to set the &#xa; controller time to the curren station date/time.
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.comm.req.BDdfRequest"/>
</extends>
<implements>
<type class="com.tridium.andoverInfinity.comm.Vt100Const"/>
</implements>
<implements>
<type class="com.tridium.andoverInfinity.comm.req.RequiresNetworkAccess"/>
</implements>
<!-- com.tridium.andoverInfinity.comm.req.BInfinitySetTimeRequest() -->
<constructor name="BInfinitySetTimeRequest" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.comm.req.BInfinitySetTimeRequest.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinitySetTimeRequest.processReceive(com.tridium.ddf.comm.IDdfDataFrame) -->
<method name="processReceive"  public="true">
<description>
The sent byte array, if successful, should leave the screen with the &#xa; cursor on the top line (CURSOR_IS_ON_HOME_VIEW_EDIT_CONNECT_LOGOUT).&#xa; Anything else is an error.
</description>
<tag name="@see">com.tridium.devDriver.comm.req.BIDdfRequest#processReceive(com.tridium.devDriver.comm.IDevDataFrame)</tag>
<parameter name="iDevDataFrame">
<type class="com.tridium.ddf.comm.IDdfDataFrame"/>
</parameter>
<return>
<type class="com.tridium.ddf.comm.rsp.BIDdfResponse"/>
</return>
<throws>
<type class="com.tridium.ddf.comm.rsp.DdfResponseException"/>
</throws>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinitySetTimeRequest.toByteArray() -->
<method name="toByteArray"  public="true">
<description>
Form a byte array.  Prepend any cursor moves or change device commands.
</description>
<tag name="@see">com.tridium.devDriver.comm.req.BIDdfRequest#toByteArray()</tag>
<return>
<type class="byte" dimension="1"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinitySetTimeRequest.setNetwork(com.tridium.andoverInfinity.BInfinityNetwork) -->
<method name="setNetwork"  public="true">
<description>
Implementation of RequiresNetworkAccess interface
</description>
<tag name="@see">com.tridium.andoverInfinity.comm.req.RequiresNetworkAccess#setNetwork(com.tridium.andoverInfinity.BInfinityNetwork)</tag>
<parameter name="network">
<type class="com.tridium.andoverInfinity.BInfinityNetwork"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinitySetTimeRequest.remainingRetryCount -->
<field name="remainingRetryCount"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;remainingRetryCount&lt;/code&gt; property.&#xa; This is the remaining number of retry counts that the BIDdfCommunicator&#xa; Will attempt before timing out completely.
</description>
<tag name="@see">#getRemainingRetryCount</tag>
<tag name="@see">#setRemainingRetryCount</tag>
</field>

<!-- com.tridium.andoverInfinity.comm.req.BInfinitySetTimeRequest.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
