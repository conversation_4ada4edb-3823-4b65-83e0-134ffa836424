<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="wb" qualifiedName="com.tridium.andoverAC256.ui.BAndoverConsole" name="BAndoverConsole" packageName="com.tridium.andoverAC256.ui" public="true">
<description>
BAndoverConsole - This view provides the user with a dumb terminal into an AC256
</description>
<tag name="@author">C<PERSON><PERSON></tag>
<tag name="@creation">10/17/2005 10:35AM</tag>
<tag name="@version">$Revision:1$ $Date: 10/17/2005 10:35AM$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.workbench.view.BWbComponentView"/>
</extends>
<property name="displayText" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;displayText&lt;/code&gt; property.
</description>
<tag name="@see">#getDisplayText</tag>
<tag name="@see">#setDisplayText</tag>
</property>

<action name="finalScrollFix" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;finalScrollFix&lt;/code&gt; action.
</description>
<tag name="@see">#finalScrollFix()</tag>
</action>

</class>
</bajadoc>
