<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BBacnetLoop" name="BBacnetLoop" packageName="javax.baja.bacnet.config" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@creation">26 Jul 2005</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.1</tag>
<extends>
<type class="javax.baja.bacnet.BBacnetObject"/>
</extends>
<property name="presentValue" flags="">
<type class="float"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</property>

<property name="facets" flags="r">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.&#xa; They are determined from the Min_Pres_Value, Max_Pres_Value, and Units&#xa; properties (if present).
</description>
<tag name="@see">#getFacets</tag>
<tag name="@see">#setFacets</tag>
</property>

<property name="statusFlags" flags="r">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
Slot for the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#getStatusFlags</tag>
<tag name="@see">#setStatusFlags</tag>
</property>

<property name="eventState" flags="r">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getEventState</tag>
<tag name="@see">#setEventState</tag>
</property>

<property name="outOfService" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#getOutOfService</tag>
<tag name="@see">#setOutOfService</tag>
</property>

<property name="outputUnits" flags="">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;outputUnits&lt;/code&gt; property.
</description>
<tag name="@see">#getOutputUnits</tag>
<tag name="@see">#setOutputUnits</tag>
</property>

<property name="manipulatedVariableReference" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference"/>
<description>
Slot for the &lt;code&gt;manipulatedVariableReference&lt;/code&gt; property.
</description>
<tag name="@see">#getManipulatedVariableReference</tag>
<tag name="@see">#setManipulatedVariableReference</tag>
</property>

<property name="controlledVariableReference" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference"/>
<description>
Slot for the &lt;code&gt;controlledVariableReference&lt;/code&gt; property.
</description>
<tag name="@see">#getControlledVariableReference</tag>
<tag name="@see">#setControlledVariableReference</tag>
</property>

<property name="controlledVariableValue" flags="">
<type class="float"/>
<description>
Slot for the &lt;code&gt;controlledVariableValue&lt;/code&gt; property.
</description>
<tag name="@see">#getControlledVariableValue</tag>
<tag name="@see">#setControlledVariableValue</tag>
</property>

<property name="controlledVariableUnits" flags="">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;controlledVariableUnits&lt;/code&gt; property.
</description>
<tag name="@see">#getControlledVariableUnits</tag>
<tag name="@see">#setControlledVariableUnits</tag>
</property>

<property name="setpointReference" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetSetpointReference"/>
<description>
Slot for the &lt;code&gt;setpointReference&lt;/code&gt; property.
</description>
<tag name="@see">#getSetpointReference</tag>
<tag name="@see">#setSetpointReference</tag>
</property>

<property name="setpoint" flags="">
<type class="float"/>
<description>
Slot for the &lt;code&gt;setpoint&lt;/code&gt; property.
</description>
<tag name="@see">#getSetpoint</tag>
<tag name="@see">#setSetpoint</tag>
</property>

<property name="action" flags="">
<type class="javax.baja.bacnet.enums.BBacnetAction"/>
<description>
Slot for the &lt;code&gt;action&lt;/code&gt; property.
</description>
<tag name="@see">#getAction</tag>
<tag name="@see">#setAction</tag>
</property>

<property name="priorityForWriting" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;priorityForWriting&lt;/code&gt; property.
</description>
<tag name="@see">#getPriorityForWriting</tag>
<tag name="@see">#setPriorityForWriting</tag>
</property>

<!-- javax.baja.bacnet.config.BBacnetLoop() -->
<constructor name="BBacnetLoop" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetLoop.getPresentValue() -->
<method name="getPresentValue"  public="true">
<description>
Get the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#presentValue</tag>
<return>
<type class="float"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.setPresentValue(float) -->
<method name="setPresentValue"  public="true">
<description>
Set the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#presentValue</tag>
<parameter name="v">
<type class="float"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.getFacets() -->
<method name="getFacets"  public="true">
<description>
Get the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.&#xa; They are determined from the Min_Pres_Value, Max_Pres_Value, and Units&#xa; properties (if present).
</description>
<tag name="@see">#facets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.setFacets(javax.baja.sys.BFacets) -->
<method name="setFacets"  public="true">
<description>
Set the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.&#xa; They are determined from the Min_Pres_Value, Max_Pres_Value, and Units&#xa; properties (if present).
</description>
<tag name="@see">#facets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.getStatusFlags() -->
<method name="getStatusFlags"  public="true">
<description>
Get the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#statusFlags</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.setStatusFlags(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="setStatusFlags"  public="true">
<description>
Set the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#statusFlags</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.getEventState() -->
<method name="getEventState"  public="true">
<description>
Get the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#eventState</tag>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.setEventState(javax.baja.sys.BEnum) -->
<method name="setEventState"  public="true">
<description>
Set the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#eventState</tag>
<parameter name="v">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.getOutOfService() -->
<method name="getOutOfService"  public="true">
<description>
Get the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#outOfService</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.setOutOfService(boolean) -->
<method name="setOutOfService"  public="true">
<description>
Set the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#outOfService</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.getOutputUnits() -->
<method name="getOutputUnits"  public="true">
<description>
Get the &lt;code&gt;outputUnits&lt;/code&gt; property.
</description>
<tag name="@see">#outputUnits</tag>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.setOutputUnits(javax.baja.sys.BEnum) -->
<method name="setOutputUnits"  public="true">
<description>
Set the &lt;code&gt;outputUnits&lt;/code&gt; property.
</description>
<tag name="@see">#outputUnits</tag>
<parameter name="v">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.getManipulatedVariableReference() -->
<method name="getManipulatedVariableReference"  public="true">
<description>
Get the &lt;code&gt;manipulatedVariableReference&lt;/code&gt; property.
</description>
<tag name="@see">#manipulatedVariableReference</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.setManipulatedVariableReference(javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference) -->
<method name="setManipulatedVariableReference"  public="true">
<description>
Set the &lt;code&gt;manipulatedVariableReference&lt;/code&gt; property.
</description>
<tag name="@see">#manipulatedVariableReference</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.getControlledVariableReference() -->
<method name="getControlledVariableReference"  public="true">
<description>
Get the &lt;code&gt;controlledVariableReference&lt;/code&gt; property.
</description>
<tag name="@see">#controlledVariableReference</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.setControlledVariableReference(javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference) -->
<method name="setControlledVariableReference"  public="true">
<description>
Set the &lt;code&gt;controlledVariableReference&lt;/code&gt; property.
</description>
<tag name="@see">#controlledVariableReference</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.getControlledVariableValue() -->
<method name="getControlledVariableValue"  public="true">
<description>
Get the &lt;code&gt;controlledVariableValue&lt;/code&gt; property.
</description>
<tag name="@see">#controlledVariableValue</tag>
<return>
<type class="float"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.setControlledVariableValue(float) -->
<method name="setControlledVariableValue"  public="true">
<description>
Set the &lt;code&gt;controlledVariableValue&lt;/code&gt; property.
</description>
<tag name="@see">#controlledVariableValue</tag>
<parameter name="v">
<type class="float"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.getControlledVariableUnits() -->
<method name="getControlledVariableUnits"  public="true">
<description>
Get the &lt;code&gt;controlledVariableUnits&lt;/code&gt; property.
</description>
<tag name="@see">#controlledVariableUnits</tag>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.setControlledVariableUnits(javax.baja.sys.BEnum) -->
<method name="setControlledVariableUnits"  public="true">
<description>
Set the &lt;code&gt;controlledVariableUnits&lt;/code&gt; property.
</description>
<tag name="@see">#controlledVariableUnits</tag>
<parameter name="v">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.getSetpointReference() -->
<method name="getSetpointReference"  public="true">
<description>
Get the &lt;code&gt;setpointReference&lt;/code&gt; property.
</description>
<tag name="@see">#setpointReference</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetSetpointReference"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.setSetpointReference(javax.baja.bacnet.datatypes.BBacnetSetpointReference) -->
<method name="setSetpointReference"  public="true">
<description>
Set the &lt;code&gt;setpointReference&lt;/code&gt; property.
</description>
<tag name="@see">#setpointReference</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetSetpointReference"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.getSetpoint() -->
<method name="getSetpoint"  public="true">
<description>
Get the &lt;code&gt;setpoint&lt;/code&gt; property.
</description>
<tag name="@see">#setpoint</tag>
<return>
<type class="float"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.setSetpoint(float) -->
<method name="setSetpoint"  public="true">
<description>
Set the &lt;code&gt;setpoint&lt;/code&gt; property.
</description>
<tag name="@see">#setpoint</tag>
<parameter name="v">
<type class="float"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.getAction() -->
<method name="getAction"  public="true">
<description>
Get the &lt;code&gt;action&lt;/code&gt; property.
</description>
<tag name="@see">#action</tag>
<return>
<type class="javax.baja.bacnet.enums.BBacnetAction"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.setAction(javax.baja.bacnet.enums.BBacnetAction) -->
<method name="setAction"  public="true">
<description>
Set the &lt;code&gt;action&lt;/code&gt; property.
</description>
<tag name="@see">#action</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.BBacnetAction"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.getPriorityForWriting() -->
<method name="getPriorityForWriting"  public="true">
<description>
Get the &lt;code&gt;priorityForWriting&lt;/code&gt; property.
</description>
<tag name="@see">#priorityForWriting</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.setPriorityForWriting(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setPriorityForWriting"  public="true">
<description>
Set the &lt;code&gt;priorityForWriting&lt;/code&gt; property.
</description>
<tag name="@see">#priorityForWriting</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.started() -->
<method name="started"  public="true">
<description>
Started.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.stopped() -->
<method name="stopped"  public="true">
<description>
Stopped.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<description>
Property changed.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.added(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="added"  public="true">
<description>
Property added.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.removed(javax.baja.sys.Property, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="removed"  public="true">
<description>
Property removed.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="oldValue">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.getSlotFacets(javax.baja.sys.Slot) -->
<method name="getSlotFacets"  public="true">
<description>
Get the slot facets.
</description>
<parameter name="s">
<type class="javax.baja.sys.Slot"/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.setOutputFacets() -->
<method name="setOutputFacets"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.getPresentValueProperty() -->
<method name="getPresentValueProperty"  public="true">
<description>
Subclasses that have a present value property should&#xa; override this method and return this property.  The&#xa; default returns null.
</description>
<return>
<type class="javax.baja.sys.Property"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetLoop.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetLoop.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetLoop.presentValue -->
<field name="presentValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetLoop.facets -->
<field name="facets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.&#xa; They are determined from the Min_Pres_Value, Max_Pres_Value, and Units&#xa; properties (if present).
</description>
<tag name="@see">#getFacets</tag>
<tag name="@see">#setFacets</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetLoop.statusFlags -->
<field name="statusFlags"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#getStatusFlags</tag>
<tag name="@see">#setStatusFlags</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetLoop.eventState -->
<field name="eventState"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getEventState</tag>
<tag name="@see">#setEventState</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetLoop.outOfService -->
<field name="outOfService"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#getOutOfService</tag>
<tag name="@see">#setOutOfService</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetLoop.outputUnits -->
<field name="outputUnits"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;outputUnits&lt;/code&gt; property.
</description>
<tag name="@see">#getOutputUnits</tag>
<tag name="@see">#setOutputUnits</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetLoop.manipulatedVariableReference -->
<field name="manipulatedVariableReference"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;manipulatedVariableReference&lt;/code&gt; property.
</description>
<tag name="@see">#getManipulatedVariableReference</tag>
<tag name="@see">#setManipulatedVariableReference</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetLoop.controlledVariableReference -->
<field name="controlledVariableReference"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;controlledVariableReference&lt;/code&gt; property.
</description>
<tag name="@see">#getControlledVariableReference</tag>
<tag name="@see">#setControlledVariableReference</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetLoop.controlledVariableValue -->
<field name="controlledVariableValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;controlledVariableValue&lt;/code&gt; property.
</description>
<tag name="@see">#getControlledVariableValue</tag>
<tag name="@see">#setControlledVariableValue</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetLoop.controlledVariableUnits -->
<field name="controlledVariableUnits"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;controlledVariableUnits&lt;/code&gt; property.
</description>
<tag name="@see">#getControlledVariableUnits</tag>
<tag name="@see">#setControlledVariableUnits</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetLoop.setpointReference -->
<field name="setpointReference"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;setpointReference&lt;/code&gt; property.
</description>
<tag name="@see">#getSetpointReference</tag>
<tag name="@see">#setSetpointReference</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetLoop.setpoint -->
<field name="setpoint"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;setpoint&lt;/code&gt; property.
</description>
<tag name="@see">#getSetpoint</tag>
<tag name="@see">#setSetpoint</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetLoop.action -->
<field name="action"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;action&lt;/code&gt; property.
</description>
<tag name="@see">#getAction</tag>
<tag name="@see">#setAction</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetLoop.priorityForWriting -->
<field name="priorityForWriting"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;priorityForWriting&lt;/code&gt; property.
</description>
<tag name="@see">#getPriorityForWriting</tag>
<tag name="@see">#setPriorityForWriting</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetLoop.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
