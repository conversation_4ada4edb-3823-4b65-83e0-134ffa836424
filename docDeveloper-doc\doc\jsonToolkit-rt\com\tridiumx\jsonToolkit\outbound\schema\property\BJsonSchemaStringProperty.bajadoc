<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaStringProperty" name="BJsonSchemaStringProperty" packageName="com.tridiumx.jsonToolkit.outbound.schema.property" public="true">
<description>
A fixed string key / value json pair. The name and value are governed by user input.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<parameterizedType class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaProperty">
<args>
<type class="java.lang.String"/>
</args>
</parameterizedType>
</extends>
<property name="stringValue" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;stringValue&lt;/code&gt; property.
</description>
<tag name="@see">#getStringValue</tag>
<tag name="@see">#setStringValue</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaStringProperty() -->
<constructor name="BJsonSchemaStringProperty" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaStringProperty.getStringValue() -->
<method name="getStringValue"  public="true">
<description>
Get the &lt;code&gt;stringValue&lt;/code&gt; property.
</description>
<tag name="@see">#stringValue</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaStringProperty.setStringValue(java.lang.String) -->
<method name="setStringValue"  public="true">
<description>
Set the &lt;code&gt;stringValue&lt;/code&gt; property.
</description>
<tag name="@see">#stringValue</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaStringProperty.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaStringProperty.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description/>
<parameter name="value">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaStringProperty"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaStringProperty.getJsonValue() -->
<method name="getJsonValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaStringProperty.stringValue -->
<field name="stringValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;stringValue&lt;/code&gt; property.
</description>
<tag name="@see">#getStringValue</tag>
<tag name="@see">#setStringValue</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaStringProperty.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
