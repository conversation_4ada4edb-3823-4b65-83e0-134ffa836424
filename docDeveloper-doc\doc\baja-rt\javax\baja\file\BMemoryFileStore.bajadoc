<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BMemoryFileStore" name="BMemoryFileStore" packageName="javax.baja.file" public="true">
<description>
BMemoryStore is a BIFileStore implementation which uses&#xa; a memory buffer for reading and writing.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">17 Mar 03</tag>
<tag name="@version">$Revision: 2$ $Date: 3/28/05 9:22:56 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.file.BAbstractFileStore"/>
</extends>
<!-- javax.baja.file.BMemoryFileStore(javax.baja.file.BFileSpace, javax.baja.file.FilePath) -->
<constructor name="BMemoryFileStore" public="true">
<parameter name="space">
<type class="javax.baja.file.BFileSpace"/>
</parameter>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.file.BMemoryFileStore.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.file.BMemoryFileStore.setFile(javax.baja.file.BIFile) -->
<method name="setFile"  public="true">
<description>
Store file object associated with this memory store.
</description>
<parameter name="f">
<type class="javax.baja.file.BIFile"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.file.BMemoryFileStore.getFile() -->
<method name="getFile"  public="true">
<description>
Get file object associated with this memory store.
</description>
<return>
<type class="javax.baja.file.BIFile"/>
</return>
</method>

<!-- javax.baja.file.BMemoryFileStore.isDirectory() -->
<method name="isDirectory"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return false.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BMemoryFileStore.isReadonly() -->
<method name="isReadonly"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return false.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BMemoryFileStore.getSize() -->
<method name="getSize"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return size of memory buffer.
</description>
<return>
<type class="long"/>
</return>
</method>

<!-- javax.baja.file.BMemoryFileStore.getLastModified() -->
<method name="getLastModified"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return BAbsTime for last time the buffer was written to.
</description>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.file.BMemoryFileStore.read() -->
<method name="read"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return buffer contents.
</description>
<return>
<type class="byte" dimension="1"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BMemoryFileStore.getInputStream() -->
<method name="getInputStream"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return InputStream for buffer.
</description>
<return>
<type class="java.io.InputStream"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BMemoryFileStore.getOutputStream() -->
<method name="getOutputStream"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get output stream to write new buffer.
</description>
<return>
<type class="java.io.OutputStream"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BMemoryFileStore.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description>
Return true if object same instance.
</description>
<parameter name="object">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BMemoryFileStore.hashCode() -->
<method name="hashCode"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.file.BMemoryFileStore.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
