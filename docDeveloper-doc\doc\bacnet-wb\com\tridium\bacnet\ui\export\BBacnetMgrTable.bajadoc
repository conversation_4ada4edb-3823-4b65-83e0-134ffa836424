<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="wb" qualifiedName="com.tridium.bacnet.ui.export.BBacnetMgrTable" name="BBacnetMgrTable" packageName="com.tridium.bacnet.ui.export" public="true">
<description>
BBacnetMgrTable is a custom implementation of the BMgrTable&#xa; infrastructure made in onder to listen for delete events on the&#xa; table.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">01 Jan 21</tag>
<tag name="@since">Niagara 4.11</tag>
<extends>
<type class="javax.baja.workbench.mgr.BMgrTable"/>
</extends>
</class>
</bajadoc>
