<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="abstractMqttDriver" runtimeProfile="rt" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>This is a driver for the MQTT client.</description>
<package name="javax.baja.mqttClientDriver.authenticator"/>
<class packageName="javax.baja.mqttClientDriver.authenticator" name="BAbstractMqttAuthenticator"><description>BAbstractMqttAuthenticator, the base of the authentication feature in any device.</description></class>
</module>
</bajadoc>
