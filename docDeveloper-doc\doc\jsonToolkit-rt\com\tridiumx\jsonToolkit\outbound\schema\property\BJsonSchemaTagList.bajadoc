<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagList" name="BJsonSchemaTagList" packageName="com.tridiumx.jsonToolkit.outbound.schema.property" public="true">
<description>
A list of name/value properties based upon selected tags found upon a binding target.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaPropertyList"/>
</extends>
<property name="dictionaryNamespaceFilter" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;dictionaryNamespaceFilter&lt;/code&gt; property.
</description>
<tag name="@see">#getDictionaryNamespaceFilter</tag>
<tag name="@see">#setDictionaryNamespaceFilter</tag>
</property>

<property name="tagIdListFilter" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;tagIdListFilter&lt;/code&gt; property.
</description>
<tag name="@see">#getTagIdListFilter</tag>
<tag name="@see">#setTagIdListFilter</tag>
</property>

<property name="includeNameSpace" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;includeNameSpace&lt;/code&gt; property.
</description>
<tag name="@see">#getIncludeNameSpace</tag>
<tag name="@see">#setIncludeNameSpace</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagList() -->
<constructor name="BJsonSchemaTagList" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagList.getDictionaryNamespaceFilter() -->
<method name="getDictionaryNamespaceFilter"  public="true">
<description>
Get the &lt;code&gt;dictionaryNamespaceFilter&lt;/code&gt; property.
</description>
<tag name="@see">#dictionaryNamespaceFilter</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagList.setDictionaryNamespaceFilter(java.lang.String) -->
<method name="setDictionaryNamespaceFilter"  public="true">
<description>
Set the &lt;code&gt;dictionaryNamespaceFilter&lt;/code&gt; property.
</description>
<tag name="@see">#dictionaryNamespaceFilter</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagList.getTagIdListFilter() -->
<method name="getTagIdListFilter"  public="true">
<description>
Get the &lt;code&gt;tagIdListFilter&lt;/code&gt; property.
</description>
<tag name="@see">#tagIdListFilter</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagList.setTagIdListFilter(java.lang.String) -->
<method name="setTagIdListFilter"  public="true">
<description>
Set the &lt;code&gt;tagIdListFilter&lt;/code&gt; property.
</description>
<tag name="@see">#tagIdListFilter</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagList.getIncludeNameSpace() -->
<method name="getIncludeNameSpace"  public="true">
<description>
Get the &lt;code&gt;includeNameSpace&lt;/code&gt; property.
</description>
<tag name="@see">#includeNameSpace</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagList.setIncludeNameSpace(boolean) -->
<method name="setIncludeNameSpace"  public="true">
<description>
Set the &lt;code&gt;includeNameSpace&lt;/code&gt; property.
</description>
<tag name="@see">#includeNameSpace</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagList.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagList.make(javax.baja.naming.BOrd, java.lang.String, java.lang.String, boolean) -->
<method name="make"  public="true" static="true">
<description/>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<parameter name="nameSpaceFilter">
<type class="java.lang.String"/>
</parameter>
<parameter name="tagIdList">
<type class="java.lang.String"/>
</parameter>
<parameter name="includeNamespace">
<type class="boolean"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagList"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagList.process(com.tridium.json.JSONWriter, boolean) -->
<method name="process"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="jsonWriter">
<type class="com.tridium.json.JSONWriter"/>
</parameter>
<parameter name="jsonKeysValid">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagList.dictionaryNamespaceFilter -->
<field name="dictionaryNamespaceFilter"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;dictionaryNamespaceFilter&lt;/code&gt; property.
</description>
<tag name="@see">#getDictionaryNamespaceFilter</tag>
<tag name="@see">#setDictionaryNamespaceFilter</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagList.tagIdListFilter -->
<field name="tagIdListFilter"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;tagIdListFilter&lt;/code&gt; property.
</description>
<tag name="@see">#getTagIdListFilter</tag>
<tag name="@see">#setTagIdListFilter</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagList.includeNameSpace -->
<field name="includeNameSpace"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;includeNameSpace&lt;/code&gt; property.
</description>
<tag name="@see">#getIncludeNameSpace</tag>
<tag name="@see">#setIncludeNameSpace</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagList.jsonName -->
<field name="jsonName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;jsonName&lt;/code&gt; property.
</description>
<tag name="@see">#getJsonName</tag>
<tag name="@see">#setJsonName</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagList.jsonNameSource -->
<field name="jsonNameSource"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;jsonNameSource&lt;/code&gt; property.
</description>
<tag name="@see">#getJsonNameSource</tag>
<tag name="@see">#setJsonNameSource</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaTagList.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
