<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.collection.ValueCollection" name="ValueCollection" packageName="javax.baja.collection" public="true">
<description>
ValueCollection represents the property values of a BComplex instance as&#xa; a Collection.  The BComplex instance is not modifiable through this interface.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">3/1/14</tag>
<tag name="@since">Niagara 4.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<parameterizedType class="java.util.Collection">
<args>
<type class="javax.baja.sys.BValue"/>
</args>
</parameterizedType>
</implements>
<!-- javax.baja.collection.ValueCollection(javax.baja.sys.BComplex) -->
<constructor name="ValueCollection" public="true">
<parameter name="obj">
<type class="javax.baja.sys.BComplex"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.collection.ValueCollection.contains(java.lang.Object) -->
<method name="contains"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="o">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.ValueCollection.containsAll(java.util.Collection&lt;?&gt;) -->
<method name="containsAll"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="c">
<parameterizedType class="java.util.Collection">
<args>
<wildcardType class="?">
</wildcardType>
</args>
</parameterizedType>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.ValueCollection.equals(java.lang.Object) -->
<method name="equals"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="o">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.ValueCollection.hashCode() -->
<method name="hashCode"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.collection.ValueCollection.isEmpty() -->
<method name="isEmpty"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.ValueCollection.size() -->
<method name="size"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.collection.ValueCollection.toArray() -->
<method name="toArray"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.Object" dimension="1"/>
</return>
</method>

<!-- javax.baja.collection.ValueCollection.&lt;T&gt;toArray(T[]) -->
<method name="toArray"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<typeParameters>
<typeVariable class="T">
</typeVariable>
</typeParameters>
<description/>
<parameter name="a">
<type class="java.lang.T" dimension="1"/>
</parameter>
<return>
<type class="java.lang.T" dimension="1"/>
</return>
</method>

<!-- javax.baja.collection.ValueCollection.iterator() -->
<method name="iterator"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<parameterizedType class="java.util.Iterator">
<args>
<type class="javax.baja.sys.BValue"/>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.collection.ValueCollection.add(javax.baja.sys.BValue) -->
<method name="add"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="p">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.ValueCollection.addAll(java.util.Collection&lt;? extends javax.baja.sys.BValue&gt;) -->
<method name="addAll"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="c">
<parameterizedType class="java.util.Collection">
<args>
<wildcardType class="?">
<bounds kind="extends">
<type class="javax.baja.sys.BValue"/>
</bounds>
</wildcardType>
</args>
</parameterizedType>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.ValueCollection.clear() -->
<method name="clear"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.collection.ValueCollection.remove(java.lang.Object) -->
<method name="remove"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="o">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.ValueCollection.removeAll(java.util.Collection&lt;?&gt;) -->
<method name="removeAll"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="c">
<parameterizedType class="java.util.Collection">
<args>
<wildcardType class="?">
</wildcardType>
</args>
</parameterizedType>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.ValueCollection.retainAll(java.util.Collection&lt;?&gt;) -->
<method name="retainAll"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="c">
<parameterizedType class="java.util.Collection">
<args>
<wildcardType class="?">
</wildcardType>
</args>
</parameterizedType>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

</class>
</bajadoc>
