<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="javax.baja.bacnetAws.config.BBacnetAwsConfigFolder" name="BBacnetAwsConfigFolder" packageName="javax.baja.bacnetAws.config" public="true">
<description>
BBacnetAwsConfigFolder is the standard container to use&#xa; under BBacnetAwsConfigDeviceExt to organize BBacnetObjects.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">07 June 2010</tag>
<tag name="@creation">Jun 7, 2010</tag>
<extends>
<type class="javax.baja.bacnet.config.BBacnetConfigFolder"/>
</extends>
<!-- javax.baja.bacnetAws.config.BBacnetAwsConfigFolder() -->
<constructor name="BBacnetAwsConfigFolder" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnetAws.config.BBacnetAwsConfigFolder.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAwsConfigFolder.getAgents(javax.baja.sys.Context) -->
<method name="getAgents"  public="true">
<description>
Get the agent list.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.agent.AgentList"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAwsConfigFolder.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
