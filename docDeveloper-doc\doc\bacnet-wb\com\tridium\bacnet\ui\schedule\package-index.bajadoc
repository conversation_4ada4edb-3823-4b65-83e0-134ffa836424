<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="wb" name="com.tridium.bacnet.ui.schedule">
<description/>
<class packageName="com.tridium.bacnet.ui.schedule" name="BBacnetScheduleExportManager"><description>BBacnetScheduleExportManager is used for configuring schedules to be imported&#xa; from a remote BACnet device into the Niagara station.</description></class>
<class packageName="com.tridium.bacnet.ui.schedule" name="BBacnetScheduleImportManager"><description>BBacnetScheduleImportManager is used for configuring schedules to be imported&#xa; from a remote BACnet device into the Niagara station.</description></class>
<class packageName="com.tridium.bacnet.ui.schedule" name="BChangeTypeDialog"><description>BChangeTypePane</description></class>
</package>
</bajadoc>
