<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.datatypes.BRemoveArrayElementAction" name="BRemoveArrayElementAction" packageName="com.tridium.bacnet.datatypes" public="true">
<description>
BRemoveArrayElementAction is used to create the dynamic action&#xa; for removing elements from a BBacnetArray.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">13 Jul 2009</tag>
<tag name="@since">Niagara 3.5</tag>
<extends>
<type class="javax.baja.sys.BAction"/>
</extends>
<property name="parameterTypeSpec" flags="hr">
<type class="javax.baja.util.BTypeSpec"/>
<description>
Slot for the &lt;code&gt;parameterTypeSpec&lt;/code&gt; property.
</description>
<tag name="@see">#getParameterTypeSpec</tag>
<tag name="@see">#setParameterTypeSpec</tag>
</property>

</class>
</bajadoc>
