<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetIntegerValueDescriptor" name="BBacnetIntegerValueDescriptor" packageName="javax.baja.bacnet.export" public="true">
<description>
BBacnetIntegerValueDescriptor exposes a ControlPoint as a Bacnet&#xa; Integer Value Descriptor.
</description>
<tag name="@author"><PERSON> on 15 Apr 15</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.export.BBacnetAnalogValueDescriptor"/>
</extends>
<!-- javax.baja.bacnet.export.BBacnetIntegerValueDescriptor() -->
<constructor name="BBacnetIntegerValueDescriptor" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetIntegerValueDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValueDescriptor.convertToAsn(double) -->
<method name="convertToAsn"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Override hook for IntegerValue&#xa; This is a lossy conversion
</description>
<parameter name="value">
<type class="double"/>
<description>
double value to convert to asn.1
</description>
</parameter>
<return>
<type class="byte" dimension="1"/>
<description>
byte[] containing the required asn.1 formatted numeric value
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValueDescriptor.convertFromAsn(byte[]) -->
<method name="convertFromAsn"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Override hook for IntegerValue&#xa; &lt;p&gt;&#xa; This is a limited conversion to 2^53
</description>
<parameter name="value">
<type class="byte" dimension="1"/>
<description>
asn.1 byte array containing a number
</description>
</parameter>
<return>
<type class="double"/>
<description>
the number decoded from the byte[]
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
<description>
if the array does not contain a properly
</description>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValueDescriptor.addRequiredProps(java.util.Vector) -->
<method name="addRequiredProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValueDescriptor.addOptionalProps(java.util.Vector) -->
<method name="addOptionalProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValueDescriptor.readProperty(int, int) -->
<method name="readProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="pId">
<type class="int"/>
</parameter>
<parameter name="ndx">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValueDescriptor.getDeadBandValue(byte[]) -->
<method name="getDeadBandValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the deadband value as Unsigned integer
</description>
<parameter name="value">
<type class="byte" dimension="1"/>
<description/>
</parameter>
<return>
<type class="double"/>
<description>
deadband value
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValueDescriptor.getDeadBandBytes(double) -->
<method name="getDeadBandBytes"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the deadband value as Asn Byte array
</description>
<parameter name="value">
<type class="double"/>
<description/>
</parameter>
<return>
<type class="byte" dimension="1"/>
<description>
deadband bytes
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValueDescriptor.getCovIncrement(byte[]) -->
<method name="getCovIncrement"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
get the COV increment as Unsigned value
</description>
<parameter name="value">
<type class="byte" dimension="1"/>
<description/>
</parameter>
<return>
<type class="double"/>
<description>
cov increment
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
<description/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetIntegerValueDescriptor.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this point is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetIntegerValueDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
