<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.routing.slot.DynamicSlotUtil" name="DynamicSlotUtil" packageName="com.tridiumx.jsonToolkit.inbound.routing.slot" public="true" interface="true" abstract="true" category="interface">
<description>
Behaviours commons to <PERSON>ler&#x27;s needing readOnly + Summary slots for&#xa; routing/demuxing of payloads
</description>
<tag name="@author"><PERSON></tag>
<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.DynamicSlotUtil.resetDynamicProperties(com.tridiumx.jsonToolkit.inbound.BJsonInbound) -->
<method name="resetDynamicProperties"  public="true" default="true">
<description>
Ensure all dynamic slots are &#x27;transient&#x27; so as not to replay messages over links on startup
</description>
<parameter name="target">
<type class="com.tridiumx.jsonToolkit.inbound.BJsonInbound"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.DynamicSlotUtil.resetDynamicProperty(com.tridiumx.jsonToolkit.inbound.BJsonInbound, javax.baja.sys.Property) -->
<method name="resetDynamicProperty"  public="true" default="true">
<description/>
<parameter name="target">
<type class="com.tridiumx.jsonToolkit.inbound.BJsonInbound"/>
</parameter>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.DynamicSlotUtil.readOnlySummaryOnAdd(com.tridiumx.jsonToolkit.inbound.BJsonInbound, javax.baja.sys.Property) -->
<method name="readOnlySummaryOnAdd"  public="true" default="true">
<description/>
<parameter name="target">
<type class="com.tridiumx.jsonToolkit.inbound.BJsonInbound"/>
</parameter>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
