<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryLeaf" name="BInfinityPointDiscoveryLeaf" packageName="com.tridium.andoverInfinity.discover" public="true">
<description>
BInfinityPointDiscoveryLeaf
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 22, 2007</tag>
<tag name="@version">$Revision$ $May 22, 2007 9:21:40 AM$</tag>
<tag name="@since"/>
<extends>
<type class="com.tridium.ddf.discover.BDdfPointDiscoveryLeaf"/>
</extends>
<!-- com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryLeaf() -->
<constructor name="BInfinityPointDiscoveryLeaf" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryLeaf.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryLeaf.getDiscoveryName() -->
<method name="getDiscoveryName"  public="true">
<description>
When a control point is added to the station from the&#xa; Point Manager, it is given this name by default (possibly&#xa; with a suffix to make it unique).  &#xa; Infinity uses the point name learned from the panel as the default name
</description>
<tag name="@see">com.tridium.ddf.discover.BDdfDiscoveryLeaf#getDiscoveryName()</tag>
<return>
<type class="java.lang.String"/>
<description>
the default name
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryLeaf.getValidDatabaseTypes() -->
<method name="getValidDatabaseTypes"  public="true">
<description>
Return an array of TypeInfo objects corresponding to all valid Niagara&#xa; Ax types for this discovery object. This is important when the end-user&#xa; clicks &#x27;Add&#x27; from the user interface for the manager.&#xa; &#xa; For this discovery object, a list of the types &#xa; which may be used to model it as a BComponent in the &#xa; station database is returned.&#xa; &#xa; The type at index 0 in the array is the type which&#xa; provides the best mapping.&#xa; &#xa; For Infinity, the following is used for mapping points to types:&#xa; 1) System Variables - created based on point name&#xa;    Point Name                    Maps to control point types (first in list is default)&#xa;    ----------------              ------------------------------------------------------&#xa;    Date                          BStringPoint&#xa;    Poweruptime                   BStringPoint&#xa;    Timeofday                     BStringPoint&#xa;    Version                       BStringPoint&#xa;    Month                         BStringPoint,BEnumPoint,BBooleanPoint&#xa;    Weekday                       BStringPoint,BEnumPoint,BBooleanPoint&#xa;    Alarms                        BNumericPoint,BStringPoint&#xa;    Dayofmonth                    BNumericPoint,BStringPoint&#xa;    Dayofyear                     BNumericPoint,BStringPoint&#xa;    Errors                        BNumericPoint,BStringPoint&#xa;    Freemem&#x22;                      BNumericPoint,BStringPoint&#xa;    Hour                          BNumericPoint,BStringPoint&#xa;    Hourofday                     BNumericPoint,BStringPoint&#xa;    Minute                        BNumericPoint,BStringPoint&#xa;    Scan                          BNumericPoint,BStringPoint&#xa;    Second                        BNumericPoint,BStringPoint&#xa;    Year                          BNumericPoint,BStringPoint&#xa;    Powerfail                     BBooleanPoint&#xa;    &#xa; 2) Assigned Points - created based on point value&#xa;    Point Value                   Maps to control point types (first in list is default)&#xa;    ----------------              ------------------------------------------------------&#xa;    On,Off,1,0                    BBooleanPoint,BBooleanWritable,&#xa;                                  BNumericPoint,BNumericWritable,&#xa;                                  BEnumPoint,BEnumWritable,&#xa;                                  BStringPoint,BStringWritable&#xa;                                  &#xa;    -On                           BEnumPoint,BEnumWritable,&#xa;                                  BNumericPoint,BNumericWritable,&#xa;                                  BStringPoint,BStringWritable&#xa;                                  &#xa;    any date or time point        BStringPoint&#xa;    &#xa;    everything else               BNumericPoint,BNumericWritable,&#xa;                                  BBooleanPoint,BBooleanWritable,&#xa;                                  BEnumPoint,BEnumWritable,&#xa;                                  BStringPoint,BStringWritable
</description>
<tag name="@see">com.tridium.ddf.discover.BDdfDiscoveryLeaf#getValidDatabaseTypes()</tag>
<return>
<type class="javax.baja.registry.TypeInfo" dimension="1"/>
<description>
a TypeInfo[] of valid types
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryLeaf.readParameters -->
<field name="readParameters"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;readParameters&lt;/code&gt; property.
</description>
<tag name="@see">#getReadParameters</tag>
<tag name="@see">#setReadParameters</tag>
</field>

<!-- com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryLeaf.writeParameters -->
<field name="writeParameters"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;writeParameters&lt;/code&gt; property.
</description>
<tag name="@see">#getWriteParameters</tag>
<tag name="@see">#setWriteParameters</tag>
</field>

<!-- com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryLeaf.pointId -->
<field name="pointId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;pointId&lt;/code&gt; property.
</description>
<tag name="@see">#getPointId</tag>
<tag name="@see">#setPointId</tag>
</field>

<!-- com.tridium.andoverInfinity.discover.BInfinityPointDiscoveryLeaf.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
