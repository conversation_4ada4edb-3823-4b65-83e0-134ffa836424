<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarmOrion" runtimeProfile="rt" qualifiedName="javax.baja.alarmOrion.BOrionAlarmClass" name="BOrionAlarmClass" packageName="javax.baja.alarmOrion" public="true">
<description>
The representation of an alarm class within the orion database.
</description>
<tag name="@author"><PERSON> on March 18, 2009</tag>
<extends>
<type class="com.tridium.orion.BOrionObject"/>
</extends>
<annotation><type class="com.tridium.orion.annotations.NiagaraOrionType"/>
</annotation>
<property name="id" flags="rs">
<type class="int"/>
<description>
Slot for the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#getId</tag>
<tag name="@see">#setId</tag>
</property>

<property name="alarmClass" flags="s">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;alarmClass&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmClass</tag>
<tag name="@see">#setAlarmClass</tag>
</property>

<property name="unackedAlarmCount" flags="s">
<type class="int"/>
<description>
Slot for the &lt;code&gt;unackedAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#getUnackedAlarmCount</tag>
<tag name="@see">#setUnackedAlarmCount</tag>
</property>

<property name="openAlarmCount" flags="s">
<type class="int"/>
<description>
Slot for the &lt;code&gt;openAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#getOpenAlarmCount</tag>
<tag name="@see">#setOpenAlarmCount</tag>
</property>

<property name="inAlarmCount" flags="s">
<type class="int"/>
<description>
Slot for the &lt;code&gt;inAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#getInAlarmCount</tag>
<tag name="@see">#setInAlarmCount</tag>
</property>

<property name="totalAlarmCount" flags="s">
<type class="int"/>
<description>
Slot for the &lt;code&gt;totalAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#getTotalAlarmCount</tag>
<tag name="@see">#setTotalAlarmCount</tag>
</property>

<property name="timeOfLastAlarm" flags="s">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;timeOfLastAlarm&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeOfLastAlarm</tag>
<tag name="@see">#setTimeOfLastAlarm</tag>
</property>

<!-- javax.baja.alarmOrion.BOrionAlarmClass() -->
<constructor name="BOrionAlarmClass" public="true">
<description/>
</constructor>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.getId() -->
<method name="getId"  public="true">
<description>
Get the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#id</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.setId(int) -->
<method name="setId"  public="true">
<description>
Set the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#id</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.getAlarmClass() -->
<method name="getAlarmClass"  public="true">
<description>
Get the &lt;code&gt;alarmClass&lt;/code&gt; property.
</description>
<tag name="@see">#alarmClass</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.setAlarmClass(java.lang.String) -->
<method name="setAlarmClass"  public="true">
<description>
Set the &lt;code&gt;alarmClass&lt;/code&gt; property.
</description>
<tag name="@see">#alarmClass</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.getUnackedAlarmCount() -->
<method name="getUnackedAlarmCount"  public="true">
<description>
Get the &lt;code&gt;unackedAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#unackedAlarmCount</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.setUnackedAlarmCount(int) -->
<method name="setUnackedAlarmCount"  public="true">
<description>
Set the &lt;code&gt;unackedAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#unackedAlarmCount</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.getOpenAlarmCount() -->
<method name="getOpenAlarmCount"  public="true">
<description>
Get the &lt;code&gt;openAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#openAlarmCount</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.setOpenAlarmCount(int) -->
<method name="setOpenAlarmCount"  public="true">
<description>
Set the &lt;code&gt;openAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#openAlarmCount</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.getInAlarmCount() -->
<method name="getInAlarmCount"  public="true">
<description>
Get the &lt;code&gt;inAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#inAlarmCount</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.setInAlarmCount(int) -->
<method name="setInAlarmCount"  public="true">
<description>
Set the &lt;code&gt;inAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#inAlarmCount</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.getTotalAlarmCount() -->
<method name="getTotalAlarmCount"  public="true">
<description>
Get the &lt;code&gt;totalAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#totalAlarmCount</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.setTotalAlarmCount(int) -->
<method name="setTotalAlarmCount"  public="true">
<description>
Set the &lt;code&gt;totalAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#totalAlarmCount</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.getTimeOfLastAlarm() -->
<method name="getTimeOfLastAlarm"  public="true">
<description>
Get the &lt;code&gt;timeOfLastAlarm&lt;/code&gt; property.
</description>
<tag name="@see">#timeOfLastAlarm</tag>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.setTimeOfLastAlarm(javax.baja.sys.BAbsTime) -->
<method name="setTimeOfLastAlarm"  public="true">
<description>
Set the &lt;code&gt;timeOfLastAlarm&lt;/code&gt; property.
</description>
<tag name="@see">#timeOfLastAlarm</tag>
<parameter name="v">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.get(java.lang.String, com.tridium.orion.OrionSession) -->
<method name="get"  public="true" static="true">
<description>
Get the alarm class of the specified name.  If one does not exist, create it.&#xa; The results are cached to speed up multiple requests for the same object.
</description>
<parameter name="alarmClassName">
<type class="java.lang.String"/>
</parameter>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="javax.baja.alarmOrion.BOrionAlarmClass"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.afterDelete(com.tridium.orion.OrionSession) -->
<method name="afterDelete"  public="true">
<description/>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.afterInsert(com.tridium.orion.OrionSession) -->
<method name="afterInsert"  public="true">
<description/>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.afterUpdate(com.tridium.orion.OrionSession) -->
<method name="afterUpdate"  public="true">
<description/>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.updateAlarmClass() -->
<method name="updateAlarmClass"  public="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Update the alarm class component with the alarm statistics stored&#xa; within the database.&#xa; &lt;p&gt;&#xa; This method was deprecated in Niagara 4.13 and &lt;code&gt;<see ref="javax.baja.alarmOrion.BOrionAlarmClass#updateAlarmClass(com.tridium.orion.OrionSession)">#updateAlarmClass(OrionSession)</see>&lt;/code&gt; should be used instead.
</description>
<tag name="@deprecated">in Niagara 4.13.  Will be removed in Niagara 5.</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.updateAlarmClass(com.tridium.orion.OrionSession) -->
<method name="updateAlarmClass"  public="true">
<description>
Update the alarm class component with the alarm statistics stored&#xa; within the database.
</description>
<tag name="@since">Niagara 4.13</tag>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.id -->
<field name="id"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#getId</tag>
<tag name="@see">#setId</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.alarmClass -->
<field name="alarmClass"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmClass&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmClass</tag>
<tag name="@see">#setAlarmClass</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.unackedAlarmCount -->
<field name="unackedAlarmCount"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;unackedAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#getUnackedAlarmCount</tag>
<tag name="@see">#setUnackedAlarmCount</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.openAlarmCount -->
<field name="openAlarmCount"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;openAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#getOpenAlarmCount</tag>
<tag name="@see">#setOpenAlarmCount</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.inAlarmCount -->
<field name="inAlarmCount"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;inAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#getInAlarmCount</tag>
<tag name="@see">#setInAlarmCount</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.totalAlarmCount -->
<field name="totalAlarmCount"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;totalAlarmCount&lt;/code&gt; property.
</description>
<tag name="@see">#getTotalAlarmCount</tag>
<tag name="@see">#setTotalAlarmCount</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.timeOfLastAlarm -->
<field name="timeOfLastAlarm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;timeOfLastAlarm&lt;/code&gt; property.
</description>
<tag name="@see">#getTimeOfLastAlarm</tag>
<tag name="@see">#setTimeOfLastAlarm</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmClass.ORION_TYPE -->
<field name="ORION_TYPE"  public="true" static="true" final="true">
<type class="com.tridium.orion.OrionType"/>
<description/>
</field>

</class>
</bajadoc>
