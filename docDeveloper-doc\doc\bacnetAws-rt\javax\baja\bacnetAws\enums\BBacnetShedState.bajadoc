<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="javax.baja.bacnetAws.enums.BBacnetShedState" name="BBacnetShedState" packageName="javax.baja.bacnetAws.enums" public="true" final="true">
<description>
Represents the BBacnetShedState define in the Bacnet spec.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">May 24, 2010</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.6 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;shedInactive&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>0</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;shedRequestPending&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>1</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;shedCompliant&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>2</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;shedNonCompliant&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>3</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnetAws.enums.BBacnetShedState.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnetAws.enums.BBacnetShedState"/>
</return>
</method>

<!-- javax.baja.bacnetAws.enums.BBacnetShedState.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnetAws.enums.BBacnetShedState"/>
</return>
</method>

<!-- javax.baja.bacnetAws.enums.BBacnetShedState.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnetAws.enums.BBacnetShedState.SHED_INACTIVE -->
<field name="SHED_INACTIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for shedInactive.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BBacnetShedState.SHED_REQUEST_PENDING -->
<field name="SHED_REQUEST_PENDING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for shedRequestPending.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BBacnetShedState.SHED_COMPLIANT -->
<field name="SHED_COMPLIANT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for shedCompliant.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BBacnetShedState.SHED_NON_COMPLIANT -->
<field name="SHED_NON_COMPLIANT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for shedNonCompliant.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BBacnetShedState.shedInactive -->
<field name="shedInactive"  public="true" static="true" final="true">
<type class="javax.baja.bacnetAws.enums.BBacnetShedState"/>
<description>
BBacnetShedState constant for shedInactive.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BBacnetShedState.shedRequestPending -->
<field name="shedRequestPending"  public="true" static="true" final="true">
<type class="javax.baja.bacnetAws.enums.BBacnetShedState"/>
<description>
BBacnetShedState constant for shedRequestPending.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BBacnetShedState.shedCompliant -->
<field name="shedCompliant"  public="true" static="true" final="true">
<type class="javax.baja.bacnetAws.enums.BBacnetShedState"/>
<description>
BBacnetShedState constant for shedCompliant.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BBacnetShedState.shedNonCompliant -->
<field name="shedNonCompliant"  public="true" static="true" final="true">
<type class="javax.baja.bacnetAws.enums.BBacnetShedState"/>
<description>
BBacnetShedState constant for shedNonCompliant.
</description>
</field>

<!-- javax.baja.bacnetAws.enums.BBacnetShedState.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnetAws.enums.BBacnetShedState"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.enums.BBacnetShedState.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
