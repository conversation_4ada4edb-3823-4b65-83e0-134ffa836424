<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaTuningPolicy" name="BJsonSchemaTuningPolicy" packageName="com.tridiumx.jsonToolkit.outbound.schema.config" public="true">
<description>
Use conventional Framework construct for min/max write etc.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.driver.point.BTuningPolicy"/>
</extends>
<property name="updateStrategy" flags="">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy"/>
<description>
Slot for the &lt;code&gt;updateStrategy&lt;/code&gt; property.
</description>
<tag name="@see">#getUpdateStrategy</tag>
<tag name="@see">#setUpdateStrategy</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaTuningPolicy() -->
<constructor name="BJsonSchemaTuningPolicy" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaTuningPolicy.getUpdateStrategy() -->
<method name="getUpdateStrategy"  public="true">
<description>
Get the &lt;code&gt;updateStrategy&lt;/code&gt; property.
</description>
<tag name="@see">#updateStrategy</tag>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaTuningPolicy.setUpdateStrategy(com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy) -->
<method name="setUpdateStrategy"  public="true">
<description>
Set the &lt;code&gt;updateStrategy&lt;/code&gt; property.
</description>
<tag name="@see">#updateStrategy</tag>
<parameter name="v">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaUpdateStrategy"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaTuningPolicy.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaTuningPolicy.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaTuningPolicy.getSchema() -->
<method name="getSchema"  public="true">
<description/>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchema"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaTuningPolicy.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaTuningPolicy.updateStrategy -->
<field name="updateStrategy"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;updateStrategy&lt;/code&gt; property.
</description>
<tag name="@see">#getUpdateStrategy</tag>
<tag name="@see">#setUpdateStrategy</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaTuningPolicy.writeOnEnabled -->
<field name="writeOnEnabled"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;writeOnEnabled&lt;/code&gt; property.&#xa; Warning - changing these to default to true causes unit tests to fail.&#xa;&#xa; Also, they override from the parent class but cannot use override=true until the parent is upgraded to use annotations
</description>
<tag name="@see">#getWriteOnEnabled</tag>
<tag name="@see">#setWriteOnEnabled</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaTuningPolicy.writeOnUp -->
<field name="writeOnUp"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;writeOnUp&lt;/code&gt; property.&#xa; Hide props not relevant to JsonToolkit
</description>
<tag name="@see">#getWriteOnUp</tag>
<tag name="@see">#setWriteOnUp</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaTuningPolicy.staleTime -->
<field name="staleTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;staleTime&lt;/code&gt; property.
</description>
<tag name="@see">#getStaleTime</tag>
<tag name="@see">#setStaleTime</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaTuningPolicy.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
