<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="wb" qualifiedName="com.tridium.bacnet.ui.export.BWritableSlotsEditor" name="BWritableSlotsEditor" packageName="com.tridium.bacnet.ui.export" public="true">
<description>
BWritableSlotsEditor.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">16 Mar 2004</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.workbench.fieldeditor.BWbFieldEditor"/>
</extends>
<action name="clearAll" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;clearAll&lt;/code&gt; action.
</description>
<tag name="@see">#clearAll()</tag>
</action>

<action name="selectAll" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;selectAll&lt;/code&gt; action.
</description>
<tag name="@see">#selectAll()</tag>
</action>

</class>
</bajadoc>
