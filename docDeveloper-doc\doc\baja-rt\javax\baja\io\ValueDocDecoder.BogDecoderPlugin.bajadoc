<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.io.ValueDocDecoder$BogDecoderPlugin" name="ValueDocDecoder.BogDecoderPlugin" packageName="javax.baja.io" public="true" static="true" final="true" innerClass="true">
<description>
BOG (Baja Object Graph) XML decoder plug-in&#xa; &lt;p&gt;&#xa; This plug-in is used to decode convention BOG XML
</description>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="javax.baja.io.ValueDocDecoder$IDecoderPlugin"/>
</implements>
<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin(java.io.InputStream) -->
<constructor name="BogDecoderPlugin" public="true">
<parameter name="in">
<type class="java.io.InputStream"/>
</parameter>
<throws>
<type class="java.lang.Exception"/>
</throws>
<description/>
</constructor>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin(javax.baja.naming.BOrd) -->
<constructor name="BogDecoderPlugin" public="true">
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<throws>
<type class="java.lang.Exception"/>
</throws>
<description/>
</constructor>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin(javax.baja.file.BIFile) -->
<constructor name="BogDecoderPlugin" public="true">
<parameter name="file">
<type class="javax.baja.file.BIFile"/>
</parameter>
<throws>
<type class="java.lang.Exception"/>
</throws>
<description/>
</constructor>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin(java.io.File) -->
<constructor name="BogDecoderPlugin" public="true">
<parameter name="file">
<type class="java.io.File"/>
</parameter>
<throws>
<type class="java.lang.Exception"/>
</throws>
<description/>
</constructor>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin(java.io.InputStream, javax.baja.sys.Context) -->
<constructor name="BogDecoderPlugin" public="true">
<parameter name="in">
<type class="java.io.InputStream"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<throws>
<type class="java.lang.Exception"/>
</throws>
<description/>
</constructor>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin(javax.baja.naming.BOrd, javax.baja.sys.Context) -->
<constructor name="BogDecoderPlugin" public="true">
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<throws>
<type class="java.lang.Exception"/>
</throws>
<description/>
</constructor>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin(javax.baja.file.BIFile, javax.baja.sys.Context) -->
<constructor name="BogDecoderPlugin" public="true">
<parameter name="file">
<type class="javax.baja.file.BIFile"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<throws>
<type class="java.lang.Exception"/>
</throws>
<description/>
</constructor>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin(java.io.File, javax.baja.sys.Context) -->
<constructor name="BogDecoderPlugin" public="true">
<parameter name="file">
<type class="java.io.File"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<throws>
<type class="java.lang.Exception"/>
</throws>
<description/>
</constructor>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.readHeader() -->
<method name="readHeader"  public="true">
<description/>
<return>
<type class="javax.baja.util.Version"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.getPassPhraseValidator() -->
<method name="getPassPhraseValidator"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.security.BIPasswordValidator"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.getPasswordObjectEncoder() -->
<method name="getPasswordObjectEncoder"  public="true">
<description/>
<return>
<type class="com.tridium.nre.security.io.BogPasswordObjectEncoder"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.setPasswordObjectEncoder(com.tridium.nre.security.io.BogPasswordObjectEncoder) -->
<method name="setPasswordObjectEncoder"  public="true">
<description/>
<parameter name="value">
<type class="com.tridium.nre.security.io.BogPasswordObjectEncoder"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.setReversibleEncodingKeySource(com.tridium.nre.security.EncryptionKeySource) -->
<method name="setReversibleEncodingKeySource"  public="true">
<description/>
<parameter name="value">
<type class="com.tridium.nre.security.EncryptionKeySource"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.setPassPhrase(java.util.Optional&lt;javax.baja.security.BPassword&gt;) -->
<method name="setPassPhrase"  public="true">
<description/>
<parameter name="value">
<parameterizedType class="java.util.Optional">
<args>
<type class="javax.baja.security.BPassword"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.decodeDocument(javax.baja.io.ValueDocDecoder) -->
<method name="decodeDocument"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="decoder">
<type class="javax.baja.io.ValueDocDecoder"/>
</parameter>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.next() -->
<method name="next"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.elem() -->
<method name="elem"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="com.tridium.nre.util.IElement"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.skip() -->
<method name="skip"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.skip(int) -->
<method name="skip"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="depth">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.type() -->
<method name="type"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.line() -->
<method name="line"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.column() -->
<method name="column"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.close() -->
<method name="close"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.depth() -->
<method name="depth"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.getEncoding() -->
<method name="getEncoding"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.isZipped() -->
<method name="isZipped"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="boolean"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.getVersion() -->
<method name="getVersion"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.util.Version"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.parse() -->
<method name="parse"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="com.tridium.nre.util.IElement"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.parse(boolean) -->
<method name="parse"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="close">
<type class="boolean"/>
</parameter>
<return>
<type class="com.tridium.nre.util.IElement"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.parseCurrent() -->
<method name="parseCurrent"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="com.tridium.nre.util.IElement"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.parseCurrent(boolean) -->
<method name="parseCurrent"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="close">
<type class="boolean"/>
</parameter>
<return>
<type class="com.tridium.nre.util.IElement"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.getTypeResolver() -->
<method name="getTypeResolver"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.io.ValueDocDecoder$ITypeResolver"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.setTypeResolver(javax.baja.io.ValueDocDecoder.ITypeResolver) -->
<method name="setTypeResolver"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="typeResolver">
<type class="javax.baja.io.ValueDocDecoder$ITypeResolver"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.err(java.lang.String, java.lang.Throwable) -->
<method name="err"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="cause">
<type class="java.lang.Throwable"/>
</parameter>
<return>
<type class="java.lang.RuntimeException"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.err(java.lang.String) -->
<method name="err"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="java.lang.RuntimeException"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.warningAndSkip(java.lang.String) -->
<method name="warningAndSkip"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.RuntimeException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.warning(java.lang.String) -->
<method name="warning"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.RuntimeException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.getLog() -->
<method name="getLog"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.util.logging.Logger"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.setLog(java.util.logging.Logger) -->
<method name="setLog"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="log">
<type class="java.util.logging.Logger"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.getWarningCount() -->
<method name="getWarningCount"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.getXmlParser() -->
<method name="getXmlParser"  public="true">
<description/>
<return>
<type class="javax.baja.xml.XParser"/>
</return>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.unmarshal(java.lang.String) -->
<method name="unmarshal"  public="true" static="true">
<description>
Unmarshal a value from an XML string
</description>
<parameter name="xml">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.unmarshal(java.lang.String, javax.baja.io.ValueDocDecoder.ITypeResolver) -->
<method name="unmarshal"  public="true" static="true">
<description>
Unmarshal a value from an XML string
</description>
<parameter name="xml">
<type class="java.lang.String"/>
</parameter>
<parameter name="typeResolver">
<type class="javax.baja.io.ValueDocDecoder$ITypeResolver"/>
</parameter>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.unmarshal(java.lang.String, javax.baja.sys.Context) -->
<method name="unmarshal"  public="true" static="true">
<description>
Unmarshal a value from an XML string
</description>
<tag name="@since">Niagara 4.0</tag>
<parameter name="xml">
<type class="java.lang.String"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.unmarshal(java.lang.String, javax.baja.io.ValueDocDecoder.ITypeResolver, javax.baja.sys.Context) -->
<method name="unmarshal"  public="true" static="true">
<description>
Unmarshal a value from an XML string
</description>
<tag name="@since">Niagara 4.0</tag>
<parameter name="xml">
<type class="java.lang.String"/>
</parameter>
<parameter name="typeResolver">
<type class="javax.baja.io.ValueDocDecoder$ITypeResolver"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.defaultTypeResolver -->
<field name="defaultTypeResolver"  public="true" static="true" final="true">
<type class="javax.baja.io.ValueDocDecoder$ITypeResolver"/>
<description/>
</field>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.passPhraseValidator -->
<field name="passPhraseValidator"  protected="true">
<type class="javax.baja.security.BIPasswordValidator"/>
<description/>
</field>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.reversibleEncodingKeySource -->
<field name="reversibleEncodingKeySource"  protected="true">
<type class="com.tridium.nre.security.EncryptionKeySource"/>
<description/>
</field>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.passwordObjectEncoder -->
<field name="passwordObjectEncoder"  protected="true">
<type class="com.tridium.nre.security.io.BogPasswordObjectEncoder"/>
<description/>
</field>

<!-- javax.baja.io.ValueDocDecoder.BogDecoderPlugin.version -->
<field name="version"  protected="true">
<type class="javax.baja.util.Version"/>
<description/>
</field>

</class>
</bajadoc>
