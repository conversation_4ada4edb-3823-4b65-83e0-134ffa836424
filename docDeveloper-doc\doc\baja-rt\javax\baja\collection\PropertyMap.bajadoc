<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.collection.PropertyMap" name="PropertyMap" packageName="javax.baja.collection" public="true">
<description>
PropertyMap represents a BComplex instance as a Map of Property to BValue.&#xa; The BComplex instance is not modifiable through this interface.
</description>
<tag name="@author"><PERSON>, <PERSON></tag>
<tag name="@creation">2/23/14, 1/19/2016</tag>
<tag name="@since">Niagara 4.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<parameterizedType class="java.util.Map">
<args>
<type class="javax.baja.sys.Property"/>
<type class="javax.baja.sys.BValue"/>
</args>
</parameterizedType>
</implements>
<!-- javax.baja.collection.PropertyMap(javax.baja.sys.BComplex) -->
<constructor name="PropertyMap" public="true">
<parameter name="obj">
<type class="javax.baja.sys.BComplex"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.collection.PropertyMap.clear() -->
<method name="clear"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.containsKey(java.lang.Object) -->
<method name="containsKey"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="key">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.containsValue(java.lang.Object) -->
<method name="containsValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="value">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.entrySet() -->
<method name="entrySet"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<parameterizedType class="java.util.Set">
<args>
<parameterizedType class="java.util.Map$Entry">
<args>
<type class="javax.baja.sys.Property"/>
<type class="javax.baja.sys.BValue"/>
</args>
</parameterizedType>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description/>
<parameter name="o">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.get(java.lang.Object) -->
<method name="get"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="key">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.hashCode() -->
<method name="hashCode"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.isEmpty() -->
<method name="isEmpty"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.keySet() -->
<method name="keySet"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<parameterizedType class="java.util.Set">
<args>
<type class="javax.baja.sys.Property"/>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.put(javax.baja.sys.Property, javax.baja.sys.BValue) -->
<method name="put"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.putAll(java.util.Map&lt;? extends javax.baja.sys.Property, ? extends javax.baja.sys.BValue&gt;) -->
<method name="putAll"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="toAdd">
<parameterizedType class="java.util.Map">
<args>
<wildcardType class="?">
<bounds kind="extends">
<type class="javax.baja.sys.Property"/>
</bounds>
</wildcardType>
<wildcardType class="?">
<bounds kind="extends">
<type class="javax.baja.sys.BValue"/>
</bounds>
</wildcardType>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.remove(java.lang.Object) -->
<method name="remove"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="property">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.size() -->
<method name="size"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.collection.PropertyMap.values() -->
<method name="values"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<parameterizedType class="java.util.Collection">
<args>
<type class="javax.baja.sys.BValue"/>
</args>
</parameterizedType>
</return>
</method>

</class>
</bajadoc>
