<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="ace" runtimeProfile="rt" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>Driver for ACE devices</description>
<package name="com.tridium.ace"/>
<package name="com.tridium.ace.datatypes"/>
<package name="com.tridium.ace.sys"/>
<package name="com.tridium.ace.component"/>
<package name="com.tridium.ace.enums"/>
<package name="com.tridium.ace.program"/>
<package name="com.tridium.ace.comm"/>
<package name="com.tridium.ace.point"/>
<class packageName="com.tridium.ace.component" name="BAceAction"><description>BAceAction</description></class>
<class packageName="com.tridium.ace.sys" name="BAceApp"><description>BAceApp</description></class>
<class packageName="com.tridium.ace.program" name="BAceAppDownloadJob"><description>BAceAppDownloadJob download app file to device list and restart devices</description></class>
<class packageName="com.tridium.ace.sys" name="BAceAppFile"><description>BAceAppFile represents a JSON file containing a ACE application definition.</description></class>
<class packageName="com.tridium.ace.enums" name="BAceCompQueryTypeEnum"><description>BAceCompQueryTypeEnum</description></class>
<class packageName="com.tridium.ace.component" name="BAceComponent"><description>BAceComponent</description></class>
<class packageName="com.tridium.ace.enums" name="BAceCovRegisterTypeEnum"><description>BAceMessageType</description></class>
<class packageName="com.tridium.ace" name="BAceDevice"><description>BAceDevice models a single device</description></class>
<class packageName="com.tridium.ace" name="BAceDeviceFolder"><description>BAceDeviceFolder is a folder for BAceDevice.</description></class>
<class packageName="com.tridium.ace.datatypes" name="BAceDownloadParams"><description>BAceDownloadParams - is container for properties passed in download application action</description></class>
<class packageName="com.tridium.ace.component" name="BAceDynamicComp"><description>BAceDynamicComp</description></class>
<class packageName="com.tridium.ace.enums" name="BAceFileTransferDirectionEnum"><description>BAceFileTransferDirectionEnum</description></class>
<class packageName="com.tridium.ace.sys" name="BAceFolder"><description>BAceFolder</description></class>
<class packageName="com.tridium.ace.comm" name="BAceIpcCommConfig"><description>BAceIpcCommConfig is commConfig implementation for ACE instance running in same platform os</description></class>
<class packageName="com.tridium.ace" name="BAceIpcNetwork"><description>BAceIpcNetwork is BAceNetwork implementation for ACE instance running in same platform os.</description></class>
<class packageName="com.tridium.ace.component" name="BAceLink"><description>BAceLink</description></class>
<class packageName="com.tridium.ace.enums" name="BAceLogLevelEnum"><description>BAceLogLevelEnum</description></class>
<class packageName="com.tridium.ace.enums" name="BAceMessageTypeEnum"><description>BAceMessageType</description></class>
<class packageName="com.tridium.ace" name="BAceNetwork"><description>BAceNetwork is base class for network of Ace devices.</description></class>
<class packageName="com.tridium.ace.point" name="BAcePointDeviceExt"><description>BAcePointDeviceExt is a container for ace proxy points.</description></class>
<class packageName="com.tridium.ace.point" name="BAcePointDiscoveryLeaf"><description>BAcePointDiscoveryLeaf is container class for point elements to display in&#xa; point discovery pane and pass to new point callback.</description></class>
<class packageName="com.tridium.ace.point" name="BAcePointDiscoveryPreferences"></class>
<class packageName="com.tridium.ace.point" name="BAcePointFolder"><description>BAcePointFolder</description></class>
<class packageName="com.tridium.ace.datatypes" name="BAcePrimitive"><description>AcePrimitive</description></class>
<class packageName="com.tridium.ace.enums" name="BAcePrimitiveTypeEnum"><description>AcePrimitiveType</description></class>
<class packageName="com.tridium.ace.point" name="BAceProxyExt"><description>BAceAbstractProxyExt</description></class>
<class packageName="com.tridium.ace.program" name="BAceReadFileJob"><description>BAceReadFileJob read file from device list</description></class>
<class packageName="com.tridium.ace.datatypes" name="BAceReadFileParams"><description>BAceReadFileParams - is container for properties passed in read file action</description></class>
<class packageName="com.tridium.ace.datatypes" name="BAceReadParams"><description>BAceReadParams - is container arguments needed to read an ACE component property</description></class>
<class packageName="com.tridium.ace.enums" name="BAceReqActionTypeEnum"><description>BAceReqActionTypeEnum</description></class>
<class packageName="com.tridium.ace.program" name="BAceSaveAppJob"><description>BAceSaveAppJob save application with job.</description></class>
<class packageName="com.tridium.ace.sys" name="BAceScheme"><description>BAceScheme is the portal from AceAppFile to AceSpace.</description></class>
<class packageName="com.tridium.ace.enums" name="BAceSlotTypeEnum"><description>BAceSlotTypeEnum</description></class>
<class packageName="com.tridium.ace.sys" name="BAceSpace"><description>BAceSpace is the component space for a ACE application file.</description></class>
<class packageName="com.tridium.ace.datatypes" name="BAceStatus"><description>BAceStatus is implementation of &lt;code&gt;BIAceInteger&lt;/code&gt; for ACE component properties&#xa; which represent &lt;code&gt;BStatus&lt;/code&gt;.</description></class>
<class packageName="com.tridium.ace.component" name="BAceTextBlock"><description>BAceTextBlock</description></class>
<class packageName="com.tridium.ace.program" name="BAceViewApp"><description>BAceViewApp uploads the current contents of running app and creates database version.</description></class>
<class packageName="com.tridium.ace.datatypes" name="BAceWriteFileParams"><description>BAceWriteFileParams - is container for properties past in download file action</description></class>
<class packageName="com.tridium.ace.point" name="BActionPoint"><description>Implements an action point, which is an integration of a single&#xa; action from a proxy, similar to what BControlPoint does for a&#xa; single value.</description></class>
<class packageName="com.tridium.ace.point" name="BBooleanActionPoint"></class>
<class packageName="com.tridium.ace.datatypes" name="BExeParam"><description>BExeParam is implementation of &lt;code&gt;BIAceInteger&lt;/code&gt; for ACE component&#xa; exeParam property.</description></class>
<class packageName="com.tridium.ace.datatypes" name="BIAceInteger" category="interface"><description>BIAceInteger is interface for classes which provide special behavior&#xa; in Niagara environment and are implemented in the ACE environment as a&#xa; numeric type that can be contained in a int.</description></class>
<class packageName="com.tridium.ace.sys" name="BIAceSpace" category="interface"><description>BIAceSpace</description></class>
<class packageName="com.tridium.ace.point" name="BNumericActionPoint"></class>
<class packageName="com.tridium.ace.point" name="BVoidActionPoint"><description>Implementation of an action point for an action with void data type.</description></class>
<class packageName="com.tridium.ace.datatypes" name="BWriteWorker"><description>BWriteWorker</description></class>
</module>
</bajadoc>
