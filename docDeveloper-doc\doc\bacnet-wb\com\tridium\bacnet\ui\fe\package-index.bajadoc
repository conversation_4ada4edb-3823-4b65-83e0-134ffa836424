<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="wb" name="com.tridium.bacnet.ui.fe">
<description/>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetAddressFE"><description>BBacnetAddressFE allows viewing and editing of a BBacnetAddress.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetAnyFE"><description>BBacnetAnyFE allows viewing and editing of a BBacnetAny.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetBinaryPvFE"><description>BBacnetBinaryPvFE allows viewing and editing of a BBacnetBinaryPv&#xa; using a text field.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetBitStringFE"><description>BBacnetBitStringFE allows viewing and editing of a&#xa; BBacnetBitString.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetCalendarEntryFE"><description>BBacnetCalendarEntryFE allows viewing and editing of a BBacnetCalendarEntry.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetDateFE"><description>BBacnetDateFE allows viewing and editing of a BBacnetDate.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetEventTypeFE"><description>BBacnetEventTypeFE allows the user to manage&#xa; the event parameters of an event enrollment object</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetNotifyTypeFE"><description>BBacnetNotifyTypeFE allows editing of BBacnetNotifyType.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetNullFE"><description>BBacnetNullFE allows viewing of a BBacnetNull&#xa; using a text field.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetObjectIdentifierFE"><description>BBacnetObjectIdentifierFE allows viewing and editing of a BBacnetObjectIdentifier.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetPriorityValueFE"><description>BBacnetPriorityValueFE allows viewing and editing of a BBacnetPriorityValue.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetRecipientFE"><description>BBacnetRecipientFE allows viewing and editing of a BBacnetRecipient.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetScaleFE"><description>BBacnetScaleFE allows viewing and editing of a BBacnetScale.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetTimeFE"><description>BBacnetTimeFE allows viewing and editing of a BBacnetTime.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetTimeStampFE"><description>BBacnetTimeStampFE allows viewing and editing of a BBacnetTimeStamp.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetTimeValueFE"><description>BBacnetTimeValueFE allows viewing and editing of a BBacnetTimeValue.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetUnsignedFE"><description>BBacnetUnsignedFE allows viewing and editing of a BBacnetUnsigned&#xa; using a text field.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BBacnetWeekNDayFE"><description>BBacnetWeekNDayFE allows user-friendly configuration&#xa; of a BacnetWeekNDay bit string.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BDiscoveryNetworksFE"><description>BDiscoveryNetworksFE allows viewing and editing of a&#xa; BDiscoveryNetworks.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BMultiWildcardableFieldFE"><description>BMultiWildcardableFieldFE allows viewing and editing of&#xa; multiple time fields.</description></class>
<class packageName="com.tridium.bacnet.ui.fe" name="BVirtualTuningPolicyNameFE"/>
</package>
</bajadoc>
