<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.collection.AbstractRow" name="AbstractRow" packageName="javax.baja.collection" public="true" abstract="true">
<description/>
<tag name="@author">&lt;a href=&#x22;mailto:<EMAIL>&#x22;&gt;<PERSON>&lt;/a&gt;</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<parameterizedType class="javax.baja.collection.Row">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</implements>
<typeParameters>
<typeVariable class="T">
<bounds>
<type class="javax.baja.sys.BIObject"/>
</bounds>
</typeVariable>
</typeParameters>
<!-- javax.baja.collection.AbstractRow(javax.baja.collection.BITable&lt;T&gt;, T) -->
<constructor name="AbstractRow" protected="true">
<parameter name="table">
<parameterizedType class="javax.baja.collection.BITable">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</parameter>
<parameter name="rowObject">
<typeVariable class="T"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.collection.AbstractRow.getTable() -->
<method name="getTable"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<parameterizedType class="javax.baja.collection.BITable">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.collection.AbstractRow.rowObject() -->
<method name="rowObject"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<typeVariable class="T"/>
</return>
</method>

<!-- javax.baja.collection.AbstractRow.cell(javax.baja.collection.Column) -->
<method name="cell"  public="true" abstract="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="column">
<type class="javax.baja.collection.Column"/>
</parameter>
<return>
<type class="javax.baja.sys.BIObject"/>
</return>
</method>

<!-- javax.baja.collection.AbstractRow.getCellFlags(javax.baja.collection.Column) -->
<method name="getCellFlags"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="column">
<type class="javax.baja.collection.Column"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.collection.AbstractRow.getCellFacets(javax.baja.collection.Column) -->
<method name="getCellFacets"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="column">
<type class="javax.baja.collection.Column"/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.collection.AbstractRow.safeCopy() -->
<method name="safeCopy"  public="true" abstract="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<parameterizedType class="javax.baja.collection.Row">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</return>
</method>

</class>
</bajadoc>
