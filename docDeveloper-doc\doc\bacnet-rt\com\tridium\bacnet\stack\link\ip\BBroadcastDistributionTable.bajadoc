<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.link.ip.BBroadcastDistributionTable" name="BBroadcastDistributionTable" packageName="com.tridium.bacnet.stack.link.ip" public="true">
<description>
BBroadcastDistributionTable represents the Broadcast&#xa; Distribution Table (BDT) of a Bacnet Broadcast Management&#xa; Device (BBMD).
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">18 Apr 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<action name="validate" flags="ha">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;validate&lt;/code&gt; action.
</description>
<tag name="@see">#validate()</tag>
</action>

</class>
</bajadoc>
