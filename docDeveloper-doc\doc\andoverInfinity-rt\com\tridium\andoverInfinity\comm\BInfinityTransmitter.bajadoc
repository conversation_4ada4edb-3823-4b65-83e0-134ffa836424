<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.BInfinityTransmitter" name="BInfinityTransmitter" packageName="com.tridium.andoverInfinity.comm" public="true">
<description>
A custom transmitter for Infinity that overrides forceTransmit method
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddfSerial.comm.BDdfSerialTransmitter"/>
</extends>
<!-- com.tridium.andoverInfinity.comm.BInfinityTransmitter() -->
<constructor name="BInfinityTransmitter" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.comm.BInfinityTransmitter.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityTransmitter.forceTransmit(com.tridium.ddf.comm.req.BIDdfRequest) -->
<method name="forceTransmit"  public="true">
<description>
Override the default implementation to pass the network object back to the &#xa; request so that the request can use screen buffer&#x27;s cursor position and other&#xa; info to help create the correct byte[] to transmit.
</description>
<tag name="@see">com.tridium.devSerialDriver.comm.BDdfSerialTransmitter#forceTransmit(com.tridium.devDriver.comm.req.BIDdfRequest)</tag>
<parameter name="devRequest">
<type class="com.tridium.ddf.comm.req.BIDdfRequest"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- com.tridium.andoverInfinity.comm.BInfinityTransmitter.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
