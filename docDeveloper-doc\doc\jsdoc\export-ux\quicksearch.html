<html>
<head>
</head>
<body style="background: transparent;">
    <script src="scripts/docstrap.lib.js"></script>
    <script src="scripts/lunr.min.js"></script>
    <script src="scripts/fulltext-search.js"></script>

    <script type="text/x-docstrap-searchdb">
    {"modules.list.html":{"id":"modules.list.html","title":"Modules","body":" export Modules nmodule/export/rc/ExportDestinationTypenmodule/export/rc/Transformernmodule/export/rc/TransformOperationnmodule/export/rc/TransformOperationProvider Modules Classes module:nmodule/export/rc/ExportDestinationType module:nmodule/export/rc/Transformer module:nmodule/export/rc/TransformOperation × Search results Close "},"index.html":{"id":"index.html","title":"Index","body":" export Modules nmodule/export/rc/ExportDestinationTypenmodule/export/rc/Transformernmodule/export/rc/TransformOperationnmodule/export/rc/TransformOperationProvider export This module contains resources for performing export operations from the browser, and implementing your own export operations. Performing an export In Workbench or the HTML5 Hx Profile, there will be an Export button shown in the toolbar. There will also be an Export button in the right-click menu on exportable objects. Either will open the Export Dialog for the selected object. In the Export Dialog, the Exporter list will show the available exporters on the current object or Widget. Configuration options will be shown for the selected exporter to allow you to choose how the export will be performed. The Destination list will show the available destinations: where to send the exported data. Configuration options will also be shown for the selected export destination. In addition, APIs are provided to allow you to implement new ways of extracting data out of your Niagara system. Implementing Export behavior in the Station and Workbench BExporter To implement a new exporter, subclass baja:Exporter and implement the export() method. This method receives a Niagara object and converts it to a stream of text or binary data. If the exporter should work only in Workbench, implement workbench:IWbViewExporter and the exporter will export the current workbench:WbView. See the Bajadoc for baja:Exporter for more details. BIExportDestinationType Workbench provides a set of export destinations out of the box, such as Save To File or View In Workbench. If you would like to implement a way of sending exported data to a different destination (e.g. Dropbox, Gmail, a REST endpoint), implement the export:IExportDestinationType interface. This provides a way to define which environments in which the destination is valid, and how the transformed data will be sent there. See the Bajadoc for export:IExportDestinationType for more details. Implementing Export behavior in the browser TransformOperationProvider All baja:Exporter classes that work in the Station will be accessible for use in the browser. Some bajaux Widgets may also export their own data in their own way, using JavaScript. To allow your Widget to hook into the Export Dialog to perform an export right from the browser, implement the TransformOperationProvider interface on your top-level Widget. In this way, your Widget can define additional transform operations to be performed when the user clicks the Export button in the toolbar. Your Widget instance can simply implement the getTransformOperations method. Each TransformOperation encapsulates a transform operation: the object to be transformed, the transformer used to transform it, and the destination to which the transformed data will be sent. See the JSDocs for more details. ExportDestinationType Similar to export:IExportDestinationType, the ExportDestinationType JavaScript module allows transformed data to be sent to a different destination, such as a POST from the browser to a REST endpoint. To implement, simply subclass ExportDestinationType. See the JSDocs for more details. For the destination to appear in the destination list in the Export Dialog, its existence must be registered with the framework. To do this, implement a Java class implementing export:IJavaScriptExportDestination and point it at your JavaScript module. × Search results Close "},"module-nmodule_export_rc_ExportDestinationType.html":{"id":"module-nmodule_export_rc_ExportDestinationType.html","title":"Module: nmodule/export/rc/ExportDestinationType","body":" export Modules nmodule/export/rc/ExportDestinationTypenmodule/export/rc/Transformernmodule/export/rc/TransformOperationnmodule/export/rc/TransformOperationProvider Module: nmodule/export/rc/ExportDestinationType API Status: Development A destination type describes where a transform operation may send its output. Examples include: the file system; the clipboard; the cloud; an external application. When sending transformed data to a destination, there are three touchpoints in that workflow: checkValid(): does this destination even work? Example: a destination that sends data to the clipboard is not valid in a browser that does not support the Clipboard API, so don't even present it to the user as an option. prepare(): does this destination work for the transform as requested by the user? Example: a destination that writes to the file system would prompt the user to make sure it's okay to overwrite a particular file. transform(): All systems go - write the data. &lt;abstract&gt; new (require(\"nmodule/export/rc/ExportDestinationType\"))() Methods checkValid() Throws: if this destination is not valid in the current environment Type Error getDestinationConfig(transformOp) When invoking a transform operation using the Export Dialog, options can be provided by the user to configure how to send the transformed data to its destination. If this destination should provide user-configurable options, override this function to provide them in the form of a Component. This Component will be shown to the user in a Property Sheet for configuration. Parameters: Name Type Description transformOp module:nmodule/export/rc/TransformOperation The transform operation this destination type is a target for Returns: The destination config object. Type baja.Component | Promise.&lt;(baja.Component|undefined)&gt; | undefined getDestinationContextObject(config) When the user invokes the transform, the Component as edited by the user will need to be converted to a context object to be used in the transform() method. This function provides a hook to perform extra processing during that conversion. By default, will return a simple mapping of the Component's slot names to their values. Parameters: Name Type Description config baja.Component The destination config component as edited by the user Returns: the context object Type object | Promise.&lt;object&gt; &lt;abstract&gt; getDisplayName() Returns: display name for the destination Type string prepare(transformOp, cx) Perform any necessary preparation before the actual transform. This is a hook for things like user prompts (\"do you want to overwrite this file?\", etc). By default, simply returns true. Parameters: Name Type Description transformOp module:nmodule/export/rc/TransformOperation the transform operation cx object the export context Returns: return or resolve true to indicate that the transform should proceed. Return false to indicate that the transform was canceled (either by the user, or conditions simply do not support this transform/destination). Throw or reject only in case of an unexpected error condition. Type Promise.&lt;boolean&gt; | boolean &lt;abstract&gt; transform(transformOp, cx) Perform the entire transformation operation: execute the transform to obtain the transformed data, and send that data to this destination. Important note: this method should not require any user interaction. It should only be data-in to data-out. Any user prompts should be completed in prepare(). Parameters: Name Type Description transformOp module:nmodule/export/rc/TransformOperation the transform operation cx object the export context Returns: Type Promise | * × Search results Close "},"module-nmodule_export_rc_Transformer.html":{"id":"module-nmodule_export_rc_Transformer.html","title":"Module: nmodule/export/rc/Transformer","body":" export Modules nmodule/export/rc/ExportDestinationTypenmodule/export/rc/Transformernmodule/export/rc/TransformOperationnmodule/export/rc/TransformOperationProvider Module: nmodule/export/rc/Transformer API Status: Development Transform one object into another type. &lt;abstract&gt; new (require(\"nmodule/export/rc/Transformer\"))() Methods getDefaultConfig(transformedObject) When invoking a transform operation using the Export Dialog, options can be provided by the user to configure how the transform is performed. If this transformer should provide user-configurable options, override this function to provide them in the form of a Component. This Component will be shown to the user in a Property Sheet for configuration. Parameters: Name Type Description transformedObject * the transform object can be used to help decide values on default config. See: module:nmodule/export/rc/TransformOperation#getTransformedObject Returns: Type baja.Component | Promise.&lt;(baja.Component|undefined)&gt; | undefined Example Provide a config option for 'prettyPrint' that uses a custom display name. getDefaultConfig() { var comp = baja.$('baja:Component'); comp.add({ slot: 'prettyPrint', value: true, cx: { displayName: 'Pretty Print' } }); return comp; } &lt;abstract&gt; getDisplayName() Returns: the display name of this transformer Type string getExportContextObject(config) When the user invokes the transform, the Component as edited by the user will need to be converted to a context object to be used in the transform() method. This function provides a hook to perform extra processing during that conversion. By default, will return a simple mapping of the Component's slot names to their values. Parameters: Name Type Description config baja.Component | object the config component as edited by the user Returns: the context object Type object | Promise.&lt;object&gt; &lt;abstract&gt; getFileExtension() Returns: the default file extension to use when saving the data produced by this transformer Type string &lt;abstract&gt; getIcon() Returns: the icon for this transformer Type string &lt;abstract&gt; getMimeType() Returns: the mime type for the data produced by this transformer Type string isSupplier() By default, all Transformers supply data to an ExportDestinationType. If there is nothing to supply to a destination, then return false and this will instruct the UI to show no ExportDestinationTypes. See: module:nmodule/export/rc/ExportDestinationType Returns: Type boolean &lt;abstract&gt; transform(object [, cx]) Transform an object into another type of data. Parameters: Name Type Argument Description object * the object to be transformed cx object &lt;optional&gt; transform context containing options as configured by the user Returns: to be resolved with the transformed data Type Promise.&lt;*&gt; | * × Search results Close "},"module-nmodule_export_rc_TransformOperation.html":{"id":"module-nmodule_export_rc_TransformOperation.html","title":"Module: nmodule/export/rc/TransformOperation","body":" export Modules nmodule/export/rc/ExportDestinationTypenmodule/export/rc/Transformernmodule/export/rc/TransformOperationnmodule/export/rc/TransformOperationProvider Module: nmodule/export/rc/TransformOperation API Status: Development A TransformOperation encapsulates the lifecycle of transforming an object into data, and sending that data to a destination. new (require(\"nmodule/export/rc/TransformOperation\"))(transformer, object) Parameters: Name Type Description transformer module:nmodule/export/rc/Transformer the transformer that will perform the transform operation object * the object to be transformed Methods doTransform(cx) Parameters: Name Type Description cx object transform context. This will be passed to the transform() method. Returns: Type Promise getDisplayName() Returns: the display name to be shown to the user to allow him or her to choose to perform this transform operation Type String getTransformedObject() Returns: the object to be transformed Type * getTransformer() Returns: the transformer that will perform the transform operation Type module:nmodule/export/rc/Transformer × Search results Close "},"module-nmodule_export_rc_TransformOperationProvider.html":{"id":"module-nmodule_export_rc_TransformOperationProvider.html","title":"Interface: module:nmodule/export/rc/TransformOperationProvider","body":" export Modules nmodule/export/rc/ExportDestinationTypenmodule/export/rc/Transformernmodule/export/rc/TransformOperationnmodule/export/rc/TransformOperationProvider Interface: module:nmodule/export/rc/TransformOperationProvider module:nmodule/export/rc/TransformOperationProvider API Status: Development A provider for TransformOperations. Methods &lt;abstract&gt; getTransformOperations(subject) Resolve an Array of the available TransformOperations to the given subject. Note that the subject received may depend on how the transform was invoked. For instance, if performing an Export command on a node in the nav tree, the subject might be a Component. If performing an Export command by using the Export button in the toolbar, the subject would be the Widget currently being viewed. Be sure to perform the appropriate type checking on the input subject. Parameters: Name Type Description subject * Returns: Type Promise.&lt;Array.&lt;module:nmodule/export/rc/TransformOperation&gt;&gt; × Search results Close "}}
    </script>

    <script type="text/javascript">
        $(document).ready(function() {
            Searcher.init();
        });

        $(window).on("message", function(msg) {
            var msgData = msg.originalEvent.data;

            if (msgData.msgid != "docstrap.quicksearch.start") {
                return;
            }

            var results = Searcher.search(msgData.searchTerms);

            window.parent.postMessage({"results": results, "msgid": "docstrap.quicksearch.done"}, "*");
        });
    </script>
</body>
</html>
