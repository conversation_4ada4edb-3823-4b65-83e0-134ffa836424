<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.OrdQueryList" name="OrdQueryList" packageName="javax.baja.naming" public="true" final="true">
<description>
OrdQueryList is used to manipulate a list of OrdQueries.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">16 Jan 03</tag>
<tag name="@version">$Revision: 6$ $Date: 5/19/03 11:15:15 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- javax.baja.naming.OrdQueryList(javax.baja.naming.OrdQuery[], int) -->
<constructor name="OrdQueryList" public="true">
<parameter name="q">
<type class="javax.baja.naming.OrdQuery" dimension="1"/>
</parameter>
<parameter name="size">
<type class="int"/>
</parameter>
<description>
Construct a list from the specified array and size.
</description>
</constructor>

<!-- javax.baja.naming.OrdQueryList(javax.baja.naming.OrdQuery[]) -->
<constructor name="OrdQueryList" public="true">
<parameter name="q">
<type class="javax.baja.naming.OrdQuery" dimension="1"/>
</parameter>
<description>
Construct a list from the specified array with &#xa; a size of q.length.
</description>
</constructor>

<!-- javax.baja.naming.OrdQueryList() -->
<constructor name="OrdQueryList" public="true">
<description>
Construct a list of size 0.
</description>
</constructor>

<!-- javax.baja.naming.OrdQueryList.size() -->
<method name="size"  public="true">
<description>
Get the number of queries in the list.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.naming.OrdQueryList.get(int) -->
<method name="get"  public="true">
<description>
Get the query at the specified index.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdQuery"/>
</return>
</method>

<!-- javax.baja.naming.OrdQueryList.toArray() -->
<method name="toArray"  public="true">
<description>
Get the querys as an array.
</description>
<return>
<type class="javax.baja.naming.OrdQuery" dimension="1"/>
</return>
</method>

<!-- javax.baja.naming.OrdQueryList.prev(int) -->
<method name="prev"  public="true">
<description>
Get the query at index-1 or null if index is zero.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdQuery"/>
</return>
</method>

<!-- javax.baja.naming.OrdQueryList.next(int) -->
<method name="next"  public="true">
<description>
Get the query at index+1 or null if past end of list.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdQuery"/>
</return>
</method>

<!-- javax.baja.naming.OrdQueryList.isSameScheme(int, int) -->
<method name="isSameScheme"  public="true">
<description>
Return if the queries at indexA and indexB&#xa; are the same class and same scheme identifier.&#xa; If indexA or indexB is out of range, then &#xa; return false.
</description>
<parameter name="indexA">
<type class="int"/>
</parameter>
<parameter name="indexB">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.OrdQueryList.add(javax.baja.naming.OrdQueryList) -->
<method name="add"  public="true">
<description>
Add the specified query list to the end of this list.
</description>
<parameter name="list">
<type class="javax.baja.naming.OrdQueryList"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.OrdQueryList.add(javax.baja.naming.OrdQuery) -->
<method name="add"  public="true">
<description>
Add the specified query to the end of this list.
</description>
<parameter name="q">
<type class="javax.baja.naming.OrdQuery"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.OrdQueryList.add(int, javax.baja.naming.OrdQuery) -->
<method name="add"  public="true">
<description>
Add the specified query at the given index.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<parameter name="q">
<type class="javax.baja.naming.OrdQuery"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.OrdQueryList.replace(int, javax.baja.naming.OrdQuery) -->
<method name="replace"  public="true">
<description>
Replace the query at the specified index.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<parameter name="q">
<type class="javax.baja.naming.OrdQuery"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.OrdQueryList.remove(int) -->
<method name="remove"  public="true">
<description>
Remove the query at the specified index.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.OrdQueryList.clear() -->
<method name="clear"  public="true">
<description>
Remove all the queries.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.OrdQueryList.trim(int) -->
<method name="trim"  public="true">
<description>
Remove queries from 0 to start-1 and &#xa; keep queries from start to size-1.
</description>
<parameter name="start">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.OrdQueryList.trim(int, int) -->
<method name="trim"  public="true">
<description>
Remove queries from 0 to start-1.  Remove&#xa; queries from end to size-1.  Keep queries &#xa; from start to end-1.
</description>
<parameter name="start">
<type class="int"/>
</parameter>
<parameter name="end">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.OrdQueryList.merge(int, javax.baja.naming.OrdQuery) -->
<method name="merge"  public="true">
<description>
Merge the queries at index and index+1 into the &#xa; specified merged query.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<parameter name="merged">
<type class="javax.baja.naming.OrdQuery"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.OrdQueryList.shiftToHost(int) -->
<method name="shiftToHost"  public="true">
<description>
Starting from index-1 and working left to index zero, &#xa; strip any queries which return false for isHost().&#xa; This is the effect of shifting the specified query&#xa; left so that it immediately follows the host query.&#xa; If no host is present, then it has the effect of&#xa; triming all the queries to the left.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.OrdQueryList.shiftToSession(int) -->
<method name="shiftToSession"  public="true">
<description>
Starting from index-1 and working left to index zero, &#xa; strip any queries which return false for isHost and &#xa; isSession().   This is the effect of shifting the specified &#xa; query left so that it immediately follows the session query.&#xa; If no session is present, then it has the effect of&#xa; triming all the queries to the left.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.OrdQueryList.toString() -->
<method name="toString"  public="true">
<description>
To string.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

</class>
</bajadoc>
