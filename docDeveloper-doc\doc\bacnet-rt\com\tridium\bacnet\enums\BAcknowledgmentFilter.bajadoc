<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.enums.BAcknowledgmentFilter" name="BAcknowledgmentFilter" packageName="com.tridium.bacnet.enums" public="true" final="true">
<description>
BAcknowledgmentFilter represents the enumeration for restricting the types&#xa; of event-initiating objects that shall be included in a response to a&#xa; GetEnrollmentSummary-Request, based on their acknowledgement state.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">04 Aug 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;all&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;acked&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;notAcked&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
</class>
</bajadoc>
