<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BIBacnetConfigFolder" name="BIBacnetConfigFolder" packageName="javax.baja.bacnet.config" public="true" interface="true" abstract="true" category="interface">
<description>
BIBacnetConfigFolder is the common interface for&#xa; BLocalBacnetDevice and BIBacnetConfigFolder.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">30 Nov 2004</tag>
<tag name="@since">Niagara 3 BACnet 1.0</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<!-- javax.baja.bacnet.config.BIBacnetConfigFolder.getConfig() -->
<method name="getConfig"  public="true" abstract="true">
<description>
Get the parent network.
</description>
<return>
<type class="javax.baja.bacnet.config.BBacnetConfigDeviceExt"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BIBacnetConfigFolder.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
