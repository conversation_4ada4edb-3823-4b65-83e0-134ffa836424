<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.datatypes.BSvoSubordinate" name="BSvoSubordinate" packageName="com.tridium.bacnet.datatypes" public="true">
<description/>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="reference" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectReference"/>
<description>
Slot for the &lt;code&gt;reference&lt;/code&gt; property.
</description>
<tag name="@see">#getReference</tag>
<tag name="@see">#setReference</tag>
</property>

<property name="annotation" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;annotation&lt;/code&gt; property.
</description>
<tag name="@see">#getAnnotation</tag>
<tag name="@see">#setAnnotation</tag>
</property>

</class>
</bajadoc>
