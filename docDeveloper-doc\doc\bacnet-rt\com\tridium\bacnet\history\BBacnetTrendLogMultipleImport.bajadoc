<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.history.BBacnetTrendLogMultipleImport" name="BBacnetTrendLogMultipleImport" packageName="com.tridium.bacnet.history" public="true">
<description>
BBacnetTrendLogMultipleImport defines an archive action for transferring&#xa; one trend log from a remote Bacnet source to the local&#xa; destination.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">09 Oct 2009</tag>
<tag name="@since">Niagara 3.5</tag>
<extends>
<type class="com.tridium.bacnet.history.BAbstractBacnetHistory"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<property name="localHistoryNames" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;localHistoryNames&lt;/code&gt; property.&#xa; List of history names currently used to store&#xa; imported records.
</description>
<tag name="@see">#getLocalHistoryNames</tag>
<tag name="@see">#setLocalHistoryNames</tag>
</property>

</class>
</bajadoc>
