<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.job.BJobService$UncaughtJobExceptionHandler" name="BJobService.UncaughtJobExceptionHandler" packageName="javax.baja.job" protected="true" static="true" innerClass="true">
<description>
Handle ForkJoinPool uncaught exceptions
</description>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="java.lang.Thread$UncaughtExceptionHandler"/>
</implements>
<!-- javax.baja.job.BJobService.UncaughtJobExceptionHandler() -->
<constructor name="UncaughtJobExceptionHandler" protected="true">
<description/>
</constructor>

<!-- javax.baja.job.BJobService.UncaughtJobExceptionHandler.uncaughtException(java.lang.Thread, java.lang.Throwable) -->
<method name="uncaughtException"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Uncaught exception handler for thread pool
</description>
<parameter name="t">
<type class="java.lang.Thread"/>
<description>
thread
</description>
</parameter>
<parameter name="e">
<type class="java.lang.Throwable"/>
<description>
Throwable
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
