<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.relative.SubscriptionTable" name="SubscriptionTable" packageName="com.tridiumx.jsonToolkit.outbound.schema.relative" public="true">
<description>
SubscriptionTable&#xa;&#xa; One of these exists per schema with relative support&#xa;&#xa; The table tracks 2 things:&#xa;&#xa; 1) A mapping between all bound member targets and the base query items they were resolved against&#xa;    This allows us to lookup the base item to pass through the schema when a subscription event occurs&#xa; 2) A mapping of all the subscribers created for the bound member bindings
</description>
<tag name="@author"><PERSON> / <PERSON></tag>
<tag name="@version">1.00</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.SubscriptionTable.register(com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaBoundMember, javax.baja.naming.OrdTarget, javax.baja.sys.BComplex) -->
<method name="register"  public="true">
<description>
Register a target to subscribe to, and the base object that was used to resolve it.&#xa; For relative schemas in cov mode - Listen for updates from the child/leaf components, submit their Schema base object(s) for&#xa; which they were resolved against, for evaluation in the event an interesting update occurs.
</description>
<parameter name="member">
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaBoundMember"/>
<description>
the schema member for which this ord target was resolved.
</description>
</parameter>
<parameter name="ordTarget">
<type class="javax.baja.naming.OrdTarget"/>
<description>
is the leaf node (resolved target) we are monitoring for changes
</description>
</parameter>
<parameter name="base">
<type class="javax.baja.sys.BComplex"/>
<description>
is what the target was resolved against, to set as the base item the schema in the event of subscription event
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.SubscriptionTable.unsubscribe(com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaBoundMember) -->
<method name="unsubscribe"  public="true">
<description>
unsubscribe from all children that were subscribed on behalf&#xa; of the given bound member
</description>
<parameter name="binding">
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaBoundMember"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.SubscriptionTable.unregisterAndUnsubscribe(javax.baja.sys.BComponent) -->
<method name="unregisterAndUnsubscribe"  public="true">
<description>
Unsubscribe from a child component of a base item and remove from the mapping.
</description>
<parameter name="subscribedComponent">
<type class="javax.baja.sys.BComponent"/>
<description>
is the component we have previously subscribed to
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.SubscriptionTable.unregisterAndUnsubscribeAll() -->
<method name="unregisterAndUnsubscribeAll"  public="true">
<description>
Unsubscribes all and unregisters everything from the basemap.
</description>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
