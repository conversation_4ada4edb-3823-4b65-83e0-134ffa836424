<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetShedState" name="BBacnetShedState" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetShedState represents the Bacnet Shed State&#xa; enumeration.&#xa; &lt;p&gt;&#xa; BBacnetShedState is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Joseph Chandler</tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;shedInactive&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;shedRequestPending&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;shedCompliant&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;shedNonCompliant&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetShedState.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetShedState"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetShedState.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetShedState"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetShedState.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetShedState.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetShedState.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetShedState.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetShedState.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetShedState.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetShedState.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetShedState.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
String representation of this BEnum.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetShedState.SHED_INACTIVE -->
<field name="SHED_INACTIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for shedInactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetShedState.SHED_REQUEST_PENDING -->
<field name="SHED_REQUEST_PENDING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for shedRequestPending.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetShedState.SHED_COMPLIANT -->
<field name="SHED_COMPLIANT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for shedCompliant.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetShedState.SHED_NON_COMPLIANT -->
<field name="SHED_NON_COMPLIANT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for shedNonCompliant.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetShedState.shedInactive -->
<field name="shedInactive"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetShedState"/>
<description>
BBacnetShedState constant for shedInactive.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetShedState.shedRequestPending -->
<field name="shedRequestPending"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetShedState"/>
<description>
BBacnetShedState constant for shedRequestPending.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetShedState.shedCompliant -->
<field name="shedCompliant"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetShedState"/>
<description>
BBacnetShedState constant for shedCompliant.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetShedState.shedNonCompliant -->
<field name="shedNonCompliant"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetShedState"/>
<description>
BBacnetShedState constant for shedNonCompliant.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetShedState.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetShedState"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetShedState.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetShedState.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetShedState.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetShedState.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
