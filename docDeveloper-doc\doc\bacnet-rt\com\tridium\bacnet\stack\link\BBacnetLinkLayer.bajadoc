<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.link.BBacnetLinkLayer" name="BBacnetLinkLayer" packageName="com.tridium.bacnet.stack.link" public="true" abstract="true">
<description>
Tridium Bacnet Data Link Layer Implementation.&lt;p&gt;&#xa; BacnetLinkLayer represents the generic superclass&#xa; for all link layer implementations.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 5$ $Date: 12/19/01 4:35:15 PM$</tag>
<tag name="@creation">7 Aug 00</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
</class>
</bajadoc>
