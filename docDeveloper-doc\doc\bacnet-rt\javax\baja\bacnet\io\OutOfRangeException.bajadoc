<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.io.OutOfRangeException" name="OutOfRangeException" packageName="javax.baja.bacnet.io" public="true" category="exception">
<description>
An OutOfRangeException is thrown whenever an&#xa; out of range error is detected in encoding or&#xa; decoding an Asn production.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 2$ $Date: 12/19/01 4:35:43 PM$</tag>
<tag name="@creation">28 Jul 00</tag>
<tag name="@since">Niagara 3.8.107</tag>
<extends>
<type class="javax.baja.bacnet.io.AsnException"/>
</extends>
<!-- javax.baja.bacnet.io.OutOfRangeException(java.lang.String) -->
<constructor name="OutOfRangeException" public="true">
<parameter name="detailMessage">
<type class="java.lang.String"/>
<description>
the error message.
</description>
</parameter>
<description>
Constructor with specified detailed message.
</description>
</constructor>

<!-- javax.baja.bacnet.io.OutOfRangeException.toString() -->
<method name="toString"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

</class>
</bajadoc>
