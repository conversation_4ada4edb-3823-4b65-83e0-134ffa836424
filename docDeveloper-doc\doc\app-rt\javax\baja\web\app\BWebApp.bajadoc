<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="app" runtimeProfile="rt" qualifiedName="javax.baja.web.app.BWebApp" name="BWebApp" packageName="javax.baja.web.app" public="true" abstract="true">
<description>
Niagara Web App
</description>
<tag name="@author">g<PERSON><PERSON><PERSON></tag>
<tag name="@creation">27 Jul 2011</tag>
<tag name="@version">1</tag>
<tag name="@since">Niagara 3.7</tag>
<extends>
<type class="javax.baja.app.BApp"/>
</extends>
<implements>
<type class="javax.baja.web.app.BIWebApp"/>
</implements>
<!-- javax.baja.web.app.BWebApp() -->
<constructor name="BWebApp" public="true">
<description/>
</constructor>

<!-- javax.baja.web.app.BWebApp.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.web.app.BWebApp.getRequiredServices() -->
<method name="getRequiredServices"  public="true">
<description/>
<return>
<type class="javax.baja.sys.Type" dimension="1"/>
</return>
</method>

<!-- javax.baja.web.app.BWebApp.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
