<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetSpecialEvent" name="BBacnetSpecialEvent" packageName="javax.baja.bacnet.datatypes" public="true">
<description>
BBacnetSpecialEvent represents the BacnetSpecialEvent sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">6 June 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="eventName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;eventName&lt;/code&gt; property.
</description>
<tag name="@see">#getEventName</tag>
<tag name="@see">#setEventName</tag>
</property>

<property name="periodChoice" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;periodChoice&lt;/code&gt; property.
</description>
<tag name="@see">#getPeriodChoice</tag>
<tag name="@see">#setPeriodChoice</tag>
</property>

<property name="period" flags="">
<type class="javax.baja.sys.BValue"/>
<description>
Slot for the &lt;code&gt;period&lt;/code&gt; property.
</description>
<tag name="@see">#getPeriod</tag>
<tag name="@see">#setPeriod</tag>
</property>

<property name="eventPriority" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;eventPriority&lt;/code&gt; property.
</description>
<tag name="@see">#getEventPriority</tag>
<tag name="@see">#setEventPriority</tag>
</property>

<action name="addTimeValue" flags="">
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetTimeValue"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;addTimeValue&lt;/code&gt; action.
</description>
<tag name="@see">#addTimeValue(BBacnetTimeValue parameter)</tag>
</action>

<topic name="specialEventChanged" flags="">
<eventType>
<type class="javax.baja.sys.BValue"/>
</eventType><description>
Slot for the &lt;code&gt;specialEventChanged&lt;/code&gt; topic.
</description>
<tag name="@see">#fireSpecialEventChanged</tag>
</topic>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent() -->
<constructor name="BBacnetSpecialEvent" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.getEventName() -->
<method name="getEventName"  public="true">
<description>
Get the &lt;code&gt;eventName&lt;/code&gt; property.
</description>
<tag name="@see">#eventName</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.setEventName(java.lang.String) -->
<method name="setEventName"  public="true">
<description>
Set the &lt;code&gt;eventName&lt;/code&gt; property.
</description>
<tag name="@see">#eventName</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.getPeriodChoice() -->
<method name="getPeriodChoice"  public="true">
<description>
Get the &lt;code&gt;periodChoice&lt;/code&gt; property.
</description>
<tag name="@see">#periodChoice</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.setPeriodChoice(int) -->
<method name="setPeriodChoice"  public="true">
<description>
Set the &lt;code&gt;periodChoice&lt;/code&gt; property.
</description>
<tag name="@see">#periodChoice</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.getPeriod() -->
<method name="getPeriod"  public="true">
<description>
Get the &lt;code&gt;period&lt;/code&gt; property.
</description>
<tag name="@see">#period</tag>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.setPeriod(javax.baja.sys.BValue) -->
<method name="setPeriod"  public="true">
<description>
Set the &lt;code&gt;period&lt;/code&gt; property.
</description>
<tag name="@see">#period</tag>
<parameter name="v">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.getEventPriority() -->
<method name="getEventPriority"  public="true">
<description>
Get the &lt;code&gt;eventPriority&lt;/code&gt; property.
</description>
<tag name="@see">#eventPriority</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.setEventPriority(int) -->
<method name="setEventPriority"  public="true">
<description>
Set the &lt;code&gt;eventPriority&lt;/code&gt; property.
</description>
<tag name="@see">#eventPriority</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.addTimeValue(javax.baja.bacnet.datatypes.BBacnetTimeValue) -->
<method name="addTimeValue"  public="true">
<description>
Invoke the &lt;code&gt;addTimeValue&lt;/code&gt; action.
</description>
<tag name="@see">#addTimeValue</tag>
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetTimeValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.fireSpecialEventChanged(javax.baja.sys.BValue) -->
<method name="fireSpecialEventChanged"  public="true">
<description>
Fire an event for the &lt;code&gt;specialEventChanged&lt;/code&gt; topic.
</description>
<tag name="@see">#specialEventChanged</tag>
<parameter name="event">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.started() -->
<method name="started"  public="true">
<description>
Started.&#xa; Subclasses &lt;b&gt;MUST&lt;/b&gt; call &lt;code&gt;super.started()&lt;/code&gt;&#xa; to ensure that this code is executed.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.added(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="added"  public="true">
<description>
Property added.&#xa; Subclasses &lt;b&gt;MUST&lt;/b&gt; call &lt;code&gt;super.added()&lt;/code&gt;&#xa; to ensure that this code is executed.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.removed(javax.baja.sys.Property, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="removed"  public="true">
<description>
Property removed.&#xa; Subclasses &lt;b&gt;MUST&lt;/b&gt; call &lt;code&gt;super.removed()&lt;/code&gt;&#xa; to ensure that this code is executed.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="v">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<description>
Property changed.&#xa; Subclasses &lt;b&gt;MUST&lt;/b&gt; call &lt;code&gt;super.changed()&lt;/code&gt;&#xa; to ensure that this code is executed.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.subscribed() -->
<method name="subscribed"  public="true" final="true">
<description>
Callback when the component enters the subscribed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.unsubscribed() -->
<method name="unsubscribed"  public="true" final="true">
<description>
Callback when the component leaves the subscribed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.getAppliedCategoryMask() -->
<method name="getAppliedCategoryMask"  public="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.getCategoryMask() -->
<method name="getCategoryMask"  public="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.getPermissions(javax.baja.sys.Context) -->
<method name="getPermissions"  public="true">
<description>
Override to route to the virtual parent when we are in a virtual space.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.security.BPermissions"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.doAddTimeValue(javax.baja.bacnet.datatypes.BBacnetTimeValue) -->
<method name="doAddTimeValue"  public="true" final="true">
<description/>
<parameter name="tv">
<type class="javax.baja.bacnet.datatypes.BBacnetTimeValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true" final="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true" final="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.nextEvent(javax.baja.sys.BAbsTime) -->
<method name="nextEvent"  public="true" final="true">
<description>
Get the next event defined in this BacnetSpecialEvent,&#xa; starting at the specified &lt;code&gt;BAbsTime&lt;/code&gt;.
</description>
<parameter name="time">
<type class="javax.baja.sys.BAbsTime"/>
<description>
the starting time.
</description>
</parameter>
<return>
<type class="javax.baja.sys.BAbsTime"/>
<description>
the &lt;code&gt;BAbsTime&lt;/code&gt; of the next defined event.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.getValue(javax.baja.sys.BAbsTime) -->
<method name="getValue"  public="true" final="true">
<description>
Get the value at a specified time.
</description>
<parameter name="time">
<type class="javax.baja.sys.BAbsTime"/>
<description>
the &lt;code&gt;BAbsTime&lt;/code&gt; for which the value is needed.
</description>
</parameter>
<return>
<type class="javax.baja.sys.BSimple"/>
<description>
the value at that time, as a BSimple.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.eventName -->
<field name="eventName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;eventName&lt;/code&gt; property.
</description>
<tag name="@see">#getEventName</tag>
<tag name="@see">#setEventName</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.periodChoice -->
<field name="periodChoice"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;periodChoice&lt;/code&gt; property.
</description>
<tag name="@see">#getPeriodChoice</tag>
<tag name="@see">#setPeriodChoice</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.period -->
<field name="period"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;period&lt;/code&gt; property.
</description>
<tag name="@see">#getPeriod</tag>
<tag name="@see">#setPeriod</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.eventPriority -->
<field name="eventPriority"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;eventPriority&lt;/code&gt; property.
</description>
<tag name="@see">#getEventPriority</tag>
<tag name="@see">#setEventPriority</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.addTimeValue -->
<field name="addTimeValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;addTimeValue&lt;/code&gt; action.
</description>
<tag name="@see">#addTimeValue(BBacnetTimeValue parameter)</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.specialEventChanged -->
<field name="specialEventChanged"  public="true" static="true" final="true">
<type class="javax.baja.sys.Topic"/>
<description>
Slot for the &lt;code&gt;specialEventChanged&lt;/code&gt; topic.
</description>
<tag name="@see">#fireSpecialEventChanged</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.CALENDAR_ENTRY_TAG -->
<field name="CALENDAR_ENTRY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.CALENDAR_REFERENCE_TAG -->
<field name="CALENDAR_REFERENCE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.LIST_OF_TIME_VALUES_TAG -->
<field name="LIST_OF_TIME_VALUES_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetSpecialEvent.EVENT_PRIORITY_TAG -->
<field name="EVENT_PRIORITY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
