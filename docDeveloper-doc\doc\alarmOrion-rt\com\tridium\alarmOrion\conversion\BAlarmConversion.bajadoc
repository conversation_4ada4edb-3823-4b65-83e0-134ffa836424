<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarmOrion" runtimeProfile="rt" qualifiedName="com.tridium.alarmOrion.conversion.BAlarmConversion" name="BAlarmConversion" packageName="com.tridium.alarmOrion.conversion" public="true" abstract="true">
<description>
This is an abstract class that creates a common component&#xa; interface for alarm conversion objects.  It handles generating&#xa; the conversion job, as well as reporting the job&#x27;s status.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">December 11, 2009</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="running" flags="rts">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;running&lt;/code&gt; property.
</description>
<tag name="@see">#getRunning</tag>
<tag name="@see">#setRunning</tag>
</property>

<property name="remaining" flags="rts">
<type class="double"/>
<description>
Slot for the &lt;code&gt;remaining&lt;/code&gt; property.
</description>
<tag name="@see">#getRemaining</tag>
<tag name="@see">#setRemaining</tag>
</property>

<property name="succeeded" flags="rts">
<type class="double"/>
<description>
Slot for the &lt;code&gt;succeeded&lt;/code&gt; property.
</description>
<tag name="@see">#getSucceeded</tag>
<tag name="@see">#setSucceeded</tag>
</property>

<property name="failed" flags="rts">
<type class="double"/>
<description>
Slot for the &lt;code&gt;failed&lt;/code&gt; property.
</description>
<tag name="@see">#getFailed</tag>
<tag name="@see">#setFailed</tag>
</property>

<property name="alarmClass" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;alarmClass&lt;/code&gt; property.&#xa; Default alarmClass to route alarm to.&#xa; If blank, use alarm&#x27;s alarmClass.
</description>
<tag name="@see">#getAlarmClass</tag>
<tag name="@see">#setAlarmClass</tag>
</property>

<property name="sourceName" flags="">
<type class="com.tridium.alarm.BTextCustomizer"/>
<description>
Slot for the &lt;code&gt;sourceName&lt;/code&gt; property.
</description>
<tag name="@see">#getSourceName</tag>
<tag name="@see">#setSourceName</tag>
</property>

<property name="changeLastUpdated" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;changeLastUpdated&lt;/code&gt; property.
</description>
<tag name="@see">#getChangeLastUpdated</tag>
<tag name="@see">#setChangeLastUpdated</tag>
</property>

<action name="run" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;run&lt;/code&gt; action.&#xa; Run the job.
</description>
<tag name="@see">#run()</tag>
</action>

<action name="update" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;update&lt;/code&gt; action.
</description>
<tag name="@see">#update()</tag>
</action>

</class>
</bajadoc>
