<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="backup" runtimeProfile="rt" qualifiedName="javax.baja.backup.BBackupService" name="BBackupService" packageName="javax.baja.backup" public="true">
<description>
BBackupService is used to define the files included in a&#xa; configuration backup such as config.bog and supporting static&#xa; files such as px, html, png, and jpegs.  Backups do not include&#xa; runtime data actively being managed by the station such as the&#xa; alarm and history databases.
</description>
<tag name="@author"><PERSON> and <PERSON> on 11 Apr 05</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BAbstractService"/>
</extends>
<implements>
<type class="javax.baja.util.BIRestrictedComponent"/>
</implements>
<property name="excludeFiles" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;excludeFiles&lt;/code&gt; property.&#xa; Semicolon separated list of file patterns to exclude from backups that&#xa; are performed while the station is running.
</description>
<tag name="@see">#getExcludeFiles</tag>
<tag name="@see">#setExcludeFiles</tag>
</property>

<property name="excludeDirectories" flags="">
<type class="javax.baja.naming.BOrdList"/>
<description>
Slot for the &lt;code&gt;excludeDirectories&lt;/code&gt; property.&#xa; List of station relative file ords to exclude from backups that are&#xa; performed while the station is running.
</description>
<tag name="@see">#getExcludeDirectories</tag>
<tag name="@see">#setExcludeDirectories</tag>
</property>

<property name="offlineExcludeFiles" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;offlineExcludeFiles&lt;/code&gt; property.&#xa; Semicolon separated list of file patterns to exclude from backups that&#xa; are performed while the station is stopped.
</description>
<tag name="@see">#getOfflineExcludeFiles</tag>
<tag name="@see">#setOfflineExcludeFiles</tag>
</property>

<property name="offlineExcludeDirectories" flags="">
<type class="javax.baja.naming.BOrdList"/>
<description>
Slot for the &lt;code&gt;offlineExcludeDirectories&lt;/code&gt; property.&#xa; List of station relative file ords to exclude from backups that are&#xa; performed while the station is stopped.
</description>
<tag name="@see">#getOfflineExcludeDirectories</tag>
<tag name="@see">#setOfflineExcludeDirectories</tag>
</property>

<!-- javax.baja.backup.BBackupService() -->
<constructor name="BBackupService" public="true">
<description/>
</constructor>

<!-- javax.baja.backup.BBackupService.getExcludeFiles() -->
<method name="getExcludeFiles"  public="true">
<description>
Get the &lt;code&gt;excludeFiles&lt;/code&gt; property.&#xa; Semicolon separated list of file patterns to exclude from backups that&#xa; are performed while the station is running.
</description>
<tag name="@see">#excludeFiles</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.backup.BBackupService.setExcludeFiles(java.lang.String) -->
<method name="setExcludeFiles"  public="true">
<description>
Set the &lt;code&gt;excludeFiles&lt;/code&gt; property.&#xa; Semicolon separated list of file patterns to exclude from backups that&#xa; are performed while the station is running.
</description>
<tag name="@see">#excludeFiles</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.backup.BBackupService.getExcludeDirectories() -->
<method name="getExcludeDirectories"  public="true">
<description>
Get the &lt;code&gt;excludeDirectories&lt;/code&gt; property.&#xa; List of station relative file ords to exclude from backups that are&#xa; performed while the station is running.
</description>
<tag name="@see">#excludeDirectories</tag>
<return>
<type class="javax.baja.naming.BOrdList"/>
</return>
</method>

<!-- javax.baja.backup.BBackupService.setExcludeDirectories(javax.baja.naming.BOrdList) -->
<method name="setExcludeDirectories"  public="true">
<description>
Set the &lt;code&gt;excludeDirectories&lt;/code&gt; property.&#xa; List of station relative file ords to exclude from backups that are&#xa; performed while the station is running.
</description>
<tag name="@see">#excludeDirectories</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrdList"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.backup.BBackupService.getOfflineExcludeFiles() -->
<method name="getOfflineExcludeFiles"  public="true">
<description>
Get the &lt;code&gt;offlineExcludeFiles&lt;/code&gt; property.&#xa; Semicolon separated list of file patterns to exclude from backups that&#xa; are performed while the station is stopped.
</description>
<tag name="@see">#offlineExcludeFiles</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.backup.BBackupService.setOfflineExcludeFiles(java.lang.String) -->
<method name="setOfflineExcludeFiles"  public="true">
<description>
Set the &lt;code&gt;offlineExcludeFiles&lt;/code&gt; property.&#xa; Semicolon separated list of file patterns to exclude from backups that&#xa; are performed while the station is stopped.
</description>
<tag name="@see">#offlineExcludeFiles</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.backup.BBackupService.getOfflineExcludeDirectories() -->
<method name="getOfflineExcludeDirectories"  public="true">
<description>
Get the &lt;code&gt;offlineExcludeDirectories&lt;/code&gt; property.&#xa; List of station relative file ords to exclude from backups that are&#xa; performed while the station is stopped.
</description>
<tag name="@see">#offlineExcludeDirectories</tag>
<return>
<type class="javax.baja.naming.BOrdList"/>
</return>
</method>

<!-- javax.baja.backup.BBackupService.setOfflineExcludeDirectories(javax.baja.naming.BOrdList) -->
<method name="setOfflineExcludeDirectories"  public="true">
<description>
Set the &lt;code&gt;offlineExcludeDirectories&lt;/code&gt; property.&#xa; List of station relative file ords to exclude from backups that are&#xa; performed while the station is stopped.
</description>
<tag name="@see">#offlineExcludeDirectories</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrdList"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.backup.BBackupService.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.backup.BBackupService.getServiceTypes() -->
<method name="getServiceTypes"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Register this component under &#x22;backup:BackupService&#x22;.
</description>
<return>
<type class="javax.baja.sys.Type" dimension="1"/>
</return>
</method>

<!-- javax.baja.backup.BBackupService.serviceStarted() -->
<method name="serviceStarted"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Service start.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.backup.BBackupService.checkParentForRestrictedComponent(javax.baja.sys.BComponent, javax.baja.sys.Context) -->
<method name="checkParentForRestrictedComponent"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
This service type is only allowed to live under the&#xa; station&#x27;s frozen ServiceContainer, but multiple instances are allowed.&#xa; Only Super Users are allowed to add an instance of this type to the station.
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.backup.BBackupService.listStationBackupFiles() -->
<method name="listStationBackupFiles"  public="true">
<description/>
<return>
<type class="javax.baja.file.BIFile" dimension="1"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.backup.BBackupService.restoreFiles(javax.baja.file.BIFile, javax.baja.sys.Context) -->
<method name="restoreFiles"  public="true">
<description>
Restore the station files using the given backup distribution file.&#xa;&#xa; If the backup distribution file states dependencies that are not met by&#xa; the station running this service, the restore fails.&#xa;&#xa; If this method completes successfully, backupFile will be deleted.&#xa;&#xa; IMPORTANT: the scope of this restore is only the file contents of the&#xa; distribution file.   This method will not install any software, change&#xa; a module content filter, or change TCP/IP settings.&#xa;&#xa; When a context is provided, adminWrite permissions is required and&#xa; this operation will be audited.
</description>
<tag name="@since">Niagara 4.13</tag>
<parameter name="backupFile">
<type class="javax.baja.file.BIFile"/>
<description>
A distribution file produced by the BBackupService.
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.backup.BBackupService.restoreFiles(javax.baja.file.BIFile) -->
<method name="restoreFiles"  public="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Restore the station files using the given backup distribution file.&#xa;&#xa; If the backup distribution file states dependencies that are not met by&#xa; the station running this service, the restore fails.&#xa;&#xa; If this method completes successfully, backupFile will be deleted.&#xa;&#xa; IMPORTANT: the scope of this restore is only the file contents of the&#xa; distribution file.   This method will not install any software, change&#xa; a module content filter, or change TCP/IP settings.
</description>
<tag name="@deprecated">since Niagara 4.13 - will be removed in Niagara 5.0. Use &lt;code&gt;<see ref="javax.baja.backup.BBackupService#restoreFiles(javax.baja.file.BIFile, javax.baja.sys.Context)">#restoreFiles(BIFile, Context)</see>&lt;/code&gt; instead.</tag>
<parameter name="backupFile">
<type class="javax.baja.file.BIFile"/>
<description>
A distribution file produced by the BBackupService.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.backup.BBackupService.restoreFiles(javax.baja.file.BIFile, boolean, boolean, javax.baja.sys.Context) -->
<method name="restoreFiles"  public="true">
<description>
Restore the station files using the given backup distribution file.&#xa;&#xa; IMPORTANT: the scope of this restore is only the file contents of the&#xa; distribution file.   This method will not install any software, change&#xa; a module content filter, or change TCP/IP settings.&#xa;&#xa; When a context is provided, adminWrite permissions is required and&#xa; this operation will be audited.
</description>
<tag name="@since">Niagara 4.13</tag>
<parameter name="backupFile">
<type class="javax.baja.file.BIFile"/>
<description>
A distribution file produced by the BBackupService
</description>
</parameter>
<parameter name="ignoreDependencies">
<type class="boolean"/>
<description>
If true, the dependencies in the backup&#xa;   file will not be verified before starting the file restore
</description>
</parameter>
<parameter name="deleteFile">
<type class="boolean"/>
<description>
If true, backupFile will be deleted after the&#xa;   method completes successfully
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.backup.BBackupService.restoreFiles(javax.baja.file.BIFile, boolean, boolean) -->
<method name="restoreFiles"  public="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Restore the station files using the given backup distribution file.&#xa;&#xa; IMPORTANT: the scope of this restore is only the file contents of the&#xa; distribution file.   This method will not install any software, change&#xa; a module content filter, or change TCP/IP settings.
</description>
<tag name="@deprecated">since Niagara 4.13 - will be removed in Niagara 5.0. Use &lt;code&gt;<see ref="javax.baja.backup.BBackupService#restoreFiles(javax.baja.file.BIFile, boolean, boolean, javax.baja.sys.Context)">#restoreFiles(BIFile, boolean, boolean, Context)</see>&lt;/code&gt; instead.</tag>
<parameter name="backupFile">
<type class="javax.baja.file.BIFile"/>
<description>
A distribution file produced by the BBackupService
</description>
</parameter>
<parameter name="ignoreDependencies">
<type class="boolean"/>
<description>
If true, the dependencies in the backup&#xa;   file will not be verified before starting the file restore
</description>
</parameter>
<parameter name="deleteFile">
<type class="boolean"/>
<description>
If true, backupFile will be deleted after the&#xa;   method completes successfully
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.backup.BBackupService.restoreFiles(javax.baja.file.BIFile, boolean, boolean, long, javax.baja.sys.Context) -->
<method name="restoreFiles"  public="true">
<description>
Restore the station files using the given backup distribution file.&#xa;&#xa; IMPORTANT: the scope of this restore is only the file contents of the&#xa; distribution file.   This method will not install any software, change&#xa; a module content filter, or change TCP/IP settings.&#xa;&#xa;  When a context is provided, adminWrite permissions is required and&#xa;  this operation will be audited.
</description>
<tag name="@since">Niagara 4.13</tag>
<parameter name="backupFile">
<type class="javax.baja.file.BIFile"/>
<description>
A distribution file produced by the BBackupService
</description>
</parameter>
<parameter name="ignoreDependencies">
<type class="boolean"/>
<description>
If true, the dependencies in the backup&#xa;   file will not be verified before starting the file restore
</description>
</parameter>
<parameter name="deleteFile">
<type class="boolean"/>
<description>
If true, backupFile will be deleted after the&#xa;   method completes successfully
</description>
</parameter>
<parameter name="shutdownDelay">
<type class="long"/>
<description>
millis to wait after returning control to the&#xa;   caller before station shutdown should commence
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.backup.BBackupService.restoreFiles(javax.baja.file.BIFile, boolean, boolean, long) -->
<method name="restoreFiles"  public="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Restore the station files using the given backup distribution file.&#xa;&#xa; IMPORTANT: the scope of this restore is only the file contents of the&#xa; distribution file.   This method will not install any software, change&#xa; a module content filter, or change TCP/IP settings.
</description>
<tag name="@deprecated">since Niagara 4.13 - will be removed in Niagara 5.0. Use &lt;code&gt;<see ref="javax.baja.backup.BBackupService#restoreFiles(javax.baja.file.BIFile, boolean, boolean, long, javax.baja.sys.Context)">#restoreFiles(BIFile, boolean, boolean, long, Context)</see>&lt;/code&gt; instead.</tag>
<parameter name="backupFile">
<type class="javax.baja.file.BIFile"/>
<description>
A distribution file produced by the BBackupService
</description>
</parameter>
<parameter name="ignoreDependencies">
<type class="boolean"/>
<description>
If true, the dependencies in the backup&#xa;   file will not be verified before starting the file restore
</description>
</parameter>
<parameter name="deleteFile">
<type class="boolean"/>
<description>
If true, backupFile will be deleted after the&#xa;   method completes successfully
</description>
</parameter>
<parameter name="shutdownDelay">
<type class="long"/>
<description>
millis to wait after returning control to the&#xa;   caller before station shutdown should commence
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.backup.BBackupService.zip(javax.baja.job.BJob, java.io.OutputStream, boolean, javax.baja.sys.Context) -->
<method name="zip"  public="true">
<description>
Given an output stream, write a zip file containing all&#xa; the backup files defined by this BackupService.  If the&#xa; close argument is true then close the output stream when&#xa; done, otherwise just finish it.  If the job argument is&#xa; non-null, then it is used to update progress, logging,&#xa; and to check for cancellation.&#xa;&#xa; When a context is provided, adminWrite permissions is required and&#xa; this operation will be audited.
</description>
<tag name="@since">Niagara 4.13</tag>
<parameter name="job">
<type class="javax.baja.job.BJob"/>
</parameter>
<parameter name="out">
<type class="java.io.OutputStream"/>
</parameter>
<parameter name="close">
<type class="boolean"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.backup.BBackupService.zip(javax.baja.job.BJob, java.io.OutputStream, boolean) -->
<method name="zip"  public="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Given an output stream, write a zip file containing all&#xa; the backup files defined by this BackupService.  If the&#xa; close argument is true then close the output stream when&#xa; done, otherwise just finish it.  If the job argument is&#xa; non-null, then it is used to update progress, logging,&#xa; and to check for cancellation.
</description>
<tag name="@deprecated">since Niagara 4.13 - will be removed in Niagara 5.0. Use &lt;code&gt;<see ref="javax.baja.backup.BBackupService#zip(javax.baja.job.BJob, java.io.OutputStream, boolean, javax.baja.sys.Context)">#zip(BJob, OutputStream, boolean, Context)</see>&lt;/code&gt; instead.</tag>
<parameter name="job">
<type class="javax.baja.job.BJob"/>
</parameter>
<parameter name="out">
<type class="java.io.OutputStream"/>
</parameter>
<parameter name="close">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.backup.BBackupService.zip(javax.baja.job.BJob, javax.baja.platform.PlatformDaemon, java.io.OutputStream, boolean, javax.baja.sys.Context) -->
<method name="zip"  public="true">
<description>
Given an output stream, write a zip file containing all the backup&#xa; files for the given remote host.  If the close argument is true then&#xa; close the output stream when done, otherwise just finish it.  If the&#xa; job argument is non-null, then it is used to update progress, logging,&#xa; and to check for cancelation.&#xa;&#xa; When a context is provided, adminWrite permissions is required and&#xa; this operation will be audited.
</description>
<tag name="@since">Niagara 4.13</tag>
<parameter name="job">
<type class="javax.baja.job.BJob"/>
</parameter>
<parameter name="platformDaemon">
<type class="javax.baja.platform.PlatformDaemon"/>
</parameter>
<parameter name="out">
<type class="java.io.OutputStream"/>
</parameter>
<parameter name="close">
<type class="boolean"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.backup.BBackupService.zip(javax.baja.job.BJob, javax.baja.platform.PlatformDaemon, java.io.OutputStream, boolean) -->
<method name="zip"  public="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Given an output stream, write a zip file containing all the backup&#xa; files for the given remote host.  If the close argument is true then&#xa; close the output stream when done, otherwise just finish it.  If the&#xa; job argument is non-null, then it is used to update progress, logging,&#xa; and to check for cancelation.
</description>
<tag name="@deprecated">since Niagara 4.13 - will be removed in Niagara 5.0. Use &lt;code&gt;<see ref="javax.baja.backup.BBackupService#zip(javax.baja.job.BJob, javax.baja.platform.PlatformDaemon, java.io.OutputStream, boolean, javax.baja.sys.Context)">#zip(BJob, PlatformDaemon, OutputStream, boolean, Context)</see>&lt;/code&gt; instead.</tag>
<parameter name="job">
<type class="javax.baja.job.BJob"/>
</parameter>
<parameter name="platformDaemon">
<type class="javax.baja.platform.PlatformDaemon"/>
</parameter>
<parameter name="out">
<type class="java.io.OutputStream"/>
</parameter>
<parameter name="close">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.backup.BBackupService.zip(javax.baja.job.JobLog, javax.baja.platform.PlatformDaemon, java.io.OutputStream, boolean, javax.baja.sys.Context) -->
<method name="zip"  public="true">
<description>
Given an output stream, write a zip file containing all the backup&#xa; files for the given remote host.  If the close argument is true then&#xa; close the output stream when done, otherwise just finish it.  If the&#xa; log argument is non-null, then it is used for logging.&#xa;&#xa; When a context is provided, adminWrite permissions is required and&#xa; this operation will be audited.
</description>
<tag name="@since">Niagara 4.13</tag>
<parameter name="log">
<type class="javax.baja.job.JobLog"/>
</parameter>
<parameter name="platformDaemon">
<type class="javax.baja.platform.PlatformDaemon"/>
</parameter>
<parameter name="out">
<type class="java.io.OutputStream"/>
</parameter>
<parameter name="close">
<type class="boolean"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.backup.BBackupService.zip(javax.baja.job.JobLog, javax.baja.platform.PlatformDaemon, java.io.OutputStream, boolean) -->
<method name="zip"  public="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Given an output stream, write a zip file containing all the backup&#xa; files for the given remote host.  If the close argument is true then&#xa; close the output stream when done, otherwise just finish it.  If the&#xa; log argument is non-null, then it is used for logging.
</description>
<tag name="@deprecated">since Niagara 4.13 - will be removed in Niagara 5.0. Use &lt;code&gt;<see ref="javax.baja.backup.BBackupService#zip(javax.baja.job.JobLog, javax.baja.platform.PlatformDaemon, java.io.OutputStream, boolean, javax.baja.sys.Context)">#zip(JobLog, PlatformDaemon, OutputStream, boolean, Context)</see>&lt;/code&gt; instead.</tag>
<parameter name="log">
<type class="javax.baja.job.JobLog"/>
</parameter>
<parameter name="platformDaemon">
<type class="javax.baja.platform.PlatformDaemon"/>
</parameter>
<parameter name="out">
<type class="java.io.OutputStream"/>
</parameter>
<parameter name="close">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.backup.BBackupService.zip(javax.baja.job.JobLog, javax.baja.backup.BBackupService.ICanceler, javax.baja.platform.PlatformDaemon, java.io.OutputStream, boolean, javax.baja.sys.Context) -->
<method name="zip"  public="true">
<description>
Given an output stream, write a zip file containing all the backup&#xa; files for the given remote host.  If the close argument is true then&#xa; close the output stream when done, otherwise just finish it.  If the&#xa; log argument is non-null, then it is used for logging.&#xa;&#xa; When a context is provided, adminWrite permissions is required and&#xa; this operation will be audited.
</description>
<tag name="@since">Niagara 4.13</tag>
<parameter name="log">
<type class="javax.baja.job.JobLog"/>
</parameter>
<parameter name="canceler">
<type class="javax.baja.backup.BBackupService$ICanceler"/>
</parameter>
<parameter name="platformDaemon">
<type class="javax.baja.platform.PlatformDaemon"/>
</parameter>
<parameter name="out">
<type class="java.io.OutputStream"/>
</parameter>
<parameter name="close">
<type class="boolean"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.backup.BBackupService.zip(javax.baja.job.JobLog, javax.baja.backup.BBackupService.ICanceler, javax.baja.platform.PlatformDaemon, java.io.OutputStream, boolean) -->
<method name="zip"  public="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Given an output stream, write a zip file containing all the backup&#xa; files for the given remote host.  If the close argument is true then&#xa; close the output stream when done, otherwise just finish it.  If the&#xa; log argument is non-null, then it is used for logging.
</description>
<tag name="@deprecated">since Niagara 4.13 - will be removed in Niagara 5.0. Use &lt;code&gt;<see ref="javax.baja.backup.BBackupService#zip(javax.baja.job.JobLog, javax.baja.backup.BBackupService.ICanceler, javax.baja.platform.PlatformDaemon, java.io.OutputStream, boolean, javax.baja.sys.Context)">#zip(JobLog, ICanceler, PlatformDaemon, OutputStream, boolean, Context)</see>&lt;/code&gt; instead.</tag>
<parameter name="log">
<type class="javax.baja.job.JobLog"/>
</parameter>
<parameter name="canceler">
<type class="javax.baja.backup.BBackupService$ICanceler"/>
</parameter>
<parameter name="platformDaemon">
<type class="javax.baja.platform.PlatformDaemon"/>
</parameter>
<parameter name="out">
<type class="java.io.OutputStream"/>
</parameter>
<parameter name="close">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.backup.BBackupService.makeCanceler(javax.baja.job.BJob) -->
<method name="makeCanceler"  public="true" static="true">
<description/>
<parameter name="job">
<type class="javax.baja.job.BJob"/>
</parameter>
<return>
<type class="javax.baja.backup.BBackupService$ICanceler"/>
</return>
</method>

<!-- javax.baja.backup.BBackupService.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.backup.BBackupService.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.backup.BBackupService.getFoxChannelId() -->
<method name="getFoxChannelId"  protected="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.backup.BBackupService.excludeFiles -->
<field name="excludeFiles"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;excludeFiles&lt;/code&gt; property.&#xa; Semicolon separated list of file patterns to exclude from backups that&#xa; are performed while the station is running.
</description>
<tag name="@see">#getExcludeFiles</tag>
<tag name="@see">#setExcludeFiles</tag>
</field>

<!-- javax.baja.backup.BBackupService.excludeDirectories -->
<field name="excludeDirectories"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;excludeDirectories&lt;/code&gt; property.&#xa; List of station relative file ords to exclude from backups that are&#xa; performed while the station is running.
</description>
<tag name="@see">#getExcludeDirectories</tag>
<tag name="@see">#setExcludeDirectories</tag>
</field>

<!-- javax.baja.backup.BBackupService.offlineExcludeFiles -->
<field name="offlineExcludeFiles"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;offlineExcludeFiles&lt;/code&gt; property.&#xa; Semicolon separated list of file patterns to exclude from backups that&#xa; are performed while the station is stopped.
</description>
<tag name="@see">#getOfflineExcludeFiles</tag>
<tag name="@see">#setOfflineExcludeFiles</tag>
</field>

<!-- javax.baja.backup.BBackupService.offlineExcludeDirectories -->
<field name="offlineExcludeDirectories"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;offlineExcludeDirectories&lt;/code&gt; property.&#xa; List of station relative file ords to exclude from backups that are&#xa; performed while the station is stopped.
</description>
<tag name="@see">#getOfflineExcludeDirectories</tag>
<tag name="@see">#setOfflineExcludeDirectories</tag>
</field>

<!-- javax.baja.backup.BBackupService.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.backup.BBackupService.log -->
<field name="log"  public="true" static="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

<!-- javax.baja.backup.BBackupService.NULL_CANCELER -->
<field name="NULL_CANCELER"  public="true" static="true" final="true">
<type class="javax.baja.backup.BBackupService$ICanceler"/>
<description/>
</field>

</class>
</bajadoc>
