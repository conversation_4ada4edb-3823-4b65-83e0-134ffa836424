<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.rsp.BInfinityAckWithData" name="BInfinityAckWithData" packageName="com.tridium.andoverInfinity.comm.rsp" public="true">
<description>
Used to send bytes to the controller as intermediate steps in multi-step&#xa; request/response sequences.  Used so that we don&#x27;t have to terminate the &#xa; transaction so we can prevent other messages from interrupting.
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.comm.rsp.BDdfResponse"/>
</extends>
<implements>
<type class="com.tridium.ddf.comm.rsp.BIDdfTransmitAckResponse"/>
</implements>
<implements>
<type class="com.tridium.ddf.comm.rsp.BIDdfMultiFrameResponse"/>
</implements>
<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityAckWithData() -->
<constructor name="BInfinityAckWithData" public="true">
<description>
Constructor with String data that is to be sent to the controller
</description>
</constructor>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityAckWithData(java.lang.String) -->
<constructor name="BInfinityAckWithData" public="true">
<parameter name="data">
<type class="java.lang.String"/>
</parameter>
<description>
Constructor with String data that is to be sent to the controller
</description>
</constructor>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityAckWithData.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityAckWithData.setAckData(java.lang.String) -->
<method name="setAckData"  public="true">
<description>
setter for string data
</description>
<parameter name="data">
<type class="java.lang.String"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityAckWithData.getBytes() -->
<method name="getBytes"  public="true">
<description>
Convert the String data to bytes to be transmitted
</description>
<tag name="@see">com.tridium.devDriver.comm.rsp.BIDevTransmitAckResponse#getBytes()</tag>
<return>
<type class="byte" dimension="1"/>
<description>
String data converted to bytes
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityAckWithData.isComplete() -->
<method name="isComplete"  public="true">
<description>
isComplete returns false for this class
</description>
<return>
<type class="boolean"/>
<description>
false
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.rsp.BInfinityAckWithData.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
