<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="aapup" runtimeProfile="rt" name="com.tridium.aapup.datatypes">
<description/>
<class packageName="com.tridium.aapup.datatypes" name="BDownloadRegionParams"/>
<class packageName="com.tridium.aapup.datatypes" name="BPupDeviceDiscoveryConfig"><description>Pup Device Discovery Config, used to narrow a range of&#xa; device address to search for devices</description></class>
<class packageName="com.tridium.aapup.datatypes" name="BPupPeerListRecord"><description>BPupPeerListRecord is a dynamic slot added to the BPupNetwork&#x27;s peer list&#xa; whenever a new device is detected on the network, whether by&#xa; discovery or passively by listening while other devices have the&#xa; token, or by virtue of being configured as a BPupDevice in the&#xa; station configuration.</description></class>
<class packageName="com.tridium.aapup.datatypes" name="BPupPointDiscoveryConfig"><description>Pup Point Discovery Config, used to govern what&#xa; channels and attribs learned during the learn process</description></class>
<class packageName="com.tridium.aapup.datatypes" name="BPupRegionRecord"><description>BPupRegionRecord is a dynamic slot added to the BPupDevice by the learn regions job</description></class>
<class packageName="com.tridium.aapup.datatypes" name="BPupTokenPassConfig"><description>BPupTokenPassConfig is a folder for storing configuration properties&#xa; for token passing scheme.</description></class>
<class packageName="com.tridium.aapup.datatypes" name="BUploadRegionParams"/>
</package>
</bajadoc>
