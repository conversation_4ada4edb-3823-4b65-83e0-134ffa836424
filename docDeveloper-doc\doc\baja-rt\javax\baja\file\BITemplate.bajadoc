<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BITemplate" name="BITemplate" packageName="javax.baja.file" public="true" interface="true" abstract="true" category="interface">
<description>
BITemplate is implemented by template files.
</description>
<tag name="@author"><PERSON> on 23 Jan 2014</tag>
<tag name="@since">Niagara 3.2</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<!-- javax.baja.file.BITemplate.getTemplateName() -->
<method name="getTemplateName"  public="true" default="true">
<description>
Get the name for this BITemplate
</description>
<tag name="@since">Niagara 4.13</tag>
<return>
<type class="java.lang.String"/>
<description>
name
</description>
</return>
</method>

<!-- javax.baja.file.BITemplate.getVendor() -->
<method name="getVendor"  public="true" abstract="true">
<description>
Get the vendor name for this BITemplate
</description>
<return>
<type class="java.lang.String"/>
<description>
vendor name
</description>
</return>
</method>

<!-- javax.baja.file.BITemplate.getVersion() -->
<method name="getVersion"  public="true" abstract="true">
<description>
Get the version name for this BITemplate
</description>
<return>
<type class="java.lang.String"/>
<description>
version
</description>
</return>
</method>

<!-- javax.baja.file.BITemplate.getDescription() -->
<method name="getDescription"  public="true" abstract="true">
<description>
Get the description string this BITemplate
</description>
<return>
<type class="java.lang.String"/>
<description>
version
</description>
</return>
</method>

<!-- javax.baja.file.BITemplate.getUID() -->
<method name="getUID"  public="true" abstract="true">
<description>
Get the uID string this BITemplate
</description>
<return>
<type class="javax.baja.util.BUuid"/>
<description>
uID
</description>
</return>
</method>

<!-- javax.baja.file.BITemplate.getBaseComponent() -->
<method name="getBaseComponent"  public="true" abstract="true">
<description>
Get BObject tree encapsulate in this template file;
</description>
<return>
<type class="javax.baja.sys.BComponent"/>
<description>
the root BComponent
</description>
</return>
<throws>
<type class="javax.baja.naming.UnresolvedException"/>
<description/>
</throws>
</method>

<!-- javax.baja.file.BITemplate.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
