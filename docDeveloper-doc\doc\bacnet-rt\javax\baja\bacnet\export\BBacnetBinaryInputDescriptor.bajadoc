<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetBinaryInputDescriptor" name="BBacnetBinaryInputDescriptor" packageName="javax.baja.bacnet.export" public="true">
<description>
BBacnetBinaryInputDescriptor exposes a ControlPoint as a Bacnet&#xa; Binary Input Object.
</description>
<tag name="@author"><PERSON> on 07 Aug 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.export.BBacnetBinaryPointDescriptor"/>
</extends>
<property name="deviceType" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;deviceType&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceType</tag>
<tag name="@see">#setDeviceType</tag>
</property>

<!-- javax.baja.bacnet.export.BBacnetBinaryInputDescriptor() -->
<constructor name="BBacnetBinaryInputDescriptor" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetBinaryInputDescriptor.getDeviceType() -->
<method name="getDeviceType"  public="true">
<description>
Get the &lt;code&gt;deviceType&lt;/code&gt; property.
</description>
<tag name="@see">#deviceType</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryInputDescriptor.setDeviceType(java.lang.String) -->
<method name="setDeviceType"  public="true">
<description>
Set the &lt;code&gt;deviceType&lt;/code&gt; property.
</description>
<tag name="@see">#deviceType</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryInputDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryInputDescriptor.getBacnetValue() -->
<method name="getBacnetValue"  public="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description/>
<tag name="@deprecated">BacnetValue is no longer necessary since out-of-service&#xa; changes will be written directly to the point via&#xa; the BOutOfServiceExt.</tag>
<return>
<type class="javax.baja.status.BStatusBoolean"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryInputDescriptor.setBacnetValue(javax.baja.status.BStatusBoolean) -->
<method name="setBacnetValue"  public="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description/>
<tag name="@deprecated">BacnetValue is no longer necessary since out-of-service&#xa; changes will be written directly to the point via&#xa; the BOutOfServiceExt.</tag>
<parameter name="v">
<type class="javax.baja.status.BStatusBoolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryInputDescriptor.getSlotFacets(javax.baja.sys.Slot) -->
<method name="getSlotFacets"  public="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get slot facets.
</description>
<parameter name="s">
<type class="javax.baja.sys.Slot"/>
<description>
slot
</description>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
<description>
the appropriate slot facets.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryInputDescriptor.getEventType() -->
<method name="getEventType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the BACnetEventType reported by this object.
</description>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryInputDescriptor.isValidAlarmExt(javax.baja.alarm.BIAlarmSource) -->
<method name="isValidAlarmExt"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is the given alarm source ext a valid extension for&#xa; exporting BACnet alarm properties?  This determines if the&#xa; given alarm source extension follows the appropriate algorithm&#xa; defined for the intrinsic alarming of a particular object&#xa; type as required by the BACnet specification.&lt;p&gt;&#xa; BACnet BinaryInput points use a ChangeOfState alarm algorithm.
</description>
<parameter name="ext">
<type class="javax.baja.alarm.BIAlarmSource"/>
<description/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if valid, otherwise false.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryInputDescriptor.readProperty(int, int) -->
<method name="readProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryInputDescriptor.writeProperty(int, int, byte[], int) -->
<method name="writeProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryInputDescriptor.addRequiredProps(java.util.Vector) -->
<method name="addRequiredProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Subclass override method to add required properties.&#xa; NOTE: You MUST call super.addRequiredProps(v) first!
</description>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
Vector containing required propertyIds.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryInputDescriptor.addOptionalProps(java.util.Vector) -->
<method name="addOptionalProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Subclass override method to add optional properties.&#xa; NOTE: You MUST call super.addOptionalProps(v) first!
</description>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
Vector containing optional propertyIds.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryInputDescriptor.readOptionalProperty(int, int) -->
<method name="readOptionalProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Read the value of an optional property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryInputDescriptor.writeOptionalProperty(int, int, byte[], int) -->
<method name="writeOptionalProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the value of an optional property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetBinaryInputDescriptor.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.&#xa; objectId is the identifier by which this point is known&#xa; to the Bacnet world.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetBinaryInputDescriptor.deviceType -->
<field name="deviceType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;deviceType&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceType</tag>
<tag name="@see">#setDeviceType</tag>
</field>

<!-- javax.baja.bacnet.export.BBacnetBinaryInputDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
