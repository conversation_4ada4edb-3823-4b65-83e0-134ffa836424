<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.selector.BJsonAtArrayIndex" name="BJsonAtArrayIndex" packageName="com.tridiumx.jsonToolkit.inbound.selector" public="true">
<description>
Allows selection of Json array values by index.&#xa; This does not support Json objects which by definition have no order guarantee.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.inbound.selector.BJsonStringSelector"/>
</extends>
<property name="index" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;index&lt;/code&gt; property.
</description>
<tag name="@see">#getIndex</tag>
<tag name="@see">#setIndex</tag>
</property>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonAtArrayIndex() -->
<constructor name="BJsonAtArrayIndex" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonAtArrayIndex.getIndex() -->
<method name="getIndex"  public="true">
<description>
Get the &lt;code&gt;index&lt;/code&gt; property.
</description>
<tag name="@see">#index</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonAtArrayIndex.setIndex(int) -->
<method name="setIndex"  public="true">
<description>
Set the &lt;code&gt;index&lt;/code&gt; property.
</description>
<tag name="@see">#index</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonAtArrayIndex.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonAtArrayIndex.routeValue(javax.baja.sys.BString, javax.baja.sys.Context) -->
<method name="routeValue"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="message">
<type class="javax.baja.sys.BString"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonAtArrayIndex.getRerunTriggers() -->
<method name="getRerunTriggers"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Property" dimension="1"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonAtArrayIndex.index -->
<field name="index"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;index&lt;/code&gt; property.
</description>
<tag name="@see">#getIndex</tag>
<tag name="@see">#setIndex</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonAtArrayIndex.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
