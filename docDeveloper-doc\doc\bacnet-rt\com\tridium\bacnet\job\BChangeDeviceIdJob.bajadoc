<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.job.BChangeDeviceIdJob" name="BChangeDeviceIdJob" packageName="com.tridium.bacnet.job" public="true">
<description/>
<extends>
<type class="com.tridium.bacnet.job.BDeviceManagerJob"/>
</extends>
<implements>
<type class="com.tridium.bacnet.stack.IAmListener"/>
</implements>
<property name="config" flags="">
<type class="com.tridium.bacnet.datatypes.BChangeDeviceIdConfig"/>
<description>
Slot for the &lt;code&gt;config&lt;/code&gt; property.
</description>
<tag name="@see">#getConfig</tag>
<tag name="@see">#setConfig</tag>
</property>

</class>
</bajadoc>
