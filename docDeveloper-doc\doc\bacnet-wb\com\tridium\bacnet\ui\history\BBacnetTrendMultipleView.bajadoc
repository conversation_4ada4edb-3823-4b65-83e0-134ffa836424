<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="wb" qualifiedName="com.tridium.bacnet.ui.history.BBacnetTrendMultipleView" name="BBacnetTrendMultipleView" packageName="com.tridium.bacnet.ui.history" public="true">
<description>
BBacnetTrendMultipleView provides a table view of all histories&#xa; associated with a single Bacnet TrendLogMultiple object.
</description>
<tag name="@author"><PERSON>  original code from BHistoryTable</tag>
<tag name="@creation">15 April 2010</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.workbench.view.BWbView"/>
</extends>
<property name="showTimeRangeEditor" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;showTimeRangeEditor&lt;/code&gt; property.&#xa; Indicates whether the time range editor should be displayed.
</description>
<tag name="@see">#getShowTimeRangeEditor</tag>
<tag name="@see">#setShowTimeRangeEditor</tag>
</property>

<property name="defaultTimeRange" flags="">
<type class="com.tridium.bql.util.BDynamicTimeRange"/>
<description>
Slot for the &lt;code&gt;defaultTimeRange&lt;/code&gt; property.
</description>
<tag name="@see">#getDefaultTimeRange</tag>
<tag name="@see">#setDefaultTimeRange</tag>
</property>

<property name="showDeltaEditor" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;showDeltaEditor&lt;/code&gt; property.&#xa; Indicates whether the delta editor should be displayed.
</description>
<tag name="@see">#getShowDeltaEditor</tag>
<tag name="@see">#setShowDeltaEditor</tag>
</property>

<property name="defaultDelta" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;defaultDelta&lt;/code&gt; property.&#xa; The delta state is True for delta logging, false for non-delta.
</description>
<tag name="@see">#getDefaultDelta</tag>
<tag name="@see">#setDefaultDelta</tag>
</property>

<property name="showLiveUpdates" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;showLiveUpdates&lt;/code&gt; property.&#xa; Indicates whether the Live Updates should be displayed.
</description>
<tag name="@since">Niagara 3.4</tag>
<tag name="@see">#getShowLiveUpdates</tag>
<tag name="@see">#setShowLiveUpdates</tag>
</property>

<property name="defaultLiveUpdates" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;defaultLiveUpdates&lt;/code&gt; property.&#xa; The Live Updates state is true for enabling&#xa; live updates, false for no live updates (displays&#xa; a static table).
</description>
<tag name="@since">Niagara 3.4</tag>
<tag name="@see">#getDefaultLiveUpdates</tag>
<tag name="@see">#setDefaultLiveUpdates</tag>
</property>

<action name="query" flags="a">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;query&lt;/code&gt; action.
</description>
<tag name="@see">#query()</tag>
</action>

</class>
</bajadoc>
