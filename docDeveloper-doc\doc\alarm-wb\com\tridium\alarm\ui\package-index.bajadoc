<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="alarm" runtimeProfile="wb" name="com.tridium.alarm.ui">
<description>
&lt;p&gt;&#xa;This package provides the enhanced functionality for user interface of alarms within the Baja framework.&#xa;&lt;/p&gt;
</description>
<class packageName="com.tridium.alarm.ui" name="BAlarmClassDef"/>
<class packageName="com.tridium.alarm.ui" name="BAlarmClassFE"><description>Plugin for BString when used as an alarm class identifier.</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmClassMapping"/>
<class packageName="com.tridium.alarm.ui" name="BAlarmClassMappingFE"/>
<class packageName="com.tridium.alarm.ui" name="BAlarmClassSummary"><description>BAlarmClassManager is a plugin for managing all alarm classes&#xa; in a station.</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmConsole"><description>BAlarmConsole provides a graphical interface for displaying, auditing,&#xa; silencing and acknowledging alarms of one or more remote Baja stations.</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmConsoleOptions"><description>BAlarmConsoleOptions wraps all configuration&#xa; for an alarm console.</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmConsoleToPdf"><description>alarm:AlarmConsole -&gt; file:PdfFile</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmConsoleUIChannel"><description>BAlarmConsoleUIChannel is used by BAlarmConsole to handle messages between&#xa; the BConsoleRecipient and BAlarmConsole.</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmDataCols"/>
<class packageName="com.tridium.alarm.ui" name="BAlarmDbMaintenance"><description>BAlarmDbMaintenance provides a view of all AlarmRecords in the db&#xa; as well as a way to add notes to records no longer appearing in &#xa; the AlarmConsole and a way clear old records from the db.</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmDbView"/>
<class packageName="com.tridium.alarm.ui" name="BAlarmDetailsDialog"/>
<class packageName="com.tridium.alarm.ui" name="BAlarmDialog"><description>BAlarmDialog</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmExtManager"><description>BAlarmExtManager is a plugin for managing all alarm extensions&#xa; in a station.</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmInstructionsManager"/>
<class packageName="com.tridium.alarm.ui" name="BAlarmPrioritiesFE"><description>Plugin for BAlarmPriorities.</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmReportDialog"/>
<class packageName="com.tridium.alarm.ui" name="BAlarmSpaceMenuAgent"><description>BAlarmSpaceMenuAgent</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmTitlePane"><description>BAlarmTilePane</description></class>
<class packageName="com.tridium.alarm.ui" name="BAlarmTransitionBitsFE"><description>Plugin for BAlarmTransitionBits.</description></class>
<class packageName="com.tridium.alarm.ui" name="BDeviceExtAlarmClassFE"/>
<class packageName="com.tridium.alarm.ui" name="BEnumAlarmRangeFE"/>
<class packageName="com.tridium.alarm.ui" name="BInstructionsFE"/>
<class packageName="com.tridium.alarm.ui" name="BLimitEnableFE"><description>BLimitEnableFE</description></class>
<class packageName="com.tridium.alarm.ui" name="BNotesDialog"/>
<class packageName="com.tridium.alarm.ui" name="BRemoteStationFE"><description>Plugin for RemoteStation String Field.</description></class>
<class packageName="com.tridium.alarm.ui" name="BStringListFE"/>
<class packageName="com.tridium.alarm.ui" name="BTextCustomizerFE"/>
<class packageName="com.tridium.alarm.ui" name="BTimeZoneDisplay"/>
<class packageName="com.tridium.alarm.ui" name="BTrayIcon"><description>BTrayIcon</description></class>
<class packageName="com.tridium.alarm.ui" name="BIAlarmServiceView" category="interface"/>
</package>
</bajadoc>
