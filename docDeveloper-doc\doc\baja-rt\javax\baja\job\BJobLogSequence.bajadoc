<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.job.BJobLogSequence" name="BJobLogSequence" packageName="javax.baja.job" public="true" final="true">
<description>
Struct encapsulating a number of encoded JobLogItems and associated sequence numbers&#xa; for the first and last log items in the contained sequence of items. The sequence numbers&#xa; are used to identify the overall position of the records in a JobLog that may have its size&#xa; limited and have removed old items. Values of -1 are used for the first and last sequence numbers&#xa; to indicate an empty sequence, such as one requested from an empty log, or a request for a&#xa; sequence beyond the end of the log. The initial sequence number of the current JobLog instance&#xa; is also provided as a property.
</description>
<tag name="@see">javax.baja.job.BJob#readLogFrom(BLong)</tag>
<tag name="@see">javax.baja.job.JobLog</tag>
<tag name="@see">javax.baja.job.JobLogItem</tag>
<tag name="@since">Niagara 4.3</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="initialSequenceNumber" flags="r">
<type class="long"/>
<description>
Slot for the &lt;code&gt;initialSequenceNumber&lt;/code&gt; property.&#xa; Property indicating the initial sequence number of the current JobLog. For a new job, this&#xa; will be 0. For jobs that have reset their logs, this will be the first sequence number&#xa; after the log was reset. A client user interface could use a change in the value of&#xa; this property to update itself accordingly.
</description>
<tag name="@see">#getInitialSequenceNumber</tag>
<tag name="@see">#setInitialSequenceNumber</tag>
</property>

<property name="firstSequenceNumber" flags="r">
<type class="long"/>
<description>
Slot for the &lt;code&gt;firstSequenceNumber&lt;/code&gt; property.&#xa; Property indicating the zero based sequence number of the first log item&#xa; in the encoded items, or -1 if the sequence is empty.
</description>
<tag name="@see">#getFirstSequenceNumber</tag>
<tag name="@see">#setFirstSequenceNumber</tag>
</property>

<property name="lastSequenceNumber" flags="r">
<type class="long"/>
<description>
Slot for the &lt;code&gt;lastSequenceNumber&lt;/code&gt; property.&#xa; Property indicating the zero based sequence number of the last log item&#xa; in the encoded items, or -1 if the sequence is empty.
</description>
<tag name="@see">#getLastSequenceNumber</tag>
<tag name="@see">#setLastSequenceNumber</tag>
</property>

<property name="encodedItems" flags="rh">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;encodedItems&lt;/code&gt; property.&#xa; A string property containing zero or more encoded log items. Client code&#xa; is not expected to access this property directly, rather it should call&#xa; the getLogItems() method, which will decode the string into an array of&#xa; JobLogItem instances.
</description>
<tag name="@see">#getEncodedItems</tag>
<tag name="@see">#setEncodedItems</tag>
</property>

<!-- javax.baja.job.BJobLogSequence() -->
<constructor name="BJobLogSequence" public="true">
<description/>
</constructor>

<!-- javax.baja.job.BJobLogSequence.getInitialSequenceNumber() -->
<method name="getInitialSequenceNumber"  public="true">
<description>
Get the &lt;code&gt;initialSequenceNumber&lt;/code&gt; property.&#xa; Property indicating the initial sequence number of the current JobLog. For a new job, this&#xa; will be 0. For jobs that have reset their logs, this will be the first sequence number&#xa; after the log was reset. A client user interface could use a change in the value of&#xa; this property to update itself accordingly.
</description>
<tag name="@see">#initialSequenceNumber</tag>
<return>
<type class="long"/>
</return>
</method>

<!-- javax.baja.job.BJobLogSequence.setInitialSequenceNumber(long) -->
<method name="setInitialSequenceNumber"  public="true">
<description>
Set the &lt;code&gt;initialSequenceNumber&lt;/code&gt; property.&#xa; Property indicating the initial sequence number of the current JobLog. For a new job, this&#xa; will be 0. For jobs that have reset their logs, this will be the first sequence number&#xa; after the log was reset. A client user interface could use a change in the value of&#xa; this property to update itself accordingly.
</description>
<tag name="@see">#initialSequenceNumber</tag>
<parameter name="v">
<type class="long"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.job.BJobLogSequence.getFirstSequenceNumber() -->
<method name="getFirstSequenceNumber"  public="true">
<description>
Get the &lt;code&gt;firstSequenceNumber&lt;/code&gt; property.&#xa; Property indicating the zero based sequence number of the first log item&#xa; in the encoded items, or -1 if the sequence is empty.
</description>
<tag name="@see">#firstSequenceNumber</tag>
<return>
<type class="long"/>
</return>
</method>

<!-- javax.baja.job.BJobLogSequence.setFirstSequenceNumber(long) -->
<method name="setFirstSequenceNumber"  public="true">
<description>
Set the &lt;code&gt;firstSequenceNumber&lt;/code&gt; property.&#xa; Property indicating the zero based sequence number of the first log item&#xa; in the encoded items, or -1 if the sequence is empty.
</description>
<tag name="@see">#firstSequenceNumber</tag>
<parameter name="v">
<type class="long"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.job.BJobLogSequence.getLastSequenceNumber() -->
<method name="getLastSequenceNumber"  public="true">
<description>
Get the &lt;code&gt;lastSequenceNumber&lt;/code&gt; property.&#xa; Property indicating the zero based sequence number of the last log item&#xa; in the encoded items, or -1 if the sequence is empty.
</description>
<tag name="@see">#lastSequenceNumber</tag>
<return>
<type class="long"/>
</return>
</method>

<!-- javax.baja.job.BJobLogSequence.setLastSequenceNumber(long) -->
<method name="setLastSequenceNumber"  public="true">
<description>
Set the &lt;code&gt;lastSequenceNumber&lt;/code&gt; property.&#xa; Property indicating the zero based sequence number of the last log item&#xa; in the encoded items, or -1 if the sequence is empty.
</description>
<tag name="@see">#lastSequenceNumber</tag>
<parameter name="v">
<type class="long"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.job.BJobLogSequence.getEncodedItems() -->
<method name="getEncodedItems"  public="true">
<description>
Get the &lt;code&gt;encodedItems&lt;/code&gt; property.&#xa; A string property containing zero or more encoded log items. Client code&#xa; is not expected to access this property directly, rather it should call&#xa; the getLogItems() method, which will decode the string into an array of&#xa; JobLogItem instances.
</description>
<tag name="@see">#encodedItems</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.job.BJobLogSequence.setEncodedItems(java.lang.String) -->
<method name="setEncodedItems"  public="true">
<description>
Set the &lt;code&gt;encodedItems&lt;/code&gt; property.&#xa; A string property containing zero or more encoded log items. Client code&#xa; is not expected to access this property directly, rather it should call&#xa; the getLogItems() method, which will decode the string into an array of&#xa; JobLogItem instances.
</description>
<tag name="@see">#encodedItems</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.job.BJobLogSequence.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.job.BJobLogSequence.getLogItems() -->
<method name="getLogItems"  public="true">
<description>
Decode the contents of the encodedItems property and return an&#xa; array of JobLogItems.
</description>
<return>
<type class="javax.baja.job.JobLogItem" dimension="1"/>
<description>
an array of zero or more JobLogItem instances
</description>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.job.BJobLogSequence.initialSequenceNumber -->
<field name="initialSequenceNumber"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;initialSequenceNumber&lt;/code&gt; property.&#xa; Property indicating the initial sequence number of the current JobLog. For a new job, this&#xa; will be 0. For jobs that have reset their logs, this will be the first sequence number&#xa; after the log was reset. A client user interface could use a change in the value of&#xa; this property to update itself accordingly.
</description>
<tag name="@see">#getInitialSequenceNumber</tag>
<tag name="@see">#setInitialSequenceNumber</tag>
</field>

<!-- javax.baja.job.BJobLogSequence.firstSequenceNumber -->
<field name="firstSequenceNumber"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;firstSequenceNumber&lt;/code&gt; property.&#xa; Property indicating the zero based sequence number of the first log item&#xa; in the encoded items, or -1 if the sequence is empty.
</description>
<tag name="@see">#getFirstSequenceNumber</tag>
<tag name="@see">#setFirstSequenceNumber</tag>
</field>

<!-- javax.baja.job.BJobLogSequence.lastSequenceNumber -->
<field name="lastSequenceNumber"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lastSequenceNumber&lt;/code&gt; property.&#xa; Property indicating the zero based sequence number of the last log item&#xa; in the encoded items, or -1 if the sequence is empty.
</description>
<tag name="@see">#getLastSequenceNumber</tag>
<tag name="@see">#setLastSequenceNumber</tag>
</field>

<!-- javax.baja.job.BJobLogSequence.encodedItems -->
<field name="encodedItems"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;encodedItems&lt;/code&gt; property.&#xa; A string property containing zero or more encoded log items. Client code&#xa; is not expected to access this property directly, rather it should call&#xa; the getLogItems() method, which will decode the string into an array of&#xa; JobLogItem instances.
</description>
<tag name="@see">#getEncodedItems</tag>
<tag name="@see">#setEncodedItems</tag>
</field>

<!-- javax.baja.job.BJobLogSequence.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
