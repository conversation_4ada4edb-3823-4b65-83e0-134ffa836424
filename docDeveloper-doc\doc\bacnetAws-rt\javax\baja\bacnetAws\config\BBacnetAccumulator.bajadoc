<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="javax.baja.bacnetAws.config.BBacnetAccumulator" name="BBacnetAccumulator" packageName="javax.baja.bacnetAws.config" public="true">
<description/>
<extends>
<type class="javax.baja.bacnet.BBacnetObject"/>
</extends>
<property name="presentValue" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</property>

<property name="facets" flags="r">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.&#xa; They are determined from the Min_Pres_Value, Max_Pres_Value, and Units&#xa; properties (if present).
</description>
<tag name="@see">#getFacets</tag>
<tag name="@see">#setFacets</tag>
</property>

<property name="statusFlags" flags="r">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
Slot for the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#getStatusFlags</tag>
<tag name="@see">#setStatusFlags</tag>
</property>

<property name="eventState" flags="r">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getEventState</tag>
<tag name="@see">#setEventState</tag>
</property>

<property name="outOfService" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#getOutOfService</tag>
<tag name="@see">#setOutOfService</tag>
</property>

<property name="scale" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetScale"/>
<description>
Slot for the &lt;code&gt;scale&lt;/code&gt; property.&#xa; the conversion factor to be multiplied with Present_Value to provide&#xa; a value with units indicated by the Units property.
</description>
<tag name="@see">#getScale</tag>
<tag name="@see">#setScale</tag>
</property>

<property name="units" flags="">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;units&lt;/code&gt; property.
</description>
<tag name="@see">#getUnits</tag>
<tag name="@see">#setUnits</tag>
</property>

<property name="maxPresValue" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;maxPresValue&lt;/code&gt; property.
</description>
<tag name="@see">#getMaxPresValue</tag>
<tag name="@see">#setMaxPresValue</tag>
</property>

<property name="loggingRecord" flags="">
<type class="javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord"/>
<description>
Slot for the &lt;code&gt;loggingRecord&lt;/code&gt; property.
</description>
<tag name="@see">#getLoggingRecord</tag>
<tag name="@see">#setLoggingRecord</tag>
</property>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator() -->
<constructor name="BBacnetAccumulator" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.getPresentValue() -->
<method name="getPresentValue"  public="true">
<description>
Get the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#presentValue</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.setPresentValue(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setPresentValue"  public="true">
<description>
Set the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#presentValue</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.getFacets() -->
<method name="getFacets"  public="true">
<description>
Get the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.&#xa; They are determined from the Min_Pres_Value, Max_Pres_Value, and Units&#xa; properties (if present).
</description>
<tag name="@see">#facets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.setFacets(javax.baja.sys.BFacets) -->
<method name="setFacets"  public="true">
<description>
Set the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.&#xa; They are determined from the Min_Pres_Value, Max_Pres_Value, and Units&#xa; properties (if present).
</description>
<tag name="@see">#facets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.getStatusFlags() -->
<method name="getStatusFlags"  public="true">
<description>
Get the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#statusFlags</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.setStatusFlags(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="setStatusFlags"  public="true">
<description>
Set the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#statusFlags</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.getEventState() -->
<method name="getEventState"  public="true">
<description>
Get the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#eventState</tag>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.setEventState(javax.baja.sys.BEnum) -->
<method name="setEventState"  public="true">
<description>
Set the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#eventState</tag>
<parameter name="v">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.getOutOfService() -->
<method name="getOutOfService"  public="true">
<description>
Get the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#outOfService</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.setOutOfService(boolean) -->
<method name="setOutOfService"  public="true">
<description>
Set the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#outOfService</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.getScale() -->
<method name="getScale"  public="true">
<description>
Get the &lt;code&gt;scale&lt;/code&gt; property.&#xa; the conversion factor to be multiplied with Present_Value to provide&#xa; a value with units indicated by the Units property.
</description>
<tag name="@see">#scale</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetScale"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.setScale(javax.baja.bacnet.datatypes.BBacnetScale) -->
<method name="setScale"  public="true">
<description>
Set the &lt;code&gt;scale&lt;/code&gt; property.&#xa; the conversion factor to be multiplied with Present_Value to provide&#xa; a value with units indicated by the Units property.
</description>
<tag name="@see">#scale</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetScale"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.getUnits() -->
<method name="getUnits"  public="true">
<description>
Get the &lt;code&gt;units&lt;/code&gt; property.
</description>
<tag name="@see">#units</tag>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.setUnits(javax.baja.sys.BEnum) -->
<method name="setUnits"  public="true">
<description>
Set the &lt;code&gt;units&lt;/code&gt; property.
</description>
<tag name="@see">#units</tag>
<parameter name="v">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.getMaxPresValue() -->
<method name="getMaxPresValue"  public="true">
<description>
Get the &lt;code&gt;maxPresValue&lt;/code&gt; property.
</description>
<tag name="@see">#maxPresValue</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.setMaxPresValue(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setMaxPresValue"  public="true">
<description>
Set the &lt;code&gt;maxPresValue&lt;/code&gt; property.
</description>
<tag name="@see">#maxPresValue</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.getLoggingRecord() -->
<method name="getLoggingRecord"  public="true">
<description>
Get the &lt;code&gt;loggingRecord&lt;/code&gt; property.
</description>
<tag name="@see">#loggingRecord</tag>
<return>
<type class="javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.setLoggingRecord(javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord) -->
<method name="setLoggingRecord"  public="true">
<description>
Set the &lt;code&gt;loggingRecord&lt;/code&gt; property.
</description>
<tag name="@see">#loggingRecord</tag>
<parameter name="v">
<type class="javax.baja.bacnetAws.datatypes.BBacnetAccumulatorRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.presentValue -->
<field name="presentValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;presentValue&lt;/code&gt; property.
</description>
<tag name="@see">#getPresentValue</tag>
<tag name="@see">#setPresentValue</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.facets -->
<field name="facets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;facets&lt;/code&gt; property.&#xa; These facets are applied against the presentValue property.&#xa; They are determined from the Min_Pres_Value, Max_Pres_Value, and Units&#xa; properties (if present).
</description>
<tag name="@see">#getFacets</tag>
<tag name="@see">#setFacets</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.statusFlags -->
<field name="statusFlags"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#getStatusFlags</tag>
<tag name="@see">#setStatusFlags</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.eventState -->
<field name="eventState"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getEventState</tag>
<tag name="@see">#setEventState</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.outOfService -->
<field name="outOfService"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;outOfService&lt;/code&gt; property.&#xa; is the physical point represented by this object out of service?&#xa; if TRUE, then this point&#x27;s Present_Value does NOT reflect the actual state&#xa; of the point.
</description>
<tag name="@see">#getOutOfService</tag>
<tag name="@see">#setOutOfService</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.scale -->
<field name="scale"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;scale&lt;/code&gt; property.&#xa; the conversion factor to be multiplied with Present_Value to provide&#xa; a value with units indicated by the Units property.
</description>
<tag name="@see">#getScale</tag>
<tag name="@see">#setScale</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.units -->
<field name="units"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;units&lt;/code&gt; property.
</description>
<tag name="@see">#getUnits</tag>
<tag name="@see">#setUnits</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.maxPresValue -->
<field name="maxPresValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;maxPresValue&lt;/code&gt; property.
</description>
<tag name="@see">#getMaxPresValue</tag>
<tag name="@see">#setMaxPresValue</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.loggingRecord -->
<field name="loggingRecord"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;loggingRecord&lt;/code&gt; property.
</description>
<tag name="@see">#getLoggingRecord</tag>
<tag name="@see">#setLoggingRecord</tag>
</field>

<!-- javax.baja.bacnetAws.config.BBacnetAccumulator.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
