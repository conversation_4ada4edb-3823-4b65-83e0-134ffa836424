<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.data.DataUtil" name="DataUtil" packageName="javax.baja.data" public="true">
<description>
DataUtil provides utility methods for the Data API.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">21 Feb 03</tag>
<tag name="@version">$Revision: 11$ $Date: 8/16/04 8:35:03 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- javax.baja.data.DataUtil() -->
<constructor name="DataUtil" public="true">
<description/>
</constructor>

<!-- javax.baja.data.DataUtil.marshal(javax.baja.data.BIDataValue) -->
<method name="marshal"  public="true" static="true">
<description>
This a standard utility to encode a data value into&#xa; into its &#x22;symbol:value&#x22; format as specified by &#xa; the value&#x27;s BNF production.  The exception to the&#xa; rule is that if the value is a BString we escape&#xa; it using SlotPath.escape.  If value is null then&#xa; return &#x22;null&#x22;.
</description>
<parameter name="value">
<type class="javax.baja.data.BIDataValue"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.data.DataUtil.marshal(javax.baja.sys.BObject) -->
<method name="marshal"  public="true" static="true">
<description>
This a standard utility to encode a data value into&#xa; into its &#x22;symbol:value&#x22; format as specified by &#xa; the value&#x27;s BNF production.  The exceptions to the&#xa; rule is that if the value is a BString or BOrd we escape&#xa; it using SlotPath.escape.  If value is null then&#xa; return &#x22;null&#x22;.
</description>
<parameter name="value">
<type class="javax.baja.sys.BObject"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.data.DataUtil.unmarshal(java.lang.String) -->
<method name="unmarshal"  public="true" static="true">
<description>
This a standard utility to decode a data value&#xa; from its &#x22;symbol:value&#x22; format.  The exception&#xa; to the rule is that &#x27;s&#x27; / BString and BOrd are unescaped&#xa; using SlotPath.unescape.  If the string is &#x22;null&#x22;&#xa; then return null.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

</class>
</bajadoc>
