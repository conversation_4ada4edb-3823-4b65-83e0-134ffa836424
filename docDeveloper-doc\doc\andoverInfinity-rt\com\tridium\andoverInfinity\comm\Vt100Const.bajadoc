<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.Vt100Const" name="Vt100Const" packageName="com.tridium.andoverInfinity.comm" public="true" interface="true" abstract="true" category="interface">
<description>
Constants for use in the Infinity driver
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<!-- com.tridium.andoverInfinity.comm.Vt100Const.RPROMPT -->
<field name="RPROMPT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.OK -->
<field name="OK"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CANCEL -->
<field name="CANCEL"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.PORT -->
<field name="PORT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.YES -->
<field name="YES"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.NO -->
<field name="NO"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.VIEW -->
<field name="VIEW"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.EDIT -->
<field name="EDIT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.FILE -->
<field name="FILE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.SEARCH -->
<field name="SEARCH"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.TOOLS -->
<field name="TOOLS"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.NORMAL_TEXT -->
<field name="NORMAL_TEXT"  public="true" static="true" final="true">
<type class="char"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.BOLD_TEXT -->
<field name="BOLD_TEXT"  public="true" static="true" final="true">
<type class="char"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.REVERSE_TEXT -->
<field name="REVERSE_TEXT"  public="true" static="true" final="true">
<type class="char"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.HORIZONTAL_LINE -->
<field name="HORIZONTAL_LINE"  public="true" static="true" final="true">
<type class="char"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.VERTICAL_LINE -->
<field name="VERTICAL_LINE"  public="true" static="true" final="true">
<type class="char"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC -->
<field name="ESC"  public="true" static="true" final="true">
<type class="char"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.SO -->
<field name="SO"  public="true" static="true" final="true">
<type class="char"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.SI -->
<field name="SI"  public="true" static="true" final="true">
<type class="char"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.BELL -->
<field name="BELL"  public="true" static="true" final="true">
<type class="char"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.NUL -->
<field name="NUL"  public="true" static="true" final="true">
<type class="char"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.LRC -->
<field name="LRC"  public="true" static="true" final="true">
<type class="char"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.URC -->
<field name="URC"  public="true" static="true" final="true">
<type class="char"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ULC -->
<field name="ULC"  public="true" static="true" final="true">
<type class="char"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.LLC -->
<field name="LLC"  public="true" static="true" final="true">
<type class="char"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_LF_MODE_NEW_LINE -->
<field name="ESC_LF_MODE_NEW_LINE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_LF_MODE_LINE_FEED -->
<field name="ESC_LF_MODE_LINE_FEED"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_CURSOR_MODE_APP_ESC -->
<field name="ESC_CURSOR_MODE_APP_ESC"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_CURSOR_MODE_CURSOR_ESC -->
<field name="ESC_CURSOR_MODE_CURSOR_ESC"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_TERM_MODE_ANSI_US -->
<field name="ESC_TERM_MODE_ANSI_US"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_TERM_MODE_VT52 -->
<field name="ESC_TERM_MODE_VT52"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_COLUMN_MODE_132 -->
<field name="ESC_COLUMN_MODE_132"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_COLUMN_MODE_80 -->
<field name="ESC_COLUMN_MODE_80"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_SCROLL_MODE_SMOOTH -->
<field name="ESC_SCROLL_MODE_SMOOTH"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_SCROLL_MODE_JUMP -->
<field name="ESC_SCROLL_MODE_JUMP"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_SCREEN_MODE_REVERSE -->
<field name="ESC_SCREEN_MODE_REVERSE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_SCREEN_MODE_NORMAL -->
<field name="ESC_SCREEN_MODE_NORMAL"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_ORIGIN_MODE_RELATIVE -->
<field name="ESC_ORIGIN_MODE_RELATIVE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_ORIGIN_MODE_ABSOLUTE -->
<field name="ESC_ORIGIN_MODE_ABSOLUTE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_WRAPAROUND_ON -->
<field name="ESC_WRAPAROUND_ON"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_WRAPAROUND_OFF -->
<field name="ESC_WRAPAROUND_OFF"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_AUTO_REPEAT_ON -->
<field name="ESC_AUTO_REPEAT_ON"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_AUTO_REPEAT_OFF -->
<field name="ESC_AUTO_REPEAT_OFF"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_INTERLACE_ON -->
<field name="ESC_INTERLACE_ON"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_INTERLACE_OFF -->
<field name="ESC_INTERLACE_OFF"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_US_ASCII -->
<field name="ESC_US_ASCII"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_INVOKE_G1_CHAR_SET -->
<field name="ESC_INVOKE_G1_CHAR_SET"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_KEYPAD_MODE_NUMERIC -->
<field name="ESC_KEYPAD_MODE_NUMERIC"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_KEYPAD_MODE_APPLICATION -->
<field name="ESC_KEYPAD_MODE_APPLICATION"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_ALL_CHAR_ATTR_OFF -->
<field name="ESC_ALL_CHAR_ATTR_OFF"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_CHAR_ATTR_BOLD -->
<field name="ESC_CHAR_ATTR_BOLD"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_CHAR_ATTR_REVERSE -->
<field name="ESC_CHAR_ATTR_REVERSE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.F1 -->
<field name="F1"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.F2 -->
<field name="F2"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.F3 -->
<field name="F3"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.F4 -->
<field name="F4"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.F12 -->
<field name="F12"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.SHIFT_TAB -->
<field name="SHIFT_TAB"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.INSERT_LINE -->
<field name="INSERT_LINE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_TO_FIRST_LINE -->
<field name="CURSOR_TO_FIRST_LINE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_TO_LAST_LINE -->
<field name="CURSOR_TO_LAST_LINE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ERASE_LINE -->
<field name="ERASE_LINE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_PAGE_UP -->
<field name="CURSOR_PAGE_UP"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_PAGE_DOWN -->
<field name="CURSOR_PAGE_DOWN"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_PAGE_UP -->
<field name="ESC_PAGE_UP"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.ESC_PAGE_DOWN -->
<field name="ESC_PAGE_DOWN"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_UP -->
<field name="CURSOR_UP"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_DOWN -->
<field name="CURSOR_DOWN"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_RIGHT -->
<field name="CURSOR_RIGHT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_LEFT -->
<field name="CURSOR_LEFT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CTLZ -->
<field name="CTLZ"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.TOGGLE_INSERT_MODE -->
<field name="TOGGLE_INSERT_MODE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_TO_LEFT_COLUMN -->
<field name="CURSOR_TO_LEFT_COLUMN"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_TO_RIGHT_SIDE -->
<field name="CURSOR_TO_RIGHT_SIDE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.DELETE_TO_RIGHT -->
<field name="DELETE_TO_RIGHT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_LOCATION_UNKNOWN -->
<field name="CURSOR_LOCATION_UNKNOWN"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_LOCATION_NOT_LOGGED_IN -->
<field name="CURSOR_LOCATION_NOT_LOGGED_IN"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_ON_HOME_VIEW_EDIT_CONNECT_LOGOUT -->
<field name="CURSOR_IS_ON_HOME_VIEW_EDIT_CONNECT_LOGOUT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_ON_HOME_FILE_EDIT_SEARCH_CHECK_TOOLS -->
<field name="CURSOR_IS_ON_HOME_FILE_EDIT_SEARCH_CHECK_TOOLS"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_COMMAND_LINE_AREA -->
<field name="CURSOR_IS_IN_COMMAND_LINE_AREA"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_FILE_EDIT_AREA -->
<field name="CURSOR_IS_IN_FILE_EDIT_AREA"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_ON_LINE_1 -->
<field name="CURSOR_IS_ON_LINE_1"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_ON_LINE_21 -->
<field name="CURSOR_IS_ON_LINE_21"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_ON_LINE_22 -->
<field name="CURSOR_IS_ON_LINE_22"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_ON_LINE_23 -->
<field name="CURSOR_IS_ON_LINE_23"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_LOGIN_DIALOG_USER_NAME -->
<field name="CURSOR_IS_IN_LOGIN_DIALOG_USER_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_LOGIN_DIALOG_PASSWORD -->
<field name="CURSOR_IS_IN_LOGIN_DIALOG_PASSWORD"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_MENU_PULL_DOWN -->
<field name="CURSOR_IS_IN_VIEW_MENU_PULL_DOWN"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_MENU_PULL_DOWN -->
<field name="CURSOR_IS_IN_EDIT_MENU_PULL_DOWN"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_CONNECT_DIALOG_BOX -->
<field name="CURSOR_IS_IN_CONNECT_DIALOG_BOX"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_LOGOUT_DIALOG_BOX_YES -->
<field name="CURSOR_IS_IN_LOGOUT_DIALOG_BOX_YES"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_LOGOUT_DIALOG_BOX_NO -->
<field name="CURSOR_IS_IN_LOGOUT_DIALOG_BOX_NO"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_SUMMARY_NONE_DEFINED_DIALOG -->
<field name="CURSOR_IS_IN_VIEW_SUMMARY_NONE_DEFINED_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_YES_NO_DIALOG_YES -->
<field name="CURSOR_IS_IN_YES_NO_DIALOG_YES"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_YES_NO_DIALOG_NO -->
<field name="CURSOR_IS_IN_YES_NO_DIALOG_NO"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_OK_DIALOG_BOX -->
<field name="CURSOR_IS_IN_OK_DIALOG_BOX"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_MESSAGES_MESSAGE_AREA -->
<field name="CURSOR_IS_IN_VIEW_MESSAGES_MESSAGE_AREA"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_MESSAGES_CLEAR_BUTTON -->
<field name="CURSOR_IS_IN_VIEW_MESSAGES_CLEAR_BUTTON"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_MESSAGES_EXIT_BUTTON -->
<field name="CURSOR_IS_IN_VIEW_MESSAGES_EXIT_BUTTON"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_POINTS_SUMMARY -->
<field name="CURSOR_IS_IN_VIEW_POINTS_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_INPUTS_SUMMARY -->
<field name="CURSOR_IS_IN_VIEW_INPUTS_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_OUTPUTS_SUMMARY -->
<field name="CURSOR_IS_IN_VIEW_OUTPUTS_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_NUMERICS_SUMMARY -->
<field name="CURSOR_IS_IN_VIEW_NUMERICS_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_STRINGS_SUMMARY -->
<field name="CURSOR_IS_IN_VIEW_STRINGS_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_SYSTEM_VARIABLES_SUMMARY -->
<field name="CURSOR_IS_IN_VIEW_SYSTEM_VARIABLES_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_DATE_TIME_SUMMARY -->
<field name="CURSOR_IS_IN_VIEW_DATE_TIME_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_FILES_SUMMARY -->
<field name="CURSOR_IS_IN_VIEW_FILES_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_PROGRAMS_SUMMARY -->
<field name="CURSOR_IS_IN_VIEW_PROGRAMS_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_INFINET_SUMMARY -->
<field name="CURSOR_IS_IN_VIEW_INFINET_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_CONTROLLER_SUMMARY -->
<field name="CURSOR_IS_IN_VIEW_CONTROLLER_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_DISABLED_POINTS_SUMMARY -->
<field name="CURSOR_IS_IN_VIEW_DISABLED_POINTS_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_DISABLED_SYSTEM_VARS_SUMMARY -->
<field name="CURSOR_IS_IN_VIEW_DISABLED_SYSTEM_VARS_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_VIEW_DISABLED_FILES_SUMMARY -->
<field name="CURSOR_IS_IN_VIEW_DISABLED_FILES_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_USERS_OPEN_USER_DIALOG -->
<field name="CURSOR_IS_IN_EDIT_USERS_OPEN_USER_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_USER -->
<field name="CURSOR_IS_IN_EDIT_USER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_USER_SAVE_AS_DIALOG -->
<field name="CURSOR_IS_IN_EDIT_USER_SAVE_AS_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_USER_DELETE_USER_DIALOG -->
<field name="CURSOR_IS_IN_EDIT_USER_DELETE_USER_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_USER_DELETE_USER_DIALOG_OK -->
<field name="CURSOR_IS_IN_EDIT_USER_DELETE_USER_DIALOG_OK"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_USER_DELETE_USER_DIALOG_CANCEL -->
<field name="CURSOR_IS_IN_EDIT_USER_DELETE_USER_DIALOG_CANCEL"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_USER_TEACH_USER_DIALOG -->
<field name="CURSOR_IS_IN_EDIT_USER_TEACH_USER_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_USER_TEACH_USER_DIALOG_OK -->
<field name="CURSOR_IS_IN_EDIT_USER_TEACH_USER_DIALOG_OK"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_USER_TEACH_USER_DIALOG_CANCEL -->
<field name="CURSOR_IS_IN_EDIT_USER_TEACH_USER_DIALOG_CANCEL"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_POINT -->
<field name="CURSOR_IS_IN_EDIT_POINT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_POINT_OPEN_POINT_DIALOG -->
<field name="CURSOR_IS_IN_EDIT_POINT_OPEN_POINT_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_POINT_SAVE_AS_DIALOG -->
<field name="CURSOR_IS_IN_EDIT_POINT_SAVE_AS_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_POINT_DETAILS_BOX -->
<field name="CURSOR_IS_IN_EDIT_POINT_DETAILS_BOX"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_POINT_DETAILS1_BOX -->
<field name="CURSOR_IS_IN_EDIT_POINT_DETAILS1_BOX"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_POINT_DETAILS_DELETE_YES -->
<field name="CURSOR_IS_IN_EDIT_POINT_DETAILS_DELETE_YES"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_POINT_DETAILS_DELETE_NO -->
<field name="CURSOR_IS_IN_EDIT_POINT_DETAILS_DELETE_NO"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_POINT_OK_DIALOG_BOX -->
<field name="CURSOR_IS_IN_EDIT_POINT_OK_DIALOG_BOX"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_POINT_DETAILS_LOGS -->
<field name="CURSOR_IS_IN_EDIT_POINT_DETAILS_LOGS"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_POINT_DETAILS_TRIGGERS -->
<field name="CURSOR_IS_IN_EDIT_POINT_DETAILS_TRIGGERS"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_POINT_DETAILS_TRIGGERS_ERROR -->
<field name="CURSOR_IS_IN_EDIT_POINT_DETAILS_TRIGGERS_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_POINT_OPEN_SYSTEM_VAR_DIALOG -->
<field name="CURSOR_IS_IN_EDIT_POINT_OPEN_SYSTEM_VAR_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_OPEN_FILE_DIALOG -->
<field name="CURSOR_IS_IN_EDIT_OPEN_FILE_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_FILE_SAVE_AS_DIALOG -->
<field name="CURSOR_IS_IN_EDIT_FILE_SAVE_AS_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_FILE_DEFINITION_WINDOW -->
<field name="CURSOR_IS_IN_FILE_DEFINITION_WINDOW"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_FILE_FILE_MENU_PULL_DOWN -->
<field name="CURSOR_IS_IN_EDIT_FILE_FILE_MENU_PULL_DOWN"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_FILE_EDIT_MENU_PULL_DOWN -->
<field name="CURSOR_IS_IN_EDIT_FILE_EDIT_MENU_PULL_DOWN"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_FILE_SEARCH_MENU_PULL_DOWN -->
<field name="CURSOR_IS_IN_EDIT_FILE_SEARCH_MENU_PULL_DOWN"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_FILE_TOOLS_MENU_PULL_DOWN -->
<field name="CURSOR_IS_IN_EDIT_FILE_TOOLS_MENU_PULL_DOWN"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_FILE_EDITOR -->
<field name="CURSOR_IS_IN_FILE_EDITOR"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_OPEN_A_POINT_DIALOG -->
<field name="CURSOR_IS_IN_TOOLS_OPEN_A_POINT_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_EDIT_POINT -->
<field name="CURSOR_IS_IN_TOOLS_EDIT_POINT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_OPEN_A_SYSTEM_VAR_DIALOG -->
<field name="CURSOR_IS_IN_TOOLS_OPEN_A_SYSTEM_VAR_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_MESSAGES_MESSAGE_AREA -->
<field name="CURSOR_IS_IN_TOOLS_MESSAGES_MESSAGE_AREA"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_MESSAGES_CLEAR_BUTTON -->
<field name="CURSOR_IS_IN_TOOLS_MESSAGES_CLEAR_BUTTON"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_MESSAGES_EXIT_BUTTON -->
<field name="CURSOR_IS_IN_TOOLS_MESSAGES_EXIT_BUTTON"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_COMMAND_WINDOW_CMDLINE_AREA -->
<field name="CURSOR_IS_IN_TOOLS_COMMAND_WINDOW_CMDLINE_AREA"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_COMMAND_WINDOW_EDITOR_AREA -->
<field name="CURSOR_IS_IN_TOOLS_COMMAND_WINDOW_EDITOR_AREA"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_FILE_YES_NO_DIALOG_BOX_YES -->
<field name="CURSOR_IS_IN_FILE_YES_NO_DIALOG_BOX_YES"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_FILE_YES_NO_DIALOG_BOX_NO -->
<field name="CURSOR_IS_IN_FILE_YES_NO_DIALOG_BOX_NO"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_FILE_OK_DIALOG_BOX -->
<field name="CURSOR_IS_IN_FILE_OK_DIALOG_BOX"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_POINT_DETAILS_BOX -->
<field name="CURSOR_IS_IN_TOOLS_POINT_DETAILS_BOX"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_POINT_DETAILS1_BOX -->
<field name="CURSOR_IS_IN_TOOLS_POINT_DETAILS1_BOX"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_POINT_DETAILS_LOGS -->
<field name="CURSOR_IS_IN_TOOLS_POINT_DETAILS_LOGS"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_POINT_DETAILS_TRIGGERS_ERROR -->
<field name="CURSOR_IS_IN_TOOLS_POINT_DETAILS_TRIGGERS_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_POINT_DETAILS_TRIGGERS -->
<field name="CURSOR_IS_IN_TOOLS_POINT_DETAILS_TRIGGERS"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_POINT_DETAILS_DELETE_YES -->
<field name="CURSOR_IS_IN_TOOLS_POINT_DETAILS_DELETE_YES"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_POINT_DETAILS_DELETE_NO -->
<field name="CURSOR_IS_IN_TOOLS_POINT_DETAILS_DELETE_NO"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_POINT_SAVE_AS_DIALOG -->
<field name="CURSOR_IS_IN_TOOLS_POINT_SAVE_AS_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_POINT_OK_DIALOG_BOX -->
<field name="CURSOR_IS_IN_TOOLS_POINT_OK_DIALOG_BOX"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_POINT_SUMMARY -->
<field name="CURSOR_IS_IN_TOOLS_POINT_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_FILE_SUMMARY -->
<field name="CURSOR_IS_IN_TOOLS_FILE_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_PROGRAM_SUMMARY -->
<field name="CURSOR_IS_IN_TOOLS_PROGRAM_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_TOOLS_SYSTEM_VAR_SUMMARY -->
<field name="CURSOR_IS_IN_TOOLS_SYSTEM_VAR_SUMMARY"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_FILE_OPEN_FILE_DIALOG -->
<field name="CURSOR_IS_IN_FILE_OPEN_FILE_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_OPEN_COMMPORT_DIALOG -->
<field name="CURSOR_IS_IN_EDIT_OPEN_COMMPORT_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_OPEN_CONTROLLER_DIALOG -->
<field name="CURSOR_IS_IN_EDIT_OPEN_CONTROLLER_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_OPEN_INFINETCONTROLLER_DIALOG -->
<field name="CURSOR_IS_IN_EDIT_OPEN_INFINETCONTROLLER_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_SYSTEM_TIME_DIALOG -->
<field name="CURSOR_IS_IN_EDIT_SYSTEM_TIME_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_SYSTEM_TIME_DIALOG_OK -->
<field name="CURSOR_IS_IN_EDIT_SYSTEM_TIME_DIALOG_OK"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_SYSTEM_TIME_DIALOG_CANCEL -->
<field name="CURSOR_IS_IN_EDIT_SYSTEM_TIME_DIALOG_CANCEL"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_OPEN_PERSON_DIALOG -->
<field name="CURSOR_IS_IN_EDIT_OPEN_PERSON_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_OPEN_AREA_DIALOG -->
<field name="CURSOR_IS_IN_EDIT_OPEN_AREA_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_OPEN_DOOR_DIALOG -->
<field name="CURSOR_IS_IN_EDIT_OPEN_DOOR_DIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_PERSON -->
<field name="CURSOR_IS_IN_EDIT_PERSON"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_AREA -->
<field name="CURSOR_IS_IN_EDIT_AREA"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_DOOR -->
<field name="CURSOR_IS_IN_EDIT_DOOR"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_PERSON_SAVEASDIALOG -->
<field name="CURSOR_IS_IN_EDIT_PERSON_SAVEASDIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_AREA_SAVEASDIALOG -->
<field name="CURSOR_IS_IN_EDIT_AREA_SAVEASDIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_DOOR_SAVEASDIALOG -->
<field name="CURSOR_IS_IN_EDIT_DOOR_SAVEASDIALOG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_PERSON_CARDINFO -->
<field name="CURSOR_IS_IN_EDIT_PERSON_CARDINFO"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_DELETE_PERSON_DIALOG_BOX_YES -->
<field name="CURSOR_IS_IN_DELETE_PERSON_DIALOG_BOX_YES"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_DELETE_PERSON_DIALOG_BOX_NO -->
<field name="CURSOR_IS_IN_DELETE_PERSON_DIALOG_BOX_NO"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_PERSON_CARDINFO_DETAILS1 -->
<field name="CURSOR_IS_IN_EDIT_PERSON_CARDINFO_DETAILS1"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_PERSON_CARDINFO_DETAILS2 -->
<field name="CURSOR_IS_IN_EDIT_PERSON_CARDINFO_DETAILS2"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_PERSON_CARDINFO_DETAILS3 -->
<field name="CURSOR_IS_IN_EDIT_PERSON_CARDINFO_DETAILS3"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_DOOR_ENTRYREADER -->
<field name="CURSOR_IS_IN_EDIT_DOOR_ENTRYREADER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_DOOR_ENTRYREADER_DETAILS1 -->
<field name="CURSOR_IS_IN_EDIT_DOOR_ENTRYREADER_DETAILS1"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_DOOR_ENTRYREADER_DETAILS2 -->
<field name="CURSOR_IS_IN_EDIT_DOOR_ENTRYREADER_DETAILS2"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_DOOR_EXITREADER -->
<field name="CURSOR_IS_IN_EDIT_DOOR_EXITREADER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_DOOR_EXITREADER_DETAILS1 -->
<field name="CURSOR_IS_IN_EDIT_DOOR_EXITREADER_DETAILS1"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_DOOR_EXITREADER_DETAILS2 -->
<field name="CURSOR_IS_IN_EDIT_DOOR_EXITREADER_DETAILS2"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_DOOR_DETAILS1 -->
<field name="CURSOR_IS_IN_EDIT_DOOR_DETAILS1"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_DOOR_DETAILS2 -->
<field name="CURSOR_IS_IN_EDIT_DOOR_DETAILS2"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_DOOR_DETAILS3 -->
<field name="CURSOR_IS_IN_EDIT_DOOR_DETAILS3"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_DOOR_XDRIVER -->
<field name="CURSOR_IS_IN_EDIT_DOOR_XDRIVER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_FILE_SEARCH -->
<field name="CURSOR_IS_IN_FILE_SEARCH"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_FILE_SEARCH_OK -->
<field name="CURSOR_IS_IN_FILE_SEARCH_OK"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_FILE_SEARCH_CANCEL -->
<field name="CURSOR_IS_IN_FILE_SEARCH_CANCEL"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_FILE_EDIT_ERROR -->
<field name="CURSOR_IS_IN_FILE_EDIT_ERROR"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_FILE_SEARCH_REPLACE -->
<field name="CURSOR_IS_IN_FILE_SEARCH_REPLACE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_CONTROLLER_OPTIONS -->
<field name="CURSOR_IS_IN_CONTROLLER_OPTIONS"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_CONTROLLER -->
<field name="CURSOR_IS_IN_EDIT_CONTROLLER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_CONTROLLER_DETAILS -->
<field name="CURSOR_IS_IN_CONTROLLER_DETAILS"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_CONTROLLER_TCPIP -->
<field name="CURSOR_IS_IN_CONTROLLER_TCPIP"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_SELECTION_BOX -->
<field name="CURSOR_IS_IN_SELECTION_BOX"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_EDIT_COMMPORT -->
<field name="CURSOR_IS_IN_EDIT_COMMPORT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_SYSTEM_DATETIME_ERROR_OK -->
<field name="CURSOR_IS_IN_SYSTEM_DATETIME_ERROR_OK"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_SYSTEM_DATETIME_ERROR_CANCEL -->
<field name="CURSOR_IS_IN_SYSTEM_DATETIME_ERROR_CANCEL"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_SAVE_INFINET_START -->
<field name="CURSOR_IS_IN_SAVE_INFINET_START"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_SAVE_INFINET_DONE -->
<field name="CURSOR_IS_IN_SAVE_INFINET_DONE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_SAVE_INFINET_ACTIVE -->
<field name="CURSOR_IS_IN_SAVE_INFINET_ACTIVE"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_BUILDING_LIST_PLEASE_WAIT -->
<field name="CURSOR_IS_IN_BUILDING_LIST_PLEASE_WAIT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.CURSOR_IS_IN_RELOAD_INFINET_START -->
<field name="CURSOR_IS_IN_RELOAD_INFINET_START"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- com.tridium.andoverInfinity.comm.Vt100Const.cursorModes -->
<field name="cursorModes"  public="true" static="true" final="true">
<type class="java.lang.String" dimension="1"/>
<description/>
</field>

</class>
</bajadoc>
