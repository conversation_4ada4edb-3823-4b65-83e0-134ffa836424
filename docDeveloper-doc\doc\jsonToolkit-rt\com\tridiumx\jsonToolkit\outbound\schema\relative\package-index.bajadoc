<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.outbound.schema.relative">
<description/>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.relative" name="BBaseAndOutputPair"><description>Pojo which holds a pair of associated schema generations values:&#xa;&#xa; the output string&#xa; the base item which the schema was generated against</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.relative" name="BBaseQuery"><description>BaseQuery publishes base items to it&#x27;s parent relative schema so Json generation can begin.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.relative" name="BRelativeJsonSchema"><description>BRelativeJsonSchema allows base objects to be passed down through the schema&#xa; members (typically by bql in the &lt;code&gt;<see ref="com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery">BBaseQuery</see>&lt;/code&gt; which can then be resolved&#xa; using relative ords like slot: or slot:../&#xa;&#xa; This concept should allow for greater scalability than regular JsonSchema,&#xa; and dynamic selection of data to export based on query result.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.relative" name="SubscriptionTable"><description>SubscriptionTable&#xa;&#xa; One of these exists per schema with relative support&#xa;&#xa; The table tracks 2 things:&#xa;&#xa; 1) A mapping between all bound member targets and the base query items they were resolved against&#xa;    This allows us to lookup the base item to pass through the schema when a subscription event occurs&#xa; 2) A mapping of all the subscribers created for the bound member bindings</description></class>
</package>
</bajadoc>
