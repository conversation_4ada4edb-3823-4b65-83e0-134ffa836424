<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.listeners.DynamicIAmListener$IAmHandler" name="DynamicIAmListener.IAmHandler" packageName="javax.baja.bacnet.listeners" public="true" abstract="true" static="true" innerClass="true">
<description/>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<parameterizedType class="java.lang.Comparable">
<args>
<type class="java.lang.Object"/>
</args>
</parameterizedType>
</implements>
<!-- javax.baja.bacnet.listeners.DynamicIAmListener.IAmHandler() -->
<constructor name="IAmHandler" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.listeners.DynamicIAmListener.IAmHandler.handle(com.tridium.bacnet.services.unconfirmed.IAmRequest, javax.baja.bacnet.datatypes.BBacnetAddress) -->
<method name="handle"  public="true" abstract="true">
<description/>
<parameter name="request">
<type class="com.tridium.bacnet.services.unconfirmed.IAmRequest"/>
</parameter>
<parameter name="sourceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.listeners.DynamicIAmListener.IAmHandler.handles(int) -->
<method name="handles"  public="true">
<description/>
<parameter name="propertyId">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.listeners.DynamicIAmListener.IAmHandler.isTrending() -->
<method name="isTrending"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.listeners.DynamicIAmListener.IAmHandler.isAlarming() -->
<method name="isAlarming"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.listeners.DynamicIAmListener.IAmHandler.isScheduling() -->
<method name="isScheduling"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.listeners.DynamicIAmListener.IAmHandler.bindDevice(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="bindDevice"  protected="true">
<description/>
<parameter name="deviceOid">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.listeners.DynamicIAmListener.IAmHandler.compareTo(java.lang.Object) -->
<method name="compareTo"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="o">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

</class>
</bajadoc>
