<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="com.tridium.bacnet.stack">
<description/>
<class packageName="com.tridium.bacnet.stack" name="BBacnetMultiPoll"><description>BBacnetMultiPoll is the subclass of BBacnetPoll that handles multithreaded&#xa; polling.</description></class>
<class packageName="com.tridium.bacnet.stack" name="BBacnetPoll"><description>BBacnetPoll is a subclass of BAbstractPollService specifically&#xa; customized for BACnet polling.</description></class>
<class packageName="com.tridium.bacnet.stack" name="BBacnetPollOrder"><description>BBacnetPollOrder provides an override hook allowing&#xa; different strategies of poll ordering.</description></class>
<class packageName="com.tridium.bacnet.stack" name="BBacnetStack"><description>BBacnetStack provides the protocol stack for Bacnet communications.</description></class>
</package>
</bajadoc>
