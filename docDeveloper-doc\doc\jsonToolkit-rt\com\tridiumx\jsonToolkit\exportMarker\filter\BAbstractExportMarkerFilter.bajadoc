<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter" name="BAbstractExportMarkerFilter" packageName="com.tridiumx.jsonToolkit.exportMarker.filter" public="true" abstract="true">
<description>
Contains properties common to all forms of filter accepting Export Marked&#xa; components for consideration.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="currentExportId" flags="rstNd">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;currentExportId&lt;/code&gt; property.&#xa; If they want the current id in the alarm/history output, then link this to a FixedString in the output
</description>
<tag name="@see">#getCurrentExportId</tag>
<tag name="@see">#setCurrentExportId</tag>
</property>

<property name="count" flags="drtNd">
<type class="int"/>
<description>
Slot for the &lt;code&gt;count&lt;/code&gt; property.&#xa; How many export marked points have been processed since the station started
</description>
<tag name="@see">#getCount</tag>
<tag name="@see">#setCount</tag>
</property>

<property name="interMessageDelay" flags="h">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;interMessageDelay&lt;/code&gt; property.
</description>
<tag name="@see">#getInterMessageDelay</tag>
<tag name="@see">#setInterMessageDelay</tag>
</property>

<action name="sendSince" flags="aso">
<parameter name="parameter">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;sendSince&lt;/code&gt; action.&#xa; Subclasses use timestamp inclusive of records at that timestamp
</description>
<tag name="@see">#sendSince(BAbsTime parameter)</tag>
</action>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter() -->
<constructor name="BAbstractExportMarkerFilter" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter.getCurrentExportId() -->
<method name="getCurrentExportId"  public="true">
<description>
Get the &lt;code&gt;currentExportId&lt;/code&gt; property.&#xa; If they want the current id in the alarm/history output, then link this to a FixedString in the output
</description>
<tag name="@see">#currentExportId</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter.setCurrentExportId(java.lang.String) -->
<method name="setCurrentExportId"  public="true">
<description>
Set the &lt;code&gt;currentExportId&lt;/code&gt; property.&#xa; If they want the current id in the alarm/history output, then link this to a FixedString in the output
</description>
<tag name="@see">#currentExportId</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter.getCount() -->
<method name="getCount"  public="true">
<description>
Get the &lt;code&gt;count&lt;/code&gt; property.&#xa; How many export marked points have been processed since the station started
</description>
<tag name="@see">#count</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter.setCount(int) -->
<method name="setCount"  public="true">
<description>
Set the &lt;code&gt;count&lt;/code&gt; property.&#xa; How many export marked points have been processed since the station started
</description>
<tag name="@see">#count</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter.getInterMessageDelay() -->
<method name="getInterMessageDelay"  public="true">
<description>
Get the &lt;code&gt;interMessageDelay&lt;/code&gt; property.
</description>
<tag name="@see">#interMessageDelay</tag>
<return>
<type class="javax.baja.sys.BRelTime"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter.setInterMessageDelay(javax.baja.sys.BRelTime) -->
<method name="setInterMessageDelay"  public="true">
<description>
Set the &lt;code&gt;interMessageDelay&lt;/code&gt; property.
</description>
<tag name="@see">#interMessageDelay</tag>
<parameter name="v">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter.sendSince(javax.baja.sys.BAbsTime) -->
<method name="sendSince"  public="true">
<description>
Invoke the &lt;code&gt;sendSince&lt;/code&gt; action.&#xa; Subclasses use timestamp inclusive of records at that timestamp
</description>
<tag name="@see">#sendSince</tag>
<parameter name="parameter">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter.doSendSince(javax.baja.sys.BAbsTime) -->
<method name="doSendSince"  public="true" abstract="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unused&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description/>
<parameter name="from">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter.post(javax.baja.sys.Action, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="post"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="action">
<type class="javax.baja.sys.Action"/>
</parameter>
<parameter name="argument">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.util.IFuture"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter.currentExportId -->
<field name="currentExportId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;currentExportId&lt;/code&gt; property.&#xa; If they want the current id in the alarm/history output, then link this to a FixedString in the output
</description>
<tag name="@see">#getCurrentExportId</tag>
<tag name="@see">#setCurrentExportId</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter.count -->
<field name="count"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;count&lt;/code&gt; property.&#xa; How many export marked points have been processed since the station started
</description>
<tag name="@see">#getCount</tag>
<tag name="@see">#setCount</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter.interMessageDelay -->
<field name="interMessageDelay"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;interMessageDelay&lt;/code&gt; property.
</description>
<tag name="@see">#getInterMessageDelay</tag>
<tag name="@see">#setInterMessageDelay</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter.sendSince -->
<field name="sendSince"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;sendSince&lt;/code&gt; action.&#xa; Subclasses use timestamp inclusive of records at that timestamp
</description>
<tag name="@see">#sendSince(BAbsTime parameter)</tag>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.exportMarker.filter.BAbstractExportMarkerFilter.log -->
<field name="log"  protected="true" static="true" final="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

</class>
</bajadoc>
