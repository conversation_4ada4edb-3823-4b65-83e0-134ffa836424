<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetWriteStatus" name="BBacnetWriteStatus" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetWriteStatus represents the BACnetWriteStatus&#xa; enumeration.&#xa; &lt;p&gt;&#xa; BBacnetWriteStatus is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0xFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Joseph Chandler</tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;idle&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;inProgress&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;successful&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;failed&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetWriteStatus"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetWriteStatus"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
String representation of this BEnum.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.IDLE -->
<field name="IDLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for idle.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.IN_PROGRESS -->
<field name="IN_PROGRESS"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for inProgress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.SUCCESSFUL -->
<field name="SUCCESSFUL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for successful.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.FAILED -->
<field name="FAILED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for failed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.idle -->
<field name="idle"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetWriteStatus"/>
<description>
BBacnetWriteStatus constant for idle.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.inProgress -->
<field name="inProgress"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetWriteStatus"/>
<description>
BBacnetWriteStatus constant for inProgress.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.successful -->
<field name="successful"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetWriteStatus"/>
<description>
BBacnetWriteStatus constant for successful.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.failed -->
<field name="failed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetWriteStatus"/>
<description>
BBacnetWriteStatus constant for failed.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetWriteStatus"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetWriteStatus.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
