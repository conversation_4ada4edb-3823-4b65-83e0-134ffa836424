<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.component.BAceTextBlock" name="BAceTextBlock" packageName="com.tridium.ace.component" public="true">
<description>
BAceTextBlock
</description>
<tag name="@author"><PERSON> on 7/26/2019</tag>
<extends>
<type class="com.tridium.ace.component.BAceComponent"/>
</extends>
<property name="text" flags="s">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;text&lt;/code&gt; property.
</description>
<tag name="@see">#getText</tag>
<tag name="@see">#setText</tag>
</property>

<property name="foreground" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;foreground&lt;/code&gt; property.
</description>
<tag name="@see">#getForeground</tag>
<tag name="@see">#setForeground</tag>
</property>

<property name="background" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;background&lt;/code&gt; property.
</description>
<tag name="@see">#getBackground</tag>
<tag name="@see">#setBackground</tag>
</property>

<property name="font" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;font&lt;/code&gt; property.
</description>
<tag name="@see">#getFont</tag>
<tag name="@see">#setFont</tag>
</property>

<property name="border" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;border&lt;/code&gt; property.
</description>
<tag name="@see">#getBorder</tag>
<tag name="@see">#setBorder</tag>
</property>

<property name="selectable" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;selectable&lt;/code&gt; property.
</description>
<tag name="@see">#getSelectable</tag>
<tag name="@see">#setSelectable</tag>
</property>

<property name="wsHeight" flags="h">
<type class="int"/>
<description>
Slot for the &lt;code&gt;wsHeight&lt;/code&gt; property.
</description>
<tag name="@see">#getWsHeight</tag>
<tag name="@see">#setWsHeight</tag>
</property>

<property name="wsWidth" flags="h">
<type class="int"/>
<description>
Slot for the &lt;code&gt;wsWidth&lt;/code&gt; property.
</description>
<tag name="@see">#getWsWidth</tag>
<tag name="@see">#setWsWidth</tag>
</property>

</class>
</bajadoc>
