<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.point.BBacnetStringProxyExt" name="BBacnetStringProxyExt" packageName="javax.baja.bacnet.point" public="true">
<description>
BBacnetStringProxyExt handles the point configuration&#xa; of a point of generic type in a Bacnet device.&#xa; &lt;p&gt;&#xa; It is the default for types NULL, OCTET_STRING, CHARACTER_STRING,&#xa; BIT_STRING, DATE, TIME, and OBJECT_IDENTIFIER.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">12 Feb 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.point.BBacnetProxyExt"/>
</extends>
<!-- javax.baja.bacnet.point.BBacnetStringProxyExt() -->
<constructor name="BBacnetStringProxyExt" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.point.BBacnetStringProxyExt.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetStringProxyExt.fromEncodedValue(byte[], javax.baja.status.BStatus, javax.baja.sys.Context) -->
<method name="fromEncodedValue"  public="true">
<description/>
<parameter name="encodedValue">
<type class="byte" dimension="1"/>
</parameter>
<parameter name="bacnetStatus">
<type class="javax.baja.status.BStatus"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetStringProxyExt.toEncodedValue(javax.baja.status.BStatusValue) -->
<method name="toEncodedValue"  public="true">
<description/>
<parameter name="newValue">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<return>
<type class="byte" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.point.BBacnetStringProxyExt.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
