<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetLogRecord" name="BBacnetLogRecord" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BBacnetLogRecord represents the BacnetLogRecord sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">18 Sep 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="timestamp" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
<description>
Slot for the &lt;code&gt;timestamp&lt;/code&gt; property.
</description>
<tag name="@see">#getTimestamp</tag>
<tag name="@see">#setTimestamp</tag>
</property>

<property name="logDatum" flags="">
<type class="javax.baja.sys.BSimple"/>
<description>
Slot for the &lt;code&gt;logDatum&lt;/code&gt; property.
</description>
<tag name="@see">#getLogDatum</tag>
<tag name="@see">#setLogDatum</tag>
</property>

<property name="statusFlags" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
Slot for the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#getStatusFlags</tag>
<tag name="@see">#setStatusFlags</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord() -->
<constructor name="BBacnetLogRecord" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord(javax.baja.bacnet.datatypes.BBacnetDateTime, javax.baja.sys.BSimple) -->
<constructor name="BBacnetLogRecord" public="true">
<parameter name="dt">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</parameter>
<parameter name="s">
<type class="javax.baja.sys.BSimple"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord(javax.baja.sys.BAbsTime, javax.baja.sys.BSimple) -->
<constructor name="BBacnetLogRecord" public="true">
<parameter name="bt">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<parameter name="s">
<type class="javax.baja.sys.BSimple"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.getTimestamp() -->
<method name="getTimestamp"  public="true">
<description>
Get the &lt;code&gt;timestamp&lt;/code&gt; property.
</description>
<tag name="@see">#timestamp</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.setTimestamp(javax.baja.bacnet.datatypes.BBacnetDateTime) -->
<method name="setTimestamp"  public="true">
<description>
Set the &lt;code&gt;timestamp&lt;/code&gt; property.
</description>
<tag name="@see">#timestamp</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.getLogDatum() -->
<method name="getLogDatum"  public="true">
<description>
Get the &lt;code&gt;logDatum&lt;/code&gt; property.
</description>
<tag name="@see">#logDatum</tag>
<return>
<type class="javax.baja.sys.BSimple"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.setLogDatum(javax.baja.sys.BSimple) -->
<method name="setLogDatum"  public="true">
<description>
Set the &lt;code&gt;logDatum&lt;/code&gt; property.
</description>
<tag name="@see">#logDatum</tag>
<parameter name="v">
<type class="javax.baja.sys.BSimple"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.getStatusFlags() -->
<method name="getStatusFlags"  public="true">
<description>
Get the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#statusFlags</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.setStatusFlags(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="setStatusFlags"  public="true">
<description>
Set the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#statusFlags</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.writeLogRecord(javax.baja.bacnet.datatypes.BBacnetDateTime, javax.baja.sys.BSimple, int, javax.baja.bacnet.datatypes.BBacnetBitString, long, javax.baja.bacnet.io.AsnOutput) -->
<method name="writeLogRecord"  public="true" static="true">
<description/>
<parameter name="timestamp">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</parameter>
<parameter name="logDatum">
<type class="javax.baja.sys.BSimple"/>
</parameter>
<parameter name="logDatumChoice">
<type class="int"/>
</parameter>
<parameter name="statusFlags">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<parameter name="trendEvent">
<type class="long"/>
</parameter>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.writeLogRecord(javax.baja.sys.BAbsTime, javax.baja.sys.BSimple, int, javax.baja.status.BStatus, long, javax.baja.bacnet.io.AsnOutput) -->
<method name="writeLogRecord"  public="true" static="true">
<description>
Write a BACnetLogRecord to the stream.&#xa; &lt;p&gt;&#xa; The encoded size of a BACnetLogRecord is:&#xa; 1 (timestamp opening tag)&#xa; 4 (date)&#xa; 4 (time)&#xa; 1 (timestamp closing tag)&#xa; 1 (log datum opening tag)&#xa; X (log datum)&#xa; 1 (log datum closing tag)&#xa; +   3 (status flags)&#xa; ------------------------------&#xa; = 15 + X where X is the encoded logDatum size
</description>
<parameter name="timestamp">
<type class="javax.baja.sys.BAbsTime"/>
<description/>
</parameter>
<parameter name="logDatum">
<type class="javax.baja.sys.BSimple"/>
<description/>
</parameter>
<parameter name="logDatumChoice">
<type class="int"/>
<description/>
</parameter>
<parameter name="statusFlags">
<type class="javax.baja.status.BStatus"/>
<description/>
</parameter>
<parameter name="trendEvent">
<type class="long"/>
<description/>
</parameter>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.getNiagaraRecordType() -->
<method name="getNiagaraRecordType"  public="true">
<description/>
<return>
<type class="javax.baja.util.BTypeSpec"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.initializeNiagaraRecord(javax.baja.history.BHistoryRecord, long) -->
<method name="initializeNiagaraRecord"  public="true">
<description/>
<parameter name="record">
<type class="javax.baja.history.BHistoryRecord"/>
</parameter>
<parameter name="seqNum">
<type class="long"/>
</parameter>
<return>
<type class="javax.baja.history.BHistoryRecord"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.timestamp -->
<field name="timestamp"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;timestamp&lt;/code&gt; property.
</description>
<tag name="@see">#getTimestamp</tag>
<tag name="@see">#setTimestamp</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.logDatum -->
<field name="logDatum"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;logDatum&lt;/code&gt; property.
</description>
<tag name="@see">#getLogDatum</tag>
<tag name="@see">#setLogDatum</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.statusFlags -->
<field name="statusFlags"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;statusFlags&lt;/code&gt; property.
</description>
<tag name="@see">#getStatusFlags</tag>
<tag name="@see">#setStatusFlags</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.TIMESTAMP_TAG -->
<field name="TIMESTAMP_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.LOG_DATUM_TAG -->
<field name="LOG_DATUM_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.STATUS_FLAGS_TAG -->
<field name="STATUS_FLAGS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.LOG_STATUS_TAG -->
<field name="LOG_STATUS_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.BOOLEAN_VALUE_TAG -->
<field name="BOOLEAN_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.REAL_VALUE_TAG -->
<field name="REAL_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.ENUM_VALUE_TAG -->
<field name="ENUM_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.UNSIGNED_VALUE_TAG -->
<field name="UNSIGNED_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.SIGNED_VALUE_TAG -->
<field name="SIGNED_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.BITSTRING_VALUE_TAG -->
<field name="BITSTRING_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.NULL_VALUE_TAG -->
<field name="NULL_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.FAILURE_TAG -->
<field name="FAILURE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.TIME_CHANGE_TAG -->
<field name="TIME_CHANGE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.ANY_VALUE_TAG -->
<field name="ANY_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.LOG_STATUS_STRING -->
<field name="LOG_STATUS_STRING"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.FAILURE_STRING -->
<field name="FAILURE_STRING"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.TIME_CHANGE_STRING -->
<field name="TIME_CHANGE_STRING"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.EVENT_STRING -->
<field name="EVENT_STRING"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.INVALID_STRING -->
<field name="INVALID_STRING"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.UNKNOWN_STRING -->
<field name="UNKNOWN_STRING"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.LOG_ENABLED_STRING -->
<field name="LOG_ENABLED_STRING"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.LOG_DISABLED_STRING -->
<field name="LOG_DISABLED_STRING"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.LOG_BUFFER_PURGED_STRING -->
<field name="LOG_BUFFER_PURGED_STRING"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.LOG_INTERRUPTED_STRING -->
<field name="LOG_INTERRUPTED_STRING"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetLogRecord.SECONDS_STRING -->
<field name="SECONDS_STRING"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

</class>
</bajadoc>
