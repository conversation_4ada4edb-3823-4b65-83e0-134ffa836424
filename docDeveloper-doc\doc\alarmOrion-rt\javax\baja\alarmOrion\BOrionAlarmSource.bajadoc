<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarmOrion" runtimeProfile="rt" qualifiedName="javax.baja.alarmOrion.BOrionAlarmSource" name="BOrionAlarmSource" packageName="javax.baja.alarmOrion" public="true">
<description/>
<extends>
<type class="com.tridium.orion.BOrionObject"/>
</extends>
<annotation><type class="com.tridium.orion.annotations.NiagaraOrionType"/>
</annotation>
<property name="id" flags="rs">
<type class="int"/>
<description>
Slot for the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#getId</tag>
<tag name="@see">#setId</tag>
</property>

<property name="source" flags="s">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;source&lt;/code&gt; property.&#xa; The path to the source of the alarm.
</description>
<tag name="@see">#getSource</tag>
<tag name="@see">#setSource</tag>
</property>

<!-- javax.baja.alarmOrion.BOrionAlarmSource() -->
<constructor name="BOrionAlarmSource" public="true">
<description/>
</constructor>

<!-- javax.baja.alarmOrion.BOrionAlarmSource.getId() -->
<method name="getId"  public="true">
<description>
Get the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#id</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSource.setId(int) -->
<method name="setId"  public="true">
<description>
Set the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#id</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSource.getSource() -->
<method name="getSource"  public="true">
<description>
Get the &lt;code&gt;source&lt;/code&gt; property.&#xa; The path to the source of the alarm.
</description>
<tag name="@see">#source</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSource.setSource(javax.baja.naming.BOrd) -->
<method name="setSource"  public="true">
<description>
Set the &lt;code&gt;source&lt;/code&gt; property.&#xa; The path to the source of the alarm.
</description>
<tag name="@see">#source</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSource.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSource.get(javax.baja.naming.BOrd, com.tridium.orion.OrionSession) -->
<method name="get"  public="true" static="true">
<description/>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="javax.baja.alarmOrion.BOrionAlarmSource"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSource.afterDelete(com.tridium.orion.OrionSession) -->
<method name="afterDelete"  public="true">
<description/>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSource.afterInsert(com.tridium.orion.OrionSession) -->
<method name="afterInsert"  public="true">
<description/>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSource.afterUpdate(com.tridium.orion.OrionSession) -->
<method name="afterUpdate"  public="true">
<description/>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmSource.OLD_SOURCE_LENGTH -->
<field name="OLD_SOURCE_LENGTH"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmSource.SOURCE_LENGTH -->
<field name="SOURCE_LENGTH"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmSource.id -->
<field name="id"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#getId</tag>
<tag name="@see">#setId</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmSource.source -->
<field name="source"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;source&lt;/code&gt; property.&#xa; The path to the source of the alarm.
</description>
<tag name="@see">#getSource</tag>
<tag name="@see">#setSource</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmSource.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmSource.ORION_TYPE -->
<field name="ORION_TYPE"  public="true" static="true" final="true">
<type class="com.tridium.orion.OrionType"/>
<description/>
</field>

</class>
</bajadoc>
