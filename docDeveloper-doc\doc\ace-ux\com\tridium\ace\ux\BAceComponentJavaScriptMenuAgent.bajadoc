<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="ux" qualifiedName="com.tridium.ace.ux.BAceComponentJavaScriptMenuAgent" name="BAceComponentJavaScriptMenuAgent" packageName="com.tridium.ace.ux" public="true">
<description>
BAceComponentJavaScriptMenuAgent to remove&#xa; commands for BAceComponents.
</description>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@creation">4 Apr 21</tag>
<tag name="@since">Niagara 4.11</tag>
<extends>
<type class="javax.baja.sys.BSingleton"/>
</extends>
<implements>
<type class="javax.baja.webeditors.menu.BIJavaScriptMenuAgent"/>
</implements>
<implements>
<type class="javax.baja.web.BIOffline"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraSingleton"/>
</annotation>
<!-- com.tridium.ace.ux.BAceComponentJavaScriptMenuAgent() -->
<constructor name="BAceComponentJavaScriptMenuAgent" protected="true">
<description/>
</constructor>

<!-- com.tridium.ace.ux.BAceComponentJavaScriptMenuAgent.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.ace.ux.BAceComponentJavaScriptMenuAgent.getJsInfo(javax.baja.sys.Context) -->
<method name="getJsInfo"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.web.js.JsInfo"/>
</return>
</method>

<!-- com.tridium.ace.ux.BAceComponentJavaScriptMenuAgent.INSTANCE -->
<field name="INSTANCE"  public="true" static="true" final="true">
<type class="com.tridium.ace.ux.BAceComponentJavaScriptMenuAgent"/>
<description/>
</field>

<!-- com.tridium.ace.ux.BAceComponentJavaScriptMenuAgent.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
