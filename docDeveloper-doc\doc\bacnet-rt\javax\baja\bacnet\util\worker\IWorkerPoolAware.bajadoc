<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.util.worker.IWorkerPoolAware" name="IWorkerPoolAware" packageName="javax.baja.bacnet.util.worker" public="true" interface="true" abstract="true" category="interface">
<description>
IWorkerAware is an interface&#xa; to allow services to have child&#xa; IBacnetWorkers dynamically added / removed.&#xa; &lt;p&gt;&#xa; This will prevent users from adding a worker pool&#xa; that will not be used by the parent class.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">26 Aug 13</tag>
<tag name="@since">Niagara 3.8 Bacnet 1.0</tag>
<!-- javax.baja.bacnet.util.worker.IWorkerPoolAware.getWorkerThreadName() -->
<method name="getWorkerThreadName"  public="true" abstract="true">
<description>
Provides a base worker name for the WorkerPool&#xa; implementation to use when spawning worker threads.
</description>
<return>
<type class="java.lang.String"/>
<description>
worker thread name
</description>
</return>
</method>

<!-- javax.baja.bacnet.util.worker.IWorkerPoolAware.stopWorker() -->
<method name="stopWorker"  public="true" abstract="true">
<description>
Allows the worker pool to take over processing of messages
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.worker.IWorkerPoolAware.getQueue() -->
<method name="getQueue"  public="true" abstract="true">
<description>
Allow the worker pool to default to using the parent&#x27;s queue.&#xa; This should simplify the configuration of some worker pools&#xa; that only need one incoming queue.
</description>
<return>
<type class="javax.baja.util.Queue"/>
<description>
the queue
</description>
</return>
</method>

<!-- javax.baja.bacnet.util.worker.IWorkerPoolAware.hasWorkerPool() -->
<method name="hasWorkerPool"  public="true" abstract="true">
<description>
Provides a mechanism to determine if the IWorkerPoolAware&#xa; already has a workerPool.
</description>
<return>
<type class="boolean"/>
<description>
true, if there is already a worker pool in use&#xa; false, if a worker pool has not been assigned.
</description>
</return>
</method>

</class>
</bajadoc>
