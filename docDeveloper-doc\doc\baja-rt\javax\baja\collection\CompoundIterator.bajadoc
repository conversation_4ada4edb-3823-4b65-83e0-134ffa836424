<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.collection.CompoundIterator" name="CompoundIterator" packageName="javax.baja.collection" public="true">
<description>
A CompoundIterator concatentates two iterators.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">2/28/14</tag>
<tag name="@since">Niagara 4.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<parameterizedType class="javax.baja.util.CloseableIterator">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</implements>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;try&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<typeParameters>
<typeVariable class="T">
</typeVariable>
</typeParameters>
<!-- javax.baja.collection.CompoundIterator(java.util.Iterator&lt;T&gt;[]) -->
<constructor name="CompoundIterator" public="true">
<parameter name="subs">
<type class="java.util.Iterator" dimension="1"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.collection.CompoundIterator.hasNext() -->
<method name="hasNext"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.collection.CompoundIterator.next() -->
<method name="next"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<typeVariable class="T"/>
</return>
</method>

<!-- javax.baja.collection.CompoundIterator.close() -->
<method name="close"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Closes any sub iterators that are AutoCloseable.&#xa;&#xa; Any subclasses that override this method should remember to call super.close().
</description>
<tag name="@since">Niagara 4.6</tag>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

</class>
</bajadoc>
