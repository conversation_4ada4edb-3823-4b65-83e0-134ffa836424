<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="wb" qualifiedName="com.tridium.andoverInfinity.ui.terminal.InfinityVt100TextModel" name="InfinityVt100TextModel" packageName="com.tridium.andoverInfinity.ui.terminal" public="true">
<description>
InfinityVt100TextModel&#xa; &#xa; Custom TextModel class for Infinity VT100 terminal view.  This model takes&#xa; an array of Strings for text content and an array of strings for text formatting.
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 22, 2007</tag>
<tag name="@version">$Revision$ $May 22, 2007 10:12:51 AM$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="javax.baja.ui.text.TextModel"/>
</extends>
<!-- com.tridium.andoverInfinity.ui.terminal.InfinityVt100TextModel() -->
<constructor name="InfinityVt100TextModel" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.ui.terminal.InfinityVt100TextModel.setText(java.lang.String[], java.lang.String[]) -->
<method name="setText"  public="true">
<description>
Set the document with a String.  The textBuffer/formatBuffer are sent&#xa; on to the BInfinityVt100TextParser to parse the lines into segments that&#xa; can be displayed on the text editor pane in different colors
</description>
<parameter name="textLines">
<type class="java.lang.String" dimension="1"/>
</parameter>
<parameter name="formatLines">
<type class="java.lang.String" dimension="1"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
