<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="axvelocity" runtimeProfile="rt" qualifiedName="javax.baja.velocity.BVelocityWebProfile" name="BVelocityWebProfile" packageName="javax.baja.velocity" public="true" abstract="true">
<description>
A Web Profile for Velocity
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">14 Jun 2011</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.7</tag>
<extends>
<type class="javax.baja.sys.BObject"/>
</extends>
<implements>
<type class="javax.baja.velocity.BIVelocityWebProfile"/>
</implements>
<!-- javax.baja.velocity.BVelocityWebProfile() -->
<constructor name="BVelocityWebProfile" public="true">
<description/>
</constructor>

<!-- javax.baja.velocity.BVelocityWebProfile.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.velocity.BVelocityWebProfile.getAppNames() -->
<method name="getAppNames"  public="true">
<description>
By default, a VelocityWebProfile does not filter by application.&#xa; This method returns null.
</description>
<return>
<type class="java.lang.String" dimension="1"/>
</return>
</method>

<!-- javax.baja.velocity.BVelocityWebProfile.hasView(javax.baja.sys.BObject, javax.baja.agent.AgentInfo) -->
<method name="hasView"  public="true">
<description>
A BVelocityWebProfile can only contain non-hx servlet views.
</description>
<parameter name="target">
<type class="javax.baja.sys.BObject"/>
</parameter>
<parameter name="agentInfo">
<type class="javax.baja.agent.AgentInfo"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.velocity.BVelocityWebProfile.makeVelocityContext(javax.baja.velocity.BVelocityView, javax.baja.web.WebOp) -->
<method name="makeVelocityContext"  public="true">
<description>
Return a new VelocityContext that will be used by the template generator.&#xa; &lt;p&gt;&#xa; By default, the profile asks the view to make the Profile.
</description>
<tag name="@see">BVelocityView#makeVelocityContext(WebOp, javax.baja.web.BIWebProfile)</tag>
<parameter name="view">
<type class="javax.baja.velocity.BVelocityView"/>
</parameter>
<parameter name="op">
<type class="javax.baja.web.WebOp"/>
</parameter>
<return>
<type class="org.apache.velocity.VelocityContext"/>
<description>
VelocityContext
</description>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.velocity.BVelocityWebProfile.write(javax.baja.velocity.BVelocityView, org.apache.velocity.VelocityContext, javax.baja.web.WebOp) -->
<method name="write"  public="true">
<description>
Write the view.
</description>
<parameter name="view">
<type class="javax.baja.velocity.BVelocityView"/>
<description>
the view that needs to be written by the Profile.
</description>
</parameter>
<parameter name="context">
<type class="org.apache.velocity.VelocityContext"/>
<description>
the Velocity Context used by the template generator.
</description>
</parameter>
<parameter name="op">
<type class="javax.baja.web.WebOp"/>
<description>
the current request&#x27;s WebOp .
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.velocity.BVelocityWebProfile.getTemplateFileOrd(javax.baja.web.WebOp) -->
<method name="getTemplateFileOrd"  public="true" abstract="true">
<description>
Return the ORD to the template file for this view.&#xa; &lt;p&gt;&#xa; Normally the ORD returned is to a file embedded in a Niagara module.&#xa; For example...&#xa; &lt;pre&gt;&#xa;   module://myModule/res/myFile.vm&#xa; &lt;/pre&gt;
</description>
<parameter name="op">
<type class="javax.baja.web.WebOp"/>
<description>
the WebOp for the current request.
</description>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
<description>
the template ORD
</description>
</return>
</method>

<!-- javax.baja.velocity.BVelocityWebProfile.getVelocityEngine() -->
<method name="getVelocityEngine"  protected="true">
<description>
Return the VelocityEngine used by the Profile
</description>
<return>
<type class="org.apache.velocity.app.VelocityEngine"/>
<description>
VelocityEngine.
</description>
</return>
</method>

<!-- javax.baja.velocity.BVelocityWebProfile.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.velocity.BVelocityWebProfile.servletViewType -->
<field name="servletViewType"  protected="true" static="true" final="true">
<type class="javax.baja.registry.TypeInfo"/>
<description/>
</field>

<!-- javax.baja.velocity.BVelocityWebProfile.hxViewType -->
<field name="hxViewType"  protected="true" static="true" final="true">
<type class="javax.baja.registry.TypeInfo"/>
<description/>
</field>

<!-- javax.baja.velocity.BVelocityWebProfile.emptyStringArray -->
<field name="emptyStringArray"  protected="true" static="true" final="true">
<type class="java.lang.String" dimension="1"/>
<description/>
</field>

</class>
</bajadoc>
