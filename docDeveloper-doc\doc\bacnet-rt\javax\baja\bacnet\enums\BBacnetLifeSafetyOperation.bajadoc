<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetLifeSafetyOperation" name="BBacnetLifeSafetyOperation" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetLifeSafetyOperation represents the Bacnet Life Safety Operation&#xa; enumeration.&#xa; &lt;p&gt;&#xa; BBacnetLifeSafetyOperation is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Craig Gemmill</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">16 May 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;none&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;silence&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;silenceAudible&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;silenceVisual&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;reset&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;resetAlarm&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;resetFault&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unsilence&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unsilenceAudible&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unsilenceVisual&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyOperation"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyOperation"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.NONE -->
<field name="NONE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for none.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.SILENCE -->
<field name="SILENCE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for silence.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.SILENCE_AUDIBLE -->
<field name="SILENCE_AUDIBLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for silenceAudible.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.SILENCE_VISUAL -->
<field name="SILENCE_VISUAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for silenceVisual.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.RESET -->
<field name="RESET"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for reset.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.RESET_ALARM -->
<field name="RESET_ALARM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for resetAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.RESET_FAULT -->
<field name="RESET_FAULT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for resetFault.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.UNSILENCE -->
<field name="UNSILENCE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unsilence.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.UNSILENCE_AUDIBLE -->
<field name="UNSILENCE_AUDIBLE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unsilenceAudible.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.UNSILENCE_VISUAL -->
<field name="UNSILENCE_VISUAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unsilenceVisual.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.none -->
<field name="none"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyOperation"/>
<description>
BBacnetLifeSafetyOperation constant for none.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.silence -->
<field name="silence"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyOperation"/>
<description>
BBacnetLifeSafetyOperation constant for silence.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.silenceAudible -->
<field name="silenceAudible"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyOperation"/>
<description>
BBacnetLifeSafetyOperation constant for silenceAudible.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.silenceVisual -->
<field name="silenceVisual"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyOperation"/>
<description>
BBacnetLifeSafetyOperation constant for silenceVisual.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.reset -->
<field name="reset"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyOperation"/>
<description>
BBacnetLifeSafetyOperation constant for reset.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.resetAlarm -->
<field name="resetAlarm"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyOperation"/>
<description>
BBacnetLifeSafetyOperation constant for resetAlarm.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.resetFault -->
<field name="resetFault"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyOperation"/>
<description>
BBacnetLifeSafetyOperation constant for resetFault.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.unsilence -->
<field name="unsilence"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyOperation"/>
<description>
BBacnetLifeSafetyOperation constant for unsilence.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.unsilenceAudible -->
<field name="unsilenceAudible"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyOperation"/>
<description>
BBacnetLifeSafetyOperation constant for unsilenceAudible.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.unsilenceVisual -->
<field name="unsilenceVisual"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyOperation"/>
<description>
BBacnetLifeSafetyOperation constant for unsilenceVisual.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetLifeSafetyOperation"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetLifeSafetyOperation.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
