<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="axvelocity" runtimeProfile="wb" qualifiedName="javax.baja.velocity.hx.BVelocityHxView" name="BVelocityHxView" packageName="javax.baja.velocity.hx" public="true" abstract="true">
<description>
Velocity Hx View&#xa; &lt;p&gt;&#xa; An Hx View that utilizes the Velocity Engine.&#xa; &lt;p&gt;&#xa; To use, extend this class and override &#x27;getTemplateFileOrd&#x27;.
</description>
<tag name="@see">BVelocityView</tag>
<tag name="@author">g<PERSON><PERSON><PERSON></tag>
<tag name="@creation">Jan 20, 2012</tag>
<tag name="@version">1</tag>
<tag name="@since">Niagara 3.7</tag>
<extends>
<type class="javax.baja.hx.BHxView"/>
</extends>
<!-- javax.baja.velocity.hx.BVelocityHxView() -->
<constructor name="BVelocityHxView" public="true">
<description/>
</constructor>

<!-- javax.baja.velocity.hx.BVelocityHxView.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.velocity.hx.BVelocityHxView.write(javax.baja.hx.HxOp) -->
<method name="write"  public="true">
<description/>
<parameter name="op">
<type class="javax.baja.hx.HxOp"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.velocity.hx.BVelocityHxView.getTemplateFileOrd(javax.baja.hx.HxOp) -->
<method name="getTemplateFileOrd"  public="true" abstract="true">
<description>
Return the ORD to the template file for this view.&#xa; &lt;p&gt;&#xa; Normally the ORD returned is to a file embedded in a Niagara module.&#xa; For example...&#xa; &lt;pre&gt;&#xa;   module://myModule/res/myFile.vm&#xa; &lt;/pre&gt;
</description>
<parameter name="op">
<type class="javax.baja.hx.HxOp"/>
<description>
the HxOp for the current request.
</description>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
<description>
the template ORD
</description>
</return>
</method>

<!-- javax.baja.velocity.hx.BVelocityHxView.makeVelocityContext(javax.baja.hx.HxOp, javax.baja.hx.BHxProfile) -->
<method name="makeVelocityContext"  protected="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Make the VelocityContext.
</description>
<parameter name="op">
<type class="javax.baja.hx.HxOp"/>
<description>
the HxOp for the current request.
</description>
</parameter>
<parameter name="profile">
<type class="javax.baja.hx.BHxProfile"/>
<description>
the Hx Profile for the current request.
</description>
</parameter>
<return>
<type class="org.apache.velocity.VelocityContext"/>
<description>
a new Velocity Context with default elements attached.
</description>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.velocity.hx.BVelocityHxView.initVelocityContext(org.apache.velocity.VelocityContext, javax.baja.hx.HxOp, javax.baja.hx.BHxProfile) -->
<method name="initVelocityContext"  protected="true">
<description>
Initialize the VelocityContext that will be provided to the template generator.
</description>
<parameter name="context">
<type class="org.apache.velocity.VelocityContext"/>
<description>
the VelocityContext to add too.
</description>
</parameter>
<parameter name="op">
<type class="javax.baja.hx.HxOp"/>
<description>
the HxOp for the current request.
</description>
</parameter>
<parameter name="profile">
<type class="javax.baja.hx.BHxProfile"/>
<description>
the Hx Profile for the current request.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.velocity.hx.BVelocityHxView.makeDefaultVelocityContext(javax.baja.hx.HxOp, javax.baja.hx.BHxProfile) -->
<method name="makeDefaultVelocityContext"  public="true" static="true">
<description>
Make the VelocityContext.
</description>
<parameter name="op">
<type class="javax.baja.hx.HxOp"/>
<description>
the HxOp for the current request.
</description>
</parameter>
<parameter name="profile">
<type class="javax.baja.hx.BHxProfile"/>
<description>
the Hx Profile for the current request.
</description>
</parameter>
<return>
<type class="org.apache.velocity.VelocityContext"/>
<description>
a new Velocity Context with default elements attached.
</description>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.velocity.hx.BVelocityHxView.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
