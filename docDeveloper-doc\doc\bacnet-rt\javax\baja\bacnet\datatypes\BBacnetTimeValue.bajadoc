<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetTimeValue" name="BBacnetTimeValue" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BBacnetTimeValue represents a BacnetTimeValue pair.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">6 June 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<implements>
<parameterizedType class="java.lang.Comparable">
<args>
<type class="java.lang.Object"/>
</args>
</parameterizedType>
</implements>
<property name="time" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
<description>
Slot for the &lt;code&gt;time&lt;/code&gt; property.
</description>
<tag name="@see">#getTime</tag>
<tag name="@see">#setTime</tag>
</property>

<property name="value" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetAny"/>
<description>
Slot for the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#getValue</tag>
<tag name="@see">#setValue</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue() -->
<constructor name="BBacnetTimeValue" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue(javax.baja.bacnet.datatypes.BBacnetTime, javax.baja.sys.BSimple) -->
<constructor name="BBacnetTimeValue" public="true">
<parameter name="time">
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
<description/>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BSimple"/>
<description/>
</parameter>
<description>
Full constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue.getTime() -->
<method name="getTime"  public="true">
<description>
Get the &lt;code&gt;time&lt;/code&gt; property.
</description>
<tag name="@see">#time</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue.setTime(javax.baja.bacnet.datatypes.BBacnetTime) -->
<method name="setTime"  public="true">
<description>
Set the &lt;code&gt;time&lt;/code&gt; property.
</description>
<tag name="@see">#time</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue.getValue() -->
<method name="getValue"  public="true">
<description>
Get the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#value</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetAny"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue.setValue(javax.baja.bacnet.datatypes.BBacnetAny) -->
<method name="setValue"  public="true">
<description>
Set the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#value</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetAny"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<description>
Property changed. If running pass changed call up to parent.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue.compareTo(java.lang.Object) -->
<method name="compareTo"  public="true">
<description>
Compare to another BBacnetTimeValue.
</description>
<parameter name="obj">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="int"/>
<description>
a negative integer, zero, or a&#xa; positive integer as this object is less&#xa; than, equal to, or greater than the&#xa; specified object.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue.isBefore(javax.baja.bacnet.datatypes.BBacnetTimeValue) -->
<method name="isBefore"  public="true">
<description/>
<parameter name="x">
<type class="javax.baja.bacnet.datatypes.BBacnetTimeValue"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the specified time-value is before this time-value.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue.isAfter(javax.baja.bacnet.datatypes.BBacnetTimeValue) -->
<method name="isAfter"  public="true">
<description/>
<parameter name="x">
<type class="javax.baja.bacnet.datatypes.BBacnetTimeValue"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the specified time-value is after this time-value.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue.isNotBefore(javax.baja.bacnet.datatypes.BBacnetTimeValue) -->
<method name="isNotBefore"  public="true">
<description/>
<parameter name="x">
<type class="javax.baja.bacnet.datatypes.BBacnetTimeValue"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the specified time is not before this time-value.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue.isNotAfter(javax.baja.bacnet.datatypes.BBacnetTimeValue) -->
<method name="isNotAfter"  public="true">
<description/>
<parameter name="x">
<type class="javax.baja.bacnet.datatypes.BBacnetTimeValue"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the specified time is not after this time-value.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue.time -->
<field name="time"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;time&lt;/code&gt; property.
</description>
<tag name="@see">#getTime</tag>
<tag name="@see">#setTime</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue.value -->
<field name="value"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;value&lt;/code&gt; property.
</description>
<tag name="@see">#getValue</tag>
<tag name="@see">#setValue</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetTimeValue.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
