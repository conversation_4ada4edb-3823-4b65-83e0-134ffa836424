<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.BInfinityPointDeviceExt" name="BInfinityPointDeviceExt" packageName="com.tridium.andoverInfinity" public="true">
<description>
BInfinityPointDeviceExt
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.BDdfPointDeviceExt"/>
</extends>
<!-- com.tridium.andoverInfinity.BInfinityPointDeviceExt() -->
<constructor name="BInfinityPointDeviceExt" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.BInfinityPointDeviceExt.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.BInfinityPointDeviceExt.getDeviceType() -->
<method name="getDeviceType"  public="true">
<description>
Infinity device type is BInfinetDevice
</description>
<tag name="@see">javax.baja.driver.point.BPointDeviceExt#getDeviceType()</tag>
<return>
<type class="javax.baja.sys.Type"/>
<description>
&lt;code&gt;BInfinetDevice.TYPE&lt;/code&gt;
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.BInfinityPointDeviceExt.getPointFolderType() -->
<method name="getPointFolderType"  public="true">
<description>
Infinity point folder type is BInfiniytPointFolder
</description>
<tag name="@see">javax.baja.driver.point.BPointDeviceExt#getPointFolderType()</tag>
<return>
<type class="javax.baja.sys.Type"/>
<description>
&lt;code&gt;BInfinityPointFolder.TYPE&lt;/code&gt;
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.BInfinityPointDeviceExt.getProxyExtType() -->
<method name="getProxyExtType"  public="true">
<description>
Infinity proxy ext type is BInfinityProxyExt
</description>
<tag name="@see">javax.baja.driver.point.BPointDeviceExt#getProxyExtType()</tag>
<return>
<type class="javax.baja.sys.Type"/>
<description>
&lt;code&gt;BInfinityProxyExt.TYPE&lt;/code&gt;
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.BInfinityPointDeviceExt.getDiscoveryFolder() -->
<method name="getDiscoveryFolder"  public="true">
<description>
Infinity does not persist discovery objects, so override to return null
</description>
<tag name="@see">com.tridium.devDriver.discover.BIDdfDiscoveryHost#getDiscoveryFolder()</tag>
<return>
<type class="javax.baja.util.BFolder"/>
<description>
null
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.BInfinityPointDeviceExt.discoveryPreferences -->
<field name="discoveryPreferences"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;discoveryPreferences&lt;/code&gt; property.
</description>
<tag name="@see">#getDiscoveryPreferences</tag>
<tag name="@see">#setDiscoveryPreferences</tag>
</field>

<!-- com.tridium.andoverInfinity.BInfinityPointDeviceExt.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
