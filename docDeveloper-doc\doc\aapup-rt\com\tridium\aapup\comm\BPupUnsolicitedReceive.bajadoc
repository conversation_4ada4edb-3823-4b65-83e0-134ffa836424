<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.comm.BPupUnsolicitedReceive" name="BPupUnsolicitedReceive" packageName="com.tridium.aapup.comm" public="true">
<description>
This class customizes unsolicited receive handling for the&#xa; aapup driver. Most functionality is inherited from BBasicUnsolicitedReceive.
</description>
<extends>
<type class="com.tridium.basicdriver.util.BBasicUnsolicitedReceive"/>
</extends>
<implements>
<type class="com.tridium.aapup.AaPupConst"/>
</implements>
</class>
</bajadoc>
