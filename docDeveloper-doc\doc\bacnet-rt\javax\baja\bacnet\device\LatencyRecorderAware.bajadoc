<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.device.LatencyRecorderAware" name="LatencyRecorderAware" packageName="javax.baja.bacnet.device" public="true" interface="true" abstract="true" category="interface">
<description>
The LatencyRecorderAware interface&#xa; provides the mechanism to register&#xa; for latency events from BBacnetDevices.
</description>
<tag name="@author"><PERSON></tag>
<!-- javax.baja.bacnet.device.LatencyRecorderAware.addLatencyRecorder(javax.baja.bacnet.device.LatencyRecorder) -->
<method name="addLatencyRecorder"  public="true" abstract="true">
<description>
Register a LatencyRecorder to receive LatencyEvents.
</description>
<parameter name="recorder">
<type class="javax.baja.bacnet.device.LatencyRecorder"/>
<description>
to add.
</description>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.device.LatencyRecorderAware.removeLatencyRecorder(javax.baja.bacnet.device.LatencyRecorder) -->
<method name="removeLatencyRecorder"  public="true" abstract="true">
<description>
Remove a registration to stop receiving LatencyEvents.
</description>
<parameter name="recorder">
<type class="javax.baja.bacnet.device.LatencyRecorder"/>
<description>
to remove.
</description>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.device.LatencyRecorderAware.isRecordingLatency() -->
<method name="isRecordingLatency"  public="true" abstract="true">
<description>
Are there any active LatencyRecorders listening for latency&#xa; events from this device?
</description>
<return>
<type class="boolean"/>
<description>
true if latency events should be generated&#xa; false if no recorders are listening
</description>
</return>
</method>

</class>
</bajadoc>
