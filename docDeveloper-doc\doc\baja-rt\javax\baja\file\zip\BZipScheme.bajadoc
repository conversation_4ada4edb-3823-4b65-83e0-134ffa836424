<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.zip.BZipScheme" name="BZipScheme" packageName="javax.baja.file.zip" public="true">
<description>
BZipScheme manages the &#x22;zip&#x22; scheme with a ZipPath query.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">4 Jan 03</tag>
<tag name="@version">$Revision: 6$ $Date: 8/30/07 10:36:55 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.naming.BOrdScheme"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraSingleton"/>
</annotation>
<!-- javax.baja.file.zip.BZipScheme.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipScheme.parse(java.lang.String) -->
<method name="parse"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return an instance of ZipPath.
</description>
<parameter name="queryBody">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdQuery"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipScheme.resolve(javax.baja.naming.OrdTarget, javax.baja.naming.OrdQuery) -->
<method name="resolve"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="base">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<parameter name="query">
<type class="javax.baja.naming.OrdQuery"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdTarget"/>
</return>
<throws>
<type class="javax.baja.naming.SyntaxException"/>
</throws>
<throws>
<type class="javax.baja.naming.UnresolvedException"/>
</throws>
</method>

<!-- javax.baja.file.zip.BZipScheme.INSTANCE -->
<field name="INSTANCE"  public="true" static="true" final="true">
<type class="javax.baja.file.zip.BZipScheme"/>
<description/>
</field>

<!-- javax.baja.file.zip.BZipScheme.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
