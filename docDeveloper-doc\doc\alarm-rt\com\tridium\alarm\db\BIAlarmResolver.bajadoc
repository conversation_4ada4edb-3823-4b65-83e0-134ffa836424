<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.db.BIAlarmResolver" name="BIAlarmResolver" packageName="com.tridium.alarm.db" public="true" interface="true" abstract="true" category="interface">
<description>
BIAlarmResolver is an object that knows how to resolve an alarm ord.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">23 Sep 2004</tag>
<tag name="@version">$Revision: 1$ $Date: 10/1/04 3:20:57 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
</class>
</bajadoc>
