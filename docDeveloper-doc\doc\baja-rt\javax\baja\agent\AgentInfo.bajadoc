<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.agent.AgentInfo" name="AgentInfo" packageName="javax.baja.agent" public="true" interface="true" abstract="true" category="interface">
<description>
AgentInfo provides summary information for an &#xa; agent within an AgentList.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">5 May 04</tag>
<tag name="@version">$Revision: 5$ $Date: 4/13/06 1:18:25 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<!-- javax.baja.agent.AgentInfo.getInstance() -->
<method name="getInstance"  public="true" abstract="true">
<description>
Create an instance of the agent.
</description>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
</method>

<!-- javax.baja.agent.AgentInfo.getAgentId() -->
<method name="getAgentId"  public="true" abstract="true">
<description>
Get the agent id which is used to uniquely&#xa; identify it in AgentLists.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.agent.AgentInfo.getAgentType() -->
<method name="getAgentType"  public="true" abstract="true">
<description>
Get the TypeInfo for the instance which would be&#xa; returned by the &lt;code&gt;getInstance()&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.registry.TypeInfo"/>
</return>
</method>

<!-- javax.baja.agent.AgentInfo.getAppName() -->
<method name="getAppName"  public="true" abstract="true">
<description>
If this agent is associated with a specific application&#xa; then return the application name key.  Otherwise return&#xa; null if a global available agent.  Application name is&#xa; used with WbProfile and HxProfile to implement application&#xa; specific view filtering.  An example for application specific &#xa; agent declaration in module-include.xml:&#xa; &lt;pre&gt;  &#xa;   &amp;lt;type name=&#x22;CustomAlarmConsole&#x22; class=&#x22;acme.BCustomAlarmConsole&#x22;&amp;gt;&#xa;     &amp;lt;agent app=&#x22;acmeMyAppliance&#x22;&amp;gt;&#xa;       &amp;lt;on type=&#x22;alarm:ConsoleRecipient&#x22;/&amp;gt;&#xa;     &amp;lt;/agent&amp;gt;&#xa;   &amp;lt;/type&amp;gt;&#xa; &lt;/pre&gt;
</description>
<tag name="@since">Niagara 3.1</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.agent.AgentInfo.getAgentOn() -->
<method name="getAgentOn"  public="true" abstract="true">
<description>
Get an array of the TypeInfos which this&#xa; agent is directly registered on.
</description>
<return>
<type class="javax.baja.registry.TypeInfo" dimension="1"/>
</return>
</method>

<!-- javax.baja.agent.AgentInfo.getDisplayName(javax.baja.sys.Context) -->
<method name="getDisplayName"  public="true" abstract="true">
<description>
Get the display name for the agent.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.agent.AgentInfo.getIcon(javax.baja.sys.Context) -->
<method name="getIcon"  public="true" abstract="true">
<description>
Get the icon for the agent.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.agent.AgentInfo.getRequiredPermissions() -->
<method name="getRequiredPermissions"  public="true" abstract="true">
<description>
Get the permissions required to use this agent.
</description>
<return>
<type class="javax.baja.security.BPermissions"/>
</return>
</method>

</class>
</bajadoc>
