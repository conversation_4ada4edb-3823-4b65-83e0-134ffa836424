<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="alarmOrion" runtimeProfile="rt" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>Niagara Alarm Orion Module</description>
<package name="javax.baja.alarmOrion"/>
<package name="com.tridium.alarmOrion.transactions"/>
<package name="com.tridium.alarmOrion.conversion"/>
<package name="com.tridium.alarmOrion.conversion.file"/>
<package name="com.tridium.alarmOrion.conversion.rdb"/>
<package name="com.tridium.alarmOrion"/>
<class packageName="com.tridium.alarmOrion.conversion" name="BAlarmConversion"><description>This is an abstract class that creates a common component&#xa; interface for alarm conversion objects.</description></class>
<class packageName="com.tridium.alarmOrion.conversion" name="BAlarmConversionJob"><description>This is an abstract class that creates a common interface &#xa; for alarm conversion jobs.</description></class>
<class packageName="com.tridium.alarmOrion.conversion.file" name="BFileAlarmConversion"></class>
<class packageName="com.tridium.alarmOrion.conversion.file" name="BFileAlarmConversionJob"><description>Converts the default file-based alarm database to an Orion&#xa; database.</description></class>
<class packageName="javax.baja.alarmOrion" name="BOrionAlarmClass"><description>The representation of an alarm class within the orion database.</description></class>
<class packageName="javax.baja.alarmOrion" name="BOrionAlarmDatabase"><description>An Orion based implementation of an alarm database.</description></class>
<class packageName="javax.baja.alarmOrion" name="BOrionAlarmFacetName"><description>The representation of an alarm data facet name within the orion database.</description></class>
<class packageName="javax.baja.alarmOrion" name="BOrionAlarmFacetValue"><description>The representation of an alarm data facet value within the orion database.</description></class>
<class packageName="com.tridium.alarmOrion" name="BOrionAlarmNameFactory"><description>BOrionAlarmNameFactory provides custom naming for tables&#xa; in the alarmOrionApp.</description></class>
<class packageName="javax.baja.alarmOrion" name="BOrionAlarmRecord"><description>The representation of an alarm record within the orion database.</description></class>
<class packageName="javax.baja.alarmOrion" name="BOrionAlarmService"><description>An Orion based implementation of an alarm service.</description></class>
<class packageName="javax.baja.alarmOrion" name="BOrionAlarmSource"></class>
<class packageName="javax.baja.alarmOrion" name="BOrionAlarmSourceOrder"></class>
<class packageName="com.tridium.alarmOrion.transactions" name="BOrionAlarmTransactionStatistics"></class>
<class packageName="com.tridium.alarmOrion.conversion.rdb" name="BRdbAlarmConversion"></class>
<class packageName="com.tridium.alarmOrion.conversion.rdb" name="BRdbAlarmConversionJob"><description>Converts the rdb alarm database to an Orion&#xa; database.</description></class>
<class packageName="javax.baja.alarmOrion" name="OrionAlarmDbConnection"><description>Connection to a BOrionAlarmDatabase.</description></class>
</module>
</bajadoc>
