<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.query.style.BNamedObjects" name="BNamedObjects" packageName="com.tridiumx.jsonToolkit.outbound.schema.query.style" public="true">
<description>
A query result writer which renders an object containing a named object for each row.&#xa; The name of each inner object is the first column value.&#xa; Be wary of duplicate keys with this option!&#xa;&#xa; {&#xa;   &#x27;a1&#x27;: {&#xa;     &#x27;b&#x27; : b1&#xa;     &#x27;c&#x27; : c1&#xa;   },&#xa;   &#x27;a2&#x27;: {&#xa;     &#x27;b&#x27; : b2&#xa;     &#x27;c&#x27; : c2&#xa;   }&#xa; }
</description>
<tag name="@author">Nick <PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.style.BQueryResultWriter"/>
</extends>
<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BNamedObjects() -->
<constructor name="BNamedObjects" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BNamedObjects.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BNamedObjects.previewText() -->
<method name="previewText"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BString"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BNamedObjects.appendJson(com.tridium.json.JSONWriter, com.tridiumx.jsonToolkit.outbound.schema.query.QueryResultHolder) -->
<method name="appendJson"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="json">
<type class="com.tridium.json.JSONWriter"/>
</parameter>
<parameter name="result">
<type class="com.tridiumx.jsonToolkit.outbound.schema.query.QueryResultHolder"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.query.style.BNamedObjects.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
