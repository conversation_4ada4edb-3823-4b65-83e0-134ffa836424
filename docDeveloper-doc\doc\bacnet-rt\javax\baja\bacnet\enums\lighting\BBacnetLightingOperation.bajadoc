<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation" name="BBacnetLightingOperation" packageName="javax.baja.bacnet.enums.lighting" public="true" final="true">
<description>
BBacnetLightingOperation represents the&#xa; BACnet Lighting Operation enumeration.&#xa; &lt;p&gt;&#xa; BBacnetLightingOperation is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Joseph Chandler</tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;none&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;fadeTo&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;rampTo&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;stepUp&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;stepDown&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;stepOn&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;stepOff&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;warn&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;warnOff&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;warnRelinquish&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;stop&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.NONE -->
<field name="NONE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for none.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.FADE_TO -->
<field name="FADE_TO"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for fadeTo.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.RAMP_TO -->
<field name="RAMP_TO"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for rampTo.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.STEP_UP -->
<field name="STEP_UP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for stepUp.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.STEP_DOWN -->
<field name="STEP_DOWN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for stepDown.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.STEP_ON -->
<field name="STEP_ON"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for stepOn.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.STEP_OFF -->
<field name="STEP_OFF"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for stepOff.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.WARN -->
<field name="WARN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for warn.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.WARN_OFF -->
<field name="WARN_OFF"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for warnOff.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.WARN_RELINQUISH -->
<field name="WARN_RELINQUISH"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for warnRelinquish.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.STOP -->
<field name="STOP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for stop.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.none -->
<field name="none"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
<description>
BBacnetLightingOperation constant for none.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.fadeTo -->
<field name="fadeTo"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
<description>
BBacnetLightingOperation constant for fadeTo.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.rampTo -->
<field name="rampTo"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
<description>
BBacnetLightingOperation constant for rampTo.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.stepUp -->
<field name="stepUp"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
<description>
BBacnetLightingOperation constant for stepUp.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.stepDown -->
<field name="stepDown"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
<description>
BBacnetLightingOperation constant for stepDown.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.stepOn -->
<field name="stepOn"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
<description>
BBacnetLightingOperation constant for stepOn.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.stepOff -->
<field name="stepOff"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
<description>
BBacnetLightingOperation constant for stepOff.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.warn -->
<field name="warn"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
<description>
BBacnetLightingOperation constant for warn.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.warnOff -->
<field name="warnOff"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
<description>
BBacnetLightingOperation constant for warnOff.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.warnRelinquish -->
<field name="warnRelinquish"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
<description>
BBacnetLightingOperation constant for warnRelinquish.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.stop -->
<field name="stop"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
<description>
BBacnetLightingOperation constant for stop.
</description>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.lighting.BBacnetLightingOperation"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.lighting.BBacnetLightingOperation.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
