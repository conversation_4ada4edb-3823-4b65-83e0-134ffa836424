<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="javax.baja.bacnet.virtual">
<description/>
<class packageName="javax.baja.bacnet.virtual" name="BacnetVirtualUtil"><description>BacnetVirtualUtil provides common utility functions used by&#xa; the BACnet virtual point framework.</description></class>
<class packageName="javax.baja.bacnet.virtual" name="BBacnetVirtualArray"/>
<class packageName="javax.baja.bacnet.virtual" name="BBacnetVirtualComponent"><description>BBacnetVirtualComponent is the implementation of BVirtualComponent for the&#xa; BACnet driver.</description></class>
<class packageName="javax.baja.bacnet.virtual" name="BBacnetVirtualGateway"><description>BBacnetVirtualGateway is the gateway to the BACnet virtual component&#xa; space.</description></class>
<class packageName="javax.baja.bacnet.virtual" name="BBacnetVirtualObject"><description>BBacnetVirtualObject is the virtual representation of a BACnet&#xa; object.</description></class>
<class packageName="javax.baja.bacnet.virtual" name="BBacnetVirtualProperty"><description>BBacnetVirtualProperty is the virtual representation of one&#xa; property of a BBacnetVirtualObject.</description></class>
<class packageName="javax.baja.bacnet.virtual" name="BLocalBacnetVirtualGateway"/>
<class packageName="javax.baja.bacnet.virtual" name="BLocalBacnetVirtualObject"/>
<class packageName="javax.baja.bacnet.virtual" name="BLocalBacnetVirtualProperty"/>
<class packageName="javax.baja.bacnet.virtual" name="BVirtualPropertyWrite"/>
<class packageName="javax.baja.bacnet.virtual" name="LocalBacnetVirtualPoll"><description>LocalBacnetVirtualPoll&#xa; This class is used for polling local virtual properties pointing to local&#xa; BACnet export properties.</description></class>
</package>
</bajadoc>
