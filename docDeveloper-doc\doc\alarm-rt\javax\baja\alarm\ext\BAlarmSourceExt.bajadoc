<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.ext.BAlarmSourceExt" name="BAlarmSourceExt" packageName="javax.baja.alarm.ext" public="true">
<description>
BAlarmSourceExt is the abstract superclass of all&#xa; Baja control alarming algorithms.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">9 Nov 00</tag>
<tag name="@version">$Revision: 141$ $Date: 5/18/11 10:46:40 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.control.BPointExtension"/>
</extends>
<implements>
<type class="javax.baja.alarm.BIAlarmSource"/>
</implements>
<implements>
<type class="javax.baja.alarm.ext.BIAlarmMessages"/>
</implements>
<property name="alarmInhibit" flags="">
<type class="javax.baja.status.BStatusBoolean"/>
<description>
Slot for the &lt;code&gt;alarmInhibit&lt;/code&gt; property.&#xa; Inhibits alarm generation.
</description>
<tag name="@see">#getAlarmInhibit</tag>
<tag name="@see">#setAlarmInhibit</tag>
</property>

<property name="inhibitTime" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;inhibitTime&lt;/code&gt; property.&#xa; Time between alarmInhibit:true-&amp;gt;false and alarmInhibit being lifted.&#xa; The reverse is 3x this delay for discrete points and 0 for numeric points.
</description>
<tag name="@see">#getInhibitTime</tag>
<tag name="@see">#setInhibitTime</tag>
</property>

<property name="alarmState" flags="rd">
<type class="javax.baja.alarm.ext.BAlarmState"/>
<description>
Slot for the &lt;code&gt;alarmState&lt;/code&gt; property.&#xa; Shows the object&#x27;s current alarm state.
</description>
<tag name="@see">#getAlarmState</tag>
<tag name="@see">#setAlarmState</tag>
</property>

<property name="timeDelay" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;timeDelay&lt;/code&gt; property.&#xa; Minimum time period that an alarm condition must exist before the object alarms.
</description>
<tag name="@see">#getTimeDelay</tag>
<tag name="@see">#setTimeDelay</tag>
</property>

<property name="timeDelayToNormal" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;timeDelayToNormal&lt;/code&gt; property.&#xa; Minimum time period that a normal condition must exist before the object returns to normal.
</description>
<tag name="@see">#getTimeDelayToNormal</tag>
<tag name="@see">#setTimeDelayToNormal</tag>
</property>

<property name="alarmEnable" flags="">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
<description>
Slot for the &lt;code&gt;alarmEnable&lt;/code&gt; property.&#xa; Flags that define the types of alarm transitions for this object that will generate alarm.
</description>
<tag name="@see">#getAlarmEnable</tag>
<tag name="@see">#setAlarmEnable</tag>
</property>

<property name="ackedTransitions" flags="hrd">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
<description>
Slot for the &lt;code&gt;ackedTransitions&lt;/code&gt; property.&#xa; Flags, that when cleared, indicate that an unacknowledged alarm transition has occurred.
</description>
<tag name="@see">#getAckedTransitions</tag>
<tag name="@see">#setAckedTransitions</tag>
</property>

<property name="toOffnormalTimes" flags="r">
<type class="javax.baja.alarm.ext.BAlarmTimestamps"/>
<description>
Slot for the &lt;code&gt;toOffnormalTimes&lt;/code&gt; property.&#xa; eventTime, normalTime, ackTime and count for last to offnormal event.
</description>
<tag name="@see">#getToOffnormalTimes</tag>
<tag name="@see">#setToOffnormalTimes</tag>
</property>

<property name="toFaultTimes" flags="r">
<type class="javax.baja.alarm.ext.BAlarmTimestamps"/>
<description>
Slot for the &lt;code&gt;toFaultTimes&lt;/code&gt; property.&#xa; eventTime, normalTime,  ackTime and count for last to fault event.
</description>
<tag name="@see">#getToFaultTimes</tag>
<tag name="@see">#setToFaultTimes</tag>
</property>

<property name="timeInCurrentState" flags="tr">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;timeInCurrentState&lt;/code&gt; property.&#xa; the time that this point has been in it&#x27;s current state&#xa; update every 10sec.
</description>
<tag name="@see">#getTimeInCurrentState</tag>
<tag name="@see">#setTimeInCurrentState</tag>
</property>

<property name="sourceName" flags="">
<type class="javax.baja.util.BFormat"/>
<description>
Slot for the &lt;code&gt;sourceName&lt;/code&gt; property.&#xa; Text descriptor for the source name of the alarm. Uses BFormat, but currently only supports lexicons.
</description>
<tag name="@see">#getSourceName</tag>
<tag name="@see">#setSourceName</tag>
</property>

<property name="toFaultText" flags="">
<type class="javax.baja.util.BFormat"/>
<description>
Slot for the &lt;code&gt;toFaultText&lt;/code&gt; property.&#xa; Text descriptor included in a to-fault alarm for this object. Uses BFormat.
</description>
<tag name="@see">#getToFaultText</tag>
<tag name="@see">#setToFaultText</tag>
</property>

<property name="toOffnormalText" flags="">
<type class="javax.baja.util.BFormat"/>
<description>
Slot for the &lt;code&gt;toOffnormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-offnormal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#getToOffnormalText</tag>
<tag name="@see">#setToOffnormalText</tag>
</property>

<property name="toNormalText" flags="">
<type class="javax.baja.util.BFormat"/>
<description>
Slot for the &lt;code&gt;toNormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-normal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#getToNormalText</tag>
<tag name="@see">#setToNormalText</tag>
</property>

<property name="hyperlinkOrd" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;hyperlinkOrd&lt;/code&gt; property.&#xa; Ord to link to for more information about this alarm.
</description>
<tag name="@see">#getHyperlinkOrd</tag>
<tag name="@see">#setHyperlinkOrd</tag>
</property>

<property name="soundFile" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;soundFile&lt;/code&gt; property.&#xa; Sound to play when the alarm comes into the alarm console.&#xa; Sound must be available on the client.
</description>
<tag name="@see">#getSoundFile</tag>
<tag name="@see">#setSoundFile</tag>
</property>

<property name="alarmIcon" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;alarmIcon&lt;/code&gt; property.&#xa; Icon to display for this alarm in the alarm console&#xa; Icon must be available on the client.
</description>
<tag name="@see">#getAlarmIcon</tag>
<tag name="@see">#setAlarmIcon</tag>
</property>

<property name="alarmInstructions" flags="">
<type class="javax.baja.alarm.BAlarmInstructions"/>
<description>
Slot for the &lt;code&gt;alarmInstructions&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmInstructions</tag>
<tag name="@see">#setAlarmInstructions</tag>
</property>

<property name="faultAlgorithm" flags="">
<type class="javax.baja.alarm.ext.BFaultAlgorithm"/>
<description>
Slot for the &lt;code&gt;faultAlgorithm&lt;/code&gt; property.&#xa; This is the fault algorithm used for this object.
</description>
<tag name="@see">#getFaultAlgorithm</tag>
<tag name="@see">#setFaultAlgorithm</tag>
</property>

<property name="offnormalAlgorithm" flags="">
<type class="javax.baja.alarm.ext.BOffnormalAlgorithm"/>
<description>
Slot for the &lt;code&gt;offnormalAlgorithm&lt;/code&gt; property.&#xa; This is the offnormal algorithm used for this object.
</description>
<tag name="@see">#getOffnormalAlgorithm</tag>
<tag name="@see">#setOffnormalAlgorithm</tag>
</property>

<property name="alarmClass" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;alarmClass&lt;/code&gt; property.&#xa; This is the alarm class used for routing this alarm.
</description>
<tag name="@see">#getAlarmClass</tag>
<tag name="@see">#setAlarmClass</tag>
</property>

<property name="metaData" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;metaData&lt;/code&gt; property.&#xa; Additional user defined data for this alarm.
</description>
<tag name="@see">#getMetaData</tag>
<tag name="@see">#setMetaData</tag>
</property>

<property name="status" flags="trh">
<type class="javax.baja.status.BStatus"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.&#xa; the status&#x27;s that propagate to the control point&#x27;s status
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</property>

<action name="timerExpired" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;timerExpired&lt;/code&gt; action.
</description>
<tag name="@see">#timerExpired()</tag>
</action>

<action name="inhibitTimerExpired" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;inhibitTimerExpired&lt;/code&gt; action.
</description>
<tag name="@see">#inhibitTimerExpired()</tag>
</action>

<action name="ackAlarm" flags="h">
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
<description>
Slot for the &lt;code&gt;ackAlarm&lt;/code&gt; action.&#xa; Acknowledge the alarm matching this ack request
</description>
<tag name="@see">#ackAlarm(BAlarmRecord parameter)</tag>
</action>

<topic name="toNormal" flags="">
<eventType>
<type class="javax.baja.alarm.BAlarmRecord"/>
</eventType><description>
Slot for the &lt;code&gt;toNormal&lt;/code&gt; topic.
</description>
<tag name="@see">#fireToNormal</tag>
</topic>

<topic name="toOffnormal" flags="">
<eventType>
<type class="javax.baja.alarm.BAlarmRecord"/>
</eventType><description>
Slot for the &lt;code&gt;toOffnormal&lt;/code&gt; topic.
</description>
<tag name="@see">#fireToOffnormal</tag>
</topic>

<topic name="toFault" flags="">
<eventType>
<type class="javax.baja.alarm.BAlarmRecord"/>
</eventType><description>
Slot for the &lt;code&gt;toFault&lt;/code&gt; topic.
</description>
<tag name="@see">#fireToFault</tag>
</topic>

<!-- javax.baja.alarm.ext.BAlarmSourceExt() -->
<constructor name="BAlarmSourceExt" public="true">
<description/>
</constructor>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getAlarmInhibit() -->
<method name="getAlarmInhibit"  public="true">
<description>
Get the &lt;code&gt;alarmInhibit&lt;/code&gt; property.&#xa; Inhibits alarm generation.
</description>
<tag name="@see">#alarmInhibit</tag>
<return>
<type class="javax.baja.status.BStatusBoolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setAlarmInhibit(javax.baja.status.BStatusBoolean) -->
<method name="setAlarmInhibit"  public="true">
<description>
Set the &lt;code&gt;alarmInhibit&lt;/code&gt; property.&#xa; Inhibits alarm generation.
</description>
<tag name="@see">#alarmInhibit</tag>
<parameter name="v">
<type class="javax.baja.status.BStatusBoolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getInhibitTime() -->
<method name="getInhibitTime"  public="true">
<description>
Get the &lt;code&gt;inhibitTime&lt;/code&gt; property.&#xa; Time between alarmInhibit:true-&amp;gt;false and alarmInhibit being lifted.&#xa; The reverse is 3x this delay for discrete points and 0 for numeric points.
</description>
<tag name="@see">#inhibitTime</tag>
<return>
<type class="javax.baja.sys.BRelTime"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setInhibitTime(javax.baja.sys.BRelTime) -->
<method name="setInhibitTime"  public="true">
<description>
Set the &lt;code&gt;inhibitTime&lt;/code&gt; property.&#xa; Time between alarmInhibit:true-&amp;gt;false and alarmInhibit being lifted.&#xa; The reverse is 3x this delay for discrete points and 0 for numeric points.
</description>
<tag name="@see">#inhibitTime</tag>
<parameter name="v">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getAlarmState() -->
<method name="getAlarmState"  public="true">
<description>
Get the &lt;code&gt;alarmState&lt;/code&gt; property.&#xa; Shows the object&#x27;s current alarm state.
</description>
<tag name="@see">#alarmState</tag>
<return>
<type class="javax.baja.alarm.ext.BAlarmState"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setAlarmState(javax.baja.alarm.ext.BAlarmState) -->
<method name="setAlarmState"  public="true">
<description>
Set the &lt;code&gt;alarmState&lt;/code&gt; property.&#xa; Shows the object&#x27;s current alarm state.
</description>
<tag name="@see">#alarmState</tag>
<parameter name="v">
<type class="javax.baja.alarm.ext.BAlarmState"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getTimeDelay() -->
<method name="getTimeDelay"  public="true">
<description>
Get the &lt;code&gt;timeDelay&lt;/code&gt; property.&#xa; Minimum time period that an alarm condition must exist before the object alarms.
</description>
<tag name="@see">#timeDelay</tag>
<return>
<type class="javax.baja.sys.BRelTime"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setTimeDelay(javax.baja.sys.BRelTime) -->
<method name="setTimeDelay"  public="true">
<description>
Set the &lt;code&gt;timeDelay&lt;/code&gt; property.&#xa; Minimum time period that an alarm condition must exist before the object alarms.
</description>
<tag name="@see">#timeDelay</tag>
<parameter name="v">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getTimeDelayToNormal() -->
<method name="getTimeDelayToNormal"  public="true">
<description>
Get the &lt;code&gt;timeDelayToNormal&lt;/code&gt; property.&#xa; Minimum time period that a normal condition must exist before the object returns to normal.
</description>
<tag name="@see">#timeDelayToNormal</tag>
<return>
<type class="javax.baja.sys.BRelTime"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setTimeDelayToNormal(javax.baja.sys.BRelTime) -->
<method name="setTimeDelayToNormal"  public="true">
<description>
Set the &lt;code&gt;timeDelayToNormal&lt;/code&gt; property.&#xa; Minimum time period that a normal condition must exist before the object returns to normal.
</description>
<tag name="@see">#timeDelayToNormal</tag>
<parameter name="v">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getAlarmEnable() -->
<method name="getAlarmEnable"  public="true">
<description>
Get the &lt;code&gt;alarmEnable&lt;/code&gt; property.&#xa; Flags that define the types of alarm transitions for this object that will generate alarm.
</description>
<tag name="@see">#alarmEnable</tag>
<return>
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setAlarmEnable(javax.baja.alarm.BAlarmTransitionBits) -->
<method name="setAlarmEnable"  public="true">
<description>
Set the &lt;code&gt;alarmEnable&lt;/code&gt; property.&#xa; Flags that define the types of alarm transitions for this object that will generate alarm.
</description>
<tag name="@see">#alarmEnable</tag>
<parameter name="v">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getAckedTransitions() -->
<method name="getAckedTransitions"  public="true">
<description>
Get the &lt;code&gt;ackedTransitions&lt;/code&gt; property.&#xa; Flags, that when cleared, indicate that an unacknowledged alarm transition has occurred.
</description>
<tag name="@see">#ackedTransitions</tag>
<return>
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setAckedTransitions(javax.baja.alarm.BAlarmTransitionBits) -->
<method name="setAckedTransitions"  public="true">
<description>
Set the &lt;code&gt;ackedTransitions&lt;/code&gt; property.&#xa; Flags, that when cleared, indicate that an unacknowledged alarm transition has occurred.
</description>
<tag name="@see">#ackedTransitions</tag>
<parameter name="v">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getToOffnormalTimes() -->
<method name="getToOffnormalTimes"  public="true">
<description>
Get the &lt;code&gt;toOffnormalTimes&lt;/code&gt; property.&#xa; eventTime, normalTime, ackTime and count for last to offnormal event.
</description>
<tag name="@see">#toOffnormalTimes</tag>
<return>
<type class="javax.baja.alarm.ext.BAlarmTimestamps"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setToOffnormalTimes(javax.baja.alarm.ext.BAlarmTimestamps) -->
<method name="setToOffnormalTimes"  public="true">
<description>
Set the &lt;code&gt;toOffnormalTimes&lt;/code&gt; property.&#xa; eventTime, normalTime, ackTime and count for last to offnormal event.
</description>
<tag name="@see">#toOffnormalTimes</tag>
<parameter name="v">
<type class="javax.baja.alarm.ext.BAlarmTimestamps"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getToFaultTimes() -->
<method name="getToFaultTimes"  public="true">
<description>
Get the &lt;code&gt;toFaultTimes&lt;/code&gt; property.&#xa; eventTime, normalTime,  ackTime and count for last to fault event.
</description>
<tag name="@see">#toFaultTimes</tag>
<return>
<type class="javax.baja.alarm.ext.BAlarmTimestamps"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setToFaultTimes(javax.baja.alarm.ext.BAlarmTimestamps) -->
<method name="setToFaultTimes"  public="true">
<description>
Set the &lt;code&gt;toFaultTimes&lt;/code&gt; property.&#xa; eventTime, normalTime,  ackTime and count for last to fault event.
</description>
<tag name="@see">#toFaultTimes</tag>
<parameter name="v">
<type class="javax.baja.alarm.ext.BAlarmTimestamps"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getTimeInCurrentState() -->
<method name="getTimeInCurrentState"  public="true">
<description>
Get the &lt;code&gt;timeInCurrentState&lt;/code&gt; property.&#xa; the time that this point has been in it&#x27;s current state&#xa; update every 10sec.
</description>
<tag name="@see">#timeInCurrentState</tag>
<return>
<type class="javax.baja.sys.BRelTime"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setTimeInCurrentState(javax.baja.sys.BRelTime) -->
<method name="setTimeInCurrentState"  public="true">
<description>
Set the &lt;code&gt;timeInCurrentState&lt;/code&gt; property.&#xa; the time that this point has been in it&#x27;s current state&#xa; update every 10sec.
</description>
<tag name="@see">#timeInCurrentState</tag>
<parameter name="v">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getSourceName() -->
<method name="getSourceName"  public="true">
<description>
Get the &lt;code&gt;sourceName&lt;/code&gt; property.&#xa; Text descriptor for the source name of the alarm. Uses BFormat, but currently only supports lexicons.
</description>
<tag name="@see">#sourceName</tag>
<return>
<type class="javax.baja.util.BFormat"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setSourceName(javax.baja.util.BFormat) -->
<method name="setSourceName"  public="true">
<description>
Set the &lt;code&gt;sourceName&lt;/code&gt; property.&#xa; Text descriptor for the source name of the alarm. Uses BFormat, but currently only supports lexicons.
</description>
<tag name="@see">#sourceName</tag>
<parameter name="v">
<type class="javax.baja.util.BFormat"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getToFaultText() -->
<method name="getToFaultText"  public="true">
<description>
Get the &lt;code&gt;toFaultText&lt;/code&gt; property.&#xa; Text descriptor included in a to-fault alarm for this object. Uses BFormat.
</description>
<tag name="@see">#toFaultText</tag>
<return>
<type class="javax.baja.util.BFormat"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setToFaultText(javax.baja.util.BFormat) -->
<method name="setToFaultText"  public="true">
<description>
Set the &lt;code&gt;toFaultText&lt;/code&gt; property.&#xa; Text descriptor included in a to-fault alarm for this object. Uses BFormat.
</description>
<tag name="@see">#toFaultText</tag>
<parameter name="v">
<type class="javax.baja.util.BFormat"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getToOffnormalText() -->
<method name="getToOffnormalText"  public="true">
<description>
Get the &lt;code&gt;toOffnormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-offnormal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#toOffnormalText</tag>
<return>
<type class="javax.baja.util.BFormat"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setToOffnormalText(javax.baja.util.BFormat) -->
<method name="setToOffnormalText"  public="true">
<description>
Set the &lt;code&gt;toOffnormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-offnormal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#toOffnormalText</tag>
<parameter name="v">
<type class="javax.baja.util.BFormat"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getToNormalText() -->
<method name="getToNormalText"  public="true">
<description>
Get the &lt;code&gt;toNormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-normal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#toNormalText</tag>
<return>
<type class="javax.baja.util.BFormat"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setToNormalText(javax.baja.util.BFormat) -->
<method name="setToNormalText"  public="true">
<description>
Set the &lt;code&gt;toNormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-normal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#toNormalText</tag>
<parameter name="v">
<type class="javax.baja.util.BFormat"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getHyperlinkOrd() -->
<method name="getHyperlinkOrd"  public="true">
<description>
Get the &lt;code&gt;hyperlinkOrd&lt;/code&gt; property.&#xa; Ord to link to for more information about this alarm.
</description>
<tag name="@see">#hyperlinkOrd</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setHyperlinkOrd(javax.baja.naming.BOrd) -->
<method name="setHyperlinkOrd"  public="true">
<description>
Set the &lt;code&gt;hyperlinkOrd&lt;/code&gt; property.&#xa; Ord to link to for more information about this alarm.
</description>
<tag name="@see">#hyperlinkOrd</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getSoundFile() -->
<method name="getSoundFile"  public="true">
<description>
Get the &lt;code&gt;soundFile&lt;/code&gt; property.&#xa; Sound to play when the alarm comes into the alarm console.&#xa; Sound must be available on the client.
</description>
<tag name="@see">#soundFile</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setSoundFile(javax.baja.naming.BOrd) -->
<method name="setSoundFile"  public="true">
<description>
Set the &lt;code&gt;soundFile&lt;/code&gt; property.&#xa; Sound to play when the alarm comes into the alarm console.&#xa; Sound must be available on the client.
</description>
<tag name="@see">#soundFile</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getAlarmIcon() -->
<method name="getAlarmIcon"  public="true">
<description>
Get the &lt;code&gt;alarmIcon&lt;/code&gt; property.&#xa; Icon to display for this alarm in the alarm console&#xa; Icon must be available on the client.
</description>
<tag name="@see">#alarmIcon</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setAlarmIcon(javax.baja.naming.BOrd) -->
<method name="setAlarmIcon"  public="true">
<description>
Set the &lt;code&gt;alarmIcon&lt;/code&gt; property.&#xa; Icon to display for this alarm in the alarm console&#xa; Icon must be available on the client.
</description>
<tag name="@see">#alarmIcon</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getAlarmInstructions() -->
<method name="getAlarmInstructions"  public="true">
<description>
Get the &lt;code&gt;alarmInstructions&lt;/code&gt; property.
</description>
<tag name="@see">#alarmInstructions</tag>
<return>
<type class="javax.baja.alarm.BAlarmInstructions"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setAlarmInstructions(javax.baja.alarm.BAlarmInstructions) -->
<method name="setAlarmInstructions"  public="true">
<description>
Set the &lt;code&gt;alarmInstructions&lt;/code&gt; property.
</description>
<tag name="@see">#alarmInstructions</tag>
<parameter name="v">
<type class="javax.baja.alarm.BAlarmInstructions"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getFaultAlgorithm() -->
<method name="getFaultAlgorithm"  public="true">
<description>
Get the &lt;code&gt;faultAlgorithm&lt;/code&gt; property.&#xa; This is the fault algorithm used for this object.
</description>
<tag name="@see">#faultAlgorithm</tag>
<return>
<type class="javax.baja.alarm.ext.BFaultAlgorithm"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setFaultAlgorithm(javax.baja.alarm.ext.BFaultAlgorithm) -->
<method name="setFaultAlgorithm"  public="true">
<description>
Set the &lt;code&gt;faultAlgorithm&lt;/code&gt; property.&#xa; This is the fault algorithm used for this object.
</description>
<tag name="@see">#faultAlgorithm</tag>
<parameter name="v">
<type class="javax.baja.alarm.ext.BFaultAlgorithm"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getOffnormalAlgorithm() -->
<method name="getOffnormalAlgorithm"  public="true">
<description>
Get the &lt;code&gt;offnormalAlgorithm&lt;/code&gt; property.&#xa; This is the offnormal algorithm used for this object.
</description>
<tag name="@see">#offnormalAlgorithm</tag>
<return>
<type class="javax.baja.alarm.ext.BOffnormalAlgorithm"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setOffnormalAlgorithm(javax.baja.alarm.ext.BOffnormalAlgorithm) -->
<method name="setOffnormalAlgorithm"  public="true">
<description>
Set the &lt;code&gt;offnormalAlgorithm&lt;/code&gt; property.&#xa; This is the offnormal algorithm used for this object.
</description>
<tag name="@see">#offnormalAlgorithm</tag>
<parameter name="v">
<type class="javax.baja.alarm.ext.BOffnormalAlgorithm"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getAlarmClass() -->
<method name="getAlarmClass"  public="true">
<description>
Get the &lt;code&gt;alarmClass&lt;/code&gt; property.&#xa; This is the alarm class used for routing this alarm.
</description>
<tag name="@see">#alarmClass</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setAlarmClass(java.lang.String) -->
<method name="setAlarmClass"  public="true">
<description>
Set the &lt;code&gt;alarmClass&lt;/code&gt; property.&#xa; This is the alarm class used for routing this alarm.
</description>
<tag name="@see">#alarmClass</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getMetaData() -->
<method name="getMetaData"  public="true">
<description>
Get the &lt;code&gt;metaData&lt;/code&gt; property.&#xa; Additional user defined data for this alarm.
</description>
<tag name="@see">#metaData</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setMetaData(javax.baja.sys.BFacets) -->
<method name="setMetaData"  public="true">
<description>
Set the &lt;code&gt;metaData&lt;/code&gt; property.&#xa; Additional user defined data for this alarm.
</description>
<tag name="@see">#metaData</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getStatus() -->
<method name="getStatus"  public="true">
<description>
Get the &lt;code&gt;status&lt;/code&gt; property.&#xa; the status&#x27;s that propagate to the control point&#x27;s status
</description>
<tag name="@see">#status</tag>
<return>
<type class="javax.baja.status.BStatus"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.setStatus(javax.baja.status.BStatus) -->
<method name="setStatus"  public="true">
<description>
Set the &lt;code&gt;status&lt;/code&gt; property.&#xa; the status&#x27;s that propagate to the control point&#x27;s status
</description>
<tag name="@see">#status</tag>
<parameter name="v">
<type class="javax.baja.status.BStatus"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.timerExpired() -->
<method name="timerExpired"  public="true">
<description>
Invoke the &lt;code&gt;timerExpired&lt;/code&gt; action.
</description>
<tag name="@see">#timerExpired</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.inhibitTimerExpired() -->
<method name="inhibitTimerExpired"  public="true">
<description>
Invoke the &lt;code&gt;inhibitTimerExpired&lt;/code&gt; action.
</description>
<tag name="@see">#inhibitTimerExpired</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.ackAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="ackAlarm"  public="true">
<description>
Invoke the &lt;code&gt;ackAlarm&lt;/code&gt; action.&#xa; Acknowledge the alarm matching this ack request
</description>
<tag name="@see">#ackAlarm</tag>
<parameter name="parameter">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.fireToOffnormal(javax.baja.alarm.BAlarmRecord) -->
<method name="fireToOffnormal"  public="true">
<description>
Fire an event for the &lt;code&gt;toOffnormal&lt;/code&gt; topic.
</description>
<tag name="@see">#toOffnormal</tag>
<parameter name="event">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.fireToFault(javax.baja.alarm.BAlarmRecord) -->
<method name="fireToFault"  public="true">
<description>
Fire an event for the &lt;code&gt;toFault&lt;/code&gt; topic.
</description>
<tag name="@see">#toFault</tag>
<parameter name="event">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.started() -->
<method name="started"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Callback for when the extension is started.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.atSteadyState() -->
<method name="atSteadyState"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.requiresPointSubscription() -->
<method name="requiresPointSubscription"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
An alarm extension requires its point to be subscribed&#xa; whenever it is enabled.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.checkPointSubscription() -->
<method name="checkPointSubscription"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
A BAlarmSourceExt&#x27;s parent must be a BControlPoint.  In&#xa; addition, fault / offnormal have special parentage requirements&#xa; that are checked at this point.
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.isSiblingLegal(javax.baja.sys.BComponent) -->
<method name="isSiblingLegal"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Any sibling is legal for an alarm extension.
</description>
<parameter name="sibling">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getSourceOrd() -->
<method name="getSourceOrd"  public="true">
<description/>
<return>
<type class="javax.baja.naming.BOrdList"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.doTimerExpired() -->
<method name="doTimerExpired"  public="true">
<description>
Callback for timer expired.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.doInhibitTimerExpired() -->
<method name="doInhibitTimerExpired"  public="true">
<description>
Callback for inhibit timer expired.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.doAckAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="doAckAlarm"  public="true">
<description/>
<parameter name="ackRequest">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.onExecute(javax.baja.status.BStatusValue, javax.baja.sys.Context) -->
<method name="onExecute"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="out">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.isTimerActive() -->
<method name="isTimerActive"  protected="true">
<description>
Check for to see if alarm inhibit timer is active&#xa; alarms
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.isInhibitTimerActive() -->
<method name="isInhibitTimerActive"  protected="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.checkAlarms(javax.baja.status.BStatusValue) -->
<method name="checkAlarms"  protected="true">
<description>
Check for to_normal, to_offnormal and to_fault&#xa; alarms
</description>
<parameter name="out">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.fireToNormal(javax.baja.alarm.BAlarmRecord) -->
<method name="fireToNormal"  public="true">
<description>
Fires the &#x27;normal&#x27; action, as well as updating alarm&#xa; metrics on the source extension
</description>
<parameter name="record">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.isLastNormalRecord(javax.baja.alarm.BAlarmRecord) -->
<method name="isLastNormalRecord"  public="true">
<description>
Returns true if the given record corresponds to the most recent alert, offnormal, or fault&#xa; alarm when the source has transitioned to normal. If there have not been any alert, offnormal,&#xa; or fault alarms for this source, this method returns true if the given record is for this&#xa; source and has a normal source state.
</description>
<tag name="@since">Niagara 4.10u5</tag>
<tag name="@since">Niagara 4.12u2</tag>
<tag name="@since">Niagara 4.13</tag>
<parameter name="alarmRecord">
<type class="javax.baja.alarm.BAlarmRecord"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getLastOffnormalTime() -->
<method name="getLastOffnormalTime"  public="true">
<description>
Return the time of the last transition to offnormal.
</description>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getLastFaultTime() -->
<method name="getLastFaultTime"  public="true">
<description>
Return the time of the last transition to fault.
</description>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getLastToNormalTime() -->
<method name="getLastToNormalTime"  public="true">
<description>
Return the time of the last transition to normal.
</description>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getLastAckTime() -->
<method name="getLastAckTime"  public="true">
<description>
Return the time of the last alarm acknowledgement.
</description>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the icon.
</description>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.toNormal -->
<field name="toNormal"  public="true" static="true" final="true">
<type class="javax.baja.sys.Topic"/>
<description>
Slot for the &lt;code&gt;toNormal&lt;/code&gt; topic.
</description>
<tag name="@see">#fireToNormal</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.alarmInhibit -->
<field name="alarmInhibit"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmInhibit&lt;/code&gt; property.&#xa; Inhibits alarm generation.
</description>
<tag name="@see">#getAlarmInhibit</tag>
<tag name="@see">#setAlarmInhibit</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.inhibitTime -->
<field name="inhibitTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;inhibitTime&lt;/code&gt; property.&#xa; Time between alarmInhibit:true-&amp;gt;false and alarmInhibit being lifted.&#xa; The reverse is 3x this delay for discrete points and 0 for numeric points.
</description>
<tag name="@see">#getInhibitTime</tag>
<tag name="@see">#setInhibitTime</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.alarmState -->
<field name="alarmState"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmState&lt;/code&gt; property.&#xa; Shows the object&#x27;s current alarm state.
</description>
<tag name="@see">#getAlarmState</tag>
<tag name="@see">#setAlarmState</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.timeDelay -->
<field name="timeDelay"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;timeDelay&lt;/code&gt; property.&#xa; Minimum time period that an alarm condition must exist before the object alarms.
</description>
<tag name="@see">#getTimeDelay</tag>
<tag name="@see">#setTimeDelay</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.timeDelayToNormal -->
<field name="timeDelayToNormal"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;timeDelayToNormal&lt;/code&gt; property.&#xa; Minimum time period that a normal condition must exist before the object returns to normal.
</description>
<tag name="@see">#getTimeDelayToNormal</tag>
<tag name="@see">#setTimeDelayToNormal</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.alarmEnable -->
<field name="alarmEnable"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmEnable&lt;/code&gt; property.&#xa; Flags that define the types of alarm transitions for this object that will generate alarm.
</description>
<tag name="@see">#getAlarmEnable</tag>
<tag name="@see">#setAlarmEnable</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.ackedTransitions -->
<field name="ackedTransitions"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;ackedTransitions&lt;/code&gt; property.&#xa; Flags, that when cleared, indicate that an unacknowledged alarm transition has occurred.
</description>
<tag name="@see">#getAckedTransitions</tag>
<tag name="@see">#setAckedTransitions</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.toOffnormalTimes -->
<field name="toOffnormalTimes"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;toOffnormalTimes&lt;/code&gt; property.&#xa; eventTime, normalTime, ackTime and count for last to offnormal event.
</description>
<tag name="@see">#getToOffnormalTimes</tag>
<tag name="@see">#setToOffnormalTimes</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.toFaultTimes -->
<field name="toFaultTimes"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;toFaultTimes&lt;/code&gt; property.&#xa; eventTime, normalTime,  ackTime and count for last to fault event.
</description>
<tag name="@see">#getToFaultTimes</tag>
<tag name="@see">#setToFaultTimes</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.timeInCurrentState -->
<field name="timeInCurrentState"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;timeInCurrentState&lt;/code&gt; property.&#xa; the time that this point has been in it&#x27;s current state&#xa; update every 10sec.
</description>
<tag name="@see">#getTimeInCurrentState</tag>
<tag name="@see">#setTimeInCurrentState</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.sourceName -->
<field name="sourceName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;sourceName&lt;/code&gt; property.&#xa; Text descriptor for the source name of the alarm. Uses BFormat, but currently only supports lexicons.
</description>
<tag name="@see">#getSourceName</tag>
<tag name="@see">#setSourceName</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.toFaultText -->
<field name="toFaultText"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;toFaultText&lt;/code&gt; property.&#xa; Text descriptor included in a to-fault alarm for this object. Uses BFormat.
</description>
<tag name="@see">#getToFaultText</tag>
<tag name="@see">#setToFaultText</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.toOffnormalText -->
<field name="toOffnormalText"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;toOffnormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-offnormal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#getToOffnormalText</tag>
<tag name="@see">#setToOffnormalText</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.toNormalText -->
<field name="toNormalText"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;toNormalText&lt;/code&gt; property.&#xa; Text descriptor included in a to-normal alarm for this object. Uses BFormat.
</description>
<tag name="@see">#getToNormalText</tag>
<tag name="@see">#setToNormalText</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.hyperlinkOrd -->
<field name="hyperlinkOrd"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;hyperlinkOrd&lt;/code&gt; property.&#xa; Ord to link to for more information about this alarm.
</description>
<tag name="@see">#getHyperlinkOrd</tag>
<tag name="@see">#setHyperlinkOrd</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.soundFile -->
<field name="soundFile"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;soundFile&lt;/code&gt; property.&#xa; Sound to play when the alarm comes into the alarm console.&#xa; Sound must be available on the client.
</description>
<tag name="@see">#getSoundFile</tag>
<tag name="@see">#setSoundFile</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.alarmIcon -->
<field name="alarmIcon"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmIcon&lt;/code&gt; property.&#xa; Icon to display for this alarm in the alarm console&#xa; Icon must be available on the client.
</description>
<tag name="@see">#getAlarmIcon</tag>
<tag name="@see">#setAlarmIcon</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.alarmInstructions -->
<field name="alarmInstructions"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmInstructions&lt;/code&gt; property.
</description>
<tag name="@see">#getAlarmInstructions</tag>
<tag name="@see">#setAlarmInstructions</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.faultAlgorithm -->
<field name="faultAlgorithm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;faultAlgorithm&lt;/code&gt; property.&#xa; This is the fault algorithm used for this object.
</description>
<tag name="@see">#getFaultAlgorithm</tag>
<tag name="@see">#setFaultAlgorithm</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.offnormalAlgorithm -->
<field name="offnormalAlgorithm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;offnormalAlgorithm&lt;/code&gt; property.&#xa; This is the offnormal algorithm used for this object.
</description>
<tag name="@see">#getOffnormalAlgorithm</tag>
<tag name="@see">#setOffnormalAlgorithm</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.alarmClass -->
<field name="alarmClass"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;alarmClass&lt;/code&gt; property.&#xa; This is the alarm class used for routing this alarm.
</description>
<tag name="@see">#getAlarmClass</tag>
<tag name="@see">#setAlarmClass</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.metaData -->
<field name="metaData"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;metaData&lt;/code&gt; property.&#xa; Additional user defined data for this alarm.
</description>
<tag name="@see">#getMetaData</tag>
<tag name="@see">#setMetaData</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.status -->
<field name="status"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.&#xa; the status&#x27;s that propagate to the control point&#x27;s status
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.timerExpired -->
<field name="timerExpired"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;timerExpired&lt;/code&gt; action.
</description>
<tag name="@see">#timerExpired()</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.inhibitTimerExpired -->
<field name="inhibitTimerExpired"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;inhibitTimerExpired&lt;/code&gt; action.
</description>
<tag name="@see">#inhibitTimerExpired()</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.ackAlarm -->
<field name="ackAlarm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;ackAlarm&lt;/code&gt; action.&#xa; Acknowledge the alarm matching this ack request
</description>
<tag name="@see">#ackAlarm(BAlarmRecord parameter)</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.toOffnormal -->
<field name="toOffnormal"  public="true" static="true" final="true">
<type class="javax.baja.sys.Topic"/>
<description>
Slot for the &lt;code&gt;toOffnormal&lt;/code&gt; topic.
</description>
<tag name="@see">#fireToOffnormal</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.toFault -->
<field name="toFault"  public="true" static="true" final="true">
<type class="javax.baja.sys.Topic"/>
<description>
Slot for the &lt;code&gt;toFault&lt;/code&gt; topic.
</description>
<tag name="@see">#fireToFault</tag>
</field>

<!-- javax.baja.alarm.ext.BAlarmSourceExt.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
