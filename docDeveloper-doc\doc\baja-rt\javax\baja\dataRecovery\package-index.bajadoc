<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="baja" runtimeProfile="rt" name="javax.baja.dataRecovery">
<description/>
<class packageName="javax.baja.dataRecovery" name="BIDataRecoveryService" category="interface"><description>BIDataRecoveryService defines the behavior that all data recovery&#xa; services, that is providers of data recovery storage, should demonstrate.</description></class>
<class packageName="javax.baja.dataRecovery" name="BIDataRecoverySource" category="interface"><description>BIDataRecoverySource defines the contract that all data recovery&#xa; sources, that is clients of data recovery storage, must obey.</description></class>
<class packageName="javax.baja.dataRecovery" name="BIDataRecoverySourceService" category="interface"><description>BIDataRecoverySourceService should be implemented by any service that&#xa; supports data recovery.</description></class>
<class packageName="javax.baja.dataRecovery" name="IDataRecoveryRecord" category="interface"><description>IDataRecoveryRecord defines the contract that all restored&#xa; data recovery must obey.</description></class>
<class packageName="javax.baja.dataRecovery" name="DataRecoveryException" category="exception"><description>Base exception class for data recovery.</description></class>
<class packageName="javax.baja.dataRecovery" name="DataRecoveryInvalidKeyException" category="exception"><description>DataRecoveryInvalidKeyException is thrown whenever the key provided by&#xa; the source was invalid (NULL, error while encoding)</description></class>
<class packageName="javax.baja.dataRecovery" name="DataRecoveryServiceInFaultException" category="exception"><description>DataRecoveryServiceInFaultException is thrown whenever a source&#xa; attempts to use the BIDataRecoveryService interface but the&#xa; service is in an invalid state for the request.</description></class>
<class packageName="javax.baja.dataRecovery" name="DataRecoveryTooLargeException" category="exception"><description>DataRecoveryException thrown when data recovery Service is asked to store&#xa; an amount of data for which it would never have enough space for.</description></class>
</package>
</bajadoc>
