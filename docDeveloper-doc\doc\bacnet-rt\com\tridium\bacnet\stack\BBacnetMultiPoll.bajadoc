<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.BBacnetMultiPoll" name="BBacnetMultiPoll" packageName="com.tridium.bacnet.stack" public="true">
<description>
BBacnetMultiPoll is the subclass of BBacnetPoll that handles multithreaded&#xa; polling.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 17$ $Date: 9/10/2003 12:55:17 PM$</tag>
<tag name="@creation">21 Jan 2002</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="com.tridium.bacnet.stack.BBacnetPoll"/>
</extends>
<property name="maxDibsPolls" flags="h">
<type class="int"/>
<description>
Slot for the &lt;code&gt;maxDibsPolls&lt;/code&gt; property.&#xa; maximum number of dibs polls between regular poll requests.
</description>
<tag name="@see">#getMaxDibsPolls</tag>
<tag name="@see">#setMaxDibsPolls</tag>
</property>

<property name="numberOfThreads" flags="r">
<type class="int"/>
<description>
Slot for the &lt;code&gt;numberOfThreads&lt;/code&gt; property.&#xa; number of poll threads to be simultaneously servicing this poll queue.
</description>
<tag name="@see">#getNumberOfThreads</tag>
<tag name="@see">#setNumberOfThreads</tag>
</property>

</class>
</bajadoc>
