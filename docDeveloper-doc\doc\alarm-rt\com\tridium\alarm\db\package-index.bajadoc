<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="alarm" runtimeProfile="rt" name="com.tridium.alarm.db">
<description/>
<class packageName="com.tridium.alarm.db" name="BAckPendingAlarmTable"><description>BAckPendingAlarmTable presents the list of ackPending alarms as a table.</description></class>
<class packageName="com.tridium.alarm.db" name="BAlarmDbQueryResult"><description>BAlarmDbQueryResult is the result of a bql query of the alarm database.</description></class>
<class packageName="com.tridium.alarm.db" name="BAlarmTable"><description>BAlarmTable is the result of a bql query of the alarm database.</description></class>
<class packageName="com.tridium.alarm.db" name="BLocalAlarmResolver"><description>Resolves alarm queries locally.</description></class>
<class packageName="com.tridium.alarm.db" name="BOpenAlarmTable"><description>BOpenAlarmTable presents the list of open alarms as a table.</description></class>
<class packageName="com.tridium.alarm.db" name="BIAlarmResolver" category="interface"><description>BIAlarmResolver is an object that knows how to resolve an alarm ord.</description></class>
</package>
</bajadoc>
