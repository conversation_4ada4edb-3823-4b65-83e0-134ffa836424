<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.io.RangeData" name="RangeData" packageName="javax.baja.bacnet.io" public="true" interface="true" abstract="true" category="interface">
<description>
RangeReference contains information to reference&#xa; a range of values in a compound property.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">13 Sep 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<implements>
<type class="javax.baja.bacnet.io.PropertyReference"/>
</implements>
<!-- javax.baja.bacnet.io.RangeData.getResultFlags() -->
<method name="getResultFlags"  public="true" abstract="true">
<description>
Get the result flags for this range data.
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
the resultFlags.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.RangeData.includesFirstItem() -->
<method name="includesFirstItem"  public="true" abstract="true">
<description>
Does the data include the first item in the list?
</description>
<return>
<type class="boolean"/>
<description>
true if the first item is included.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.RangeData.includesLastItem() -->
<method name="includesLastItem"  public="true" abstract="true">
<description>
Does the data include the last item in the list?
</description>
<return>
<type class="boolean"/>
<description>
true if the last item is included.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.RangeData.isMoreItems() -->
<method name="isMoreItems"  public="true" abstract="true">
<description>
Are there more items in the list that match the request?
</description>
<return>
<type class="boolean"/>
<description>
true if more items in the list match the&#xa; request but are not included in the data.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.RangeData.getItemCount() -->
<method name="getItemCount"  public="true" abstract="true">
<description>
Get the number of items.
</description>
<return>
<type class="long"/>
<description>
the item count.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.RangeData.getFirstSequenceNumber() -->
<method name="getFirstSequenceNumber"  public="true" abstract="true">
<description>
Get the sequence number of the first item in the data.
</description>
<return>
<type class="long"/>
<description>
the firstSequenceNumber.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.RangeData.getItemData() -->
<method name="getItemData"  public="true" abstract="true">
<description>
Get the item data.
</description>
<return>
<type class="byte" dimension="1"/>
<description>
the items as an encoded byte array.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.RangeData.getError() -->
<method name="getError"  public="true" abstract="true">
<description>
Get the error.
</description>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
an ErrorType if this is an error result,&#xa; or null if this is a success.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.RangeData.getErrorClass() -->
<method name="getErrorClass"  public="true" abstract="true">
<description>
Get the error class.
</description>
<return>
<type class="int"/>
<description>
an int representing a value in the BBacnetErrorClass&#xa; enumeration indicating the class of failure,&#xa; or null if this is a success.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.RangeData.getErrorCode() -->
<method name="getErrorCode"  public="true" abstract="true">
<description>
Get the error code.
</description>
<return>
<type class="int"/>
<description>
an int representing a value in the BBacnetErrorCode&#xa; enumeration indicating the reason for failure,&#xa; or null if this is a success.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.RangeData.isError() -->
<method name="isError"  public="true" abstract="true">
<description>
Is this a failure result?
</description>
<return>
<type class="boolean"/>
<description>
TRUE if this is an error result, or FALSE if it is a success.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.RangeData.RESULT_FLAGS -->
<field name="RESULT_FLAGS"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.io.RangeData.ITEM_COUNT -->
<field name="ITEM_COUNT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.io.RangeData.ITEM_DATA_NO_SEQ_NUM -->
<field name="ITEM_DATA_NO_SEQ_NUM"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.io.RangeData.FIRST_SEQUENCE_NUMBER -->
<field name="FIRST_SEQUENCE_NUMBER"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.io.RangeData.ITEM_DATA -->
<field name="ITEM_DATA"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
