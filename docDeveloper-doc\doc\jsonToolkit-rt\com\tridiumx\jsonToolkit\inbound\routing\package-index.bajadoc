<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.inbound.routing">
<description/>
<class packageName="com.tridiumx.jsonToolkit.inbound.routing" name="BJsonArrayRouter"><description>Json Array Router is used to redirect each incoming array index to a new slot.</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.routing" name="BJsonDemuxRouter"><description>Given input { name: &#x22;Bob&#x22;, age: 60, happy: true }</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.routing" name="BJsonMessageRouter"><description>The Json Message Router allows inbound messages to be transferred to an onward component&#xa; suitable for processing, or handling, the message.</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.routing" name="BJsonRouter"><description>Base class for JSON message routers.</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.routing" name="RoutingFailedException" category="exception"><description>Routing failed for a given payload and reason.</description></class>
</package>
</bajadoc>
