<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BErrorType" name="BErrorType" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BErrorType represents the Error sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">28 Jul 2006</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<implements>
<type class="javax.baja.bacnet.io.ErrorType"/>
</implements>
<property name="errorClass" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;errorClass&lt;/code&gt; property.
</description>
<tag name="@see">#getErrorClass</tag>
<tag name="@see">#setErrorClass</tag>
</property>

<property name="errorCode" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;errorCode&lt;/code&gt; property.
</description>
<tag name="@see">#getErrorCode</tag>
<tag name="@see">#setErrorCode</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BErrorType() -->
<constructor name="BErrorType" public="true">
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BErrorType(int, int) -->
<constructor name="BErrorType" public="true">
<parameter name="errClass">
<type class="int"/>
<description/>
</parameter>
<parameter name="errCode">
<type class="int"/>
<description/>
</parameter>
<description>
Fully specified constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BErrorType.getErrorClass() -->
<method name="getErrorClass"  public="true">
<description>
Get the &lt;code&gt;errorClass&lt;/code&gt; property.
</description>
<tag name="@see">#errorClass</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BErrorType.setErrorClass(int) -->
<method name="setErrorClass"  public="true">
<description>
Set the &lt;code&gt;errorClass&lt;/code&gt; property.
</description>
<tag name="@see">#errorClass</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BErrorType.getErrorCode() -->
<method name="getErrorCode"  public="true">
<description>
Get the &lt;code&gt;errorCode&lt;/code&gt; property.
</description>
<tag name="@see">#errorCode</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BErrorType.setErrorCode(int) -->
<method name="setErrorCode"  public="true">
<description>
Set the &lt;code&gt;errorCode&lt;/code&gt; property.
</description>
<tag name="@see">#errorCode</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BErrorType.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BErrorType.writeEncoded(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeEncoded"  public="true">
<description>
Encode the property value data to Asn.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the Asn encoder.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BErrorType.readEncoded(javax.baja.bacnet.io.AsnInput) -->
<method name="readEncoded"  public="true">
<description>
Decode the property value data from Asn.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the Asn decoder.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
<description>
if there is an Asn error.
</description>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BErrorType.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BErrorType.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BErrorType.isDefault() -->
<method name="isDefault"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BErrorType.setToDefault(javax.baja.sys.Context) -->
<method name="setToDefault"  public="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BErrorType.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BErrorType.errorClass -->
<field name="errorClass"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;errorClass&lt;/code&gt; property.
</description>
<tag name="@see">#getErrorClass</tag>
<tag name="@see">#setErrorClass</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BErrorType.errorCode -->
<field name="errorCode"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;errorCode&lt;/code&gt; property.
</description>
<tag name="@see">#getErrorCode</tag>
<tag name="@see">#setErrorCode</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BErrorType.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
