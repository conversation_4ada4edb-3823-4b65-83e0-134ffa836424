<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BBacnetEventEnrollment" name="BBacnetEventEnrollment" packageName="javax.baja.bacnet.config" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">28 Jul 2006</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="javax.baja.bacnet.config.BBacnetCreatableObject"/>
</extends>
<property name="eventType" flags="">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;eventType&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getEventType</tag>
<tag name="@see">#setEventType</tag>
</property>

<property name="notifyType" flags="">
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
<description>
Slot for the &lt;code&gt;notifyType&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getNotifyType</tag>
<tag name="@see">#setNotifyType</tag>
</property>

<property name="eventParameters" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetEventParameter"/>
<description>
Slot for the &lt;code&gt;eventParameters&lt;/code&gt; property.&#xa; determines the algorithm used to monitor the referenced object&#xa; and provides the parameter values needed for this algorithm.
</description>
<tag name="@see">#getEventParameters</tag>
<tag name="@see">#setEventParameters</tag>
</property>

<property name="objectPropertyReference" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
<description>
Slot for the &lt;code&gt;objectPropertyReference&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectPropertyReference</tag>
<tag name="@see">#setObjectPropertyReference</tag>
</property>

<property name="eventState" flags="r">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getEventState</tag>
<tag name="@see">#setEventState</tag>
</property>

<property name="eventEnable" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
Slot for the &lt;code&gt;eventEnable&lt;/code&gt; property.&#xa; conveys flags that whether notifications are enabled for the various&#xa; transitions.
</description>
<tag name="@see">#getEventEnable</tag>
<tag name="@see">#setEventEnable</tag>
</property>

<property name="ackedTransitions" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
Slot for the &lt;code&gt;ackedTransitions&lt;/code&gt; property.&#xa; indicates whether the last transition of each type has been acknowledged.
</description>
<tag name="@see">#getAckedTransitions</tag>
<tag name="@see">#setAckedTransitions</tag>
</property>

<property name="notificationClass" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
<description>
Slot for the &lt;code&gt;notificationClass&lt;/code&gt; property.&#xa; references a Notification Class in the same device that specifies the&#xa; handling, reporting, and acknowledgment characteristics for this object.
</description>
<tag name="@see">#getNotificationClass</tag>
<tag name="@see">#setNotificationClass</tag>
</property>

<property name="eventTimeStamps" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
<description>
Slot for the &lt;code&gt;eventTimeStamps&lt;/code&gt; property.&#xa; contains the times of the last event notifications for each transition type.
</description>
<tag name="@see">#getEventTimeStamps</tag>
<tag name="@see">#setEventTimeStamps</tag>
</property>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment() -->
<constructor name="BBacnetEventEnrollment" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.getEventType() -->
<method name="getEventType"  public="true">
<description>
Get the &lt;code&gt;eventType&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#eventType</tag>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.setEventType(javax.baja.sys.BEnum) -->
<method name="setEventType"  public="true">
<description>
Set the &lt;code&gt;eventType&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#eventType</tag>
<parameter name="v">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.getNotifyType() -->
<method name="getNotifyType"  public="true">
<description>
Get the &lt;code&gt;notifyType&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#notifyType</tag>
<return>
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.setNotifyType(javax.baja.bacnet.enums.BBacnetNotifyType) -->
<method name="setNotifyType"  public="true">
<description>
Set the &lt;code&gt;notifyType&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#notifyType</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.getEventParameters() -->
<method name="getEventParameters"  public="true">
<description>
Get the &lt;code&gt;eventParameters&lt;/code&gt; property.&#xa; determines the algorithm used to monitor the referenced object&#xa; and provides the parameter values needed for this algorithm.
</description>
<tag name="@see">#eventParameters</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetEventParameter"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.setEventParameters(javax.baja.bacnet.datatypes.BBacnetEventParameter) -->
<method name="setEventParameters"  public="true">
<description>
Set the &lt;code&gt;eventParameters&lt;/code&gt; property.&#xa; determines the algorithm used to monitor the referenced object&#xa; and provides the parameter values needed for this algorithm.
</description>
<tag name="@see">#eventParameters</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetEventParameter"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.getObjectPropertyReference() -->
<method name="getObjectPropertyReference"  public="true">
<description>
Get the &lt;code&gt;objectPropertyReference&lt;/code&gt; property.
</description>
<tag name="@see">#objectPropertyReference</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.setObjectPropertyReference(javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference) -->
<method name="setObjectPropertyReference"  public="true">
<description>
Set the &lt;code&gt;objectPropertyReference&lt;/code&gt; property.
</description>
<tag name="@see">#objectPropertyReference</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetDeviceObjectPropertyReference"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.getEventState() -->
<method name="getEventState"  public="true">
<description>
Get the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#eventState</tag>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.setEventState(javax.baja.sys.BEnum) -->
<method name="setEventState"  public="true">
<description>
Set the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#eventState</tag>
<parameter name="v">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.getEventEnable() -->
<method name="getEventEnable"  public="true">
<description>
Get the &lt;code&gt;eventEnable&lt;/code&gt; property.&#xa; conveys flags that whether notifications are enabled for the various&#xa; transitions.
</description>
<tag name="@see">#eventEnable</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.setEventEnable(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="setEventEnable"  public="true">
<description>
Set the &lt;code&gt;eventEnable&lt;/code&gt; property.&#xa; conveys flags that whether notifications are enabled for the various&#xa; transitions.
</description>
<tag name="@see">#eventEnable</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.getAckedTransitions() -->
<method name="getAckedTransitions"  public="true">
<description>
Get the &lt;code&gt;ackedTransitions&lt;/code&gt; property.&#xa; indicates whether the last transition of each type has been acknowledged.
</description>
<tag name="@see">#ackedTransitions</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.setAckedTransitions(javax.baja.bacnet.datatypes.BBacnetBitString) -->
<method name="setAckedTransitions"  public="true">
<description>
Set the &lt;code&gt;ackedTransitions&lt;/code&gt; property.&#xa; indicates whether the last transition of each type has been acknowledged.
</description>
<tag name="@see">#ackedTransitions</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.getNotificationClass() -->
<method name="getNotificationClass"  public="true">
<description>
Get the &lt;code&gt;notificationClass&lt;/code&gt; property.&#xa; references a Notification Class in the same device that specifies the&#xa; handling, reporting, and acknowledgment characteristics for this object.
</description>
<tag name="@see">#notificationClass</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.setNotificationClass(javax.baja.bacnet.datatypes.BBacnetUnsigned) -->
<method name="setNotificationClass"  public="true">
<description>
Set the &lt;code&gt;notificationClass&lt;/code&gt; property.&#xa; references a Notification Class in the same device that specifies the&#xa; handling, reporting, and acknowledgment characteristics for this object.
</description>
<tag name="@see">#notificationClass</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetUnsigned"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.getEventTimeStamps() -->
<method name="getEventTimeStamps"  public="true">
<description>
Get the &lt;code&gt;eventTimeStamps&lt;/code&gt; property.&#xa; contains the times of the last event notifications for each transition type.
</description>
<tag name="@see">#eventTimeStamps</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.setEventTimeStamps(javax.baja.bacnet.datatypes.BBacnetArray) -->
<method name="setEventTimeStamps"  public="true">
<description>
Set the &lt;code&gt;eventTimeStamps&lt;/code&gt; property.&#xa; contains the times of the last event notifications for each transition type.
</description>
<tag name="@see">#eventTimeStamps</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetArray"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.addObjectInitialValues(javax.baja.nre.util.Array&lt;javax.baja.bacnet.io.PropertyValue&gt;) -->
<method name="addObjectInitialValues"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
<type class="javax.baja.bacnet.io.PropertyValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.eventType -->
<field name="eventType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;eventType&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getEventType</tag>
<tag name="@see">#setEventType</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.notifyType -->
<field name="notifyType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;notifyType&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getNotifyType</tag>
<tag name="@see">#setNotifyType</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.eventParameters -->
<field name="eventParameters"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;eventParameters&lt;/code&gt; property.&#xa; determines the algorithm used to monitor the referenced object&#xa; and provides the parameter values needed for this algorithm.
</description>
<tag name="@see">#getEventParameters</tag>
<tag name="@see">#setEventParameters</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.objectPropertyReference -->
<field name="objectPropertyReference"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectPropertyReference&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectPropertyReference</tag>
<tag name="@see">#setObjectPropertyReference</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.eventState -->
<field name="eventState"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;eventState&lt;/code&gt; property.&#xa; eventState indicates if this object has an active event state.
</description>
<tag name="@see">#getEventState</tag>
<tag name="@see">#setEventState</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.eventEnable -->
<field name="eventEnable"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;eventEnable&lt;/code&gt; property.&#xa; conveys flags that whether notifications are enabled for the various&#xa; transitions.
</description>
<tag name="@see">#getEventEnable</tag>
<tag name="@see">#setEventEnable</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.ackedTransitions -->
<field name="ackedTransitions"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;ackedTransitions&lt;/code&gt; property.&#xa; indicates whether the last transition of each type has been acknowledged.
</description>
<tag name="@see">#getAckedTransitions</tag>
<tag name="@see">#setAckedTransitions</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.notificationClass -->
<field name="notificationClass"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;notificationClass&lt;/code&gt; property.&#xa; references a Notification Class in the same device that specifies the&#xa; handling, reporting, and acknowledgment characteristics for this object.
</description>
<tag name="@see">#getNotificationClass</tag>
<tag name="@see">#setNotificationClass</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.eventTimeStamps -->
<field name="eventTimeStamps"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;eventTimeStamps&lt;/code&gt; property.&#xa; contains the times of the last event notifications for each transition type.
</description>
<tag name="@see">#getEventTimeStamps</tag>
<tag name="@see">#setEventTimeStamps</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetEventEnrollment.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
