<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.OrdQuery" name="OrdQuery" packageName="javax.baja.naming" public="true" interface="true" abstract="true" category="interface">
<description>
OrdQuery encapsulates a single query within a BOrd.  &#xa; A query is composed of a scheme id and an ASCII body.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">3 Jan 03</tag>
<tag name="@version">$Revision: 9$ $Date: 3/28/05 9:23:01 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<!-- javax.baja.naming.OrdQuery.getScheme() -->
<method name="getScheme"  public="true" abstract="true">
<description>
Get the scheme id of the query.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.OrdQuery.getBody() -->
<method name="getBody"  public="true" abstract="true">
<description>
Get the ASCII body of the query.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.OrdQuery.isHost() -->
<method name="isHost"  public="true" abstract="true">
<description>
Return if this OrdQuery is an host.  Host queries are&#xa; absolute and resolve to a BHost.  Since host queries are &#xa; absolute they trump any queries to their left during &#xa; normalization.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.OrdQuery.isSession() -->
<method name="isSession"  public="true" abstract="true">
<description>
Return if this OrdQuery is a session.  Sessions queries &#xa; are absolute within a host, and resolve to a BISession.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.OrdQuery.normalize(javax.baja.naming.OrdQueryList, int) -->
<method name="normalize"  public="true" abstract="true">
<description>
Starting in Niagara 4.13, this method is only called by the framework from&#xa; the &lt;code&gt;<see ref="javax.baja.naming.OrdQuery#normalize(javax.baja.naming.OrdQueryList, int, javax.baja.sys.Context)">#normalize(OrdQueryList, int, Context)</see>&lt;/code&gt; default implementation&#xa; (if that method is not overridden by the subclass). Refer to&#xa; &lt;code&gt;<see ref="javax.baja.naming.OrdQuery#normalize(javax.baja.naming.OrdQueryList, int, javax.baja.sys.Context)">#normalize(OrdQueryList, int, Context)</see>&lt;/code&gt; for a description of when&#xa; this method is called. It is commonly used when the Context parameter is&#xa; not needed to perform normalization. Note that this method may also be&#xa; called directly (outside of the framework usage), but that is not common.
</description>
<parameter name="list">
<type class="javax.baja.naming.OrdQueryList"/>
</parameter>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.OrdQuery.normalize(javax.baja.naming.OrdQueryList, int, javax.baja.sys.Context) -->
<method name="normalize"  public="true" default="true">
<description>
This method is called by the framework during BOrd normalization to give&#xa; each query the ability to normalize itself.  The index specifies the&#xa; location of this query in the parsed queries list.  This method allows&#xa; OrdQueries to merge or truncate relative ORDs. A Context parameter is also&#xa; provided to give more information about the context in which this method&#xa; was called.  The Context parameter may be null, but, for example, it can&#xa; also be &lt;code&gt;<see ref="javax.baja.naming.OrdQuery#RESOLVING_ORD_CX">#RESOLVING_ORD_CX</see>&lt;/code&gt; if this method is being called at BOrd&#xa; resolution time (see&#xa; &lt;code&gt;<see ref="javax.baja.naming.BOrd#resolve(javax.baja.sys.BObject, javax.baja.sys.Context)">BOrd#resolve(BObject, Context)</see>&lt;/code&gt;). If this&#xa; method is not overridden by subclasses, the default behavior is to ignore&#xa; the Context parameter and reroute the call directly to&#xa; &lt;code&gt;<see ref="javax.baja.naming.OrdQuery#normalize(javax.baja.naming.OrdQueryList, int)">#normalize(OrdQueryList, int)</see>&lt;/code&gt;.
</description>
<tag name="@since">Niagara 4.13</tag>
<parameter name="list">
<type class="javax.baja.naming.OrdQueryList"/>
</parameter>
<parameter name="index">
<type class="int"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.OrdQuery.toString() -->
<method name="toString"  public="true" abstract="true">
<description>
Return &lt;code&gt;scheme + &amp;#x22;:&amp;#x22; + body&lt;/code&gt;.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.OrdQuery.RESOLVING_ORD_CX -->
<field name="RESOLVING_ORD_CX"  public="true" static="true" final="true">
<type class="javax.baja.sys.Context"/>
<description>
Constant Context instance passed as the final parameter to the&#xa; &lt;code&gt;<see ref="javax.baja.naming.OrdQuery#normalize(javax.baja.naming.OrdQueryList, int, javax.baja.sys.Context)">#normalize(OrdQueryList, int, Context)</see>&lt;/code&gt; method only when it is&#xa; called at ORD resolution time.
</description>
<tag name="@since">Niagara 4.13</tag>
</field>

</class>
</bajadoc>
