<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery" name="BBaseQuery" packageName="com.tridiumx.jsonToolkit.outbound.schema.relative" public="true">
<description>
BaseQuery publishes base items to it&#x27;s parent relative schema so Json generation can begin.&#xa;&#xa; Each base item published will be the &#x22;first portion&#x22; of added to the relative ORDs in a relative schema.&#xa;&#xa; It is invoked using the generateJson action on the parent schema.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="status" flags="r">
<type class="javax.baja.status.BStatus"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.&#xa; Indicate query fails, no records, non-component result
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</property>

<property name="faultCause" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</property>

<property name="baseQuery" flags="s">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;baseQuery&lt;/code&gt; property.&#xa; Ord supplying Components to Schema, for example &#x22;slot:/Drivers|bql:select * from control:NumericPoint&#x22;&#xa;&#xa; Or for root component query use relative bql --&gt; slot:../|bql:select name, status from driver:Device
</description>
<tag name="@see">#getBaseQuery</tag>
<tag name="@see">#setBaseQuery</tag>
</property>

<property name="publishInterval" flags="">
<type class="javax.baja.sys.BRelTime"/>
<description>
Slot for the &lt;code&gt;publishInterval&lt;/code&gt; property.&#xa; The interval between executions of the base query
</description>
<tag name="@see">#getPublishInterval</tag>
<tag name="@see">#setPublishInterval</tag>
</property>

<property name="lastPublishCount" flags="tNr">
<type class="int"/>
<description>
Slot for the &lt;code&gt;lastPublishCount&lt;/code&gt; property.&#xa; How many objects were output
</description>
<tag name="@see">#getLastPublishCount</tag>
<tag name="@see">#setLastPublishCount</tag>
</property>

<property name="lastPublishTime" flags="tNr">
<type class="javax.baja.sys.BAbsTime"/>
<description>
Slot for the &lt;code&gt;lastPublishTime&lt;/code&gt; property.&#xa; Records the last time base objects were sent to the parent Schema
</description>
<tag name="@see">#getLastPublishTime</tag>
<tag name="@see">#setLastPublishTime</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery() -->
<constructor name="BBaseQuery" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.getStatus() -->
<method name="getStatus"  public="true">
<description>
Get the &lt;code&gt;status&lt;/code&gt; property.&#xa; Indicate query fails, no records, non-component result
</description>
<tag name="@see">#status</tag>
<return>
<type class="javax.baja.status.BStatus"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.setStatus(javax.baja.status.BStatus) -->
<method name="setStatus"  public="true">
<description>
Set the &lt;code&gt;status&lt;/code&gt; property.&#xa; Indicate query fails, no records, non-component result
</description>
<tag name="@see">#status</tag>
<parameter name="v">
<type class="javax.baja.status.BStatus"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.getFaultCause() -->
<method name="getFaultCause"  public="true">
<description>
Get the &lt;code&gt;faultCause&lt;/code&gt; property.
</description>
<tag name="@see">#faultCause</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.setFaultCause(java.lang.String) -->
<method name="setFaultCause"  public="true">
<description>
Set the &lt;code&gt;faultCause&lt;/code&gt; property.
</description>
<tag name="@see">#faultCause</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.getBaseQuery() -->
<method name="getBaseQuery"  public="true">
<description>
Get the &lt;code&gt;baseQuery&lt;/code&gt; property.&#xa; Ord supplying Components to Schema, for example &#x22;slot:/Drivers|bql:select * from control:NumericPoint&#x22;&#xa;&#xa; Or for root component query use relative bql --&gt; slot:../|bql:select name, status from driver:Device
</description>
<tag name="@see">#baseQuery</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.setBaseQuery(javax.baja.naming.BOrd) -->
<method name="setBaseQuery"  public="true">
<description>
Set the &lt;code&gt;baseQuery&lt;/code&gt; property.&#xa; Ord supplying Components to Schema, for example &#x22;slot:/Drivers|bql:select * from control:NumericPoint&#x22;&#xa;&#xa; Or for root component query use relative bql --&gt; slot:../|bql:select name, status from driver:Device
</description>
<tag name="@see">#baseQuery</tag>
<parameter name="v">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.getPublishInterval() -->
<method name="getPublishInterval"  public="true">
<description>
Get the &lt;code&gt;publishInterval&lt;/code&gt; property.&#xa; The interval between executions of the base query
</description>
<tag name="@see">#publishInterval</tag>
<return>
<type class="javax.baja.sys.BRelTime"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.setPublishInterval(javax.baja.sys.BRelTime) -->
<method name="setPublishInterval"  public="true">
<description>
Set the &lt;code&gt;publishInterval&lt;/code&gt; property.&#xa; The interval between executions of the base query
</description>
<tag name="@see">#publishInterval</tag>
<parameter name="v">
<type class="javax.baja.sys.BRelTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.getLastPublishCount() -->
<method name="getLastPublishCount"  public="true">
<description>
Get the &lt;code&gt;lastPublishCount&lt;/code&gt; property.&#xa; How many objects were output
</description>
<tag name="@see">#lastPublishCount</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.setLastPublishCount(int) -->
<method name="setLastPublishCount"  public="true">
<description>
Set the &lt;code&gt;lastPublishCount&lt;/code&gt; property.&#xa; How many objects were output
</description>
<tag name="@see">#lastPublishCount</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.getLastPublishTime() -->
<method name="getLastPublishTime"  public="true">
<description>
Get the &lt;code&gt;lastPublishTime&lt;/code&gt; property.&#xa; Records the last time base objects were sent to the parent Schema
</description>
<tag name="@see">#lastPublishTime</tag>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.setLastPublishTime(javax.baja.sys.BAbsTime) -->
<method name="setLastPublishTime"  public="true">
<description>
Set the &lt;code&gt;lastPublishTime&lt;/code&gt; property.&#xa; Records the last time base objects were sent to the parent Schema
</description>
<tag name="@see">#lastPublishTime</tag>
<parameter name="v">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.status -->
<field name="status"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.&#xa; Indicate query fails, no records, non-component result
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.faultCause -->
<field name="faultCause"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.baseQuery -->
<field name="baseQuery"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;baseQuery&lt;/code&gt; property.&#xa; Ord supplying Components to Schema, for example &#x22;slot:/Drivers|bql:select * from control:NumericPoint&#x22;&#xa;&#xa; Or for root component query use relative bql --&gt; slot:../|bql:select name, status from driver:Device
</description>
<tag name="@see">#getBaseQuery</tag>
<tag name="@see">#setBaseQuery</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.publishInterval -->
<field name="publishInterval"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;publishInterval&lt;/code&gt; property.&#xa; The interval between executions of the base query
</description>
<tag name="@see">#getPublishInterval</tag>
<tag name="@see">#setPublishInterval</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.lastPublishCount -->
<field name="lastPublishCount"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lastPublishCount&lt;/code&gt; property.&#xa; How many objects were output
</description>
<tag name="@see">#getLastPublishCount</tag>
<tag name="@see">#setLastPublishCount</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.lastPublishTime -->
<field name="lastPublishTime"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;lastPublishTime&lt;/code&gt; property.&#xa; Records the last time base objects were sent to the parent Schema
</description>
<tag name="@see">#getLastPublishTime</tag>
<tag name="@see">#setLastPublishTime</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.relative.BBaseQuery.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
