<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder" name="BJsonSchemaConfigFolder" packageName="com.tridiumx.jsonToolkit.outbound.schema.config.folder" public="true">
<description>
Root location for Schema config props
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.util.BFolder"/>
</extends>
<property name="nameCasingRule" flags="">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing"/>
<description>
Slot for the &lt;code&gt;nameCasingRule&lt;/code&gt; property.&#xa; Case options for the forming of json keys, providing uniformity in case of variation in Niagara naming.
</description>
<tag name="@see">#getNameCasingRule</tag>
<tag name="@see">#setNameCasingRule</tag>
</property>

<property name="nameSpacingRule" flags="">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing"/>
<description>
Slot for the &lt;code&gt;nameSpacingRule&lt;/code&gt; property.&#xa; Options for dealing with spaces in the forming of json keys. Eq hyphenate, underscore, remove.
</description>
<tag name="@see">#getNameSpacingRule</tag>
<tag name="@see">#setNameSpacingRule</tag>
</property>

<property name="dateFormatPattern" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;dateFormatPattern&lt;/code&gt; property.&#xa; The customer can use a Java SimpleDateFormat&#xa; String to define the time formatting used
</description>
<tag name="@see">#getDateFormatPattern</tag>
<tag name="@see">#setDateFormatPattern</tag>
</property>

<property name="numericPrecision" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;numericPrecision&lt;/code&gt; property.&#xa; The number of decimal digits to show on exported floating point numbers
</description>
<tag name="@see">#getNumericPrecision</tag>
<tag name="@see">#setNumericPrecision</tag>
</property>

<property name="useEscapeCharacters" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;useEscapeCharacters&lt;/code&gt; property.&#xa; When *false* any escape chars encountered will be *unescaped* - eg $20 will become a &#x22; &#x22; or space character.
</description>
<tag name="@see">#getUseEscapeCharacters</tag>
<tag name="@see">#setUseEscapeCharacters</tag>
</property>

<property name="tuningPolicy" flags="">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaTuningPolicy"/>
<description>
Slot for the &lt;code&gt;tuningPolicy&lt;/code&gt; property.&#xa; Tuning policies allow
</description>
<tag name="@see">#getTuningPolicy</tag>
<tag name="@see">#setTuningPolicy</tag>
</property>

<property name="overrides" flags="">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaOverrideFolder"/>
<description>
Slot for the &lt;code&gt;overrides&lt;/code&gt; property.
</description>
<tag name="@see">#getOverrides</tag>
<tag name="@see">#setOverrides</tag>
</property>

<property name="debug" flags="">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaDebugFolder"/>
<description>
Slot for the &lt;code&gt;debug&lt;/code&gt; property.
</description>
<tag name="@see">#getDebug</tag>
<tag name="@see">#setDebug</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder() -->
<constructor name="BJsonSchemaConfigFolder" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.getNameCasingRule() -->
<method name="getNameCasingRule"  public="true">
<description>
Get the &lt;code&gt;nameCasingRule&lt;/code&gt; property.&#xa; Case options for the forming of json keys, providing uniformity in case of variation in Niagara naming.
</description>
<tag name="@see">#nameCasingRule</tag>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.setNameCasingRule(com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing) -->
<method name="setNameCasingRule"  public="true">
<description>
Set the &lt;code&gt;nameCasingRule&lt;/code&gt; property.&#xa; Case options for the forming of json keys, providing uniformity in case of variation in Niagara naming.
</description>
<tag name="@see">#nameCasingRule</tag>
<parameter name="v">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameCasing"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.getNameSpacingRule() -->
<method name="getNameSpacingRule"  public="true">
<description>
Get the &lt;code&gt;nameSpacingRule&lt;/code&gt; property.&#xa; Options for dealing with spaces in the forming of json keys. Eq hyphenate, underscore, remove.
</description>
<tag name="@see">#nameSpacingRule</tag>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.setNameSpacingRule(com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing) -->
<method name="setNameSpacingRule"  public="true">
<description>
Set the &lt;code&gt;nameSpacingRule&lt;/code&gt; property.&#xa; Options for dealing with spaces in the forming of json keys. Eq hyphenate, underscore, remove.
</description>
<tag name="@see">#nameSpacingRule</tag>
<parameter name="v">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.getDateFormatPattern() -->
<method name="getDateFormatPattern"  public="true">
<description>
Get the &lt;code&gt;dateFormatPattern&lt;/code&gt; property.&#xa; The customer can use a Java SimpleDateFormat&#xa; String to define the time formatting used
</description>
<tag name="@see">#dateFormatPattern</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.setDateFormatPattern(java.lang.String) -->
<method name="setDateFormatPattern"  public="true">
<description>
Set the &lt;code&gt;dateFormatPattern&lt;/code&gt; property.&#xa; The customer can use a Java SimpleDateFormat&#xa; String to define the time formatting used
</description>
<tag name="@see">#dateFormatPattern</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.getNumericPrecision() -->
<method name="getNumericPrecision"  public="true">
<description>
Get the &lt;code&gt;numericPrecision&lt;/code&gt; property.&#xa; The number of decimal digits to show on exported floating point numbers
</description>
<tag name="@see">#numericPrecision</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.setNumericPrecision(int) -->
<method name="setNumericPrecision"  public="true">
<description>
Set the &lt;code&gt;numericPrecision&lt;/code&gt; property.&#xa; The number of decimal digits to show on exported floating point numbers
</description>
<tag name="@see">#numericPrecision</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.getUseEscapeCharacters() -->
<method name="getUseEscapeCharacters"  public="true">
<description>
Get the &lt;code&gt;useEscapeCharacters&lt;/code&gt; property.&#xa; When *false* any escape chars encountered will be *unescaped* - eg $20 will become a &#x22; &#x22; or space character.
</description>
<tag name="@see">#useEscapeCharacters</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.setUseEscapeCharacters(boolean) -->
<method name="setUseEscapeCharacters"  public="true">
<description>
Set the &lt;code&gt;useEscapeCharacters&lt;/code&gt; property.&#xa; When *false* any escape chars encountered will be *unescaped* - eg $20 will become a &#x22; &#x22; or space character.
</description>
<tag name="@see">#useEscapeCharacters</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.getTuningPolicy() -->
<method name="getTuningPolicy"  public="true">
<description>
Get the &lt;code&gt;tuningPolicy&lt;/code&gt; property.&#xa; Tuning policies allow
</description>
<tag name="@see">#tuningPolicy</tag>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaTuningPolicy"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.setTuningPolicy(com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaTuningPolicy) -->
<method name="setTuningPolicy"  public="true">
<description>
Set the &lt;code&gt;tuningPolicy&lt;/code&gt; property.&#xa; Tuning policies allow
</description>
<tag name="@see">#tuningPolicy</tag>
<parameter name="v">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaTuningPolicy"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.getOverrides() -->
<method name="getOverrides"  public="true">
<description>
Get the &lt;code&gt;overrides&lt;/code&gt; property.
</description>
<tag name="@see">#overrides</tag>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaOverrideFolder"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.setOverrides(com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaOverrideFolder) -->
<method name="setOverrides"  public="true">
<description>
Set the &lt;code&gt;overrides&lt;/code&gt; property.
</description>
<tag name="@see">#overrides</tag>
<parameter name="v">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaOverrideFolder"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.getDebug() -->
<method name="getDebug"  public="true">
<description>
Get the &lt;code&gt;debug&lt;/code&gt; property.
</description>
<tag name="@see">#debug</tag>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaDebugFolder"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.setDebug(com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaDebugFolder) -->
<method name="setDebug"  public="true">
<description>
Set the &lt;code&gt;debug&lt;/code&gt; property.
</description>
<tag name="@see">#debug</tag>
<parameter name="v">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaDebugFolder"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.getSchema() -->
<method name="getSchema"  public="true">
<description/>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchema"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Add gear badge to folder icon
</description>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.started() -->
<method name="started"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.formatTime(javax.baja.sys.BAbsTime) -->
<method name="formatTime"  public="true">
<description>
Use the Schema date format to covert local AbsTime to user desired output
</description>
<parameter name="input">
<type class="javax.baja.sys.BAbsTime"/>
<description>
BAbsTime to convert
</description>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the formatted result based on the date format property
</description>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.getDateFormat() -->
<method name="getDateFormat"  public="true">
<description/>
<return>
<type class="java.text.DateFormat"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.nameCasingRule -->
<field name="nameCasingRule"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;nameCasingRule&lt;/code&gt; property.&#xa; Case options for the forming of json keys, providing uniformity in case of variation in Niagara naming.
</description>
<tag name="@see">#getNameCasingRule</tag>
<tag name="@see">#setNameCasingRule</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.nameSpacingRule -->
<field name="nameSpacingRule"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;nameSpacingRule&lt;/code&gt; property.&#xa; Options for dealing with spaces in the forming of json keys. Eq hyphenate, underscore, remove.
</description>
<tag name="@see">#getNameSpacingRule</tag>
<tag name="@see">#setNameSpacingRule</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.dateFormatPattern -->
<field name="dateFormatPattern"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;dateFormatPattern&lt;/code&gt; property.&#xa; The customer can use a Java SimpleDateFormat&#xa; String to define the time formatting used
</description>
<tag name="@see">#getDateFormatPattern</tag>
<tag name="@see">#setDateFormatPattern</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.numericPrecision -->
<field name="numericPrecision"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;numericPrecision&lt;/code&gt; property.&#xa; The number of decimal digits to show on exported floating point numbers
</description>
<tag name="@see">#getNumericPrecision</tag>
<tag name="@see">#setNumericPrecision</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.useEscapeCharacters -->
<field name="useEscapeCharacters"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;useEscapeCharacters&lt;/code&gt; property.&#xa; When *false* any escape chars encountered will be *unescaped* - eg $20 will become a &#x22; &#x22; or space character.
</description>
<tag name="@see">#getUseEscapeCharacters</tag>
<tag name="@see">#setUseEscapeCharacters</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.tuningPolicy -->
<field name="tuningPolicy"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;tuningPolicy&lt;/code&gt; property.&#xa; Tuning policies allow
</description>
<tag name="@see">#getTuningPolicy</tag>
<tag name="@see">#setTuningPolicy</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.overrides -->
<field name="overrides"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;overrides&lt;/code&gt; property.
</description>
<tag name="@see">#getOverrides</tag>
<tag name="@see">#setOverrides</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.debug -->
<field name="debug"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;debug&lt;/code&gt; property.
</description>
<tag name="@see">#getDebug</tag>
<tag name="@see">#setDebug</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaConfigFolder.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
