<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>webEditors Mixin: module:nmodule/webEditors/rc/wb/mgr/MgrLearn</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">webEditors</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-nmodule_webEditors_rc_fe_baja_BaseEditor.html">nmodule/webEditors/rc/fe/baja/BaseEditor</a></li><li><a href="module-nmodule_webEditors_rc_fe_BaseWidget.html">nmodule/webEditors/rc/fe/BaseWidget</a></li><li><a href="module-nmodule_webEditors_rc_fe_fe.html">nmodule/webEditors/rc/fe/fe</a></li><li><a href="module-nmodule_webEditors_rc_fe_feDialogs.html">nmodule/webEditors/rc/fe/feDialogs</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_commands_MgrCommand.html">nmodule/webEditors/rc/wb/mgr/commands/MgrCommand</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_Manager.html">nmodule/webEditors/rc/wb/mgr/Manager</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrLearn.html">nmodule/webEditors/rc/wb/mgr/MgrLearn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrStateHandler.html">nmodule/webEditors/rc/wb/mgr/MgrStateHandler</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_MgrTypeInfo.html">nmodule/webEditors/rc/wb/mgr/MgrTypeInfo</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_IconMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/IconMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_MixinPropMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/MixinPropMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_NameMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/NameMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_PropertyPathMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/PropertyPathMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_columns_TypeMgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/columns/TypeMgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrColumn.html">nmodule/webEditors/rc/wb/mgr/model/MgrColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_mgr_model_MgrModel.html">nmodule/webEditors/rc/wb/mgr/model/MgrModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Column.html">nmodule/webEditors/rc/wb/table/model/Column</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_DisplayNameColumn.html">nmodule/webEditors/rc/wb/table/model/columns/DisplayNameColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_IconColumn.html">nmodule/webEditors/rc/wb/table/model/columns/IconColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_JsonObjectPropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/JsonObjectPropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_PropertyColumn.html">nmodule/webEditors/rc/wb/table/model/columns/PropertyColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_columns_ToStringColumn.html">nmodule/webEditors/rc/wb/table/model/columns/ToStringColumn</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentSource.html">nmodule/webEditors/rc/wb/table/model/ComponentSource</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_ComponentTableModel.html">nmodule/webEditors/rc/wb/table/model/ComponentTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_Row.html">nmodule/webEditors/rc/wb/table/model/Row</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_model_TableModel.html">nmodule/webEditors/rc/wb/table/model/TableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_Table.html">nmodule/webEditors/rc/wb/table/Table</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeNodeRow.html">nmodule/webEditors/rc/wb/table/tree/TreeNodeRow</a></li><li><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeTableModel.html">nmodule/webEditors/rc/wb/table/tree/TreeTableModel</a></li><li><a href="module-nmodule_webEditors_rc_wb_tree_TreeNode.html">nmodule/webEditors/rc/wb/tree/TreeNode</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="tutorials.list.html" class="dropdown-toggle" data-toggle="dropdown">Tutorials<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="tutorial-6-managers.html">Managers</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Mixin: module:nmodule/webEditors/rc/wb/mgr/MgrLearn</h1>
<section>

<header>
    
        <h2>
        module:nmodule/webEditors/rc/wb/mgr/MgrLearn
        </h2>
        
    
</header>


<article>
    <div class="container-overview">
    
        
            <div class="description"><p>API Status: <strong>Development</strong></p>
<p>A mixin to provide learn support to a bajaux manager view.</p>
<p>To support discovery, in addition to applying this mixin, the target manager object must<br>
provide several functions that this mixin will use to accomplish the discovery and the<br>
creation of new components from the discovered items.</p>
<p>The concrete manager must provide a <code>makeLearnModel()</code> method. This should return a<br>
<code>Promise</code> that will resolve to a <code>TreeTableModel</code>. This will be used as the data model<br>
for the discovery table. On completion of the discovery job, the manager should use the<br>
result of the job to insert items into the discovery model.</p>
<p>The concrete manager must also provide an implementation of a <code>doDiscover()</code> function<br>
that will create a job (typically by invoking an action that will submit a job<br>
and return the ord), and then set the job on the manager via the <code>setJob()</code> function.<br>
This function will accept the job instance or the ord for a job, specified either as<br>
a <code>baja.Ord</code> or a string.</p>
<p>Once the job is complete, a 'jobcomplete' tinyevent will be emitted on the manager. The<br>
concrete manager will also typically have a handler for that event, which will get the<br>
discovered items from the job by some means, and then update the discovery table. This<br>
will normally involve inserting nodes into the learn model. The manager may store arbitrary<br>
data on those nodes, which it may retrieve later via the node's <code>value()</code> function.</p>
<p>The manager must also implement a <code>getTypesForDiscoverySubject()</code> function. This will be called<br>
when dragging an item from the discovery table to the database table or invoking the 'add'<br>
command. The function may be called several times, each time its argument will be a<br>
<code>TreeNode</code> representing the item to be added into to the database table. The implementation<br>
of this function is expected to return a single <code>MgrTypeInfo</code> instance or any array of them.<br>
These will be used to create a new component instance of the required type for the discovered<br>
node.</p>
<p>Also to support the addition of new components, the manager should implement a function<br>
called <code>getProposedValuesFromDiscovery()</code>. This will be passed the tree node that was dragged<br>
from the discovery table to the database table. The function should obtain any information<br>
the manager had set on the node at discovery time and use it to create an object containing<br>
the initial values for the new component. The names of properties on the object returned by<br>
the function will be compared against the column names in the main database model. For the<br>
columns that have matching names, the values of those properties will be used to set the<br>
initial proposed values on the new row(s) when the dialog for editing the new instances is<br>
displayed.</p></div>
        

        
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


        
            <h3>Example</h3>
            
        <p class="code-caption">Add the MgrLearn mixin to a Manager subclass to add learn
functionality.</p>
    
    <pre class="sunlight-highlight-javascript">require([...&#x27;nmodule/webEditors/rc/wb/mgr/MgrLearn&#x27;], function (...MgrLearn) {
  function MyManager() {
    Manager.apply(this, arguments);
    MgrLearn(this);
  }
  MyManager.prototype = Object.create(Manager.prototype);

  //implement abstract functions
  MyManager.prototype.doDiscover = function () { ...
});</pre>


        
    
    </div>

    
        <h3 class="subsection-title">Extends</h3>

        


    <ul>
        <li><a href="module-nmodule_webEditors_rc_wb_mgr_Manager.html">module:nmodule/webEditors/rc/wb/mgr/Manager</a></li>
    </ul>


    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id="doDiscover"><span class="type-signature">&lt;abstract> </span>doDiscover()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Abstract method used to initiate the discovery process. What this<br>
implementation does is a matter for the concrete manager, but the typical<br>
pattern will be to invoke an Action that will submit a job, and then set<br>
that job or its Ord on the manager via the <code>#setJob()</code> function.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>Optionally return a Promise</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>
|

<span class="param-type">*</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getExisting"><span class="type-signature"></span>getExisting(discovery)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Search for the existing component that matches the given node from the<br>
discovery table. To match a component, the concrete manager subclass<br>
must contain a function named <code>isExisting()</code> which will be passed the<br>
discovery object and a component. The function will be used as a predicate and<br>
should return true if the given component represents the same item as<br>
the discovery table item, false otherwise. If the manager does not provide<br>
such a function, all discovery nodes will be considered as not matching any<br>
existing components.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>discovery</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            

            

            <td class="description last"><p>a discovered object</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>the existing component that was found to match<br>
the given discovery node, or undefined if no such match was found.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise.&lt;(baja.Component|undefined)></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getJob"><span class="type-signature"></span>getJob()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the discovery job currently set against the manager.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">baja.Component</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getLearnModel"><span class="type-signature"></span>getLearnModel()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Get the learn model. The model will have been created via a call to <code>makeLearnModel()</code>; a<br>
function that the concrete manager must provide. This will return the <code>TreeTableModel</code><br>
resolved from the Promise.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="module-nmodule_webEditors_rc_wb_table_tree_TreeTableModel.html">module:nmodule/webEditors/rc/wb/table/tree/TreeTableModel</a></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getProposedValuesFromDiscovery"><span class="type-signature">&lt;abstract> </span>getProposedValuesFromDiscovery(discovery, subject)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Abstract method to get the initial values for a discovered node when it is<br>
being added to the station as a new component. This method should return an<br>
Object instance, with the values to be used by the new instances. The<br>
returned object may have a property called 'name', which will be used to<br>
set the slot name of the new component. It may also have a child object<br>
named 'values'. Each property of this object with a name that matches the<br>
name of a <code>Column</code> in the main table model will have that property's value<br>
used as the initial value when the component editor is displayed.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>discovery</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            

            

            <td class="description last"><p>an object obtained from a node in discovery table.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>subject</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            

            

            <td class="description last"><p>the subject of the <code>Row</code> whose values are to be<br>
proposed.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li>module:nmodule/webEditors/rc/wb/table/tree/TreeNode</li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>an object literal with the name and<br>
initial values to be used for the new component.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Object</span>
|

<span class="param-type">Promise.&lt;Object></span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
        <p class="code-caption">Return the initial values for the component name, and the
'version' and 'address' columns</p>
    
    <pre class="sunlight-highlight-javascript">MyDeviceMgr.prototype.getProposedValuesFromDiscovery = function (discovery) {
  return {
    name: discovery.deviceName,
    values: {
      address: discovery.address,
      version: discovery.firmwareVersionMajor + &#x27;.&#x27; + discovery.firmwareVersionMinor
    }
  };
};</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getTypesForDiscoverySubject"><span class="type-signature">&lt;abstract> </span>getTypesForDiscoverySubject(discovery)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Abstract method to get the Component type(s) that could be created for the<br>
given discovery node when adding it to the station as a component. If<br>
returning an array, the first element of the array should be the type that<br>
represents the best mapping for the discovery item.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>discovery</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            

            

            <td class="description last"><p>a discovery object</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>an array of TypeSpecs that could be<br>
constructed from this node, or a Promise resolving to one</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise.&lt;Array.&lt;string>></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="isExisting"><span class="type-signature"></span>isExisting(discovery, component)</h4>
    
    
</dt>
<dd>

    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>discovery</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            

            

            <td class="description last"><p>the discovery item</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>component</code></td>
            

            <td class="type">
            
                
<span class="param-type">baja.Component</span>



            
            </td>

            

            

            <td class="description last"><p>component already existing in local<br>
database</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>true if the local component already<br>
represents the discovery item</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">boolean</span>
|

<span class="param-type">Promise.&lt;boolean></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="makeDiscoveryCommands"><span class="type-signature"></span>makeDiscoveryCommands()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Creates and returns an array of discovery related commands.<br>
These are the LearnModeCommand (show/hide the learn pane),<br>
DiscoverCommand, CancelDiscoverCommand, AddCommand, and MatchCommand.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>a new array containing<br>
the discovery related commands.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array.&lt;module:bajaux/commands/Command></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="makeLearnModel"><span class="type-signature">&lt;abstract> </span>makeLearnModel()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Abstract method used to obtain the model for the learn tree table. This<br>
should return a <code>TreeTableModel</code>, or a Promise that resolves to one.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>a<br>
tree table model that will be used by the manager's discovery table.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise.&lt;<a href="module-nmodule_webEditors_rc_wb_table_tree_TreeTableModel.html">module:nmodule/webEditors/rc/wb/table/tree/TreeTableModel</a>></span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="newInstanceFromDiscoverySubject"><span class="type-signature"></span>newInstanceFromDiscoverySubject(discovery, typeInfos)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Creates a new component instance from the types the manager specified<br>
for a particular node in the discovery table. If the manager returned<br>
more than one type, this default implementation will return a new<br>
instance based on the first type info.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>discovery</code></td>
            

            <td class="type">
            
                
<span class="param-type">*</span>



            
            </td>

            

            

            <td class="description last"><p>an instance of a discovery object (e.g. an<br>
<code>ndriver:NDiscoveryLeaf</code>), dragged from the discovery table and dropped<br>
onto the database table or selected when the 'Add' command was invoked.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>typeInfos</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="module-nmodule_webEditors_rc_wb_mgr_MgrTypeInfo.html">module:nmodule/webEditors/rc/wb/mgr/MgrTypeInfo</a>></span>



            
            </td>

            

            

            <td class="description last"><p>an<br>
array of MgrTypeInfos, created from the type or types returned<br>
by the manager's <code>getTypesForDiscoverySubject()</code> implementation.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>a Promise of new component instance for the discovered item<br>
based on the provided type information.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="setJob"><span class="type-signature"></span>setJob(params)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Attach a job to this manager, typically as part of a driver discovery process.<br>
The act of attaching a job will subscribe to it, and cause a 'jobcomplete' event<br>
to be emitted once the job is complete. A manager will typically update the learn<br>
model at that point.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>params</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>an Object literal containing the parameters for this function.</p>
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Argument</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>jobOrOrd</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>
|

<span class="param-type">baja.Ord</span>
|

<span class="param-type">baja.Component</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>either a BJob instance, an<br>
Ord referencing a job, or a String containing the ord for a job.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>depth</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>optional parameter that will be used when subscribing to<br>
the job once it has completed; this allows the job plus its final set of children to<br>
be subscribed. A depth of 1 will subscribe to the job, 2 will subscribe the job and<br>
its children, and so on. Subscription will default to a depth of 1 if this parameter<br>
is not specified.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Promise</span>



    </dd>
</dl>


        

    
</dd>

        </dl>
    

    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	webEditors Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:29:00+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>