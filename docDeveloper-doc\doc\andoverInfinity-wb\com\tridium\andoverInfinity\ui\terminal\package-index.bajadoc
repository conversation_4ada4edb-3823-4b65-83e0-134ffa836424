<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="andoverInfinity" runtimeProfile="wb" name="com.tridium.andoverInfinity.ui.terminal">
<description/>
<class packageName="com.tridium.andoverInfinity.ui.terminal" name="BInfinityVirtualTerminal"><description>BInfinityVirtualTerminal - This view provides the user with&#xa; view into the VT100 interface of the infinity network.</description></class>
<class packageName="com.tridium.andoverInfinity.ui.terminal" name="InfinityVt100TextController"><description>InfinityVt100TextController extends TextController to&#xa; intercept mouse and keyboard input and route&#xa; to the network object of andover infinity.</description></class>
<class packageName="com.tridium.andoverInfinity.ui.terminal" name="InfinityVt100TextModel"><description>InfinityVt100TextModel&#xa; &#xa; Custom TextModel class for Infinity VT100 terminal view.</description></class>
<class packageName="com.tridium.andoverInfinity.ui.terminal" name="InfinityVt100TextParser"><description>InfinityVt100TextParser&#xa; &#xa; Custom parser for VT100 manager view.</description></class>
</package>
</bajadoc>
