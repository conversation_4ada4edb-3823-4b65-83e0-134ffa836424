<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.datatypes.BUploadRegionParams" name="BUploadRegionParams" packageName="com.tridium.aapup.datatypes" public="true">
<description/>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="regionNumber" flags="r">
<type class="int"/>
<description>
Slot for the &lt;code&gt;regionNumber&lt;/code&gt; property.&#xa; Specifies the region number to upload from
</description>
<tag name="@see">#getRegionNumber</tag>
<tag name="@see">#setRegionNumber</tag>
</property>

<property name="regionName" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;regionName&lt;/code&gt; property.&#xa; text description of the region, 8 chars max
</description>
<tag name="@see">#getRegionName</tag>
<tag name="@see">#setRegionName</tag>
</property>

<property name="regionSize" flags="r">
<type class="int"/>
<description>
Slot for the &lt;code&gt;regionSize&lt;/code&gt; property.&#xa; The size of the region
</description>
<tag name="@see">#getRegionSize</tag>
<tag name="@see">#setRegionSize</tag>
</property>

<property name="postSessionId" flags="h">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;postSessionId&lt;/code&gt; property.&#xa; Station side fox session id
</description>
<tag name="@see">#getPostSessionId</tag>
<tag name="@see">#setPostSessionId</tag>
</property>

<property name="postPath" flags="h">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;postPath&lt;/code&gt; property.&#xa; Path on remote machine to post to
</description>
<tag name="@see">#getPostPath</tag>
<tag name="@see">#setPostPath</tag>
</property>

</class>
</bajadoc>
