<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.BBacnetObject" name="BBacnetObject" packageName="javax.baja.bacnet" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 17$ $Date: 12/17/01 9:14:09 AM$</tag>
<tag name="@creation">20 Jul 00</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.driver.loadable.BLoadable"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<implements>
<type class="javax.baja.bacnet.util.BIBacnetPollable"/>
</implements>
<property name="pollFrequency" flags="">
<type class="javax.baja.driver.util.BPollFrequency"/>
<description>
Slot for the &lt;code&gt;pollFrequency&lt;/code&gt; property.
</description>
<tag name="@see">#getPollFrequency</tag>
<tag name="@see">#setPollFrequency</tag>
</property>

<property name="status" flags="tr">
<type class="javax.baja.status.BStatus"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</property>

<property name="faultCause" flags="tr">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</property>

<property name="objectId" flags="s">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="objectName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;objectName&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectName</tag>
<tag name="@see">#setObjectName</tag>
</property>

<property name="objectType" flags="r">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</property>

<action name="readBacnetProperty" flags="h">
<parameter name="parameter">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
<description>
Slot for the &lt;code&gt;readBacnetProperty&lt;/code&gt; action.
</description>
<tag name="@see">#readBacnetProperty(BEnum parameter)</tag>
</action>

<action name="writeBacnetProperty" flags="h">
<parameter name="parameter">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;writeBacnetProperty&lt;/code&gt; action.
</description>
<tag name="@see">#writeBacnetProperty(BEnum parameter)</tag>
</action>

<action name="uploadRequiredProperties" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;uploadRequiredProperties&lt;/code&gt; action.
</description>
<tag name="@see">#uploadRequiredProperties()</tag>
</action>

<action name="uploadOptionalProperties" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;uploadOptionalProperties&lt;/code&gt; action.
</description>
<tag name="@see">#uploadOptionalProperties()</tag>
</action>

<!-- javax.baja.bacnet.BBacnetObject() -->
<constructor name="BBacnetObject" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.BBacnetObject.getPollFrequency() -->
<method name="getPollFrequency"  public="true">
<description>
Get the &lt;code&gt;pollFrequency&lt;/code&gt; property.
</description>
<tag name="@see">#pollFrequency</tag>
<return>
<type class="javax.baja.driver.util.BPollFrequency"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.setPollFrequency(javax.baja.driver.util.BPollFrequency) -->
<method name="setPollFrequency"  public="true">
<description>
Set the &lt;code&gt;pollFrequency&lt;/code&gt; property.
</description>
<tag name="@see">#pollFrequency</tag>
<parameter name="v">
<type class="javax.baja.driver.util.BPollFrequency"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.getStatus() -->
<method name="getStatus"  public="true">
<description>
Get the &lt;code&gt;status&lt;/code&gt; property.
</description>
<tag name="@see">#status</tag>
<return>
<type class="javax.baja.status.BStatus"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.setStatus(javax.baja.status.BStatus) -->
<method name="setStatus"  public="true">
<description>
Set the &lt;code&gt;status&lt;/code&gt; property.
</description>
<tag name="@see">#status</tag>
<parameter name="v">
<type class="javax.baja.status.BStatus"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.getFaultCause() -->
<method name="getFaultCause"  public="true">
<description>
Get the &lt;code&gt;faultCause&lt;/code&gt; property.
</description>
<tag name="@see">#faultCause</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.setFaultCause(java.lang.String) -->
<method name="setFaultCause"  public="true">
<description>
Set the &lt;code&gt;faultCause&lt;/code&gt; property.
</description>
<tag name="@see">#faultCause</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.getObjectId() -->
<method name="getObjectId"  public="true">
<description>
Get the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#objectId</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.setObjectId(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="setObjectId"  public="true">
<description>
Set the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#objectId</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.getObjectName() -->
<method name="getObjectName"  public="true">
<description>
Get the &lt;code&gt;objectName&lt;/code&gt; property.
</description>
<tag name="@see">#objectName</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.setObjectName(java.lang.String) -->
<method name="setObjectName"  public="true">
<description>
Set the &lt;code&gt;objectName&lt;/code&gt; property.
</description>
<tag name="@see">#objectName</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.getObjectType() -->
<method name="getObjectType"  public="true">
<description>
Get the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#objectType</tag>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.setObjectType(javax.baja.sys.BEnum) -->
<method name="setObjectType"  public="true">
<description>
Set the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#objectType</tag>
<parameter name="v">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.readBacnetProperty(javax.baja.sys.BEnum) -->
<method name="readBacnetProperty"  public="true">
<description>
Invoke the &lt;code&gt;readBacnetProperty&lt;/code&gt; action.
</description>
<tag name="@see">#readBacnetProperty</tag>
<parameter name="parameter">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.writeBacnetProperty(javax.baja.sys.BEnum) -->
<method name="writeBacnetProperty"  public="true">
<description>
Invoke the &lt;code&gt;writeBacnetProperty&lt;/code&gt; action.
</description>
<tag name="@see">#writeBacnetProperty</tag>
<parameter name="parameter">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.uploadRequiredProperties() -->
<method name="uploadRequiredProperties"  public="true">
<description>
Invoke the &lt;code&gt;uploadRequiredProperties&lt;/code&gt; action.
</description>
<tag name="@see">#uploadRequiredProperties</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.uploadOptionalProperties() -->
<method name="uploadOptionalProperties"  public="true">
<description>
Invoke the &lt;code&gt;uploadOptionalProperties&lt;/code&gt; action.
</description>
<tag name="@see">#uploadOptionalProperties</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.make(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="make"  public="true" static="true">
<description>
Create a new BBacnetObject from the given object-identifier.
</description>
<parameter name="id">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the object-identifier specifying the new object.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.BBacnetObject"/>
<description>
a BBacnetObject with the given objectId.
</description>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.getTypeInfo(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="getTypeInfo"  public="true" static="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Get the TypeInfo for the given BACnet Object ID.
</description>
<tag name="@deprecated">Use getTypeInfos(BBacnetObjectIdentifier) instead</tag>
<parameter name="id">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="javax.baja.registry.TypeInfo"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.getTypeInfos(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="getTypeInfos"  public="true" static="true">
<description>
Get the TypeInfos available for the given BACnet Object ID.
</description>
<parameter name="id">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="javax.baja.registry.TypeInfo" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.started() -->
<method name="started"  public="true">
<description>
Started.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.BBacnetObject.stopped() -->
<method name="stopped"  public="true">
<description>
Stopped.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.BBacnetObject.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<description>
Property changed.
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.getSlotFacets(javax.baja.sys.Slot) -->
<method name="getSlotFacets"  public="true">
<description>
For objectId, get the facets from the device&#x27;s object type facets.
</description>
<parameter name="slot">
<type class="javax.baja.sys.Slot"/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.subscribed() -->
<method name="subscribed"  public="true">
<description>
Callback when the component enters the subscribed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.unsubscribed() -->
<method name="unsubscribed"  public="true">
<description>
Callback when the component exits the subscribed state.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.doUpload(javax.baja.driver.loadable.BUploadParameters, javax.baja.sys.Context) -->
<method name="doUpload"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Callback for processing upload on async thread.&#xa; Default implementation is to call asyncUpload on all&#xa; children implementing the Loadable interface.
</description>
<parameter name="p">
<type class="javax.baja.driver.loadable.BUploadParameters"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.doUploadRequiredProperties() -->
<method name="doUploadRequiredProperties"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.doUploadOptionalProperties() -->
<method name="doUploadOptionalProperties"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.doDownload(javax.baja.driver.loadable.BDownloadParameters, javax.baja.sys.Context) -->
<method name="doDownload"  public="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Callback for processing download on async thread.&#xa; Default implementation is to call asyncDownload on all&#xa; children implementing the  Loadable interface.
</description>
<parameter name="p">
<type class="javax.baja.driver.loadable.BDownloadParameters"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.doReadBacnetProperty(javax.baja.sys.BEnum) -->
<method name="doReadBacnetProperty"  public="true">
<description>
Read a property.
</description>
<parameter name="propId">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.BBacnetObject.doWriteBacnetProperty(javax.baja.sys.BEnum) -->
<method name="doWriteBacnetProperty"  public="true">
<description>
Write a property.
</description>
<parameter name="propId">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.BBacnetObject.network() -->
<method name="network"  protected="true" final="true">
<description/>
<return>
<type class="javax.baja.bacnet.BBacnetNetwork"/>
<description>
the BBacnetNetwork containing this BBacnetObject.
</description>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.device() -->
<method name="device"  public="true" final="true">
<description/>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
<description>
the BBacnetDevice containing this BBacnetObject.
</description>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.config() -->
<method name="config"  protected="true" final="true">
<description/>
<return>
<type class="javax.baja.bacnet.config.BBacnetConfigDeviceExt"/>
<description>
the BBacnetDevice containing this BBacnetObject.
</description>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.getPresentValueProperty() -->
<method name="getPresentValueProperty"  public="true">
<description>
Subclasses that have a present value property should&#xa; override this method and return this property.  The&#xa; default returns null.
</description>
<return>
<type class="javax.baja.sys.Property"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.setOutputFacets() -->
<method name="setOutputFacets"  protected="true">
<description>
Set the output facets.&#xa; Object types with properties containing meta-data about the main value&#xa; can use this to set a slot containing these facets.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.shouldPoll(int) -->
<method name="shouldPoll"  protected="true">
<description>
Should this property ID be polled?&#xa; Override point for objects to filter properties for polling, e.g.,&#xa; Object_List in Device object, or Log_Buffer in Trend Log.
</description>
<parameter name="propertyId">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.toEncodedValue(javax.baja.bacnet.BBacnetObject.BacnetPropertyData, javax.baja.sys.Property) -->
<method name="toEncodedValue"  protected="true">
<description>
Convert the property to an ASN.1-encoded byte array.&#xa; Subclasses with properties requiring specialized encoding&#xa; may need to override this method.
</description>
<parameter name="d">
<type class="javax.baja.bacnet.BBacnetObject$BacnetPropertyData"/>
<description/>
</parameter>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
<description/>
</parameter>
<return>
<type class="byte" dimension="1"/>
<description>
encoded byte array
</description>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.readProperty(javax.baja.sys.Property) -->
<method name="readProperty"  public="true">
<description>
Read a Bacnet property.
</description>
<parameter name="prop">
<type class="javax.baja.sys.Property"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.BBacnetObject.writeProperty(javax.baja.sys.Property) -->
<method name="writeProperty"  public="true">
<description>
Write a Bacnet property.
</description>
<parameter name="prop">
<type class="javax.baja.sys.Property"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.BBacnetObject.writeProperty(javax.baja.sys.Property, int, byte[]) -->
<method name="writeProperty"  public="true">
<description>
Write a Bacnet property.
</description>
<parameter name="prop">
<type class="javax.baja.sys.Property"/>
<description/>
</parameter>
<parameter name="arrayIndex">
<type class="int"/>
<description/>
</parameter>
<parameter name="encodedValue">
<type class="byte" dimension="1"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.BBacnetObject.addListElement(javax.baja.sys.Property, javax.baja.sys.BValue) -->
<method name="addListElement"  public="true">
<description>
Add an element to a list property.
</description>
<parameter name="prop">
<type class="javax.baja.sys.Property"/>
<description/>
</parameter>
<parameter name="listElement">
<type class="javax.baja.sys.BValue"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.BBacnetObject.removeListElement(javax.baja.sys.Property, javax.baja.sys.BValue) -->
<method name="removeListElement"  public="true">
<description>
Remove an element from a list property.
</description>
<parameter name="prop">
<type class="javax.baja.sys.Property"/>
<description/>
</parameter>
<parameter name="listElement">
<type class="javax.baja.sys.BValue"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.BBacnetObject.getPollableType() -->
<method name="getPollableType"  public="true" final="true">
<description>
Get the pollable type of this object.
</description>
<return>
<type class="int"/>
<description>
one of the pollable types defined in BIBacnetPollable.
</description>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.poll() -->
<method name="poll"  public="true" final="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Poll the object.
</description>
<tag name="@deprecated"/>
<return>
<type class="boolean"/>
<description>
true if the object was successfully polled, false if not.
</description>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.readOk() -->
<method name="readOk"  public="true" final="true">
<description>
Indicate successful poll.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.readFail(java.lang.String) -->
<method name="readFail"  public="true" final="true">
<description>
Indicate a failure polling this object.
</description>
<parameter name="failureMsg">
<type class="java.lang.String"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.fromEncodedValue(byte[], javax.baja.status.BStatus, javax.baja.sys.Context) -->
<method name="fromEncodedValue"  public="true" final="true">
<description>
Normalize the encoded data into the pollable&#x27;s data structure.
</description>
<parameter name="encodedValue">
<type class="byte" dimension="1"/>
<description/>
</parameter>
<parameter name="status">
<type class="javax.baja.status.BStatus"/>
<description/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.getPollListEntries() -->
<method name="getPollListEntries"  public="true" final="true">
<description>
Get the list of poll list entries for this pollable.&#xa; The first entry for points must be the configured property.
</description>
<return>
<type class="javax.baja.bacnet.util.PollListEntry" dimension="1"/>
<description>
the list of poll list entries.
</description>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.buildPolledProperties() -->
<method name="buildPolledProperties"  protected="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<description>
Spy.
</description>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.BBacnetObject.getIcon() -->
<method name="getIcon"  public="true">
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.makeFacets(int, int) -->
<method name="makeFacets"  protected="true" static="true">
<description>
Make a BFacets with property ID and Asn type.
</description>
<parameter name="propertyId">
<type class="int"/>
</parameter>
<parameter name="asnType">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.makeFacets(int, int, java.util.Map&lt;java.lang.String, javax.baja.data.BIDataValue&gt;) -->
<method name="makeFacets"  protected="true" static="true">
<description>
Make a BFacets with property ID, Asn type, and a Map&#xa; which contains additional info.&#xa; Used for bit strings, to name the bits.
</description>
<parameter name="propertyId">
<type class="int"/>
</parameter>
<parameter name="asnType">
<type class="int"/>
</parameter>
<parameter name="m">
<parameterizedType class="java.util.Map">
<args>
<type class="java.lang.String"/>
<type class="javax.baja.data.BIDataValue"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.makeFacets(int, int, java.lang.String[], javax.baja.data.BIDataValue[]) -->
<method name="makeFacets"  protected="true" static="true">
<description>
Make a BFacets with property ID, Asn type, and two arrays of additional&#xa; keys and values.
</description>
<parameter name="propertyId">
<type class="int"/>
</parameter>
<parameter name="asnType">
<type class="int"/>
</parameter>
<parameter name="keys">
<type class="java.lang.String" dimension="1"/>
</parameter>
<parameter name="values">
<type class="javax.baja.data.BIDataValue" dimension="1"/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.makeFacets(javax.baja.bacnet.util.PropertyInfo, javax.baja.sys.BValue) -->
<method name="makeFacets"  protected="true" static="true">
<description>
Make a BFacets from the given &lt;code&gt;PropertyInfo&lt;/code&gt;.&#xa; Used in dynamic creation of properties.
</description>
<parameter name="info">
<type class="javax.baja.bacnet.util.PropertyInfo"/>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.getPropertyData(javax.baja.sys.Property) -->
<method name="getPropertyData"  protected="true">
<description/>
<parameter name="prop">
<type class="javax.baja.sys.Property"/>
</parameter>
<return>
<type class="javax.baja.bacnet.BBacnetObject$BacnetPropertyData"/>
</return>
</method>

<!-- javax.baja.bacnet.BBacnetObject.pollFrequency -->
<field name="pollFrequency"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;pollFrequency&lt;/code&gt; property.
</description>
<tag name="@see">#getPollFrequency</tag>
<tag name="@see">#setPollFrequency</tag>
</field>

<!-- javax.baja.bacnet.BBacnetObject.status -->
<field name="status"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</field>

<!-- javax.baja.bacnet.BBacnetObject.faultCause -->
<field name="faultCause"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</field>

<!-- javax.baja.bacnet.BBacnetObject.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnet.BBacnetObject.objectName -->
<field name="objectName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectName&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectName</tag>
<tag name="@see">#setObjectName</tag>
</field>

<!-- javax.baja.bacnet.BBacnetObject.objectType -->
<field name="objectType"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectType&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectType</tag>
<tag name="@see">#setObjectType</tag>
</field>

<!-- javax.baja.bacnet.BBacnetObject.download -->
<field name="download"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;download&lt;/code&gt; action.
</description>
<tag name="@see">#download(BDownloadParameters parameter)</tag>
</field>

<!-- javax.baja.bacnet.BBacnetObject.readBacnetProperty -->
<field name="readBacnetProperty"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;readBacnetProperty&lt;/code&gt; action.
</description>
<tag name="@see">#readBacnetProperty(BEnum parameter)</tag>
</field>

<!-- javax.baja.bacnet.BBacnetObject.writeBacnetProperty -->
<field name="writeBacnetProperty"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;writeBacnetProperty&lt;/code&gt; action.
</description>
<tag name="@see">#writeBacnetProperty(BEnum parameter)</tag>
</field>

<!-- javax.baja.bacnet.BBacnetObject.uploadRequiredProperties -->
<field name="uploadRequiredProperties"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;uploadRequiredProperties&lt;/code&gt; action.
</description>
<tag name="@see">#uploadRequiredProperties()</tag>
</field>

<!-- javax.baja.bacnet.BBacnetObject.uploadOptionalProperties -->
<field name="uploadOptionalProperties"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;uploadOptionalProperties&lt;/code&gt; action.
</description>
<tag name="@see">#uploadOptionalProperties()</tag>
</field>

<!-- javax.baja.bacnet.BBacnetObject.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.BBacnetObject.lex -->
<field name="lex"  protected="true" static="true" final="true">
<type class="javax.baja.util.Lexicon"/>
<description/>
</field>

<!-- javax.baja.bacnet.BBacnetObject.log -->
<field name="log"  public="true" static="true" final="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

<!-- javax.baja.bacnet.BBacnetObject.plog -->
<field name="plog"  public="true" static="true" final="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

<!-- javax.baja.bacnet.BBacnetObject.polledProperties -->
<field name="polledProperties"  protected="true" volatile="true">
<parameterizedType class="java.util.ArrayList">
<args>
<type class="javax.baja.bacnet.util.PollListEntry"/>
</args>
</parameterizedType>
<description>
List of BACnetPropertyReferences for the properties of this object.
</description>
</field>

<!-- javax.baja.bacnet.BBacnetObject.PID -->
<field name="PID"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Property ID facet name.
</description>
</field>

<!-- javax.baja.bacnet.BBacnetObject.ASN_TYPE -->
<field name="ASN_TYPE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description>
Asn Type facet name.
</description>
</field>

</class>
</bajadoc>
