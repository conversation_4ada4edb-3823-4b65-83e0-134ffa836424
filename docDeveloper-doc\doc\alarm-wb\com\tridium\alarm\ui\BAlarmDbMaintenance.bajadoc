<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.ui.BAlarmDbMaintenance" name="BAlarmDbMaintenance" packageName="com.tridium.alarm.ui" public="true">
<description>
BAlarmDbMaintenance provides a view of all AlarmRecords in the db&#xa; as well as a way to add notes to records no longer appearing in &#xa; the AlarmConsole and a way clear old records from the db.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">05 Oct 04</tag>
<tag name="@version">$Revision: 23$ $Date: 5/9/08 2:06:12 PM EDT$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="com.tridium.alarm.ui.BAlarmDbView"/>
</extends>
</class>
</bajadoc>
