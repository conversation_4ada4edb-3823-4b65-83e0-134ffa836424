<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="baja" runtimeProfile="rt" name="javax.baja.job">
<description>
&lt;p&gt;Jobs are used to manage tasks which run asynchronously in &#xa;the background but require user visiblity.&lt;/p&gt;
</description>
<class packageName="javax.baja.job" name="BJob"><description>BJob is used to manage tasks which run asynchronously in &#xa; the background but require user visibility.</description></class>
<class packageName="javax.baja.job" name="BJobLogSequence"><description>Struct encapsulating a number of encoded JobLogItems and associated sequence numbers&#xa; for the first and last log items in the contained sequence of items.</description></class>
<class packageName="javax.baja.job" name="BJobService"><description>BJobService is used to manage all the BJobs in a station VM.</description></class>
<class packageName="javax.baja.job" name="BJobService.MonitorWorker"><description>Monitor the thread pool</description></class>
<class packageName="javax.baja.job" name="BJobService.UncaughtJobExceptionHandler"><description>Handle ForkJoinPool uncaught exceptions</description></class>
<class packageName="javax.baja.job" name="BJobState"><description>BJobState enumerates the state machine of a Job&#x27;s lifecycle.</description></class>
<class packageName="javax.baja.job" name="BRunnableJob"><description>BRunnableJob provides a simple job implementations &#xa; for executing runnable tasks in the job framework.</description></class>
<class packageName="javax.baja.job" name="BSimpleJob"><description>BSimpleJob provides a base class to use for simple &#xa; implementations which just launch a new background&#xa; thread for processing.</description></class>
<class packageName="javax.baja.job" name="JobLog"><description>JobLog is a list of JobLogItems which provide a &#xa; detailed account of a Job&#x27;s execution.</description></class>
<class packageName="javax.baja.job" name="JobLogItem"><description>JobLogItem is a record within a JobLog.</description></class>
<class packageName="javax.baja.job" name="BIJobService" category="interface"><description>IJobService provides a common interface used for managing jobs&#xa; consistently in both a station and workbench environment.</description></class>
<class packageName="javax.baja.job" name="JobCancelException" category="exception"><description>JobCancelException is the desired exception to used to &#xa; exit out of a method during a BJob.cancel.</description></class>
</package>
</bajadoc>
