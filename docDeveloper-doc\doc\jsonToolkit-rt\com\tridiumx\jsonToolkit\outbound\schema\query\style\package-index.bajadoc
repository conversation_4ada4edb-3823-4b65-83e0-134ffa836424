<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.outbound.schema.query.style">
<description/>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.query.style" name="BColumnArray"><description>A query result writer which renders the results as an array of arrays,&#xa; where each array contains the values for a given column.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.query.style" name="BColumnArrayWithHeader"><description>A query result writer as per the &lt;code&gt;<see ref="com.tridiumx.jsonToolkit.outbound.schema.query.style.BColumnArray">BColumnArray</see>&lt;/code&gt; with the column&#xa;  name as the first entry in each array.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.query.style" name="BKeyValuePairObject"><description>A query result writer which renders an object containing each&#xa; row as a key value pair using the first 2 columns.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.query.style" name="BNamedObjects"><description>A query result writer which renders an object containing a named object for each row.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.query.style" name="BObjectsArray"><description>A query result writer which renders an array containing an object per row.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.query.style" name="BQueryResultWriter"><description>Base class for all query result writers.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.query.style" name="BRowArray"><description>A query result writer which renders each row of the result table as a&#xa; json array within a containing array.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.query.style" name="BRowArrayWithHeader"><description>A query result writer as per the &lt;code&gt;<see ref="com.tridiumx.jsonToolkit.outbound.schema.query.style.BRowArray">BRowArray</see>&lt;/code&gt; with a header row&#xa; that includes the column names.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.query.style" name="BSingleColumnArray"><description>A query result writer which renders the results of a query with one column only as a&#xa; single array.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.query.style" name="QueryStyleManager"><description>Maintains the list of registered query output style writers.</description></class>
</package>
</bajadoc>
