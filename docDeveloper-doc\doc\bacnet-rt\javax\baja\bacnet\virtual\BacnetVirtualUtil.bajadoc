<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.virtual.BacnetVirtualUtil" name="BacnetVirtualUtil" packageName="javax.baja.bacnet.virtual" public="true">
<description>
BacnetVirtualUtil provides common utility functions used by&#xa; the BACnet virtual point framework.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">02 Nov 2007</tag>
<tag name="@since">NiagaraAX 3.2</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<!-- javax.baja.bacnet.virtual.BacnetVirtualUtil() -->
<constructor name="BacnetVirtualUtil" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.virtual.BacnetVirtualUtil.isVirtual(javax.baja.sys.BComponent) -->
<method name="isVirtual"  public="true" static="true">
<description>
Is this component in a virtual space?  This should identify virtual spaces&#xa; on both the client (workbench) and server (station) sides.
</description>
<parameter name="c">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this component is in a virtual space.
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BacnetVirtualUtil.getVirtualProperty(javax.baja.sys.BComponent) -->
<method name="getVirtualProperty"  public="true" static="true">
<description>
Get the containing BBacnetVirtualProperty if it exists.&#xa; Short circuit if this component is not in a virtual space.
</description>
<parameter name="c">
<type class="javax.baja.sys.BComponent"/>
<description/>
</parameter>
<return>
<type class="javax.baja.bacnet.virtual.BBacnetVirtualProperty"/>
<description>
bacnetVirtualProperty
</description>
</return>
</method>

</class>
</bajadoc>
