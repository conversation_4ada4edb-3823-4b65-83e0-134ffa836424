<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BSourceState" name="BSourceState" packageName="javax.baja.alarm" public="true" final="true">
<description>
BSourceState represents the state of an alarm source.&#xa;&#xa; &lt;ul&gt;&#xa;  &lt;li&gt;offnormal: the source is out of the defined normal range&lt;/li&gt;&#xa;  &lt;li&gt;fault: the source is is reporting an invalid value/condition&lt;/li&gt;&#xa;  &lt;li&gt;normal: the source is in its normal range&lt;/li&gt;&#xa;  &lt;li&gt;alert: an alarm that does not have a normal state&lt;/li&gt;&#xa; &lt;/ul&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">14 Sep 04</tag>
<tag name="@version">$Revision: 5$ $Date: 3/30/05 11:35:59 AM EST$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;normal&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;offnormal&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;fault&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;alert&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.alarm.BSourceState.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.alarm.BSourceState"/>
</return>
</method>

<!-- javax.baja.alarm.BSourceState.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.alarm.BSourceState"/>
</return>
</method>

<!-- javax.baja.alarm.BSourceState.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.BSourceState.getAlarmTransitionBits() -->
<method name="getAlarmTransitionBits"  public="true">
<description/>
<return>
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</return>
</method>

<!-- javax.baja.alarm.BSourceState.NORMAL -->
<field name="NORMAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for normal.
</description>
</field>

<!-- javax.baja.alarm.BSourceState.OFFNORMAL -->
<field name="OFFNORMAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for offnormal.
</description>
</field>

<!-- javax.baja.alarm.BSourceState.FAULT -->
<field name="FAULT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for fault.
</description>
</field>

<!-- javax.baja.alarm.BSourceState.ALERT -->
<field name="ALERT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for alert.
</description>
</field>

<!-- javax.baja.alarm.BSourceState.normal -->
<field name="normal"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BSourceState"/>
<description>
BSourceState constant for normal.
</description>
</field>

<!-- javax.baja.alarm.BSourceState.offnormal -->
<field name="offnormal"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BSourceState"/>
<description>
BSourceState constant for offnormal.
</description>
</field>

<!-- javax.baja.alarm.BSourceState.fault -->
<field name="fault"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BSourceState"/>
<description>
BSourceState constant for fault.
</description>
</field>

<!-- javax.baja.alarm.BSourceState.alert -->
<field name="alert"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BSourceState"/>
<description>
BSourceState constant for alert.
</description>
</field>

<!-- javax.baja.alarm.BSourceState.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BSourceState"/>
<description/>
</field>

<!-- javax.baja.alarm.BSourceState.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
