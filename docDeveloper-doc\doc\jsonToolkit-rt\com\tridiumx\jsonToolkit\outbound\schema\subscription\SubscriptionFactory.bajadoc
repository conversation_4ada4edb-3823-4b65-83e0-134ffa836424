<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionFactory" name="SubscriptionFactory" packageName="com.tridiumx.jsonToolkit.outbound.schema.subscription" public="true" final="true">
<description>
A factory for constructing subscriptions based upon the binding type and target type.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionFactory.makeSubscription(com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaBoundMember, javax.baja.naming.OrdTarget) -->
<method name="makeSubscription"  public="true" static="true">
<description/>
<parameter name="member">
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaBoundMember"/>
</parameter>
<parameter name="ordTarget">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.subscription.Subscription"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionFactory.getSubscriptionTarget(javax.baja.naming.OrdTarget, com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaBoundMember) -->
<method name="getSubscriptionTarget"  public="true" static="true">
<description/>
<parameter name="ordTarget">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<parameter name="binding">
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaBoundMember"/>
</parameter>
<return>
<type class="javax.baja.sys.BComponent"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionFactory.PROPAGATE_FILTER -->
<field name="PROPAGATE_FILTER"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionEventFilter"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionFactory.IGNORE_ALL_FILTER -->
<field name="IGNORE_ALL_FILTER"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionEventFilter"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionFactory.COMPONENT_CHANGED_FILTER -->
<field name="COMPONENT_CHANGED_FILTER"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionEventFilter"/>
<description/>
</field>

</class>
</bajadoc>
