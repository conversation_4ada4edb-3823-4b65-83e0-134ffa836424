<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.point.BActionPoint" name="BActionPoint" packageName="com.tridium.ace.point" public="true">
<description>
Implements an action point, which is an integration of a single&#xa; action from a proxy, similar to what BControlPoint does for a&#xa; single value.
</description>
<tag name="@author"><PERSON> on 6/14/2017.</tag>
<extends>
<type class="javax.baja.control.BControlPoint"/>
</extends>
<property name="out" flags="">
<type class="javax.baja.status.BStatusBoolean"/>
<description>
Slot for the &lt;code&gt;out&lt;/code&gt; property.
</description>
<tag name="@see">#getOut</tag>
<tag name="@see">#setOut</tag>
</property>

</class>
</bajadoc>
