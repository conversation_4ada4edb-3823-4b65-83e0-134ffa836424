<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="aapup" runtimeProfile="rt" name="com.tridium.aapup.job">
<description/>
<class packageName="com.tridium.aapup.job" name="BPupDiscoverDevicesJob"><description>PUP Device Discovery.</description></class>
<class packageName="com.tridium.aapup.job" name="BPupDiscoverPointsJob"><description>PUP Point Discovery.</description></class>
<class packageName="com.tridium.aapup.job" name="BPupDiscoveryDevice"><description>PUP Discovery Device.</description></class>
<class packageName="com.tridium.aapup.job" name="BPupDiscoveryPoint"><description>PUP Discovery Point models a point as it is learned but before it&#xa; is added to the database</description></class>
<class packageName="com.tridium.aapup.job" name="BPupDownloadRegionJob"><description>PUP Download SPL&#xa; This job class handles the implementation of download&#xa; of SPL programs to a given region in a controller</description></class>
<class packageName="com.tridium.aapup.job" name="BPupLearnRegionsJob"><description>PUP Region Discovery.</description></class>
<class packageName="com.tridium.aapup.job" name="BPupUploadRegionJob"><description>PUP Upload Region Job&#xa; This job class handles the implementation of upload&#xa; of SPL programs from a given region in a controller to a file</description></class>
</package>
</bajadoc>
