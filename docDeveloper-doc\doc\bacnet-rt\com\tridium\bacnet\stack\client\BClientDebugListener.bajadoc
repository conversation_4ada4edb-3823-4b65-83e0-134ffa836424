<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.client.BClientDebugListener" name="BClientDebugListener" packageName="com.tridium.bacnet.stack.client" public="true">
<description/>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="com.tridium.bacnet.stack.AppDebugListener"/>
</implements>
<property name="enabled" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;enabled&lt;/code&gt; property.&#xa; is this listener enabled?&#xa; if false, no debug is printed.
</description>
<tag name="@see">#getEnabled</tag>
<tag name="@see">#setEnabled</tag>
</property>

<property name="deviceNameEnabled" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;deviceNameEnabled&lt;/code&gt; property.&#xa; should this listener filter packets by the address of the device&#xa; with the name specified by the deviceName property?
</description>
<tag name="@see">#getDeviceNameEnabled</tag>
<tag name="@see">#setDeviceNameEnabled</tag>
</property>

<property name="deviceName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;deviceName&lt;/code&gt; property.&#xa; name of the BACnet device.  The address of the Niagara BacnetDevice&#xa; with this name will be used to filter packets.
</description>
<tag name="@see">#getDeviceName</tag>
<tag name="@see">#setDeviceName</tag>
</property>

<property name="addressEnabled" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;addressEnabled&lt;/code&gt; property.&#xa; should this listener filter packets by the address specified&#xa; deviceAddress?
</description>
<tag name="@see">#getAddressEnabled</tag>
<tag name="@see">#setAddressEnabled</tag>
</property>

<property name="deviceAddress" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
Slot for the &lt;code&gt;deviceAddress&lt;/code&gt; property.&#xa; BACnet address to filter packets by.
</description>
<tag name="@see">#getDeviceAddress</tag>
<tag name="@see">#setDeviceAddress</tag>
</property>

<property name="serviceChoiceEnabled" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;serviceChoiceEnabled&lt;/code&gt; property.&#xa; should this listener filter packets based on the application layer&#xa; service choice (e.g., ReadProperty, WriteProperty) of the packets?
</description>
<tag name="@see">#getServiceChoiceEnabled</tag>
<tag name="@see">#setServiceChoiceEnabled</tag>
</property>

<property name="serviceChoices" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetBitString"/>
<description>
Slot for the &lt;code&gt;serviceChoices&lt;/code&gt; property.
</description>
<tag name="@see">#getServiceChoices</tag>
<tag name="@see">#setServiceChoices</tag>
</property>

<property name="objectIdEnabled" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;objectIdEnabled&lt;/code&gt; property.&#xa; should this listener filter packets based on the objectId referenced&#xa; in the packets?
</description>
<tag name="@see">#getObjectIdEnabled</tag>
<tag name="@see">#setObjectIdEnabled</tag>
</property>

<property name="objectId" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

</class>
</bajadoc>
