<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BReadAccessSpecification" name="BReadAccessSpecification" packageName="javax.baja.bacnet.datatypes" public="true">
<description>
BReadAccessSpecification represents the ReadAccessSpecification sequence.&#xa; This is used in the Group object.  It is not the class used for handling&#xa; ReadAccessSpecifications in the usage of ReadPropertyMultiple during&#xa; polling.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">26 Jul 2005</tag>
<tag name="@since">Niagara 3.1</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="objectIdentifier" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectIdentifier&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectIdentifier</tag>
<tag name="@see">#setObjectIdentifier</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BReadAccessSpecification() -->
<constructor name="BReadAccessSpecification" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BReadAccessSpecification(javax.baja.bacnet.datatypes.BBacnetPropertyReference) -->
<constructor name="BReadAccessSpecification" public="true">
<parameter name="ref">
<type class="javax.baja.bacnet.datatypes.BBacnetPropertyReference"/>
</parameter>
<description>
Single reference constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BReadAccessSpecification(javax.baja.bacnet.datatypes.BBacnetPropertyReference[]) -->
<constructor name="BReadAccessSpecification" public="true">
<parameter name="refs">
<type class="javax.baja.bacnet.datatypes.BBacnetPropertyReference" dimension="1"/>
</parameter>
<description>
Single reference constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BReadAccessSpecification.getObjectIdentifier() -->
<method name="getObjectIdentifier"  public="true">
<description>
Get the &lt;code&gt;objectIdentifier&lt;/code&gt; property.
</description>
<tag name="@see">#objectIdentifier</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadAccessSpecification.setObjectIdentifier(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="setObjectIdentifier"  public="true">
<description>
Set the &lt;code&gt;objectIdentifier&lt;/code&gt; property.
</description>
<tag name="@see">#objectIdentifier</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadAccessSpecification.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadAccessSpecification.isChildLegal(javax.baja.sys.BComponent) -->
<method name="isChildLegal"  public="true">
<description>
No component children are allowed.  The only valid child is a&#xa; BBacnetObjectPropertyReference.
</description>
<parameter name="child">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadAccessSpecification.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadAccessSpecification.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BReadAccessSpecification.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BReadAccessSpecification.objectIdentifier -->
<field name="objectIdentifier"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectIdentifier&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectIdentifier</tag>
<tag name="@see">#setObjectIdentifier</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BReadAccessSpecification.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BReadAccessSpecification.OBJECT_ID_TAG -->
<field name="OBJECT_ID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BReadAccessSpecification.LIST_OF_PROPERTY_REFERENCES_TAG -->
<field name="LIST_OF_PROPERTY_REFERENCES_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
