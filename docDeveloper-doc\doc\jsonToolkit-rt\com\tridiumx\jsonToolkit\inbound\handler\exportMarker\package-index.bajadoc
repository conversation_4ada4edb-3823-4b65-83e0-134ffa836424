<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.inbound.handler.exportMarker">
<description/>
<class packageName="com.tridiumx.jsonToolkit.inbound.handler.exportMarker" name="BJsonExportDeregistrationHandler"><description>Allows the cloud platform to remove it&#x27;s identifier from export marked points,&#xa; effectively &#x22;unregistering&#x22; them.</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.handler.exportMarker" name="BJsonExportRegistrationHandler"><description>Allows the cloud (or other &#x22;external system&#x22;) target to assign it&#x27;s own&#xa; identifier or primary key to export marked points in the Niagara station&#xa; which can be used to locate them in future, or included in exports to that&#xa; cloud system.</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.handler.exportMarker" name="BJsonExportSetpointHandler"><description>Export Setpoint Handler allows an external json message to change the value&#xa; of a Control Point.</description></class>
</package>
</bajadoc>
