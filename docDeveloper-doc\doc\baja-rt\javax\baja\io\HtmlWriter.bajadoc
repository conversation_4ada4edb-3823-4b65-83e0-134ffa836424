<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.io.HtmlWriter" name="HtmlWriter" packageName="javax.baja.io" public="true">
<description>
HtmlWriter is used to generate HTML code with common tags.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">28 Feb 03</tag>
<tag name="@version">$Revision: 10$ $Date: 6/25/08 1:56:14 PM EDT$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="java.io.PrintWriter"/>
</extends>
<!-- javax.baja.io.HtmlWriter(java.io.Writer) -->
<constructor name="HtmlWriter" public="true">
<parameter name="out">
<type class="java.io.Writer"/>
</parameter>
<description>
Construct for specified writer.
</description>
</constructor>

<!-- javax.baja.io.HtmlWriter(java.io.OutputStream) -->
<constructor name="HtmlWriter" public="true">
<parameter name="out">
<type class="java.io.OutputStream"/>
</parameter>
<description>
Construct for specified output stream.
</description>
</constructor>

<!-- javax.baja.io.HtmlWriter.println(java.lang.String) -->
<method name="println"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Println always uses &#x22;\n&#x22; for newline.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.w(java.lang.Object) -->
<method name="w"  public="true">
<description>
Write the specified Object and return this.
</description>
<parameter name="o">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.w(boolean) -->
<method name="w"  public="true" final="true">
<description>
Write the specified boolean and return this.
</description>
<parameter name="b">
<type class="boolean"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.w(char) -->
<method name="w"  public="true" final="true">
<description>
Write the specified char and return this.
</description>
<parameter name="c">
<type class="char"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.w(int) -->
<method name="w"  public="true" final="true">
<description>
Write the specified int and return this.
</description>
<parameter name="i">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.w(long) -->
<method name="w"  public="true" final="true">
<description>
Write the specified long and return this.
</description>
<parameter name="l">
<type class="long"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.w(float) -->
<method name="w"  public="true" final="true">
<description>
Write the specified float and return this.
</description>
<parameter name="f">
<type class="float"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.w(double) -->
<method name="w"  public="true" final="true">
<description>
Write the specified double and return this.
</description>
<parameter name="d">
<type class="double"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.nbsp() -->
<method name="nbsp"  public="true" final="true">
<description>
Write a non-breaking space character and return this.
</description>
<tag name="@since">Niagara 3.4</tag>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.nl() -->
<method name="nl"  public="true" final="true">
<description>
Write a newline character and return this.
</description>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.indent(int) -->
<method name="indent"  public="true" final="true">
<description>
Write the specified number of spaces.
</description>
<parameter name="indent">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.attr(java.lang.String, java.lang.String) -->
<method name="attr"  public="true" final="true">
<description>
Write an attribute pair &lt;code&gt;name=&amp;#x27;value&amp;#x27;&lt;/code&gt;&#xa; where the value is written using safe().
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="value">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.attr(java.lang.String, int) -->
<method name="attr"  public="true" final="true">
<description>
Convenience for &lt;code&gt;attr(name, Integer.toString(value))&lt;/code&gt;.
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="value">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.attr(java.lang.String, float) -->
<method name="attr"  public="true" final="true">
<description>
Convenience for &lt;code&gt;attr(name, Float.toString(value))&lt;/code&gt;.
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="value">
<type class="float"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.attr(java.lang.String, double) -->
<method name="attr"  public="true" final="true">
<description>
Convenience for &lt;code&gt;attr(name, Double.toString(value))&lt;/code&gt;.
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="value">
<type class="double"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.attr(java.lang.String, java.lang.Object) -->
<method name="attr"  public="true" final="true">
<description>
Convenience for &lt;code&gt;attr(name, String.valueOf(value))&lt;/code&gt;.
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<parameter name="value">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.safe(java.lang.String, boolean) -->
<method name="safe"  public="true" final="true">
<description>
Convenience for &lt;code&gt;XWriter.safe(this, s, escapeWhitespace)&lt;/code&gt;.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<parameter name="escapeWhitespace">
<type class="boolean"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.safe(java.lang.Object) -->
<method name="safe"  public="true" final="true">
<description>
Convenience for &lt;code&gt;XWriter.safe(this, s, true)&lt;/code&gt;.
</description>
<parameter name="s">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.safe(int, boolean) -->
<method name="safe"  public="true" final="true">
<description>
Convenience for &lt;code&gt;XWriter.safe(this, c, escapeWhitespace)&lt;/code&gt;.
</description>
<parameter name="c">
<type class="int"/>
</parameter>
<parameter name="escapeWhitespace">
<type class="boolean"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.href(java.lang.String) -->
<method name="href"  public="true">
<description>
Use this method to sanitize all hrefs used in &#xa; anchors, images, and other urls.  The default&#xa; implementation returns the specified parameter.
</description>
<parameter name="href">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.a(java.lang.String, java.lang.Object) -->
<method name="a"  public="true">
<description>
Write an anchor tag with the specified href and text&#xa; between the open and close anchor elements.
</description>
<parameter name="href">
<type class="java.lang.String"/>
</parameter>
<parameter name="body">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.createLink(java.lang.String, java.lang.Object) -->
<method name="createLink"  public="true">
<description>
Create an &lt;code&gt;&amp;lt;a&amp;gt;&lt;/code&gt; tag with the specified href and body. The href and&#xa; body will be HTML-sanitized.
</description>
<tag name="@since">Niagara 4.10</tag>
<parameter name="href">
<type class="java.lang.String"/>
</parameter>
<parameter name="body">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.a(java.lang.String) -->
<method name="a"  public="true">
<description>
Convenience for &lt;code&gt;a(href, href)&lt;/code&gt;.
</description>
<parameter name="href">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.startTable(boolean) -->
<method name="startTable"  public="true">
<description>
Write a table start tag.
</description>
<parameter name="border">
<type class="boolean"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.trTitle(java.lang.Object, int) -->
<method name="trTitle"  public="true">
<description>
Write a table row with the specified colspan that&#xa; can be used as a title header to separate rows.
</description>
<parameter name="title">
<type class="java.lang.Object"/>
</parameter>
<parameter name="colspan">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.thTitle(java.lang.Object) -->
<method name="thTitle"  public="true">
<description>
Write a table header column using the predefined&#xa; formatting of &lt;code&gt;trTitle()&lt;/code&gt;.
</description>
<parameter name="title">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.endTable() -->
<method name="endTable"  public="true">
<description>
Write a table end tag.
</description>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.tr(java.lang.Object) -->
<method name="tr"  public="true">
<description>
Write a table row with one td column.
</description>
<parameter name="c0">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.tr(java.lang.Object, java.lang.Object) -->
<method name="tr"  public="true">
<description>
Write a table row with two td columns.
</description>
<parameter name="c0">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c1">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.tr(java.lang.Object, java.lang.Object, java.lang.Object) -->
<method name="tr"  public="true">
<description>
Write a table row with three td columns.
</description>
<parameter name="c0">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c1">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c2">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.tr(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object) -->
<method name="tr"  public="true">
<description>
Write a table row with four td columns.
</description>
<parameter name="c0">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c1">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c2">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c3">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.tr(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object) -->
<method name="tr"  public="true">
<description>
Write a table row with five td columns.
</description>
<parameter name="c0">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c1">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c2">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c3">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c4">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.tr(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object) -->
<method name="tr"  public="true">
<description>
Write a table row with six td columns.
</description>
<parameter name="c0">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c1">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c2">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c3">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c4">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c5">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.tr(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object) -->
<method name="tr"  public="true">
<description>
Write a table row with seven td columns.
</description>
<parameter name="c0">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c1">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c2">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c3">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c4">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c5">
<type class="java.lang.Object"/>
</parameter>
<parameter name="c6">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.th(java.lang.Object) -->
<method name="th"  public="true">
<description>
Write a th tag with align=left and nowrap.
</description>
<parameter name="s">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.td(java.lang.Object) -->
<method name="td"  public="true">
<description>
Write a td tag with align=left and nowrap.
</description>
<parameter name="s">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.thTitle() -->
<method name="thTitle"  public="true">
<description>
Starts a new &lt;code&gt;&amp;lt;th&amp;gt;&lt;/code&gt; element to contain the table title. Remember to&#xa; call &lt;code&gt;endTh()&lt;/code&gt; after writing its contents.
</description>
<tag name="@since">Niagara 4.10</tag>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.th() -->
<method name="th"  public="true">
<description>
Starts a new &lt;code&gt;&amp;lt;th&amp;gt;&lt;/code&gt; element for a table header. Remember to call&#xa; &lt;code&gt;endTh()&lt;/code&gt; after writing its contents.
</description>
<tag name="@since">Niagara 4.10</tag>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.endTh() -->
<method name="endTh"  public="true">
<description>
Ends an open &lt;code&gt;&amp;lt;th&amp;gt;&lt;/code&gt; element.
</description>
<tag name="@since">Niagara 4.10</tag>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.tr() -->
<method name="tr"  public="true">
<description>
Starts a new &lt;code&gt;&amp;lt;tr&amp;gt;&lt;/code&gt; element. Remember to call &lt;code&gt;endTr()&lt;/code&gt; after&#xa; writing its contents.
</description>
<tag name="@since">Niagara 4.10</tag>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.endTr() -->
<method name="endTr"  public="true">
<description>
Ends an open &lt;code&gt;&amp;lt;tr&amp;gt;&lt;/code&gt; element.
</description>
<tag name="@since">Niagara 4.10</tag>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.td() -->
<method name="td"  public="true">
<description>
Starts a new &lt;code&gt;&amp;lt;td&amp;gt;&lt;/code&gt; element. Remember to call &lt;code&gt;endTd()&lt;/code&gt; after&#xa; writing its contents.
</description>
<tag name="@since">Niagara 4.10</tag>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

<!-- javax.baja.io.HtmlWriter.endTd() -->
<method name="endTd"  public="true">
<description>
Ends an open &lt;code&gt;&amp;lt;td&amp;gt;&lt;/code&gt; element.
</description>
<tag name="@since">Niagara 4.10</tag>
<return>
<type class="javax.baja.io.HtmlWriter"/>
</return>
</method>

</class>
</bajadoc>
