<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.req.BInfinityKeystrokeRequest" name="BInfinityKeystrokeRequest" packageName="com.tridium.andoverInfinity.comm.req" public="true">
<description>
Used to send keystrokes from client to server side while in terminal session&#xa; in the Vt100 manager view.
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.comm.req.BDdfRawTransmitRequest"/>
</extends>
<!-- com.tridium.andoverInfinity.comm.req.BInfinityKeystrokeRequest() -->
<constructor name="BInfinityKeystrokeRequest" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityKeystrokeRequest(byte[]) -->
<constructor name="BInfinityKeystrokeRequest" public="true">
<parameter name="bytes">
<type class="byte" dimension="1"/>
</parameter>
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityKeystrokeRequest.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityKeystrokeRequest.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
