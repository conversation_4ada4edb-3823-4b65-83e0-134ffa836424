<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="com.tridium.bacnetAws.datatypes.BObjectParameters" name="BObjectParameters" packageName="com.tridium.bacnetAws.datatypes" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@creation">07 June 2010</tag>
<tag name="@author">e333748</tag>
<tag name="@creation">Jun 7, 2010</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="ords" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;ords&lt;/code&gt; property.
</description>
<tag name="@see">#getOrds</tag>
<tag name="@see">#setOrds</tag>
</property>

<property name="objectIds" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;objectIds&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectIds</tag>
<tag name="@see">#setObjectIds</tag>
</property>

</class>
</bajadoc>
