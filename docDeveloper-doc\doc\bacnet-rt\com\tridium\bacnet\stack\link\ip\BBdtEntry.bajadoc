<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.link.ip.BBdtEntry" name="BBdtEntry" packageName="com.tridium.bacnet.stack.link.ip" public="true">
<description>
BBdtEntry represents an entry in the&#xa; Broadcast Distribution Table (BDT) of a Bacnet Broadcast Management&#xa; Device (BBMD).
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">18 Apr 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="bacnetIPAddress" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;bacnetIPAddress&lt;/code&gt; property.&#xa; the device&#x27;s Bacnet/IP address.
</description>
<tag name="@see">#getBacnetIPAddress</tag>
<tag name="@see">#setBacnetIPAddress</tag>
</property>

<property name="broadcastDistributionMask" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;broadcastDistributionMask&lt;/code&gt; property.&#xa; the broadcast distribution mask for the device.
</description>
<tag name="@see">#getBroadcastDistributionMask</tag>
<tag name="@see">#setBroadcastDistributionMask</tag>
</property>

</class>
</bajadoc>
