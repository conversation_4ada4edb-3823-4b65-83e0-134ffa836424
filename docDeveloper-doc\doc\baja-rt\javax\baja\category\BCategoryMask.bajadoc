<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.category.BCategoryMask" name="BCategoryMask" packageName="javax.baja.category" public="true" final="true">
<description>
BCategoryMask is a bitmask of category numbers.  BCategoryMasks are&#xa; used by BICategorizable as serializable, indirect references to &#xa; a station&#x27;s BCategories.  BCategoryMasks are represented as hexidecimal&#xa; strings where &#x22;1&#x22; represents membership in category 1 and &#x22;a&#x22; represents&#xa; membership in categories 4 and 2.  The &#x22;&#x22; empty string represents&#xa; the null category (or inherit), and the &#x22;*&#x22; represents the wildcard&#xa; category which matches all.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">10 Feb 05</tag>
<tag name="@version">$Revision: 6$ $Date: 7/30/08 10:53:50 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BSimple"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
<!-- javax.baja.category.BCategoryMask.or(javax.baja.category.BCategoryMask, javax.baja.category.BCategoryMask) -->
<method name="or"  public="true" static="true">
<description>
Perform a bitwise or of the two masks.  If either mask is&#xa; the wildcard, then return the wildcard.
</description>
<parameter name="a">
<type class="javax.baja.category.BCategoryMask"/>
</parameter>
<parameter name="b">
<type class="javax.baja.category.BCategoryMask"/>
</parameter>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.category.BCategoryMask.and(javax.baja.category.BCategoryMask, javax.baja.category.BCategoryMask) -->
<method name="and"  public="true" static="true">
<description>
Perform a bitwise and of the two masks.  If either mask is&#xa; the wildcard, then return the other mask.
</description>
<parameter name="a">
<type class="javax.baja.category.BCategoryMask"/>
</parameter>
<parameter name="b">
<type class="javax.baja.category.BCategoryMask"/>
</parameter>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.category.BCategoryMask.make(int[]) -->
<method name="make"  public="true" static="true">
<description>
Make BCategoryMask using an array of category indices.
</description>
<parameter name="indices">
<type class="int" dimension="1"/>
</parameter>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.category.BCategoryMask.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Make BCategoryMask using a hexidecimal string represenation&#xa; of the category membership.  For example &#x22;a&#x22; represents membership &#xa; in categories 4 and 2.  Note the hex string must only use&#xa; lower case and contain no leading zeros.  The special string &#x22;&#x22; maps&#xa; to NULL and &#x22;*&#x22; to WILDCARD.
</description>
<parameter name="hex">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.category.BCategoryMask.isNull() -->
<method name="isNull"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is this the NULL instance which returns false for all categories.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.category.BCategoryMask.isWildcard() -->
<method name="isWildcard"  public="true">
<description>
Is this the WILDCARD instance which returns true for all categories.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.category.BCategoryMask.size() -->
<method name="size"  public="true">
<description>
Get the number of categories contained by this mask.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.category.BCategoryMask.get(int) -->
<method name="get"  public="true">
<description>
Return if this mask has the bit set for the specified category &#xa; index.  Always return false for NULL and true for WILDCARD.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.category.BCategoryMask.hashCode() -->
<method name="hashCode"  public="true">
<description>
Hash is based on &lt;code&gt;System.identityHashCode()&lt;/code&gt;.&#xa; Added override for this method in Niagara 3.4.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.category.BCategoryMask.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description/>
<parameter name="obj">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.category.BCategoryMask.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.category.BCategoryMask.encode(java.io.DataOutput) -->
<method name="encode"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="out">
<type class="java.io.DataOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.category.BCategoryMask.decode(java.io.DataInput) -->
<method name="decode"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="in">
<type class="java.io.DataInput"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.category.BCategoryMask.encodeToString() -->
<method name="encodeToString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.category.BCategoryMask.decodeFromString(java.lang.String) -->
<method name="decodeFromString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
</method>

<!-- javax.baja.category.BCategoryMask.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.category.BCategoryMask.NULL -->
<field name="NULL"  public="true" static="true" final="true">
<type class="javax.baja.category.BCategoryMask"/>
<description>
The null instance is &#x22;&#x22;, no bits set.
</description>
</field>

<!-- javax.baja.category.BCategoryMask.WILDCARD -->
<field name="WILDCARD"  public="true" static="true" final="true">
<type class="javax.baja.category.BCategoryMask"/>
<description>
The wildcard instance is &#x22;*&#x22;, all bits set.
</description>
</field>

<!-- javax.baja.category.BCategoryMask.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.category.BCategoryMask"/>
<description>
This is default instance is NULL.
</description>
</field>

<!-- javax.baja.category.BCategoryMask.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
