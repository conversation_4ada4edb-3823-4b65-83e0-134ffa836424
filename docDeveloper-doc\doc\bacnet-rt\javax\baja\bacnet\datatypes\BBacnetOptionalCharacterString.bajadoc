<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString" name="BBacnetOptionalCharacterString" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
Recipient for an alarm to be exported to Bacnet.&#xa; &lt;p&gt;&#xa; BBacnetRecipient represents the BacnetRecipient&#xa; choice.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 3$ $Date: 12/10/01 9:26:16 AM$</tag>
<tag name="@since">Niagara 4.0.1</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="choice" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</property>

<property name="characterString" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;characterString&lt;/code&gt; property.
</description>
<tag name="@see">#getCharacterString</tag>
<tag name="@see">#setCharacterString</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString() -->
<constructor name="BBacnetOptionalCharacterString" public="true">
<description>
Default (NULL) constructor
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString(java.lang.String) -->
<constructor name="BBacnetOptionalCharacterString" public="true">
<parameter name="string">
<type class="java.lang.String"/>
<description>
String to wrap in an optional character string value object
</description>
</parameter>
<description>
String ID constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString.getChoice() -->
<method name="getChoice"  public="true">
<description>
Get the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString.setChoice(int) -->
<method name="setChoice"  public="true">
<description>
Set the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString.getCharacterString() -->
<method name="getCharacterString"  public="true">
<description>
Get the &lt;code&gt;characterString&lt;/code&gt; property.
</description>
<tag name="@see">#characterString</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString.setCharacterString(java.lang.String) -->
<method name="setCharacterString"  public="true">
<description>
Set the &lt;code&gt;characterString&lt;/code&gt; property.
</description>
<tag name="@see">#characterString</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString.isNull() -->
<method name="isNull"  public="true">
<description>
Is this a device-type recipient?
</description>
<return>
<type class="boolean"/>
<description>
true if device, false if address.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString.isCharacterString() -->
<method name="isCharacterString"  public="true">
<description>
Is this a address-type recipient?
</description>
<return>
<type class="boolean"/>
<description>
true if address, false if device.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString.getCharacterStringValue() -->
<method name="getCharacterStringValue"  public="true">
<description>
Get the recipient as a BValue.
</description>
<return>
<type class="javax.baja.sys.BValue"/>
<description>
the recipient.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString.choice -->
<field name="choice"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString.characterString -->
<field name="characterString"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;characterString&lt;/code&gt; property.
</description>
<tag name="@see">#getCharacterString</tag>
<tag name="@see">#setCharacterString</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString.NULL_TAG -->
<field name="NULL_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetOptionalCharacterString.CHARACTER_STRING_TAG -->
<field name="CHARACTER_STRING_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
