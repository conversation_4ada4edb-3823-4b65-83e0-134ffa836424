<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.category.BOrdToCategoryMap" name="BOrdToCategoryMap" packageName="javax.baja.category" public="true" final="true">
<description>
BOrdToCategoryMap stores a table mapping BOrds to BCategoryMasks.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">19 Feb 05</tag>
<tag name="@version">$Revision: 4$ $Date: 9/3/08 10:20:48 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BSimple"/>
</extends>
<implements>
<type class="javax.baja.sys.BIUnlinkable"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
<!-- javax.baja.category.BOrdToCategoryMap.make(javax.baja.naming.BOrd[], javax.baja.category.BCategoryMask[]) -->
<method name="make"  public="true" static="true">
<description>
Make a map with the specified ord and category arrays.
</description>
<parameter name="ords">
<type class="javax.baja.naming.BOrd" dimension="1"/>
</parameter>
<parameter name="cats">
<type class="javax.baja.category.BCategoryMask" dimension="1"/>
</parameter>
<return>
<type class="javax.baja.category.BOrdToCategoryMap"/>
</return>
</method>

<!-- javax.baja.category.BOrdToCategoryMap.setCategoryMask(javax.baja.naming.BOrd, javax.baja.category.BCategoryMask) -->
<method name="setCategoryMask"  public="true">
<description>
Set the category mask for the object identified by the specified ord.
</description>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
<description>
The ordInSession for an object to categorize.
</description>
</parameter>
<parameter name="mask">
<type class="javax.baja.category.BCategoryMask"/>
<description>
The mask for the categorizable object.
</description>
</parameter>
<return>
<type class="javax.baja.category.BOrdToCategoryMap"/>
</return>
</method>

<!-- javax.baja.category.BOrdToCategoryMap.isNull() -->
<method name="isNull"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is this the NULL instance which has a size of zero.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.category.BOrdToCategoryMap.size() -->
<method name="size"  public="true">
<description>
Get the number of ord/category items in this map.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.category.BOrdToCategoryMap.getOrd(int) -->
<method name="getOrd"  public="true">
<description>
Get the ord at the specified index: 0 to size()-1.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.category.BOrdToCategoryMap.getCategoryMask(int) -->
<method name="getCategoryMask"  public="true">
<description>
Get the category mask at the specified index: 0 to size()-1.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.category.BOrdToCategoryMap.getCategoryMask(javax.baja.naming.BOrd) -->
<method name="getCategoryMask"  public="true">
<description>
Given an ord, search for an exact match based on comparing &#xa; the specified ord to find the ord in this map.  If an&#xa; exact match isn&#x27;t found, return null.
</description>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.category.BOrdToCategoryMap.getAppliedCategoryMask(javax.baja.naming.BOrd) -->
<method name="getAppliedCategoryMask"  public="true">
<description>
Given an ord, search for the best match based on comparing &#xa; the specified ord to find the ord in this map with the longest &#xa; matching substring.  If no ords in the map are matches, then&#xa; return null.
</description>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.category.BOrdToCategoryMap.hashCode() -->
<method name="hashCode"  public="true">
<description>
BOrdToCategoryMap uses its encodeToString() value&#x27;s hash code.
</description>
<tag name="@since">Niagara 3.4</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.category.BOrdToCategoryMap.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description/>
<parameter name="obj">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.category.BOrdToCategoryMap.encode(java.io.DataOutput) -->
<method name="encode"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="out">
<type class="java.io.DataOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.category.BOrdToCategoryMap.decode(java.io.DataInput) -->
<method name="decode"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="in">
<type class="java.io.DataInput"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.category.BOrdToCategoryMap.encodeToString() -->
<method name="encodeToString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.category.BOrdToCategoryMap.decodeFromString(java.lang.String) -->
<method name="decodeFromString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
</method>

<!-- javax.baja.category.BOrdToCategoryMap.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.category.BOrdToCategoryMap.NULL -->
<field name="NULL"  public="true" static="true" final="true">
<type class="javax.baja.category.BOrdToCategoryMap"/>
<description>
The null instance has a size of zero.
</description>
</field>

<!-- javax.baja.category.BOrdToCategoryMap.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.category.BOrdToCategoryMap"/>
<description>
This is default instance is NULL.
</description>
</field>

<!-- javax.baja.category.BOrdToCategoryMap.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
