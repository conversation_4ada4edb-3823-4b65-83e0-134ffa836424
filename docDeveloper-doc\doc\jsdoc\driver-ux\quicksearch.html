<html>
<head>
</head>
<body style="background: transparent;">
    <script src="scripts/docstrap.lib.js"></script>
    <script src="scripts/lunr.min.js"></script>
    <script src="scripts/fulltext-search.js"></script>

    <script type="text/x-docstrap-searchdb">
    {"modules.list.html":{"id":"modules.list.html","title":"Modules","body":" driver Modules nmodule/driver/rc/wb/mgr/DeviceMgrnmodule/driver/rc/wb/mgr/DeviceMgrModelnmodule/driver/rc/wb/mgr/DriverMgrnmodule/driver/rc/wb/mgr/PointMgrnmodule/driver/rc/wb/mgr/PointMgrModel Modules Classes module:nmodule/driver/rc/wb/mgr/DeviceMgr module:nmodule/driver/rc/wb/mgr/DeviceMgrModel module:nmodule/driver/rc/wb/mgr/DriverMgr module:nmodule/driver/rc/wb/mgr/PointMgr module:nmodule/driver/rc/wb/mgr/PointMgrModel × Search results Close "},"index.html":{"id":"index.html","title":"Index","body":" driver Modules nmodule/driver/rc/wb/mgr/DeviceMgrnmodule/driver/rc/wb/mgr/DeviceMgrModelnmodule/driver/rc/wb/mgr/DriverMgrnmodule/driver/rc/wb/mgr/PointMgrnmodule/driver/rc/wb/mgr/PointMgrModel driver This module contains resources for building Manager views on Points or Devices within driver networks. See managers for general information on creating bajaux manager views. × Search results Close "},"module-nmodule_driver_rc_wb_mgr_DeviceMgr.html":{"id":"module-nmodule_driver_rc_wb_mgr_DeviceMgr.html","title":"Module: nmodule/driver/rc/wb/mgr/DeviceMgr","body":" driver Modules nmodule/driver/rc/wb/mgr/DeviceMgrnmodule/driver/rc/wb/mgr/DeviceMgrModelnmodule/driver/rc/wb/mgr/DriverMgrnmodule/driver/rc/wb/mgr/PointMgrnmodule/driver/rc/wb/mgr/PointMgrModel Module: nmodule/driver/rc/wb/mgr/DeviceMgr new (require(\"nmodule/driver/rc/wb/mgr/DeviceMgr\"))( [params]) API Status: Development Instance for managing devices inside a container. Extends: module:nmodule/driver/rc/wb/mgr/DriverMgr Parameters: Name Type Argument Description params Object &lt;optional&gt; an object containing the constructor parameters. Properties Name Type Argument Description keyName String &lt;optional&gt; the key name used for lexicon entries for this view. moduleName String &lt;optional&gt; the module name used for lexicon entries for this view. subscriptionDepth Number &lt;optional&gt; the depth to subscribe the component tree. Will default to 1, if not specified. subscriptionFilter module:nmodule/webEditors/rc/wb/util/subscriptionUtil~SubscriptionFilter &lt;optional&gt; Starting in Niagara 4.13, if the optional subscriptionFilter function is provided, it will be called for each potentially subscribable BComponent with its current depth. By returning true only for the desired components, the subscription will subscribe to what is needed. subscribeCallback module:nmodule/webEditors/rc/wb/util/subscriptionUtil~SubscribeCallback &lt;optional&gt; Starting in Niagara 4.13, if the optional subscribeCallback function is provided, it will receive a callback when a component is subscribed. This can allow you to add additional subscriptions outside the normal depth and filter results. Methods buildMainTableCell(column, row, dom) Override of the base manager's build cell function. Parameters: Name Type Description column module:nmodule/webEditors/rc/wb/table/model/Column The column for the cell row module:nmodule/webEditors/rc/wb/table/model/Row The row for the cell dom JQuery Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#buildMainTableCell Returns: Type Promise componentAdded(parent, child) Function called when a new component is added. If the component is a folder and the all descendants command is selected, we want to subscribe to that folder to the correct depth. Parameters: Name Type Description parent baja.Component child baja.Component Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#componentAdded componentChanged(comp) Function called when a property of one of the row's subjects or descendants changes. This is used to update the table when, for example, a property on a point's proxy extension is changed. Parameters: Name Type Description comp baja.Component The component notifying the changed property. Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#componentChanged componentRemoved(value) Function called when a subscribed component is removed. If the component was a folder and the all descendants command is selected, we want to unsubscribe to that folder. Parameters: Name Type Description value baja.Component Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#componentRemoved componentRenamed(comp) Function called when the depth subscriber notifies a renamed component. This will try to emit a 'changed' event on the component source. Parameters: Name Type Description comp Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#componentRenamed doDestroy() Destroy the widget. This will clean up the event handler we have attached for listening to descendant changes. Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#doDestroy Returns: Type * doInitialize(dom) Manager initialization. In addition to the base manager initialization, this will configure double click handling on the table rows and ensure that the basic commands are in the appropriate default state. Parameters: Name Type Description dom JQuery Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#doInitialize Returns: Type * doLoad(comp) Load the widget from the component. This will hook up the event handlers to the depth subscriber used by this type. Parameters: Name Type Description comp baja.Component Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#doLoad Returns: Type Promise finishMainTableRow(row, dom) Overrides the basic manager #finishMainTableRow function with some extra css information specified on the dom for the table row. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row a table row instance dom JQuery Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#finishMainTableRow getSubject(elem) Get the subject via the manager's main table. Parameters: Name Type Description elem Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#getSubject Returns: Type * getSubscriptionDepth() Get the configured component subscription depth for the driver manager. This value is specified by the 'subscriptionDepth' parameter property in the constructor. Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#getSubscriptionDepth Returns: Type Number makeCommands() Return the default set of Command instances for a device manager. The basic set are commands for creating a new folder (if a folder type was specified in the constructor's parameters), creating a new point type and editing an existing point type. Concrete point manager types may override this function to append extra commands and/or remove the default ones. Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#makeCommands Returns: Type Array.&lt;module:bajaux/commands/Command&gt; × Search results Close "},"module-nmodule_driver_rc_wb_mgr_DeviceMgrModel.html":{"id":"module-nmodule_driver_rc_wb_mgr_DeviceMgrModel.html","title":"Module: nmodule/driver/rc/wb/mgr/DeviceMgrModel","body":" driver Modules nmodule/driver/rc/wb/mgr/DeviceMgrnmodule/driver/rc/wb/mgr/DeviceMgrModelnmodule/driver/rc/wb/mgr/DriverMgrnmodule/driver/rc/wb/mgr/PointMgrnmodule/driver/rc/wb/mgr/PointMgrModel Module: nmodule/driver/rc/wb/mgr/DeviceMgrModel new (require(\"nmodule/driver/rc/wb/mgr/DeviceMgrModel\"))(params) API Status: Development A MgrModel type for a DeviceMgr derived type as an agent on a driver's BDeviceNetwork or BDeviceFolder concrete type. Extends: module:nmodule/webEditors/rc/wb/mgr/model/MgrModel Parameters: Name Type Description params Object object containing the constructor parameters Properties Name Type Argument Description component baja.Component the component containing the devices to be shown in the manager, typically a device network or device folder. folderType string | Type &lt;optional&gt; optional parameter indicating the folder type for the manager. Methods getNavDisplayName() Get the display name from the display name of the root component container. This is used for the title of the tab in the HTML5 hx profile. makeRow(subject) Make a row for the given subject with the appropriate icon for the row. Overrides TableModel.makeRow(). Parameters: Name Type Description subject The subject of the row. Should be a device or folder instance. Returns: Type module:nmodule/webEditors/rc/wb/table/model/Row × Search results Close "},"module-nmodule_driver_rc_wb_mgr_DriverMgr.html":{"id":"module-nmodule_driver_rc_wb_mgr_DriverMgr.html","title":"Module: nmodule/driver/rc/wb/mgr/DriverMgr","body":" driver Modules nmodule/driver/rc/wb/mgr/DeviceMgrnmodule/driver/rc/wb/mgr/DeviceMgrModelnmodule/driver/rc/wb/mgr/DriverMgrnmodule/driver/rc/wb/mgr/PointMgrnmodule/driver/rc/wb/mgr/PointMgrModel Module: nmodule/driver/rc/wb/mgr/DriverMgr new (require(\"nmodule/driver/rc/wb/mgr/DriverMgr\"))() API Status: Development DriverMgr constructor. Contains functionality for working with components within a driver network. There is usually no reason to extend this directly; extend DeviceMgr or PointMgr instead. Extends: module:nmodule/webEditors/rc/wb/mgr/Manager Parameters: Name Type Argument Description params.keyName String &lt;optional&gt; the key name used for lexicon entries for this view. params.moduleName String &lt;optional&gt; the module name used for lexicon entries for this view. params.subscriptionDepth Number &lt;optional&gt; the depth to subscribe the component tree. params.subscriptionFilter module:nmodule/webEditors/rc/wb/util/subscriptionUtil~SubscriptionFilter &lt;optional&gt; Starting in Niagara 4.13, if the optional subscriptionFilter function is provided, it will be called for each potentially subscribable BComponent with its current depth. By returning true only for the desired components, the subscription will subscribe to what is needed. params.subscribeCallback module:nmodule/webEditors/rc/wb/util/subscriptionUtil~SubscribeCallback &lt;optional&gt; Starting in Niagara 4.13, if the optional subscribeCallback function is provided, it will receive a callback when a component is subscribed. This can allow you to add additional subscriptions outside the normal depth and filter results. params.folderType String | Type &lt;optional&gt; optional parameter indicating the folder type used for the manager view. This will be used by the NewFolder command. See: module:nmodule/driver/rc/wb/mgr/DeviceMgr module:nmodule/driver/rc/wb/mgr/PointMgr Methods buildMainTableCell(column, row, dom) Override of the base manager's build cell function. Parameters: Name Type Description column module:nmodule/webEditors/rc/wb/table/model/Column The column for the cell row module:nmodule/webEditors/rc/wb/table/model/Row The row for the cell dom JQuery Returns: Type Promise componentAdded(parent, child) Function called when a new component is added. If the component is a folder and the all descendants command is selected, we want to subscribe to that folder to the correct depth. Parameters: Name Type Description parent baja.Component child baja.Component componentChanged(comp) Function called when a property of one of the row's subjects or descendants changes. This is used to update the table when, for example, a property on a point's proxy extension is changed. Parameters: Name Type Description comp baja.Component The component notifying the changed property. componentRemoved(value) Function called when a subscribed component is removed. If the component was a folder and the all descendants command is selected, we want to unsubscribe to that folder. Parameters: Name Type Description value baja.Component componentRenamed(comp) Function called when the depth subscriber notifies a renamed component. This will try to emit a 'changed' event on the component source. Parameters: Name Type Description comp doDestroy() Destroy the widget. This will clean up the event handler we have attached for listening to descendant changes. Returns: Type * doInitialize(dom) Manager initialization. In addition to the base manager initialization, this will configure double click handling on the table rows and ensure that the basic commands are in the appropriate default state. Parameters: Name Type Description dom JQuery Returns: Type * doLoad(comp) Load the widget from the component. This will hook up the event handlers to the depth subscriber used by this type. Parameters: Name Type Description comp baja.Component Returns: Type Promise finishMainTableRow(row, dom) Overrides the basic manager #finishMainTableRow function with some extra css information specified on the dom for the table row. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row a table row instance dom JQuery getSubject(elem) Get the subject via the manager's main table. Parameters: Name Type Description elem Returns: Type * getSubscriptionDepth() Get the configured component subscription depth for the driver manager. This value is specified by the 'subscriptionDepth' parameter property in the constructor. Returns: Type Number makeCommands() Return the default set of Command instances for a device manager. The basic set are commands for creating a new folder (if a folder type was specified in the constructor's parameters), creating a new point type and editing an existing point type. Concrete point manager types may override this function to append extra commands and/or remove the default ones. Returns: Type Array.&lt;module:bajaux/commands/Command&gt; × Search results Close "},"module-nmodule_driver_rc_wb_mgr_PointMgr.html":{"id":"module-nmodule_driver_rc_wb_mgr_PointMgr.html","title":"Module: nmodule/driver/rc/wb/mgr/PointMgr","body":" driver Modules nmodule/driver/rc/wb/mgr/DeviceMgrnmodule/driver/rc/wb/mgr/DeviceMgrModelnmodule/driver/rc/wb/mgr/DriverMgrnmodule/driver/rc/wb/mgr/PointMgrnmodule/driver/rc/wb/mgr/PointMgrModel Module: nmodule/driver/rc/wb/mgr/PointMgr new (require(\"nmodule/driver/rc/wb/mgr/PointMgr\"))( [params]) API Status: Development Instance for managing points inside a container. Extends: module:nmodule/driver/rc/wb/mgr/DriverMgr Parameters: Name Type Argument Description params Object &lt;optional&gt; an object containing the constructor parameters. Properties Name Type Argument Description keyName String &lt;optional&gt; the key name used for lexicon entries for this view. moduleName String &lt;optional&gt; the module name used for lexicon entries for this view. subscriptionDepth Number &lt;optional&gt; the depth to subscribe the component tree. If not specified, the default depth will be 3, which will subscribe to the first level of properties on a point's proxy extension. subscriptionFilter module:nmodule/webEditors/rc/wb/util/subscriptionUtil~SubscriptionFilter &lt;optional&gt; Starting in Niagara 4.13, if the optional subscriptionFilter function is provided, it will be called for each potentially subscribable BComponent with its current depth. By returning true only for the desired components, the subscription will subscribe to what is needed. subscribeCallback module:nmodule/webEditors/rc/wb/util/subscriptionUtil~SubscribeCallback &lt;optional&gt; Starting in Niagara 4.13, if the optional subscribeCallback function is provided, it will receive a callback when a component is subscribed. This can allow you to add additional subscriptions outside the normal depth and filter results. Methods buildMainTableCell(column, row, dom) Override of the base manager's build cell function. Parameters: Name Type Description column module:nmodule/webEditors/rc/wb/table/model/Column The column for the cell row module:nmodule/webEditors/rc/wb/table/model/Row The row for the cell dom JQuery Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#buildMainTableCell Returns: Type Promise componentAdded(parent, child) Function called when a new component is added. If the component is a folder and the all descendants command is selected, we want to subscribe to that folder to the correct depth. Parameters: Name Type Description parent baja.Component child baja.Component Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#componentAdded componentChanged(comp) Function called when a property of one of the row's subjects or descendants changes. This is used to update the table when, for example, a property on a point's proxy extension is changed. Parameters: Name Type Description comp baja.Component The component notifying the changed property. Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#componentChanged componentRemoved(value) Function called when a subscribed component is removed. If the component was a folder and the all descendants command is selected, we want to unsubscribe to that folder. Parameters: Name Type Description value baja.Component Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#componentRemoved componentRenamed(comp) Function called when the depth subscriber notifies a renamed component. This will try to emit a 'changed' event on the component source. Parameters: Name Type Description comp Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#componentRenamed doDestroy() Destroy the widget. This will clean up the event handler we have attached for listening to descendant changes. Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#doDestroy Returns: Type * doInitialize(dom) Manager initialization. In addition to the base manager initialization, this will configure double click handling on the table rows and ensure that the basic commands are in the appropriate default state. Parameters: Name Type Description dom JQuery Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#doInitialize Returns: Type * doLoad(comp) Load the widget from the component. This will hook up the event handlers to the depth subscriber used by this type. Parameters: Name Type Description comp baja.Component Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#doLoad Returns: Type Promise finishMainTableRow(row, dom) Overrides the basic manager #finishMainTableRow function with some extra css information specified on the dom for the table row. Parameters: Name Type Description row module:nmodule/webEditors/rc/wb/table/model/Row a table row instance dom JQuery Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#finishMainTableRow getNewTypes() Function to return an array of types to be offered upon the execution of the command to add new instances. Returns: Type Array.&lt;module:nmodule/webEditors/rc/wb/mgr/MgrTypeInfo&gt; getSubject(elem) Get the subject via the manager's main table. Parameters: Name Type Description elem Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#getSubject Returns: Type * getSubscriptionDepth() Get the configured component subscription depth for the driver manager. This value is specified by the 'subscriptionDepth' parameter property in the constructor. Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#getSubscriptionDepth Returns: Type Number makeCommands() Return the default set of Command instances for a device manager. The basic set are commands for creating a new folder (if a folder type was specified in the constructor's parameters), creating a new point type and editing an existing point type. Concrete point manager types may override this function to append extra commands and/or remove the default ones. Inherited From: module:nmodule/driver/rc/wb/mgr/DriverMgr#makeCommands Returns: Type Array.&lt;module:bajaux/commands/Command&gt; × Search results Close "},"module-nmodule_driver_rc_wb_mgr_PointMgrModel.html":{"id":"module-nmodule_driver_rc_wb_mgr_PointMgrModel.html","title":"Module: nmodule/driver/rc/wb/mgr/PointMgrModel","body":" driver Modules nmodule/driver/rc/wb/mgr/DeviceMgrnmodule/driver/rc/wb/mgr/DeviceMgrModelnmodule/driver/rc/wb/mgr/DriverMgrnmodule/driver/rc/wb/mgr/PointMgrnmodule/driver/rc/wb/mgr/PointMgrModel Module: nmodule/driver/rc/wb/mgr/PointMgrModel new (require(\"nmodule/driver/rc/wb/mgr/PointMgrModel\"))(params) API Status: Development A MgrModel type for a PointMgr derived type as an agent on a driver's BPointDeviceExt type. Extends: module:nmodule/webEditors/rc/wb/mgr/model/MgrModel Parameters: Name Type Description params Object object containing the constructor parameters Properties Name Type Argument Description component baja.Component the component containing the points to be shown in the manager, typically a device's point ext or a point folder. folderType string | Type &lt;optional&gt; optional parameter indicating the folder type for the manager. This will be used by the 'new folder' command. Methods &lt;static&gt; addBooleanPointTypes(writable, types) Add the boolean point types to the given array. If the writable parameter is false, the writable boolean type will not be added. Parameters: Name Type Description writable boolean If true, will add the BooleanWritable type to the array. types Array An array of that will have the numeric types appended. If not specified, a new array will be created and returned. Returns: The provided array or a new one containing the appropriate boolean point types. &lt;static&gt; addEnumPointTypes(writable, types) Add the standard enum point types to the given array. If the writable parameter is false, the writable enum type will not be added. Parameters: Name Type Description writable boolean If true, will add the EnumWritable type to the array. types Array An array of that will have the numeric types appended. If not specified, a new array will be created and returned. Returns: The provided array or a new one containing the appropriate enum point types. &lt;static&gt; addNumericPointTypes(writable, types) Add the standard numeric point types to the given array. If the writable parameter is false, the writable numeric type will not be added. Parameters: Name Type Description writable boolean If true, will add the NumericWritable type to the array. types Array An array of that will have the numeric types appended. If not specified, a new array will be created and returned. Returns: The provided array or a new one containing the appropriate numeric point types. &lt;static&gt; addStringPointTypes(writable, types) Add the standard numeric point types to the given array. If the writable parameter is false, the writable numeric type will not be added. Parameters: Name Type Description writable boolean If true, will add the StringWritable type to the array. types Array An array of that will have the numeric types appended. If not specified, a new array will be created and returned. Returns: The provided array or a new one containing the appropriate numeric point types. &lt;static&gt; getDefaultNewTypes() Return MgrTypeInfo instances for the default new types for a point manager. This includes writable and non-writable versions of the four basic point data types (boolean, numeric, enum, string). Returns: Type Promise.&lt;Array.&lt;module:nmodule/webEditors/rc/wb/mgr/MgrTypeInfo&gt;&gt; getNavDisplayName() Get the display name from the display name of the root component container. This is used for the title of the tab in the HTML5 hx profile. getProxyExtType() Return the proxy extension type used by the concrete driver implementation. This is used by the default implementation of the #newInstance() function. Returns: Type string | Type makeRow(subject) Make a row for the given subject with the appropriate icon for the row. Overrides TableModel.makeRow(). Parameters: Name Type Description subject The subject of the row. Should be a point or folder instance. Returns: Type module:nmodule/webEditors/rc/wb/table/model/Row newInstance(typeInfo) Override point to customize how new instances of the selected type spec are instantiated. The default implementation will create a point and proxy ext using the type specified by the getProxyExtType() function. Parameters: Name Type Description typeInfo module:nmodule/webEditors/rc/wb/mgr/MgrTypeInfo Returns: Type baja.Value | Promise × Search results Close "}}
    </script>

    <script type="text/javascript">
        $(document).ready(function() {
            Searcher.init();
        });

        $(window).on("message", function(msg) {
            var msgData = msg.originalEvent.data;

            if (msgData.msgid != "docstrap.quicksearch.start") {
                return;
            }

            var results = Searcher.search(msgData.searchTerms);

            window.parent.postMessage({"results": results, "msgid": "docstrap.quicksearch.done"}, "*");
        });
    </script>
</body>
</html>
