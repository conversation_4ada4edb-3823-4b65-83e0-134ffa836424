<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.util.BBacnetWorker" name="BBacnetWorker" packageName="javax.baja.bacnet.util" public="true">
<description>
BBacnetWorker is the implementation of BWorker for Bacnet.&#xa; It uses a CoalesceQueue, so that writes can be coalesced as&#xa; needed.  Non-coalescing behavior can be accomplished by enqueueing&#xa; objects that do not implement BICoalescable.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">11 Feb 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.util.BWorker"/>
</extends>
<implements>
<type class="javax.baja.bacnet.util.worker.IWorkerPoolAware"/>
</implements>
<property name="maxQueueSize" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;maxQueueSize&lt;/code&gt; property.&#xa; the size of the queue for this worker.
</description>
<tag name="@see">#getMaxQueueSize</tag>
<tag name="@see">#setMaxQueueSize</tag>
</property>

<property name="workerThreadName" flags="tr">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;workerThreadName&lt;/code&gt; property.&#xa; name of the worker thread.
</description>
<tag name="@see">#getWorkerThreadName</tag>
<tag name="@see">#setWorkerThreadName</tag>
</property>

<!-- javax.baja.bacnet.util.BBacnetWorker() -->
<constructor name="BBacnetWorker" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.util.BBacnetWorker(int) -->
<constructor name="BBacnetWorker" public="true">
<parameter name="queueSize">
<type class="int"/>
</parameter>
<description>
Constructor with queue size.
</description>
</constructor>

<!-- javax.baja.bacnet.util.BBacnetWorker(java.lang.String) -->
<constructor name="BBacnetWorker" public="true">
<parameter name="workerName">
<type class="java.lang.String"/>
<description/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.util.BBacnetWorker.getMaxQueueSize() -->
<method name="getMaxQueueSize"  public="true">
<description>
Get the &lt;code&gt;maxQueueSize&lt;/code&gt; property.&#xa; the size of the queue for this worker.
</description>
<tag name="@see">#maxQueueSize</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BBacnetWorker.setMaxQueueSize(int) -->
<method name="setMaxQueueSize"  public="true">
<description>
Set the &lt;code&gt;maxQueueSize&lt;/code&gt; property.&#xa; the size of the queue for this worker.
</description>
<tag name="@see">#maxQueueSize</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BBacnetWorker.getWorkerThreadName() -->
<method name="getWorkerThreadName"  public="true">
<description>
Get the &lt;code&gt;workerThreadName&lt;/code&gt; property.&#xa; name of the worker thread.
</description>
<tag name="@see">#workerThreadName</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BBacnetWorker.setWorkerThreadName(java.lang.String) -->
<method name="setWorkerThreadName"  public="true">
<description>
Set the &lt;code&gt;workerThreadName&lt;/code&gt; property.&#xa; name of the worker thread.
</description>
<tag name="@see">#workerThreadName</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BBacnetWorker.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BBacnetWorker.started() -->
<method name="started"  public="true">
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.util.BBacnetWorker.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BBacnetWorker.added(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="added"  public="true">
<description/>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BBacnetWorker.removed(javax.baja.sys.Property, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="removed"  public="true">
<description/>
<parameter name="property">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="oldValue">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BBacnetWorker.stopWorker() -->
<method name="stopWorker"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BBacnetWorker.post(java.lang.Runnable) -->
<method name="post"  public="true">
<description>
Post an action to be run asynchronously.
</description>
<parameter name="r">
<type class="java.lang.Runnable"/>
</parameter>
<return>
<type class="javax.baja.util.IFuture"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BBacnetWorker.getWorker() -->
<method name="getWorker"  public="true">
<description>
Start running this task.
</description>
<return>
<type class="javax.baja.util.Worker"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BBacnetWorker.setWorker(javax.baja.util.Worker) -->
<method name="setWorker"  public="true">
<description/>
<parameter name="worker">
<type class="javax.baja.util.Worker"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BBacnetWorker.dump() -->
<method name="dump"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BBacnetWorker.getQueueSize() -->
<method name="getQueueSize"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BBacnetWorker.getQueue() -->
<method name="getQueue"  public="true">
<description/>
<return>
<type class="javax.baja.util.Queue"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BBacnetWorker.hasWorkerPool() -->
<method name="hasWorkerPool"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BBacnetWorker.maxQueueSize -->
<field name="maxQueueSize"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;maxQueueSize&lt;/code&gt; property.&#xa; the size of the queue for this worker.
</description>
<tag name="@see">#getMaxQueueSize</tag>
<tag name="@see">#setMaxQueueSize</tag>
</field>

<!-- javax.baja.bacnet.util.BBacnetWorker.workerThreadName -->
<field name="workerThreadName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;workerThreadName&lt;/code&gt; property.&#xa; name of the worker thread.
</description>
<tag name="@see">#getWorkerThreadName</tag>
<tag name="@see">#setWorkerThreadName</tag>
</field>

<!-- javax.baja.bacnet.util.BBacnetWorker.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BBacnetWorker.queue -->
<field name="queue"  protected="true">
<type class="javax.baja.util.CoalesceQueue"/>
<description/>
</field>

<!-- javax.baja.bacnet.util.BBacnetWorker.worker -->
<field name="worker"  protected="true">
<type class="javax.baja.util.Worker"/>
<description/>
</field>

</class>
</bajadoc>
