<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.selector.BJsonBooleanSelector" name="BJsonBooleanSelector" packageName="com.tridiumx.jsonToolkit.inbound.selector" public="true" abstract="true">
<description>
Selectors that output a boolean as their result
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.inbound.selector.BJsonSelector"/>
</extends>
<property name="out" flags="rst">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;out&lt;/code&gt; property.
</description>
<tag name="@see">#getOut</tag>
<tag name="@see">#setOut</tag>
</property>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonBooleanSelector() -->
<constructor name="BJsonBooleanSelector" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonBooleanSelector.getOut() -->
<method name="getOut"  public="true">
<description>
Get the &lt;code&gt;out&lt;/code&gt; property.
</description>
<tag name="@see">#out</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonBooleanSelector.setOut(boolean) -->
<method name="setOut"  public="true">
<description>
Set the &lt;code&gt;out&lt;/code&gt; property.
</description>
<tag name="@see">#out</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonBooleanSelector.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonBooleanSelector.getOutProperty() -->
<method name="getOutProperty"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Property"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonBooleanSelector.out -->
<field name="out"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;out&lt;/code&gt; property.
</description>
<tag name="@see">#getOut</tag>
<tag name="@see">#setOut</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.selector.BJsonBooleanSelector.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
