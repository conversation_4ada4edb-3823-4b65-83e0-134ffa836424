<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.RemoteQueryable" name="RemoteQueryable" packageName="javax.baja.naming" public="true" interface="true" abstract="true" category="interface">
<description>
A RemoteQueryable object is an object that can take a BQL ord,&#xa; process the query remotely and return the result.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">16 Oct 2003</tag>
<tag name="@version">$Revision: 2$ $Date: 3/28/05 10:03:22 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<!-- javax.baja.naming.RemoteQueryable.bqlQuery(javax.baja.naming.BOrd) -->
<method name="bqlQuery"  public="true" abstract="true">
<description>
Resolve the specified BQL ord and return the result.
</description>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
</method>

<!-- javax.baja.naming.RemoteQueryable.bqlQuery(javax.baja.naming.BOrd, javax.baja.sys.Context) -->
<method name="bqlQuery"  public="true" default="true">
<description>
Resolve the specified BQL ord and return the result using the additional&#xa; Context information provided.
</description>
<tag name="@since">Niagara 4.11</tag>
<parameter name="ord">
<type class="javax.baja.naming.BOrd"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
</method>

</class>
</bajadoc>
