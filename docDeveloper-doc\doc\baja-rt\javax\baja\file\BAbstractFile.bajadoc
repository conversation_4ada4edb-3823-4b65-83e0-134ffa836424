<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.BAbstractFile" name="BAbstractFile" packageName="javax.baja.file" public="true" abstract="true">
<description>
BAbstractFile provides a default base class upon which &#xa; to build BIFile implementations.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">24 Jan 03</tag>
<tag name="@version">$Revision: 31$ $Date: 8/14/09 10:37:29 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BObject"/>
</extends>
<implements>
<type class="javax.baja.file.BIFile"/>
</implements>
<!-- javax.baja.file.BAbstractFile(javax.baja.file.BIFileStore) -->
<constructor name="BAbstractFile" public="true">
<parameter name="store">
<type class="javax.baja.file.BIFileStore"/>
</parameter>
<description>
Construct a file with the specified store.
</description>
</constructor>

<!-- javax.baja.file.BAbstractFile() -->
<constructor name="BAbstractFile" public="true">
<description>
Construct (must set store using setStore())..
</description>
</constructor>

<!-- javax.baja.file.BAbstractFile.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getStore() -->
<method name="getStore"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the file&#x27;s backing store.  A file store should&#xa; always be configured either with the constructor or&#xa; the setStore() method.
</description>
<return>
<type class="javax.baja.file.BIFileStore"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.setStore(javax.baja.file.BIFileStore) -->
<method name="setStore"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the file&#x27;s backing store.
</description>
<parameter name="store">
<type class="javax.baja.file.BIFileStore"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getFileSpace() -->
<method name="getFileSpace"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getStore().getFileSpace()&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.file.BFileSpace"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getFilePath() -->
<method name="getFilePath"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getStore().getFilePath()&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.file.FilePath"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getFileName() -->
<method name="getFileName"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getStore().getFileName()&lt;/code&gt;.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getExtension() -->
<method name="getExtension"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getStore().getExtension()&lt;/code&gt;.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.isDirectory() -->
<method name="isDirectory"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getStore().isDirectory()&lt;/code&gt;.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getSize() -->
<method name="getSize"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getStore().getSize()&lt;/code&gt;.
</description>
<return>
<type class="long"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getLastModified() -->
<method name="getLastModified"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getStore().getLastModified()&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.setLastModified(javax.baja.sys.BAbsTime) -->
<method name="setLastModified"  public="true">
<description>
Set the Abstract Store&#x27;s lastModified absTime to the nearest second.
</description>
<tag name="@since">Niagara 3.5</tag>
<parameter name="absTime">
<type class="javax.baja.sys.BAbsTime"/>
</parameter>
<return>
<type class="boolean"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BAbstractFile.isReadonly() -->
<method name="isReadonly"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getStore().isReadonly()&lt;/code&gt;.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getInputStream() -->
<method name="getInputStream"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getStore().getInputStream()&lt;/code&gt;.
</description>
<return>
<type class="java.io.InputStream"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BAbstractFile.read() -->
<method name="read"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getStore().read()&lt;/code&gt;.
</description>
<return>
<type class="byte" dimension="1"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BAbstractFile.delete() -->
<method name="delete"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Call &lt;code&gt;getFileSpace().delete(getFilePath())&lt;/code&gt;.
</description>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BAbstractFile.getOutputStream() -->
<method name="getOutputStream"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getStore().getOutputStream()&lt;/code&gt;.
</description>
<return>
<type class="java.io.OutputStream"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BAbstractFile.write(byte[]) -->
<method name="write"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Call &lt;code&gt;getStore().write()&lt;/code&gt;.
</description>
<parameter name="content">
<type class="byte" dimension="1"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BAbstractFile.getMimeType() -->
<method name="getMimeType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Default returns &lt;code&gt;&#x22;application/octet-stream&#x22;&lt;/code&gt;
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getCrc() -->
<method name="getCrc"  public="true">
<description>
Return &lt;code&gt;getStore().getCrc()&lt;/code&gt;.
</description>
<tag name="@since">Niagara 3.5</tag>
<return>
<type class="long"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.BAbstractFile.getSpace() -->
<method name="getSpace"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getFileSpace()&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.space.BSpace"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getAbsoluteOrd() -->
<method name="getAbsoluteOrd"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;fileSpace.absoluteOrd + filePath&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getOrdInHost() -->
<method name="getOrdInHost"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;fileSpace.ordInHost + filePath&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getOrdInSession() -->
<method name="getOrdInSession"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;fileSpace.ordInSession + filePath&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getOrdInSpace() -->
<method name="getOrdInSpace"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getOrdInSession()&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.isPendingMove() -->
<method name="isPendingMove"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return pending move flag.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.setPendingMove(boolean) -->
<method name="setPendingMove"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return pending move flag.
</description>
<parameter name="pendingMove">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getNavName() -->
<method name="getNavName"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getFileName()&lt;/code&gt;.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getNavDisplayName(javax.baja.sys.Context) -->
<method name="getNavDisplayName"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getNavName()&lt;/code&gt;.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getNavParent() -->
<method name="getNavParent"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;fileSpace.findFile(filePath.parent)&lt;/code&gt;
</description>
<return>
<type class="javax.baja.nav.BINavNode"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.resolveNavChild(java.lang.String) -->
<method name="resolveNavChild"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Call getNavChild and if it returns null then&#xa; throw UnresolvedException.
</description>
<parameter name="navName">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.nav.BINavNode"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getNavOrd() -->
<method name="getNavOrd"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getAbsoluteOrd()&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getNavIcon() -->
<method name="getNavIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Default is to return getIcon().
</description>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getCategoryMask() -->
<method name="getCategoryMask"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Files are mapped to categories by ord in &lt;code&gt;CategoryService.ordMap&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getAppliedCategoryMask() -->
<method name="getAppliedCategoryMask"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Files are mapped to categories by ord in &lt;code&gt;CategoryService.ordMap&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.category.BCategoryMask"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getPermissions(javax.baja.sys.Context) -->
<method name="getPermissions"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Call &lt;code&gt;getStore().getPermissions(this, cx)&lt;/code&gt;.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.security.BPermissions"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.canRead(javax.baja.naming.OrdTarget) -->
<method name="canRead"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return if operator read is enabled.
</description>
<parameter name="cx">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.canWrite(javax.baja.naming.OrdTarget) -->
<method name="canWrite"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return if operator write is enabled.
</description>
<parameter name="cx">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.canInvoke(javax.baja.naming.OrdTarget) -->
<method name="canInvoke"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return if operator invoke is enabled.
</description>
<parameter name="cx">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.toPathString() -->
<method name="toPathString"  public="true">
<description>
Get path string, guaranteed not to be null.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getStore().toString(cx)&lt;/code&gt;.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.hashCode() -->
<method name="hashCode"  public="true">
<description>
Return &lt;code&gt;getStore().hashCode()&lt;/code&gt;.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description>
If object is a BIFile, then return if the stores&#xa; are equal.
</description>
<parameter name="object">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.file.BAbstractFile.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Dump slots and information common to all BComplex&#x27;s.
</description>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.file.BAbstractFile.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
