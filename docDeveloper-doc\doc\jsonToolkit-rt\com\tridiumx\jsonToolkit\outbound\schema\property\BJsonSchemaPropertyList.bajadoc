<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaPropertyList" name="BJsonSchemaPropertyList" packageName="com.tridiumx.jsonToolkit.outbound.schema.property" public="true" abstract="true">
<description>
A list property name value pairs.&#xa; Only valid if this node is inside an object or array (where the keys will be stripped), cannot sit at top level&#xa; or support nested children.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaBoundMember"/>
</extends>
<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaPropertyList() -->
<constructor name="BJsonSchemaPropertyList" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaPropertyList.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaPropertyList.isChildLegal(javax.baja.sys.BComponent) -->
<method name="isChildLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="child">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaPropertyList.getPropertiesToIncludeInJson(javax.baja.sys.BComplex) -->
<method name="getPropertiesToIncludeInJson"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="resolvedTarget">
<type class="javax.baja.sys.BComplex"/>
</parameter>
<return>
<parameterizedType class="java.util.List">
<args>
<type class="java.lang.String"/>
</args>
</parameterizedType>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaPropertyList.writeProperty(com.tridium.json.JSONWriter, java.lang.String, javax.baja.sys.BObject) -->
<method name="writeProperty"  protected="true">
<description/>
<parameter name="json">
<type class="com.tridium.json.JSONWriter"/>
</parameter>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BObject"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaPropertyList.jsonName -->
<field name="jsonName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;jsonName&lt;/code&gt; property.
</description>
<tag name="@see">#getJsonName</tag>
<tag name="@see">#setJsonName</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaPropertyList.jsonNameSource -->
<field name="jsonNameSource"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;jsonNameSource&lt;/code&gt; property.
</description>
<tag name="@see">#getJsonNameSource</tag>
<tag name="@see">#setJsonNameSource</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaPropertyList.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
