<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="baja" runtimeProfile="rt" name="javax.baja.authn">
<description/>
<class packageName="javax.baja.authn" name="AuthenticationUtil"><description>This utility class offers support for authentication&#xa; related tasks.</description></class>
<class packageName="javax.baja.authn" name="BAuthenticationScheme"><description>Base class for all authentication schemes to be added to the&#xa; Authentication Service.</description></class>
<class packageName="javax.baja.authn" name="BPasswordAuthenticationScheme"/>
<class packageName="javax.baja.authn" name="BSSOAuthenticationScheme"><description>This is the base class for &lt;code&gt;<see ref="javax.baja.authn.BAuthenticationScheme">BAuthenticationScheme</see>&lt;/code&gt;s that can&#xa; perform Single Sign On.</description></class>
</package>
</bajadoc>
