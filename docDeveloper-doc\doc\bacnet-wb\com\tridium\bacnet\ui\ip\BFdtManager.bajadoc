<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="wb" qualifiedName="com.tridium.bacnet.ui.ip.BFdtManager" name="BFdtManager" packageName="com.tridium.bacnet.ui.ip" public="true">
<description>
BFdtManager provides access to the BACnet/IP&#xa; Foreign Device Table (FDT) of the local BBMD.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">23 Oct 03</tag>
<tag name="@version">$Revision: 1$ $Date: 12/18/2003 9:44:58 AM$</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.workbench.view.BWbComponentView"/>
</extends>
</class>
</bajadoc>
