<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnetOws" runtimeProfile="rt" name="com.tridium.bacnetOws">
<description/>
<class packageName="com.tridium.bacnetOws" name="BBacnetOwsDeviceFolder"><description>BBacnetOwsDeviceFolder.</description></class>
<class packageName="com.tridium.bacnetOws" name="BBacnetOwsNetwork"><description>BBacnetOwsNetwork is the base container for the Tridium Bacnet Operator Workstation&#xa; service.</description></class>
<class packageName="com.tridium.bacnetOws" name="BBacnetThreadPoolWorker"/>
<class packageName="com.tridium.bacnetOws" name="BLocalBacnetOwsDevice"><description>BLocalBacnetOwsDevice is the representation of Niagara as a Bacnet&#xa; device on the Bacnet internetwork.</description></class>
</package>
</bajadoc>
