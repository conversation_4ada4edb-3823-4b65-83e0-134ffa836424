<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.subscription.BSubscriptionSlotBlacklist" name="BSubscriptionSlotBlacklist" packageName="com.tridiumx.jsonToolkit.outbound.schema.subscription" public="true">
<description>
Global slot filter for subscription events.&#xa; Some slots e.g wsAnnotation are pretty irrelevant and would not want to generate a new json schema every time it changes.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="com.tridiumx.jsonToolkit.outbound.schema.subscription.SubscriptionEventFilter"/>
</implements>
<property name="slotList" flags="">
<parameterizedType class="com.tridiumx.jsonToolkit.util.BListOf">
<args>
<wildcardType class="?">
<bounds kind="extends">
<type class="javax.baja.sys.BValue"/>
</bounds>
</wildcardType>
</args>
</parameterizedType>
<description>
Slot for the &lt;code&gt;slotList&lt;/code&gt; property.
</description>
<tag name="@see">#getSlotList</tag>
<tag name="@see">#setSlotList</tag>
</property>

<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.BSubscriptionSlotBlacklist() -->
<constructor name="BSubscriptionSlotBlacklist" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.BSubscriptionSlotBlacklist.getSlotList() -->
<method name="getSlotList"  public="true">
<description>
Get the &lt;code&gt;slotList&lt;/code&gt; property.
</description>
<tag name="@see">#slotList</tag>
<return>
<parameterizedType class="com.tridiumx.jsonToolkit.util.BListOf">
<args>
<wildcardType class="?">
<bounds kind="extends">
<type class="javax.baja.sys.BValue"/>
</bounds>
</wildcardType>
</args>
</parameterizedType>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.BSubscriptionSlotBlacklist.setSlotList(com.tridiumx.jsonToolkit.util.BListOf&lt;? extends javax.baja.sys.BValue&gt;) -->
<method name="setSlotList"  public="true">
<description>
Set the &lt;code&gt;slotList&lt;/code&gt; property.
</description>
<tag name="@see">#slotList</tag>
<parameter name="v">
<parameterizedType class="com.tridiumx.jsonToolkit.util.BListOf">
<args>
<wildcardType class="?">
<bounds kind="extends">
<type class="javax.baja.sys.BValue"/>
</bounds>
</wildcardType>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.BSubscriptionSlotBlacklist.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.BSubscriptionSlotBlacklist.test(javax.baja.sys.BComponentEvent) -->
<method name="test"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="event">
<type class="javax.baja.sys.BComponentEvent"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.support.FilterResult"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.BSubscriptionSlotBlacklist.slotList -->
<field name="slotList"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;slotList&lt;/code&gt; property.
</description>
<tag name="@see">#getSlotList</tag>
<tag name="@see">#setSlotList</tag>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.subscription.BSubscriptionSlotBlacklist.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
