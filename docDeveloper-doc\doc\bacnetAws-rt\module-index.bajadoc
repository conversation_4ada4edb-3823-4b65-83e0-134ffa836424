<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="bacnetAws" runtimeProfile="rt" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>Niagara BACnet AWS Driver</description>
<package name="javax.baja.bacnetAws.datatypes"/>
<package name="javax.baja.bacnetAws.enums"/>
<package name="javax.baja.bacnetAws.config"/>
<package name="com.tridium.bacnetAws.history"/>
<package name="com.tridium.bacnetAws"/>
<package name="com.tridium.bacnetAws.datatypes"/>
<package name="com.tridium.bacnetAws.enums"/>
<package name="com.tridium.bacnetAws.job"/>
<class packageName="javax.baja.bacnetAws.enums" name="BAccumulatorStatus"><description>- Insert description here.</description></class>
<class packageName="com.tridium.bacnetAws.datatypes" name="BBackupConfig"><description>BBackupConfig represents the choices for the&#xa; user in the Backup procedure.</description></class>
<class packageName="com.tridium.bacnetAws.job" name="BBackupJob"><description>BBackupJob backs up the configuration in a BacnetWsDevice.</description></class>
<class packageName="javax.baja.bacnetAws.config" name="BBacnetAccessDoor"></class>
<class packageName="javax.baja.bacnetAws.config" name="BBacnetAccumulator"></class>
<class packageName="javax.baja.bacnetAws.datatypes" name="BBacnetAccumulatorRecord"><description>BBacnetAccumulatorRecord represents the BACnetAccumulatorRecord sequence.</description></class>
<class packageName="javax.baja.bacnetAws.datatypes" name="BBacnetActionCommand"></class>
<class packageName="javax.baja.bacnetAws.datatypes" name="BBacnetActionList"></class>
<class packageName="javax.baja.bacnetAws.config" name="BBacnetAwsConfigDeviceExt"><description>BBacnetConfigDeviceExt represents the configuration representation of a&#xa; Bacnet Aws device.</description></class>
<class packageName="javax.baja.bacnetAws.config" name="BBacnetAwsConfigFolder"><description>BBacnetAwsConfigFolder is the standard container to use&#xa; under BBacnetAwsConfigDeviceExt to organize BBacnetObjects.</description></class>
<class packageName="com.tridium.bacnetAws" name="BBacnetAwsDevice"><description>BBacnetDevice represents the Baja shadow object for a Bacnet device under a BacnetAwsNetwork.</description></class>
<class packageName="com.tridium.bacnetAws" name="BBacnetAwsDeviceFolder"><description>BBacnetAwsDeviceFolder.</description></class>
<class packageName="com.tridium.bacnetAws.job" name="BBacnetAwsDiscoverTrendLogsJob"><description>BBacnetAwsDiscoverTrendLogsJob augments BBacnetDiscoverTrendLogsJob to also&#xa; handle EventLogs.</description></class>
<class packageName="com.tridium.bacnetAws.history" name="BBacnetAwsHistoryDeviceExt"><description>BBacnetOwsHistoryDeviceExt adds support for TrendMultiple.</description></class>
<class packageName="com.tridium.bacnetAws" name="BBacnetAwsNetwork"><description>BBacnetAwsNetwork is the base container for the Tridium Bacnet Advanced Workstation&#xa; service.</description></class>
<class packageName="javax.baja.bacnetAws.config" name="BBacnetCommand"></class>
<class packageName="com.tridium.bacnetAws.job" name="BBacnetCreateObjectJob"><description>BBacnetCreateObjectJob is used to create objects in a Bacnet device.</description></class>
<class packageName="com.tridium.bacnetAws.job" name="BBacnetDeleteObjectJob"><description>BBacnetCreateObjectJob is used to create objects in a Bacnet device.</description></class>
<class packageName="javax.baja.bacnetAws.enums" name="BBacnetDoorValue"></class>
<class packageName="javax.baja.bacnetAws.config" name="BBacnetEventLog"></class>
<class packageName="com.tridium.bacnetAws.history" name="BBacnetEventLogImport"><description>BBacnetEventLogImport defines an archive action for transferring&#xa; one event log from a remote Bacnet source to the local&#xa; destination.</description></class>
<class packageName="javax.baja.bacnetAws.datatypes" name="BBacnetEventLogRecord"><description>BBacnetEventLogRecord represents the BacneteEventLogRecord sequence.</description></class>
<class packageName="com.tridium.bacnetAws.history" name="BBacnetEventRecord"><description>&lt;code&gt;BBacnetEventRecord&lt;/code&gt; is a Bacnet event record</description></class>
<class packageName="javax.baja.bacnetAws.config" name="BBacnetLoadControl"></class>
<class packageName="javax.baja.bacnetAws.config" name="BBacnetPulseConverter"></class>
<class packageName="javax.baja.bacnetAws.datatypes" name="BBacnetSessionKey"><description>BBacnetSessionKey represents the BacnetSessionKey data type.</description></class>
<class packageName="javax.baja.bacnetAws.datatypes" name="BBacnetShedLevel"><description>BBacnetScale represents the BACnetScale data type.</description></class>
<class packageName="javax.baja.bacnetAws.enums" name="BBacnetShedState"><description>Represents the BBacnetShedState define in the Bacnet spec.</description></class>
<class packageName="javax.baja.bacnetAws.config" name="BBacnetStructuredView"></class>
<class packageName="javax.baja.bacnetAws.datatypes" name="BBacnetVtSession"><description>BBacnetVtSession represents the BACnetVtSession data type.</description></class>
<class packageName="com.tridium.bacnetAws.datatypes" name="BCommControlConfig"><description>This class file specifies parameters to constrain a&#xa; Device Communication Control request.</description></class>
<class packageName="com.tridium.bacnetAws.datatypes" name="BCreateObjectParameters"><description>Create Object Parameters transfer the data from &#xa;  the WB to the Station providing the creation job &#xa;  the required information.</description></class>
<class packageName="com.tridium.bacnetAws.job" name="BDeviceCommControlJob"><description>BDeviceCommControlJob enables or disables communications in a  BacnetWsDevice.</description></class>
<class packageName="com.tridium.bacnetAws" name="BLocalBacnetAwsDevice"><description>BLocalBacnetAwsDevice is the representation of Niagara as a Bacnet&#xa; device on the Bacnet internetwork.</description></class>
<class packageName="com.tridium.bacnetAws.datatypes" name="BObjectParameters"></class>
<class packageName="com.tridium.bacnetAws.enums" name="BReinitializeCommand"><description>BReinitializeCommand represents the choices available to the user&#xa; to issue to a device using the ReinitializeDevice-Request.</description></class>
<class packageName="com.tridium.bacnetAws.datatypes" name="BReinitializeDeviceConfig"><description>BReinitializeDeviceConfig represents the choices for the&#xa; user in manually issuing a ReinitializeDevice-Request to a device.</description></class>
<class packageName="com.tridium.bacnetAws.job" name="BReinitializeDeviceJob"><description>BReinitializeDeviceJob reinitializes a BacnetWsDevice.</description></class>
<class packageName="com.tridium.bacnetAws.datatypes" name="BRestoreConfig"><description>BRestoreConfig represents the choices for the&#xa; user in the Restore procedure.</description></class>
<class packageName="com.tridium.bacnetAws.job" name="BRestoreJob"><description>BRestoreJob restores the configuration in a BacnetWsDevice.</description></class>
</module>
</bajadoc>
