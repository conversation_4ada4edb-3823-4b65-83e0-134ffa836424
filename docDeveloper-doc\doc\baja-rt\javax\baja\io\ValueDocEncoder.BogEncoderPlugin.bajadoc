<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.io.ValueDocEncoder$BogEncoderPlugin" name="ValueDocEncoder.BogEncoderPlugin" packageName="javax.baja.io" public="true" static="true" final="true" innerClass="true">
<description/>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</implements>
<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin(java.io.File) -->
<constructor name="BogEncoderPlugin" public="true">
<parameter name="file">
<type class="java.io.File"/>
</parameter>
<throws>
<type class="java.io.IOException"/>
</throws>
<description/>
</constructor>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin(java.io.OutputStream) -->
<constructor name="BogEncoderPlugin" public="true">
<parameter name="out">
<type class="java.io.OutputStream"/>
</parameter>
<throws>
<type class="java.io.IOException"/>
</throws>
<description/>
</constructor>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin(java.io.File, javax.baja.sys.Context) -->
<constructor name="BogEncoderPlugin" public="true">
<parameter name="file">
<type class="java.io.File"/>
</parameter>
<parameter name="pluginContext">
<type class="javax.baja.sys.Context"/>
</parameter>
<throws>
<type class="java.io.IOException"/>
</throws>
<description/>
</constructor>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin(java.io.OutputStream, javax.baja.sys.Context) -->
<constructor name="BogEncoderPlugin" public="true">
<parameter name="out">
<type class="java.io.OutputStream"/>
</parameter>
<parameter name="pluginContext">
<type class="javax.baja.sys.Context"/>
</parameter>
<throws>
<type class="java.io.IOException"/>
</throws>
<description/>
</constructor>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.encodeDocument(javax.baja.io.ValueDocEncoder, javax.baja.sys.BValue) -->
<method name="encodeDocument"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="encoder">
<type class="javax.baja.io.ValueDocEncoder"/>
</parameter>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.start(java.lang.String) -->
<method name="start"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.startArray(java.lang.String) -->
<method name="startArray"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.endArray() -->
<method name="endArray"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.end() -->
<method name="end"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.end(java.lang.String) -->
<method name="end"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.endAttr() -->
<method name="endAttr"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.newLine() -->
<method name="newLine"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.key(java.lang.String) -->
<method name="key"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.attr(java.lang.String, boolean) -->
<method name="attr"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<parameter name="val">
<type class="boolean"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.attr(java.lang.String, double) -->
<method name="attr"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<parameter name="val">
<type class="double"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.attr(java.lang.String, java.lang.String) -->
<method name="attr"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<parameter name="str">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.attrSafe(java.lang.String, java.lang.String) -->
<method name="attrSafe"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<parameter name="str">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.failOnEncodingExceptions() -->
<method name="failOnEncodingExceptions"  public="true">
<description>
Returns true if an exception thrown while encoding a single object should cause the entire&#xa; operation to fail.   The value defaults to true, and can be modified using &lt;code&gt;<see ref="javax.baja.io.ValueDocEncoder.BogEncoderPlugin#setFailOnEncodingExceptions(boolean)">#setFailOnEncodingExceptions(boolean)</see>&lt;/code&gt;
</description>
<tag name="@since">Niagara 4.0</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.setFailOnEncodingExceptions(boolean) -->
<method name="setFailOnEncodingExceptions"  public="true">
<description>
Configure the plugin to determine whether encoding exceptions cause immediate failures
</description>
<tag name="@since">Niagara 4.0</tag>
<parameter name="value">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.processEncodingException(javax.baja.sys.BObject, java.lang.Exception) -->
<method name="processEncodingException"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
If &lt;code&gt;<see ref="javax.baja.io.ValueDocEncoder.BogEncoderPlugin#setFailOnEncodingExceptions(boolean)">#setFailOnEncodingExceptions(boolean)</see>&lt;/code&gt; has been used to override the default of true,&#xa; then return false, otherwise throw an &lt;code&gt;<see ref="java.io.IOException">IOException</see>&lt;/code&gt; that wraps the given exception
</description>
<tag name="@since">Niagara 4.0</tag>
<parameter name="object">
<type class="javax.baja.sys.BObject"/>
<description>
object whose encoding has failed
</description>
</parameter>
<parameter name="e">
<type class="java.lang.Exception"/>
<description>
exception that was thrown as a result of the encoding failure
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the exception was handled by the plugin and doesn&#x27;t need additional handling&#xa;         by the encoder.   If false, the exception is unhandled and will be logged and added&#xa;         to a list that can be retrieved using &lt;code&gt;<see ref="javax.baja.io.ValueDocEncoder#getUnhandledEncodingExceptions()">ValueDocEncoder#getUnhandledEncodingExceptions()</see>&lt;/code&gt;&#xa;         after encoding completes
</description>
</return>
<throws>
<type class="java.io.IOException"/>
<description>
will be thrown if the plugin determines that the entire encoding operation&#xa;         should fail
</description>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.value(java.lang.String) -->
<method name="value"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="str">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.comment(java.lang.String) -->
<method name="comment"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="text">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.incrementIndent() -->
<method name="incrementIndent"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.decrementIndent() -->
<method name="decrementIndent"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.indent() -->
<method name="indent"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.getIndent() -->
<method name="getIndent"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.encodeType(javax.baja.sys.Type) -->
<method name="encodeType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="type">
<type class="javax.baja.sys.Type"/>
</parameter>
<return>
<type class="javax.baja.io.ValueDocEncoder$IEncoderPlugin"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.flush() -->
<method name="flush"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.close() -->
<method name="close"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.isZipped() -->
<method name="isZipped"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.setZipped(boolean) -->
<method name="setZipped"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="zipped">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.version() -->
<method name="version"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.util.Version"/>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.setVersion(javax.baja.util.Version) -->
<method name="setVersion"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="version">
<type class="javax.baja.util.Version"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.setBogPasswordObjectEncoder(com.tridium.nre.security.io.BogPasswordObjectEncoder) -->
<method name="setBogPasswordObjectEncoder"  public="true">
<description/>
<parameter name="value">
<type class="com.tridium.nre.security.io.BogPasswordObjectEncoder"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.getBogPasswordObjectEncoder() -->
<method name="getBogPasswordObjectEncoder"  public="true">
<description/>
<return>
<type class="com.tridium.nre.security.io.BogPasswordObjectEncoder"/>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.setPassPhrase(java.util.Optional&lt;javax.baja.security.BPassword&gt;) -->
<method name="setPassPhrase"  public="true">
<description/>
<parameter name="value">
<parameterizedType class="java.util.Optional">
<args>
<type class="javax.baja.security.BPassword"/>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.marshal(javax.baja.sys.BValue) -->
<method name="marshal"  public="true" static="true">
<description>
Marshal the specified value into an XML String.&#xa; This XML is not a full document - see encode().
</description>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.marshal(javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="marshal"  public="true" static="true">
<description>
Marshal the specified value into an XML String.&#xa; This XML is not a full document - see encode().
</description>
<tag name="@since">Niagara 4.0</tag>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.getWriter() -->
<method name="getWriter"  public="true">
<description/>
<return>
<type class="javax.baja.xml.XWriter"/>
</return>
</method>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.indent -->
<field name="indent"  protected="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.io.ValueDocEncoder.BogEncoderPlugin.version -->
<field name="version"  protected="true">
<type class="javax.baja.util.Version"/>
<description/>
</field>

</class>
</bajadoc>
