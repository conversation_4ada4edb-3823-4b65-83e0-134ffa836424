<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BacnetPropertyList" name="BacnetPropertyList" packageName="javax.baja.bacnet.export" public="true" final="true">
<description>
Every BACnetObject in devices &amp;gt; PR 14&#xa; will include a PropertyList.&#xa; &lt;p&gt;&#xa; This new property will eventually&#xa; allow for faster property discovery,&#xa; especially for devices that do not support segmentation,&#xa; or contain a large number of proprietary properties.&#xa; &lt;p&gt;&#xa; The BacnetPropertyList utility class&#xa; consolidates methods related to the special&#xa; handling of the BACnetPropertyList Array.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<!-- javax.baja.bacnet.export.BacnetPropertyList.makePropertyList(int[]...) -->
<method name="makePropertyList"  public="true" static="true" isVarargs="true">
<description/>
<parameter name="propertyLists">
<type class="int" dimension="2"/>
</parameter>
<return>
<type class="int" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BacnetPropertyList.readAll(int[]) -->
<method name="readAll"  public="true" static="true">
<description/>
<parameter name="props">
<type class="int" dimension="1"/>
</parameter>
<return>
<type class="byte" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BacnetPropertyList.size(int[]) -->
<method name="size"  public="true" static="true">
<description/>
<parameter name="props">
<type class="int" dimension="1"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BacnetPropertyList.read(int, int[]) -->
<method name="read"  public="true" static="true">
<description/>
<parameter name="ndx">
<type class="int"/>
</parameter>
<parameter name="props">
<type class="int" dimension="1"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BacnetPropertyList.getInvalidIdx(int, int) -->
<method name="getInvalidIdx"  public="true" static="true">
<description/>
<parameter name="propId">
<type class="int"/>
</parameter>
<parameter name="ndx">
<type class="int"/>
</parameter>
<return>
<type class="com.tridium.bacnet.asn.NReadPropertyResult"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BacnetPropertyList.shouldInclude(int) -->
<method name="shouldInclude"  public="true" static="true">
<description/>
<parameter name="propId">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

</class>
</bajadoc>
