<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.ext.offnormal.BBooleanCommandFailureAlgorithm" name="BBooleanCommandFailureAlgorithm" packageName="javax.baja.alarm.ext.offnormal" public="true">
<description>
BBooleanCommandFailureAlgorithm implements command&#xa; failure alarm detection algorithm for boolean&#xa; objects as described in BACnet.  If feedback and output&#xa; values of the point are not equal for timeDelay duration,&#xa; an offnormal alarm is generated.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">04 May 01</tag>
<tag name="@version">$Revision: 29$ $Date: 3/23/05 11:53:24 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.alarm.ext.offnormal.BTwoStateAlgorithm"/>
</extends>
<property name="feedbackValue" flags="s">
<type class="javax.baja.status.BStatusBoolean"/>
<description>
Slot for the &lt;code&gt;feedbackValue&lt;/code&gt; property.&#xa; Feedback Value
</description>
<tag name="@see">#getFeedbackValue</tag>
<tag name="@see">#setFeedbackValue</tag>
</property>

<!-- javax.baja.alarm.ext.offnormal.BBooleanCommandFailureAlgorithm() -->
<constructor name="BBooleanCommandFailureAlgorithm" public="true">
<description/>
</constructor>

<!-- javax.baja.alarm.ext.offnormal.BBooleanCommandFailureAlgorithm.getFeedbackValue() -->
<method name="getFeedbackValue"  public="true">
<description>
Get the &lt;code&gt;feedbackValue&lt;/code&gt; property.&#xa; Feedback Value
</description>
<tag name="@see">#feedbackValue</tag>
<return>
<type class="javax.baja.status.BStatusBoolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BBooleanCommandFailureAlgorithm.setFeedbackValue(javax.baja.status.BStatusBoolean) -->
<method name="setFeedbackValue"  public="true">
<description>
Set the &lt;code&gt;feedbackValue&lt;/code&gt; property.&#xa; Feedback Value
</description>
<tag name="@see">#feedbackValue</tag>
<parameter name="v">
<type class="javax.baja.status.BStatusBoolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BBooleanCommandFailureAlgorithm.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BBooleanCommandFailureAlgorithm.isGrandparentLegal(javax.baja.sys.BComponent) -->
<method name="isGrandparentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
A BBooleanCommandFailureAlgorithm&#x27;s grandparent must implement&#xa; the BooleanPoint interface
</description>
<parameter name="grandparent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BBooleanCommandFailureAlgorithm.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BBooleanCommandFailureAlgorithm.isNormal(javax.baja.status.BStatusValue) -->
<method name="isNormal"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return true if the feedback value matches output value
</description>
<parameter name="o">
<type class="javax.baja.status.BStatusValue"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BBooleanCommandFailureAlgorithm.writeAlarmData(javax.baja.status.BStatusValue, java.util.Map) -->
<method name="writeAlarmData"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Write the key-value pairs defining alarm data for the&#xa;  alarm algorithm and state to the given Facets.&#xa; &lt;p&gt;&#xa;  The alarm data for a Command Failure alarm is given by&#xa;  BACnet table 13-3, Standard Object Property Values&#xa;  returned in notifications.
</description>
<parameter name="out">
<type class="javax.baja.status.BStatusValue"/>
<description>
The relevant control point status value
</description>
</parameter>
<parameter name="map">
<parameterizedType class="java.util.Map">
<args>
</args>
</parameterizedType>
<description>
The map.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarm.ext.offnormal.BBooleanCommandFailureAlgorithm.feedbackValue -->
<field name="feedbackValue"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;feedbackValue&lt;/code&gt; property.&#xa; Feedback Value
</description>
<tag name="@see">#getFeedbackValue</tag>
<tag name="@see">#setFeedbackValue</tag>
</field>

<!-- javax.baja.alarm.ext.offnormal.BBooleanCommandFailureAlgorithm.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
