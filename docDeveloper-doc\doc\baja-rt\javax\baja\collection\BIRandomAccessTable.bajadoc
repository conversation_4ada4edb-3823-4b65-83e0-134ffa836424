<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.collection.BIRandomAccessTable" name="BIRandomAccessTable" packageName="javax.baja.collection" public="true" interface="true" abstract="true" category="interface">
<description>
A &lt;code&gt;<see ref="javax.baja.collection.BITable">BITable</see>&lt;/code&gt; that supports random access to its cells.
</description>
<tag name="@author">&lt;a href=&#x22;mailto:<EMAIL>&#x22;&gt;<PERSON>&lt;/a&gt;</tag>
<implements>
<parameterizedType class="javax.baja.collection.BITable">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</implements>
<typeParameters>
<typeVariable class="T">
<bounds>
<type class="javax.baja.sys.BIObject"/>
</bounds>
</typeVariable>
</typeParameters>
<!-- javax.baja.collection.BIRandomAccessTable.get(int) -->
<method name="get"  public="true" abstract="true">
<description>
Get the object at the given row.
</description>
<parameter name="row">
<type class="int"/>
</parameter>
<return>
<parameterizedType class="javax.baja.collection.Row">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.collection.BIRandomAccessTable.size() -->
<method name="size"  public="true" abstract="true">
<description>
Get the number of rows in the table.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.collection.BIRandomAccessTable.sort(javax.baja.collection.Column, boolean) -->
<method name="sort"  public="true" default="true">
<description>
Sort the table by the given column. The default implementation will read this table into&#xa; a &lt;code&gt;<see ref="javax.baja.collection.BInMemoryTable">BInMemoryTable</see>&lt;/code&gt; and then sort that.
</description>
<parameter name="column">
<type class="javax.baja.collection.Column"/>
<description>
the column to sort by
</description>
</parameter>
<parameter name="ascending">
<type class="boolean"/>
<description>
if true, sort the table in ascending order by the given column
</description>
</parameter>
<return>
<parameterizedType class="javax.baja.collection.BIRandomAccessTable">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
<description>
a BIRandomAccessTable that is sorted.
</description>
</return>
</method>

<!-- javax.baja.collection.BIRandomAccessTable.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
