<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.BacnetException" name="BacnetException" packageName="javax.baja.bacnet" public="true" category="exception">
<description>
BacnetException is the base class for exceptions that&#xa; occur while performing any Bacnet operations.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">26 Sep 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BajaException"/>
</extends>
<!-- javax.baja.bacnet.BacnetException(java.lang.String, java.lang.Throwable) -->
<constructor name="BacnetException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="cause">
<type class="java.lang.Throwable"/>
</parameter>
<description>
Constructor with specified message and nested exception.
</description>
</constructor>

<!-- javax.baja.bacnet.BacnetException(java.lang.Throwable) -->
<constructor name="BacnetException" public="true">
<parameter name="cause">
<type class="java.lang.Throwable"/>
</parameter>
<description>
Constructor with specified nested exception.
</description>
</constructor>

<!-- javax.baja.bacnet.BacnetException(java.lang.String) -->
<constructor name="BacnetException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<description>
Constructor with specified message.
</description>
</constructor>

<!-- javax.baja.bacnet.BacnetException() -->
<constructor name="BacnetException" public="true">
<description>
No argument constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.BacnetException.lex -->
<field name="lex"  protected="true" static="true">
<type class="javax.baja.util.Lexicon"/>
<description/>
</field>

</class>
</bajadoc>
