<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.file.zip.BZipFileEntry" name="BZipFileEntry" packageName="javax.baja.file.zip" public="true">
<description>
BZipFileEntry is a BIFileStore implementation for &#xa; local files using java.util.zip.ZipEntry.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">24 Jan 03</tag>
<tag name="@version">$Revision: 5$ $Date: 8/14/09 10:38:47 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.file.BAbstractFileStore"/>
</extends>
<!-- javax.baja.file.zip.BZipFileEntry(javax.baja.file.zip.BZipSpace, javax.baja.file.FilePath, java.util.zip.ZipEntry) -->
<constructor name="BZipFileEntry" public="true">
<parameter name="space">
<type class="javax.baja.file.zip.BZipSpace"/>
</parameter>
<parameter name="path">
<type class="javax.baja.file.FilePath"/>
</parameter>
<parameter name="zipEntry">
<type class="java.util.zip.ZipEntry"/>
</parameter>
<description>
Construct.
</description>
</constructor>

<!-- javax.baja.file.zip.BZipFileEntry.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFileEntry.getZipEntry() -->
<method name="getZipEntry"  public="true">
<description>
Get the ZipEntry this instance wraps.
</description>
<return>
<type class="java.util.zip.ZipEntry"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFileEntry.isReadonly() -->
<method name="isReadonly"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return true.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFileEntry.getSize() -->
<method name="getSize"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return &lt;code&gt;getZipEntry().getSize()&lt;/code&gt;.
</description>
<return>
<type class="long"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFileEntry.getLastModified() -->
<method name="getLastModified"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return BAbsTime for File.lastModified().
</description>
<return>
<type class="javax.baja.sys.BAbsTime"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFileEntry.getInputStream() -->
<method name="getInputStream"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return InputStream for ZipEntry.
</description>
<return>
<type class="java.io.InputStream"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.zip.BZipFileEntry.getCrc() -->
<method name="getCrc"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return generated CRC
</description>
<tag name="@since">Niagara 3.5</tag>
<return>
<type class="long"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.file.zip.BZipFileEntry.hashCode() -->
<method name="hashCode"  public="true">
<description>
Return hash code.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.file.zip.BZipFileEntry.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
