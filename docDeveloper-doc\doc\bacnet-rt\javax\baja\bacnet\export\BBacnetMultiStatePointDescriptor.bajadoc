<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.export.BBacnetMultiStatePointDescriptor" name="BBacnetMultiStatePointDescriptor" packageName="javax.baja.bacnet.export" public="true" abstract="true">
<description>
BBacnetMultiStatePointDescriptor is the superclass for multi-state&#xa; point extensions exposing MultiStatePoints to Bacnet.
</description>
<tag name="@author"><PERSON> on 25 Jul 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.export.BBacnetPointDescriptor"/>
</extends>
<!-- javax.baja.bacnet.export.BBacnetMultiStatePointDescriptor() -->
<constructor name="BBacnetMultiStatePointDescriptor" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.export.BBacnetMultiStatePointDescriptor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStatePointDescriptor.isPointTypeLegal(javax.baja.control.BControlPoint) -->
<method name="isPointTypeLegal"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
BBacnetEnumPointDescriptor may only expose BEnumPoints.
</description>
<parameter name="pt">
<type class="javax.baja.control.BControlPoint"/>
<description>
the exposed point
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the Niagara point type is legal for this point type.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStatePointDescriptor.readProperty(int, int) -->
<method name="readProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.PropertyValue"/>
<description>
a PropertyValue containing either the encoded value or the error.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStatePointDescriptor.readRange(javax.baja.bacnet.io.RangeReference) -->
<method name="readRange"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Read the specified range of values of a compound property.
</description>
<parameter name="rangeReference">
<type class="javax.baja.bacnet.io.RangeReference"/>
<description>
the range reference describing the requested range.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.RangeData"/>
<description>
a byte array containing the encoded range.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.RejectException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStatePointDescriptor.writeProperty(int, int, byte[], int) -->
<method name="writeProperty"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Set the value of a property.&#xa; Subclasses with additional properties override this to check for&#xa; their properties.  If no match is found, call this superclass&#xa; method to check these properties.
</description>
<parameter name="pId">
<type class="int"/>
<description>
the requested property-identifier.
</description>
</parameter>
<parameter name="ndx">
<type class="int"/>
<description>
the property array index (-1 if not specified).
</description>
</parameter>
<parameter name="val">
<type class="byte" dimension="1"/>
<description>
the Asn-encoded value for the property.
</description>
</parameter>
<parameter name="pri">
<type class="int"/>
<description>
the priority level (only used for commandable properties).
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
null if everything goes OK, or&#xa; an ErrorType describing the error if not.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStatePointDescriptor.readRange(javax.baja.bacnet.io.RangeReference, java.lang.Integer[], int) -->
<method name="readRange"  protected="true">
<description>
Execute the ReadRange for the given class and maximum encoded size&#xa; of the data type.
</description>
<parameter name="ref">
<type class="javax.baja.bacnet.io.RangeReference"/>
</parameter>
<parameter name="list">
<type class="java.lang.Integer" dimension="1"/>
</parameter>
<parameter name="maxEncodedSize">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.RangeData"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStatePointDescriptor.addRequiredProps(java.util.Vector) -->
<method name="addRequiredProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Subclass override method to add required properties.&#xa; NOTE: You MUST call super.addRequiredProps(v) first!
</description>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
Vector containing required propertyIds.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStatePointDescriptor.addOptionalProps(java.util.Vector) -->
<method name="addOptionalProps"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
<annotationValue kind="expr">
<expression>&#x22;unchecked&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Subclass override method to add optional properties.&#xa; NOTE: You MUST call super.addOptionalProps(v) first!
</description>
<parameter name="v">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
Vector containing optional propertyIds.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStatePointDescriptor.checkPointConfiguration() -->
<method name="checkPointConfiguration"  protected="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Override point for subclasses to provide additional configuration&#xa; constraints to allow point export.  Default implementation returns true.
</description>
<return>
<type class="boolean"/>
<description>
true if configuration is ok, false otherwise.
</description>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStatePointDescriptor.validate() -->
<method name="validate"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Override point for subclasses to validate their exposed point&#x27;s&#xa; current state.  Default implementation does nothing.  Some points may&#xa; set the BACnet status flags to fault if the Niagara value is disallowed&#xa; for the exposed BACnet object type.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStatePointDescriptor.getWritableEnumRange(byte[], javax.baja.sys.BEnumRange, boolean) -->
<method name="getWritableEnumRange"  protected="true" static="true">
<description>
Get the enum range values from the passed byte array
</description>
<parameter name="val">
<type class="byte" dimension="1"/>
<description/>
</parameter>
<parameter name="tagRange">
<type class="javax.baja.sys.BEnumRange"/>
</parameter>
<parameter name="skipOrdinals">
<type class="boolean"/>
</parameter>
<return>
<type class="javax.baja.bacnet.util.EnumRangeWrapper"/>
<description>
enumRange wrapper
</description>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
<description/>
</throws>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStatePointDescriptor.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.bacnet.export.BBacnetMultiStatePointDescriptor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
