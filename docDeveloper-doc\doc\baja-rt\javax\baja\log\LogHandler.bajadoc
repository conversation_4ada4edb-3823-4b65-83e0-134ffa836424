<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.log.LogHandler" name="LogHandler" packageName="javax.baja.log" public="true" interface="true" abstract="true" category="interface">
<description>
LogHandler is implemented by objects which&#xa; wish to receive log callbacks.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">9 Apr 00</tag>
<tag name="@version">$Revision: 3$ $Date: 11/15/00 8:08:40 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<tag name="@deprecated">as of Niagara 4.0.</tag>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<!-- javax.baja.log.LogHandler.publish(javax.baja.log.LogRecord) -->
<method name="publish"  public="true" abstract="true">
<description>
Publish a log record.
</description>
<parameter name="record">
<type class="javax.baja.log.LogRecord"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

</class>
</bajadoc>
