<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnetOws" runtimeProfile="rt" name="com.tridium.bacnetOws.job">
<description/>
<class packageName="com.tridium.bacnetOws.job" name="BGetEnrollmentSummaryJob"><description>BGetEnrollmentSummaryJob queries a device for objects that meet specific&#xa; filter criteria for event generation capability and/or status.</description></class>
<class packageName="com.tridium.bacnetOws.job" name="BGetEventInformationJob"><description>BGetEventInformationJob gets event information from a BacnetWsDevice.</description></class>
</package>
</bajadoc>
