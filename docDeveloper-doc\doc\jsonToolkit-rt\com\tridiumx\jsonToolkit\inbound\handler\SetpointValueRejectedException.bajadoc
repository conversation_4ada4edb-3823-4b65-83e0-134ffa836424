<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.handler.SetpointValueRejectedException" name="SetpointValueRejectedException" packageName="com.tridiumx.jsonToolkit.inbound.handler" public="true" category="exception">
<description>
Provides a basic means to test and report on writes from the platform which would exceed any&#xa; min/max facets present on the target point
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="java.lang.Exception"/>
</extends>
<!-- com.tridiumx.jsonToolkit.inbound.handler.SetpointValueRejectedException(java.lang.String) -->
<constructor name="SetpointValueRejectedException" public="true">
<parameter name="why">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

</class>
</bajadoc>
