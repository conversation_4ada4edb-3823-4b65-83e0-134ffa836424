<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="javax.baja.bacnet.export">
<description/>
<class packageName="javax.baja.bacnet.export" name="BacnetDescriptorUtil"><description>A collection of methods for interacting with BacnetDescriptor instances.</description></class>
<class packageName="javax.baja.bacnet.export" name="BacnetPropertyList"><description>Every BACnetObject in devices &amp;gt; PR 14&#xa; will include a PropertyList.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetAnalogInputDescriptor"><description>BBacnetAnalogInputDescriptor exposes a ControlPoint as a Bacnet&#xa; Analog Input Object.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetAnalogOutputDescriptor"><description>BBacnetAnalogOutputDescriptor exposes a ControlPoint as a Bacnet&#xa; Analog Output Object.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetAnalogPointDescriptor"><description>BBacnetAnalogPointDescriptor is the superclass for analog-type&#xa; point extensions exposing NumericPoints to Bacnet.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetAnalogValueDescriptor"><description>BBacnetAnalogValueDescriptor exposes a ControlPoint as a Bacnet&#xa; Analog Value Object.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetAnalogValuePrioritizedDescriptor"><description>BBacnetAnalogValuePrioritizedDescriptor exposes a ControlPoint as a writable (non-commandable) Bacnet&#xa; Analog Value Object.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetAnalogWritableDescriptor"><description>BBacnetAnalogWritableDescriptor exposes a ControlPoint as a&#xa; commandable analog point.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetBinaryInputDescriptor"><description>BBacnetBinaryInputDescriptor exposes a ControlPoint as a Bacnet&#xa; Binary Input Object.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetBinaryOutputDescriptor"><description>BBacnetBinaryOutputDescriptor exposes a ControlPoint as a Bacnet&#xa; Binary Output Object.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetBinaryPointDescriptor"><description>BBacnetBinaryPointDescriptor is the superclass for binary-type&#xa; point extensions exposing BooleanPoints to Bacnet.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetBinaryValueDescriptor"><description>BBacnetBinaryValueDescriptor exposes a ControlPoint as a Bacnet&#xa; Binary Value Object.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetBinaryValuePrioritizedDescriptor"><description>BBacnetBinaryValuePrioritizedDescriptor exposes a ControlPoint as a non-commandable&#xa; Bacnet Binary Value Object.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetBinaryWritableDescriptor"><description>BBacnetBinaryWritableDescriptor exposes a ControlPoint as a&#xa; commandable binary point.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetBooleanScheduleDescriptor"><description>BBacnetBooleanScheduleDescriptor exposes a Niagara schedule to Bacnet.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetCalendarDescriptor"><description>BBacnetCalendarDescriptor is the extension that exposes Bacnet Calendar capability.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetCharacterStringDescriptor"><description>BBacnetCharacterStringDescriptor is the extension that exposes&#xa; Bacnet CharacterString capability.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetDynamicScheduleDescriptor"><description>BBacnetDynamicScheduleDescriptor represents an intermediate descriptor that&#xa; gets created during dynamic schedule object creation in bacnet when actual type&#xa; of the schedule to be created cannot be inferred using the initial values.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetEnumScheduleDescriptor"><description>BBacnetEnumScheduleDescriptor exposes a Niagara schedule to Bacnet.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetEventEnrollmentDescriptor"><description>BBacnetEventEnrollmentDescriptor exposes a Niagara event to Bacnet.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetEventSource"><description>BBacnetEventSource is the base class for all BACnet export descriptors&#xa; that represent event-initiating BACnet objects.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetFileDescriptor"><description>BBacnetFileDescriptor exposes a BIFile to Bacnet as a File object.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetIntegerValueDescriptor"><description>BBacnetIntegerValueDescriptor exposes a ControlPoint as a Bacnet&#xa; Integer Value Descriptor.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetIntegerValuePrioritizedDescriptor"><description>BBacnetLargeAnalogValueDescriptor exposes a ControlPoint as a writable (non-commandable) Bacnet&#xa; Analog Value Object.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetLargeAnalogValueDescriptor"><description>BBacnetLargeAnalogValueDescriptor exposes a ControlPoint as a Bacnet&#xa; Large Analog Value Object.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetLargeAnalogValuePrioritizedDescriptor"><description>BBacnetLargeAnalogValueDescriptor exposes a ControlPoint as a writable (non-commandable) Bacnet&#xa; Analog Value Object.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetLoopDescriptor"><description>BBacnetLoopDescriptor is the extension that allows a kitControl&#xa; BLoopPoint to be exposed to Bacnet.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetMultiStateInputDescriptor"><description>BBacnetMultiStateInputDescriptor exposes a ControlPoint as a Bacnet&#xa; MultiState Input Object.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetMultiStateOutputDescriptor"><description>BBacnetMultiStateOutputDescriptor exposes a ControlPoint as a Bacnet&#xa; Multi State Output Object.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetMultiStatePointDescriptor"><description>BBacnetMultiStatePointDescriptor is the superclass for multi-state&#xa; point extensions exposing MultiStatePoints to Bacnet.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetMultiStateValueDescriptor"><description>BBacnetMultiStateValueDescriptor exposes a ControlPoint as a Bacnet&#xa; MultiState Value Object.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetMultiStateValuePrioritizedDescriptor"><description>BBacnetMultiStateValuePrioritizedDescriptor exposes a ControlPoint as a non-commandable Bacnet&#xa; Multi State Value Object.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetMultiStateWritableDescriptor"><description>BBacnetMultiStateWritableDescriptor exposes a ControlPoint as a&#xa; commandable MultiState point.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetNiagaraHistoryDescriptor"><description>BBacnetNiagaraHistoryDescriptor is the archive component which exposes&#xa; a Niagara history to Bacnet as a trend log.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetNotificationClassDescriptor"><description>BBacnetNotificationClassDescriptor is the extension that allows a BAlarmClass&#xa; to be exposed to Bacnet as a Notification_Class object.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetNumericScheduleDescriptor"><description>BBacnetNumericScheduleDescriptor exposes a Niagara schedule to Bacnet.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetPointDescriptor"><description>BBacnetPointDescriptor is the extension that allows a normal Baja&#xa; ControlPoint to be exposed to Bacnet.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetPositiveIntegerValueDescriptor"><description>BBacnetPositiveIntegerValueDescriptor exposes a ControlPoint as a Bacnet&#xa; Positive Integer Value Descriptor.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetPositiveIntegerValuePrioritizedDescriptor"><description>BBacnetPositiveIntegerValuePrioritizedDescriptor exposes a ControlPoint as a Bacnet&#xa; Positive Integer Value Prioritized Descriptor.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetScheduleDescriptor"><description>BBacnetScheduleDescriptor exposes a Niagara schedule to Bacnet.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetStringScheduleDescriptor"><description>BBacnetStringScheduleDescriptor exposes a Niagara schedule to Bacnet.</description></class>
<class packageName="javax.baja.bacnet.export" name="BBacnetTrendLogDescriptor"><description>BBacnetTrendLogDescriptor exports a Bacnet trend log extension to Bacnet.</description></class>
<class packageName="javax.baja.bacnet.export" name="BIBacnetExportObject.ObjectSubscriber"/>
<class packageName="javax.baja.bacnet.export" name="BLocalBacnetDevice"><description>BLocalBacnetDevice is the representation of Niagara as a Bacnet&#xa; device on the Bacnet internetwork.</description></class>
<class packageName="javax.baja.bacnet.export" name="BOutOfServiceExt"/>
<class packageName="javax.baja.bacnet.export" name="BReliabilityAlarmSourceExt"><description>BReliabilityAlarmSourceExt defines the intrinsic alarming/notification&#xa; for the change of reliability property.</description></class>
<class packageName="javax.baja.bacnet.export" name="Cov"><description>BacnetCovSubscriber handles sending a Cov notification to any&#xa; Cov subscribers when the point&#x27;s value changes.</description></class>
<class packageName="javax.baja.bacnet.export" name="BacnetPropertyListProvider" category="interface"/>
<class packageName="javax.baja.bacnet.export" name="BacnetWritableDescriptor" category="interface"><description>Marker interface so the UI can tell if a descriptor can be written from BACnet.</description></class>
<class packageName="javax.baja.bacnet.export" name="BIBacnetCovSource" category="interface"><description>BIBacnetCovSource is the interface implemented by all export&#xa; descriptors that support object-level COV subscription.</description></class>
<class packageName="javax.baja.bacnet.export" name="BIBacnetExportObject" category="interface"><description>BIBacnetExportObject is the interface implemented by all objects&#xa; that export Niagara objects as Bacnet objects.</description></class>
</package>
</bajadoc>
