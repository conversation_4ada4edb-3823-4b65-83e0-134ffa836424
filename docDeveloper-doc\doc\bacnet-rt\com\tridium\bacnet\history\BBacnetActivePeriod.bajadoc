<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.history.BBacnetActivePeriod" name="BBacnetActivePeriod" packageName="com.tridium.bacnet.history" public="true">
<description>
BBacnetActivePeriod defines when trend log&#xa; collection is active based on absolute date/time range.&#xa; &lt;p&gt;&#xa; Note that for BACnet, Start_Time and Stop_Time may not contain any wildcards,&#xa; or the conditions for enabling logging based on this active period are ignored.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 1$ $Date: 8/25/2003 11:04:55 AM$</tag>
<tag name="@creation">25 Aug 03</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.history.ext.BActivePeriod"/>
</extends>
<property name="startTime" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
<description>
Slot for the &lt;code&gt;startTime&lt;/code&gt; property.&#xa; The absolute start date/time on which to start collecting.
</description>
<tag name="@see">#getStartTime</tag>
<tag name="@see">#setStartTime</tag>
</property>

<property name="stopTime" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
<description>
Slot for the &lt;code&gt;stopTime&lt;/code&gt; property.&#xa; The absolute stop date/time on which to stop collecting.
</description>
<tag name="@see">#getStopTime</tag>
<tag name="@see">#setStopTime</tag>
</property>

</class>
</bajadoc>
