<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="javax.baja.bacnet.io">
<description/>
<class packageName="javax.baja.bacnet.io" name="BBacnetComm"><description>BBacnetComm is the object that exposes the communications&#xa; stack.</description></class>
<class packageName="javax.baja.bacnet.io" name="AsnInput" category="interface"><description>This interface specifies the methods for decoding Niagara&#xa; quantities from BACnet ASN.1 primitives.</description></class>
<class packageName="javax.baja.bacnet.io" name="AsnOutput" category="interface"><description>This interface specifies the methods for encoding Niagara&#xa; quantities into BACnet ASN.1 primitives.</description></class>
<class packageName="javax.baja.bacnet.io" name="BacnetServiceListener" category="interface"><description>BacnetServiceListener is the root interface for all Listener&#xa; interfaces that listen for BACnet service requests.</description></class>
<class packageName="javax.baja.bacnet.io" name="ChangeListError" category="interface"><description>The ChangeListError sequence is returned in response to&#xa; AddListElement and RemoveListElement requests when the&#xa; request cannot be executed.</description></class>
<class packageName="javax.baja.bacnet.io" name="ErrorType" category="interface"><description>ErrorType is an interface representing the Bacnet Error sequence.</description></class>
<class packageName="javax.baja.bacnet.io" name="EventNotificationListener" category="interface"><description>EventNotificationListener is the interface that classes must implement&#xa; to be notified when BACnet event notifications are received by the Niagara&#xa; BACnet comm stack.</description></class>
<class packageName="javax.baja.bacnet.io" name="FileData" category="interface"><description>FileData contains information about data that is either read from&#xa; or written to a BACnet File object using the AtomicReadFile or&#xa; AtomicWriteFile service requests.</description></class>
<class packageName="javax.baja.bacnet.io" name="PrivateTransferListener" category="interface"><description>PrivateTransferListener is the interface objects use to identify that they&#xa; need to be informed of incoming PrivateTransfer requests.</description></class>
<class packageName="javax.baja.bacnet.io" name="PropertyReference" category="interface"><description>PropertyReference contains information to reference&#xa; a property to be read.</description></class>
<class packageName="javax.baja.bacnet.io" name="PropertyValue" category="interface"><description>PropertyValue contains the results of reading a property value,&#xa; or the value to be written to a property.</description></class>
<class packageName="javax.baja.bacnet.io" name="RangeData" category="interface"><description>RangeReference contains information to reference&#xa; a range of values in a compound property.</description></class>
<class packageName="javax.baja.bacnet.io" name="RangeReference" category="interface"><description>RangeReference contains information to request&#xa; a range of values in a compound property.</description></class>
<class packageName="javax.baja.bacnet.io" name="AbortException" category="exception"><description>AbortExceptions are thrown when an error is encountered that&#xa; should result in a transaction being aborted.</description></class>
<class packageName="javax.baja.bacnet.io" name="AsnDataTypeNotSupportedException" category="exception"><description>A AsnDataTypeNotSupportedException is thrown whenever an&#xa; a provided data type is not in the set of supported data types&#xa; for a selected field.</description></class>
<class packageName="javax.baja.bacnet.io" name="AsnException" category="exception"><description>An AsnException is thrown whenever a error is&#xa; detected in encoding or decoding an Asn production.</description></class>
<class packageName="javax.baja.bacnet.io" name="DataTypeNotSupportedException" category="exception"><description>A DataTypeNotSupportedException is thrown whenever an&#xa; a provided data type is not in the set of supported data types&#xa; for a selected field.</description></class>
<class packageName="javax.baja.bacnet.io" name="ErrorException" category="exception"><description>ErrorExceptions are thrown when an error is encountered during&#xa; a Bacnet transaction.</description></class>
<class packageName="javax.baja.bacnet.io" name="IllegalActionInitiationError" category="exception"/>
<class packageName="javax.baja.bacnet.io" name="OutOfRangeException" category="exception"><description>An OutOfRangeException is thrown whenever an&#xa; out of range error is detected in encoding or&#xa; decoding an Asn production.</description></class>
<class packageName="javax.baja.bacnet.io" name="RejectException" category="exception"><description>RejectExceptions are thrown when an error is encountered that&#xa; should result in a transaction being rejected.</description></class>
</package>
</bajadoc>
