<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="app" runtimeProfile="rt" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>Niagara Applications</description>
<package name="javax.baja.app"/>
<package name="javax.baja.web.app"/>
<package name="javax.baja.web.app.mobile"/>
<class packageName="javax.baja.app" name="BApp"><description>Niagara Application</description></class>
<class packageName="javax.baja.app" name="BAppContainer"><description>Niagara Application Container</description></class>
<class packageName="javax.baja.app" name="BAppFolder"><description>Niagara Application Folder</description></class>
<class packageName="javax.baja.web.app" name="BBajaScriptWebApp"><description>BajaScript Web App</description></class>
<class packageName="javax.baja.app" name="BIAppComponent" category="interface"><description>Interface for all App related Components</description></class>
<class packageName="javax.baja.app" name="BIAppFolder" category="interface"><description>Interface for all App Folders</description></class>
<class packageName="javax.baja.web.app" name="BIBajaScriptWebApp" category="interface"><description>Interface for a BajaScript Web App</description></class>
<class packageName="javax.baja.web.app.mobile" name="BIMobileWebApp" category="interface"><description>Interface for a Mobile Web App</description></class>
<class packageName="javax.baja.web.app" name="BIWebApp" category="interface"><description>Interface for a Web App</description></class>
<class packageName="javax.baja.web.app" name="BWebApp"><description>Niagara Web App</description></class>
<class packageName="javax.baja.web.app.mobile" name="MobileWebAppUtil"><description>Utility methods for Mobile Web Apps</description></class>
</module>
</bajadoc>
