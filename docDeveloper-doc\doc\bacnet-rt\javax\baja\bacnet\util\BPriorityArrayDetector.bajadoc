<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.util.BPriorityArrayDetector" name="BPriorityArrayDetector" packageName="javax.baja.bacnet.util" public="true">
<description>
The PriorityArrayDetector enables stations engineered offline&#xa;  to properly detect the presence or absence of a Priority Array once&#xa;  the station has been installed.&#xa;  &lt;p&gt;&#xa;  The PriorityArrayDetector component can be used to detect or&#xa;  redetect the presence or absence of a PriorityArray in a&#xa;  BacnetProxyExt.&#xa;  &lt;p&gt;&#xa;  To use the component, add a BPriorityArrayDetector to the&#xa;  location in the nav tree that needs to be detected.&#xa;  &lt;p&gt;&#xa;  For example, to rescan the entire network add the&#xa;  detector as a child component of the network.&#xa;  &lt;p&gt;&#xa;  To detect priority arrays in just a single device,&#xa;  add the detector as a child of the desired device.
</description>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<action name="detect" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;detect&lt;/code&gt; action.
</description>
<tag name="@see">#detect()</tag>
</action>

<!-- javax.baja.bacnet.util.BPriorityArrayDetector() -->
<constructor name="BPriorityArrayDetector" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.util.BPriorityArrayDetector.detect() -->
<method name="detect"  public="true">
<description>
Invoke the &lt;code&gt;detect&lt;/code&gt; action.
</description>
<tag name="@see">#detect</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BPriorityArrayDetector.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BPriorityArrayDetector.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BPriorityArrayDetector.doDetect() -->
<method name="doDetect"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BPriorityArrayDetector.detectPriorityArray(javax.baja.sys.BComponent) -->
<method name="detectPriorityArray"  public="true">
<description/>
<parameter name="component">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BPriorityArrayDetector.getIcon() -->
<method name="getIcon"  public="true">
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.bacnet.util.BPriorityArrayDetector.detect -->
<field name="detect"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;detect&lt;/code&gt; action.
</description>
<tag name="@see">#detect()</tag>
</field>

<!-- javax.baja.bacnet.util.BPriorityArrayDetector.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
