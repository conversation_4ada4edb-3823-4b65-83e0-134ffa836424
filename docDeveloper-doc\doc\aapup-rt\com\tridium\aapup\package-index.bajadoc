<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="aapup" runtimeProfile="rt" name="com.tridium.aapup">
<description/>
<class packageName="com.tridium.aapup" name="BPupDevice"><description>Models a PUP device</description></class>
<class packageName="com.tridium.aapup" name="BPupDeviceFolder"><description>AaPup implementation of BDeviceFolder&#xa; Is a container for instances of BPupDevice</description></class>
<class packageName="com.tridium.aapup" name="BPupNetwork"><description>BAaPupNetwork - represents an PUP Serial Network.</description></class>
<class packageName="com.tridium.aapup" name="BPupPeerListFolder"><description>BPupPeerListFolder is the standard container to use&#xa; under BPupNetwork to organize BPupDeviceRecords.</description></class>
<class packageName="com.tridium.aapup" name="BPupRegionsFolder"><description>BPupRegionsFolder is the standard container to use&#xa; under BPupDevice to organize BPupRegionRecords.</description></class>
</package>
</bajadoc>
