<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="baja" runtimeProfile="rt" name="javax.baja.naming">
<description>
&lt;p&gt;Classes used for object naming and resolution.&lt;/p&gt;
</description>
<class packageName="javax.baja.naming" name="BasicQuery"><description>BasicQuery is a simple implementation of OrdQuery &#xa; that stores the scheme and body as fields.</description></class>
<class packageName="javax.baja.naming" name="BatchResolve"><description>BatchResolve is used to resolve a list of ords together.</description></class>
<class packageName="javax.baja.naming" name="BHost"><description>BHost is the navigation node used to organize navigation &#xa; by hosts.</description></class>
<class packageName="javax.baja.naming" name="BIpHost"><description>BIpHost is used to represent a host machine that is identified&#xa; with an IP address.</description></class>
<class packageName="javax.baja.naming" name="BIpScheme"><description>BIpScheme is used to identify BIpHosts as &#x22;&lt;code&gt;ip:&amp;lt;hostname&amp;gt;&lt;/code&gt;&#x22;.</description></class>
<class packageName="javax.baja.naming" name="BLocalHost"><description>BLocalHost is the BHost for the local machine.</description></class>
<class packageName="javax.baja.naming" name="BLocalScheme"><description>BLocalScheme is the ord scheme represented as &#x22;local:&#x22;.</description></class>
<class packageName="javax.baja.naming" name="BModuleScheme"><description>BModuleScheme is used to resolve modules and their contents&#xa; using a ModuleQuery.</description></class>
<class packageName="javax.baja.naming" name="BOrd"><description>BOrd is an &#x22;Object Resolution Descriptor&#x22;.</description></class>
<class packageName="javax.baja.naming" name="BOrdList"><description>BOrdList stores zero or more BOrds.</description></class>
<class packageName="javax.baja.naming" name="BOrdScheme"><description>BOrdSchemes provide management of a registered ord scheme ID.</description></class>
<class packageName="javax.baja.naming" name="BResolveScheme"/>
<class packageName="javax.baja.naming" name="BServiceScheme"><description>BServiceScheme is used to lookup a service by &#xa; type &#x22;service:baja:UserService&#x22;.</description></class>
<class packageName="javax.baja.naming" name="BSession"><description>BSession</description></class>
<class packageName="javax.baja.naming" name="BSlotScheme"><description>BSlotScheme is used to navigate the slot tree.</description></class>
<class packageName="javax.baja.naming" name="BStationSessionScheme"/>
<class packageName="javax.baja.naming" name="BTypeScheme"><description>An ORD scheme that resolves a type spec (moduleName:typeName)&#xa; to a &lt;code&gt;<see ref="javax.baja.util.BTypeSpec">javax.baja.util.BTypeSpec</see>&lt;/code&gt;.</description></class>
<class packageName="javax.baja.naming" name="BViewScheme"><description>BViewScheme is a passive scheme that annotates an existing&#xa; ord with a view typespec.</description></class>
<class packageName="javax.baja.naming" name="OrdQueryList"><description>OrdQueryList is used to manipulate a list of OrdQueries.</description></class>
<class packageName="javax.baja.naming" name="OrdTarget"><description>OrdTarget is the result of resolving a BOrd.</description></class>
<class packageName="javax.baja.naming" name="OrdUtil"/>
<class packageName="javax.baja.naming" name="SlotPath"><description>SlotPath is a ord scheme for resolving BValues&#xa; using slot names.</description></class>
<class packageName="javax.baja.naming" name="TagPath"><description>TagPath is a tag scheme for resolving tag values.</description></class>
<class packageName="javax.baja.naming" name="ViewQuery"><description>ViewQuery defines user agent information.</description></class>
<class packageName="javax.baja.naming" name="BIAlias" category="interface"/>
<class packageName="javax.baja.naming" name="BISession" category="interface"><description>BISession is implemented by BObjects which &#xa; represent a session within a host.</description></class>
<class packageName="javax.baja.naming" name="BServiceScheme.ServiceSession" category="interface"><description>This interface is implemented by BISessions which &#xa; can lookup a service by type.</description></class>
<class packageName="javax.baja.naming" name="OrdQuery" category="interface"><description>OrdQuery encapsulates a single query within a BOrd.</description></class>
<class packageName="javax.baja.naming" name="Path" category="interface"><description>Path is the interface implemented by OrdQueries that contain a path in a hierarchical naming&#xa; system.</description></class>
<class packageName="javax.baja.naming" name="Queryable" category="interface"><description>A Queryable object knows how to provide a cursor for iterating&#xa; through objects that satisfy a query.</description></class>
<class packageName="javax.baja.naming" name="RemoteQueryable" category="interface"><description>A RemoteQueryable object is an object that can take a BQL ord,&#xa; process the query remotely and return the result.</description></class>
<class packageName="javax.baja.naming" name="InvalidOrdBaseException" category="exception"><description>InvalidOrdBaseException is thrown when the base for&#xa; a query is not valid for the query scheme.</description></class>
<class packageName="javax.baja.naming" name="InvalidRootSchemeException" category="exception"><description>InvalidRootSchemeException is thrown when a non-root scheme&#xa; is used as the first scheme in an ord.</description></class>
<class packageName="javax.baja.naming" name="NullOrdException" category="exception"><description>NullOrdException is thrown when attempting a BOrd&#xa; operation on the null ord.</description></class>
<class packageName="javax.baja.naming" name="SyntaxException" category="exception"><description>SyntaxException indicates an invalid ord or query syntax.</description></class>
<class packageName="javax.baja.naming" name="UnknownSchemeException" category="exception"><description>UnknownSchemeException is thrown when an unregistered&#xa; scheme is encountered in parsing a BOrd.</description></class>
<class packageName="javax.baja.naming" name="UnresolvedException" category="exception"><description>The UnresolvedException is thrown when attempting &#xa; to resolve the source object of a BOrd.</description></class>
</package>
</bajadoc>
