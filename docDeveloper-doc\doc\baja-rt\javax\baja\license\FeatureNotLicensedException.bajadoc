<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.license.FeatureNotLicensedException" name="FeatureNotLicensedException" packageName="javax.baja.license" public="true" category="exception">
<description>
FeatureNotLicensedException indicates an attempt to use&#xa; a feature which not currently licensed for this machine.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">3 Nov 01</tag>
<tag name="@version">$Revision: 2$ $Date: 3/28/05 9:22:59 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.license.LicenseException"/>
</extends>
<!-- javax.baja.license.FeatureNotLicensedException(java.lang.String) -->
<constructor name="FeatureNotLicensedException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<description>
Construct a FeatureNotLicensedException with the given message.
</description>
</constructor>

<!-- javax.baja.license.FeatureNotLicensedException() -->
<constructor name="FeatureNotLicensedException" public="true">
<description>
Construct a FeatureNotLicensedException.
</description>
</constructor>

</class>
</bajadoc>
