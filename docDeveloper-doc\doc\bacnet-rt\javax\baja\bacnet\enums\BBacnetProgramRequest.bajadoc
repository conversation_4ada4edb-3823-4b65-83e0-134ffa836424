<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetProgramRequest" name="BBacnetProgramRequest" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetProgramRequest represents the BACnetProgramRequest&#xa; enumeration.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">17 Oct 2005</tag>
<tag name="@since">Niagara 3.1</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;ready&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;load&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;run&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;halt&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;restart&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;unload&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetProgramRequest.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetProgramRequest"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetProgramRequest.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetProgramRequest"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetProgramRequest.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetProgramRequest.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetProgramRequest.READY -->
<field name="READY"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for ready.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramRequest.LOAD -->
<field name="LOAD"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for load.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramRequest.RUN -->
<field name="RUN"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for run.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramRequest.HALT -->
<field name="HALT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for halt.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramRequest.RESTART -->
<field name="RESTART"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for restart.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramRequest.UNLOAD -->
<field name="UNLOAD"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for unload.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramRequest.ready -->
<field name="ready"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetProgramRequest"/>
<description>
BBacnetProgramRequest constant for ready.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramRequest.load -->
<field name="load"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetProgramRequest"/>
<description>
BBacnetProgramRequest constant for load.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramRequest.run -->
<field name="run"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetProgramRequest"/>
<description>
BBacnetProgramRequest constant for run.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramRequest.halt -->
<field name="halt"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetProgramRequest"/>
<description>
BBacnetProgramRequest constant for halt.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramRequest.restart -->
<field name="restart"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetProgramRequest"/>
<description>
BBacnetProgramRequest constant for restart.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramRequest.unload -->
<field name="unload"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetProgramRequest"/>
<description>
BBacnetProgramRequest constant for unload.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramRequest.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetProgramRequest"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetProgramRequest.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
