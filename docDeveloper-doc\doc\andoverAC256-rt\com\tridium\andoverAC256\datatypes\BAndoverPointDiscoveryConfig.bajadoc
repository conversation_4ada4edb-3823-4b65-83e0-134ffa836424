<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.datatypes.BAndoverPointDiscoveryConfig" name="BAndoverPointDiscoveryConfig" packageName="com.tridium.andoverAC256.datatypes" public="true">
<description>
AndoverAC256 Device Discovery Config, used to govern what&#xa; tables or sub-panel are learned during the learn process
</description>
<tag name="@author">C<PERSON><PERSON></tag>
<tag name="@creation">4/26/2005 11:15AM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.76</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="svPoints" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;svPoints&lt;/code&gt; property.
</description>
<tag name="@see">#getSvPoints</tag>
<tag name="@see">#setSvPoints</tag>
</property>

<property name="saPoints" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;saPoints&lt;/code&gt; property.
</description>
<tag name="@see">#getSaPoints</tag>
<tag name="@see">#setSaPoints</tag>
</property>

<property name="sxPoints" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;sxPoints&lt;/code&gt; property.
</description>
<tag name="@see">#getSxPoints</tag>
<tag name="@see">#setSxPoints</tag>
</property>

<property name="userFlagPoints" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;userFlagPoints&lt;/code&gt; property.
</description>
<tag name="@see">#getUserFlagPoints</tag>
<tag name="@see">#setUserFlagPoints</tag>
</property>

<property name="systemFlagPoints" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;systemFlagPoints&lt;/code&gt; property.
</description>
<tag name="@see">#getSystemFlagPoints</tag>
<tag name="@see">#setSystemFlagPoints</tag>
</property>

<property name="iou" flags="">
<type class="com.tridium.andoverAC256.datatypes.BAndoverIouPointDiscoveryConfig"/>
<description>
Slot for the &lt;code&gt;iou&lt;/code&gt; property.
</description>
<tag name="@see">#getIou</tag>
<tag name="@see">#setIou</tag>
</property>

<property name="lcu" flags="">
<type class="com.tridium.andoverAC256.datatypes.BAndoverLcuPointDiscoveryConfig"/>
<description>
Slot for the &lt;code&gt;lcu&lt;/code&gt; property.
</description>
<tag name="@see">#getLcu</tag>
<tag name="@see">#setLcu</tag>
</property>

</class>
</bajadoc>
