<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.collection.BITable" name="BITable" packageName="javax.baja.collection" public="true" interface="true" abstract="true" category="interface">
<description>
BITable is a collection members are accessible by&#xa; row and whose members&#x27; elements are accessible by column.
</description>
<tag name="@author">&lt;a href=&#x22;mailto:<EMAIL>&#x22;&gt;<PERSON>&lt;/a&gt;</tag>
<tag name="@author">&lt;a href=&#x22;mailto:<EMAIL>&#x22;&gt;<PERSON>lt;/a&gt;</tag>
<implements>
<parameterizedType class="javax.baja.sys.Cursorable">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</implements>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<typeParameters>
<typeVariable class="T">
<bounds>
<type class="javax.baja.sys.BIObject"/>
</bounds>
</typeVariable>
</typeParameters>
<!-- javax.baja.collection.BITable.cursor() -->
<method name="cursor"  public="true" abstract="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get a table cursor for iterating the rows of the table (covariant return).
</description>
<return>
<parameterizedType class="javax.baja.collection.TableCursor">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</return>
</method>

<!-- javax.baja.collection.BITable.getColumns() -->
<method name="getColumns"  public="true" abstract="true">
<description>
Get the list of columns in the table.
</description>
<return>
<type class="javax.baja.collection.ColumnList"/>
</return>
</method>

<!-- javax.baja.collection.BITable.getTableFacets() -->
<method name="getTableFacets"  public="true" abstract="true">
<description>
Get the facets that apply to the entire table.
</description>
<return>
<type class="javax.baja.sys.BFacets"/>
<description>
Returns the table facets or BFacets.NULL if&#xa;   if no facets are defined for the table.
</description>
</return>
</method>

<!-- javax.baja.collection.BITable.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
