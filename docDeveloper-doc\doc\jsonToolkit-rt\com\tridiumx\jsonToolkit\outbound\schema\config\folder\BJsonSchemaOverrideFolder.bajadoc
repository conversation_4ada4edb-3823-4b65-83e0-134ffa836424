<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaOverrideFolder" name="BJsonSchemaOverrideFolder" packageName="com.tridiumx.jsonToolkit.outbound.schema.config.folder" public="true">
<description>
Contains json type overrides
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.util.BFolder"/>
</extends>
<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaOverrideFolder() -->
<constructor name="BJsonSchemaOverrideFolder" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaOverrideFolder.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaOverrideFolder.processOverrides(javax.baja.sys.BValue) -->
<method name="processOverrides"  public="true">
<description>
This looks for a BProgram beneath config/overrides which has a method with signature:&#xa;    public BValue onOverride(final BValue input)
</description>
<parameter name="value">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="javax.baja.sys.BValue"/>
<description>
any overridden value, or the value sent
</description>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaOverrideFolder.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaOverrideFolder.isChildLegal(javax.baja.sys.BComponent) -->
<method name="isChildLegal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="child">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaOverrideFolder.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaOverrideFolder.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.folder.BJsonSchemaOverrideFolder.ovLog -->
<field name="ovLog"  public="true" static="true" final="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

</class>
</bajadoc>
