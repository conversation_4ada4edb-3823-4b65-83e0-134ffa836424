<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="baja" runtimeProfile="rt" name="javax.baja.log">
<description>
This package contains the standard Baja logging APIs.
</description>
<class packageName="javax.baja.log" name="Console<PERSON>ogHandler"><description>DefaultLogHandler provides a simple implementation&#xa; of the LogHandler to dump log messages to standard&#xa; output.</description></class>
<class packageName="javax.baja.log" name="Log"><description>Log is used to log system events.</description></class>
<class packageName="javax.baja.log" name="LogRecord"><description>LogRecord contains the information&#xa; associated with a logged message.</description></class>
<class packageName="javax.baja.log" name="LogHandler" category="interface"><description>LogHandler is implemented by objects which&#xa; wish to receive log callbacks.</description></class>
</package>
</bajadoc>
