<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.io.BIContextEncodable" name="BIContextEncodable" packageName="javax.baja.io" public="true" interface="true" abstract="true" category="interface">
<description>
BIEncodable is implemented by BObjects which can be&#xa; serialized and unserialized into both a binary and&#xa; String format, and can accept a context to their&#xa; encodeToString method
</description>
<tag name="@author"><PERSON>an</tag>
<tag name="@creation">03/01/2013</tag>
<implements>
<type class="javax.baja.io.BIEncodable"/>
</implements>
<!-- javax.baja.io.BIContextEncodable.encode(java.io.DataOutput, javax.baja.sys.Context) -->
<method name="encode"  public="true" abstract="true">
<description/>
<parameter name="out">
<type class="java.io.DataOutput"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.BIContextEncodable.decode(java.io.DataInput, javax.baja.sys.Context) -->
<method name="decode"  public="true" abstract="true">
<description/>
<parameter name="in">
<type class="java.io.DataInput"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.BIContextEncodable.encodeToString(javax.baja.sys.Context) -->
<method name="encodeToString"  public="true" abstract="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.BIContextEncodable.decodeFromString(java.lang.String, javax.baja.sys.Context) -->
<method name="decodeFromString"  public="true" abstract="true">
<description/>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.BIContextEncodable.encode(java.io.DataOutput) -->
<method name="encode"  public="true" default="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="out">
<type class="java.io.DataOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.BIContextEncodable.decode(java.io.DataInput) -->
<method name="decode"  public="true" default="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="in">
<type class="java.io.DataInput"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.BIContextEncodable.encodeToString() -->
<method name="encodeToString"  public="true" default="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.BIContextEncodable.decodeFromString(java.lang.String) -->
<method name="decodeFromString"  public="true" default="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.BIContextEncodable.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
