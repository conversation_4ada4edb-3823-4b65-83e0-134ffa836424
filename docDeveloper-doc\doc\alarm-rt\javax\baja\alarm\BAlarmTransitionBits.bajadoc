<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BAlarmTransitionBits" name="BAlarmTransitionBits" packageName="javax.baja.alarm" public="true" final="true">
<description>
The BAlarmTransitionBits object contains a bit for each&#xa; alarm state transition type defined within Baja:&#xa;    toOffnormal&#xa;    toFault&#xa;    toNormal
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">9 Nov 00</tag>
<tag name="@version">$Revision: 20$ $Date: 9/30/08 5:08:59 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BBitString"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
<!-- javax.baja.alarm.BAlarmTransitionBits.make(boolean) -->
<method name="make"  public="true" static="true">
<description>
Construct an instance in which all days are &#xa; set to the given value
</description>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.make(int) -->
<method name="make"  public="true" static="true">
<description>
Construct an instance using the given bits
</description>
<parameter name="bits">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.make(javax.baja.alarm.BAlarmTransitionBits, javax.baja.alarm.BAlarmTransitionBits, boolean) -->
<method name="make"  public="true" static="true">
<description>
Construct an instance by adding or subtracting bits from another.
</description>
<parameter name="old">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</parameter>
<parameter name="nBits">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</parameter>
<parameter name="set">
<type class="boolean"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.getBits() -->
<method name="getBits"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.includes(javax.baja.alarm.BSourceState) -->
<method name="includes"  public="true">
<description/>
<parameter name="state">
<type class="javax.baja.alarm.BSourceState"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.isToOffnormal() -->
<method name="isToOffnormal"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.isToFault() -->
<method name="isToFault"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.isToNormal() -->
<method name="isToNormal"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.isToAlert() -->
<method name="isToAlert"  public="true">
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.getBit(int) -->
<method name="getBit"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return if the bit specified by the given ordinal is set.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.getBit(java.lang.String) -->
<method name="getBit"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return if the bit specified by the given tag is set.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.getOrdinals() -->
<method name="getOrdinals"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get an array enumerating the list of all known&#xa; ordinal values of this bitstring instance.
</description>
<return>
<type class="int" dimension="1"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.isOrdinal(int) -->
<method name="isOrdinal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is the specified ordinal value included in this&#xa; bitstring&#x27;s range of valid ordinals.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.getTag(int) -->
<method name="getTag"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the tag identifier for an ordinal value.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.getDisplayTag(int, javax.baja.sys.Context) -->
<method name="getDisplayTag"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the user readable tag for an ordinal value.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.getInstance(int[]) -->
<method name="getInstance"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the BBitString instance which maps to the &#xa; specified set of ordinal values.
</description>
<parameter name="ordinals">
<type class="int" dimension="1"/>
</parameter>
<return>
<type class="javax.baja.sys.BBitString"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.isTag(java.lang.String) -->
<method name="isTag"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return true if the specified tag is contained by the range.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.tagToOrdinal(java.lang.String) -->
<method name="tagToOrdinal"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the ordinal associated with the specified tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.isEmpty() -->
<method name="isEmpty"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return if true if no bits set.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.getEmptyTag() -->
<method name="getEmptyTag"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
The empty tag is &#x22;none&#x22;.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.isNull() -->
<method name="isNull"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.hashCode() -->
<method name="hashCode"  public="true">
<description>
BAlarmTransitionBits hash code.
</description>
<tag name="@since">Niagara 3.4</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description>
Equality is based on direct object reference
</description>
<parameter name="obj">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
To string.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.encode(java.io.DataOutput) -->
<method name="encode"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
BDaysOfWeekBits is serialized using writeInt
</description>
<parameter name="out">
<type class="java.io.DataOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.decode(java.io.DataInput) -->
<method name="decode"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
BDaysOfWeekBits is unserialized using readInt()
</description>
<parameter name="in">
<type class="java.io.DataInput"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.encodeToString() -->
<method name="encodeToString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Text format is the bit mask in hex.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.decodeFromString(java.lang.String) -->
<method name="decodeFromString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Read the bit mask as hex.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmTransitionBits.TO_OFFNORMAL -->
<field name="TO_OFFNORMAL"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmTransitionBits.TO_FAULT -->
<field name="TO_FAULT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmTransitionBits.TO_NORMAL -->
<field name="TO_NORMAL"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmTransitionBits.TO_ALERT -->
<field name="TO_ALERT"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmTransitionBits.toOffnormal -->
<field name="toOffnormal"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmTransitionBits.toFault -->
<field name="toFault"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmTransitionBits.toNormal -->
<field name="toNormal"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmTransitionBits.toAlert -->
<field name="toAlert"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmTransitionBits.SHOW_OFF_NORMAL -->
<field name="SHOW_OFF_NORMAL"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmTransitionBits.SHOW_NORMAL -->
<field name="SHOW_NORMAL"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmTransitionBits.SHOW_FAULT -->
<field name="SHOW_FAULT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmTransitionBits.SHOW_ALERT -->
<field name="SHOW_ALERT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.alarm.BAlarmTransitionBits.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
<description>
DEFAULT has all bits set
</description>
</field>

<!-- javax.baja.alarm.BAlarmTransitionBits.EMPTY -->
<field name="EMPTY"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
<description>
EMPTY has all bits clear
</description>
</field>

<!-- javax.baja.alarm.BAlarmTransitionBits.ALL -->
<field name="ALL"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BAlarmTransitionBits"/>
<description>
ALL is DEFAULT
</description>
</field>

<!-- javax.baja.alarm.BAlarmTransitionBits.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
