<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.inbound.handler">
<description/>
<class packageName="com.tridiumx.jsonToolkit.inbound.handler" name="BAlarmUuidAckHandler"><description>BAlarmUuidAckHandler tries to acknowledge alarms in the stations alarmDb matching the&#xa; uuid(s) provided&#xa;&#xa; {user: &#x22;shaun&#x22;,alarms: [ &#x22;5cf9c8b2-1542-42ba-a1fd-5f753c777bc0&#x22; ]}&#xa;&#xa; **result** This topic report the results of alarm ack to allow logging or post process activity.</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.handler" name="BJsonHandler"><description>Handlers perform a specific task with the data routed/selected via the other inbound components.</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.handler" name="BJsonSetPointHandler"><description>Routes incoming setpoint values to control writable points.</description></class>
<class packageName="com.tridiumx.jsonToolkit.inbound.handler" name="SetpointValueRejectedException" category="exception"><description>Provides a basic means to test and report on writes from the platform which would exceed any&#xa; min/max facets present on the target point</description></class>
</package>
</bajadoc>
