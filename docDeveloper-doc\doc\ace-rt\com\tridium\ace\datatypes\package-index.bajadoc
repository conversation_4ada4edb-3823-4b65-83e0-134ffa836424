<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="ace" runtimeProfile="rt" name="com.tridium.ace.datatypes">
<description/>
<class packageName="com.tridium.ace.datatypes" name="BAceDownloadParams"><description>BAceDownloadParams - is container for properties passed in download application action</description></class>
<class packageName="com.tridium.ace.datatypes" name="BAcePrimitive"><description>AcePrimitive</description></class>
<class packageName="com.tridium.ace.datatypes" name="BAceReadFileParams"><description>BAceReadFileParams - is container for properties passed in read file action</description></class>
<class packageName="com.tridium.ace.datatypes" name="BAceReadParams"><description>BAceReadParams - is container arguments needed to read an ACE component property</description></class>
<class packageName="com.tridium.ace.datatypes" name="BAceStatus"><description>BAceStatus is implementation of &lt;code&gt;BIAceInteger&lt;/code&gt; for ACE component properties&#xa; which represent &lt;code&gt;BStatus&lt;/code&gt;.</description></class>
<class packageName="com.tridium.ace.datatypes" name="BAceWriteFileParams"><description>BAceWriteFileParams - is container for properties past in download file action</description></class>
<class packageName="com.tridium.ace.datatypes" name="BExeParam"><description>BExeParam is implementation of &lt;code&gt;BIAceInteger&lt;/code&gt; for ACE component&#xa; exeParam property.</description></class>
<class packageName="com.tridium.ace.datatypes" name="BWriteWorker"><description>BWriteWorker</description></class>
<class packageName="com.tridium.ace.datatypes" name="BIAceInteger" category="interface"><description>BIAceInteger is interface for classes which provide special behavior&#xa; in Niagara environment and are implemented in the ACE environment as a&#xa; numeric type that can be contained in a int.</description></class>
</package>
</bajadoc>
