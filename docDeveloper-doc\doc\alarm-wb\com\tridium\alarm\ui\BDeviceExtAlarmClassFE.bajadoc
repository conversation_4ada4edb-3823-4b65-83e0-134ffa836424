<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="wb" qualifiedName="com.tridium.alarm.ui.BDeviceExtAlarmClassFE" name="BDeviceExtAlarmClassFE" packageName="com.tridium.alarm.ui" public="true">
<description/>
<tag name="@author"><PERSON></tag>
<tag name="@creation">02 Dec 03</tag>
<tag name="@version">$Revision: 8$ $Date: 5/21/10 12:32:58 PM EDT$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="javax.baja.workbench.fieldeditor.BWbFieldEditor"/>
</extends>
<action name="textOpChanged" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;textOpChanged&lt;/code&gt; action.
</description>
<tag name="@see">#textOpChanged()</tag>
</action>

</class>
</bajadoc>
