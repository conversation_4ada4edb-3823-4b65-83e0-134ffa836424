<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="jsonToolkit" runtimeProfile="rt" name="com.tridiumx.jsonToolkit.outbound.schema.object">
<description/>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.object" name="BJsonSchemaBoundObject"><description>A json object which is bound by an ord to a target station component/slot and whose internal key value pairs&#xa; are the slots inside that target.</description></class>
<class packageName="com.tridiumx.jsonToolkit.outbound.schema.object" name="BJsonSchemaObject"><description>A named object container for other &lt;code&gt;<see ref="com.tridiumx.jsonToolkit.outbound.schema.BJsonSchemaMember">BJsonSchemaMember</see>&lt;/code&gt; children.</description></class>
</package>
</bajadoc>
