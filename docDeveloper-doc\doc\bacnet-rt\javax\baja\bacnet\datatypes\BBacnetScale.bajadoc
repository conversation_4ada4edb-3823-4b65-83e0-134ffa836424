<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetScale" name="BBacnetScale" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BBacnetScale represents the BACnetScale data type.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">09 Jan 2006</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="choice" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</property>

<property name="scale" flags="">
<type class="double"/>
<description>
Slot for the &lt;code&gt;scale&lt;/code&gt; property.
</description>
<tag name="@see">#getScale</tag>
<tag name="@see">#setScale</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetScale() -->
<constructor name="BBacnetScale" public="true">
<description>
Constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetScale(double) -->
<constructor name="BBacnetScale" public="true">
<parameter name="v">
<type class="double"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetScale(int) -->
<constructor name="BBacnetScale" public="true">
<parameter name="v">
<type class="int"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetScale.getChoice() -->
<method name="getChoice"  public="true">
<description>
Get the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetScale.setChoice(int) -->
<method name="setChoice"  public="true">
<description>
Set the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#choice</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetScale.getScale() -->
<method name="getScale"  public="true">
<description>
Get the &lt;code&gt;scale&lt;/code&gt; property.
</description>
<tag name="@see">#scale</tag>
<return>
<type class="double"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetScale.setScale(double) -->
<method name="setScale"  public="true">
<description>
Set the &lt;code&gt;scale&lt;/code&gt; property.
</description>
<tag name="@see">#scale</tag>
<parameter name="v">
<type class="double"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetScale.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetScale.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetScale.setScale(double, javax.baja.sys.Context) -->
<method name="setScale"  public="true">
<description>
Set the scale to an integer scale type.
</description>
<parameter name="v">
<type class="double"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetScale.setScale(int) -->
<method name="setScale"  public="true">
<description>
Set the scale to an integer scale type.
</description>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetScale.setScale(int, javax.baja.sys.Context) -->
<method name="setScale"  public="true">
<description>
Set the scale to an integer scale type.
</description>
<parameter name="v">
<type class="int"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetScale.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetScale.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetScale.choice -->
<field name="choice"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;choice&lt;/code&gt; property.
</description>
<tag name="@see">#getChoice</tag>
<tag name="@see">#setChoice</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetScale.scale -->
<field name="scale"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;scale&lt;/code&gt; property.
</description>
<tag name="@see">#getScale</tag>
<tag name="@see">#setScale</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetScale.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetScale.FLOAT_SCALE_TAG -->
<field name="FLOAT_SCALE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetScale.INTEGER_SCALE_TAG -->
<field name="INTEGER_SCALE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
