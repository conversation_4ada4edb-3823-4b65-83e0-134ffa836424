<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportSetpointHandler" name="BJsonExportSetpointHandler" packageName="com.tridiumx.jsonToolkit.inbound.handler.exportMarker" public="true">
<description>
Export Setpoint Handler allows an external json message to change the value&#xa; of a Control Point.&#xa;&#xa; This uses the id property of an Export Marker &lt;code&gt;<see ref="com.tridiumx.jsonToolkit.exportMarker.BJsonExportMarker">BJsonExportMarker</see>&lt;/code&gt;&#xa; to locate the target.
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="com.tridiumx.jsonToolkit.inbound.handler.BJsonSetPointHandler"/>
</extends>
<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportSetpointHandler() -->
<constructor name="BJsonExportSetpointHandler" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportSetpointHandler.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportSetpointHandler.lookupTarget(javax.baja.sys.BString, java.lang.String) -->
<method name="lookupTarget"  protected="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="msg">
<type class="javax.baja.sys.BString"/>
</parameter>
<parameter name="id">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.control.BControlPoint"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.inbound.routing.RoutingFailedException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportSetpointHandler.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.exportMarker.BJsonExportSetpointHandler.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
