<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="andoverInfinity" runtimeProfile="rt" name="com.tridium.andoverInfinity.comm.req">
<description/>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityDeviceDiscoverRequest"><description>Device discover request for Infinity.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityDevicePingRequest"><description>Ping message for infinity.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityInitializeScreenRequest"><description>BInfinityInitializeScreenRequest sends a &#x22;\n&#x22; + CTL-Z to force&#xa; the infinity panel to resend all lines.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityKeystrokeRequest"><description>Used to send keystrokes from client to server side while in terminal session&#xa; in the Vt100 manager view.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityLogonSequenceRequest"><description>Logon is sent from BInfinityInitializeScreenRequest (&#x22;window&#x22;)&#xa; if the initialize screen request(CTL-Z) was unsuccessful.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityPointAutoRequest"><description>BInfinityPointAutoRequest is used to send an &#x22;Enable&#x22; command&#xa; to the field panel whenever a point returns to auto.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityPointDisableRequest"><description>BInfinityPointDisableRequest is used to send an &#x22;Disable&#x22; command&#xa; to the field panel whenever an action is invoked from the proxy ext.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityPointDiscoverRequest"><description>Point discover request for Infinity</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityPointEnableRequest"><description>BInfinityPointEnableRequest is used to send an &#x22;Enable&#x22; command&#xa; to the field panel whenever an action is invoked from the proxy ext.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityPollForControllerInfoRequest"><description>Navigates the View/Controllers and Edit/Controllers menus to retrieve info&#xa; about the device serial number, model, and unit number.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityReadPointRequest"><description>Message to read a points value</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityReloadInfinetRequest"><description>Message to put the infinity network device into reload mode</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityReloadLineRequest"><description>Used to send a single line from a reload file to the controller in reload mode</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinitySaveInfinetRequest"><description>Used to send message to put the Infinity panel into backup mode, and &#xa; send the backup bytes to the client side when done</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinitySetTimeRequest"><description>Used to navigate the Edit/System Date and Time menus to set the &#xa; controller time to the curren station date/time.</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="BInfinityWritePointRequest"><description>Used to send a write value to the controller panel</description></class>
<class packageName="com.tridium.andoverInfinity.comm.req" name="RequiresNetworkAccess" category="interface"><description>Most Infinity requests require access to the screen buffer to get current screen&#xa; mode.</description></class>
</package>
</bajadoc>
