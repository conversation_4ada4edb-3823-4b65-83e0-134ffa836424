<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.io.PropertyValue" name="PropertyValue" packageName="javax.baja.bacnet.io" public="true" interface="true" abstract="true" category="interface">
<description>
PropertyValue contains the results of reading a property value,&#xa; or the value to be written to a property.&#xa; &lt;p&gt;&#xa; The result can be either a byte array containing the Asn-encoded&#xa; value of the property, or a combination of a BBacnetErrorClass and&#xa; a BBacnetErrorCode containing the reason why a read was unable&#xa; to be performed.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">12 Mar 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<!-- javax.baja.bacnet.io.PropertyValue.getPropertyId() -->
<method name="getPropertyId"  public="true" abstract="true">
<description>
Get the propertyId.
</description>
<return>
<type class="int"/>
<description>
the propertyID.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.PropertyValue.getPropertyArrayIndex() -->
<method name="getPropertyArrayIndex"  public="true" abstract="true">
<description>
Get the property array index.
</description>
<return>
<type class="int"/>
<description>
the array index.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.PropertyValue.getPropertyValue() -->
<method name="getPropertyValue"  public="true" abstract="true">
<description>
Get the encoded value.
</description>
<return>
<type class="byte" dimension="1"/>
<description>
a byte array containing the Asn-encoded value,&#xa; or null if this is a failure.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.PropertyValue.getPriority() -->
<method name="getPriority"  public="true" abstract="true">
<description>
Get the priority.
</description>
<return>
<type class="int"/>
<description>
the priority associated with this value.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.PropertyValue.getPropertyAccessError() -->
<method name="getPropertyAccessError"  public="true" abstract="true">
<description>
Get the error.
</description>
<return>
<type class="javax.baja.bacnet.io.ErrorType"/>
<description>
an ErrorType if this is an error result,&#xa; or null if this is a success.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.PropertyValue.getErrorClass() -->
<method name="getErrorClass"  public="true" abstract="true">
<description>
Get the error class.
</description>
<return>
<type class="int"/>
<description>
an int representing a value in the BBacnetErrorClass&#xa; enumeration indicating the class of failure,&#xa; or null if this is a success.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.PropertyValue.getErrorCode() -->
<method name="getErrorCode"  public="true" abstract="true">
<description>
Get the error code.
</description>
<return>
<type class="int"/>
<description>
an int representing a value in the BBacnetErrorCode&#xa; enumeration indicating the reason for failure,&#xa; or null if this is a success.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.PropertyValue.isError() -->
<method name="isError"  public="true" abstract="true">
<description>
Is this a failure result?
</description>
<return>
<type class="boolean"/>
<description>
TRUE if this is an error result, or FALSE if it is a success.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.PropertyValue.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true" abstract="true">
<description>
Encode the property value data to Asn.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the Asn encoder.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.PropertyValue.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true" abstract="true">
<description>
Decode the property value data from Asn.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the Asn decoder.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
<description>
if there is an Asn error.
</description>
</throws>
<throws>
<type class="javax.baja.bacnet.io.RejectException"/>
</throws>
</method>

</class>
</bajadoc>
