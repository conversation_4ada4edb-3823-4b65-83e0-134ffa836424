<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="com.tridium.bacnetAws.datatypes.BCreateObjectParameters" name="BCreateObjectParameters" packageName="com.tridium.bacnetAws.datatypes" public="true">
<description>
Create Object Parameters transfer the data from &#xa;  the WB to the Station providing the creation job &#xa;  the required information.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">Apr 8, 2011</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="objectIds" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;objectIds&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectIds</tag>
<tag name="@see">#setObjectIds</tag>
</property>

<property name="parentOrd" flags="">
<type class="javax.baja.naming.BOrd"/>
<description>
Slot for the &lt;code&gt;parentOrd&lt;/code&gt; property.
</description>
<tag name="@see">#getParentOrd</tag>
<tag name="@see">#setParentOrd</tag>
</property>

<property name="names" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;names&lt;/code&gt; property.
</description>
<tag name="@see">#getNames</tag>
<tag name="@see">#setNames</tag>
</property>

<property name="sendInitValues" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;sendInitValues&lt;/code&gt; property.
</description>
<tag name="@see">#getSendInitValues</tag>
<tag name="@see">#setSendInitValues</tag>
</property>

</class>
</bajadoc>
