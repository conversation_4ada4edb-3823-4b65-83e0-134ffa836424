<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.BBacnetStack" name="BBacnetStack" packageName="com.tridium.bacnet.stack" public="true">
<description>
BBacnetStack provides the protocol stack for Bacnet communications.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">19 Apr 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.bacnet.io.BBacnetComm"/>
</extends>
<implements>
<type class="com.tridium.bacnet.stack.IAmListener"/>
</implements>
<property name="client" flags="">
<type class="com.tridium.bacnet.stack.client.BBacnetClientLayer"/>
<description>
Slot for the &lt;code&gt;client&lt;/code&gt; property.
</description>
<tag name="@see">#getClient</tag>
<tag name="@see">#setClient</tag>
</property>

<property name="server" flags="">
<type class="com.tridium.bacnet.stack.server.BBacnetServerLayer"/>
<description>
Slot for the &lt;code&gt;server&lt;/code&gt; property.
</description>
<tag name="@see">#getServer</tag>
<tag name="@see">#setServer</tag>
</property>

<property name="transport" flags="">
<type class="com.tridium.bacnet.stack.transport.BBacnetTransportLayer"/>
<description>
Slot for the &lt;code&gt;transport&lt;/code&gt; property.
</description>
<tag name="@see">#getTransport</tag>
<tag name="@see">#setTransport</tag>
</property>

<property name="network" flags="">
<type class="com.tridium.bacnet.stack.network.BBacnetNetworkLayer"/>
<description>
Slot for the &lt;code&gt;network&lt;/code&gt; property.
</description>
<tag name="@see">#getNetwork</tag>
<tag name="@see">#setNetwork</tag>
</property>

<action name="dumpDeviceRegistry" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;dumpDeviceRegistry&lt;/code&gt; action.
</description>
<tag name="@see">#dumpDeviceRegistry()</tag>
</action>

</class>
</bajadoc>
