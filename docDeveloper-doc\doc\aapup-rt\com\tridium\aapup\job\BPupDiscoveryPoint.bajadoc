<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.job.BPupDiscoveryPoint" name="BPupDiscoveryPoint" packageName="com.tridium.aapup.job" public="true">
<description>
PUP Discovery Point models a point as it is learned but before it&#xa; is added to the database
</description>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@creation">7/26/2005 1:18PM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.91</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="channel" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;channel&lt;/code&gt; property.&#xa; the channel the point was learned from
</description>
<tag name="@see">#getChannel</tag>
<tag name="@see">#setChannel</tag>
</property>

<property name="channelDescription" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;channelDescription&lt;/code&gt; property.&#xa; the channel description originating from the deviceTypes.xml file
</description>
<tag name="@see">#getChannelDescription</tag>
<tag name="@see">#setChannelDescription</tag>
</property>

<property name="attribute" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;attribute&lt;/code&gt; property.&#xa; the attribute in the channel this point corresponds to
</description>
<tag name="@see">#getAttribute</tag>
<tag name="@see">#setAttribute</tag>
</property>

<property name="attributeType" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;attributeType&lt;/code&gt; property.&#xa; the attribute type used for data conversion
</description>
<tag name="@see">#getAttributeType</tag>
<tag name="@see">#setAttributeType</tag>
</property>

<property name="attributeValue" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;attributeValue&lt;/code&gt; property.&#xa; a converted string representation of the point value
</description>
<tag name="@see">#getAttributeValue</tag>
<tag name="@see">#setAttributeValue</tag>
</property>

<property name="prefix" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;prefix&lt;/code&gt; property.&#xa; The value which is used for the front-end of the point&#x27;s name&#xa; The value &#x22;&#x22; will cause a fallback to AX&#x27;s naming conventions.&#xa; Normally originates from the deviceTypes.xml parameter &#x22;prefix&#x22;
</description>
<tag name="@see">#getPrefix</tag>
<tag name="@see">#setPrefix</tag>
</property>

</class>
</bajadoc>
