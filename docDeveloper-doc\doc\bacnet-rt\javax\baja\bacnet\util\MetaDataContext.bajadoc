<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.util.MetaDataContext" name="MetaDataContext" packageName="javax.baja.bacnet.util" public="true">
<description/>
<extends>
<type class="javax.baja.sys.BasicContext"/>
</extends>
<!-- javax.baja.bacnet.util.MetaDataContext(java.lang.String) -->
<constructor name="MetaDataContext" public="true">
<parameter name="propName">
<type class="java.lang.String"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.util.MetaDataContext.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description/>
<parameter name="o">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.util.MetaDataContext.hashCode() -->
<method name="hashCode"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.util.MetaDataContext.toString() -->
<method name="toString"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.util.MetaDataContext.getPropertyName() -->
<method name="getPropertyName"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

</class>
</bajadoc>
