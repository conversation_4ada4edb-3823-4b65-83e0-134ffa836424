<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarmOrion" runtimeProfile="rt" qualifiedName="javax.baja.alarmOrion.BOrionAlarmFacetName" name="BOrionAlarmFacetName" packageName="javax.baja.alarmOrion" public="true">
<description>
The representation of an alarm data facet name within the orion database.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">March 18, 2009</tag>
<extends>
<type class="com.tridium.orion.BOrionObject"/>
</extends>
<annotation><type class="com.tridium.orion.annotations.NiagaraOrionType"/>
</annotation>
<property name="id" flags="rs">
<type class="int"/>
<description>
Slot for the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#getId</tag>
<tag name="@see">#setId</tag>
</property>

<property name="facetName" flags="s">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;facetName&lt;/code&gt; property.
</description>
<tag name="@see">#getFacetName</tag>
<tag name="@see">#setFacetName</tag>
</property>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetName() -->
<constructor name="BOrionAlarmFacetName" public="true">
<description/>
</constructor>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetName.getId() -->
<method name="getId"  public="true">
<description>
Get the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#id</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetName.setId(int) -->
<method name="setId"  public="true">
<description>
Set the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#id</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetName.getFacetName() -->
<method name="getFacetName"  public="true">
<description>
Get the &lt;code&gt;facetName&lt;/code&gt; property.
</description>
<tag name="@see">#facetName</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetName.setFacetName(java.lang.String) -->
<method name="setFacetName"  public="true">
<description>
Set the &lt;code&gt;facetName&lt;/code&gt; property.
</description>
<tag name="@see">#facetName</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetName.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetName.get(java.lang.String, com.tridium.orion.OrionSession) -->
<method name="get"  public="true" static="true">
<description>
Get the facet record of the specified name.  If one does not exist, create it.&#xa; The results are cached to speed up multiple requests for the same object.
</description>
<parameter name="key">
<type class="java.lang.String"/>
</parameter>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="javax.baja.alarmOrion.BOrionAlarmFacetName"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetName.get(int, com.tridium.orion.OrionSession) -->
<method name="get"  public="true" static="true">
<description>
Get the facet record of the specified name.  If one does not exist, create it.&#xa; The results are cached to speed up multiple requests for the same object.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="javax.baja.alarmOrion.BOrionAlarmFacetName"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetName.afterDelete(com.tridium.orion.OrionSession) -->
<method name="afterDelete"  public="true">
<description/>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetName.afterInsert(com.tridium.orion.OrionSession) -->
<method name="afterInsert"  public="true">
<description/>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetName.afterUpdate(com.tridium.orion.OrionSession) -->
<method name="afterUpdate"  public="true">
<description/>
<parameter name="session">
<type class="com.tridium.orion.OrionSession"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetName.id -->
<field name="id"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;id&lt;/code&gt; property.
</description>
<tag name="@see">#getId</tag>
<tag name="@see">#setId</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetName.facetName -->
<field name="facetName"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;facetName&lt;/code&gt; property.
</description>
<tag name="@see">#getFacetName</tag>
<tag name="@see">#setFacetName</tag>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetName.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.alarmOrion.BOrionAlarmFacetName.ORION_TYPE -->
<field name="ORION_TYPE"  public="true" static="true" final="true">
<type class="com.tridium.orion.OrionType"/>
<description/>
</field>

</class>
</bajadoc>
