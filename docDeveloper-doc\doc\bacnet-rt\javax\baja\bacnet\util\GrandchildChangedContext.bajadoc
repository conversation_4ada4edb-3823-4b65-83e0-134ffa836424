<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.util.GrandchildChangedContext" name="GrandchildChangedContext" packageName="javax.baja.bacnet.util" public="true">
<description/>
<extends>
<type class="javax.baja.sys.BasicContext"/>
</extends>
<!-- javax.baja.bacnet.util.GrandchildChangedContext(int, byte[]) -->
<constructor name="GrandchildChangedContext" public="true">
<parameter name="arrayIndex">
<type class="int"/>
</parameter>
<parameter name="encodedValue">
<type class="byte" dimension="1"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.util.GrandchildChangedContext.getArrayIndex() -->
<method name="getArrayIndex"  public="true">
<description/>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnet.util.GrandchildChangedContext.getEncodedValue() -->
<method name="getEncodedValue"  public="true">
<description/>
<return>
<type class="byte" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.util.GrandchildChangedContext.toString() -->
<method name="toString"  public="true">
<description/>
<return>
<type class="java.lang.String"/>
</return>
</method>

</class>
</bajadoc>
