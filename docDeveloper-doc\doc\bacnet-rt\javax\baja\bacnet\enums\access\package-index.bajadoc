<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="javax.baja.bacnet.enums.access">
<description/>
<class packageName="javax.baja.bacnet.enums.access" name="BBacnetAccessAuthenticationFactorDisable"><description>BBacnetAccessAuthenticationFactorDisable represents the Bacnet&#xa; Access Authentication Factor Disable enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums.access" name="BBacnetAccessCredentialDisable"><description>BBacnetAccessCredentialDisable represents the Bacnet&#xa; Access Credential Disable enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums.access" name="BBacnetAccessCredentialDisableReason"><description>BBacnetAccessCredentialDisableReason represents the&#xa; Bacnet Access Credential Disable Reason enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums.access" name="BBacnetAccessEvent"><description>BBacnetAccessEvent represents the Bacnet&#xa; Access Event enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums.access" name="BBacnetAccessPassbackMode"><description>BBacnetAccessZoneOccupancyState represents the Bacnet&#xa; Access Zone Occupancy State enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums.access" name="BBacnetAccessUserType"><description>BBacnetAccessUserType represents the Bacnet&#xa; Access User Type enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums.access" name="BBacnetAccessZoneOccupancyState"><description>BBacnetAccessZoneOccupancyState represents the Bacnet&#xa; Access Zone Occupancy State enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums.access" name="BBacnetAuthenticationFactorType"><description>BBacnetAccessCredentialDisable represents the Bacnet&#xa; Access Credential Disable enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums.access" name="BBacnetAuthenticationStatus"><description>BBacnetAccessCredentialDisable represents the Bacnet&#xa; Access Credential Disable enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums.access" name="BBacnetAuthorizationExemption"><description>BBacnetAuthorizationExemption represents the&#xa; BACnetAuthorizationExemption enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums.access" name="BBacnetAuthorizationMode"><description>BBacnetAccessCredentialDisable represents the Bacnet&#xa; Access Credential Disable enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums.access" name="BBacnetDoorAlarmState"><description>BBacnetDoorAlarmState represents the Bacnet&#xa; Bacnet Door Alarm State enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums.access" name="BBacnetDoorSecuredStatus"><description>BBacnetDoorValue represents the Bacnet&#xa; Bacnet Door Value enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums.access" name="BBacnetDoorStatus"><description>BBacnetDoorStatus represents the Bacnet&#xa; Bacnet Door Status enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums.access" name="BBacnetDoorValue"><description>BBacnetDoorValue represents the Bacnet&#xa; Bacnet Door Value enumeration.</description></class>
<class packageName="javax.baja.bacnet.enums.access" name="BBacnetLockStatus"><description>BBacnetDoorStatus represents the Bacnet&#xa; Bacnet Door Status enumeration.</description></class>
</package>
</bajadoc>
