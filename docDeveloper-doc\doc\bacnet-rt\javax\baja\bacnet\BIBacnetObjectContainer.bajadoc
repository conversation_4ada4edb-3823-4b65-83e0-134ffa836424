<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.BIBacnetObjectContainer" name="BIBacnetObjectContainer" packageName="javax.baja.bacnet" public="true" interface="true" abstract="true" category="interface">
<description>
BIBacnetObjectContainer resolves a triplet of object identifier,&#xa; property identifier, and property array index to a Bacnet point.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">02 Sep 03</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<!-- javax.baja.bacnet.BIBacnetObjectContainer.lookupBacnetObject(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int, java.lang.String) -->
<method name="lookupBacnetObject"  public="true" abstract="true">
<description>
Look up and return the Bacnet object with the given reference.
</description>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<parameter name="propertyId">
<type class="int"/>
<description/>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
<description/>
</parameter>
<parameter name="domain">
<type class="java.lang.String"/>
<description>
the realm in which to look up the object: point, schedule, history
</description>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
<description>
a BObject with the given reference parameters, or null if&#xa; this container does not contain any objects with the given parameters.
</description>
</return>
</method>

<!-- javax.baja.bacnet.BIBacnetObjectContainer.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.BIBacnetObjectContainer.POINT -->
<field name="POINT"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BIBacnetObjectContainer.SCHEDULE -->
<field name="SCHEDULE"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BIBacnetObjectContainer.HISTORY -->
<field name="HISTORY"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.BIBacnetObjectContainer.CONFIG -->
<field name="CONFIG"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

</class>
</bajadoc>
