<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.server.BOverrideMode" name="BOverrideMode" packageName="com.tridium.bacnet.stack.server" public="true" final="true">
<description>
BOverrideMode determines when the BACnet overridden flag.&#xa; &lt;p&gt;&#xa; &lt;table border=&#x22;1&#x22;&gt;&#xa;&#xa; &lt;tr&gt;&#xa;   &lt;th align=&#x22;left&#x22;&gt;Enum&lt;/th&gt;&#xa;   &lt;th&gt;Description&lt;/th&gt;&#xa; &lt;/tr&gt;&#xa;&#xa;  &lt;tr&gt;&#xa;    &lt;th align=&#x22;left&#x22;&gt;legacy&lt;/th&gt;&#xa;    &lt;td&gt;&#xa;      Sets the BACnet overridden flag when either priority 1 or 8 is the active level. This maps&#xa;      the Niagara overridden flag to the BACnet overridden flag.&#xa;    &lt;/td&gt;&#xa;  &lt;/tr&gt;&#xa;&#xa;  &lt;tr&gt;&#xa;    &lt;th align=&#x22;left&#x22;&gt;oneOnly&lt;/th&gt;&#xa;    &lt;td&gt;Sets the BACnet overridden flag only when priority 1 is the active level.&lt;/td&gt;&#xa;  &lt;/tr&gt;&#xa;&#xa; &lt;/table&gt;
</description>
<tag name="@author">Bishal Debbarma on 12/2/2021</tag>
<tag name="@since">Niagara 4.12</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;legacy&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;oneOnly&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
<elementValue name="defaultValue">
<annotationValue kind="expr">
<expression>&#x22;legacy&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</class>
</bajadoc>
