<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BIAlarmSource" name="BIAlarmSource" packageName="javax.baja.alarm" public="true" interface="true" abstract="true" category="interface">
<description>
The BIAlarmSource interface must be implemented by all&#xa; BObjects capable of generating alarms &#xa; &lt;p&gt;&#xa; This interface must be implemented by declaring the&#xa; following actions on the BIAlarmSource:&#xa; &lt;pre&gt;&#xa;    ackAlarm(ackRequest: BAlarmRecord): boolean     &#xa;      -- Acknowledge the alarm matching this ack request&#xa;      flags = readonly, hidden&#xa; &lt;/pre&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">19 Feb 01</tag>
<tag name="@version">$Revision: 3$ $Date: 6/16/04 11:10:56 AM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<!-- javax.baja.alarm.BIAlarmSource.ackAlarm(javax.baja.alarm.BAlarmRecord) -->
<method name="ackAlarm"  public="true" abstract="true">
<description>
Acknowledge the alarm matching the given acknowledge &#xa; request.&#xa; Classes implementing AlarmSource must implement the ackAlarm &#xa; method as an action.
</description>
<parameter name="ackRequest">
<type class="javax.baja.alarm.BAlarmRecord"/>
<description>
The acknowledgement request alarm record.
</description>
</parameter>
<return>
<type class="javax.baja.sys.BBoolean"/>
<description>
true if alarm acked (clear ack bit in status), false if stale ack
</description>
</return>
</method>

<!-- javax.baja.alarm.BIAlarmSource.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
