<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.datatypes.BProgramReloadSettings" name="BProgramReloadSettings" packageName="com.tridium.andoverAC256.datatypes" public="true">
<description>
BProgramReloadSettings: Component that encapsulates all relevant&#xa; data used to re-set the number of IOUs and LCUs in the Andover&#xa; configuration.  Necessary info in the event the Andover panel&#xa; loses its program and has to be reloaded.
</description>
<tag name="@author">Clif <PERSON></tag>
<tag name="@creation">10/13/2004 1:05PM</tag>
<tag name="@version">$Revision$ $Date: 10/13/2004 1:05PM$</tag>
<tag name="@since">Niagara 3.0 andoverAC256 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="reloadRequired" flags="rtd">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;reloadRequired&lt;/code&gt; property.&#xa; Whenever a controller looses its mind, the unit will&#xa; restart with time and system options not set.  This is the&#xa; clue that the program is lost and needs to be reloaded.&#xa; The driver sets this flag if this case is detected.
</description>
<tag name="@see">#getReloadRequired</tag>
<tag name="@see">#setReloadRequired</tag>
</property>

<property name="lBusIousAtStartup" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;lBusIousAtStartup&lt;/code&gt; property.&#xa; Whenever a controller looses its mind, the unit will&#xa; restart with a prompt &#x22;L-Bus Number of IOU&#x27;s ?&#x22; which&#xa; must be answered before a program reload can be attempted.&#xa; This property provides the answer
</description>
<tag name="@see">#getLBusIousAtStartup</tag>
<tag name="@see">#setLBusIousAtStartup</tag>
</property>

<property name="acnetLCUsAtStartup" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;acnetLCUsAtStartup&lt;/code&gt; property.&#xa; Whenever a controller looses its mind, the unit will&#xa; restart with a prompt &#x22;ACNET Number of LCU&#x27;s ?&#x22; which&#xa; must be answered before a program reload can be attempted.&#xa; This property provides the answer
</description>
<tag name="@see">#getAcnetLCUsAtStartup</tag>
<tag name="@see">#setAcnetLCUsAtStartup</tag>
</property>

</class>
</bajadoc>
