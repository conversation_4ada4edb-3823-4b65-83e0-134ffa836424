<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="javax.baja.bacnet.listeners">
<description/>
<class packageName="javax.baja.bacnet.listeners" name="DynamicIAmListener"><description>DynamicIAmListener: As a part of the AMEV AS-B implementations, since we are&#xa; supporting Dynamic Device Binding mainly for Trending, Alarming and&#xa; Scheduling, we do a lot of RP and CoVs to remote device.</description></class>
<class packageName="javax.baja.bacnet.listeners" name="DynamicIAmListener.IAmHandler"/>
</package>
</bajadoc>
