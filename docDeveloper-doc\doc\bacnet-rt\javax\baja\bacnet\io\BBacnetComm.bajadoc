<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.io.BBacnetComm" name="BBacnetComm" packageName="javax.baja.bacnet.io" public="true" abstract="true">
<description>
BBacnetComm is the object that exposes the communications&#xa; stack.  No methods exist, because different stack implementations&#xa; may have varying levels of support for the specification.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">19 Apr 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<property name="commControl" flags="t">
<type class="javax.baja.bacnet.enums.BBacnetCommControl"/>
<description>
Slot for the &lt;code&gt;commControl&lt;/code&gt; property.
</description>
<tag name="@see">#getCommControl</tag>
<tag name="@see">#setCommControl</tag>
</property>

<action name="enableComm" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;enableComm&lt;/code&gt; action.
</description>
<tag name="@see">#enableComm()</tag>
</action>

<action name="disableInitiation" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;disableInitiation&lt;/code&gt; action.
</description>
<tag name="@see">#disableInitiation()</tag>
</action>

<action name="disableComm" flags="">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;disableComm&lt;/code&gt; action.
</description>
<tag name="@see">#disableComm()</tag>
</action>

<!-- javax.baja.bacnet.io.BBacnetComm() -->
<constructor name="BBacnetComm" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.io.BBacnetComm.getCommControl() -->
<method name="getCommControl"  public="true">
<description>
Get the &lt;code&gt;commControl&lt;/code&gt; property.
</description>
<tag name="@see">#commControl</tag>
<return>
<type class="javax.baja.bacnet.enums.BBacnetCommControl"/>
</return>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.setCommControl(javax.baja.bacnet.enums.BBacnetCommControl) -->
<method name="setCommControl"  public="true">
<description>
Set the &lt;code&gt;commControl&lt;/code&gt; property.
</description>
<tag name="@see">#commControl</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.BBacnetCommControl"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.enableComm() -->
<method name="enableComm"  public="true">
<description>
Invoke the &lt;code&gt;enableComm&lt;/code&gt; action.
</description>
<tag name="@see">#enableComm</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.disableInitiation() -->
<method name="disableInitiation"  public="true">
<description>
Invoke the &lt;code&gt;disableInitiation&lt;/code&gt; action.
</description>
<tag name="@see">#disableInitiation</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.disableComm() -->
<method name="disableComm"  public="true">
<description>
Invoke the &lt;code&gt;disableComm&lt;/code&gt; action.
</description>
<tag name="@see">#disableComm</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true">
<description/>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.isSiblingLegal(javax.baja.sys.BComponent) -->
<method name="isSiblingLegal"  public="true">
<description/>
<parameter name="sibling">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.doEnableComm() -->
<method name="doEnableComm"  public="true">
<description>
Enable Bacnet communications.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.doDisableInitiation() -->
<method name="doDisableInitiation"  public="true">
<description>
Disable Bacnet communications.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.doDisableComm() -->
<method name="doDisableComm"  public="true">
<description>
Disable Bacnet communications.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.isCommExecutionEnabled() -->
<method name="isCommExecutionEnabled"  public="true">
<description>
Is Bacnet communication allowed for execution of requests?&#xa; This checks that the commControl property is NOT set to disable,&#xa; and that the objectId of the local Bacnet device&#xa; is configured properly.&#xa; &lt;p&gt;&#xa; If both of these are configured properly, request execution&#xa; (responding to Bacnet requests) is allowed.
</description>
<return>
<type class="boolean"/>
<description>
true if Bacnet request execution is enabled.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.isCommInitiationEnabled() -->
<method name="isCommInitiationEnabled"  public="true">
<description>
Is communication allowed for initiation of requests?&#xa; This checks that the commControl property is set to enabled,&#xa; and that the objectId of the local Bacnet device is configured&#xa; to a valid id.&#xa; &lt;p&gt;&#xa; If both of these are configured properly, comm is allowed.
</description>
<return>
<type class="boolean"/>
<description>
true if Bacnet request initiation is enabled.
</description>
</return>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.acknowledgeAlarm(javax.baja.bacnet.datatypes.BBacnetAddress, long, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, javax.baja.bacnet.enums.BBacnetEventState, javax.baja.bacnet.datatypes.BBacnetTimeStamp, java.lang.String, javax.baja.bacnet.datatypes.BBacnetTimeStamp, javax.baja.bacnet.enums.BCharacterSetEncoding) -->
<method name="acknowledgeAlarm"  public="true" abstract="true">
<description>
Issue an AcknowledgeAlarm request.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<parameter name="acknowledgingProcessId">
<type class="long"/>
</parameter>
<parameter name="eventObjectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="eventStateAcknowledged">
<type class="javax.baja.bacnet.enums.BBacnetEventState"/>
</parameter>
<parameter name="timestamp">
<type class="javax.baja.bacnet.datatypes.BBacnetTimeStamp"/>
</parameter>
<parameter name="acknowledgementSource">
<type class="java.lang.String"/>
</parameter>
<parameter name="timeOfAcknowledgement">
<type class="javax.baja.bacnet.datatypes.BBacnetTimeStamp"/>
</parameter>
<parameter name="encoding">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.confirmedCovNotification(javax.baja.bacnet.datatypes.BBacnetAddress, long, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, long, javax.baja.bacnet.io.PropertyValue[]) -->
<method name="confirmedCovNotification"  public="true" abstract="true">
<description>
Issue a ConfirmedCovNotification request.
</description>
<parameter name="address">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<parameter name="subscriberProcessId">
<type class="long"/>
</parameter>
<parameter name="initiatingDeviceId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="monitoredObjectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="timeRemaining">
<type class="long"/>
</parameter>
<parameter name="listOfValues">
<type class="javax.baja.bacnet.io.PropertyValue" dimension="1"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.confirmedEventNotification(javax.baja.bacnet.datatypes.BBacnetAddress, long, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, javax.baja.bacnet.datatypes.BBacnetTimeStamp, long, int, javax.baja.sys.BEnum, java.lang.String, javax.baja.bacnet.enums.BBacnetNotifyType, boolean, javax.baja.sys.BEnum, javax.baja.sys.BEnum, com.tridium.bacnet.asn.BacnetNotificationParameters, javax.baja.bacnet.enums.BCharacterSetEncoding) -->
<method name="confirmedEventNotification"  public="true" abstract="true">
<description>
Issue a ConfirmedEventNotification request.
</description>
<parameter name="address">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<parameter name="processId">
<type class="long"/>
</parameter>
<parameter name="initiatingDeviceId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="eventObjectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="timeStamp">
<type class="javax.baja.bacnet.datatypes.BBacnetTimeStamp"/>
</parameter>
<parameter name="notificationClass">
<type class="long"/>
</parameter>
<parameter name="priority">
<type class="int"/>
</parameter>
<parameter name="eventType">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<parameter name="messageText">
<type class="java.lang.String"/>
</parameter>
<parameter name="notifyType">
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
</parameter>
<parameter name="ackRequired">
<type class="boolean"/>
</parameter>
<parameter name="fromState">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<parameter name="toState">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<parameter name="eventValues">
<type class="com.tridium.bacnet.asn.BacnetNotificationParameters"/>
</parameter>
<parameter name="encoding">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.getAlarmSummary(javax.baja.bacnet.datatypes.BBacnetAddress) -->
<method name="getAlarmSummary"  public="true" abstract="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Issue a GetAlarmSummary request.
</description>
<parameter name="address">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<return>
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.getEnrollmentSummary(javax.baja.bacnet.datatypes.BBacnetAddress, int, javax.baja.bacnet.datatypes.BBacnetRecipientProcess, int, javax.baja.sys.BEnum, int[], long) -->
<method name="getEnrollmentSummary"  public="true" abstract="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Issue a GetEnrollmentSummary request.
</description>
<parameter name="address">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<parameter name="acknowledgmentFilter">
<type class="int"/>
</parameter>
<parameter name="enrollmentFilter">
<type class="javax.baja.bacnet.datatypes.BBacnetRecipientProcess"/>
</parameter>
<parameter name="eventStateFilter">
<type class="int"/>
</parameter>
<parameter name="eventTypeFilter">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<parameter name="priorityFilter">
<type class="int" dimension="1"/>
</parameter>
<parameter name="notificationClassFilter">
<type class="long"/>
</parameter>
<return>
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.getEventInformation(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="getEventInformation"  public="true" abstract="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Issue a GetEventInformation request.
</description>
<parameter name="address">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<parameter name="lastReceivedObjectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.subscribeCov(javax.baja.bacnet.datatypes.BBacnetAddress, long, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, boolean, long) -->
<method name="subscribeCov"  public="true" abstract="true">
<description>
Issue a SubscribeCov request.
</description>
<parameter name="address">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<parameter name="processId">
<type class="long"/>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="issueConfirmedNotifications">
<type class="boolean"/>
</parameter>
<parameter name="lifetime">
<type class="long"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.unsubscribeCov(javax.baja.bacnet.datatypes.BBacnetAddress, long, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="unsubscribeCov"  public="true" abstract="true">
<description>
Issue a SubscribeCov request indicating a subsription cancellation.
</description>
<parameter name="address">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<parameter name="processId">
<type class="long"/>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.subscribeCovProperty(javax.baja.bacnet.datatypes.BBacnetAddress, long, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, boolean, long, javax.baja.bacnet.io.PropertyReference, javax.baja.sys.BDouble) -->
<method name="subscribeCovProperty"  public="true" abstract="true">
<description>
Issue a SubscribeCov request.
</description>
<parameter name="address">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<parameter name="processId">
<type class="long"/>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="issueConfirmedNotifications">
<type class="boolean"/>
</parameter>
<parameter name="lifetime">
<type class="long"/>
</parameter>
<parameter name="monitoredPropertyIdentifier">
<type class="javax.baja.bacnet.io.PropertyReference"/>
</parameter>
<parameter name="covIncrement">
<type class="javax.baja.sys.BDouble"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.unsubscribeCovProperty(javax.baja.bacnet.datatypes.BBacnetAddress, long, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, javax.baja.bacnet.io.PropertyReference) -->
<method name="unsubscribeCovProperty"  public="true" abstract="true">
<description>
Issue a SubscribeCov request indicating a subsription cancellation.
</description>
<parameter name="address">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<parameter name="processId">
<type class="long"/>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="monitoredPropertyIdentifier">
<type class="javax.baja.bacnet.io.PropertyReference"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.atomicReadFileRecord(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, long) -->
<method name="atomicReadFileRecord"  public="true" abstract="true">
<description>
Issue an AtomicReadFile-Request, using record access.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description/>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<parameter name="start">
<type class="int"/>
<description>
file start position
</description>
</parameter>
<parameter name="count">
<type class="long"/>
<description>
number of octets to read
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.FileData"/>
<description>
a FileData containing the information read from the device.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.atomicReadFileStream(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, long) -->
<method name="atomicReadFileStream"  public="true" abstract="true">
<description>
Issue an AtomicReadFile-Request, using stream access.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description/>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<parameter name="start">
<type class="int"/>
<description>
file start position
</description>
</parameter>
<parameter name="count">
<type class="long"/>
<description>
number of octets to read
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.io.FileData"/>
<description>
a FileData containing the information read from the device.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.atomicWriteFileRecord(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, long, javax.baja.bacnet.datatypes.BBacnetOctetString[]) -->
<method name="atomicWriteFileRecord"  public="true" abstract="true">
<description>
Issue an AtomicWriteFile-Request, using record access.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description/>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<parameter name="start">
<type class="int"/>
<description>
file start record
</description>
</parameter>
<parameter name="count">
<type class="long"/>
<description>
number of records to write
</description>
</parameter>
<parameter name="fileRecordData">
<type class="javax.baja.bacnet.datatypes.BBacnetOctetString" dimension="1"/>
<description>
the source record data
</description>
</parameter>
<return>
<type class="int"/>
<description>
the actual starting record of the written data.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.atomicWriteFileStream(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, byte[]) -->
<method name="atomicWriteFileStream"  public="true" abstract="true">
<description>
Issue an AtomicWriteFile-Request, using stream access.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description/>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<parameter name="start">
<type class="int"/>
<description>
file start position
</description>
</parameter>
<parameter name="fileData">
<type class="byte" dimension="1"/>
<description>
the source file data
</description>
</parameter>
<return>
<type class="int"/>
<description>
the actual starting position of the written data.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.addListElement(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, byte[]) -->
<method name="addListElement"  public="true" abstract="true">
<description>
Issue an AddListElement request.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the address of the remote device.
</description>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the object-identifier of the remote object.
</description>
</parameter>
<parameter name="propertyId">
<type class="int"/>
<description>
the property-identifier of the desired property.
</description>
</parameter>
<parameter name="listOfElements">
<type class="byte" dimension="1"/>
<description>
the encoded list of elements to be added.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.addListElement(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int, byte[]) -->
<method name="addListElement"  public="true" abstract="true">
<description>
Issue an AddListElement request.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the address of the remote device.
</description>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the object-identifier of the remote object.
</description>
</parameter>
<parameter name="propertyId">
<type class="int"/>
<description>
the property-identifier of the desired property.
</description>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
<description>
the array index, if specified.
</description>
</parameter>
<parameter name="listOfElements">
<type class="byte" dimension="1"/>
<description>
the encoded list of elements to be added.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.removeListElement(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, byte[]) -->
<method name="removeListElement"  public="true" abstract="true">
<description>
Issue a RemoveListElement request.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the address of the remote device.
</description>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the object-identifier of the remote object.
</description>
</parameter>
<parameter name="propertyId">
<type class="int"/>
<description>
the property-identifier of the desired property.
</description>
</parameter>
<parameter name="listOfElements">
<type class="byte" dimension="1"/>
<description>
the encoded list of elements to be removed.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.removeListElement(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int, byte[]) -->
<method name="removeListElement"  public="true" abstract="true">
<description>
Issue a RemoveListElement request.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the address of the remote device.
</description>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the object-identifier of the remote object.
</description>
</parameter>
<parameter name="propertyId">
<type class="int"/>
<description>
the property-identifier of the desired property.
</description>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
<description>
the array index, if specified.
</description>
</parameter>
<parameter name="listOfElements">
<type class="byte" dimension="1"/>
<description>
the encoded list of elements to be removed.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.createObject(javax.baja.bacnet.datatypes.BBacnetAddress, int, javax.baja.nre.util.Array) -->
<method name="createObject"  public="true" abstract="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Issue a CreateObject service request.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the address of the remote device.
</description>
</parameter>
<parameter name="objectType">
<type class="int"/>
<description>
the object type of the new object.
</description>
</parameter>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
</args>
</parameterizedType>
<description>
the list of initial values.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the objectId of the new object.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.createObject(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, javax.baja.nre.util.Array) -->
<method name="createObject"  public="true" abstract="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Issue a CreateObject service request.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the address of the remote device.
</description>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the object type of the new object.
</description>
</parameter>
<parameter name="listOfInitialValues">
<parameterizedType class="javax.baja.nre.util.Array">
<args>
</args>
</parameterizedType>
<description>
the list of initial values.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the objectId of the new object.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.deleteObject(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="deleteObject"  public="true" abstract="true">
<description>
Issue a DeleteObject service request.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the address of the remote device.
</description>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the object type of the new object.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.readProperty(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int) -->
<method name="readProperty"  public="true" abstract="true">
<description>
Issue a ReadProperty service request.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the address of the remote device.
</description>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the object-identifier of the remote object.
</description>
</parameter>
<parameter name="propertyId">
<type class="int"/>
<description>
the property-identifier of the desired property.
</description>
</parameter>
<return>
<type class="byte" dimension="1"/>
<description>
a byte array containing the encoded value.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.readProperty(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int) -->
<method name="readProperty"  public="true" abstract="true">
<description>
Issue a ReadProperty service request.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the address of the remote device.
</description>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the object-identifier of the remote object.
</description>
</parameter>
<parameter name="propertyId">
<type class="int"/>
<description>
the property-identifier of the desired property.
</description>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
<description>
the array index, if specified.
</description>
</parameter>
<return>
<type class="byte" dimension="1"/>
<description>
a byte array containing the encoded value.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.readPropertyMultiple(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, java.util.Vector) -->
<method name="readPropertyMultiple"  public="true" abstract="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
Convenience method for requesting multiple properties from only&#xa; one object.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the address to which the request is sent.
</description>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the object identifier of the object to poll.
</description>
</parameter>
<parameter name="propRefs">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
a Vector containing NBacnetPropertyReferences&#xa;                           for each property requested.
</description>
</parameter>
<return>
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
a Vector containing the list of Read Property Results, as PropertyValues,&#xa; if the request succeeds, or the input Vector if the request&#xa; is aborted or rejected.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.readPropertyMultiple(javax.baja.bacnet.datatypes.BBacnetAddress, java.util.Vector) -->
<method name="readPropertyMultiple"  public="true" abstract="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
ReadPropertyMultiple used in device-level polling.&#xa; If this request is aborted or rejected (probably due to&#xa; maxAPDULengthAccepted and/or segmentation issues), the&#xa; Iterator returned will be the iterator containing the&#xa; property requests.  This allows the requesting object&#xa; to make a decision to split up the request into&#xa; smaller chunks.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the address to which the request is sent.
</description>
</parameter>
<parameter name="readAccessSpecs">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
a Vector containing the NReadAccessSpecs .
</description>
</parameter>
<return>
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
<description>
a Vector containing the NReadAccessResults in the same&#xa; order as the specifications if the request succeeds, or&#xa; the input Vector if the request is aborted or rejected.
</description>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.readRange(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int, int, long, javax.baja.bacnet.datatypes.BBacnetDateTime, int) -->
<method name="readRange"  public="true" abstract="true">
<description>
Issue a ReadRange service request.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description/>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<parameter name="propertyId">
<type class="int"/>
<description/>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
<description/>
</parameter>
<parameter name="rangeType">
<type class="int"/>
</parameter>
<parameter name="referenceIndex">
<type class="long"/>
</parameter>
<parameter name="referenceTime">
<type class="javax.baja.bacnet.datatypes.BBacnetDateTime"/>
</parameter>
<parameter name="count">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.io.RangeData"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.writeProperty(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int, byte[]) -->
<method name="writeProperty"  public="true" abstract="true">
<description>
Issue a WriteProperty service request with no priority level.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the address of the remote device to write to.
</description>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the object-identifier of the remote object.
</description>
</parameter>
<parameter name="propertyId">
<type class="int"/>
<description>
the property-identifier of the desired property.
</description>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
<description>
the array index, if specified.
</description>
</parameter>
<parameter name="encodedValue">
<type class="byte" dimension="1"/>
<description>
byte array containing Asn-encoded value to write.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.writeProperty(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, byte[]) -->
<method name="writeProperty"  public="true" abstract="true">
<description>
Issue a WriteProperty service request with no priority or index.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the address of the remote device to write to.
</description>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the object-identifier of the remote object.
</description>
</parameter>
<parameter name="propertyId">
<type class="int"/>
<description>
the property-identifier of the desired property.
</description>
</parameter>
<parameter name="encodedValue">
<type class="byte" dimension="1"/>
<description>
byte array containing Asn-encoded value to write.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.writeProperty(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int, byte[], int) -->
<method name="writeProperty"  public="true" abstract="true">
<description>
Issue a WriteProperty service request.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the address of the remote device to write to.
</description>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the object-identifier of the remote object.
</description>
</parameter>
<parameter name="propertyId">
<type class="int"/>
<description>
the property-identifier of the desired property.
</description>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
<description>
the array index, if specified.
</description>
</parameter>
<parameter name="encodedValue">
<type class="byte" dimension="1"/>
<description>
byte array containing Asn-encoded value to write.
</description>
</parameter>
<parameter name="priorityLevel">
<type class="int"/>
<description>
the priority level, if specified.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.writePropertyMultiple(javax.baja.bacnet.datatypes.BBacnetAddress, java.util.Vector) -->
<method name="writePropertyMultiple"  public="true" abstract="true">
<annotation><type class="java.lang.SuppressWarnings"/>
<elementValue name="value">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="expr">
<expression>&#x22;rawtypes&#x22;</expression>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<description>
WritePropertyMultiple is not used by Niagara, but it is required for&#xa; BACnet B-OWS compliance, so we make the best effort we can to send this&#xa; type of request.  By setting the useWritePropertyMultiple flag to true,&#xa; writes will use this format instead of the WriteProperty format.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<parameter name="writeAccessSpecs">
<parameterizedType class="java.util.Vector">
<args>
</args>
</parameterizedType>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.deviceCommunicationControl(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.enums.BBacnetCommControl, javax.baja.sys.BRelTime, java.lang.String, javax.baja.bacnet.enums.BCharacterSetEncoding) -->
<method name="deviceCommunicationControl"  public="true" abstract="true">
<description>
Issue a DeviceCommunicationControl Request.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description/>
</parameter>
<parameter name="enableDisable">
<type class="javax.baja.bacnet.enums.BBacnetCommControl"/>
<description/>
</parameter>
<parameter name="duration">
<type class="javax.baja.sys.BRelTime"/>
<description/>
</parameter>
<parameter name="password">
<type class="java.lang.String"/>
<description/>
</parameter>
<parameter name="encoding">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.confirmedPrivateTransfer(javax.baja.bacnet.datatypes.BBacnetAddress, int, int, byte[]) -->
<method name="confirmedPrivateTransfer"  public="true" abstract="true">
<description>
Issue a ConfirmedPrivateTransfer Request.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description/>
</parameter>
<parameter name="vendorId">
<type class="int"/>
<description/>
</parameter>
<parameter name="serviceNumber">
<type class="int"/>
<description/>
</parameter>
<parameter name="serviceParameters">
<type class="byte" dimension="1"/>
<description/>
</parameter>
<return>
<type class="byte" dimension="1"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.reinitializeDevice(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.enums.BBacnetReinitializedDeviceState, java.lang.String, javax.baja.bacnet.enums.BCharacterSetEncoding) -->
<method name="reinitializeDevice"  public="true" abstract="true">
<description>
Issue a ReinitializeDevice Request.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<parameter name="reinitializedStateOfDevice">
<type class="javax.baja.bacnet.enums.BBacnetReinitializedDeviceState"/>
</parameter>
<parameter name="password">
<type class="java.lang.String"/>
</parameter>
<parameter name="encoding">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.iAm() -->
<method name="iAm"  public="true" abstract="true">
<description>
Issue an I-Am request.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.iHave(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, java.lang.String, javax.baja.bacnet.enums.BCharacterSetEncoding) -->
<method name="iHave"  public="true" abstract="true">
<description>
Issue an I-Have request.
</description>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<parameter name="objectName">
<type class="java.lang.String"/>
<description/>
</parameter>
<parameter name="encoding">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.unconfirmedCovNotification(javax.baja.bacnet.datatypes.BBacnetAddress, long, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, long, javax.baja.bacnet.io.PropertyValue[]) -->
<method name="unconfirmedCovNotification"  public="true" abstract="true">
<description>
Issue an UnconfirmedCovNotification request.
</description>
<parameter name="address">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<parameter name="subscriberProcessId">
<type class="long"/>
</parameter>
<parameter name="initiatingDeviceId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="monitoredObjectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="timeRemaining">
<type class="long"/>
</parameter>
<parameter name="listOfValues">
<type class="javax.baja.bacnet.io.PropertyValue" dimension="1"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.unconfirmedEventNotification(javax.baja.bacnet.datatypes.BBacnetAddress, long, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, javax.baja.bacnet.datatypes.BBacnetTimeStamp, long, int, javax.baja.sys.BEnum, java.lang.String, javax.baja.bacnet.enums.BBacnetNotifyType, boolean, javax.baja.sys.BEnum, javax.baja.sys.BEnum, com.tridium.bacnet.asn.BacnetNotificationParameters, javax.baja.bacnet.enums.BCharacterSetEncoding) -->
<method name="unconfirmedEventNotification"  public="true" abstract="true">
<description>
Issue an UnconfirmedEventNotification request.
</description>
<parameter name="address">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<parameter name="processId">
<type class="long"/>
</parameter>
<parameter name="initiatingDeviceId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="eventObjectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<parameter name="timeStamp">
<type class="javax.baja.bacnet.datatypes.BBacnetTimeStamp"/>
</parameter>
<parameter name="notificationClass">
<type class="long"/>
</parameter>
<parameter name="priority">
<type class="int"/>
</parameter>
<parameter name="eventType">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<parameter name="messageText">
<type class="java.lang.String"/>
</parameter>
<parameter name="notifyType">
<type class="javax.baja.bacnet.enums.BBacnetNotifyType"/>
</parameter>
<parameter name="ackRequired">
<type class="boolean"/>
</parameter>
<parameter name="fromState">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<parameter name="toState">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<parameter name="eventValues">
<type class="com.tridium.bacnet.asn.BacnetNotificationParameters"/>
</parameter>
<parameter name="encoding">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.unconfirmedPrivateTransfer(javax.baja.bacnet.datatypes.BBacnetAddress, int, int, byte[]) -->
<method name="unconfirmedPrivateTransfer"  public="true" abstract="true">
<description>
Issue an UnconfirmedPrivateTransfer Request.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description/>
</parameter>
<parameter name="vendorId">
<type class="int"/>
<description/>
</parameter>
<parameter name="serviceNumber">
<type class="int"/>
<description/>
</parameter>
<parameter name="serviceParameters">
<type class="byte" dimension="1"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.timeSynch(javax.baja.bacnet.datatypes.BBacnetRecipient) -->
<method name="timeSynch"  public="true" abstract="true">
<description>
Send a TimeSynchronization message to the given address.
</description>
<parameter name="recipient">
<type class="javax.baja.bacnet.datatypes.BBacnetRecipient"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.whoHas(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="whoHas"  public="true" abstract="true">
<description>
Broadcast a Who-Has message with object Id, and no instance limits.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the target device address.
</description>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the desired object id.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.whoHas(javax.baja.bacnet.datatypes.BBacnetAddress, javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int) -->
<method name="whoHas"  public="true" abstract="true">
<description>
Broadcast a Who-Has message with instance limits.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the target device address.
</description>
</parameter>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
the desired object id.
</description>
</parameter>
<parameter name="lowLimit">
<type class="int"/>
<description>
device instance range low limit.
</description>
</parameter>
<parameter name="highLimit">
<type class="int"/>
<description>
device instance range high limit.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.whoHas(javax.baja.bacnet.datatypes.BBacnetAddress, java.lang.String, javax.baja.bacnet.enums.BCharacterSetEncoding) -->
<method name="whoHas"  public="true" abstract="true">
<description>
Broadcast a Who-Has message with object Id, and no instance limits.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the target device address.
</description>
</parameter>
<parameter name="objectName">
<type class="java.lang.String"/>
<description>
the desired object name.
</description>
</parameter>
<parameter name="charset">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.whoHas(javax.baja.bacnet.datatypes.BBacnetAddress, java.lang.String, javax.baja.bacnet.enums.BCharacterSetEncoding, int, int) -->
<method name="whoHas"  public="true" abstract="true">
<description>
Broadcast a Who-Has message with instance limits.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the target device address.
</description>
</parameter>
<parameter name="objectName">
<type class="java.lang.String"/>
<description>
the desired object name.
</description>
</parameter>
<parameter name="charset">
<type class="javax.baja.bacnet.enums.BCharacterSetEncoding"/>
</parameter>
<parameter name="lowLimit">
<type class="int"/>
<description>
device instance range low limit.
</description>
</parameter>
<parameter name="highLimit">
<type class="int"/>
<description>
device instance range high limit.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.whoIs(javax.baja.bacnet.datatypes.BBacnetAddress) -->
<method name="whoIs"  public="true" abstract="true">
<description>
Broadcast a Who-Is message with no instance limits.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
<description>
the target device address.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.whoIs(javax.baja.bacnet.datatypes.BBacnetAddress, int, int) -->
<method name="whoIs"  public="true" abstract="true">
<description>
Broadcast a Who-Is message with instance limits.
</description>
<parameter name="deviceAddress">
<type class="javax.baja.bacnet.datatypes.BBacnetAddress"/>
</parameter>
<parameter name="lowLimit">
<type class="int"/>
<description>
device instance range low limit.
</description>
</parameter>
<parameter name="highLimit">
<type class="int"/>
<description>
device instance range high limit.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.utcTimeSynch(javax.baja.bacnet.datatypes.BBacnetRecipient) -->
<method name="utcTimeSynch"  public="true" abstract="true">
<description>
Send a UTCTimeSynchronization message to the given address.
</description>
<parameter name="recipient">
<type class="javax.baja.bacnet.datatypes.BBacnetRecipient"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.BacnetException"/>
</throws>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.registerBacnetListener(javax.baja.bacnet.io.BacnetServiceListener, int) -->
<method name="registerBacnetListener"  public="true" abstract="true">
<description>
Register the given listener to receive services of the specified&#xa; service type.&#xa; The serviceType is specified in Clause 21 of the BACnet Specification,&#xa; under the BACnetServicesSupported bit string definition.&#xa; The listener must implement the appropriate listener&#xa; interface to receive the specified service type.  For example,&#xa; if you wish to receive ConfirmedPrivateTransfer requests, the&#xa; listener supplied must be a PrivateTransferListener.
</description>
<parameter name="listener">
<type class="javax.baja.bacnet.io.BacnetServiceListener"/>
<description>
a BacnetServiceListener of the appropriate type
</description>
</parameter>
<parameter name="serviceIndex">
<type class="int"/>
<description>
the service type, as identified in BacnetServicesSupported
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.unregisterBacnetListener(javax.baja.bacnet.io.BacnetServiceListener, int) -->
<method name="unregisterBacnetListener"  public="true" abstract="true">
<description>
Unregister the given listener from receiving services of the specified&#xa; service type.&#xa; The serviceType is specified in Clause 21 of the BACnet Specification,&#xa; under the BACnetServicesSupported bit string definition.&#xa; The listener must implement the appropriate listener&#xa; interface to receive the specified service type.  For example,&#xa; if you wish to receive ConfirmedPrivateTransfer requests, the&#xa; listener supplied must be a PrivateTransferListener.
</description>
<parameter name="listener">
<type class="javax.baja.bacnet.io.BacnetServiceListener"/>
<description>
a BacnetServiceListener of the appropriate type
</description>
</parameter>
<parameter name="serviceIndex">
<type class="int"/>
<description>
the service type, as identified in BacnetServicesSupported
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.getIcon() -->
<method name="getIcon"  public="true">
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.bacnet.io.BBacnetComm.commControl -->
<field name="commControl"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;commControl&lt;/code&gt; property.
</description>
<tag name="@see">#getCommControl</tag>
<tag name="@see">#setCommControl</tag>
</field>

<!-- javax.baja.bacnet.io.BBacnetComm.enableComm -->
<field name="enableComm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;enableComm&lt;/code&gt; action.
</description>
<tag name="@see">#enableComm()</tag>
</field>

<!-- javax.baja.bacnet.io.BBacnetComm.disableInitiation -->
<field name="disableInitiation"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;disableInitiation&lt;/code&gt; action.
</description>
<tag name="@see">#disableInitiation()</tag>
</field>

<!-- javax.baja.bacnet.io.BBacnetComm.disableComm -->
<field name="disableComm"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;disableComm&lt;/code&gt; action.
</description>
<tag name="@see">#disableComm()</tag>
</field>

<!-- javax.baja.bacnet.io.BBacnetComm.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
