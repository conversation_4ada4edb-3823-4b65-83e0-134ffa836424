<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.job.BPupDiscoveryDevice" name="BPupDiscoveryDevice" packageName="com.tridium.aapup.job" public="true">
<description>
PUP Discovery Device.
</description>
<tag name="@author">C<PERSON><PERSON></tag>
<tag name="@creation">7/25/2005 11:58AM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.91</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="unitNumber" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;unitNumber&lt;/code&gt; property.&#xa; the address of the discovered device
</description>
<tag name="@see">#getUnitNumber</tag>
<tag name="@see">#setUnitNumber</tag>
</property>

<property name="manufacturer" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;manufacturer&lt;/code&gt; property.&#xa; the manufacturer code read back from the discovered device
</description>
<tag name="@see">#getManufacturer</tag>
<tag name="@see">#setManufacturer</tag>
</property>

<property name="controllerType" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;controllerType&lt;/code&gt; property.&#xa; the controller type code read back from the discovered device
</description>
<tag name="@see">#getControllerType</tag>
<tag name="@see">#setControllerType</tag>
</property>

<property name="controllerDescription" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;controllerDescription&lt;/code&gt; property.&#xa; the controller description read from a deviceTypes file determined by&#xa; using manufacturer and controllerType properties
</description>
<tag name="@see">#getControllerDescription</tag>
<tag name="@see">#setControllerDescription</tag>
</property>

<property name="peerType" flags="">
<type class="com.tridium.aapup.enums.BPupPeerTypeEnum"/>
<description>
Slot for the &lt;code&gt;peerType&lt;/code&gt; property.&#xa; the peer type read back from the discovered device
</description>
<tag name="@see">#getPeerType</tag>
<tag name="@see">#setPeerType</tag>
</property>

<property name="version" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;version&lt;/code&gt; property.&#xa; defines whether or not version 8 PUP protocol is supported
</description>
<tag name="@see">#getVersion</tag>
<tag name="@see">#setVersion</tag>
</property>

</class>
</bajadoc>
