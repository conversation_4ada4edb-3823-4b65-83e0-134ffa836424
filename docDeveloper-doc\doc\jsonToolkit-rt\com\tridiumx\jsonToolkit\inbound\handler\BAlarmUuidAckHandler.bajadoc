<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.handler.BAlarmUuidAckHandler" name="BAlarmUuidAckHandler" packageName="com.tridiumx.jsonToolkit.inbound.handler" public="true">
<description>
BAlarmUuidAckHandler tries to acknowledge alarms in the stations alarmDb matching the&#xa; uuid(s) provided&#xa;&#xa; {user: &#x22;shaun&#x22;,alarms: [ &#x22;5cf9c8b2-1542-42ba-a1fd-5f753c777bc0&#x22; ]}&#xa;&#xa; **result** This topic report the results of alarm ack to allow logging or post process activity. Example Output:&#xa;    - `&#x22;Ack-ed alarm &#x22; + record`&#xa;    - `&#x22;Already ack-ed in alarmDb &#x22; + record`&#xa;    - `&#x22;Could not create BUuid from &#x22; + uuid`
</description>
<tag name="@author">Jason Woollard</tag>
<extends>
<type class="com.tridiumx.jsonToolkit.inbound.handler.BJsonHandler"/>
</extends>
<property name="ackSource" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;ackSource&lt;/code&gt; property.&#xa; **AckSource** is a String appended to every AlarmRecord acknowledged, to allow auditing in future.&#xa; It is stored as AckSource in the alarm data.
</description>
<tag name="@see">#getAckSource</tag>
<tag name="@see">#setAckSource</tag>
</property>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BAlarmUuidAckHandler() -->
<constructor name="BAlarmUuidAckHandler" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BAlarmUuidAckHandler.getAckSource() -->
<method name="getAckSource"  public="true">
<description>
Get the &lt;code&gt;ackSource&lt;/code&gt; property.&#xa; **AckSource** is a String appended to every AlarmRecord acknowledged, to allow auditing in future.&#xa; It is stored as AckSource in the alarm data.
</description>
<tag name="@see">#ackSource</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BAlarmUuidAckHandler.setAckSource(java.lang.String) -->
<method name="setAckSource"  public="true">
<description>
Set the &lt;code&gt;ackSource&lt;/code&gt; property.&#xa; **AckSource** is a String appended to every AlarmRecord acknowledged, to allow auditing in future.&#xa; It is stored as AckSource in the alarm data.
</description>
<tag name="@see">#ackSource</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BAlarmUuidAckHandler.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BAlarmUuidAckHandler.routeValue(javax.baja.sys.BString, javax.baja.sys.Context) -->
<method name="routeValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Accepts one UUID, or a JSON Array of UUID to try and Ack,&#xa; Try and ack these alarms - Always send an array&#xa;&#xa;  { user: [ uuid1, uuid2, ... ] }
</description>
<parameter name="alarmAckInput">
<type class="javax.baja.sys.BString"/>
<description>
A comma separated list of uuid&#x27;s to ack, with or without JSON array framing.
</description>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="com.tridiumx.jsonToolkit.inbound.routing.RoutingFailedException"/>
</throws>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BAlarmUuidAckHandler.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BAlarmUuidAckHandler.ackSource -->
<field name="ackSource"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;ackSource&lt;/code&gt; property.&#xa; **AckSource** is a String appended to every AlarmRecord acknowledged, to allow auditing in future.&#xa; It is stored as AckSource in the alarm data.
</description>
<tag name="@see">#getAckSource</tag>
<tag name="@see">#setAckSource</tag>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.handler.BAlarmUuidAckHandler.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
