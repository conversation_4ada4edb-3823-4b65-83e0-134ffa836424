<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.AlarmException" name="AlarmException" packageName="javax.baja.alarm" public="true" category="exception">
<description>
Base exception class for alarm.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">22 Sep 2004</tag>
<tag name="@version">$Revision: 1$ $Date: 10/1/04 3:21:16 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BajaRuntimeException"/>
</extends>
<!-- javax.baja.alarm.AlarmException(java.lang.String, java.lang.Throwable) -->
<constructor name="AlarmException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<parameter name="e">
<type class="java.lang.Throwable"/>
</parameter>
<description>
Constructor with specified message and nested exception.
</description>
</constructor>

<!-- javax.baja.alarm.AlarmException(java.lang.Throwable) -->
<constructor name="AlarmException" public="true">
<parameter name="e">
<type class="java.lang.Throwable"/>
</parameter>
<description>
Constructor with specified nested exception.
</description>
</constructor>

<!-- javax.baja.alarm.AlarmException(java.lang.String) -->
<constructor name="AlarmException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<description>
Construct a AlarmException with the given message.
</description>
</constructor>

<!-- javax.baja.alarm.AlarmException() -->
<constructor name="AlarmException" public="true">
<description>
No argument constructor.
</description>
</constructor>

</class>
</bajadoc>
