<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.job.BDeviceManagerJob" name="BDeviceManagerJob" packageName="com.tridium.bacnet.job" public="true" abstract="true">
<description>
BDeviceManagerJob is the base class for jobs submitted by the&#xa; device manager GUI.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">07 Sep 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.job.BSimpleJob"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
</class>
</bajadoc>
