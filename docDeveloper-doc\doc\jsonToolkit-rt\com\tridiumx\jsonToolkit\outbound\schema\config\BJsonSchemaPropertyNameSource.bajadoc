<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource" name="BJsonSchemaPropertyNameSource" packageName="com.tridiumx.jsonToolkit.outbound.schema.config" public="true" final="true">
<description>
Different available options for what name to use for json keys for pairs within a bound object.&#xa;&#xa; displayName - uses the displayName of the target slot&#xa; name - the name of of the target slot
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;displayName&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;name&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource.DISPLAY_NAME -->
<field name="DISPLAY_NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for displayName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource.NAME -->
<field name="NAME"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for name.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource.displayName -->
<field name="displayName"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource"/>
<description>
BJsonSchemaPropertyNameSource constant for displayName.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource.name -->
<field name="name"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource"/>
<description>
BJsonSchemaPropertyNameSource constant for name.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaPropertyNameSource.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
