<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.io.BacnetServiceListener" name="BacnetServiceListener" packageName="javax.baja.bacnet.io" public="true" interface="true" abstract="true" category="interface">
<description>
BacnetServiceListener is the root interface for all Listener&#xa; interfaces that listen for BACnet service requests.
</description>
</class>
</bajadoc>
