<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="platHwScan" runtimeProfile="rt" qualifiedName="com.tridium.platHwScan.enums.BLeftRightBottomTopEnum" name="BLeftRightBottomTopEnum" packageName="com.tridium.platHwScan.enums" public="true" final="true">
<description>
BLeftRightBottomTopEnum has enumerations for left, right, down and up.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">12 Jan 12</tag>
<tag name="@version">$Revision: $ $Date: $</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;left&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;right&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bottom&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;top&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- com.tridium.platHwScan.enums.BLeftRightBottomTopEnum.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="com.tridium.platHwScan.enums.BLeftRightBottomTopEnum"/>
</return>
</method>

<!-- com.tridium.platHwScan.enums.BLeftRightBottomTopEnum.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridium.platHwScan.enums.BLeftRightBottomTopEnum"/>
</return>
</method>

<!-- com.tridium.platHwScan.enums.BLeftRightBottomTopEnum.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.platHwScan.enums.BLeftRightBottomTopEnum.LEFT -->
<field name="LEFT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for left.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BLeftRightBottomTopEnum.RIGHT -->
<field name="RIGHT"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for right.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BLeftRightBottomTopEnum.BOTTOM -->
<field name="BOTTOM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bottom.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BLeftRightBottomTopEnum.TOP -->
<field name="TOP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for top.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BLeftRightBottomTopEnum.left -->
<field name="left"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BLeftRightBottomTopEnum"/>
<description>
BLeftRightBottomTopEnum constant for left.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BLeftRightBottomTopEnum.right -->
<field name="right"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BLeftRightBottomTopEnum"/>
<description>
BLeftRightBottomTopEnum constant for right.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BLeftRightBottomTopEnum.bottom -->
<field name="bottom"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BLeftRightBottomTopEnum"/>
<description>
BLeftRightBottomTopEnum constant for bottom.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BLeftRightBottomTopEnum.top -->
<field name="top"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BLeftRightBottomTopEnum"/>
<description>
BLeftRightBottomTopEnum constant for top.
</description>
</field>

<!-- com.tridium.platHwScan.enums.BLeftRightBottomTopEnum.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="com.tridium.platHwScan.enums.BLeftRightBottomTopEnum"/>
<description/>
</field>

<!-- com.tridium.platHwScan.enums.BLeftRightBottomTopEnum.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
