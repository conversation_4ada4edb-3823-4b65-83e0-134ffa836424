<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.SlotPath" name="SlotPath" packageName="javax.baja.naming" public="true">
<description>
SlotPath is a ord scheme for resolving BValues&#xa; using slot names.  The BNF is:&#xa; &lt;pre&gt;&#xa;   slotpath  := absolute | relative&#xa;   absolute  := &#x22;/&#x22; path&#xa;   relative  := backup | path&#xa;   backup    := ( &#x22;../&#x22; )* path&#xa;   path      := name [ &#x22;/&#x22; path]&#xa;   name      := nameStart (namePart)*&#xa;&#xa;   nameStart  := alpha | escape&#xa;   namePart   := alpha | digit | safe | escape&#xa;   safe       := &#x22;_&#x22;&#xa;   alpha      := &#x22;a&#x22;-&#x22;z&#x22; | &#x22;A-Z&#x22;&#xa;   digit      := &#x22;0&#x22;-&#x22;9&#x22;&#xa;   escape     := asciiEsc | unicodeEsc&#xa;   asciiEsc   := &#x22;$&#x22; hex hex&#xa;   unicodeEsc := &#x22;$u&#x22; hex hex hex hex&#xa;   hex        := &#x27;a&#x27;-&#x27;f&#x27; | &#x27;A&#x27;-&#x27;F&#x27; | digit&#xa; &lt;/pre&gt;
</description>
<tag name="@author">Brian Frank</tag>
<tag name="@version">$Revision: 21$ $Date: 11/30/06 6:08:15 PM EST$</tag>
<tag name="@creation">8 Jan 03</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="java.lang.Object"/>
</extends>
<implements>
<type class="javax.baja.naming.OrdQuery"/>
</implements>
<implements>
<type class="javax.baja.naming.Path"/>
</implements>
<!-- javax.baja.naming.SlotPath(java.lang.String, java.lang.String) -->
<constructor name="SlotPath" public="true">
<parameter name="scheme">
<type class="java.lang.String"/>
</parameter>
<parameter name="body">
<type class="java.lang.String"/>
</parameter>
<throws>
<type class="javax.baja.naming.SyntaxException"/>
<description>
if the body isn&#x27;t a valid file path.
</description>
</throws>
<description>
Construct an SlotPath with the specified scheme and body.
</description>
</constructor>

<!-- javax.baja.naming.SlotPath(java.lang.String, java.lang.String[]) -->
<constructor name="SlotPath" public="true">
<parameter name="scheme">
<type class="java.lang.String"/>
</parameter>
<parameter name="names">
<type class="java.lang.String" dimension="1"/>
</parameter>
<throws>
<type class="javax.baja.naming.SyntaxException"/>
</throws>
<description>
Construct an SlotPath with the specified scheme and names.
</description>
</constructor>

<!-- javax.baja.naming.SlotPath(java.lang.String) -->
<constructor name="SlotPath" public="true">
<parameter name="body">
<type class="java.lang.String"/>
</parameter>
<throws>
<type class="javax.baja.naming.SyntaxException"/>
</throws>
<description>
Convenience with &#x22;slot&#x22; scheme.
</description>
</constructor>

<!-- javax.baja.naming.SlotPath.makeSlotPath(java.lang.String, java.lang.String) -->
<method name="makeSlotPath"  protected="true">
<description>
Creates a new SlotPath instance for the given scheme and body.&#xa; Allows subclasses a chance to create new SlotPath instances.
</description>
<tag name="@since">Niagara 3.2</tag>
<parameter name="scheme">
<type class="java.lang.String"/>
</parameter>
<parameter name="body">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.naming.SlotPath"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.isAbsolute() -->
<method name="isAbsolute"  public="true">
<description>
Return true if this slot path is absolute&#xa; starting with a leading slash.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.isRelative() -->
<method name="isRelative"  public="true">
<description>
Return inverse of isAbsolute.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.getBackupDepth() -->
<method name="getBackupDepth"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the number of leading &#x22;../&#x22; indicating a&#xa; relative backup.  If this path is absolute or&#xa; directory relative then return 0.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.getParentPath() -->
<method name="getParentPath"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Convenience for &lt;code&gt;getParent()&lt;/code&gt;.
</description>
<return>
<type class="javax.baja.naming.Path"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.getParent() -->
<method name="getParent"  public="true">
<description>
Return a SlotPath for the parent path or null if&#xa; there is no parent.
</description>
<return>
<type class="javax.baja.naming.SlotPath"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.depth() -->
<method name="depth"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the number of names in the path after the&#xa; absolute or relative backup prefix.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.nameAt(int) -->
<method name="nameAt"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the name at the zero based index between 0 and depth()-1.
</description>
<parameter name="depth">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.getNames() -->
<method name="getNames"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get a copy of the names array.
</description>
<return>
<type class="java.lang.String" dimension="1"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.makePath(java.lang.String) -->
<method name="makePath"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<tag name="@since">Niagara 4.3U1</tag>
<parameter name="body">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdQuery"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.isValidPathName(java.lang.String) -->
<method name="isValidPathName"  protected="true">
<description>
Does the specified string contain a valid name.&#xa; Allows subclasses a chance to check for a valid path name.
</description>
<tag name="@since">Niagara 3.2</tag>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.isValidName(java.lang.String) -->
<method name="isValidName"  public="true" static="true">
<description>
Does the specified string contain a valid name.
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.verifyValidName(java.lang.String) -->
<method name="verifyValidName"  public="true" static="true">
<description>
If the specified name is invalid then throw an IllegalNameException.
</description>
<parameter name="name">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.escape(java.lang.String) -->
<method name="escape"  public="true" static="true">
<description>
Escape the specified string.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.unescape(java.lang.String) -->
<method name="unescape"  public="true" static="true">
<description>
Unescape the specified string.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.isHost() -->
<method name="isHost"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return false.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.isSession() -->
<method name="isSession"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return false.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.getScheme() -->
<method name="getScheme"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return the scheme field.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.getBody() -->
<method name="getBody"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Return the body field.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.normalize(javax.baja.naming.OrdQueryList, int) -->
<method name="normalize"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
If the query at index+1 is also a SlotPath, then perform&#xa; a merge using the &lt;code&gt;merge()&lt;/code&gt; method.
</description>
<parameter name="list">
<type class="javax.baja.naming.OrdQueryList"/>
</parameter>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.merge(javax.baja.naming.SlotPath) -->
<method name="merge"  public="true">
<description>
Merge this path with the specified path.  If the specified&#xa; path is absolute, then return it since it trumps this one.&#xa; If the specified path is relative then create a new&#xa; merged path taking in account any backup.
</description>
<parameter name="a">
<type class="javax.baja.naming.SlotPath"/>
</parameter>
<return>
<type class="javax.baja.naming.SlotPath"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.toDisplayString() -->
<method name="toDisplayString"  public="true">
<description>
Return the body with path names unescaped.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.toString() -->
<method name="toString"  public="true">
<description>
Return &lt;code&gt;scheme + &amp;#x22;:&amp;#x22; + body&lt;/code&gt;.
</description>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.naming.SlotPath.EMPTY_SLOT_PATH -->
<field name="EMPTY_SLOT_PATH"  public="true" static="true" final="true">
<type class="javax.baja.naming.SlotPath"/>
<description>
A SlotPath constant with an empty body String
</description>
<tag name="@since">Niagara 4.13</tag>
</field>

</class>
</bajadoc>
