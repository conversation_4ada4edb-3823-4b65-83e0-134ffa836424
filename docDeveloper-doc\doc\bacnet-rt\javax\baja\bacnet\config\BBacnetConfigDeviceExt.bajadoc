<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.config.BBacnetConfigDeviceExt" name="BBacnetConfigDeviceExt" packageName="javax.baja.bacnet.config" public="true">
<description>
BBacnetConfigDeviceExt represents the configuration representation of a&#xa; Bacnet device.&#xa; &lt;p&gt;&#xa; This contains a BBacnetDeviceObject, which contains all the properties&#xa; of a Bacnet Device object as defined by the Bacnet specification.&#xa; &lt;p&gt;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">04 Jan 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.driver.BDeviceExt"/>
</extends>
<implements>
<type class="javax.baja.driver.loadable.BILoadable"/>
</implements>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<implements>
<type class="javax.baja.bacnet.BIBacnetObjectContainer"/>
</implements>
<implements>
<type class="javax.baja.bacnet.config.BIBacnetConfigFolder"/>
</implements>
<property name="deviceObject" flags="">
<type class="javax.baja.bacnet.config.BBacnetDeviceObject"/>
<description>
Slot for the &lt;code&gt;deviceObject&lt;/code&gt; property.&#xa; deviceObject contains the Bacnet parameters for this Bacnet&#xa; Device, such as the vendor name and ID, model name, and the&#xa; segmentation support.
</description>
<tag name="@see">#getDeviceObject</tag>
<tag name="@see">#setDeviceObject</tag>
</property>

<action name="upload" flags="a">
<parameter name="parameter">
<type class="javax.baja.driver.loadable.BUploadParameters"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;upload&lt;/code&gt; action.&#xa; Upload reads data from the physical device.
</description>
<tag name="@see">#upload(BUploadParameters parameter)</tag>
</action>

<action name="download" flags="a">
<parameter name="parameter">
<type class="javax.baja.driver.loadable.BDownloadParameters"/>
</parameter>
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;download&lt;/code&gt; action.&#xa; Download writes data to the physical device.
</description>
<tag name="@see">#download(BDownloadParameters parameter)</tag>
</action>

<action name="submitConfigDiscoveryJob" flags="h">
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;submitConfigDiscoveryJob&lt;/code&gt; action.
</description>
<tag name="@see">#submitConfigDiscoveryJob()</tag>
</action>

<action name="getConfigTypes" flags="h">
<return>
<type class="javax.baja.sys.BString"/>
</return>
<description>
Slot for the &lt;code&gt;getConfigTypes&lt;/code&gt; action.
</description>
<tag name="@see">#getConfigTypes()</tag>
</action>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt() -->
<constructor name="BBacnetConfigDeviceExt" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<constructor name="BBacnetConfigDeviceExt" public="true">
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<description/>
</constructor>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.getDeviceObject() -->
<method name="getDeviceObject"  public="true">
<description>
Get the &lt;code&gt;deviceObject&lt;/code&gt; property.&#xa; deviceObject contains the Bacnet parameters for this Bacnet&#xa; Device, such as the vendor name and ID, model name, and the&#xa; segmentation support.
</description>
<tag name="@see">#deviceObject</tag>
<return>
<type class="javax.baja.bacnet.config.BBacnetDeviceObject"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.setDeviceObject(javax.baja.bacnet.config.BBacnetDeviceObject) -->
<method name="setDeviceObject"  public="true">
<description>
Set the &lt;code&gt;deviceObject&lt;/code&gt; property.&#xa; deviceObject contains the Bacnet parameters for this Bacnet&#xa; Device, such as the vendor name and ID, model name, and the&#xa; segmentation support.
</description>
<tag name="@see">#deviceObject</tag>
<parameter name="v">
<type class="javax.baja.bacnet.config.BBacnetDeviceObject"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.upload(javax.baja.driver.loadable.BUploadParameters) -->
<method name="upload"  public="true">
<description>
Invoke the &lt;code&gt;upload&lt;/code&gt; action.&#xa; Upload reads data from the physical device.
</description>
<tag name="@see">#upload</tag>
<parameter name="parameter">
<type class="javax.baja.driver.loadable.BUploadParameters"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.download(javax.baja.driver.loadable.BDownloadParameters) -->
<method name="download"  public="true">
<description>
Invoke the &lt;code&gt;download&lt;/code&gt; action.&#xa; Download writes data to the physical device.
</description>
<tag name="@see">#download</tag>
<parameter name="parameter">
<type class="javax.baja.driver.loadable.BDownloadParameters"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.submitConfigDiscoveryJob() -->
<method name="submitConfigDiscoveryJob"  public="true">
<description>
Invoke the &lt;code&gt;submitConfigDiscoveryJob&lt;/code&gt; action.
</description>
<tag name="@see">#submitConfigDiscoveryJob</tag>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.getConfigTypes() -->
<method name="getConfigTypes"  public="true">
<description>
Invoke the &lt;code&gt;getConfigTypes&lt;/code&gt; action.
</description>
<tag name="@see">#getConfigTypes</tag>
<return>
<type class="javax.baja.sys.BString"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.getConfig() -->
<method name="getConfig"  public="true">
<description>
Get the parent network.
</description>
<return>
<type class="javax.baja.bacnet.config.BBacnetConfigDeviceExt"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.getObjectId() -->
<method name="getObjectId"  public="true">
<description/>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.network() -->
<method name="network"  public="true" final="true">
<description/>
<return>
<type class="javax.baja.bacnet.BBacnetNetwork"/>
<description>
the BBacnetNetwork containing this BBacnetObject.
</description>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.device() -->
<method name="device"  public="true" final="true">
<description/>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.getObjectList() -->
<method name="getObjectList"  public="true" synchronized="true">
<description>
Get the BBacnetObject list as an array.
</description>
<return>
<type class="javax.baja.bacnet.BBacnetObject" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.isParentLegal(javax.baja.sys.BComponent) -->
<method name="isParentLegal"  public="true" final="true">
<description>
BBacnetConfigDeviceExt must be contained directly in a BBacnetDevice.
</description>
<parameter name="parent">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.isChildLegal(javax.baja.sys.BComponent) -->
<method name="isChildLegal"  public="true" final="true">
<description>
BBacnetConfigDeviceExt may contain at most one device object.
</description>
<parameter name="child">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.lookupBacnetObject(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="lookupBacnetObject"  public="true">
<description>
Look up and return the Bacnet object with the given reference.
</description>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<return>
<type class="javax.baja.bacnet.BBacnetObject"/>
<description>
a BObject with the given reference parameters, or null if&#xa; this container does not contain any objects with the given parameters.
</description>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.lookupBacnetObject(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier, int, int, java.lang.String) -->
<method name="lookupBacnetObject"  public="true">
<description>
Look up and return the Bacnet object with the given reference.
</description>
<parameter name="objectId">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description/>
</parameter>
<parameter name="propertyId">
<type class="int"/>
<description/>
</parameter>
<parameter name="propertyArrayIndex">
<type class="int"/>
<description/>
</parameter>
<parameter name="domain">
<type class="java.lang.String"/>
<description>
the realm in which to look up the object: point, schedule, history
</description>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
<description>
a BObject with the given reference parameters, or null if&#xa; this container does not contain any objects with the given parameters.
</description>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.post(javax.baja.sys.Action, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="post"  public="true">
<description>
Route to postUpload or postDownload.
</description>
<parameter name="action">
<type class="javax.baja.sys.Action"/>
</parameter>
<parameter name="arg">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.util.IFuture"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.postUpload(javax.baja.driver.loadable.BUploadParameters, javax.baja.sys.Context) -->
<method name="postUpload"  protected="true">
<description>
Post an upload Invocation.
</description>
<parameter name="params">
<type class="javax.baja.driver.loadable.BUploadParameters"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.util.IFuture"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.postDownload(javax.baja.driver.loadable.BDownloadParameters, javax.baja.sys.Context) -->
<method name="postDownload"  protected="true">
<description>
Post a download Invocation.
</description>
<parameter name="params">
<type class="javax.baja.driver.loadable.BDownloadParameters"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.util.IFuture"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.postAsync(java.lang.Runnable) -->
<method name="postAsync"  public="true">
<description>
Convenience for &lt;code&gt;getNetwork().postAsync(r)&lt;/code&gt;.
</description>
<parameter name="r">
<type class="java.lang.Runnable"/>
</parameter>
<return>
<type class="javax.baja.util.IFuture"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.doSubmitConfigDiscoveryJob(javax.baja.sys.Context) -->
<method name="doSubmitConfigDiscoveryJob"  public="true">
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.doGetConfigTypes() -->
<method name="doGetConfigTypes"  public="true">
<description/>
<return>
<type class="javax.baja.sys.BString"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.doUpload(javax.baja.driver.loadable.BUploadParameters, javax.baja.sys.Context) -->
<method name="doUpload"  public="true">
<description>
Callback for processing upload on async thread.&#xa; Default implementation is to call doUpload on all&#xa; children implementing the Loadable interface.
</description>
<parameter name="p">
<type class="javax.baja.driver.loadable.BUploadParameters"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.doDownload(javax.baja.driver.loadable.BDownloadParameters, javax.baja.sys.Context) -->
<method name="doDownload"  public="true">
<description>
Callback for processing downLoad on async thread.&#xa; Default implementation is to call asyncDownload on all&#xa; children implementing the Loadable interface.
</description>
<parameter name="p">
<type class="javax.baja.driver.loadable.BDownloadParameters"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.getIcon() -->
<method name="getIcon"  public="true">
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.deviceObject -->
<field name="deviceObject"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;deviceObject&lt;/code&gt; property.&#xa; deviceObject contains the Bacnet parameters for this Bacnet&#xa; Device, such as the vendor name and ID, model name, and the&#xa; segmentation support.
</description>
<tag name="@see">#getDeviceObject</tag>
<tag name="@see">#setDeviceObject</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.upload -->
<field name="upload"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;upload&lt;/code&gt; action.&#xa; Upload reads data from the physical device.
</description>
<tag name="@see">#upload(BUploadParameters parameter)</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.download -->
<field name="download"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;download&lt;/code&gt; action.&#xa; Download writes data to the physical device.
</description>
<tag name="@see">#download(BDownloadParameters parameter)</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.submitConfigDiscoveryJob -->
<field name="submitConfigDiscoveryJob"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;submitConfigDiscoveryJob&lt;/code&gt; action.
</description>
<tag name="@see">#submitConfigDiscoveryJob()</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.getConfigTypes -->
<field name="getConfigTypes"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;getConfigTypes&lt;/code&gt; action.
</description>
<tag name="@see">#getConfigTypes()</tag>
</field>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.config.BBacnetConfigDeviceExt.log -->
<field name="log"  public="true" static="true" final="true">
<type class="java.util.logging.Logger"/>
<description/>
</field>

</class>
</bajadoc>
