<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaUnixTimeProperty" name="BJsonSchemaUnixTimeProperty" packageName="com.tridiumx.jsonToolkit.outbound.schema.property" public="true">
<description>
Outputs the current unix timestamp as a long numeric
</description>
<tag name="@author"><PERSON></tag>
<extends>
<parameterizedType class="com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaProperty">
<args>
<type class="java.lang.Long"/>
</args>
</parameterizedType>
</extends>
<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaUnixTimeProperty() -->
<constructor name="BJsonSchemaUnixTimeProperty" public="true">
<description/>
</constructor>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaUnixTimeProperty.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaUnixTimeProperty.getJsonValue() -->
<method name="getJsonValue"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="java.lang.Long"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaUnixTimeProperty.getIcon() -->
<method name="getIcon"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.BIcon"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.property.BJsonSchemaUnixTimeProperty.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
