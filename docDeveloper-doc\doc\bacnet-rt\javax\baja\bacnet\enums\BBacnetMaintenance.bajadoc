<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.BBacnetMaintenance" name="BBacnetMaintenance" packageName="javax.baja.bacnet.enums" public="true" final="true">
<description>
BBacnetMaintenance represents the Bacnet Maintenance&#xa; enumeration.&#xa; &lt;p&gt;&#xa; BBacnetMaintenance is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-255 are reserved for use by ASHRAE.&#xa; Values from 256-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Craig Gemmill</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">16 May 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;none&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;periodicTest&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;needServiceOperational&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;needServiceInoperative&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.BBacnetMaintenance.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetMaintenance"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.BBacnetMaintenance"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.NONE -->
<field name="NONE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for none.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.PERIODIC_TEST -->
<field name="PERIODIC_TEST"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for periodicTest.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.NEED_SERVICE_OPERATIONAL -->
<field name="NEED_SERVICE_OPERATIONAL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for needServiceOperational.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.NEED_SERVICE_INOPERATIVE -->
<field name="NEED_SERVICE_INOPERATIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for needServiceInoperative.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.none -->
<field name="none"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetMaintenance"/>
<description>
BBacnetMaintenance constant for none.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.periodicTest -->
<field name="periodicTest"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetMaintenance"/>
<description>
BBacnetMaintenance constant for periodicTest.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.needServiceOperational -->
<field name="needServiceOperational"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetMaintenance"/>
<description>
BBacnetMaintenance constant for needServiceOperational.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.needServiceInoperative -->
<field name="needServiceInoperative"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetMaintenance"/>
<description>
BBacnetMaintenance constant for needServiceInoperative.
</description>
</field>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.BBacnetMaintenance"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.BBacnetMaintenance.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
