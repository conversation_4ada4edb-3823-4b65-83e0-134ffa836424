<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.history.BBacnetBitStringTrendLogExt" name="BBacnetBitStringTrendLogExt" packageName="com.tridium.bacnet.history" public="true">
<description>
&lt;code&gt; BBacnetBitStringTrendLogExt &lt;/code&gt; represents the extension for trending bacnet bit string
</description>
<extends>
<type class="com.tridium.bacnet.history.BBacnetNumericIntervalTrendLogExt"/>
</extends>
<property name="propertyId" flags="r">
<type class="int"/>
<description>
Slot for the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyId</tag>
<tag name="@see">#setPropertyId</tag>
</property>

<property name="length" flags="rd">
<type class="int"/>
<description>
Slot for the &lt;code&gt;length&lt;/code&gt; property.
</description>
<tag name="@see">#getLength</tag>
<tag name="@see">#setLength</tag>
</property>

</class>
</bajadoc>
