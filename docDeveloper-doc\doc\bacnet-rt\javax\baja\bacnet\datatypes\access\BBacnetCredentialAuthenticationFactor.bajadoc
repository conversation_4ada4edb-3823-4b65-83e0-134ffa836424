<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.access.BBacnetCredentialAuthenticationFactor" name="BBacnetCredentialAuthenticationFactor" packageName="javax.baja.bacnet.datatypes.access" public="true" final="true">
<description>
BBacnetAssignedAccessRights represents the BACnetAssignedAccessRights&#xa; sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="disable" flags="">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessAuthenticationFactorDisable"/>
<description>
Slot for the &lt;code&gt;disable&lt;/code&gt; property.
</description>
<tag name="@see">#getDisable</tag>
<tag name="@see">#setDisable</tag>
</property>

<property name="authenticationFactor" flags="">
<type class="javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor"/>
<description>
Slot for the &lt;code&gt;authenticationFactor&lt;/code&gt; property.
</description>
<tag name="@see">#getAuthenticationFactor</tag>
<tag name="@see">#setAuthenticationFactor</tag>
</property>

<!-- javax.baja.bacnet.datatypes.access.BBacnetCredentialAuthenticationFactor() -->
<constructor name="BBacnetCredentialAuthenticationFactor" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.access.BBacnetCredentialAuthenticationFactor(javax.baja.bacnet.enums.access.BBacnetAccessAuthenticationFactorDisable, javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor) -->
<constructor name="BBacnetCredentialAuthenticationFactor" public="true">
<parameter name="disable">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessAuthenticationFactorDisable"/>
</parameter>
<parameter name="authFactor">
<type class="javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor"/>
</parameter>
<description>
Standard constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.access.BBacnetCredentialAuthenticationFactor.getDisable() -->
<method name="getDisable"  public="true">
<description>
Get the &lt;code&gt;disable&lt;/code&gt; property.
</description>
<tag name="@see">#disable</tag>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetAccessAuthenticationFactorDisable"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetCredentialAuthenticationFactor.setDisable(javax.baja.bacnet.enums.access.BBacnetAccessAuthenticationFactorDisable) -->
<method name="setDisable"  public="true">
<description>
Set the &lt;code&gt;disable&lt;/code&gt; property.
</description>
<tag name="@see">#disable</tag>
<parameter name="v">
<type class="javax.baja.bacnet.enums.access.BBacnetAccessAuthenticationFactorDisable"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetCredentialAuthenticationFactor.getAuthenticationFactor() -->
<method name="getAuthenticationFactor"  public="true">
<description>
Get the &lt;code&gt;authenticationFactor&lt;/code&gt; property.
</description>
<tag name="@see">#authenticationFactor</tag>
<return>
<type class="javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetCredentialAuthenticationFactor.setAuthenticationFactor(javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor) -->
<method name="setAuthenticationFactor"  public="true">
<description>
Set the &lt;code&gt;authenticationFactor&lt;/code&gt; property.
</description>
<tag name="@see">#authenticationFactor</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.access.BBacnetAuthenticationFactor"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetCredentialAuthenticationFactor.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetCredentialAuthenticationFactor.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetCredentialAuthenticationFactor.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetCredentialAuthenticationFactor.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.access.BBacnetCredentialAuthenticationFactor.disable -->
<field name="disable"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;disable&lt;/code&gt; property.
</description>
<tag name="@see">#getDisable</tag>
<tag name="@see">#setDisable</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetCredentialAuthenticationFactor.authenticationFactor -->
<field name="authenticationFactor"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;authenticationFactor&lt;/code&gt; property.
</description>
<tag name="@see">#getAuthenticationFactor</tag>
<tag name="@see">#setAuthenticationFactor</tag>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetCredentialAuthenticationFactor.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetCredentialAuthenticationFactor.DISABLE_TAG -->
<field name="DISABLE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.access.BBacnetCredentialAuthenticationFactor.AUTH_FACTOR_TAG -->
<field name="AUTH_FACTOR_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
