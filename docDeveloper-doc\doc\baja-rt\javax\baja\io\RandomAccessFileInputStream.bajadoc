<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.io.RandomAccessFileInputStream" name="RandomAccessFileInputStream" packageName="javax.baja.io" public="true">
<description>
RandomAccessFileInputStream is an input stream for reading from&#xa; a RandomAccessFile.  It manages the file pointer for the&#xa; RandomAccessFile internally so that file pointer is always in&#xa; the correct position for reading with respect to the input stream.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">10 Oct 2002</tag>
<tag name="@version">$Revision: 1$ $Date: 10/11/02 2:12:47 PM EDT$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="java.io.InputStream"/>
</extends>
<!-- javax.baja.io.RandomAccessFileInputStream(java.io.RandomAccessFile) -->
<constructor name="RandomAccessFileInputStream" public="true">
<parameter name="in">
<type class="java.io.RandomAccessFile"/>
</parameter>
<description>
Construct an input stream for the specified file.
</description>
</constructor>

<!-- javax.baja.io.RandomAccessFileInputStream(java.io.RandomAccessFile, long) -->
<constructor name="RandomAccessFileInputStream" public="true">
<parameter name="in">
<type class="java.io.RandomAccessFile"/>
</parameter>
<parameter name="initFp">
<type class="long"/>
</parameter>
<description>
Construct an input stream for the specified file.
</description>
</constructor>

<!-- javax.baja.io.RandomAccessFileInputStream.available() -->
<method name="available"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<tag name="@see">InputStream#available()</tag>
<return>
<type class="int"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.RandomAccessFileInputStream.close() -->
<method name="close"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
This method DOES NOT actually close the underlying file.  It is&#xa; assumed that if this class is being used, the file should not&#xa; be closed.  If this is not the case, a normal FileInputStream&#xa; can likely be used instead.
</description>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.RandomAccessFileInputStream.markSupported() -->
<method name="markSupported"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Mark is not supported on this type.
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.io.RandomAccessFileInputStream.seek(long) -->
<method name="seek"  public="true">
<description/>
<tag name="@see">java.io.RandomAccessFile#seek(long)</tag>
<parameter name="fp">
<type class="long"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.io.RandomAccessFileInputStream.read() -->
<method name="read"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<tag name="@see">InputStream#read()</tag>
<return>
<type class="int"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.RandomAccessFileInputStream.read(byte[]) -->
<method name="read"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<tag name="@see">java.io.InputStream#read(byte[])</tag>
<parameter name="b">
<type class="byte" dimension="1"/>
</parameter>
<return>
<type class="int"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.io.RandomAccessFileInputStream.read(byte[], int, int) -->
<method name="read"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<tag name="@see">java.io.InputStream#read(byte[], int, int)</tag>
<parameter name="b">
<type class="byte" dimension="1"/>
</parameter>
<parameter name="offset">
<type class="int"/>
</parameter>
<parameter name="len">
<type class="int"/>
</parameter>
<return>
<type class="int"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

</class>
</bajadoc>
