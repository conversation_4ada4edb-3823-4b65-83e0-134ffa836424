<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.virtual.BBacnetVirtualComponent" name="BBacnetVirtualComponent" packageName="javax.baja.bacnet.virtual" public="true">
<description>
BBacnetVirtualComponent is the implementation of BVirtualComponent for the&#xa; BACnet driver. It includes the syntax for indicating a fixed priority for&#xa; potential writes, optional declaration of a different tuning policy, and&#xa; specification of additional sources for status information.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">12 Dec 2006</tag>
<tag name="@since">Niagara 3.2</tag>
<tag name="@deprecated"/>
<extends>
<type class="javax.baja.virtual.BVirtualComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.util.BIBacnetPollable"/>
</implements>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<property name="facets" flags="">
<type class="javax.baja.sys.BFacets"/>
<description>
Slot for the &lt;code&gt;facets&lt;/code&gt; property.
</description>
<tag name="@see">#getFacets</tag>
<tag name="@see">#setFacets</tag>
</property>

<action name="subscribe" flags="ah">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;subscribe&lt;/code&gt; action.
</description>
<tag name="@see">#subscribe()</tag>
</action>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent() -->
<constructor name="BBacnetVirtualComponent" public="true">
<description>
Required no-arg constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent(java.lang.String) -->
<constructor name="BBacnetVirtualComponent" public="true">
<parameter name="virtualPathName">
<type class="java.lang.String"/>
</parameter>
<description>
The syntax for creating BacnetVirtualComponents is:&lt;p&gt;&#xa; &lt;code&gt;object_Id/propertyId/index&lt;/code&gt;&lt;p&gt;&#xa; For example,&lt;p&gt;&#xa; &lt;code&gt;analogInput_1/presentValue&lt;/code&gt;&#xa; &lt;code&gt;analogOutput_10/priorityArray/8&lt;/code&gt;&lt;p&gt;&lt;p&gt;&#xa; Optional entries may be added separated by semicolons:&lt;p&gt;&#xa; &lt;code&gt;priority=&lt;i&gt;X&lt;/i&gt;&lt;/code&gt; may be added after the objectId;&lt;p&gt;&#xa; &lt;code&gt;policy=&lt;i&gt;tpName&lt;/i&gt;&lt;/code&gt; may be added after the objectId;&lt;p&gt;&#xa; &lt;code&gt;status=&lt;i&gt;type_inst_propId_ndx_dev_inst&lt;/i&gt;&lt;/code&gt; may be added after the property name.
</description>
</constructor>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.getFacets() -->
<method name="getFacets"  public="true">
<description>
Get the &lt;code&gt;facets&lt;/code&gt; property.
</description>
<tag name="@see">#facets</tag>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.setFacets(javax.baja.sys.BFacets) -->
<method name="setFacets"  public="true">
<description>
Set the &lt;code&gt;facets&lt;/code&gt; property.
</description>
<tag name="@see">#facets</tag>
<parameter name="v">
<type class="javax.baja.sys.BFacets"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.subscribe() -->
<method name="subscribe"  public="true">
<description>
Invoke the &lt;code&gt;subscribe&lt;/code&gt; action.
</description>
<tag name="@see">#subscribe</tag>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.doSubscribe() -->
<method name="doSubscribe"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
Enqueues the invocation in the network object.&#xa; public IFuture post(Action a, BValue arg, Context cx)&#xa; {&#xa; network().postAsync(new Invocation(this,a,arg,cx));&#xa; return null;&#xa; }
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.isChildLegal(javax.baja.sys.BComponent) -->
<method name="isChildLegal"  public="true">
<description>
Override default behavior to allow non-virtual components.  Some&#xa; BACnet properties are represented in Niagara by BComponent datatypes.
</description>
<parameter name="c">
<type class="javax.baja.sys.BComponent"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.stopped() -->
<method name="stopped"  public="true">
<description/>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.added(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="added"  public="true">
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.removed(javax.baja.sys.Property, javax.baja.sys.BValue, javax.baja.sys.Context) -->
<method name="removed"  public="true">
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="oldValue">
<type class="javax.baja.sys.BValue"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.subscribed() -->
<method name="subscribed"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.unsubscribed() -->
<method name="unsubscribed"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<description/>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.getObjectId() -->
<method name="getObjectId"  protected="true">
<description>
ObjectId is stored as an instance field in the virtual component.
</description>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.getWritePriority() -->
<method name="getWritePriority"  public="true">
<description>
Get the priority to be used for writing the Present_Value property of this&#xa; object.
</description>
<return>
<type class="int"/>
<description>
write priority
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.getPolicy() -->
<method name="getPolicy"  public="true">
<description>
Get the BTuningPolicy configured by policyName. If the policyName doesn&#x27;t&#xa; map to a valid policy then log a warning and use the defaultPolicy.&#xa; &lt;p&gt;&#xa; Note that all virtual components are polled at this time.
</description>
<return>
<type class="javax.baja.bacnet.point.BBacnetTuningPolicy"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.getSlotFacets(javax.baja.sys.Slot) -->
<method name="getSlotFacets"  public="true">
<description/>
<parameter name="s">
<type class="javax.baja.sys.Slot"/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.isStatusProp(java.lang.String) -->
<method name="isStatusProp"  public="true" static="true">
<description>
Does this property require a status? This is based on whether the &#x22;status=&#x22;&#xa; option exists in the virtualPathName. If true, the property will be created&#xa; as a BStatusValue, instead of the default type.
</description>
<parameter name="virtualPathName">
<type class="java.lang.String"/>
<description/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this property requires an associated status.
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.getStatusSource(java.lang.String) -->
<method name="getStatusSource"  public="true" static="true">
<description>
Extract the source(s) of status information from the property name used to&#xa; create the virtual component. This inspects each modifier, separated by&#xa; semicolons, for the &#x22;status=&#x22; tag. Found status sources are returned as a&#xa; BString, separated by semicolons.
</description>
<parameter name="propertyName">
<type class="java.lang.String"/>
<description/>
</parameter>
<return>
<type class="javax.baja.sys.BString"/>
<description>
status source
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.updateStatus() -->
<method name="updateStatus"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.getPollFrequency() -->
<method name="getPollFrequency"  public="true">
<description/>
<return>
<type class="javax.baja.driver.util.BPollFrequency"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.device() -->
<method name="device"  public="true">
<description>
Get the containing device object which will poll this object.
</description>
<return>
<type class="javax.baja.bacnet.BBacnetDevice"/>
<description>
the containing BBacnetDevice
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.getPollableType() -->
<method name="getPollableType"  public="true">
<description>
Get the pollable type of this object.
</description>
<return>
<type class="int"/>
<description>
one of the pollable types defined in BIBacnetPollable.
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.poll() -->
<method name="poll"  public="true">
<annotation><type class="java.lang.Deprecated"/>
</annotation>
<description>
Poll the node.
</description>
<tag name="@deprecated">As of 3.2</tag>
<return>
<type class="boolean"/>
<description>
true if a poll was attempted to this node, or false if the poll&#xa; was skipped due to device down, out of service, etc.
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.readFail(java.lang.String) -->
<method name="readFail"  public="true">
<description>
Indicate a failure polling this object.
</description>
<parameter name="failureMsg">
<type class="java.lang.String"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.fromEncodedValue(byte[], javax.baja.status.BStatus, javax.baja.sys.Context) -->
<method name="fromEncodedValue"  public="true">
<description>
Normalize the encoded data into the pollable&#x27;s data structure.
</description>
<parameter name="encodedValue">
<type class="byte" dimension="1"/>
<description/>
</parameter>
<parameter name="status">
<type class="javax.baja.status.BStatus"/>
<description/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
<description/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.getPollListEntries() -->
<method name="getPollListEntries"  public="true">
<description>
Get the list of poll list entries for this pollable. The first entry for&#xa; points must be the configured property.
</description>
<return>
<type class="javax.baja.bacnet.util.PollListEntry" dimension="1"/>
<description>
the list of poll list entries.
</description>
</return>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.spy(javax.baja.spy.SpyWriter) -->
<method name="spy"  public="true">
<description/>
<parameter name="out">
<type class="javax.baja.spy.SpyWriter"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.lang.Exception"/>
</throws>
</method>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.facets -->
<field name="facets"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;facets&lt;/code&gt; property.
</description>
<tag name="@see">#getFacets</tag>
<tag name="@see">#setFacets</tag>
</field>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.subscribe -->
<field name="subscribe"  public="true" static="true" final="true">
<type class="javax.baja.sys.Action"/>
<description>
Slot for the &lt;code&gt;subscribe&lt;/code&gt; action.
</description>
<tag name="@see">#subscribe()</tag>
</field>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.virtual.BBacnetVirtualComponent.INDEX -->
<field name="INDEX"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

</class>
</bajadoc>
