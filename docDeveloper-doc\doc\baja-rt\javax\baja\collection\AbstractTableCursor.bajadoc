<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.collection.AbstractTableCursor" name="AbstractTableCursor" packageName="javax.baja.collection" public="true" abstract="true">
<description>
A general implementation of a &lt;code&gt;<see ref="javax.baja.collection.TableCursor">TableCursor</see>&lt;/code&gt;.  Subclasses only need to provide an implementation&#xa; of &lt;code&gt;<see ref="javax.baja.collection.AbstractTableCursor#row()">#row()</see>&lt;/code&gt;.
</description>
<tag name="@author">&lt;a href=&#x22;mailto:mgian<PERSON><PERSON>@tridium.com&#x22;&gt;<PERSON>lt;/a&gt;</tag>
<extends>
<parameterizedType class="javax.baja.collection.AbstractCursor">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</extends>
<implements>
<parameterizedType class="javax.baja.collection.TableCursor">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</implements>
<typeParameters>
<typeVariable class="T">
<bounds>
<type class="javax.baja.sys.BIObject"/>
</bounds>
</typeVariable>
</typeParameters>
<!-- javax.baja.collection.AbstractTableCursor(javax.baja.collection.BITable&lt;T&gt;) -->
<constructor name="AbstractTableCursor" protected="true">
<parameter name="table">
<parameterizedType class="javax.baja.collection.BITable">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
</parameter>
<description/>
</constructor>

<!-- javax.baja.collection.AbstractTableCursor.getTable() -->
<method name="getTable"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Should only be overridden to add a covariant return method.
</description>
<return>
<parameterizedType class="javax.baja.collection.BITable">
<args>
<typeVariable class="T"/>
</args>
</parameterizedType>
<description>
this cursor&#x27;s table
</description>
</return>
</method>

<!-- javax.baja.collection.AbstractTableCursor.doGet() -->
<method name="doGet"  protected="true" final="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Get the row object from the current row.
</description>
<return>
<typeVariable class="T"/>
<description>
row object
</description>
</return>
</method>

</class>
</bajadoc>
