<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnetAws" runtimeProfile="rt" name="javax.baja.bacnetAws.config">
<description/>
<class packageName="javax.baja.bacnetAws.config" name="BBacnetAccessDoor"/>
<class packageName="javax.baja.bacnetAws.config" name="BBacnetAccumulator"/>
<class packageName="javax.baja.bacnetAws.config" name="BBacnetAwsConfigDeviceExt"><description>BBacnetConfigDeviceExt represents the configuration representation of a&#xa; Bacnet Aws device.</description></class>
<class packageName="javax.baja.bacnetAws.config" name="BBacnetAwsConfigFolder"><description>BBacnetAwsConfigFolder is the standard container to use&#xa; under BBacnetAwsConfigDeviceExt to organize BBacnetObjects.</description></class>
<class packageName="javax.baja.bacnetAws.config" name="BBacnetCommand"/>
<class packageName="javax.baja.bacnetAws.config" name="BBacnetEventLog"/>
<class packageName="javax.baja.bacnetAws.config" name="BBacnetLoadControl"/>
<class packageName="javax.baja.bacnetAws.config" name="BBacnetPulseConverter"/>
<class packageName="javax.baja.bacnetAws.config" name="BBacnetStructuredView"/>
</package>
</bajadoc>
