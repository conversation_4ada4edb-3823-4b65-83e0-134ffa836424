<!DOCTYPE html>

<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>js Class: Lexicon</title>

	<!--[if lt IE 9]>
	<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
	<![endif]-->
	<link type="text/css" rel="stylesheet" href="styles/sunlight.default.css">

	<link type="text/css" rel="stylesheet" href="styles/site.cerulean.css">

</head>

<body>

<div class="navbar navbar-default navbar-fixed-top navbar-inverse">
<div class="container">
	<div class="navbar-header">
		<a class="navbar-brand" href="index.html">js</a>
		<button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#topNavigation">
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
			<span class="icon-bar"></span>
        </button>
	</div>
	<div class="navbar-collapse collapse" id="topNavigation">
		<ul class="nav navbar-nav">
			
			<li class="dropdown">
				<a href="modules.list.html" class="dropdown-toggle" data-toggle="dropdown">Modules<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-dialogs.html">dialogs</a></li><li><a href="module-lex.html">lex</a></li><li><a href="module-log.html">log</a></li><li><a href="module-nmodule_js_rc_csrf_csrfUtil.html">nmodule/js/rc/csrf/csrfUtil</a></li><li><a href="module-nmodule_js_rc_jasmine_promiseUtils.html">nmodule/js/rc/jasmine/promiseUtils</a></li><li><a href="module-nmodule_js_rc_lex_lex.html">nmodule/js/rc/lex/lex</a></li><li><a href="module-nmodule_js_rc_log_Level.html">nmodule/js/rc/log/Level</a></li><li><a href="module-nmodule_js_rc_log_Log.html">nmodule/js/rc/log/Log</a></li>
				</ul>
			</li>
			
			<li class="dropdown">
				<a href="classes.list.html" class="dropdown-toggle" data-toggle="dropdown">Classes<b class="caret"></b></a>
				<ul class="dropdown-menu ">
					<li><a href="module-dialogs-Dialog.html">dialogs~Dialog</a></li><li><a href="module-nmodule_js_rc_lex_lex-Lexicon.html">nmodule/js/rc/lex/lex~Lexicon</a></li><li><a href="module-nmodule_js_rc_log_Log.Level.html">nmodule/js/rc/log/Log.Level</a></li>
				</ul>
			</li>
			
		</ul>
        
            <div class="col-sm-3 col-md-3">
                <form class="navbar-form" role="search">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Search" name="q" id="search-input">
                        <div class="input-group-btn">
                            <button class="btn btn-default" id="search-submit"><i class="glyphicon glyphicon-search"></i></button>
                        </div>
                    </div>
                </form>
            </div>
        
	</div>

</div>
</div>


<div class="container" id="toc-content">
<div class="row">

	
	<div class="col-md-8">
	
		<div id="main">
			

	<h1 class="page-title">Class: Lexicon</h1>
<section>

<header>
    
        <h2>
            <span class="ancestors"><a href="module-nmodule_js_rc_lex_lex.html">nmodule/js/rc/lex/lex</a>~</span>
        
        Lexicon
        </h2>
        
    
</header>


<article>
    <div class="container-overview">
    
        
<hr>
<dt>
    <h4 class="name" id="Lexicon"><span class="type-signature"></span>new Lexicon(moduleName, data)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>A Lexicon is a map of locale specific name/value pairs for a module.</p>
<p>An instance of a Lexicon can be accessed indirectly by use of the<br>
module method.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>moduleName</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>The name of the Niagara Module this Lexicon relates too.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>data</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>An object contained key values pairs for the Lexicon.</p></td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    

    
        <h5>Example</h5>
        
        <p class="code-caption">Access a module's Lexicon</p>
    
    <pre class="sunlight-highlight-javascript">lexjs.module(&quot;js&quot;)
       .then(function (lex) {
         console.log(&quot;Some text from a lexicon: &quot; + lex.get(&quot;dialogs.ok&quot;));
       });</pre>


    
</dd>

    
    </div>

    

    

    

    

    

    

    
        <h3 class="subsection-title">Methods</h3>

        <dl>
            
<hr>
<dt>
    <h4 class="name" id="get"><span class="type-signature"></span>get(obj)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return a value from the Lexicon for a given key.</p>
<p>The argument for this method can be either a String key followed by arguments or an Object Literal.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>obj</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>
|

<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>the Object Literal that contains the method's arguments or a String key.</p>
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>the key to look up.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>def</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>the default value to return if the key can't be found.<br>
By default this is null.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>args</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array</span>
|

<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>arguments used for String formatting. If the first parameter<br>
is a String key, this list can just be further arguments for the function.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            
<div class="param-desc">
    <p>the value for the Lexicon or return def if can't be found.</p>
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
        <h5>Examples</h5>
        
        <p class="code-caption">Access a Lexicon value via its key name.</p>
    
    <pre class="sunlight-highlight-javascript">lexjs.module(&quot;js&quot;)
       .then(function (lex) {
         console.log(lex.get(&quot;dialogs.ok&quot;));
       });
       </pre>

        <p class="code-caption">Access a Lexicon value via its key name with some formatted parameters.</p>
    
    <pre class="sunlight-highlight-javascript">lexjs.module(&quot;bajaui&quot;)
       .then(function (lex) {
          var val = lex.get(&quot;fileSearch.scanningFiles&quot;, &quot;alpha&quot;, &quot;omega&quot;))
          // Prints out: Scanning files (found alpha of omega)...
          console.log(val);
       })); 
       </pre>

        <p class="code-caption">Provide a default value if the key can't be found and use an Object Literal
  instead</p>
    
    <pre class="sunlight-highlight-javascript">lexjs.module(&quot;bajaui&quot;)
       .then(function (lex) {
         // Use an Object Literal instead of multiple arguments and provide a default value.
         var val = lex.get({
           key: &quot;fileSearch.scanningFiles&quot;,
           def: &quot;Return this if the key can&#x27;t be found in the Lexicon&quot;,
           args: [&quot;alpha&quot;, &quot;omega&quot;]
         });
         console.log(val);
       });</pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getModuleName"><span class="type-signature"></span>getModuleName()</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the Lexicon's module name.</p>
    </div>
    

    

    

    
    

    
<dl class="details">
    

	

	

	

    

    

    

    

	

	

	

	

	



	

	

	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
        <h5>Example</h5>
        
        <p class="code-caption">Return a Lexicon's module name</p>
    
    <pre class="sunlight-highlight-javascript">lexjs.module(&quot;bajaui&quot;)
       .then(function (lex) {
          // Prints out: bajaui
          console.log(lex.getModuleName());
       })); </pre>


    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getRaw"><span class="type-signature"></span>getRaw(obj)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return the raw and unescaped value of the key which is not safe to display.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>obj</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>
|

<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>the Object Literal that contains the method's arguments or a String key.</p>
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>the key to look up.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>def</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>the default value to return if the key can't be found.<br>
By default this is null.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>args</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array</span>
|

<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>arguments used for String formatting. If the first parameter<br>
is a String key, this list can just be further arguments for the function.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.8</li>
		</ul>
	</dd>
	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li>Lexicon.get</li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
</dd>

        
            
<hr>
<dt>
    <h4 class="name" id="getSafe"><span class="type-signature"></span>getSafe(obj)</h4>
    
    
</dt>
<dd>

    
    <div class="description">
        <p>Return escaped value of the key which is safe to display.</p>
    </div>
    

    

    

    
    
        <h5>Parameters:</h5>
        

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>obj</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>
|

<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>the Object Literal that contains the method's arguments or a String key.</p>
                <h6 class="method-params-label method-subparams-label">Properties</h6>
                

<table class="params table table-striped">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>the key to look up.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>def</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>the default value to return if the key can't be found.<br>
By default this is null.</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>args</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array</span>
|

<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>arguments used for String formatting. If the first parameter<br>
is a String key, this list can just be further arguments for the function.</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

    

    
<dl class="details">
    

	

	
	<dt class="tag-since method-doc-label method-doc-details-label">Since:</dt>
	<dd class="tag-since">
		<ul class="dummy">
			<li>Niagara 4.8</li>
		</ul>
	</dd>
	

	

    

    

    

    

	

	

	

	

	



	

	

	
	<dt class="tag-see method-doc-label method-doc-details-label">See:</dt>
	<dd class="tag-see">
		<ul>
			<li>Lexicon.get</li>
			</ul>
	</dd>
	

	
</dl>


    

    

    

    

    

    
    <h5>Returns:</h5>
    
            


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>


        

    
</dd>

        </dl>
    

    

    
</article>

</section>




		</div>
	</div>

	<div class="clearfix"></div>

	
		<div class="col-md-3">
			<div id="toc" class="col-md-3 hidden-xs hidden-sm hidden-md"></div>
		</div>
	

</div>
</div>


    <div class="modal fade" id="searchResults">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Search results</h4>
          </div>
          <div class="modal-body"></div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
          </div>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div>


<footer>


	<span class="copyright">
	js Copyright © 2024 Tridium. DocStrap Copyright © 2012-2013 the contributors to the JSDoc3 and DocStrap projects.
	</span>

<span class="jsdoc-message">
	Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.6</a>
	
		on 2024-05-03T00:28:57+00:00
	
	using the <a href="https://github.com/docstrap/docstrap">DocStrap template</a>.
</span>
</footer>

<script src="scripts/docstrap.lib.js"></script>
<script src="scripts/toc.js"></script>

    <script type="text/javascript" src="scripts/fulltext-search-ui.js"></script>


<script>
$( function () {
	$( "[id*='$']" ).each( function () {
		var $this = $( this );

		$this.attr( "id", $this.attr( "id" ).replace( "$", "__" ) );
	} );

	$( ".tutorial-section pre, .readme-section pre, pre.prettyprint.source" ).each( function () {
		var $this = $( this );

		var example = $this.find( "code" );
		exampleText = example.html();
		var lang = /{@lang (.*?)}/.exec( exampleText );
		if ( lang && lang[1] ) {
			exampleText = exampleText.replace( lang[0], "" );
			example.html( exampleText );
			lang = lang[1];
		} else {
			var langClassMatch = example.parent()[0].className.match(/lang\-(\S+)/);
			lang = langClassMatch ? langClassMatch[1] : "javascript";
		}

		if ( lang ) {

			$this
			.addClass( "sunlight-highlight-" + lang )
			.addClass( "linenums" )
			.html( example.html() );

		}
	} );

	Sunlight.highlightAll( {
		lineNumbers : true,
		showMenu : true,
		enableDoclinks : true
	} );

	$.catchAnchorLinks( {
        navbarOffset: 10
	} );
	$( "#toc" ).toc( {
		anchorName  : function ( i, heading, prefix ) {
			return $( heading ).attr( "id" ) || ( prefix + i );
		},
		selectors   : "#toc-content h1,#toc-content h2,#toc-content h3,#toc-content h4",
		showAndHide : false,
		smoothScrolling: true
	} );

	$( "#main span[id^='toc']" ).addClass( "toc-shim" );
	$( '.dropdown-toggle' ).dropdown();

    $( "table" ).each( function () {
      var $this = $( this );
      $this.addClass('table');
    } );

} );
</script>



<!--Navigation and Symbol Display-->


<!--Google Analytics-->



    <script type="text/javascript">
        $(document).ready(function() {
            SearcherDisplay.init();
        });
    </script>


</body>
</html>