<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<module name="andoverInfinity" runtimeProfile="wb" bajaVersion="0" vendor="Tridium" vendorVersion="*********">
<description>Andover Infinity Driver</description>
<package name="com.tridium.andoverInfinity.ui"/>
<package name="com.tridium.andoverInfinity.ui.terminal"/>
<class packageName="com.tridium.andoverInfinity.ui" name="BInfinityBufferLoaderMenuAgent"><description>BInfinityBufferLoaderMenuAgent is an agent to load and save the BVt100&#xa; screen buffer to/from a file on the client machine.</description></class>
<class packageName="com.tridium.andoverInfinity.ui" name="BInfinityBufferLoaderMenuAgent.LoadScreenBufferFromFileCommand"></class>
<class packageName="com.tridium.andoverInfinity.ui" name="BInfinityBufferLoaderMenuAgent.SaveScreenBufferToFileCommand"></class>
<class packageName="com.tridium.andoverInfinity.ui.terminal" name="BInfinityVirtualTerminal"><description>BInfinityVirtualTerminal - This view provides the user with&#xa; view into the VT100 interface of the infinity network.</description></class>
<class packageName="com.tridium.andoverInfinity.ui.terminal" name="InfinityVt100TextController"><description>InfinityVt100TextController extends TextController to&#xa; intercept mouse and keyboard input and route&#xa; to the network object of andover infinity.</description></class>
<class packageName="com.tridium.andoverInfinity.ui.terminal" name="InfinityVt100TextModel"><description>InfinityVt100TextModel&#xa; &#xa; Custom TextModel class for Infinity VT100 terminal view.</description></class>
<class packageName="com.tridium.andoverInfinity.ui.terminal" name="InfinityVt100TextParser"><description>InfinityVt100TextParser&#xa; &#xa; Custom parser for VT100 manager view.</description></class>
</module>
</bajadoc>
