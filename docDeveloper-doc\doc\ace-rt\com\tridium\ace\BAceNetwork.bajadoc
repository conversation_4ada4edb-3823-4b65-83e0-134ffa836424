<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.BAceNetwork" name="BAceNetwork" packageName="com.tridium.ace" public="true" abstract="true">
<description>
BAceNetwork is base class for network of Ace devices.
</description>
<tag name="@author"><PERSON> on 02-Sep-16</tag>
<extends>
<type class="com.tridium.ndriver.BNNetwork"/>
</extends>
<property name="pollScheduler" flags="">
<type class="com.tridium.ndriver.poll.BNPollScheduler"/>
<description>
Slot for the &lt;code&gt;pollScheduler&lt;/code&gt; property.
</description>
<tag name="@see">#getPollScheduler</tag>
<tag name="@see">#setPollScheduler</tag>
</property>

<property name="commConfig" flags="">
<type class="com.tridium.ndriver.datatypes.BCommConfig"/>
<description>
Slot for the &lt;code&gt;commConfig&lt;/code&gt; property.
</description>
<tag name="@see">#getCommConfig</tag>
<tag name="@see">#setCommConfig</tag>
</property>

<action name="downloadApp" flags="h">
<parameter name="parameter">
<type class="com.tridium.ace.datatypes.BAceDownloadParams"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;downloadApp&lt;/code&gt; action.
</description>
<tag name="@see">#downloadApp(BAceDownloadParams parameter)</tag>
</action>

<action name="uploadApp" flags="h">
<parameter name="parameter">
<type class="com.tridium.ace.datatypes.BAceReadFileParams"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;uploadApp&lt;/code&gt; action.
</description>
<tag name="@see">#uploadApp(BAceReadFileParams parameter)</tag>
</action>

</class>
</bajadoc>
