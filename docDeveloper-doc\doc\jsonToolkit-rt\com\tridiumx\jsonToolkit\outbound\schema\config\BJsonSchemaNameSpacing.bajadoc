<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing" name="BJsonSchemaNameSpacing" packageName="com.tridiumx.jsonToolkit.outbound.schema.config" public="true" final="true">
<description>
Different available options for dealing with spaces in the forming of json keys.&#xa; As the names of things in niagara might not have been entered consistently this allows uniformity.&#xa;&#xa; e.g if a point was called &#x27;Space Temp&#x27;&#xa;&#xa; remove-     &#x22;SpaceTemp&#x22; : ...&#xa; keep-       &#x22;Space Temp&#x22; : ...&#xa; hyphenate-  &#x22;Space-Temp&#x22; : ...&#xa; underscore- &#x22;Space_Temp&#x22; : ...&#xa; urlEncode-  &#x22;Space+Temp&#x22; : ...&#xa;&#xa; or if the name was &#x27;SpaceTemp&#x27; or &#x27;spaceTemp&#x27;&#xa;&#xa; add-    &#x22;Space Temp&#x22; : ...&#xa; injects a space on case changes (but not at start of name)
</description>
<tag name="@author">Nick Dodd</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;remove&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;keep&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;add&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;hyphenate&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;underscore&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;urlEncode&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
</annotation>
<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing.REMOVE -->
<field name="REMOVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for remove.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing.KEEP -->
<field name="KEEP"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for keep.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing.ADD -->
<field name="ADD"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for add.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing.HYPHENATE -->
<field name="HYPHENATE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for hyphenate.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing.UNDERSCORE -->
<field name="UNDERSCORE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for underscore.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing.URL_ENCODE -->
<field name="URL_ENCODE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for urlEncode.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing.remove -->
<field name="remove"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing"/>
<description>
BJsonSchemaNameSpacing constant for remove.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing.keep -->
<field name="keep"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing"/>
<description>
BJsonSchemaNameSpacing constant for keep.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing.add -->
<field name="add"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing"/>
<description>
BJsonSchemaNameSpacing constant for add.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing.hyphenate -->
<field name="hyphenate"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing"/>
<description>
BJsonSchemaNameSpacing constant for hyphenate.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing.underscore -->
<field name="underscore"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing"/>
<description>
BJsonSchemaNameSpacing constant for underscore.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing.urlEncode -->
<field name="urlEncode"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing"/>
<description>
BJsonSchemaNameSpacing constant for urlEncode.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.outbound.schema.config.BJsonSchemaNameSpacing.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
