<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.schedule.BBacnetScheduleDeviceExt" name="BBacnetScheduleDeviceExt" packageName="com.tridium.bacnet.schedule" public="true">
<description>
BBacnetScheduleDeviceExt.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">06 Jan 2003</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.driver.schedule.BScheduleDeviceExt"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<implements>
<type class="javax.baja.bacnet.BIBacnetObjectContainer"/>
</implements>
<property name="skipWriteOnError" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;skipWriteOnError&lt;/code&gt; property.&#xa; should Niagara skip subsequent writes to any schedule properties&#xa; that encounter errors?  Allows station to proceed without errors,&#xa; but may mask problems in communicating to devices.
</description>
<tag name="@see">#getSkipWriteOnError</tag>
<tag name="@see">#setSkipWriteOnError</tag>
</property>

<action name="submitScheduleDiscoveryJob" flags="h">
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;submitScheduleDiscoveryJob&lt;/code&gt; action.
</description>
<tag name="@see">#submitScheduleDiscoveryJob()</tag>
</action>

</class>
</bajadoc>
