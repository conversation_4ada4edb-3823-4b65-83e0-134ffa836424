<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetOctetString" name="BBacnetOctetString" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BBacnetOctetString represents an octet string (byte array).
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 2$ $Date: 11/20/01 9:19:59 AM$</tag>
<tag name="@creation">26 Oct 01</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BSimple"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.make(byte[]) -->
<method name="make"  public="true" static="true" synchronized="true">
<description>
Factory method.&#xa; This also handles maintenance of a pool of BBacnetOctetStrings.&#xa; The convention is that the first element in the pool is&#xa; the null entry, for faster reference of this common instance.
</description>
<parameter name="arr">
<type class="byte" dimension="1"/>
<description>
byte array to use.
</description>
</parameter>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetOctetString"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.isNull() -->
<method name="isNull"  public="true">
<description>
Some types of BObjects are used to indicate&#xa; a null value.  This method allows those types to&#xa; declare their null status by overriding this common&#xa; method.  The default is to return false.
</description>
<return>
<type class="boolean"/>
<description>
true if this octet string contains a null array.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description>
Returns true if the byte arrays are exactly equal.
</description>
<parameter name="obj">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.encode(java.io.DataOutput) -->
<method name="encode"  public="true">
<description>
Encode the simple type using a binary format&#xa; that can be translated using decode.
</description>
<parameter name="out">
<type class="java.io.DataOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.decode(java.io.DataInput) -->
<method name="decode"  public="true">
<description>
Decode the simple using the same binary format&#xa; that was written using encode, and return the new&#xa; instance.  Under no circumstances should this&#xa; instance be modified.
</description>
<parameter name="in">
<type class="java.io.DataInput"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.encodeToString() -->
<method name="encodeToString"  public="true">
<description>
Encode the simple using a String format&#xa; that can be translated using decodeFromString.
</description>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.encodeToString(byte[]) -->
<method name="encodeToString"  public="true" static="true">
<description>
Encode a byte array using the String format&#xa; for the BACnet octet string simple.
</description>
<tag name="@since">Niagara 4.11</tag>
<parameter name="arr">
<type class="byte" dimension="1"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.decodeFromString(java.lang.String) -->
<method name="decodeFromString"  public="true">
<description>
Decode the simple using the same String format&#xa; that was written using encodeToString, and return&#xa; the new instance.  Under no circumstances should&#xa; this instance be modified.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.length() -->
<method name="length"  public="true">
<description>
Get the length of the byte array.
</description>
<return>
<type class="int"/>
<description>
the length as an int.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.byteAt(int) -->
<method name="byteAt"  public="true">
<description>
Get the byte at the specified index.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="byte"/>
<description>
the specified byte as a byte.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.getBytes() -->
<method name="getBytes"  public="true">
<description>
Get (a copy of) the byte array.
</description>
<return>
<type class="byte" dimension="1"/>
<description>
a byte array copied from this octet string&#x27;s array.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.getAddr() -->
<method name="getAddr"  public="true">
<description>
Get the byte array.&#xa; Do not alter the contents of this array and complain&#xa; when things go wrong.
</description>
<return>
<type class="byte" dimension="1"/>
<description>
a this octet string&#x27;s array.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.hashCode() -->
<method name="hashCode"  public="true">
<description>
Hash code.&#xa; The hash code for a BBacnetOctetString is its unique id.
</description>
<return>
<type class="int"/>
<description>
the id assigned when the string is created.
</description>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.bytesToString(byte[]) -->
<method name="bytesToString"  public="true" static="true">
<description>
Encode the simple using a String format&#xa; that can be translated using decodeFromString.
</description>
<parameter name="b">
<type class="byte" dimension="1"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.stringToBytes(java.lang.String) -->
<method name="stringToBytes"  public="true" static="true">
<description>
Decode the simple using the same String format&#xa; that was written using encodeToString, and return&#xa; the new instance.  Under no circumstances should&#xa; this instance be modified.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="byte" dimension="1"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.BACNET_OCTET_STRING -->
<field name="BACNET_OCTET_STRING"  public="true" static="true" final="true">
<type class="java.lang.String"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.datatypes.BBacnetOctetString"/>
<description>
Default BBacnetOctetString is a null byte array.
</description>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.BACNET_WEEK_N_DAY -->
<field name="BACNET_WEEK_N_DAY"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.datatypes.BBacnetOctetString"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetOctetString.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
