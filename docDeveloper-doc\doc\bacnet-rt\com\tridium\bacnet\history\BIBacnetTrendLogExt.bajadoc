<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.history.BIBacnetTrendLogExt" name="BIBacnetTrendLogExt" packageName="com.tridium.bacnet.history" public="true" interface="true" abstract="true" category="interface">
<description>
BIBacnetTrendLogExt is the interface which all BACnet Trend Log extensions&#xa; must implement.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">29 Nov 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
</class>
</bajadoc>
