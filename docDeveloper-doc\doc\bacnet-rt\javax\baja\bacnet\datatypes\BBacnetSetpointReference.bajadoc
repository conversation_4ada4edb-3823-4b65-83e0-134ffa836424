<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.datatypes.BBacnetSetpointReference" name="BBacnetSetpointReference" packageName="javax.baja.bacnet.datatypes" public="true" final="true">
<description>
BBacnetSetpointReference represents the BacnetSetpointReference&#xa; sequence.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">09 Sep 2004</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="referenceUsed" flags="h">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;referenceUsed&lt;/code&gt; property.
</description>
<tag name="@see">#getReferenceUsed</tag>
<tag name="@see">#setReferenceUsed</tag>
</property>

<property name="reference" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference"/>
<description>
Slot for the &lt;code&gt;reference&lt;/code&gt; property.
</description>
<tag name="@see">#getReference</tag>
<tag name="@see">#setReference</tag>
</property>

<!-- javax.baja.bacnet.datatypes.BBacnetSetpointReference() -->
<constructor name="BBacnetSetpointReference" public="true">
<description>
Default constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetSetpointReference(javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference) -->
<constructor name="BBacnetSetpointReference" public="true">
<parameter name="setpointReference">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference"/>
</parameter>
<description>
Standard constructor.
</description>
</constructor>

<!-- javax.baja.bacnet.datatypes.BBacnetSetpointReference.getReferenceUsed() -->
<method name="getReferenceUsed"  public="true">
<description>
Get the &lt;code&gt;referenceUsed&lt;/code&gt; property.
</description>
<tag name="@see">#referenceUsed</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSetpointReference.setReferenceUsed(boolean) -->
<method name="setReferenceUsed"  public="true">
<description>
Set the &lt;code&gt;referenceUsed&lt;/code&gt; property.
</description>
<tag name="@see">#referenceUsed</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSetpointReference.getReference() -->
<method name="getReference"  public="true">
<description>
Get the &lt;code&gt;reference&lt;/code&gt; property.
</description>
<tag name="@see">#reference</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSetpointReference.setReference(javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference) -->
<method name="setReference"  public="true">
<description>
Set the &lt;code&gt;reference&lt;/code&gt; property.
</description>
<tag name="@see">#reference</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSetpointReference.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSetpointReference.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
To String.
</description>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSetpointReference.getSetpointReference() -->
<method name="getSetpointReference"  public="true">
<description/>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSetpointReference.setSetpointReference(javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference) -->
<method name="setSetpointReference"  public="true">
<description/>
<parameter name="setpointReference">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSetpointReference.setSetpointReference(javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference, javax.baja.sys.Context) -->
<method name="setSetpointReference"  public="true">
<description/>
<parameter name="setpointReference">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectPropertyReference"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSetpointReference.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description>
Write the value to the Asn output stream.
</description>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
<description>
the AsnOutput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSetpointReference.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description>
Read the value from the Asn input stream.
</description>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
<description>
the AsnInput stream.
</description>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnet.datatypes.BBacnetSetpointReference.referenceUsed -->
<field name="referenceUsed"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;referenceUsed&lt;/code&gt; property.
</description>
<tag name="@see">#getReferenceUsed</tag>
<tag name="@see">#setReferenceUsed</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetSetpointReference.reference -->
<field name="reference"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;reference&lt;/code&gt; property.
</description>
<tag name="@see">#getReference</tag>
<tag name="@see">#setReference</tag>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetSetpointReference.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.datatypes.BBacnetSetpointReference.SETPOINT_REFERENCE_TAG -->
<field name="SETPOINT_REFERENCE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
