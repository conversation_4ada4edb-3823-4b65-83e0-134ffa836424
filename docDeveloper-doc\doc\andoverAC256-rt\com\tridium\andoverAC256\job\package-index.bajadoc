<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="andoverAC256" runtimeProfile="rt" name="com.tridium.andoverAC256.job">
<description/>
<class packageName="com.tridium.andoverAC256.job" name="BAndoverBackupControllerJob"><description>Andover AC256 Point Discovery.</description></class>
<class packageName="com.tridium.andoverAC256.job" name="BAndoverConsoleJob"><description>BAndoverConsoleJob sends commands from an&#xa; AndoverConole to the com port and routes&#xa; responses back to the AndoverConsole.</description></class>
<class packageName="com.tridium.andoverAC256.job" name="BAndoverDiscoverPointsJob"><description>Andover AC256 Point Discovery.</description></class>
<class packageName="com.tridium.andoverAC256.job" name="BAndoverDiscoveryPoint"><description>Andover AC256 Discovery Point.</description></class>
<class packageName="com.tridium.andoverAC256.job" name="BAndoverRestoreControllerJob"><description>Andover AC256 Restore a controller&#x27;s program.</description></class>
<class packageName="com.tridium.andoverAC256.job" name="BAndoverRunScriptJob"><description>Run a script from an BAndoverScript object</description></class>
<class packageName="com.tridium.andoverAC256.job" name="BAndoverSetOptionsJob"><description>Andover AC256 Set Options.</description></class>
</package>
</bajadoc>
