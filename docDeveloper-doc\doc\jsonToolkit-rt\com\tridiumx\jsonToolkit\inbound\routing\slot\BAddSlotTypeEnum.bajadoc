<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="jsonToolkit" runtimeProfile="rt" qualifiedName="com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum" name="BAddSlotTypeEnum" packageName="com.tridiumx.jsonToolkit.inbound.routing.slot" public="true" final="true">
<description>
Types of slot that can be added to a Handler
</description>
<tag name="@author"><PERSON></tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;string&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;bool&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;num&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;statusString&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;statusBool&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;statusNum&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
<elementValue name="defaultValue">
<annotationValue kind="expr">
<expression>&#x22;string&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.isSupportedType(javax.baja.sys.Property) -->
<method name="isSupportedType"  public="true" static="true">
<description>
Checks whether a given property matches one of the add slot types for routers.
</description>
<tag name="@since">Niagara 4.11</tag>
<parameter name="prop">
<type class="javax.baja.sys.Property"/>
<description>
a property to test
</description>
</parameter>
<return>
<type class="boolean"/>
<description>
true if the property matches one of the add slot types for routers.
</description>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.isStatusValue() -->
<method name="isStatusValue"  public="true">
<description>
Checks whether a given property is a status value
</description>
<tag name="@since">Niagara 4.13</tag>
<return>
<type class="boolean"/>
<description>
true if the property matches one of the status types.
</description>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.getSlotDefaultValue() -->
<method name="getSlotDefaultValue"  public="true">
<description>
Return value widened to BValue since Niagara 4.11.
</description>
<return>
<type class="javax.baja.sys.BValue"/>
<description>
the default value for the selected slot type.
</description>
</return>
</method>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.STRING -->
<field name="STRING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for string.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.BOOL -->
<field name="BOOL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for bool.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.NUM -->
<field name="NUM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for num.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.STATUS_STRING -->
<field name="STATUS_STRING"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for statusString.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.STATUS_BOOL -->
<field name="STATUS_BOOL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for statusBool.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.STATUS_NUM -->
<field name="STATUS_NUM"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for statusNum.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.string -->
<field name="string"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum"/>
<description>
BAddSlotTypeEnum constant for string.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.bool -->
<field name="bool"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum"/>
<description>
BAddSlotTypeEnum constant for bool.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.num -->
<field name="num"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum"/>
<description>
BAddSlotTypeEnum constant for num.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.statusString -->
<field name="statusString"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum"/>
<description>
BAddSlotTypeEnum constant for statusString.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.statusBool -->
<field name="statusBool"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum"/>
<description>
BAddSlotTypeEnum constant for statusBool.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.statusNum -->
<field name="statusNum"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum"/>
<description>
BAddSlotTypeEnum constant for statusNum.
</description>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum"/>
<description/>
</field>

<!-- com.tridiumx.jsonToolkit.inbound.routing.slot.BAddSlotTypeEnum.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
