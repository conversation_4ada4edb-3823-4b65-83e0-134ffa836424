<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BIAlarmClassFolder" name="BIAlarmClassFolder" packageName="javax.baja.alarm" public="true" interface="true" abstract="true" category="interface">
<description>
BIAlarmClassFolder is a marker interface for Objects that are folders of &#xa; BAlarmClasses.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">29 Oct 08</tag>
<tag name="@version">$Revision: 1$ $Date: 11/4/08 8:26:23 AM EST$</tag>
<tag name="@since">Niagara 3.0</tag>
<implements>
<type class="javax.baja.sys.BInterface"/>
</implements>
<!-- javax.baja.alarm.BIAlarmClassFolder.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
