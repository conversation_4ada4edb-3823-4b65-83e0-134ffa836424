<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="platHwScan" runtimeProfile="rt" qualifiedName="com.tridium.platHwScan.optionCards.BOptionModule" name="BOptionModule" packageName="com.tridium.platHwScan.optionCards" public="true">
<description>
BOptionModule inherits everything from BOptionCard. This is used for the Titan&#xa; Hardware Scan so the property sheet says &#x22;Option Module&#x22; instead of &#x22;Option Card&#x22;
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">1/5/2014</tag>
<tag name="@since">Niagara 4.0</tag>
<extends>
<type class="com.tridium.platHwScan.optionCards.BOptionCard"/>
</extends>
<!-- com.tridium.platHwScan.optionCards.BOptionModule() -->
<constructor name="BOptionModule" public="true">
<description/>
</constructor>

<!-- com.tridium.platHwScan.optionCards.BOptionModule.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.platHwScan.optionCards.BOptionModule.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
