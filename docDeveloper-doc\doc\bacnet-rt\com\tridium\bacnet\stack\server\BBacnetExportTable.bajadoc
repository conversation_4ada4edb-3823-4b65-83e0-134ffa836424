<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.stack.server.BBacnetExportTable" name="BBacnetExportTable" packageName="com.tridium.bacnet.stack.server" public="true">
<description>
BBacnetExportTable manages the objects exported to Bacnet.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">05 Nov 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="com.tridium.bacnet.stack.server.BIBacnetExportFolder"/>
</implements>
<action name="getObjectOrdById" flags="h">
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="javax.baja.naming.BOrd"/>
</return>
<description>
Slot for the &lt;code&gt;getObjectOrdById&lt;/code&gt; action.
</description>
<tag name="@see">#getObjectOrdById(BBacnetObjectIdentifier parameter)</tag>
</action>

<action name="getNextInst" flags="h">
<parameter name="parameter">
<type class="com.tridium.bacnet.datatypes.BNextInstArgs"/>
</parameter>
<return>
<type class="javax.baja.sys.BInteger"/>
</return>
<description>
Slot for the &lt;code&gt;getNextInst&lt;/code&gt; action.
</description>
<tag name="@see">#getNextInst(BNextInstArgs parameter)</tag>
</action>

<action name="reorderOutOfServiceExt" flags="h">
<return>
<type class="void"/>
</return>
<description>
Slot for the &lt;code&gt;reorderOutOfServiceExt&lt;/code&gt; action.
</description>
<tag name="@see">#reorderOutOfServiceExt()</tag>
</action>

</class>
</bajadoc>
