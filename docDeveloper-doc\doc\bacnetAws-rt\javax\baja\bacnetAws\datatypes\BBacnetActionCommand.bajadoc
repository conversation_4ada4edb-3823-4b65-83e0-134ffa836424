<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="rt" qualifiedName="javax.baja.bacnetAws.datatypes.BBacnetActionCommand" name="BBacnetActionCommand" packageName="javax.baja.bacnetAws.datatypes" public="true">
<description/>
<extends>
<type class="javax.baja.sys.BComponent"/>
</extends>
<implements>
<type class="javax.baja.bacnet.datatypes.BIBacnetDataType"/>
</implements>
<property name="deviceId" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;deviceId&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceId</tag>
<tag name="@see">#setDeviceId</tag>
</property>

<property name="objectId" flags="">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</property>

<property name="propertyId" flags="">
<type class="javax.baja.sys.BEnum"/>
<description>
Slot for the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyId</tag>
<tag name="@see">#setPropertyId</tag>
</property>

<property name="propertyArrayIndex" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyArrayIndex</tag>
<tag name="@see">#setPropertyArrayIndex</tag>
</property>

<property name="priority" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;priority&lt;/code&gt; property.
</description>
<tag name="@see">#getPriority</tag>
<tag name="@see">#setPriority</tag>
</property>

<property name="postDelay" flags="">
<type class="int"/>
<description>
Slot for the &lt;code&gt;postDelay&lt;/code&gt; property.
</description>
<tag name="@see">#getPostDelay</tag>
<tag name="@see">#setPostDelay</tag>
</property>

<property name="quitOnFailure" flags="">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;quitOnFailure&lt;/code&gt; property.
</description>
<tag name="@see">#getQuitOnFailure</tag>
<tag name="@see">#setQuitOnFailure</tag>
</property>

<property name="writeSuccessful" flags="r">
<type class="boolean"/>
<description>
Slot for the &lt;code&gt;writeSuccessful&lt;/code&gt; property.
</description>
<tag name="@see">#getWriteSuccessful</tag>
<tag name="@see">#setWriteSuccessful</tag>
</property>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand() -->
<constructor name="BBacnetActionCommand" public="true">
<description/>
</constructor>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.getDeviceId() -->
<method name="getDeviceId"  public="true">
<description>
Get the &lt;code&gt;deviceId&lt;/code&gt; property.
</description>
<tag name="@see">#deviceId</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.setDeviceId(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="setDeviceId"  public="true">
<description>
Set the &lt;code&gt;deviceId&lt;/code&gt; property.
</description>
<tag name="@see">#deviceId</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.getObjectId() -->
<method name="getObjectId"  public="true">
<description>
Get the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#objectId</tag>
<return>
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.setObjectId(javax.baja.bacnet.datatypes.BBacnetObjectIdentifier) -->
<method name="setObjectId"  public="true">
<description>
Set the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#objectId</tag>
<parameter name="v">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.getPropertyId() -->
<method name="getPropertyId"  public="true">
<description>
Get the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#propertyId</tag>
<return>
<type class="javax.baja.sys.BEnum"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.setPropertyId(javax.baja.sys.BEnum) -->
<method name="setPropertyId"  public="true">
<description>
Set the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#propertyId</tag>
<parameter name="v">
<type class="javax.baja.sys.BEnum"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.getPropertyArrayIndex() -->
<method name="getPropertyArrayIndex"  public="true">
<description>
Get the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#propertyArrayIndex</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.setPropertyArrayIndex(int) -->
<method name="setPropertyArrayIndex"  public="true">
<description>
Set the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#propertyArrayIndex</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.getPriority() -->
<method name="getPriority"  public="true">
<description>
Get the &lt;code&gt;priority&lt;/code&gt; property.
</description>
<tag name="@see">#priority</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.setPriority(int) -->
<method name="setPriority"  public="true">
<description>
Set the &lt;code&gt;priority&lt;/code&gt; property.
</description>
<tag name="@see">#priority</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.getPostDelay() -->
<method name="getPostDelay"  public="true">
<description>
Get the &lt;code&gt;postDelay&lt;/code&gt; property.
</description>
<tag name="@see">#postDelay</tag>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.setPostDelay(int) -->
<method name="setPostDelay"  public="true">
<description>
Set the &lt;code&gt;postDelay&lt;/code&gt; property.
</description>
<tag name="@see">#postDelay</tag>
<parameter name="v">
<type class="int"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.getQuitOnFailure() -->
<method name="getQuitOnFailure"  public="true">
<description>
Get the &lt;code&gt;quitOnFailure&lt;/code&gt; property.
</description>
<tag name="@see">#quitOnFailure</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.setQuitOnFailure(boolean) -->
<method name="setQuitOnFailure"  public="true">
<description>
Set the &lt;code&gt;quitOnFailure&lt;/code&gt; property.
</description>
<tag name="@see">#quitOnFailure</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.getWriteSuccessful() -->
<method name="getWriteSuccessful"  public="true">
<description>
Get the &lt;code&gt;writeSuccessful&lt;/code&gt; property.
</description>
<tag name="@see">#writeSuccessful</tag>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.setWriteSuccessful(boolean) -->
<method name="setWriteSuccessful"  public="true">
<description>
Set the &lt;code&gt;writeSuccessful&lt;/code&gt; property.
</description>
<tag name="@see">#writeSuccessful</tag>
<parameter name="v">
<type class="boolean"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.changed(javax.baja.sys.Property, javax.baja.sys.Context) -->
<method name="changed"  public="true">
<description>
Changed. Pass callback to parent
</description>
<parameter name="p">
<type class="javax.baja.sys.Property"/>
</parameter>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.getPropertyValue() -->
<method name="getPropertyValue"  public="true">
<description/>
<return>
<type class="javax.baja.sys.BValue"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.setPropertyValue(javax.baja.sys.BValue) -->
<method name="setPropertyValue"  public="true">
<description/>
<parameter name="v">
<type class="javax.baja.sys.BValue"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.readAsn(javax.baja.bacnet.io.AsnInput) -->
<method name="readAsn"  public="true">
<description/>
<parameter name="in">
<type class="javax.baja.bacnet.io.AsnInput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="javax.baja.bacnet.io.AsnException"/>
</throws>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.writeAsn(javax.baja.bacnet.io.AsnOutput) -->
<method name="writeAsn"  public="true">
<description/>
<parameter name="out">
<type class="javax.baja.bacnet.io.AsnOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description/>
<parameter name="c">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.deviceId -->
<field name="deviceId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;deviceId&lt;/code&gt; property.
</description>
<tag name="@see">#getDeviceId</tag>
<tag name="@see">#setDeviceId</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.objectId -->
<field name="objectId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;objectId&lt;/code&gt; property.
</description>
<tag name="@see">#getObjectId</tag>
<tag name="@see">#setObjectId</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.propertyId -->
<field name="propertyId"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;propertyId&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyId</tag>
<tag name="@see">#setPropertyId</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.propertyArrayIndex -->
<field name="propertyArrayIndex"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;propertyArrayIndex&lt;/code&gt; property.
</description>
<tag name="@see">#getPropertyArrayIndex</tag>
<tag name="@see">#setPropertyArrayIndex</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.priority -->
<field name="priority"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;priority&lt;/code&gt; property.
</description>
<tag name="@see">#getPriority</tag>
<tag name="@see">#setPriority</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.postDelay -->
<field name="postDelay"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;postDelay&lt;/code&gt; property.
</description>
<tag name="@see">#getPostDelay</tag>
<tag name="@see">#setPostDelay</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.quitOnFailure -->
<field name="quitOnFailure"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;quitOnFailure&lt;/code&gt; property.
</description>
<tag name="@see">#getQuitOnFailure</tag>
<tag name="@see">#setQuitOnFailure</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.writeSuccessful -->
<field name="writeSuccessful"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;writeSuccessful&lt;/code&gt; property.
</description>
<tag name="@see">#getWriteSuccessful</tag>
<tag name="@see">#setWriteSuccessful</tag>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.DEVICE_ID_TAG -->
<field name="DEVICE_ID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.OBJECT_ID_TAG -->
<field name="OBJECT_ID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.PROPERTY_ID_TAG -->
<field name="PROPERTY_ID_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.PROPERTY_ARRAY_INDEX_TAG -->
<field name="PROPERTY_ARRAY_INDEX_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.PROPERTY_VALUE_TAG -->
<field name="PROPERTY_VALUE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.PRIORITY_TAG -->
<field name="PRIORITY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.POST_DELAY_TAG -->
<field name="POST_DELAY_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.QUIT_ON_FAILURE_TAG -->
<field name="QUIT_ON_FAILURE_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnetAws.datatypes.BBacnetActionCommand.WRITE_SUCCESSFUL_TAG -->
<field name="WRITE_SUCCESSFUL_TAG"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
