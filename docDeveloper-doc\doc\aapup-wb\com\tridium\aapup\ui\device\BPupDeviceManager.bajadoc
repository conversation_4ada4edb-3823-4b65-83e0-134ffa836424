<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="wb" qualifiedName="com.tridium.aapup.ui.device.BPupDeviceManager" name="BPupDeviceManager" packageName="com.tridium.aapup.ui.device" public="true">
<description>
BPupDeviceManager uses the BAbstractLearn framework to&#xa; provide a way for the user to create PUP Devices.
</description>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@creation">7/21/2005 8:40AM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.91</tag>
<extends>
<type class="javax.baja.driver.ui.device.BDeviceManager"/>
</extends>
</class>
</bajadoc>
