<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="javax.baja.alarm.BAlarmInstructions" name="BAlarmInstructions" packageName="javax.baja.alarm" public="true" final="true">
<description>
Ordered list of User Instructions for how to handle an alarm.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">20 Oct 05</tag>
<tag name="@version">$Revision: 3$ $Date: 12/14/05 9:59:06 AM EST$</tag>
<tag name="@since">Niagara 3.0</tag>
<extends>
<type class="javax.baja.sys.BSimple"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
<!-- javax.baja.alarm.BAlarmInstructions.make(javax.baja.util.BFormat) -->
<method name="make"  public="true" static="true">
<description>
Make for a single instruction.
</description>
<parameter name="instruction">
<type class="javax.baja.util.BFormat"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmInstructions"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmInstructions.make(javax.baja.util.BFormat[]) -->
<method name="make"  public="true" static="true">
<description>
Make for an array of instructions.
</description>
<parameter name="instructions">
<type class="javax.baja.util.BFormat" dimension="1"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmInstructions"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmInstructions.add(javax.baja.alarm.BAlarmInstructions, javax.baja.util.BFormat) -->
<method name="add"  public="true" static="true">
<description>
Make a new list by appending the specified &#xa; instruction to the original list.
</description>
<parameter name="orig">
<type class="javax.baja.alarm.BAlarmInstructions"/>
</parameter>
<parameter name="instruction">
<type class="javax.baja.util.BFormat"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmInstructions"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmInstructions.remove(javax.baja.alarm.BAlarmInstructions, int) -->
<method name="remove"  public="true" static="true">
<description>
Make a new list by removing the instruction at the specified index.
</description>
<parameter name="orig">
<type class="javax.baja.alarm.BAlarmInstructions"/>
</parameter>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmInstructions"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmInstructions.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Convenience for &lt;code&gt;DEFAULT.decodeFromString(string)&lt;/code&gt;
</description>
<parameter name="string">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.alarm.BAlarmInstructions"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmInstructions.get(int) -->
<method name="get"  public="true">
<description>
Get the instruction at the specified index.
</description>
<parameter name="index">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.util.BFormat"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmInstructions.size() -->
<method name="size"  public="true">
<description>
Get the number of instructions in this list.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmInstructions.isNull() -->
<method name="isNull"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Is this instance null?
</description>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmInstructions.toArray() -->
<method name="toArray"  public="true">
<description>
Get the array of ords.
</description>
<return>
<type class="javax.baja.util.BFormat" dimension="1"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmInstructions.hashCode() -->
<method name="hashCode"  public="true">
<description>
Hash is based on hashes of individual ords.
</description>
<return>
<type class="int"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmInstructions.equals(java.lang.Object) -->
<method name="equals"  public="true">
<description>
Equality is based on equality of individual ords.
</description>
<parameter name="obj">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmInstructions.encode(java.io.DataOutput) -->
<method name="encode"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
BOrd is encoded as using writeUTF().
</description>
<parameter name="out">
<type class="java.io.DataOutput"/>
</parameter>
<return>
<type class="void"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmInstructions.decode(java.io.DataInput) -->
<method name="decode"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
BOrd is decoded using readUTF().
</description>
<parameter name="in">
<type class="java.io.DataInput"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmInstructions.encodeToString() -->
<method name="encodeToString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Write the simple in text format.
</description>
<return>
<type class="java.lang.String"/>
</return>
<throws>
<type class="java.io.IOException"/>
</throws>
</method>

<!-- javax.baja.alarm.BAlarmInstructions.decodeFromString(java.lang.String) -->
<method name="decodeFromString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description>
Read the simple from text format.
</description>
<parameter name="s">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.sys.BObject"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmInstructions.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="cx">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmInstructions.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.alarm.BAlarmInstructions.NULL -->
<field name="NULL"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BAlarmInstructions"/>
<description>
Null is the empty list.
</description>
</field>

<!-- javax.baja.alarm.BAlarmInstructions.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.alarm.BAlarmInstructions"/>
<description>
The default is the empty list.
</description>
</field>

<!-- javax.baja.alarm.BAlarmInstructions.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
