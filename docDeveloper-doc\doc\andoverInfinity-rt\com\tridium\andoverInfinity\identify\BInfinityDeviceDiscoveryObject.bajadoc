<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoveryObject" name="BInfinityDeviceDiscoveryObject" packageName="com.tridium.andoverInfinity.identify" public="true">
<description>
BInfinityDeviceDiscoveryObject&#xa; &#xa; This class extends &lt;code&gt;BInfinityDeviceId&lt;/code&gt; to provide an additional&#xa; property to display the &#x22;Online&#x22; or &#x22;Offline&#x22; status of &#xa; a device as it is displayed as a discovery object.  All other discovery &#xa; properties are already defined on the &lt;code&gt;BInfinityDeviceId&lt;/code&gt;
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 22, 2007</tag>
<tag name="@version">$Revision$ $May 22, 2007 9:32:21 AM$</tag>
<tag name="@since"/>
<extends>
<type class="com.tridium.andoverInfinity.identify.BInfinityDeviceId"/>
</extends>
<implements>
<type class="com.tridium.ddf.discover.BIDdfDiscoveryObject"/>
</implements>
<property name="onlineStatus" flags="r">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;onlineStatus&lt;/code&gt; property.
</description>
<tag name="@see">#getOnlineStatus</tag>
<tag name="@see">#setOnlineStatus</tag>
</property>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoveryObject() -->
<constructor name="BInfinityDeviceDiscoveryObject" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoveryObject.getOnlineStatus() -->
<method name="getOnlineStatus"  public="true">
<description>
Get the &lt;code&gt;onlineStatus&lt;/code&gt; property.
</description>
<tag name="@see">#onlineStatus</tag>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoveryObject.setOnlineStatus(java.lang.String) -->
<method name="setOnlineStatus"  public="true">
<description>
Set the &lt;code&gt;onlineStatus&lt;/code&gt; property.
</description>
<tag name="@see">#onlineStatus</tag>
<parameter name="v">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoveryObject.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoveryObject.equivalent(java.lang.Object) -->
<method name="equivalent"  public="true">
<description>
Equivalency is defined by identical controller names, port, model, serial number,&#xa; and Id.
</description>
<tag name="@see">javax.baja.sys.BInterface#equivalent(java.lang.Object)</tag>
<parameter name="obj">
<type class="java.lang.Object"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if equivalent
</description>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoveryObject.toString(javax.baja.sys.Context) -->
<method name="toString"  public="true">
<description>
String representation shows all properties
</description>
<tag name="@see">javax.baja.sys.BInterface#toString(javax.baja.sys.Context)</tag>
<parameter name="context">
<type class="javax.baja.sys.Context"/>
</parameter>
<return>
<type class="java.lang.String"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoveryObject.onlineStatus -->
<field name="onlineStatus"  public="true" static="true" final="true">
<type class="javax.baja.sys.Property"/>
<description>
Slot for the &lt;code&gt;onlineStatus&lt;/code&gt; property.
</description>
<tag name="@see">#getOnlineStatus</tag>
<tag name="@see">#setOnlineStatus</tag>
</field>

<!-- com.tridium.andoverInfinity.identify.BInfinityDeviceDiscoveryObject.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
