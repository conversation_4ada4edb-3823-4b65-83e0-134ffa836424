<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetAws" runtimeProfile="wb" qualifiedName="com.tridium.bacnetAws.ui.config.BBacnetAwsConfigManager" name="BBacnetAwsConfigManager" packageName="com.tridium.bacnetAws.ui.config" public="true">
<description>
BBacnetAwsConfigManager allows the user to create and manage config&#xa; objects within a BACnet Aws device.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">07 June 2010</tag>
<extends>
<type class="com.tridium.bacnet.ui.config.BBacnetConfigManager"/>
</extends>
</class>
</bajadoc>
