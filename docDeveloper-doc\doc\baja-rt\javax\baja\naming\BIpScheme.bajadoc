<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.naming.BIpScheme" name="BIpScheme" packageName="javax.baja.naming" public="true">
<description>
BIpScheme is used to identify BIpHosts as &#x22;&lt;code&gt;ip:&amp;lt;hostname&amp;gt;&lt;/code&gt;&#x22;.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">14 Jan 03</tag>
<tag name="@version">$Revision: 3$ $Date: 3/28/05 9:23:00 AM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.naming.BOrdScheme"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NiagaraSingleton"/>
</annotation>
<!-- javax.baja.naming.BIpScheme.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.naming.BIpScheme.parse(java.lang.String) -->
<method name="parse"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="queryBody">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdQuery"/>
</return>
</method>

<!-- javax.baja.naming.BIpScheme.resolve(javax.baja.naming.OrdTarget, javax.baja.naming.OrdQuery) -->
<method name="resolve"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<parameter name="base">
<type class="javax.baja.naming.OrdTarget"/>
</parameter>
<parameter name="query">
<type class="javax.baja.naming.OrdQuery"/>
</parameter>
<return>
<type class="javax.baja.naming.OrdTarget"/>
</return>
<throws>
<type class="javax.baja.naming.SyntaxException"/>
</throws>
<throws>
<type class="javax.baja.naming.UnresolvedException"/>
</throws>
</method>

<!-- javax.baja.naming.BIpScheme.INSTANCE -->
<field name="INSTANCE"  public="true" static="true" final="true">
<type class="javax.baja.naming.BIpScheme"/>
<description/>
</field>

<!-- javax.baja.naming.BIpScheme.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
