<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="bacnet" runtimeProfile="rt" name="javax.baja.bacnet.device">
<description/>
<class packageName="javax.baja.bacnet.device" name="LatencyRecorder" category="interface"><description>The LatencyRecorder interface can&#xa; be implemented to receive latency events&#xa; for interactions with BBacnetDevices.</description></class>
<class packageName="javax.baja.bacnet.device" name="LatencyRecorderAware" category="interface"><description>The LatencyRecorderAware interface&#xa; provides the mechanism to register&#xa; for latency events from BBacnetDevices.</description></class>
</package>
</bajadoc>
