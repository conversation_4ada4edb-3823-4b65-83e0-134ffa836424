<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverInfinity" runtimeProfile="rt" qualifiedName="com.tridium.andoverInfinity.comm.req.BInfinityLogonSequenceRequest" name="BInfinityLogonSequenceRequest" packageName="com.tridium.andoverInfinity.comm.req" public="true">
<description>
Logon is sent from BInfinityInitializeScreenRequest (&#x22;window&#x22;)&#xa; if the initialize screen request(CTL-Z) was unsuccessful.
</description>
<tag name="@author">cturman</tag>
<tag name="@creation">May 1, 2007</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.2</tag>
<extends>
<type class="com.tridium.ddf.comm.req.BDdfRequest"/>
</extends>
<implements>
<type class="com.tridium.ddf.comm.req.BIDdfCustomRequest"/>
</implements>
<implements>
<type class="com.tridium.andoverInfinity.comm.Vt100Const"/>
</implements>
<!-- com.tridium.andoverInfinity.comm.req.BInfinityLogonSequenceRequest() -->
<constructor name="BInfinityLogonSequenceRequest" public="true">
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityLogonSequenceRequest(com.tridium.andoverInfinity.BInfinityNetwork) -->
<constructor name="BInfinityLogonSequenceRequest" public="true">
<parameter name="net">
<type class="com.tridium.andoverInfinity.BInfinityNetwork"/>
</parameter>
<description/>
</constructor>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityLogonSequenceRequest.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityLogonSequenceRequest.processReceive(com.tridium.ddf.comm.IDdfDataFrame) -->
<method name="processReceive"  public="true">
<description>
Logon involves responding with user name or password, depending on the &#xa; reported cursor mode.  Once we report cursor is in command line area, we&#xa; are done.  We send BInfinityAckWithData messages to send the user name, &#xa; password data.
</description>
<tag name="@see">com.tridium.devDriver.comm.req.BIDdfRequest#processReceive(com.tridium.devDriver.comm.IDevDataFrame)</tag>
<parameter name="iDevDataFrame">
<type class="com.tridium.ddf.comm.IDdfDataFrame"/>
</parameter>
<return>
<type class="com.tridium.ddf.comm.rsp.BIDdfResponse"/>
</return>
<throws>
<type class="com.tridium.ddf.comm.rsp.DdfResponseException"/>
</throws>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityLogonSequenceRequest.toByteArray() -->
<method name="toByteArray"  public="true">
<description>
To logon, send the &#x22;window&#x22; command,  The correct response is a dialog box&#xa; prompting for user name/password.
</description>
<tag name="@see">com.tridium.devDriver.comm.req.BIDdfRequest#toByteArray()</tag>
<return>
<type class="byte" dimension="1"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityLogonSequenceRequest.processErrorResponse(com.tridium.ddf.comm.rsp.DdfResponseException) -->
<method name="processErrorResponse"  public="true">
<description/>
<parameter name="errorRsp">
<type class="com.tridium.ddf.comm.rsp.DdfResponseException"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityLogonSequenceRequest.processLateResponse(com.tridium.ddf.comm.rsp.BIDdfResponse) -->
<method name="processLateResponse"  public="true">
<description/>
<parameter name="devRsp">
<type class="com.tridium.ddf.comm.rsp.BIDdfResponse"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityLogonSequenceRequest.processResponse(com.tridium.ddf.comm.rsp.BIDdfResponse) -->
<method name="processResponse"  public="true">
<description/>
<parameter name="devRsp">
<type class="com.tridium.ddf.comm.rsp.BIDdfResponse"/>
</parameter>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityLogonSequenceRequest.processTimeout() -->
<method name="processTimeout"  public="true">
<description/>
<return>
<type class="void"/>
</return>
</method>

<!-- com.tridium.andoverInfinity.comm.req.BInfinityLogonSequenceRequest.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

</class>
</bajadoc>
