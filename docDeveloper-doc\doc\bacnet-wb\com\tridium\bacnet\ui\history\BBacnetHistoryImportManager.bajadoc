<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="wb" qualifiedName="com.tridium.bacnet.ui.history.BBacnetHistoryImportManager" name="BBacnetHistoryImportManager" packageName="com.tridium.bacnet.ui.history" public="true">
<description>
BBacnetHistoryImportManager is the implementation of BHistoryImportManager&#xa; for managing history imports from a remote BACnet device into the&#xa; local station.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">12 Apr 2004</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3 BACnet 1.0</tag>
<extends>
<type class="javax.baja.driver.ui.history.BHistoryImportManager"/>
</extends>
</class>
</bajadoc>
