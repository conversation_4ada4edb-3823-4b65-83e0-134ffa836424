<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="com.tridium.bacnet.job.BBacnetDiscoverPointsJob" name="BBacnetDiscoverPointsJob" packageName="com.tridium.bacnet.job" public="true">
<description>
BBacnetDiscoverPointsJob represents a request from Niagara to&#xa; read the Object_List property of a remote device and create BBacnetObjects&#xa; for any objects that are not currently mapped in the Niagara database.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@creation">02 Nov 02</tag>
<tag name="@since">Niagara 3 Bacnet 1.0</tag>
<extends>
<type class="com.tridium.bacnet.job.BBacnetDiscoverJob"/>
</extends>
<action name="discover" flags="">
<parameter name="parameter">
<type class="com.tridium.bacnet.job.BDiscoveryPoint"/>
</parameter>
<return>
<type class="com.tridium.bacnet.job.BDiscoveryPointTable"/>
</return>
<description>
Slot for the &lt;code&gt;discover&lt;/code&gt; action.
</description>
<tag name="@see">#discover(BDiscoveryPoint parameter)</tag>
</action>

<action name="discoverFacets" flags="">
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="javax.baja.sys.BFacets"/>
</return>
<description>
Slot for the &lt;code&gt;discoverFacets&lt;/code&gt; action.
</description>
<tag name="@see">#discoverFacets(BBacnetObjectIdentifier parameter)</tag>
</action>

<action name="checkForPriorityArray" flags="">
<parameter name="parameter">
<type class="javax.baja.bacnet.datatypes.BBacnetObjectIdentifier"/>
</parameter>
<return>
<type class="javax.baja.sys.BBoolean"/>
</return>
<description>
Slot for the &lt;code&gt;checkForPriorityArray&lt;/code&gt; action.
</description>
<tag name="@see">#checkForPriorityArray(BBacnetObjectIdentifier parameter)</tag>
</action>

</class>
</bajadoc>
