<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnet" runtimeProfile="rt" qualifiedName="javax.baja.bacnet.enums.access.BBacnetAuthorizationMode" name="BBacnetAuthorizationMode" packageName="javax.baja.bacnet.enums.access" public="true" final="true">
<description>
BBacnetAccessCredentialDisable represents the Bacnet&#xa; Access Credential Disable enumeration.&#xa; &lt;p&gt;&#xa; BBacnetAccessCredentialDisable is an &#x22;extensible&#x22; enumeration.&#xa; Values 0-63 are reserved for use by ASHRAE.&#xa; Values from 64-65535 (0x3FFFFF)&#xa; can be used for proprietary extensions.&#xa; &lt;p&gt;&#xa; Note that for proprietary extensions, a given ordinal is not&#xa; globally mapped to the same enumeration.  Type X from vendor&#xa; A will be different than type X from vendor B.  Extensions are&#xa; also not guaranteed unique within a vendor&#x27;s own products, so&#xa; type Y in device A from vendor A will in general be different&#xa; than type Y in device B from vendor A.
</description>
<tag name="@author">Joseph Chandler</tag>
<tag name="@creation">15 Apr 15</tag>
<tag name="@since">Niagara 4</tag>
<extends>
<type class="javax.baja.sys.BFrozenEnum"/>
</extends>
<implements>
<type class="javax.baja.bacnet.BacnetConst"/>
</implements>
<annotation><type class="javax.baja.nre.annotations.NiagaraEnum"/>
<elementValue name="range">
<annotationValue kind="array">
<elementArray>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;authorize&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>0</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;grantActive&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>1</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;denyAll&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>2</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;verificationRequired&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>3</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;authorizationDelayed&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>4</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
<annotationValue kind="annotation">
<annotation><type class="javax.baja.nre.annotations.Range"/>
<elementValue name="value">
<annotationValue kind="expr">
<expression>&#x22;none&#x22;</expression>
</annotationValue>
</elementValue>
<elementValue name="ordinal">
<annotationValue kind="expr">
<expression>5</expression>
</annotationValue>
</elementValue>
</annotation>
</annotationValue>
</elementArray>
</annotationValue>
</elementValue>
<elementValue name="defaultValue">
<annotationValue kind="expr">
<expression>&#x22;denyAll&#x22;</expression>
</annotationValue>
</elementValue>
</annotation>
<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.make(int) -->
<method name="make"  public="true" static="true">
<description>
Factory method with ordinal.
</description>
<parameter name="ordinal">
<type class="int"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationMode"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.make(java.lang.String) -->
<method name="make"  public="true" static="true">
<description>
Factory method with tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationMode"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.getType() -->
<method name="getType"  public="true">
<annotation><type class="java.lang.Override"/>
</annotation>
<description/>
<return>
<type class="javax.baja.sys.Type"/>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.tag(int) -->
<method name="tag"  public="true" static="true">
<description>
Create a string tag for the given ordinal.
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="java.lang.String"/>
<description>
the tag for the ordinal, if it is known,&#xa; or construct one using standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.ordinal(java.lang.String) -->
<method name="ordinal"  public="true" static="true">
<description>
Get the ordinal for the given tag.
</description>
<parameter name="tag">
<type class="java.lang.String"/>
</parameter>
<return>
<type class="int"/>
<description>
the ordinal for the tag, if it is known,&#xa; or generate one if the tag uses standard prefixes.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.isProprietary(int) -->
<method name="isProprietary"  public="true" static="true">
<description>
Is this a proprietary extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is a proprietary extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.isAshrae(int) -->
<method name="isAshrae"  public="true" static="true">
<description>
Is this an ASHRAE extension?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this is an ASHRAE extension.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.isValid(int) -->
<method name="isValid"  public="true" static="true">
<description>
Is this id valid for this enumeration?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is within the allowed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.isFixed(int) -->
<method name="isFixed"  public="true" static="true">
<description>
Is this id part of the predefined (fixed) range?
</description>
<parameter name="id">
<type class="int"/>
</parameter>
<return>
<type class="boolean"/>
<description>
true if this id is in the fixed range.
</description>
</return>
</method>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.AUTHORIZE -->
<field name="AUTHORIZE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for authorize.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.GRANT_ACTIVE -->
<field name="GRANT_ACTIVE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for grantActive.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.DENY_ALL -->
<field name="DENY_ALL"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for denyAll.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.VERIFICATION_REQUIRED -->
<field name="VERIFICATION_REQUIRED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for verificationRequired.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.AUTHORIZATION_DELAYED -->
<field name="AUTHORIZATION_DELAYED"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for authorizationDelayed.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.NONE -->
<field name="NONE"  public="true" static="true" final="true">
<type class="int"/>
<description>
Ordinal value for none.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.authorize -->
<field name="authorize"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationMode"/>
<description>
BBacnetAuthorizationMode constant for authorize.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.grantActive -->
<field name="grantActive"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationMode"/>
<description>
BBacnetAuthorizationMode constant for grantActive.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.denyAll -->
<field name="denyAll"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationMode"/>
<description>
BBacnetAuthorizationMode constant for denyAll.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.verificationRequired -->
<field name="verificationRequired"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationMode"/>
<description>
BBacnetAuthorizationMode constant for verificationRequired.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.authorizationDelayed -->
<field name="authorizationDelayed"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationMode"/>
<description>
BBacnetAuthorizationMode constant for authorizationDelayed.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.none -->
<field name="none"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationMode"/>
<description>
BBacnetAuthorizationMode constant for none.
</description>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.DEFAULT -->
<field name="DEFAULT"  public="true" static="true" final="true">
<type class="javax.baja.bacnet.enums.access.BBacnetAuthorizationMode"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.TYPE -->
<field name="TYPE"  public="true" static="true" final="true">
<type class="javax.baja.sys.Type"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.MAX_ASHRAE_ID -->
<field name="MAX_ASHRAE_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.MAX_RESERVED_ID -->
<field name="MAX_RESERVED_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

<!-- javax.baja.bacnet.enums.access.BBacnetAuthorizationMode.MAX_ID -->
<field name="MAX_ID"  public="true" static="true" final="true">
<type class="int"/>
<description/>
</field>

</class>
</bajadoc>
