<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="alarmOrion" runtimeProfile="rt" name="javax.baja.alarmOrion">
<description/>
<class packageName="javax.baja.alarmOrion" name="BOrionAlarmClass"><description>The representation of an alarm class within the orion database.</description></class>
<class packageName="javax.baja.alarmOrion" name="BOrionAlarmDatabase"><description>An Orion based implementation of an alarm database.</description></class>
<class packageName="javax.baja.alarmOrion" name="BOrionAlarmFacetName"><description>The representation of an alarm data facet name within the orion database.</description></class>
<class packageName="javax.baja.alarmOrion" name="BOrionAlarmFacetValue"><description>The representation of an alarm data facet value within the orion database.</description></class>
<class packageName="javax.baja.alarmOrion" name="BOrionAlarmRecord"><description>The representation of an alarm record within the orion database.</description></class>
<class packageName="javax.baja.alarmOrion" name="BOrionAlarmService"><description>An Orion based implementation of an alarm service.</description></class>
<class packageName="javax.baja.alarmOrion" name="BOrionAlarmSource"/>
<class packageName="javax.baja.alarmOrion" name="BOrionAlarmSourceOrder"/>
<class packageName="javax.baja.alarmOrion" name="OrionAlarmDbConnection"><description>Connection to a BOrionAlarmDatabase.</description></class>
</package>
</bajadoc>
