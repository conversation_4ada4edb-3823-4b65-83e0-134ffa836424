<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="platHwScan" runtimeProfile="rt" name="com.tridium.platHwScan.optionCards">
<description/>
<class packageName="com.tridium.platHwScan.optionCards" name="BOptionCard"><description>BOptionCard contains information describing the characteristics&#xa; of a hardware option card.</description></class>
<class packageName="com.tridium.platHwScan.optionCards" name="BOptionModule"><description>BOptionModule inherits everything from BOptionCard.</description></class>
<class packageName="com.tridium.platHwScan.optionCards" name="BOptionSlot"><description>BOptionSlot represents the electrical connection for JACE option cards</description></class>
</package>
</bajadoc>
