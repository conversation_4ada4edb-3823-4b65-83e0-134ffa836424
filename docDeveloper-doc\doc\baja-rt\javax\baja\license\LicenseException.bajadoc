<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="baja" runtimeProfile="rt" qualifiedName="javax.baja.license.LicenseException" name="LicenseException" packageName="javax.baja.license" public="true" category="exception">
<description>
LicenseException indicates a licensing problem.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">3 Nov 01</tag>
<tag name="@version">$Revision: 1$ $Date: 11/25/01 2:12:37 PM EST$</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.sys.BajaRuntimeException"/>
</extends>
<!-- javax.baja.license.LicenseException(java.lang.String) -->
<constructor name="LicenseException" public="true">
<parameter name="msg">
<type class="java.lang.String"/>
</parameter>
<description>
Construct a LicensedException with the given message.
</description>
</constructor>

<!-- javax.baja.license.LicenseException() -->
<constructor name="LicenseException" public="true">
<description>
Construct a LicensedException.
</description>
</constructor>

</class>
</bajadoc>
