<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="alarm" runtimeProfile="rt" qualifiedName="com.tridium.alarm.BConsoleRecipient" name="BConsoleRecipient" packageName="com.tridium.alarm" public="true">
<description>
Recipient for an alarm console.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@version">$Revision: 79$ $Date: 1/10/11 11:38:59 AM EST$</tag>
<tag name="@creation">18 Jul 01</tag>
<tag name="@since">Baja 1.0</tag>
<extends>
<type class="javax.baja.alarm.BAlarmRecipient"/>
</extends>
<property name="status" flags="trsd">
<type class="javax.baja.status.BStatus"/>
<description>
Slot for the &lt;code&gt;status&lt;/code&gt; property.
</description>
<tag name="@see">#getStatus</tag>
<tag name="@see">#setStatus</tag>
</property>

<property name="faultCause" flags="trd">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;faultCause&lt;/code&gt; property.
</description>
<tag name="@see">#getFaultCause</tag>
<tag name="@see">#setFaultCause</tag>
</property>

<property name="defaultTimeRange" flags="">
<type class="com.tridium.bql.util.BDynamicTimeRange"/>
<description>
Slot for the &lt;code&gt;defaultTimeRange&lt;/code&gt; property.
</description>
<tag name="@see">#getDefaultTimeRange</tag>
<tag name="@see">#setDefaultTimeRange</tag>
</property>

</class>
</bajadoc>
