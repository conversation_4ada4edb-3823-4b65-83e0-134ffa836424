<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="bacnetOws" runtimeProfile="rt" qualifiedName="com.tridium.bacnetOws.BLocalBacnetOwsDevice" name="BLocalBacnetOwsDevice" packageName="com.tridium.bacnetOws" public="true">
<description>
BLocalBacnetOwsDevice is the representation of Niagara as a Bacnet&#xa; device on the Bacnet internetwork.&#xa; &lt;p&gt;&#xa; Objects in Niagara are exposed to Bacnet through the&#xa; methods defined in the BIBacnetServerObject API.
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">09 Sep 2009</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">Niagara 3.5</tag>
<extends>
<type class="javax.baja.bacnet.export.BLocalBacnetDevice"/>
</extends>
</class>
</bajadoc>
