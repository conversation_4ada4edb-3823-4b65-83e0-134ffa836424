<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<package module="baja" runtimeProfile="rt" name="javax.baja.file.zip">
<description>
&lt;p&gt;APIs for access to expose zip files as a file system.&lt;/p&gt;
</description>
<class packageName="javax.baja.file.zip" name="BZipFile"><description>BZipFile is a BIFile that stores its own file space&#xa; addressed using the &#x22;zip&#x22; ord scheme.</description></class>
<class packageName="javax.baja.file.zip" name="BZipFileDirectory"><description>BZipFileDirectory is an in-memory file store of a &#xa; directory within a zip file.</description></class>
<class packageName="javax.baja.file.zip" name="BZipFileEntry"><description>BZipFileEntry is a BIFileStore implementation for &#xa; local files using java.util.zip.ZipEntry.</description></class>
<class packageName="javax.baja.file.zip" name="BZipScheme"><description>BZipScheme manages the &#x22;zip&#x22; scheme with a ZipPath query.</description></class>
<class packageName="javax.baja.file.zip" name="BZipSpace"><description>BZipSpace is a BFileSpace for a directory tree within BZipFile.</description></class>
<class packageName="javax.baja.file.zip" name="BZipZipSpace"><description>BZipZipSpace - space for a zip file in a zip file.</description></class>
<class packageName="javax.baja.file.zip" name="ZipPath"><description>Specialized FilePath which refers to a single entry in a zip file</description></class>
</package>
</bajadoc>
