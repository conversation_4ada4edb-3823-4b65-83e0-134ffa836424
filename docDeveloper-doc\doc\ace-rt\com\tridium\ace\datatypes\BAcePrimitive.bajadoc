<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="ace" runtimeProfile="rt" qualifiedName="com.tridium.ace.datatypes.BAcePrimitive" name="BAcePrimitive" packageName="com.tridium.ace.datatypes" public="true" final="true">
<description>
AcePrimitive
</description>
<tag name="@author"><PERSON></tag>
<tag name="@creation">2/26/2016</tag>
<extends>
<type class="javax.baja.sys.BSimple"/>
</extends>
<annotation><type class="javax.baja.nre.annotations.NoSlotomatic"/>
</annotation>
</class>
</bajadoc>
