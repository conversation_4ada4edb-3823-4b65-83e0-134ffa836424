<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="andoverAC256" runtimeProfile="rt" qualifiedName="com.tridium.andoverAC256.job.BAndoverDiscoveryPoint" name="BAndoverDiscoveryPoint" packageName="com.tridium.andoverAC256.job" public="true">
<description>
Andover AC256 Discovery Point.
</description>
<tag name="@author"><PERSON><PERSON><PERSON></tag>
<tag name="@creation">4/26/2005 11:49AM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.76</tag>
<extends>
<type class="javax.baja.sys.BStruct"/>
</extends>
<property name="assignedName" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;assignedName&lt;/code&gt; property.&#xa; the name of the point in the ac256 panel
</description>
<tag name="@see">#getAssignedName</tag>
<tag name="@see">#setAssignedName</tag>
</property>

<property name="valueString" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;valueString&lt;/code&gt; property.&#xa; The raw value portion of the string
</description>
<tag name="@see">#getValueString</tag>
<tag name="@see">#setValueString</tag>
</property>

<property name="origin" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;origin&lt;/code&gt; property.&#xa; will contain the name of the AC256 table or the LCU that&#xa; originated the point
</description>
<tag name="@see">#getOrigin</tag>
<tag name="@see">#setOrigin</tag>
</property>

<property name="pointNotes" flags="">
<type class="java.lang.String"/>
<description>
Slot for the &lt;code&gt;pointNotes&lt;/code&gt; property.&#xa; may contain extra information such as the native I/O type&#xa; in an LCU (thermistor input, voltage input, etc) or the&#xa; point terminals.  If not learned, will contain the&#xa; text &#x22;&#x22;, but can edited to add user defined info.
</description>
<tag name="@see">#getPointNotes</tag>
<tag name="@see">#setPointNotes</tag>
</property>

</class>
</bajadoc>
