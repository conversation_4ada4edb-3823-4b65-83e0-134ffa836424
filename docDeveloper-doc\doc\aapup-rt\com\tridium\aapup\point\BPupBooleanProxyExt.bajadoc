<?xml version="1.0" encoding="UTF-8"?>
<bajadoc version="2.0" createdBy="niagara-baja-doclet-1.0.9" createdAt="03-May-2024" createdOn="883e7f7a9875">
<class module="aapup" runtimeProfile="rt" qualifiedName="com.tridium.aapup.point.BPupBooleanProxyExt" name="BPupBooleanProxyExt" packageName="com.tridium.aapup.point" public="true">
<description>
BPupBooleanProxyExt is the proxy extension for bringing&#xa; PUP point information into Niagara Boolean Points.
</description>
<tag name="@author">C<PERSON><PERSON></tag>
<tag name="@creation">7/28/2005 11:25AM</tag>
<tag name="@version">$Revision$ $Date$</tag>
<tag name="@since">3.0.91</tag>
<extends>
<type class="com.tridium.aapup.point.BPupProxyExt"/>
</extends>
<property name="valueToWriteIfTrue" flags="">
<type class="double"/>
<description>
Slot for the &lt;code&gt;valueToWriteIfTrue&lt;/code&gt; property.&#xa; numeric value to be written to the controller on if a &#x22;true&#x22;&#xa; is to be written.  Normally, this is 1.0
</description>
<tag name="@see">#getValueToWriteIfTrue</tag>
<tag name="@see">#setValueToWriteIfTrue</tag>
</property>

<property name="valueToWriteIfFalse" flags="">
<type class="double"/>
<description>
Slot for the &lt;code&gt;valueToWriteIfFalse&lt;/code&gt; property.&#xa; numeric value to be written to the controller on if a &#x22;false&#x22;&#xa; is to be written.  Normally, this is 0.0
</description>
<tag name="@see">#getValueToWriteIfFalse</tag>
<tag name="@see">#setValueToWriteIfFalse</tag>
</property>

</class>
</bajadoc>
